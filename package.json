{"name": "pagedoo-meta-live-admin", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --mode dev", "dev:force": "yarn && vite --mode dev --force", "dev-prod": "vite --mode production", "build:dev": "vite build --mode dev", "build": "vite build", "build:dev-ad": "vite build --mode dev-ad --base //dev-avatarcdn.pay.qq.com/intelligent/live/page/", "build-ad": "vite build --mode production-ad --sourcemap hidden", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "lint": "eslint --ext .js,.jsx,.ts,.tsx,.vue src", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx,.vue src --fix", "preview": "vite preview", "tsc": "tsc", "build:monitor:lib": "vite build --config ./vite.lib-monitor.ts", "preinstall": "npx only-allow yarn", "update:lib:all": "yarn add @tencent/pagedoo-editor @tencent/pagedoo-editor @tencent/pagedoo-library @tencent/gems-preview @tencent/gems-html-generator @tencent/pagedoo-time-navigator && npm run deduplicate", "update:lib:editor": "yarn add @tencent/pagedoo-editor && npm run deduplicate", "deduplicate": "npx -y yarn-deduplicate"}, "dependencies": {"@formily/antd": "2.3.1", "@formily/core": "2.3.1", "@formily/react": "2.3.1", "@formily/tdesign-react": "^1.0.0-beta.15", "@tencent/avatar-client-bridge-type": "^0.0.24", "@tencent/creative-mb": "^0.0.59", "@tencent/eventbus": "^1.0.8", "@tencent/gems-ability": "^0.1.3", "@tencent/gems-html-generator": "^0.0.27", "@tencent/gems-preview": "^1.0.37", "@tencent/midas-util": "^1.0.53", "@tencent/pagedoo-editor": "0.0.79", "@tencent/pagedoo-library": "^1.0.65", "@tencent/pagedoo-time-navigator": "1.0.39", "@tencent/react-designable": "^1.0.7", "@tencent/spaui": "^3.0.328-beta.0", "aegis-web-sdk": "^1.39.1", "ahooks": "^3.7.11", "antd": "^4.12.3", "await-to-js": "^3.0.0", "axios": "^1.6.8", "big.js": "^6.2.2", "bowser": "^2.11.0", "copy-to-clipboard": "^3.3.3", "cos-js-sdk-v5": "^1.8.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "detect-gpu": "^5.0.59", "extendable-error": "^0.1.7", "fabric": "^6.3.0", "flv.js": "1.6.2", "highlight-words-core": "^1.2.3", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "mime": "^4.0.4", "mockjs": "^1.1.0", "moment": "^2.30.1", "path-browserify": "^1.0.1", "qrcode": "^1.5.3", "rc-dock": "^3.3.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.1.1", "react-router-dom": "^6.22.3", "recoil": "^0.7.7", "recoil-nexus": "^0.5.0", "semver": "^7.6.3", "spark-md5": "^3.0.2", "tdesign-icons-react": "^0.3.2", "tdesign-react": "^1.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@emotion/react": "^11.11.4", "@tailwindcss/line-clamp": "^0.4.4", "@tencent/eslint-config-prettier-typescript-react": "^2.1.0", "@tencent/pbmockts": "^2.0.29", "@types/big.js": "^6.2.2", "@types/crypto-js": "^4.2.2", "@types/fabric": "^5.3.8", "@types/highlight-words-core": "^1.2.3", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.66", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.22", "@types/spark-md5": "^3.0.4", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "babel-runtime-jsx-plus": "^0.1.5", "eslint": "^8.46.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-plus": "^0.1.0", "eslint-plugin-prettier": "^4.2.1", "less": "^4.2.0", "less-plugin-module-resolver": "^1.0.3", "lint-staged": "^13.0.3", "postcss": "^8.4.38", "prettier": "^2.8.7", "rollup-plugin-visualizer": "^5.12.0", "sass-embedded": "^1.80.3", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.4.15", "vite-plugin-html-config": "^1.0.11", "vite-plugin-jsx-plus": "^0.1.0", "yorkie": "^2.0.0"}, "prettier": {"singleQuote": true}, "gitHooks": {"commit-msg": "commitlint -E GIT_PARAMS", "pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}