// 配置说明 https://git.woa.com/cocao/pbmock
module.exports = {
  output: './src/pb/', // 输出的目录 相对于项目根目录
  iwikiOnly: false, // 为true时仅推送iwiki文档 不生成任何桩代码
  iwikiPush: 'app_pbmock', // 默认使用新版推送
  iwikiProtocol: 'https', // 指定协议 默认https 如果在devcloud环境 请使用http
  task: [
    {
      name: '', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:pagedoo/pagedoo/protocol/material.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'dev', // 如果是master分支请留空
      protocol: './material.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.material', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
        // method: 'POST', // 可选 请求方式 默认POST string|function
        // path: '/myPath/%name%', // 可选 请求路径 默认标准路由路径 string|function
        // requestPreview: (req) => req, // 可选 对入参进行处理 function
        // responsePreview: (resp) => resp, // 可选 对返回进行处理 function
      },
    },
    {
      name: 'MetaFeedbackSvr', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:pagedoo/pagedoo/protocol/meta_feedback_svr.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'dev', // 如果是master分支请留空
      protocol: './meta_feedback_svr.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.meta_feedback_svr', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {},
    },
    {
      name: 'CosStoreSvr', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:midas-arch/midas-protocol/channels-school/cos_store.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'dev', // 如果是master分支请留空
      protocol: './cos_store.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.cos_store', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
        // method: 'POST', // 可选 请求方式 默认POST string|function
        // path: '/myPath/%name%', // 可选 请求路径 默认标准路由路径 string|function
        // requestPreview: (req) => req, // 可选 对入参进行处理 function
        // responsePreview: (resp) => resp, // 可选 对返回进行处理 function
      },
    },
    {
      name: '', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:dip/protocal.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'test_get_ppt_content', // 如果是master分支请留空
      protocol: './virtual_man_backend.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.dip.manager_sys_api', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
        // method: 'POST', // 可选 请求方式 默认POST string|function
        // path: '/myPath/%name%', // 可选 请求路径 默认标准路由路径 string|function
        // requestPreview: (req) => req, // 可选 对入参进行处理 function
        // responsePreview: (resp) => resp, // 可选 对返回进行处理 function
      },
    },
    {
      type: 'git',
      git: '***************:pagedoo/pagedoo/protocol/content_manage.git',
      branch: 'dman',
      protocol: './content_manage.proto',
      package: 'trpc.tencent.content_manage',
    },
    {
      name: 'DipContentManage',
      type: 'git',
      git: '***************:dip/protocals/content_manage.git',
      branch: 'master',
      protocol: './content_manage.proto',
      package: 'trpc.tencent.content_manage',
    },
    // 内容服务，创建等
    {
      name: '', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:pagedoo/pagedoo/protocol/page_content.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'dev', // 如果是master分支请留空
      protocol: './page_content.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.page_content', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {},
    },
    // {
    //   name: 'NpcJob', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
    //   type: 'git', // file:协议在本地、git:协议在git仓库
    //   git: '***************:dip/protocal.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
    //   branch: 'support_ppt_job', // 如果是master分支请留空
    //   protocol: './npc-job-controller-server.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
    //   package: 'trpc.dip.npc_job_controller', // 填pb文件里面package
    //   iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
    //   doc: {},
    // },
    //   临时
    {
      name: 'NpcJob', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:midas-arch/midas-protocol/digitalman/npc_ppt_scripts_svr.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'master', // 如果是master分支请留空
      protocol: './npc_ppt_scripts_svr.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.npc_ppt_scripts_svr', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {},
    },
    {
      name: 'RealTimeInteraction',
      type: 'git',
      git: '***************:dip/protos.git',
      branch: 'master',
      protocol: './interaction/interaction.proto',
      package: 'trpc.tencent.interaction',
      iwiki: '',
      doc: {},
    },
    {
      name: 'GeneralContentSvr', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:pagedoo/pagedoo/protocol/page_content.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'dev', // 如果是master分支请留空
      protocol: './page_content.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.page_content', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
      },
    },
    {
      name: 'DigitalManProc',
      type: 'git',
      git: '***************:midas-arch/midas-protocol/digitalman/guanggao_goods_digital_man.git',
      branch: 'dev',
      protocol: './guanggao_goods_digital_man.proto',
      package: 'trpc.tencent.guanggao_goods_digital_man',
      iwiki: '',
      doc: {},
    },
    {
      name: 'ResourceSvr',
      type: 'git',
      git: '***************:dip/dip-resource/virtual_man_resource_protos.git',
      branch: 'support_video_job',
      protocol: './virtual_man_resource_manager.proto',
      package: 'trpc.tencent.virtual_man_resource_manager',
      iwiki: '',
      doc: {},
    },
    {
      name: 'LiveInteractionAnswerSvr',
      type: 'git',
      git: '***************:dip/protocals/live_interaction_answer_manager_proto.git',
      branch: 'live_answer',
      protocol: './live_interaction_answer_manager.proto',
      package: 'trpc.tencent.live_interaction_answer_manager',
      iwiki: '',
      doc: {},
    },
    {
      name: 'ShuzirenImageMngSvr',
      type: 'git',
      git: '***************:dip/protocals/shuzhiren_image_mng_protos.git',
      branch: 'master',
      protocol: './shuzhiren_image_mng.proto',
      package: 'trpc.tencent.shuziren_image_mng_svr',
      iwiki: '',
      doc: {},
    },
    {
      name: '', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:dip/protocals/virtual_man_manager_api_protos.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'test_get_ppt_content', // 如果是master分支请留空
      protocol: './virtual_man_backend.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.dip.manager_sys_api', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
        // method: 'POST', // 可选 请求方式 默认POST string|function
        // path: '/myPath/%name%', // 可选 请求路径 默认标准路由路径 string|function
        // requestPreview: (req) => req, // 可选 对入参进行处理 function
        // responsePreview: (resp) => resp, // 可选 对返回进行处理 function
      },
    },
    {
      name: 'NpcScriptSvr', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:dip/protocals/npc_job_controller_server_proto.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'npc-svr-new', // 如果是master分支请留空
      protocol: './npc-job-controller-server.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.dip.npc_job_controller', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
      },
    },
    {
      name: 'ClientSvr', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:midas-arch/midas-protocol/digitalman/client_copilot.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'master', // 如果是master分支请留空
      protocol: './client_copilot.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.client_copilot', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
      },
    },
    {
      name: 'MultilingualService', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:minfra/intelli_app/protocol/multilingual_service.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'master', // 如果是master分支请留空
      protocol: './multilingual_service.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.midas_oversea.intelli_app', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
      },
    },
    {
      name: 'LiveGroupSvr', // 可自定义服务名 支持 字符串 多服务时可使用：字符串数组 对象(原服务名 => 新服务名)
      type: 'git', // file:协议在本地、git:协议在git仓库
      git: '***************:dip/protocals/dman_toc_dev_data_svr.git', // 如果type是git 填写git仓库地址 如果type是file 请留空
      branch: 'dev', // 如果是master分支请留空
      protocol: './dman_toc_dev_data_svr.proto', // 协议位置 如果type是file 路径相对于项目根目录 如果type是git 路径相对于仓库根目录
      package: 'trpc.tencent.dman_toc_dev_data', // 填pb文件里面package
      iwiki: '', // 添加iwiki地址的pageId（纯数字） 不填则不上传协议文档 打开iwiki新建一页获取url中的pageId
      doc: {
      },
    },
  ],
};
