import react from '@vitejs/plugin-react';
import { execSync } from 'child_process';
import { LessPluginModuleResolver } from 'less-plugin-module-resolver';
import * as path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig, loadEnv, Plugin } from 'vite';
import htmlPlugin from 'vite-plugin-html-config';
import jsxPlusPlugin from 'vite-plugin-jsx-plus';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { GlobalConfig } from './src/globalConfig';

execSync('npm run build:monitor:lib', {
  cwd: __dirname,
});

const materialHMRPlugin = (
  options: {
    port?: number;
    interval?: number;
  } = {}
): Plugin => {
  const { port = 5678, interval = 2000 } = options;
  return {
    name: 'material-hmr-plugin',
    transformIndexHtml: () => {
      return [
        {
          tag: 'script',
          attrs: {
            type: 'module',
            src: `./src/pages/Editor/material-hmr.ts?port=${port}&interval=${interval}`,
          },
          injectTo: 'body',
        },
      ];
    },
    apply: 'serve',
  };
};

export default defineConfig(({ mode, command }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  const { buildConf } = GlobalConfig[process.env.VITE_RUNNING_SYSTEM];
  const base =
    process.env.VITE_ENV === 'dev' ? buildConf.devBaseCDN : buildConf.baseCDN;

  return defineConfig({
    plugins: [
      react({
        jsxImportSource: '@emotion/react',
      }),
      htmlPlugin({
        // favicon: './logo.svg',
        title: buildConf.title,
        favicon: buildConf.favicon,
        links: buildConf.cssCDN.map((href: string) => ({
          rel: 'stylesheet',
          href,
        })),
      }),
      visualizer({
        // open: true, // 在构建完成后自动打开分析报告
        // gzipSize: true, // 显示 gzip 压缩后的大小
      }),
      jsxPlusPlugin(),
      /**
       * whistle需要增加相应代理
       * https://0.0.0.0:5678 http://0.0.0.0:5678 resCors://enable
       */
      materialHMRPlugin({
        interval: 3000,
      }),
      {
        name: 'change-sourcemap-root',
        generateBundle: (_, bundle) => {
          Object.keys(bundle).filter((name) => {
            if (name.endsWith('.js.map')) {
              bundle[name].fileName = path.join(
                __dirname,
                './sourcemaps',
                name.replace('assets/', '')
              );
              return true;
            }
            return false;
          });
        },
      },
    ],
    resolve: {
      alias: {
        '@': '/src/',
        '@routes': buildConf.routesPath,
        '@lib-dist': '/lib-dist/',
        // 设置编辑器别名，确保包都指向管理台的node_modules，保证单例在一套依赖中
        // '@tencent/pagedoo-editor/dist': path.resolve(
        //   __dirname,
        //   '../../../pagedoo-editor/packages/editor/dist'
        // ),
        // '@tencent/pagedoo-editor': path.resolve(
        //   __dirname,
        //   '../../../pagedoo-editor/packages/editor/src'
        // ),
        // '@tencent/pagedoo-library': path.resolve(
        //   __dirname,
        //   './node_modules/@tencent/pagedoo-library'
        // ),
        // '@tencent/eventbus': path.resolve(
        //   __dirname,
        //   './node_modules/@tencent/eventbus'
        // ),
        // '@tencent/gems-shared': path.resolve(
        //   __dirname,
        //   './node_modules/@tencent/gems-shared'
        // ),
        // '@tencent/gems-renderer-core': path.resolve(
        //   __dirname,
        //   './node_modules/@tencent/gems-renderer-core'
        // ),
        // '@tencent/gems-preview': path.resolve(
        //   __dirname,
        //   './node_modules/@tencent/gems-preview'
        // ),
        // '@tencent/pagedoo-time-navigator': path.resolve(
        //   __dirname,
        //   './node_modules/@tencent/pagedoo-time-navigator'
        // ),
      },
    },
    build: {
      outDir: './dist',
      rollupOptions: {
        input: { index: 'index.html', preview: 'preview.html' },
      },
    },
    base,
    // `${process.env.VITE_UPLOAD_CDN_BASE_URL}/dman/` ??
    // '//avatarcdn.pay.qq.com/dman/',
    css: {
      modules: {
        localsConvention: 'camelCase',
        generateScopedName: '[local]_[hash:base64:5]',
      },
      preprocessorOptions: {
        less: {
          modifyVars: {
            // 如需自定义组件其他 token, 在此处配置
          },
          charset: false,
          javascriptEnabled: true,
          webpackImporter: true,
          plugins: [
            new LessPluginModuleResolver({
              alias: {
                '~': '',
              },
            }),
          ],
        },
        scss: {
          javascriptEnabled: true,
        },
      },
    },
    define: {
      'process.env': {},
    },
    server: {
      allowedHosts: [
        'admuse.qq.com',
        'avatar.pay.qq.com',
        'dev-avatar.pay.qq.com',
      ],
      host: '0.0.0.0',
      port: 8090,
      cors: true,
      strictPort: true,
      allowedHosts: ['avatar.pay.qq.com'],
      proxy: {
        // '/api': {
        //   target: 'http://localhost:8088',
        //   changeOrigin: true,
        // },
      },
      // fs: {
      //   allow: [
      //     // 允许访问项目根目录之外的特定目录
      //     path.resolve(__dirname, '../../../pagedoo-editor/packages/editor'),
      //     __dirname
      //   ]
      // }
    },
  });
});
