import { useSearchParams } from 'react-router-dom';

export default function useSelectTab(
  primaryKey: string,
  defaultValue = ''
): [string, (value: string) => void] {
  const [params, setParams] = useSearchParams({
    [primaryKey]: defaultValue,
  });

  return [
    params.get(primaryKey) || defaultValue,
    (value: string) =>
      setParams((p) => {
        p.set(primaryKey, value);
        return p;
      }),
  ];
}
