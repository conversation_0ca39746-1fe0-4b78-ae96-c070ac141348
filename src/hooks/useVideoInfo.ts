import { useEffect } from 'react';
import { useRequest } from 'ahooks';
import { getVideoFirstFrame } from '@/utils/getVideoFirstFrame';

interface VideoInfo {
  status: 'error' | 'success';
  url: string;
  info?: {
    duration?: number;
    width?: number;
    height?: number;
    direction?: 'horizontal' | 'vertical';
    size?: number;
  };
}

interface UseVideoInfoOptions {
  videoUrl: string | null;
  options?: {
    /**
     * 获取首帧，同时也会返回元数据
     */
    fetchFirstFrame?: boolean;
    /**
     * 获取视频大小，依赖服务端配置，暂不可用
     * @deprecated
     */
    fetchSize?: boolean;
    /**
     * 只获取视频元数据
     */
    fetchBaseInfo?: boolean;
    // /**
    //  * 是否自动上传封面图
    //  */
    // autoUpload?: boolean;
  };
}

export async function fetchVideoMetadata(videoUrl: string): Promise<{
  duration: number;
  width: number;
  height: number;
  direction: 'horizontal' | 'vertical';
}> {
  const video = document.createElement('video');
  video.setAttribute('crossorigin', 'anonymous');
  video.setAttribute('src', videoUrl);
  video.setAttribute('muted', 'muted');
  video.muted = true;
  video.volume = 0;

  await new Promise<void>((resolve, reject) => {
    video.addEventListener('loadedmetadata', () => resolve());
    video.addEventListener('error', reject);
  });

  return {
    duration: video.duration,
    width: video.videoWidth,
    height: video.videoHeight,
    direction: video.videoWidth > video.videoHeight ? 'horizontal' : 'vertical',
  };
}

/**
 * 跨域问题
 * @param videoUrl
 * @returns
 */
async function fetchVideoSize(videoUrl: string): Promise<number> {
  const response = await fetch(videoUrl, {
    method: 'GET',
    headers: {
      Range: 'bytes=0-0', // 只请求第一个字节
    },
  });

  const contentRange = response.headers.get('Content-Range');
  console.log('!!!!cont', contentRange);
  if (contentRange) {
    const sizeMatch = contentRange.match(/bytes \d+-\d+\/(\d+)/);
    console.log('!!!sizeMatch', sizeMatch, contentRange);
    if (sizeMatch) {
      return parseInt(sizeMatch[1], 10);
    }
  }

  return 0;
}

/**
 * 获取视频信息
 * @param param0
 * @returns
 */
export function useVideoInfo({ videoUrl, options }: UseVideoInfoOptions) {
  const {
    fetchFirstFrame = false, // 默认值设为 false
    fetchSize = false,
    fetchBaseInfo = false,
  } = options || {};

  const fetchVideoInfo = async () => {
    if (!videoUrl) return;
    try {
      let firstFrameUrl = '';
      let baseInfo: Partial<VideoInfo['info']> = {};

      if (fetchFirstFrame) {
        // 获取视频第一帧及基本信息
        const firstFrame = await getVideoFirstFrame({ videoUrl });
        if (firstFrame.status !== 'success') {
          throw new Error('Failed to fetch video first frame');
        }
        firstFrameUrl = firstFrame.url;
        baseInfo = firstFrame.info || {};
      }

      // 如果不获取首帧封面，只获取视频元数据
      if (fetchBaseInfo && !fetchFirstFrame) {
        // 获取视频元数据
        baseInfo = await fetchVideoMetadata(videoUrl);
      }

      let size = 0;
      if (fetchSize) {
        size = await fetchVideoSize(videoUrl);
      }

      return {
        status: 'success',
        url: firstFrameUrl,
        info: {
          ...baseInfo,
          size: fetchSize ? size : undefined,
        },
      };
    } catch (error) {
      console.error('Error fetching video info:', error);
      throw error; // 重新抛出错误，以便 useRequest 捕获
    }
  };

  const {
    data: videoInfo,
    loading,
    error,
    run,
  } = useRequest(fetchVideoInfo, {
    manual: true,
    onError: (err) => {
      console.error('useRequest encountered an error:', err);
      // 在这里可以添加更多的错误处理逻辑
    },
  });

  useEffect(() => {
    run();
  }, [videoUrl, fetchFirstFrame, fetchSize, run]);

  return { videoInfo, loading, error };
}
