import { useEffect, useMemo } from 'react';
import { useLocalStorageState, useMemoizedFn } from 'ahooks';

export const ONE_DAY_IN_MS = 2 * 60 * 60 * 1000; // 24小时的毫秒数

export const useExpiringLocalStorageState = <T>(
  key: string,
  options: { defaultValue: T; listenStorageChange?: boolean; expiry?: number }
) => {
  const { defaultValue, listenStorageChange, expiry } = options;

  const [state, setState] = useLocalStorageState<{ value: T; expiry?: number }>(
    key,
    {
      defaultValue: {
        value: defaultValue,
        expiry: Date.now() + (expiry || ONE_DAY_IN_MS),
      },
      listenStorageChange,
    }
  );

  const isExpired = useMemo(() => {
    return state?.expiry ? Date.now() > state.expiry : false;
  }, [state?.expiry]);

  useEffect(() => {
    if (isExpired) {
      localStorage.removeItem(key);
      setState({
        value: defaultValue,
        expiry: Date.now() + (expiry || ONE_DAY_IN_MS),
      });
    }
  }, [isExpired, key, defaultValue, expiry, setState]);

  const setExpiringState = useMemoizedFn((value: T) => {
    setState({ value, expiry: Date.now() + (expiry || ONE_DAY_IN_MS) });
  });
  return [state?.value, setExpiringState] as const;
};
