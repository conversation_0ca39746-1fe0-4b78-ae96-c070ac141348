// src/hooks/useMove.ts
import { useLatest, useMemoizedFn } from 'ahooks';
import { direction } from 'html2canvas/dist/types/css/property-descriptors/direction';
import { RefObject, useState, useCallback, useEffect, useRef } from 'react';
export enum DIRECTION {
  HORIZON = 'horizon',
  VERTICAL = 'vertical',
}
interface IMoveOption {
  // 拖拽前
  onDragStart?: () => void;
  // 拖拽后
  onDragEnd?: () => void;
  // 移动方向，不传就是都可以
  direction?: DIRECTION[];
  // 安全区
  safeRange?: [number, number];
}
export const useMove = (
  el: RefObject<HTMLDivElement>,
  {
    direction = [DIRECTION.HORIZON, DIRECTION.VERTICAL],
    onDragEnd,
    onDragStart,
    safeRange = [80, 0],
  }: IMoveOption
) => {
  const [screenRect, setScreenRect] = useState({
    screenHeight: window.innerHeight,
    screenWidth: window.innerWidth,
  });
  const enableClickRef = useRef(true);
  // 实时更新窗口大小数值
  useEffect(() => {
    const handleResize = () => {
      setScreenRect({
        screenHeight: window.innerHeight,
        screenWidth: window.innerWidth,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  // 确保移动时不超出安全区域
  const safeClientX = useMemoizedFn((clientX: number): number => {
    if (clientX < safeRange[1]) return safeRange[1];
    if (clientX > screenRect.screenWidth - safeRange[1])
      return screenRect.screenWidth - safeRange[1];
    return clientX;
  });

  // 确保移动时不超出安全区域
  const safeClientY = useMemoizedFn((clientY: number): number => {
    if (clientY < safeRange[0]) return safeRange[0];
    if (clientY > screenRect.screenHeight - safeRange[0])
      return screenRect.screenHeight - safeRange[0];
    return clientY;
  });

  // transform 偏移量
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const latestIsDraggingRef = useLatest(isDragging);
  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (onDragStart) {
        onDragStart();
      }
      enableClickRef.current = true;
      const clickTime = e.timeStamp;
      // 最初的在浏览器的绝对位置
      const mouseDownY = e.clientY;
      const startX = e.clientX - position.x;
      const startY = e.clientY - position.y;
      const handleMouseMove = (event: MouseEvent) => {
        setIsDragging(true);
        // 是否移动x,y位置看入参
        const horizon = direction.includes(DIRECTION.HORIZON);
        const vertical = direction.includes(DIRECTION.VERTICAL);
        const position = {
          x: horizon ? safeClientX(event.clientX) - startX : 0,
          y: vertical ? safeClientY(event.clientY) - startY : 0,
        };
        setPosition(position);
      };

      const handleMouseUp = (event: MouseEvent) => {
        setIsDragging(false);
        if (
          event.timeStamp - clickTime > 300 ||
          Math.abs(event.clientY - mouseDownY) > 10
        )
          enableClickRef.current = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        event.stopPropagation();
        if (onDragEnd) {
          onDragEnd();
        }
      };

      // react合成事件的机制，绑定到document上
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      e.target.addEventListener(
        'click',
        (event) => {
          if (!enableClickRef.current) {
            event.stopPropagation();
          }
        },
        true
      );
      e.stopPropagation();
      e.preventDefault();
    },
    [
      onDragStart,
      position.x,
      position.y,
      direction,
      safeClientX,
      safeClientY,
      onDragEnd,
    ]
  );

  return {
    onMouseDown: handleMouseDown,
    isDragging,
    style: {
      transform: `translate(${position.x}px, ${position.y}px)`,
    },
  };
};
