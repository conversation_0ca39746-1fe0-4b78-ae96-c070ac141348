import { useInterval, useMemoizedFn, useUnmount } from 'ahooks';
import { useState } from 'react';

/**
 * 提供
 * @param fn
 * @param delay
 * @returns
 */
export const useIntervalUpdate = (
  fn: () => void,
  delay?: number,
  options?: {
    immediate?: boolean;
  }
) => {
  const [interval, setInterval] = useState<number | undefined>(delay);
  const clear = useInterval(fn, interval, options);

  useUnmount(() => {
    clear();
  });

  const updateInterval = useMemoizedFn((delay: number | undefined) => {
    setInterval(delay);
  });

  const stopInterval = useMemoizedFn(() => {
    setInterval(0);
  });

  return { updateInterval, stopInterval };
};
