import { ILoginSession, LoginStateAtom } from '@/model/login';
import { IUserInfo<PERSON>tom, UserInfoAtom } from '@/model/user';
import { NeedLoginError } from '@/utils/login/login-errors';
import {
  createLoginHandle,
  IGoLogoutOptions,
  IGotoLoginOptions,
} from '@/utils/login/sdk';
import to from 'await-to-js';
import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';
import { useRecoilState } from 'recoil';

export type LoginContextType = ReturnType<typeof useLogin>;
export const LoginContext = createContext<LoginContextType | null>(null);

export function useLogin() {
  const [loginState, setLoginState] = useRecoilState(LoginStateAtom);
  const [userInfo, setUserInfo] = useRecoilState(UserInfoAtom);
  const [isFetching, setIsFetching] = useState(false);
  const loginInstRef = useRef(createLoginHandle());
  const navigate = useNavigate();
  if (import.meta.env.VITE_ENV === 'dev') {
    (window as any).__setLoginState = setLoginState;
  }
  const refresh = useCallback(
    async (options?: { noCache?: boolean }) => {
      setIsFetching(true);
      const [err, info] = await to(
        loginInstRef.current?.fetchLoginInfo(options)
      );
      setIsFetching(false);
      if (err || !info) {
        setLoginState(undefined);
        setUserInfo(undefined);
        throw new NeedLoginError(loginInstRef.current.getLoginType());
      }
      setLoginState({
        ...info.loginSession,
      });
      setUserInfo({
        avatar: info.loginUserInfo.avatar,
        nickname: info.loginUserInfo.nickName,
        adExtend: info.loginUserInfo.adExtend,
        pagedooExtend: info.loginUserInfo.pagedooExtend,
      });
      return {
        loginSession: info.loginSession,
        loginUserInfo: info.loginUserInfo,
      };
    },
    [setLoginState, setUserInfo]
  );

  const goToLogin = useCallback(
    async (options?: IGotoLoginOptions) => {
      await loginInstRef.current.gotoLogin({ ...options, navigate });
    },
    [navigate]
  );

  const getLoginInfo = useCallback((): {
    loginSession: ILoginSession | undefined;
    userInfo: IUserInfoAtom | undefined;
  } => {
    return {
      loginSession: loginState,
      userInfo,
    };
  }, [loginState, userInfo]);

  const goToLoginOut = useCallback(
    async (options?: IGoLogoutOptions) => {
      setLoginState(undefined);
      setUserInfo(undefined);
      console.log('[LoginHook] ', 'before logout');
      await loginInstRef.current.goLoginOut(options);
    },
    [setLoginState, setUserInfo]
  );
  useEffect(() => {
    const unsubscribe = loginInstRef.current.onRefresh(refresh);
    return () => {
      unsubscribe();
    };
  }, [refresh]);

  // useMount(() => {
  //   refresh();
  // });
  return useMemo(
    () => ({
      isFetchingLogin: isFetching,
      fetchLoginInfo: refresh,
      goToLogin,
      getLoginInfo,
      goToLoginOut,
    }),
    [isFetching, refresh, goToLogin, getLoginInfo, goToLoginOut]
  );
}

// 开发环境模拟登录态
// if (import.meta.env.VITE_ENV === 'dev') {
//   const loginSession = {
//     offer_id: '800000008',
//     // wx_appid: 'wx951bdcac522929b6',
//     session_id: 'hy_gameid',
//     session_type: 'st_dummy',
//     openid: 'openid',
//     openkey: 'openkey',
//     strinifyLoginInfo: '',
//   };
//   loginSession.strinifyLoginInfo = JSON.stringify(loginSession);
//   setLoginState(loginSession);
//   setUserInfo({
//     avatar: '',
//     nickname: 'dev',
//   });
//   return;
// }
