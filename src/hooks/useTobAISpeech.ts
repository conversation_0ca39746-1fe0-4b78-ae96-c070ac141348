import { useCallback, useEffect, useRef, useState } from 'react';
import { DigitalManProc } from '@/pb/pb';
import { RespError } from '@/pb/config';
import { useMemoizedFn } from 'ahooks';
import { PromiseType } from '@/utils/type-util';

type Req = Parameters<(typeof DigitalManProc)['ScriptGenerate']>[0];
type QueryResp = PromiseType<
  ReturnType<(typeof DigitalManProc)['GetLiveScript']>
>;
export type ScriptType = QueryResp['script_list'][0];
// interface IUseGenerateSpeechOptions {
//   speechText: string;
//   scriptId: string;
//   liveID: string;
// }
export const useGenerateAISpeech = () => {
  const [generating, setIsGenrating] = useState(false);
  const [scriptList, setScriptList] = useState<QueryResp['script_list']>([]);
  const [isGenerated, setIsGenerated] = useState(false);
  const cancelQueryRef = useRef<() => void>();

  const queryResult = useCallback(async (liveID: string) => {
    try {
      const resp = await DigitalManProc.GetLiveScript({
        live_id: liveID,
        is_preview: true,
      });
      return {
        needRetry: resp.script_list.length === 0,
        list: resp.script_list,
      };
    } catch (e) {
      return {
        needRetry: false,
        list: [],
        err: e as RespError,
      };
    }
  }, []);
  const waitForResult = useCallback(
    (options: { liveID: string; limitCount: number }) => {
      let timeout: any = null;
      let stopped = false;
      const { liveID, limitCount } = options;
      const start = async () => {
        let current = 0;
        try {
          const list = await new Promise<QueryResp['script_list']>(
            (resolve, reject) => {
              const query = async () => {
                if (stopped) return resolve([]);
                if (limitCount > 0 && current >= limitCount)
                  return reject(new Error('查询话术超时'));
                const { needRetry, list, err } = await queryResult(liveID);
                if (needRetry) {
                  current += 1;
                  // 10秒轮询一次
                  timeout = setTimeout(query, 10000);
                  return;
                }
                if (err) return reject(err);
                resolve(list);
              };
              query();
            }
          );
          return list;
        } catch (e) {
          throw e;
        }
      };
      const stop = () => {
        clearTimeout(timeout);
        stopped = true;
      };
      return {
        start,
        stop,
      };
    },
    [queryResult]
  );
  const generate = useCallback(
    async (
      req: Req,
      options?: {
        generateOnly?: boolean;
      }
    ): Promise<ScriptType[]> => {
      setIsGenrating(true);
      // TODO: MOCK START
      // await new Promise((resolve) => setTimeout(resolve, 2000));
      // setIsGenrating(false);

      // const list = [
      //   {
      //     live_id: 'mock',
      //     script_content:
      //       '春风轻拂，万物苏醒。桃花嫣红，梨花带雨，春天如一位多情的诗人，用她那细腻的笔触，在大地上描绘出一幅幅美丽的画卷。此刻，我愿用千言万语，去追寻春天的足迹，去感受她的诗意与浪漫。“等闲识得东风面，万紫千红总是春。”朱熹的这句诗，恰到好处地描绘了春天的绚烂多彩。你看，那河边的柳树，不是已经垂下了绿丝绦吗？那田野上的小麦，不是已经露出了翠绿的尖尖角吗？它们都在春风的吹拂下，欢快地舞蹈着，歌唱着。春天，是一位博爱的画家。她用最美的色彩，为大地增辉；她用最柔和的线条，为万物勾勒轮廓。在她的笔下，山青了，水秀了，花开了，鸟鸣了。一切都变得那么春天，是一位博爱的画家。她用最美的色彩，为大地增辉；她用最柔和的线条，为万物勾勒轮廓。在她的笔下，山青了，水秀了，花开了，鸟鸣了。一切都变得那么春天，是一位博爱的画家。她用最美的色彩，为大地增辉；她用最柔和的线条，为万物勾勒轮廓。在她的笔下，山青了，水秀了，花开了，鸟鸣了。一切都变得那么春天，是一位博爱的画家。她用最美的色彩，为大地增辉；她用最柔和的线条，为万物勾勒轮廓。在她的笔下，山青了，水秀了，花开了，鸟鸣了。一切都变得那么春天，是一位博爱的画家。她用最美的色彩，为大地增辉；她用最柔和的线条，为万物勾勒轮廓。在她的笔下，山青了，水秀了，花开了，鸟鸣了。一切都变得那么春天，是一位博爱的画家。她用最美的色彩，为大地增辉；她用最柔和的线条，为万物勾勒轮廓。在她的笔下，山青了，水秀了，花开了，鸟鸣了。一切都变得那么春天，是一位博爱的画家。她用最美的色彩',
      //     script_id: 'mock_script_id',
      //   },
      // ];
      // setIsGenerated(list.length > 0);
      // setScriptList(list);
      // return list;
      // MOCK END
      await DigitalManProc.ScriptGenerate(req);
      if (options?.generateOnly) {
        setIsGenrating(false);
        return [];
      }
      // 进入轮询查询
      const { start, stop } = waitForResult({
        limitCount: 20,
        liveID: req.live_id || '',
      });
      cancelQueryRef.current = () => {
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        cancelQueryRef.current = () => {};
        stop();
      };
      try {
        const list = await start();
        // TODO： 产品交互上这里暂时只取第一个
        setScriptList(list);
        setIsGenerated(list.length > 0);
        return list;
      } catch (e) {
        throw e;
      } finally {
        setIsGenrating(false);
      }
    },
    [waitForResult]
  );
  const cancel = useMemoizedFn(() => {
    cancelQueryRef.current?.();
  });

  return {
    isGenerating: generating,
    isGenerated,
    scriptList,
    generate,
    cancel,
  };
};
