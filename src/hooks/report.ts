import { useCallback, useEffect, useMemo } from 'react';
import { IReportSDK, ReportSDK } from '@tencent/gems-ability';
import { useRecoilState } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { useMemoizedFn } from 'ahooks';
import EventBus from '@tencent/eventbus';
import { IReportType, IBaseReportType } from '@tencent/pagedoo-editor';
import type { DomAction } from '@tencent/dom-action-sdk';

import { useLocation } from 'react-router-dom';
import { routes } from '@routes';

const domAction = new EventBus();

const eventMap: {
  [x in Parameters<DomAction<IReportType>['emit']>[0]]?: string;
} = {
  PageVisible: 'exposure_page',
  PageInvisible: 'leave_page',
  // PageScroll: 'slide_down_page',
  ItemVisible: 'exposure_module',
  ItemClick: 'click_module',
} as const;

export function useReport() {
  return domAction as unknown as DomAction<{
    [x in keyof (IReportType & IBaseReportType)]: (IReportType &
      IBaseReportType)[x] & {
      meta_data?: {
        Script_id: string;
        Action?:
          | 'Download'
          | 'Exposure'
          | 'Submit'
          | 'Cancel'
          | 'Input'
          | 'Play'
          | 'Positive'
          | 'Negative';
      };
    };
  }>;
}

function matchPageId(pathname: string) {
  const find = routes.find((router) => router.path === pathname);
  if (find?.handle?.reportPageId) {
    return find.handle.reportPageId;
  }
  return pathname;
}

export function useInitReport() {
  // 数据上报
  const [loginState] = useRecoilState(LoginStateAtom);
  const reactLocation = useLocation();

  const reportParams = useMemo<IReportSDK>(
    () => ({
      userInfo: {
        app_id: import.meta.env.VITE_APP_ID,
        user_id: `${loginState?.openid || ''}`,
      },
      chan: {
        chan_share: 'false',
        chan_source: '',
        chan_custom_id: '',
        chan_custom_desc: '',
      },
      pagedoo: {
        pagedoo_type: '',
        pagedoo_id: matchPageId(reactLocation.pathname),
        pagedoo_name: 'avatar_name',
      },
      api: '//pagedooapi.pay.qq.com',
      // 透传字段
      metaData: {},
      // p.meta.isEditor
      disableLog: false,
    }),
    [loginState, reactLocation.pathname]
  );

  // 初始化数据上报能力
  const reportInstance = useMemo(
    () => new ReportSDK(reportParams),
    [reportParams]
  );

  reportInstance.init({
    page_id: matchPageId(reactLocation.pathname),
    page_title: '',
    page_result_info: '',
  });

  const domAction = useReport();
  const getReportInstance = useMemoizedFn(() => reportInstance);

  const handleEvent = useCallback(
    (event: Parameters<DomAction<IReportType>['emit']>[0]) =>
      domAction.on(event, (data) => {
        console.log('on report', data);
        const {
          stay_time,
          browse_depth,
          element_type,
          complete,
          module_id,
          goods_id,
          goods_type,
          components_id,
          components_type,
          text,
          extra_event_parameter,
          activity_id,
          content,
          meta_data,
        } = data as any;
        getReportInstance().log({
          event_code: eventMap[event] as string,
          event_parameter: {
            stay_time,
            browse_depth,
            element_type,
            complete,
            ...extra_event_parameter,
          },
          module_id,
          module_parameter: {
            goods_id,
            goods_type,
            components_id,
            components_type,
            text: text || content,
            activity_id,
            ...meta_data,
          },
        });
      }),
    [domAction, getReportInstance]
  );

  const eventQueue = useMemo(
    () =>
      Object.keys(eventMap).map((i) => handleEvent(i as keyof typeof eventMap)),
    [handleEvent]
  );
  useEffect(
    () => () => {
      eventQueue.forEach((i) => i());
    },
    [eventQueue]
  );
}
