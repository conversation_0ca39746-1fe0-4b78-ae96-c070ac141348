import { useRef } from 'react';
import { getFormattedAudioDurations } from '@/utils/audio';
import { isString } from 'lodash-es';

/**
 * 直接传字符串 或者字符串数组
 * @returns
 */
export default function useAudioDuration() {
  const ref = useRef(new Map());

  return async (urls: string[] | string) => {
    if (isString(urls)) {
      const uncached = !ref.current.has(urls);
      if (uncached) {
        const durations = await getFormattedAudioDurations([urls]);
        ref.current.set(urls, durations[0]);
      }
      return ref.current.get(urls);
    }

    const uncached = urls.filter((url) => !ref.current.has(url));

    if (uncached.length > 0) {
      const durations = await getFormattedAudioDurations(uncached);

      uncached.forEach((u, i) => ref.current.set(u, durations[i]));
    }

    return urls.map((u) => ref.current.get(u) || 0);
  };
}
