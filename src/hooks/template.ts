import { TemplateItem } from '@/components/TemplateList';
import { RespType } from '@/pb/config';
import { ResourceSvr } from '@/pb/pb';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';
import { CONTENT_TYPE_MAP } from '@/const/common';

export function useTemplateSelectorOptions(
  content_list_type: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value']
) {
  // 获取筛选器列表
  const { data: selectorRes } = useRequest(
    async () => {
      const map = {
        [CONTENT_TYPE_MAP.Video.value]: 1,
        [CONTENT_TYPE_MAP.Live.value]: 2,
      };
      return ResourceSvr.GetVideoTemplateSelector({
        content_list_type: map[content_list_type],
      });
    },
    {
      cacheKey: `QuerySelectorOption-${content_list_type}`,
    }
  );
  // 筛选器列表
  const selectorOption = useMemo(() => {
    if (!selectorRes) return;
    return {
      label: selectorRes.selector_label_name,
      selectorId: selectorRes.selector_id,
      options: selectorRes.content_selector_list.map((i) => ({
        label: i.selector_name,
        value: i.selector_value,
        childenSelector: i.selector_class_list
          .map((i) => ({
            label: i.selector_label_name,
            selectorId: i.selector_id,
            options: i.selector_item_list.map((i) => ({
              label: i.selector_name,
              value: i.selector_value,
            })),
          }))
          .filter((i, index) => index !== 0),
      })),
    };
  }, [selectorRes]);
  return {
    selectorOption,
  };
}

export interface TemplateDataItem extends TemplateItem {
  original_data: RespType<
    typeof ResourceSvr.GetVideoTemplateList
  >['video_template_list'][0];
}

export function useGetTemplateList(
  selectorValue: Record<string, string>,
  pageNum: number,
  pageSize: number,
  contentListType: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'],
  searchKey?: string
) {
  // 获取模版列表
  const request = useRequest(
    async () => {
      if (!Object.entries(selectorValue).length)
        return Promise.resolve(undefined);
      const map = {
        [CONTENT_TYPE_MAP.Video.value]: 1,
        [CONTENT_TYPE_MAP.Live.value]: 2,
      };
      return ResourceSvr.GetVideoTemplateList({
        selector_query_list: Object.entries(selectorValue).map(([k, v]) => ({
          selector_id: k,
          selector_value: v,
        })),
        page_num: pageNum,
        page_size: pageSize,
        search_key: searchKey,
        content_list_type: map[contentListType],
      });
    },
    {
      refreshDeps: [pageNum, pageSize, selectorValue, searchKey],
    }
  );
  // 模版列表
  const list: TemplateDataItem[] = useMemo(() => {
    return (request.data?.video_template_list || []).map((i) => ({
      id: i.video_template_id,
      name: i.video_template_name,
      poster_url: i.video_template_pic_url,
      original_data: i,
    }));
  }, [request.data]);
  return {
    request,
    list,
    count: request.data?.query_count || 0,
    loading: request.loading,
  };
}
