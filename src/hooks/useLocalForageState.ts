import { useState, useCallback, useEffect } from 'react';
import localforage from 'localforage';
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks';

// 类型定义：设置状态的函数类型
type SetValue<T> = React.Dispatch<React.SetStateAction<T>>;

// 类型定义：选项参数接口
interface UseLocalForageStateOptions<T> {
  defaultValue?: T; // 初始值，可以不传
  listenForChanges?: boolean; // 是否启用实时监听变化，默认为 false
}

// 自定义 Hook：使用 localForage 进行状态管理
function useLocalForageState<T>(
  key: string, // 存储键
  options: UseLocalForageStateOptions<T> = {} // 选项参数
): [T | undefined, SetValue<T | undefined>, () => Promise<void>] {
  const { defaultValue, listenForChanges } = options;
  const [state, setState] = useState<T | undefined>(defaultValue);
  const [loaded, setLoaded] = useState(false);

  // 加载状态：在组件挂载时从 localForage 中加载状态
  useEffect(() => {
    const loadState = async () => {
      try {
        const storedValue = await localforage.getItem<T>(key);
        if (storedValue !== null) {
          setState(storedValue);
        } else if (defaultValue !== undefined) {
          setState(defaultValue);
        }
        setLoaded(true);
      } catch (error) {
        console.error('Error loading state from localForage', error);
        setLoaded(true);
      }
    };
    loadState();
  }, [key, defaultValue]);

  // 保存状态：在状态更新时保存到 localForage
  useDeepCompareEffect(() => {
    if (loaded) {
      const saveState = async () => {
        try {
          console.log('!!!!,setItem', state);
          await localforage.setItem(key, state);
          // 触发自定义事件
          window.dispatchEvent(
            new CustomEvent(`localforage-${key}-change`, { detail: state })
          );
        } catch (error) {
          console.error('Error saving state to localForage', error);
        }
      };
      saveState();
    }
  }, [key, state, loaded]);

  // 移除状态：提供 removeItem 方法
  const removeItem = useCallback(async () => {
    try {
      await localforage.removeItem(key);
      setState(defaultValue);
      // 触发自定义事件
      window.dispatchEvent(
        new CustomEvent(`localforage-${key}-change`, { detail: defaultValue })
      );
    } catch (error) {
      console.error('Error removing state from localForage', error);
    }
  }, [key, defaultValue]);

  // 包装 setState 以处理函数参数
  const setLocalForageState: SetValue<T | undefined> = useMemoizedFn(
    (value) => {
      setState((prevState) => {
        const newValue =
          typeof value === 'function'
            ? (value as (prevState: T | undefined) => T)(
                prevState ?? defaultValue
              )
            : value;
        return newValue;
      });
    }
  );

  const handleStorageChange = useMemoizedFn((event: CustomEvent) => {
    // 仅在事件的 detail 与当前 state 不同时更新状态，避免重复更新
    if (event.detail !== state) {
      setState(event.detail);
    }
  });

  // 实时监听 localForage 中的变化
  useEffect(() => {
    if (listenForChanges) {
      window.addEventListener(
        `localforage-${key}-change`,
        handleStorageChange as EventListener
      );

      return () => {
        window.removeEventListener(
          `localforage-${key}-change`,
          handleStorageChange as EventListener
        );
      };
    }
  }, [handleStorageChange, key, listenForChanges, state]);

  return [state, setLocalForageState, removeItem];
}

export default useLocalForageState;
