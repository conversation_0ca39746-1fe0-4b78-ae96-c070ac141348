/**
 * <AUTHOR>
 * @date 2024/5/9 下午3:33
 * @desc 论询问npc状态的hook
 */
import { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { NpcScriptSvr } from '@/pb/pb';
import { MessagePlugin } from 'tdesign-react';

// 1:任务完成
// 2:执行中
// -1:无效
// 3:手动停止 */
export const enum IntervalNpcStatus {
  FINISH = 1,
  EXECUTING = 2,
  INVALID = -1,
  STOP = 3,
  Fail,
}

export const NPC_STEP = {
  PARSE_PPT: 'input_content_prompt',
  GENERATE_SCRIPT: 'script_generate',
};

export const useIntervalNpcStatus = ({
  // jobId,
  interval = 1000,
  autoStop = true,
  onJobFinish,
  onJobError,
}: {
  // jobId: string;
  interval?: number;
  autoStop?: boolean;
  onJobFinish?: () => void;
  onJobError?: () => void;
}) => {
  const [percent, setPercent] = useState(0);
  const [isPolling, setIsPolling] = useState(false);
  // 1:任务完成
  // 2:执行中
  // -1:无效
  // 3:手动停止 */
  const [status, setStatus] = useState<IntervalNpcStatus>(
    IntervalNpcStatus.INVALID
  );
  const [jobId, setJobId] = useState('');
  const [stepName, setStepName] = useState('');

  const { run, loading } = useRequest(
    () => {
      return NpcScriptSvr.QueryNpcJobStatus({
        job_id: jobId,
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        // 返回的是浮点数，需要转换为百分比，保留两位小数
        const percent = Number((response.progress_percent * 100).toFixed(2));
        setPercent(percent > 100 ? 100 : percent);
        setStatus(response.job_status);
        setStepName(response.stage_name);
        // 没有执行状态就停止轮询
        if (response.job_status !== IntervalNpcStatus.EXECUTING && autoStop) {
          setIsPolling(false);
        }
      },
    }
  );

  const pollingRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    if (isPolling) {
      pollingRef.current = setInterval(() => {
        run();
      }, interval);
    } else {
      pollingRef.current && clearInterval(pollingRef.current);
    }

    return () => {
      pollingRef.current && clearInterval(pollingRef.current);
    };
  }, [run, interval, isPolling]);

  useEffect(() => {
    // 任务完成
    if (status === IntervalNpcStatus.FINISH) {
      onJobFinish?.();
    }
    if (
      status === IntervalNpcStatus.Fail ||
      status === IntervalNpcStatus.STOP
    ) {
      onJobError?.();
      // 重置状态
      setStatus(IntervalNpcStatus.INVALID);
      setPercent(0);
    }
  }, [onJobFinish, onJobError, status]);

  const startPolling = (jobId: string) => {
    if (!jobId) {
      void MessagePlugin.warning('未找到任务Id');
      return;
    }
    setJobId(jobId);
    // 重置状态
    setStatus(IntervalNpcStatus.INVALID);
    setPercent(0);
    // 开始
    setIsPolling(true);
  };

  const stopPolling = () => {
    setIsPolling(false);
  };

  return { stepName, status, percent, loading, startPolling, stopPolling };
};
