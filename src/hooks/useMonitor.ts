import { getMonitor } from '@/utils/monitor';
import { createContext, useContext } from 'react';

export interface IMonitorContextValue {
  monitor: ReturnType<typeof getMonitor> | null;
}

export const MonitorContext = createContext<IMonitorContextValue>({
  monitor: null,
});

export const useMonitor = () => {
  const context = useContext(MonitorContext);
  return {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    monitor: context.monitor!,
  };
};
