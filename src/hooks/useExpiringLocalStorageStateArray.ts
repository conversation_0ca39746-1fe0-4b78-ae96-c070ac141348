import { useEffect, useMemo } from 'react';
import {
  useDeepCompareEffect,
  useLocalStorageState,
  useMemoizedFn,
} from 'ahooks';

export const EXPIRY_TIME = 2 * 60 * 60 * 1000; // 24小时的毫秒数

interface ExpiringItem<T> {
  value: T;
  expiry: number;
}

interface ExpiringLocalStorageOptions<T> {
  defaultValue?: T;
  listenStorageChange?: boolean;
  expiry?: number;
}

export const useExpiringLocalStorageArrayState = <T>(
  key: string,
  options: ExpiringLocalStorageOptions<T[]>
) => {
  const { defaultValue, listenStorageChange, expiry } = options;

  const [state, setState] = useLocalStorageState<ExpiringItem<T>[]>(key, {
    defaultValue: [],
    listenStorageChange,
  });

  // 未过期的值
  const filteredState = useMemo(() => {
    if (!state) return [];
    return state.filter((item) => item.expiry > Date.now());
  }, [state]);

  useDeepCompareEffect(() => {
    // 如果有过期的值，那么就会不一样，重新 state 一下
    // FIXME: 只会在初始化的时候执行
    if (filteredState.length !== state?.length) {
      setState(filteredState);
    }
  }, [filteredState, state, setState]);

  /**
   * 暴露出去的 setState 方法，
   */
  const setExpiringArrayState = useMemoizedFn(
    (valueOrUpdater: T[] | ((prev: T[]) => T[])) => {
      const newValue =
        typeof valueOrUpdater === 'function'
          ? valueOrUpdater(filteredState.map((item) => item.value))
          : valueOrUpdater;

      const newArray = newValue.map((item) => ({
        value: item,
        expiry: Date.now() + (expiry || EXPIRY_TIME),
      }));
      setState(newArray);
    }
  );

  const currentState = useMemo(() => {
    return filteredState.map((item) => item.value);
  }, [filteredState]);

  return [currentState, setExpiringArrayState] as const;
};
