import { useMemoizedFn } from 'ahooks';
import { useState, useEffect, RefObject } from 'react';

interface UseInfiniteScrollOptions {
  containerRef: RefObject<HTMLElement>;
  threshold?: number;
}

interface UseInfiniteScrollResult<T> {
  data: T[];
  loading: boolean;
  noMore: boolean;
  loadMore: () => void;
  refresh: () => Promise<void>;
}

const useInfiniteScroll = <T>(
  fetchData: (page: number) => Promise<T[]>,
  { containerRef, threshold = 100 }: UseInfiniteScrollOptions
): UseInfiniteScrollResult<T> => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [noMore, setNoMore] = useState(false);
  const DEFAULT_SIZE = 20;

  const loadMore = useMemoizedFn(async () => {
    if (loading || noMore) return;
    setLoading(true);
    try {
      const newData = await fetchData(page);
      if (newData.length < DEFAULT_SIZE) {
        setNoMore(true);
      }
      setData((prevData) => [...prevData, ...newData]);
      setPage((prevPage) => prevPage + 1);
    } catch (error) {
      console.error('Error loading more data:', error);
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    const handleScroll = () => {
      const container = containerRef.current || document.documentElement;
      if (
        container.scrollHeight - container.scrollTop <=
        container.clientHeight + threshold
      ) {
        loadMore();
      }
    };

    const container = containerRef.current || window;
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [loadMore, containerRef, threshold]);

  const refresh = useMemoizedFn(async () => {
    setLoading(true);
    setPage(1);
    setNoMore(false);
    try {
      const newData = await fetchData(1);
      setData(newData);
      setPage(2);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    loadMore();
  }, [loadMore]);

  return { data, loading, noMore, loadMore, refresh };
};

export default useInfiniteScroll;
