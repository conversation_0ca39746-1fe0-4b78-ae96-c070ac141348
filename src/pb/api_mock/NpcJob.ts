/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type CreateNPCJObSyncRequest = {
  /**@name model_input_content
model_input_content  "{\"ppt_url\": \"xxxxxx\",\"page_url\": \"xxxxx\"}"*/
  model_input_content?: string
  /**@name npc_id
npcId pagedoo_0038_1（代表ppt生剧本）*/
  npc_id?: string
  /**@name job_id
job_id 任务id*/
  job_id?: string
}
export type CreateNPCJObSyncResponse = {
  /**@name model_return_content
脚本数据*/
  model_return_content: string
  /**@name code
错误码*/
  code: string
  /**@name message
错误信息*/
  message: string
}
/**@name CreateNPCJObSync
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateNPCJObSync = (
  props: ImplRequest<CreateNPCJObSyncRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.npc_ppt_scripts_svr.NpcPptScriptsSvr/CreateNPCJObSync",
    { model_return_content: "脚本数据", code: "错误码", message: "错误信息" },
    props,
  ) as ImplResponse<CreateNPCJObSyncResponse>
/**@name CreateNpcJobRequest
@required npc_id,input_content
@example
{
"npc_id": "app_test1",
"model_input_content": "https://channels-school.qq.com/shop/learning-center/index.html",
"create_user": "shannon",
"model_input_type": "docx",
"auto_create_script": false,
"npc_extend_config": "{\"content_type\":\"rule\"}"
}
@description 创建npc任务请求*/
export type CreateNpcJobRequest = {
  /**@name npc_id
@description npc_id
@example npc_id*/
  npc_id?: string
  /**@name model_input_content
@description 输入内容
@example https://channels-school.qq.com/shop/learning-center/index.html*/
  model_input_content?: string
  /**@name create_user
@description 创建人
@example shannon*/
  create_user?: string
  /**@name model_input_type
@description 模型输入类型
@example ppt/docx/html*/
  model_input_type?: string
  /**@name auto_create_script
@description 是否自动创建脚本
@example false*/
  auto_create_script?: boolean
  /**@name npc_extend_config
@description 扩展配置
@example {"content_type":"rule"}*/
  npc_extend_config?: string
  /**@name subject_parameters
@description 主题参数
@example ["剧本大标题", "剧本小标题"]*/
  subject_parameters?: string[]
}
/**@name CreateNpcJobResponse
@required code,message,data
@example
{
"code":"0",
"message":"ok",
"data": {
"job_id": "bcc09076-5748"
}
}
@description npc任务创建返回*/
export type CreateNpcJobResponse = {
  /**@name code
@description 结果码
@example 0*/
  code: string
  /**@name message
@description 结果信息
@example ok*/
  message: string
  /**@name data
@description 请求结果详情
@example {
"job_id": "bcc09076-5748"
}*/
  data: {
    /**@name job_id
@description job_id
@example bcc09076-5748*/
    job_id: string
  }
}
/**@name CreateNpcJob
@summary 创建npc任务
@description 创建npc任务，启动输入内容解析
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateNpcJob = (
  props: ImplRequest<CreateNpcJobRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.npc_ppt_scripts_svr.NpcPptScriptsSvr/CreateNpcJob",
    { code: "0", message: "ok", data: { job_id: "bcc09076-5748" } },
    props,
  ) as ImplResponse<CreateNpcJobResponse>
/**@name QueryNpcJobStatusRequest
@required job_id
@example
{
"job_id": "bcc09076-5748"
}
@description 查npc任务状态*/
export type QueryNpcJobStatusRequest = {
  /**@name job_id
@description job_id
@example bcc09076-5748*/
  job_id?: string
}
/**@name QueryNpcJobStatusResponse
@required code,message,data
@example
{
"code":"0",
"message":"ok",
"data": {
"job_id": "bcc09076-5748"
}
}
@description 查npc任务状态返回*/
export type QueryNpcJobStatusResponse = {
  /**@name code
@description 结果码
@example 0*/
  code: string
  /**@name message
@description 结果信息
@example ok*/
  message: string
  /**@name data
@description 请求结果详情
@example {
"job_id": "bcc09076-5748"
}*/
  data: {
    /**@name job_id
@description job_id
@example bcc09076-5748*/
    job_id: string
    /**@name job_stage
@description job_id
@example bcc09076-5748*/
    job_stage: string
    /**@name job_status
@description 任务状态
@example 0:初始状态; 10:传入内容预处理完成; 20:输入内容多模态处理完成；30:npc_script生成前置处理完成；40:npc脚本生成完成；50:后置处理剧本生成完成*/
    job_status: number
    /**@name progress_percent
@description 进度百分率
@example 0.28*/
    progress_percent: number
    /**@name stage_name
@description stage_name
@example 画面拆解中*/
    stage_name: string
  }
}
/**@name QueryNpcJobStatus
@summary 查npc任务状态
@description 查npc任务状态
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryNpcJobStatus = (
  props: ImplRequest<QueryNpcJobStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.npc_ppt_scripts_svr.NpcPptScriptsSvr/QueryNpcJobStatus",
    { code: "0", message: "ok", data: { job_id: "bcc09076-5748" } },
    props,
  ) as ImplResponse<QueryNpcJobStatusResponse>
/**@name StartNpcScriptsTaskRequest
@required job_id
@example
{
"job_id": "bcc09076-5748"
}
@description 查npc任务状态*/
export type StartNpcScriptsTaskRequest = {
  /**@name job_id
@description job_id
@example bcc09076-5748*/
  job_id?: string
}
/**@name StartNpcScriptsTaskResponse
@required code,message,data
@example
{
"code":"0",
"message":"ok",
"data": {
"job_id": "bcc09076-5748"
}
}
@description 查npc任务状态返回*/
export type StartNpcScriptsTaskResponse = {
  /**@name code
@description 结果码
@example 0*/
  code: string
  /**@name message
@description 结果信息
@example ok*/
  message: string
  /**@name data
@description 请求结果详情
@example {
"job_id": "bcc09076-5748"
}*/
  data: {
    /**@name job_id
@description job_id
@example bcc09076-5748*/
    job_id: string
    /**@name job_status
@description 任务状态
@example 0:初始状态; 10:传入内容预处理完成; 20:输入内容多模态处理完成；30:npc_script生成前置处理完成；40:npc脚本生成完成；50:后置处理剧本生成完成*/
    job_status: number
  }
}
/**@name StartNpcScriptsTask
@summary 启动npc脚本生成
@description 启动npc脚本生成
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const StartNpcScriptsTask = (
  props: ImplRequest<StartNpcScriptsTaskRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.npc_ppt_scripts_svr.NpcPptScriptsSvr/StartNpcScriptsTask",
    { code: "0", message: "ok", data: { job_id: "bcc09076-5748" } },
    props,
  ) as ImplResponse<StartNpcScriptsTaskResponse>
/**@name QueryNpcJobInfoRequest
@required job_id
@example
{
"job_id": "bcc09076-5748"
}
@description 查npc任务信息*/
export type QueryNpcJobInfoRequest = {
  /**@name job_id
@description job_id
@example bcc09076-5748*/
  job_id?: string
}
/**@name QueryNpcJobInfoResponse
@required code,message,data
@example
{
"code":"0",
"message":"ok",
"data": {
"job_id": "bcc09076-5748"
}
}
@description 查npc任务信息*/
export type QueryNpcJobInfoResponse = {
  /**@name code
@description 结果码
@example 0*/
  code: string
  /**@name message
@description 结果信息
@example ok*/
  message: string
  /**@name data
@description 请求结果详情
@example {
"job_status": 2,
"prompt_content": "prompt_content",
"script_content": "script_content"
}*/
  data: {
    /**@name job_id
@description job_id
@example bcc09076-5748*/
    job_id: string
    /**@name job_status
@description 任务状态
@example 0:初始状态; 10:传入内容预处理完成; 20:输入内容多模态处理完成；30:npc_script生成前置处理完成；40:npc脚本生成完成；50:后置处理剧本生成完成*/
    job_status: number
    /**@name job_stage
@description job_id
@example bcc09076-5748*/
    job_stage: string
    /**@name prompt_content
@description 输入内容格式化后数据
@example*/
    prompt_content: string
    /**@name script_content
@description 脚本内容
@example*/
    script_content: string
    /**@name script_element_content
@description 脚本元素内容
@example*/
    script_element_content: string
    /**@name info
@description 基本信息
@example*/
    info: {
      /**@name video_duration
@description 视频时长
@example 180*/
      video_duration: string
      /**@name video_address
@description 视频地址
@example xxxxx*/
      video_address: string
    }
    /**@name subject_content
@description 主题内容
@example*/
    subject_content: string
  }
}
/**@name QueryNpcJobInfo
@summary 查npc任务信息
@description 查npc任务信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryNpcJobInfo = (
  props: ImplRequest<QueryNpcJobInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.npc_ppt_scripts_svr.NpcPptScriptsSvr/QueryNpcJobInfo",
    { code: "0", message: "ok", data: { job_id: "bcc09076-5748" } },
    props,
  ) as ImplResponse<QueryNpcJobInfoResponse>
