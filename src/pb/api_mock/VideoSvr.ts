/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type NewVideoRequest = {
  content_id?: string
  content_name?: string
  content_type?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
  /**@name video_definition
视频清晰度，SD:标清;HD:高清;FHD:全高清;QHD:四高清;UHD:超高清;HDR:高动态范围*/
  video_definition?: string
}
export type NewVideoResponse = { result_code: string; result_info: string }
/**@name NewVideo
@summary 新建一个短视频页面
@description 新建一个短视频页面
@alias /api/content_manage.VideoSvr/NewVideo
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const NewVideo = (
  props: ImplRequest<NewVideoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/NewVideo",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<NewVideoResponse>
export type UpdateVideoRequest = {
  content_id?: string
  content_name?: string
  content_type?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
  /**@name video_definition
视频清晰度，SD:标清;HD:高清;FHD:全高清;QHD:四高清;UHD:超高清;HDR:高动态范围*/
  video_definition?: string
}
export type UpdateVideoResponse = { result_code: string; result_info: string }
/**@name UpdateVideo
@summary 更新短视频页面
@description 更新短视频页面
@alias /api/content_manage.VideoSvr/UpdateVideo
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateVideo = (
  props: ImplRequest<UpdateVideoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/UpdateVideo",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateVideoResponse>
export type VideoListRequest = {
  video_name_or_id?: string
  /**@name content_type
content_type 内容类型 ppt_live script_live*/
  content_type?: string
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
}
export type VideoListResponse = {
  result_code: string
  result_info: string
  data: {
    video_list: {
      video_id: string
      video_name: string
      video_poster: string
      content_type: string
      video_play_time: string
      video_size: string
      /**@name video_status
video_status  视频的发布状态 1:保存未发布 2:发布未录制 3:发布录制中 4:发布且录制失败 5:发布录制完成*/
      video_status: string
      h5_url: string
      video_url: string
      create_user: string
      create_time: string
      modify_user: string
      modify_time: string
      /**@name progress_percent
@description 进度百分率
@example 0.28*/
      progress_percent: number
    }[]
    count: string
  }
}
/**@name VideoList
@summary 查询短视频页面信息列表
@description 查询短视频页面信息列表
@alias /api/content_manage.VideoSvr/VideoList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoList = (
  props: ImplRequest<VideoListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/VideoList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "video_list|1-2": [
          {
            video_id: "@string",
            video_name: "@string",
            video_poster: "@string",
            content_type: "@string",
            video_play_time: "@datetime",
            video_size: "@string",
            video_status:
              "video_status  视频的发布状态 1:保存未发布 2:发布未录制 3:发布录制中 4:发布且录制失败 5:发布录制完成",
            h5_url: "@string",
            video_url: "@string",
            create_user: "@name",
            create_time: "@string",
            modify_user: "@name",
            modify_time: "@string",
            progress_percent: 0.28,
          },
        ],
        count: "@string",
      },
    },
    props,
  ) as ImplResponse<VideoListResponse>
export type VideoStopRequest = { video_id?: string }
export type VideoStopResponse = { result_code: string; result_info: string }
/**@name VideoStop
@summary 暂停短视频录制
@description 暂停短视频录制
@alias /api/content_manage.VideoSvr/VideoStop
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoStop = (
  props: ImplRequest<VideoStopRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/VideoStop",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<VideoStopResponse>
export type VideoDeleteRequest = { video_id?: string }
export type VideoDeleteResponse = { result_code: string; result_info: string }
/**@name VideoDelete
@summary 删除短视频页面信息
@description 删除短视频页面信息
@alias /api/content_manage.VideoSvr/VideoDelete
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoDelete = (
  props: ImplRequest<VideoDeleteRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/VideoDelete",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<VideoDeleteResponse>
export type VideoCopyRequest = { video_id?: string }
export type VideoCopyResponse = { result_code: string; result_info: string }
/**@name VideoCopy
@summary 复制短视频信息
@description 复制短视频信息
@alias /api/content_manage.VideoSvr/VideoCopy
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoCopy = (
  props: ImplRequest<VideoCopyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/VideoCopy",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<VideoCopyResponse>
export type VideoReleaseRequest = {
  video_id?: string
  video_definition?: string
  video_format?: string
  /**@name release_flag
0x1=仅发布不开播*/
  release_flag?: number
}
export type VideoReleaseResponse = { result_code: string; result_info: string }
/**@name VideoRelease
@summary 发布短视频页面并录制
@description 发布短视频页面并录制
@alias /api/content_manage.VideoSvr/VideoRelease
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoRelease = (
  props: ImplRequest<VideoReleaseRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.VideoSvr/VideoRelease",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<VideoReleaseResponse>
