/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type LiveGroupAddRequest = {
  /**@name title
标题
@description：
@example:*/
  title?: string
}
export type LiveGroupAddResponse = {
  /**@name data
直播分组表
@description：直播分组表
@example:*/
  data: {
    /**@name live_group_id
直播分组ID
@description：
@example:*/
    live_group_id: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
    /**@name app_id
appid
@description：
@example:*/
    app_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name title
标题
@description：
@example:*/
    title: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name LiveGroupAdd
@alias=/LiveGroup/Add
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupAdd = (
  props: ImplRequest<LiveGroupAddRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupAdd",
    {
      data: {
        live_group_id: "直播分组ID\n@description：\n@example:",
        system_code: "系统代码\n@description：\n@example:",
        app_id: "appid\n@description：\n@example:",
        user_id: "用户ID\n@description：\n@example:",
        title: "标题\n@description：\n@example:",
        status: "@integer(1, 100)",
        create_time: "@datetime",
        update_time: "@datetime",
      },
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<LiveGroupAddResponse>
export type LiveGroupBindRequest = {
  /**@name live_group_id
直播分组ID
@description：
@example:*/
  live_group_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name is_owner
是否主直播
@description：是否为owner，直播分组的基础资料，从owner的直播获取
@example:*/
  is_owner?: number
}
export type LiveGroupBindResponse = {
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name LiveGroupBind
@alias=LiveGroup/Bind
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupBind = (
  props: ImplRequest<LiveGroupBindRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupBind",
    {
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<LiveGroupBindResponse>
export type LiveGroupDeleteRequest = {
  /**@name live_group_id
直播分组ID
@description：
@example:*/
  live_group_id?: string
}
export type LiveGroupDeleteResponse = {
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name LiveGroupDelete
@alias=/LiveGroup/Delete
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupDelete = (
  props: ImplRequest<LiveGroupDeleteRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupDelete",
    {
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<LiveGroupDeleteResponse>
export type LiveGroupDetailRequest = {
  /**@name live_group_id
直播分组ID
@description：
@example:*/
  live_group_id?: string
}
export type LiveGroupDetailResponse = {
  /**@name data
直播分组表
@description：直播分组表
@example:*/
  data: {
    /**@name live_group_id
直播分组ID
@description：
@example:*/
    live_group_id: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
    /**@name app_id
appid
@description：
@example:*/
    app_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name title
标题
@description：
@example:*/
    title: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name LiveGroupDetail
@alias=/LiveGroup/Detail
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupDetail = (
  props: ImplRequest<LiveGroupDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupDetail",
    {
      data: {
        live_group_id: "直播分组ID\n@description：\n@example:",
        system_code: "系统代码\n@description：\n@example:",
        app_id: "appid\n@description：\n@example:",
        user_id: "用户ID\n@description：\n@example:",
        title: "标题\n@description：\n@example:",
        status: "@integer(1, 100)",
        create_time: "@datetime",
        update_time: "@datetime",
      },
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<LiveGroupDetailResponse>
export type LiveGroupLiveInfoListRequest = {
  /**@name live_group_id
直播分组ID
@description：
@example:*/
  live_group_id?: string
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name page_size
页大小
@description：每页大小
@example:*/
  page_size?: number
}
export type LiveGroupLiveInfoListResponse = {
  /**@name data
直播分组与直播ID的关系表
@description：直播分组与直播ID的关系表
@example:*/
  data: {
    /**@name live_group_id
直播分组ID
@description：
@example:*/
    live_group_id: string
    /**@name live_id
直播ID
@description：
@example:*/
    live_id: string
    /**@name is_owner
是否主直播
@description：是否为owner，直播分组的基础资料，从owner的直播获取
@example:*/
    is_owner: number
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name live_extend_config_list
直播扩展配置项
@description：
@example:*/
    live_extend_config_list: {
      /**@name node_id
直播节点ID
@description：
@example:*/
      node_id: string
      /**@name config_item_id
配置项ID
@description：
@example:*/
      config_item_id: string
      /**@name config_item_value
配置项值
@description：
@example:*/
      config_item_value: string
      /**@name update_time
更新时间
@description：
@example:*/
      update_time: string
      /**@name live_id
直播ID
@description：
@example:*/
      live_id: string
    }[]
    /**@name metalive_push_config
直播扩展信息推送配置
@description：
@example:*/
    metalive_push_config: {
      /**@name rtmp_push_url
rtmp推流地址
@description：
@example:*/
      rtmp_push_url: string
      /**@name rtmp_play_url
流播放地址
@description：
@example:*/
      rtmp_play_url: string
    }
    /**@name barrage_crawl_status
弹幕爬取状态
@description：
@example:*/
    barrage_crawl_status: string
    /**@name content_type
内容类型
@description：
@example:*/
    content_type: string
    /**@name create_user
创建者ID
@description：
@example:*/
    create_user: string
    /**@name last_start_live_time
最后开始直播时间
@description：
@example:*/
    last_start_live_time: string
    /**@name meta_live_id
元直播ID
@description：
@example:*/
    meta_live_id: string
    /**@name meta_live_name
元直播名称
@description：
@example:*/
    meta_live_name: string
    /**@name language
语言
@description：
@example:*/
    language: string
    /**@name meta_live_poster
网址
@description：
@example:*/
    meta_live_poster: string
    /**@name meta_live_status
元直播状态
@description：
@example:*/
    meta_live_status: number
    /**@name meta_live_url
直播 MetaLiveUrl
@description：
@example:*/
    meta_live_url: string
    /**@name modify_time
修改时间（时间戳）
@description：
@example:*/
    modify_time: string
    /**@name modify_user
更新者ID
@description：
@example:*/
    modify_user: string
    /**@name publish_user
发布者ID
@description：
@example:*/
    publish_user: string
    /**@name publish_time
发布时间
@description：
@example:*/
    publish_time: string
    /**@name bind_time
绑定时间
@description：
@example:*/
    bind_time: string
  }[]
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name total_num
总计数
@description：
@example:*/
  total_num: number
}
/**@name LiveGroupLiveInfoList
@alias=LiveGroup/LiveInfoList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupLiveInfoList = (
  props: ImplRequest<LiveGroupLiveInfoListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupLiveInfoList",
    {
      "data|1-2": [
        {
          live_group_id: "直播分组ID\n@description：\n@example:",
          live_id: "直播ID\n@description：\n@example:",
          is_owner: "@integer(1, 100)",
          status: "@integer(1, 100)",
          create_time: "@datetime",
          "live_extend_config_list|1-2": [
            {
              node_id: "直播节点ID\n@description：\n@example:",
              config_item_id: "配置项ID\n@description：\n@example:",
              config_item_value: "配置项值\n@description：\n@example:",
              update_time: "@datetime",
              live_id: "直播ID\n@description：\n@example:",
            },
          ],
          metalive_push_config: {
            rtmp_push_url: "rtmp推流地址\n@description：\n@example:",
            rtmp_play_url: "流播放地址\n@description：\n@example:",
          },
          barrage_crawl_status: "弹幕爬取状态\n@description：\n@example:",
          content_type: "内容类型\n@description：\n@example:",
          create_user: "@name",
          last_start_live_time: "@datetime",
          meta_live_id: "元直播ID\n@description：\n@example:",
          meta_live_name: "元直播名称\n@description：\n@example:",
          language: "语言\n@description：\n@example:",
          meta_live_poster: "网址\n@description：\n@example:",
          meta_live_status: "@integer(1, 100)",
          meta_live_url: "直播 MetaLiveUrl\n@description：\n@example:",
          modify_time: "@datetime",
          modify_user: "@name",
          publish_user: "@name",
          publish_time: "@datetime",
          bind_time: "@datetime",
        },
      ],
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      total_num: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<LiveGroupLiveInfoListResponse>
export type LiveGroupModifyRequest = {
  /**@name live_group_id
直播分组ID
@description：
@example:*/
  live_group_id?: string
  /**@name title
标题
@description：
@example:*/
  title?: string
}
export type LiveGroupModifyResponse = {
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name data
直播分组表
@description：直播分组表
@example:*/
  data: {
    /**@name live_group_id
直播分组ID
@description：
@example:*/
    live_group_id: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
    /**@name app_id
appid
@description：
@example:*/
    app_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name title
标题
@description：
@example:*/
    title: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }
}
/**@name LiveGroupModify
@alias=/LiveGroup/Modify
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupModify = (
  props: ImplRequest<LiveGroupModifyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupModify",
    {
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      data: {
        live_group_id: "直播分组ID\n@description：\n@example:",
        system_code: "系统代码\n@description：\n@example:",
        app_id: "appid\n@description：\n@example:",
        user_id: "用户ID\n@description：\n@example:",
        title: "标题\n@description：\n@example:",
        status: "@integer(1, 100)",
        create_time: "@datetime",
        update_time: "@datetime",
      },
    },
    props,
  ) as ImplResponse<LiveGroupModifyResponse>
export type LiveGroupQueryListRequest = {
  /**@name title
标题
@description：
@example:*/
  title?: string
}
export type LiveGroupQueryListResponse = {
  /**@name data
直播分组表
@description：直播分组表
@example:*/
  data: {
    /**@name live_group_id
直播分组ID
@description：
@example:*/
    live_group_id: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
    /**@name app_id
appid
@description：
@example:*/
    app_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name title
标题
@description：
@example:*/
    title: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }[]
  /**@name msg
错误消息
@description：
@example:*/
  msg: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name LiveGroupQueryList
@alias=/LiveGroup/QueryList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveGroupQueryList = (
  props: ImplRequest<LiveGroupQueryListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dman_toc_dev_data.Service/LiveGroupQueryList",
    {
      "data|1-2": [
        {
          live_group_id: "直播分组ID\n@description：\n@example:",
          system_code: "系统代码\n@description：\n@example:",
          app_id: "appid\n@description：\n@example:",
          user_id: "用户ID\n@description：\n@example:",
          title: "标题\n@description：\n@example:",
          status: "@integer(1, 100)",
          create_time: "@datetime",
          update_time: "@datetime",
        },
      ],
      msg: "错误消息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<LiveGroupQueryListResponse>
