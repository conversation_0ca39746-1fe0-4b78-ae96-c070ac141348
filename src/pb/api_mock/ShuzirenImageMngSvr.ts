/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type CreateImageIDRequest = {
  /**@name task_name
数字人形象任务的名称
@description：
@example:*/
  task_name?: string
  /**@name anchor_name
主播名称
@description：
@example:*/
  anchor_name?: string
  /**@name user_nick
用户昵称
@description：
@example:*/
  user_nick?: string
  /**@name training_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
  training_type?: number
}
export type CreateImageIDResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id: string
}
/**@name CreateImageID
@summary CreateImageID
@description  产生一个新的数字人形象ID
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateImageID = (
  props: ImplRequest<CreateImageIDRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/CreateImageID",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      image_id: "数字人形象ID\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<CreateImageIDResponse>
export type CreateImageTaskAllRequest = {
  /**@name anchor_name
主播名称
@description：
@example:*/
  anchor_name?: string
  /**@name user_nick
用户昵称
@description：
@example:*/
  user_nick?: string
  /**@name task_name
数字人形象任务的名称
@description：
@example:*/
  task_name?: string
  /**@name material_background_pic_url
原始背景图地址
@description：
@example:*/
  material_background_pic_url?: string
  /**@name is_image_have_background
训练出来的形象是否带原始背景
@description：1代表带有
@example:
@enum:
1:有
0:没有*/
  is_image_have_background?: number
  /**@name resolution
数智人分辨率
@description：数智人分辨率，形象定制返回，格式为：1920x1080
@example: 1920x1080*/
  resolution?: string
  /**@name voice_driver_url
驱动demo的音频文件
@description：
@example:*/
  voice_driver_url?: string
  /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
  text_driver?: string
  /**@name image_sexy
形象性别
@description：
@example:
@enum:
1:男
2:女*/
  image_sexy?: number
  /**@name identity_video_background_url
口述版授权书背景图
@description：
@example:*/
  identity_video_background_url?: string
  /**@name identity_video_cos_url
口述版授权书
@description：
@example:*/
  identity_video_cos_url?: string
  /**@name notes
备注信息
@description：描述备注
@example:*/
  notes?: string
  /**@name material_video_url
原始视频地址
@description：
@example:*/
  material_video_url?: string
  /**@name training_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
  training_type?: number
}
export type CreateImageTaskAllResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id: string
}
/**@name CreateImageTaskAll
@summary CreateImageTaskAll
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateImageTaskAll = (
  props: ImplRequest<CreateImageTaskAllRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/CreateImageTaskAll",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      image_id: "数字人形象ID\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<CreateImageTaskAllResponse>
export type CreateVoiceTaskRequest = {
  /**@name material_voice_url
原始音频地址
@description：
@example:*/
  material_voice_url?: string
  /**@name voice_task_name
音色名称
@description：
@example:*/
  voice_task_name?: string
  /**@name voice_sexy
声音性别
@description：
@example:*/
  voice_sexy?: number
  /**@name notes
备注信息
@description：描述备注
@example:*/
  notes?: string
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
  /**@name reference_text
训练音频的参考文本
@description：
@example:*/
  reference_text?: string
  /**@name voice_category
音色类别
@description：数字人音色类别信息
@example:*/
  voice_category?: {
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1?: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2?: string
  }
}
export type CreateVoiceTaskResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id: string
}
/**@name CreateVoiceTask
@summary CreateVoiceTask
@description  本接口将音质检测和声音训练合为一步，训练结果通过VoicePractiseCallBack接口回调。
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateVoiceTask = (
  props: ImplRequest<CreateVoiceTaskRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/CreateVoiceTask",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      voice_task_id: "音色任务ID\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<CreateVoiceTaskResponse>
export type DeleteImageIdRequest = {
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type DeleteImageIdResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name DeleteImageId
@summary DeleteImageId
@description  设置标志为，不可读。
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteImageId = (
  props: ImplRequest<DeleteImageIdRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/DeleteImageId",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<DeleteImageIdResponse>
export type DeleteVoiceIdRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
}
export type DeleteVoiceIdResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name DeleteVoiceId
@summary DeleteVoiceId
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteVoiceId = (
  props: ImplRequest<DeleteVoiceIdRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/DeleteVoiceId",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<DeleteVoiceIdResponse>
export type DetectVoiceQualityRequest = {
  /**@name material_voice_url
原始音频地址
@description：
@example:*/
  material_voice_url?: string
  /**@name reference_text
训练音频的参考文本
@description：
@example:*/
  reference_text?: string
}
export type DetectVoiceQualityResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id: string
}
/**@name DetectVoiceQuality
@summary DetectVoiceQuality
@description  提交音质检测→查询音质检测进度→提交声音训练
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DetectVoiceQuality = (
  props: ImplRequest<DetectVoiceQualityRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/DetectVoiceQuality",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      voice_task_id: "音色任务ID\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<DetectVoiceQualityResponse>
export type GetImageDetailRequest = {
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type GetImageDetailResponse = {
  /**@name image_task_info
数字人形象任务
@description：
@example:*/
  image_task_info: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name image_id
数字人形象ID
@description：
@example:*/
    image_id: string
    /**@name anchor_name
主播名称
@description：
@example:*/
    anchor_name: string
    /**@name tc_task_id
腾讯云数字人任务ID
@description：
@example:*/
    tc_task_id: string
    /**@name process_status
处理进度
@description：
@example:
@enum:
1:素材提交
2:开始训练
100:训练完成*/
    process_status: number
    /**@name task_name
数字人形象任务的名称
@description：
@example:*/
    task_name: string
    /**@name nature_score
自然度得分
@description：
@example:*/
    nature_score: number
    /**@name affinity_score
亲和度得分
@description：
@example:*/
    affinity_score: number
    /**@name fluency_score
流畅度得分
@description：
@example:*/
    fluency_score: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name image_sexy
形象性别
@description：
@example:
@enum:
1:男
2:女*/
    image_sexy: number
    /**@name identity_video_background_url
口述版授权书背景图
@description：
@example:*/
    identity_video_background_url: string
    /**@name material_video_url
原始视频地址
@description：
@example:*/
    material_video_url: string
    /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
    text_driver: string
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
    /**@name material_background_pic_url
原始背景图地址
@description：
@example:*/
    material_background_pic_url: string
    /**@name identity_video_cos_url
口述版授权书
@description：
@example:*/
    identity_video_cos_url: string
    /**@name image_flags
数字人形象标识位
@description：形象的标志位字段，0x01：录制视频是来自本地
@example:*/
    image_flags: number
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name confirm_stage_info
confirm阶段的StageInfo信息
@description：
@example:*/
    confirm_stage_info: string
    /**@name virtualman_key
数智人virtualmankey
@description：
@example:*/
    virtualman_key: string
    /**@name asset_list_info
数字人资产列表
@description：
@example:*/
    asset_list_info: string
    /**@name is_have_background
形象定制是否包含背景图
@description：形象定制类型，训练出来的形象是否带原始背景。默认“否”，即不带原始背景，在应用过程中可以按需更换背景
@example:*/
    is_have_background: boolean
    /**@name is_image_have_background
训练出来的形象是否带原始背景
@description：1代表带有
@example:
@enum:
1:有
0:没有*/
    is_image_have_background: number
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
    /**@name resolution
数智人分辨率
@description：数智人分辨率，形象定制返回，格式为：1920x1080
@example: 1920x1080*/
    resolution: string
    /**@name pratise_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
    pratise_type: number
    /**@name category_level1_name
一级类目名称
@description：
@example:*/
    category_level1_name: string
    /**@name category_level2_name
二级类目名称
@description：
@example:*/
    category_level2_name: string
    /**@name store_status
入库状态
@description：
@example:*/
    store_status: number
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name is_new
是否有new标志
@description：
@example:*/
    is_new: number
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
  }
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name GetImageDetail
@summary GetImageDetail
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetImageDetail = (
  props: ImplRequest<GetImageDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetImageDetail",
    {
      image_task_info: {
        user_id: "用户ID\n@description：\n@example:",
        user_type: "@integer(1, 100)",
        image_id: "数字人形象ID\n@description：\n@example:",
        anchor_name: "主播名称\n@description：\n@example:",
        tc_task_id: "腾讯云数字人任务ID\n@description：\n@example:",
        process_status: "@integer(1, 100)",
        task_name: "数字人形象任务的名称\n@description：\n@example:",
        nature_score: "@integer(1, 100)",
        affinity_score: "@integer(1, 100)",
        fluency_score: "@integer(1, 100)",
        create_time: "@datetime",
        update_time: "@datetime",
        image_sexy: "@integer(1, 100)",
        identity_video_background_url:
          "口述版授权书背景图\n@description：\n@example:",
        material_video_url: "原始视频地址\n@description：\n@example:",
        text_driver:
          "生成驱动demo的文本内容\n@description：用于生成驱动demo的文本内容\n@example:",
        notes: "备注信息\n@description：描述备注\n@example:",
        material_background_pic_url:
          "原始背景图地址\n@description：\n@example:",
        identity_video_cos_url: "口述版授权书\n@description：\n@example:",
        image_flags: "@integer(1, 100)",
        user_nick: "用户昵称\n@description：\n@example:",
        confirm_stage_info:
          "confirm阶段的StageInfo信息\n@description：\n@example:",
        virtualman_key: "数智人virtualmankey\n@description：\n@example:",
        asset_list_info: "数字人资产列表\n@description：\n@example:",
        is_have_background: "@boolean",
        is_image_have_background: "@integer(1, 100)",
        fail_message_info: "训练失败错误信息\n@description：\n@example:",
        resolution:
          "数智人分辨率\n@description：数智人分辨率，形象定制返回，格式为：1920x1080\n@example: 1920x1080",
        pratise_type: "@integer(1, 100)",
        category_level1_name: "一级类目名称\n@description：\n@example:",
        category_level2_name: "二级类目名称\n@description：\n@example:",
        store_status: "@integer(1, 100)",
        category_level1: "一级类目\n@description：\n@example:",
        category_level2: "二级类目\n@description：\n@example:",
        is_new: "@integer(1, 100)",
        platform: "平台\n@description：\n@example:",
        system_code: "系统代码\n@description：\n@example:",
      },
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetImageDetailResponse>
export type GetImageInforListRequest = {
  /**@name page_size
每页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name search_key
搜索关键字
@description：
@example:*/
  search_key?: string
}
export type GetImageInforListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name image_task_info_list
数字人形象任务
@description：
@example:*/
  image_task_info_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name image_id
数字人形象ID
@description：
@example:*/
    image_id: string
    /**@name anchor_name
主播名称
@description：
@example:*/
    anchor_name: string
    /**@name tc_task_id
腾讯云数字人任务ID
@description：
@example:*/
    tc_task_id: string
    /**@name process_status
处理进度
@description：
@example:
@enum:
1:素材提交
2:开始训练
100:训练完成*/
    process_status: number
    /**@name task_name
数字人形象任务的名称
@description：
@example:*/
    task_name: string
    /**@name nature_score
自然度得分
@description：
@example:*/
    nature_score: number
    /**@name affinity_score
亲和度得分
@description：
@example:*/
    affinity_score: number
    /**@name fluency_score
流畅度得分
@description：
@example:*/
    fluency_score: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name image_sexy
形象性别
@description：
@example:
@enum:
1:男
2:女*/
    image_sexy: number
    /**@name identity_video_background_url
口述版授权书背景图
@description：
@example:*/
    identity_video_background_url: string
    /**@name material_video_url
原始视频地址
@description：
@example:*/
    material_video_url: string
    /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
    text_driver: string
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
    /**@name material_background_pic_url
原始背景图地址
@description：
@example:*/
    material_background_pic_url: string
    /**@name identity_video_cos_url
口述版授权书
@description：
@example:*/
    identity_video_cos_url: string
    /**@name image_flags
数字人形象标识位
@description：形象的标志位字段，0x01：录制视频是来自本地
@example:*/
    image_flags: number
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name confirm_stage_info
confirm阶段的StageInfo信息
@description：
@example:*/
    confirm_stage_info: string
    /**@name virtualman_key
数智人virtualmankey
@description：
@example:*/
    virtualman_key: string
    /**@name asset_list_info
数字人资产列表
@description：
@example:*/
    asset_list_info: string
    /**@name is_have_background
形象定制是否包含背景图
@description：形象定制类型，训练出来的形象是否带原始背景。默认“否”，即不带原始背景，在应用过程中可以按需更换背景
@example:*/
    is_have_background: boolean
    /**@name is_image_have_background
训练出来的形象是否带原始背景
@description：1代表带有
@example:
@enum:
1:有
0:没有*/
    is_image_have_background: number
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
    /**@name resolution
数智人分辨率
@description：数智人分辨率，形象定制返回，格式为：1920x1080
@example: 1920x1080*/
    resolution: string
    /**@name pratise_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
    pratise_type: number
    /**@name category_level1_name
一级类目名称
@description：
@example:*/
    category_level1_name: string
    /**@name category_level2_name
二级类目名称
@description：
@example:*/
    category_level2_name: string
    /**@name store_status
入库状态
@description：
@example:*/
    store_status: number
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name is_new
是否有new标志
@description：
@example:*/
    is_new: number
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
  }[]
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
}
/**@name GetImageInforList
@summary GetImageInforList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetImageInforList = (
  props: ImplRequest<GetImageInforListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetImageInforList",
    {
      message: "错误描述信息\n@description：\n@example:",
      "image_task_info_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          user_type: "@integer(1, 100)",
          image_id: "数字人形象ID\n@description：\n@example:",
          anchor_name: "主播名称\n@description：\n@example:",
          tc_task_id: "腾讯云数字人任务ID\n@description：\n@example:",
          process_status: "@integer(1, 100)",
          task_name: "数字人形象任务的名称\n@description：\n@example:",
          nature_score: "@integer(1, 100)",
          affinity_score: "@integer(1, 100)",
          fluency_score: "@integer(1, 100)",
          create_time: "@datetime",
          update_time: "@datetime",
          image_sexy: "@integer(1, 100)",
          identity_video_background_url:
            "口述版授权书背景图\n@description：\n@example:",
          material_video_url: "原始视频地址\n@description：\n@example:",
          text_driver:
            "生成驱动demo的文本内容\n@description：用于生成驱动demo的文本内容\n@example:",
          notes: "备注信息\n@description：描述备注\n@example:",
          material_background_pic_url:
            "原始背景图地址\n@description：\n@example:",
          identity_video_cos_url: "口述版授权书\n@description：\n@example:",
          image_flags: "@integer(1, 100)",
          user_nick: "用户昵称\n@description：\n@example:",
          confirm_stage_info:
            "confirm阶段的StageInfo信息\n@description：\n@example:",
          virtualman_key: "数智人virtualmankey\n@description：\n@example:",
          asset_list_info: "数字人资产列表\n@description：\n@example:",
          is_have_background: "@boolean",
          is_image_have_background: "@integer(1, 100)",
          fail_message_info: "训练失败错误信息\n@description：\n@example:",
          resolution:
            "数智人分辨率\n@description：数智人分辨率，形象定制返回，格式为：1920x1080\n@example: 1920x1080",
          pratise_type: "@integer(1, 100)",
          category_level1_name: "一级类目名称\n@description：\n@example:",
          category_level2_name: "二级类目名称\n@description：\n@example:",
          store_status: "@integer(1, 100)",
          category_level1: "一级类目\n@description：\n@example:",
          category_level2: "二级类目\n@description：\n@example:",
          is_new: "@integer(1, 100)",
          platform: "平台\n@description：\n@example:",
          system_code: "系统代码\n@description：\n@example:",
        },
      ],
      code: "状态码\n@description：\n@example:",
      query_count: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<GetImageInforListResponse>
export type GetImageLogListRequest = {
  /**@name log_level
日志等级
@description：操作流水等级
@example:
@enum:
1:普通详情日志
2:普通关键日志
3:重要详情日志
4:重要关键日志*/
  log_level?: number
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type GetImageLogListResponse = {
  /**@name images_operator_logs_list
数字人操作流水
@description：
@example:*/
  images_operator_logs_list: {
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name log_level
日志等级
@description：操作流水等级
@example:
@enum:
1:普通详情日志
2:普通关键日志
3:重要详情日志
4:重要关键日志*/
    log_level: number
    /**@name image_id
数字人形象ID
@description：
@example:*/
    image_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name GetImageLogList
@summary GetImageLogList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetImageLogList = (
  props: ImplRequest<GetImageLogListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetImageLogList",
    {
      "images_operator_logs_list|1-2": [
        {
          notes: "备注信息\n@description：描述备注\n@example:",
          update_time: "@datetime",
          log_level: "@integer(1, 100)",
          image_id: "数字人形象ID\n@description：\n@example:",
          user_type: "@integer(1, 100)",
          user_id: "用户ID\n@description：\n@example:",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetImageLogListResponse>
export type GetUploadCredentialsRequest = {
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type GetUploadCredentialsResponse = {
  Credentials: {
    /**@name Token
临时密钥令牌
@description：
@example:*/
    Token: string
    /**@name TmpSecretId
临时证书密钥ID
@description：
@example:*/
    TmpSecretId: string
    /**@name TmpSecretKey
临时证书密钥Key
@description：
@example:*/
    TmpSecretKey: string
  }
  /**@name ExpiredTime
临时证书有效的时间
@description：临时证书有效的时间，返回 Unix 时间戳，精确到秒
@example: 1547696355*/
  ExpiredTime: number
  /**@name PathPrefix
上传到cos上的路径前缀
@description："域名/customer-pipline/{数字}/{uuid}/"
@example: "域名/customer-pipline/{数字}/{uuid}/"*/
  PathPrefix: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name GetUploadCredentials
@summary GetUploadCredentials
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetUploadCredentials = (
  props: ImplRequest<GetUploadCredentialsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetUploadCredentials",
    {
      Credentials: {
        Token: "临时密钥令牌\n@description：\n@example:",
        TmpSecretId: "临时证书密钥ID\n@description：\n@example:",
        TmpSecretKey: "临时证书密钥Key\n@description：\n@example:",
      },
      ExpiredTime: "@integer(1, 100)",
      PathPrefix:
        '上传到cos上的路径前缀\n@description："域名/customer-pipline/{数字}/{uuid}/"\n@example: "域名/customer-pipline/{数字}/{uuid}/"',
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetUploadCredentialsResponse>
export type GetVoiceDetectProgressRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
}
export type GetVoiceDetectProgressResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name tc_audio_id
腾讯云返回的音频id
@description：
@example:*/
  tc_audio_id: string
  /**@name tc_task_state
腾讯云任务检测状态
@description：
@example:*/
  tc_task_state: string
  /**@name tc_detect_msg
腾讯云检测提示信息
@description：
@example:*/
  tc_detect_msg: string
  /**@name tc_detect_code
腾讯云检测code
@description：
@example:*/
  tc_detect_code: number
}
/**@name GetVoiceDetectProgress
@summary GetVoiceDetectProgress
@description  腾讯云和自研tts都参照这里的定义https://cloud.tencent.com/document/product/1240/104987
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVoiceDetectProgress = (
  props: ImplRequest<GetVoiceDetectProgressRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetVoiceDetectProgress",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      tc_audio_id: "腾讯云返回的音频id\n@description：\n@example:",
      tc_task_state: "腾讯云任务检测状态\n@description：\n@example:",
      tc_detect_msg: "腾讯云检测提示信息\n@description：\n@example:",
      tc_detect_code: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<GetVoiceDetectProgressResponse>
export type GetVoiceLogListRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
  /**@name log_level
日志等级
@description：操作流水等级
@example:
@enum:
1:普通详情日志
2:普通关键日志
3:重要详情日志
4:重要关键日志*/
  log_level?: number
}
export type GetVoiceLogListResponse = {
  /**@name voice_operator_logs_list
音色任务操作流水
@description：
@example:*/
  voice_operator_logs_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name voice_task_id
音色任务ID
@description：
@example:*/
    voice_task_id: string
    /**@name log_level
日志等级
@description：操作流水等级
@example:
@enum:
1:普通详情日志
2:普通关键日志
3:重要详情日志
4:重要关键日志*/
    log_level: number
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
  }[]
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name GetVoiceLogList
@summary GetVoiceLogList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVoiceLogList = (
  props: ImplRequest<GetVoiceLogListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetVoiceLogList",
    {
      "voice_operator_logs_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          user_type: "@integer(1, 100)",
          voice_task_id: "音色任务ID\n@description：\n@example:",
          log_level: "@integer(1, 100)",
          update_time: "@datetime",
          notes: "备注信息\n@description：描述备注\n@example:",
        },
      ],
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetVoiceLogListResponse>
export type GetVoiceTaskListRequest = {
  /**@name page_size
每页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name search_key
搜索关键字
@description：
@example:*/
  search_key?: string
}
export type GetVoiceTaskListResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name voice_task_info_list
数字人音色任务
@description：
@example:*/
  voice_task_info_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name voice_task_id
音色任务ID
@description：
@example:*/
    voice_task_id: string
    /**@name voice_task_name
音色名称
@description：
@example:*/
    voice_task_name: string
    /**@name process_status
处理进度
@description：
@example:
@enum:
1:素材提交
2:开始训练
100:训练完成*/
    process_status: number
    /**@name nature_score
自然度得分
@description：
@example:*/
    nature_score: number
    /**@name affinity_score
亲和度得分
@description：
@example:*/
    affinity_score: number
    /**@name fluency_score
流畅度得分
@description：
@example:*/
    fluency_score: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name voice_sexy
声音性别
@description：
@example:*/
    voice_sexy: number
    /**@name material_voice_url
原始音频地址
@description：
@example:*/
    material_voice_url: string
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
    /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
    text_driver: string
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
    /**@name voice_mod_uri
音频模型数据uri
@description：可以通过此uri，获取到可用的模型。
@example:*/
    voice_mod_uri: string
    /**@name category_level1_name
一级类目名称
@description：
@example:*/
    category_level1_name: string
    /**@name category_level2_name
二级类目名称
@description：
@example:*/
    category_level2_name: string
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name store_status
入库状态
@description：
@example:*/
    store_status: number
    /**@name is_new
是否有new标志
@description：
@example:*/
    is_new: number
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name tc_task_id
腾讯云数字人任务ID
@description：
@example:*/
    tc_task_id: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
  }[]
}
/**@name GetVoiceTaskList
@summary GetVoiceTaskList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVoiceTaskList = (
  props: ImplRequest<GetVoiceTaskListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/GetVoiceTaskList",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      query_count: "@integer(1, 100)",
      "voice_task_info_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          user_type: "@integer(1, 100)",
          voice_task_id: "音色任务ID\n@description：\n@example:",
          voice_task_name: "音色名称\n@description：\n@example:",
          process_status: "@integer(1, 100)",
          nature_score: "@integer(1, 100)",
          affinity_score: "@integer(1, 100)",
          fluency_score: "@integer(1, 100)",
          create_time: "@datetime",
          update_time: "@datetime",
          voice_sexy: "@integer(1, 100)",
          material_voice_url: "原始音频地址\n@description：\n@example:",
          user_nick: "用户昵称\n@description：\n@example:",
          notes: "备注信息\n@description：描述备注\n@example:",
          text_driver:
            "生成驱动demo的文本内容\n@description：用于生成驱动demo的文本内容\n@example:",
          fail_message_info: "训练失败错误信息\n@description：\n@example:",
          voice_mod_uri:
            "音频模型数据uri\n@description：可以通过此uri，获取到可用的模型。\n@example:",
          category_level1_name: "一级类目名称\n@description：\n@example:",
          category_level2_name: "二级类目名称\n@description：\n@example:",
          category_level1: "一级类目\n@description：\n@example:",
          category_level2: "二级类目\n@description：\n@example:",
          store_status: "@integer(1, 100)",
          is_new: "@integer(1, 100)",
          platform: "平台\n@description：\n@example:",
          tc_task_id: "腾讯云数字人任务ID\n@description：\n@example:",
          system_code: "系统代码\n@description：\n@example:",
        },
      ],
    },
    props,
  ) as ImplResponse<GetVoiceTaskListResponse>
export type ImagePractiseStatusConfirmRequest = {
  /**@name reason
返回原因
@description：
@example:*/
  reason?: string
  /**@name operate
操作
@description：
@example:
@enum:
AGREE:认同
REJECT:驳回*/
  operate?: string
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type ImagePractiseStatusConfirmResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name ImagePractiseStatusConfirm
@summary ImagePractiseStatusConfirm
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ImagePractiseStatusConfirm = (
  props: ImplRequest<ImagePractiseStatusConfirmRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/ImagePractiseStatusConfirm",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<ImagePractiseStatusConfirmResponse>
export type QueryImagePractiseStatusRequest = {
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type QueryImagePractiseStatusResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name image_task_info
数字人形象任务
@description：
@example:*/
  image_task_info: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name image_id
数字人形象ID
@description：
@example:*/
    image_id: string
    /**@name anchor_name
主播名称
@description：
@example:*/
    anchor_name: string
    /**@name tc_task_id
腾讯云数字人任务ID
@description：
@example:*/
    tc_task_id: string
    /**@name process_status
处理进度
@description：
@example:
@enum:
1:素材提交
2:开始训练
100:训练完成*/
    process_status: number
    /**@name task_name
数字人形象任务的名称
@description：
@example:*/
    task_name: string
    /**@name nature_score
自然度得分
@description：
@example:*/
    nature_score: number
    /**@name affinity_score
亲和度得分
@description：
@example:*/
    affinity_score: number
    /**@name fluency_score
流畅度得分
@description：
@example:*/
    fluency_score: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name image_sexy
形象性别
@description：
@example:
@enum:
1:男
2:女*/
    image_sexy: number
    /**@name identity_video_background_url
口述版授权书背景图
@description：
@example:*/
    identity_video_background_url: string
    /**@name material_video_url
原始视频地址
@description：
@example:*/
    material_video_url: string
    /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
    text_driver: string
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
    /**@name material_background_pic_url
原始背景图地址
@description：
@example:*/
    material_background_pic_url: string
    /**@name identity_video_cos_url
口述版授权书
@description：
@example:*/
    identity_video_cos_url: string
    /**@name image_flags
数字人形象标识位
@description：形象的标志位字段，0x01：录制视频是来自本地
@example:*/
    image_flags: number
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name confirm_stage_info
confirm阶段的StageInfo信息
@description：
@example:*/
    confirm_stage_info: string
    /**@name virtualman_key
数智人virtualmankey
@description：
@example:*/
    virtualman_key: string
    /**@name asset_list_info
数字人资产列表
@description：
@example:*/
    asset_list_info: string
    /**@name is_have_background
形象定制是否包含背景图
@description：形象定制类型，训练出来的形象是否带原始背景。默认“否”，即不带原始背景，在应用过程中可以按需更换背景
@example:*/
    is_have_background: boolean
    /**@name is_image_have_background
训练出来的形象是否带原始背景
@description：1代表带有
@example:
@enum:
1:有
0:没有*/
    is_image_have_background: number
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
    /**@name resolution
数智人分辨率
@description：数智人分辨率，形象定制返回，格式为：1920x1080
@example: 1920x1080*/
    resolution: string
    /**@name pratise_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
    pratise_type: number
    /**@name category_level1_name
一级类目名称
@description：
@example:*/
    category_level1_name: string
    /**@name category_level2_name
二级类目名称
@description：
@example:*/
    category_level2_name: string
    /**@name store_status
入库状态
@description：
@example:*/
    store_status: number
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name is_new
是否有new标志
@description：
@example:*/
    is_new: number
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
  }
  /**@name detail_msg_infor
详细的错误信息
@description：
@example:*/
  detail_msg_infor: string
}
/**@name QueryImagePractiseStatus
@summary QueryImagePractiseStatus
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryImagePractiseStatus = (
  props: ImplRequest<QueryImagePractiseStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/QueryImagePractiseStatus",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      image_task_info: {
        user_id: "用户ID\n@description：\n@example:",
        user_type: "@integer(1, 100)",
        image_id: "数字人形象ID\n@description：\n@example:",
        anchor_name: "主播名称\n@description：\n@example:",
        tc_task_id: "腾讯云数字人任务ID\n@description：\n@example:",
        process_status: "@integer(1, 100)",
        task_name: "数字人形象任务的名称\n@description：\n@example:",
        nature_score: "@integer(1, 100)",
        affinity_score: "@integer(1, 100)",
        fluency_score: "@integer(1, 100)",
        create_time: "@datetime",
        update_time: "@datetime",
        image_sexy: "@integer(1, 100)",
        identity_video_background_url:
          "口述版授权书背景图\n@description：\n@example:",
        material_video_url: "原始视频地址\n@description：\n@example:",
        text_driver:
          "生成驱动demo的文本内容\n@description：用于生成驱动demo的文本内容\n@example:",
        notes: "备注信息\n@description：描述备注\n@example:",
        material_background_pic_url:
          "原始背景图地址\n@description：\n@example:",
        identity_video_cos_url: "口述版授权书\n@description：\n@example:",
        image_flags: "@integer(1, 100)",
        user_nick: "用户昵称\n@description：\n@example:",
        confirm_stage_info:
          "confirm阶段的StageInfo信息\n@description：\n@example:",
        virtualman_key: "数智人virtualmankey\n@description：\n@example:",
        asset_list_info: "数字人资产列表\n@description：\n@example:",
        is_have_background: "@boolean",
        is_image_have_background: "@integer(1, 100)",
        fail_message_info: "训练失败错误信息\n@description：\n@example:",
        resolution:
          "数智人分辨率\n@description：数智人分辨率，形象定制返回，格式为：1920x1080\n@example: 1920x1080",
        pratise_type: "@integer(1, 100)",
        category_level1_name: "一级类目名称\n@description：\n@example:",
        category_level2_name: "二级类目名称\n@description：\n@example:",
        store_status: "@integer(1, 100)",
        category_level1: "一级类目\n@description：\n@example:",
        category_level2: "二级类目\n@description：\n@example:",
        is_new: "@integer(1, 100)",
        platform: "平台\n@description：\n@example:",
        system_code: "系统代码\n@description：\n@example:",
      },
      detail_msg_infor: "详细的错误信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<QueryImagePractiseStatusResponse>
export type QueryVoiceDetailRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
}
export type QueryVoiceDetailResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name voice_task_info
数字人音色任务
@description：
@example:*/
  voice_task_info: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name voice_task_id
音色任务ID
@description：
@example:*/
    voice_task_id: string
    /**@name voice_task_name
音色名称
@description：
@example:*/
    voice_task_name: string
    /**@name process_status
处理进度
@description：
@example:
@enum:
1:素材提交
2:开始训练
100:训练完成*/
    process_status: number
    /**@name nature_score
自然度得分
@description：
@example:*/
    nature_score: number
    /**@name affinity_score
亲和度得分
@description：
@example:*/
    affinity_score: number
    /**@name fluency_score
流畅度得分
@description：
@example:*/
    fluency_score: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name voice_sexy
声音性别
@description：
@example:*/
    voice_sexy: number
    /**@name material_voice_url
原始音频地址
@description：
@example:*/
    material_voice_url: string
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name notes
备注信息
@description：描述备注
@example:*/
    notes: string
    /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
    text_driver: string
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
    /**@name voice_mod_uri
音频模型数据uri
@description：可以通过此uri，获取到可用的模型。
@example:*/
    voice_mod_uri: string
    /**@name category_level1_name
一级类目名称
@description：
@example:*/
    category_level1_name: string
    /**@name category_level2_name
二级类目名称
@description：
@example:*/
    category_level2_name: string
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name store_status
入库状态
@description：
@example:*/
    store_status: number
    /**@name is_new
是否有new标志
@description：
@example:*/
    is_new: number
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name tc_task_id
腾讯云数字人任务ID
@description：
@example:*/
    tc_task_id: string
    /**@name system_code
系统代码
@description：
@example:*/
    system_code: string
  }
}
/**@name QueryVoiceDetail
@summary QueryVoiceDetail
@description  process_status定义：2：开始训练，1000：训练完成, 1001: 训练失败
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryVoiceDetail = (
  props: ImplRequest<QueryVoiceDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/QueryVoiceDetail",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      voice_task_info: {
        user_id: "用户ID\n@description：\n@example:",
        user_type: "@integer(1, 100)",
        voice_task_id: "音色任务ID\n@description：\n@example:",
        voice_task_name: "音色名称\n@description：\n@example:",
        process_status: "@integer(1, 100)",
        nature_score: "@integer(1, 100)",
        affinity_score: "@integer(1, 100)",
        fluency_score: "@integer(1, 100)",
        create_time: "@datetime",
        update_time: "@datetime",
        voice_sexy: "@integer(1, 100)",
        material_voice_url: "原始音频地址\n@description：\n@example:",
        user_nick: "用户昵称\n@description：\n@example:",
        notes: "备注信息\n@description：描述备注\n@example:",
        text_driver:
          "生成驱动demo的文本内容\n@description：用于生成驱动demo的文本内容\n@example:",
        fail_message_info: "训练失败错误信息\n@description：\n@example:",
        voice_mod_uri:
          "音频模型数据uri\n@description：可以通过此uri，获取到可用的模型。\n@example:",
        category_level1_name: "一级类目名称\n@description：\n@example:",
        category_level2_name: "二级类目名称\n@description：\n@example:",
        category_level1: "一级类目\n@description：\n@example:",
        category_level2: "二级类目\n@description：\n@example:",
        store_status: "@integer(1, 100)",
        is_new: "@integer(1, 100)",
        platform: "平台\n@description：\n@example:",
        tc_task_id: "腾讯云数字人任务ID\n@description：\n@example:",
        system_code: "系统代码\n@description：\n@example:",
      },
    },
    props,
  ) as ImplResponse<QueryVoiceDetailResponse>
export type ReportSubscriptionMessageRequest = {
  /**@name open_id
微信侧openid
@description：
@example:*/
  open_id?: string
  /**@name task_id
任务id
@description：
@example:*/
  task_id?: string
  /**@name image_page
点击模板卡片后的跳转页面(形象)
@description：
@example:*/
  image_page?: string
  /**@name voice_page
点击模板卡片后的跳转页面(音色)
@description：
@example:*/
  voice_page?: string
  /**@name pratise_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
  pratise_type?: number
  /**@name image_message_subscribed
是否订阅了形象信息
@description：
@example:*/
  image_message_subscribed?: boolean
  /**@name voice_message_subscribed
是否订阅了音色信息
@description：
@example:*/
  voice_message_subscribed?: boolean
  /**@name miniprogram_state
跳转小程序类型
@description：
@example:*/
  miniprogram_state?: string
}
export type ReportSubscriptionMessageResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name ReportSubscriptionMessage
@summary ReportSubscriptionMessage
@description  上报订阅消息的信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ReportSubscriptionMessage = (
  props: ImplRequest<ReportSubscriptionMessageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/ReportSubscriptionMessage",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<ReportSubscriptionMessageResponse>
export type SaveDemoInforRequest = {
  /**@name voice_driver_url
驱动demo的音频文件
@description：
@example:*/
  voice_driver_url?: string
  /**@name text_driver
生成驱动demo的文本内容
@description：用于生成驱动demo的文本内容
@example:*/
  text_driver?: string
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type SaveDemoInforResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SaveDemoInfor
@summary SaveDemoInfor
@description  保存Demo文案
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveDemoInfor = (
  props: ImplRequest<SaveDemoInforRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/SaveDemoInfor",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SaveDemoInforResponse>
export type SaveImageInforRequest = {
  /**@name image_sexy
形象性别
@description：
@example:
@enum:
1:男
2:女*/
  image_sexy?: number
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
  /**@name identity_video_background_url
口述版授权书背景图
@description：
@example:*/
  identity_video_background_url?: string
  /**@name identity_video_cos_url
口述版授权书
@description：
@example:*/
  identity_video_cos_url?: string
  /**@name notes
备注信息
@description：描述备注
@example:*/
  notes?: string
  /**@name anchor_name
主播名称
@description：
@example:*/
  anchor_name?: string
  /**@name image_category
形象类别
@description：数字人形象类别信息
@example:*/
  image_category?: {
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1?: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2?: string
  }
  /**@name voice_category
音色类别
@description：数字人音色类别信息
@example:*/
  voice_category?: {
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1?: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2?: string
  }
}
export type SaveImageInforResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SaveImageInfor
@summary SaveImageInfor
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveImageInfor = (
  props: ImplRequest<SaveImageInforRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/SaveImageInfor",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SaveImageInforResponse>
export type SaveVideoInforRequest = {
  /**@name material_background_pic_url
原始背景图地址
@description：
@example:*/
  material_background_pic_url?: string
  /**@name material_video_url
原始视频地址
@description：
@example:*/
  material_video_url?: string
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
  /**@name is_video_from_local_upload
视频是否来自本地上传
@description：
@example:*/
  is_video_from_local_upload?: boolean
  /**@name is_image_have_background
训练出来的形象是否带原始背景
@description：1代表带有
@example:
@enum:
1:有
0:没有*/
  is_image_have_background?: number
  /**@name resolution
数智人分辨率
@description：数智人分辨率，形象定制返回，格式为：1920x1080
@example: 1920x1080*/
  resolution?: string
  /**@name user_nick
用户昵称
@description：
@example:*/
  user_nick?: string
  /**@name pratise_type
训练类型
@description：0x01: 仅训练视频；0x02: 训练音频
@example:*/
  pratise_type?: number
}
export type SaveVideoInforResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SaveVideoInfor
@summary SaveVideoInfor
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveVideoInfor = (
  props: ImplRequest<SaveVideoInforRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/SaveVideoInfor",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SaveVideoInforResponse>
export type SaveVoiceMakeRequest = {
  /**@name voice_task_name
音色名称
@description：
@example:*/
  voice_task_name?: string
  /**@name voice_sexy
声音性别
@description：
@example:*/
  voice_sexy?: number
  /**@name notes
备注信息
@description：描述备注
@example:*/
  notes?: string
  /**@name tc_audio_id
腾讯云返回的音频id
@description：
@example:*/
  tc_audio_id?: string
  /**@name voice_category
音色类别
@description：数字人音色类别信息
@example:*/
  voice_category?: {
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1?: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2?: string
  }
}
export type SaveVoiceMakeResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id: string
}
/**@name SaveVoiceMake
@summary SaveVoiceMake
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveVoiceMake = (
  props: ImplRequest<SaveVoiceMakeRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/SaveVoiceMake",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      voice_task_id: "音色任务ID\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SaveVoiceMakeResponse>
export type StartImagePractiseRequest = {
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type StartImagePractiseResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name StartImagePractise
@summary StartImagePractise
@description  触发腾讯云的定制api：https://cloud.tencent.com/document/product/1240/96069
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const StartImagePractise = (
  props: ImplRequest<StartImagePractiseRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/StartImagePractise",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<StartImagePractiseResponse>
export type UpdateImageScoreRequest = {
  /**@name nature_score
自然度得分
@description：
@example:*/
  nature_score?: number
  /**@name affinity_score
亲和度得分
@description：
@example:*/
  affinity_score?: number
  /**@name fluency_score
流畅度得分
@description：
@example:*/
  fluency_score?: number
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type UpdateImageScoreResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateImageScore
@summary UpdateImageScore
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateImageScore = (
  props: ImplRequest<UpdateImageScoreRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/UpdateImageScore",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<UpdateImageScoreResponse>
export type UpdateVoiceScoreRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
  /**@name fluency_score
流畅度得分
@description：
@example:*/
  fluency_score?: number
  /**@name affinity_score
亲和度得分
@description：
@example:*/
  affinity_score?: number
  /**@name nature_score
自然度得分
@description：
@example:*/
  nature_score?: number
}
export type UpdateVoiceScoreResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name UpdateVoiceScore
@summary UpdateVoiceScore
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateVoiceScore = (
  props: ImplRequest<UpdateVoiceScoreRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/UpdateVoiceScore",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<UpdateVoiceScoreResponse>
export type VoiceDetectCallbackRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
  /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
  status?: number
  /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
  fail_message_info?: string
  /**@name voice_mod_uri
音频模型数据uri
@description：可以通过此uri，获取到可用的模型。
@example:*/
  voice_mod_uri?: string
}
export type VoiceDetectCallbackResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name VoiceDetectCallback
@summary VoiceDetectCallback
@description  此接口只供自研tts音质检测使用
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VoiceDetectCallback = (
  props: ImplRequest<VoiceDetectCallbackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/VoiceDetectCallback",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<VoiceDetectCallbackResponse>
export type VoicePractiseCallBackRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
  /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
  status?: number
  /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
  fail_message_info?: string
  /**@name voice_mod_uri
音频模型数据uri
@description：可以通过此uri，获取到可用的模型。
@example:*/
  voice_mod_uri?: string
}
export type VoicePractiseCallBackResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name VoicePractiseCallBack
@summary VoicePractiseCallBack
@description  音频训练结果回调
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VoicePractiseCallBack = (
  props: ImplRequest<VoicePractiseCallBackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.shuziren_image_mng_svr.Service/VoicePractiseCallBack",
    {
      code: "状态码\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<VoicePractiseCallBackResponse>
