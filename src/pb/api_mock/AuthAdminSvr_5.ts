/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type UserLoginCheckRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  user_id?: string
  env_id?: string
}
export type UserLoginCheckResponse = {
  result_code: string
  result_info: string
}
/**@name UserLoginCheck
@summary 查询用户信息
@description 查询用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UserLoginCheck = (
  props: ImplRequest<UserLoginCheckRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/UserLoginCheck",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UserLoginCheckResponse>
export type UserLoginRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  user_id?: string
  user_pwd?: string
}
export type UserLoginResponse = {
  result_code: string
  result_info: string
  data: { token: string }
}
/**@name UserLogin
@summary 查询用户信息
@description 查询用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UserLogin = (
  props: ImplRequest<UserLoginRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/UserLogin",
    {
      result_code: "@string",
      result_info: "@string",
      data: { token: "@string" },
    },
    props,
  ) as ImplResponse<UserLoginResponse>
export type UserLoginVerifyRequest = { token?: string }
export type UserLoginVerifyResponse = {
  result_code: string
  result_info: string
  data: {
    sys_code: string
    merchant: string
    /**@name app_id
appid,在TMC中是租户ID,在midas中是商户ID*/
    app_id: string
    user_id: string
    nick_name: string
    account_type: string
    account_id: string
  }
}
/**@name UserLoginVerify
@summary 查询用户信息
@description 查询用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UserLoginVerify = (
  props: ImplRequest<UserLoginVerifyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/UserLoginVerify",
    {
      result_code: "@string",
      result_info: "@string",
      data: { token: "@string" },
    },
    props,
  ) as ImplResponse<UserLoginVerifyResponse>
export type QueryUserResourcesRequest = { user_id_list?: string[] }
export type QueryUserResourcesResponse = {
  result_code: string
  result_info: string
  data: { user_resource: { [key: string]: string } }
}
/**@name QueryUserResources
@summary 查询用户所有的资源列表
@description 查询用户所有的资源列表
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryUserResources = (
  props: ImplRequest<QueryUserResourcesRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/QueryUserResources",
    {
      result_code: "@string",
      result_info: "@string",
      data: { token: "@string" },
    },
    props,
  ) as ImplResponse<QueryUserResourcesResponse>
export type QueryUserRequest = {
  /**@name user_id
user_id的列表，传值的话就是查询指定范围的用户信息*/
  user_id?: string[]
  page_num?: number
  page_size?: number
}
export type QueryUserResponse = {
  result_code: string
  result_info: string
  data: {
    total_size: number
    user_list: {
      sys_code: string
      merchant: string
      app_id: string
      sys_name: string
      user_id: string
      role_info_list: {
        sys_code: string
        merchant: string
        app_id: string
        role_id: string
        role_name: string
        creator: string
        role_description: string
        bind_user_count: number
        create_time: string
        resource_id_list: string[]
        user_id_list: string[]
      }[]
      creator_id: string
      creator_name: string
      create_time: string
    }[]
  }
}
/**@name QueryUser
@summary 查询用户信息
@description 查询用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryUser = (
  props: ImplRequest<QueryUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/QueryUser",
    {
      result_code: "@string",
      result_info: "@string",
      data: { token: "@string" },
    },
    props,
  ) as ImplResponse<QueryUserResponse>
export type AddUserRequest = {
  user_id?: string
  user_pwd?: string
  role_id_list?: string[]
}
export type AddUserResponse = { result_code: string; result_info: string }
/**@name AddUser
@summary 新增用户信息
@description 新增用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddUser = (
  props: ImplRequest<AddUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/AddUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<AddUserResponse>
export type UpdateUserRequest = {
  user_id?: string
  user_pwd?: string
  role_id_list?: string[]
}
export type UpdateUserResponse = { result_code: string; result_info: string }
/**@name UpdateUser
@summary 更新用户信息
@description 更新用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateUser = (
  props: ImplRequest<UpdateUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/UpdateUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateUserResponse>
export type DeleteUserRequest = { user_id?: string }
export type DeleteUserResponse = { result_code: string; result_info: string }
/**@name DeleteUser
@summary 删除用户信息
@description 删除用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteUser = (
  props: ImplRequest<DeleteUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/DeleteUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<DeleteUserResponse>
export type InitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<InitAppUserRequest["super_admin"]>
  common?: NonNullable<InitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<InitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<InitAppUserRequest["super_admin"]>
  financial?: NonNullable<InitAppUserRequest["super_admin"]>
}
export type InitAppUserResponse = { result_code: string; result_info: string }
/**@name InitAppUser
@summary 初始化应用用户信息
@description 初始化应用用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const InitAppUser = (
  props: ImplRequest<InitAppUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/InitAppUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<InitAppUserResponse>
export type TryInitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<TryInitAppUserRequest["super_admin"]>
  common?: NonNullable<TryInitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<TryInitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<TryInitAppUserRequest["super_admin"]>
  financial?: NonNullable<TryInitAppUserRequest["super_admin"]>
}
export type TryInitAppUserResponse = {
  result_code: string
  result_info: string
}
/**@name TryInitAppUser
@summary 初始化应用用户信息Try
@description 初始化应用用户信息Try
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const TryInitAppUser = (
  props: ImplRequest<TryInitAppUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/TryInitAppUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<TryInitAppUserResponse>
export type ConfirmInitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  common?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  financial?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
}
export type ConfirmInitAppUserResponse = {
  result_code: string
  result_info: string
}
/**@name ConfirmInitAppUser
@summary 初始化应用用户信息Confirm
@description 初始化应用用户信息Confirm
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ConfirmInitAppUser = (
  props: ImplRequest<ConfirmInitAppUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/ConfirmInitAppUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ConfirmInitAppUserResponse>
export type CancelInitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  common?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  financial?: NonNullable<CancelInitAppUserRequest["super_admin"]>
}
export type CancelInitAppUserResponse = {
  result_code: string
  result_info: string
}
/**@name CancelInitAppUser
@summary 初始化应用用户信息Cancel
@description 初始化应用用户信息Cancel
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CancelInitAppUser = (
  props: ImplRequest<CancelInitAppUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/CancelInitAppUser",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<CancelInitAppUserResponse>
export type QueryUserAppListRequest = {}
export type QueryUserAppListResponse = {
  result_code: string
  result_info: string
  data: { merchant_map: { [key: string]: { app_id: string[] } } }
}
/**@name QueryUserAppList
@summary 初始化应用用户信息Cancel
@description 初始化应用用户信息Cancel
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryUserAppList = (
  props: ImplRequest<QueryUserAppListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/QueryUserAppList",
    {
      result_code: "@string",
      result_info: "@string",
      data: { token: "@string" },
    },
    props,
  ) as ImplResponse<QueryUserAppListResponse>
export type QueryAppUserRequest = { appId?: string }
export type QueryAppUserResponse = {
  result_code: string
  result_info: string
  data: { appId: string; merchantId: string; uid: string; sysCode: string }[]
}
/**@name QueryAppUser
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryAppUser = (
  props: ImplRequest<QueryAppUserRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.User/QueryAppUser",
    {
      result_code: "@string",
      result_info: "@string",
      "data|1-2": [
        {
          user_id: "用户ID",
          user_nick_name: "用户昵称",
          phone_number: "手机号码",
          wx_id: "微信ID",
          qq_number: "QQ号码",
          "role_ids|1-2": ["角色名称: admin:管理员, collaborator:协作者"],
        },
      ],
    },
    props,
  ) as ImplResponse<QueryAppUserResponse>
