/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type GetApproveListRequest = {
  /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
  status?: string[]
  /**@name approve_type
审核类型: COLLABORATOR*/
  approve_type?: string
  /**@name page_num
页码*/
  page_num?: number
  /**@name page_size
每页大小*/
  page_size?: number
}
export type GetApproveListResponse = {
  data: {
    /**@name count
审批信息数量*/
    count: number
    /**@name collaborator_approve_info_list
协作者审批信息列表*/
    collaborator_approve_info_list: {
      /**@name approve_id
审批ID*/
      approve_id: string
      /**@name user_id
用户ID*/
      user_id: string
      /**@name app_id
账号ID*/
      app_id: string
      /**@name user_nick_name
用户昵称*/
      user_nick_name: string
      /**@name user_avatar_url
用户头像*/
      user_avatar_url: string
      createTime: string
      /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
      status: string
    }[]
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetApproveList
@summary 获取审核列表
@description 根据审核状态和类型获取审核信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetApproveList = (
  props: ImplRequest<GetApproveListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Approve/GetApproveList",
    {
      data: {
        count: "@integer(1, 100)",
        "collaborator_approve_info_list|1-2": [
          {
            approve_id: "审批ID",
            user_id: "用户ID",
            app_id: "账号ID",
            user_nick_name: "用户昵称",
            user_avatar_url: "用户头像",
            createTime: "@datetime",
            status: "审核状态: APPROVED, PENDING, REJECTED",
          },
        ],
      },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<GetApproveListResponse>
export type SubmitApproveRequest = {
  /**@name approve_id
审核ID*/
  approve_id?: string
  /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
  status?: string
  /**@name comments
审核意见*/
  comments?: string
}
export type SubmitApproveResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name SubmitApprove
@summary 提交审核结果
@description 提交审核意见和状态
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SubmitApprove = (
  props: ImplRequest<SubmitApproveRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Approve/SubmitApprove",
    { result_code: "返回结果代码", result_info: "返回结果信息" },
    props,
  ) as ImplResponse<SubmitApproveResponse>
