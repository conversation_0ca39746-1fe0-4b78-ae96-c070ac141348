/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type SaveScriptRequest = {
  /**@name research_name
调研名称*/
  research_name?: string
  /**@name script_info
脚本详情列表 为5个脚本*/
  script_info?: string[]
  type?: string
  app_id?: string
  /**@name source
创建来源*/
  source?: string
  /**@name nodes_count
每个脚本的环节数, 数组数量和script_info要一致*/
  nodes_count?: number[]
  /**@name application_scenarios
应用场景, 与视频和直播的application_scenarios对应*/
  application_scenarios?: string
  /**@name hidden
是否隐藏 0:可见 1:隐藏*/
  hidden?: number
}
export type SaveScriptResponse = {
  result_code: string
  result_info: string
  data: {
    script_list: {
      /**@name research_id
调研id   1个调研id对应有5个脚本*/
      research_id: string
      /**@name research_name
调研名称*/
      research_name: string
      /**@name script_id
脚本id*/
      script_id: string
      script_info: string
      app_id: string
      /**@name nodes_count
本脚本的环节数*/
      nodes_count: number
      /**@name application_scenarios
应用场景, 与视频和直播的application_scenarios对应*/
      application_scenarios: string
    }[]
  }
}
/**@name SaveScript
================ 脚本相关 ================
SaveScript 保存脚本信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveScript = (
  props: ImplRequest<SaveScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/SaveScript",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "script_list|1-2": [
          {
            research_id: "调研id   1个调研id对应有5个脚本",
            research_name: "调研名称",
            script_id: "脚本id",
            script_info: "@string",
            app_id: "@string",
            nodes_count: "@integer(1, 100)",
            application_scenarios:
              "应用场景, 与视频和直播的application_scenarios对应",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<SaveScriptResponse>
export type ModifyScriptRequest = {
  /**@name research_id
调研id   1个调研id对应有5个脚本*/
  research_id?: string
  /**@name script_id
脚本id*/
  script_id?: string
  script_info?: string
  app_id?: string
  nodes_count?: number
}
export type ModifyScriptResponse = { result_code: string; result_info: string }
/**@name ModifyScript
ModifyScript 修改脚本信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ModifyScript = (
  props: ImplRequest<ModifyScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/ModifyScript",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ModifyScriptResponse>
export type ModifyOneResearchRequest = {
  /**@name research_id
调研名称*/
  research_id?: string
  /**@name script_info
脚本信息 仅支持修改一条*/
  script_info?: string
  type?: string
  app_id?: string
  nodes_count?: number
}
export type ModifyOneResearchResponse = {
  result_code: string
  result_info: string
}
/**@name ModifyOneResearch
ModifyScript 修改脚本信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ModifyOneResearch = (
  props: ImplRequest<ModifyOneResearchRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/ModifyOneResearch",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ModifyOneResearchResponse>
export type QueryResearchListRequest = { type?: string; app_id?: string }
export type QueryResearchListResponse = {
  result_code: string
  result_info: string
  data: {
    research_list: {
      research_id: string
      research_name: string
      create_time: string
      app_id: string
    }[]
  }
}
/**@name QueryResearchList
QueryResearchList 查询调研列表
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryResearchList = (
  props: ImplRequest<QueryResearchListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryResearchList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "research_list|1-2": [
          {
            research_id: "@string",
            research_name: "@string",
            create_time: "@string",
            app_id: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryResearchListResponse>
export type QueryOnlyResearchListRequest = {
  app_id?: string
  type?: string
  /**@name search_key
搜索关键词*/
  search_key?: string
  /**@name page_no
分页*/
  page_no?: number
  /**@name page_size
每页大小*/
  page_size?: number
  /**@name application_scenarios
可选，应用场景。与视频和直播的application_scenarios对应*/
  application_scenarios?: string
  /**@name show_hidden
可选，是否显示隐藏的记录。0：只显示可见记录，1：只显示隐藏记录  2：显示所有记录*/
  show_hidden?: number
}
export type QueryOnlyResearchListResponse = {
  result_code: string
  result_info: string
  data: {
    page_no: number
    page_size: number
    research_list: {
      app_id: string
      type: string
      research_id: string
      research_name: string
      user_id: string
      source: string
      scripts_count: number
      all_nodes_count: number
      status: number
      create_time: string
      update_time: string
      application_scenarios: string
      hidden: number
    }[]
    search_key: string
    /**@name all_count
记录总数*/
    all_count: number
  }
}
/**@name QueryOnlyResearchList
QueryOnlyResearchList 只查询调研列表，新接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryOnlyResearchList = (
  props: ImplRequest<QueryOnlyResearchListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryOnlyResearchList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        page_no: "@integer(1, 100)",
        page_size: "@integer(1, 100)",
        "research_list|1-2": [
          {
            app_id: "@string",
            type: "@string",
            research_id: "@string",
            research_name: "@string",
            user_id: "@string",
            source: "@string",
            scripts_count: "@integer(1, 100)",
            all_nodes_count: "@integer(1, 100)",
            status: "@integer(1, 100)",
            create_time: "@string",
            update_time: "@string",
            application_scenarios: "@string",
            hidden: "@integer(1, 100)",
          },
        ],
        search_key: "@string",
        all_count: "@integer(1, 100)",
      },
    },
    props,
  ) as ImplResponse<QueryOnlyResearchListResponse>
export type QueryScriptListRequest = { research_id?: string; app_id?: string }
export type QueryScriptListResponse = {
  result_code: string
  result_info: string
  data: {
    script_list: {
      /**@name research_id
调研id   1个调研id对应有5个脚本*/
      research_id: string
      /**@name research_name
调研名称*/
      research_name: string
      /**@name script_id
脚本id*/
      script_id: string
      script_info: string
      app_id: string
      /**@name nodes_count
本脚本的环节数*/
      nodes_count: number
      /**@name application_scenarios
应用场景, 与视频和直播的application_scenarios对应*/
      application_scenarios: string
    }[]
    count: number
  }
}
/**@name QueryScriptList
QueryScriptList 查询脚本列表
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryScriptList = (
  props: ImplRequest<QueryScriptListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryScriptList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "script_list|1-2": [
          {
            research_id: "调研id   1个调研id对应有5个脚本",
            research_name: "调研名称",
            script_id: "脚本id",
            script_info: "@string",
            app_id: "@string",
            nodes_count: "@integer(1, 100)",
            application_scenarios:
              "应用场景, 与视频和直播的application_scenarios对应",
          },
        ],
        count: "@integer(1, 100)",
      },
    },
    props,
  ) as ImplResponse<QueryScriptListResponse>
export type ScriptEvaluateInfoRequest = {
  /**@name research_id
调研id   1个调研id对应有5个脚本*/
  research_id?: string
  /**@name script_id
脚本id*/
  script_id?: string
  /**@name evaluate
赞/踩  nice/bad*/
  evaluate?: string
  /**@name evaluate_info
赞/踩 原因*/
  evaluate_info?: string
  app_id?: string
}
export type ScriptEvaluateInfoResponse = {
  result_code: string
  result_info: string
}
/**@name ScriptEvaluateInfo
ScriptEvaluateInfo 脚本反馈 赞/踩 反馈具体信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ScriptEvaluateInfo = (
  props: ImplRequest<ScriptEvaluateInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/ScriptEvaluateInfo",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ScriptEvaluateInfoResponse>
export type VideoFeedbackInfoRequest = {
  /**@name research_id
调研id   1个调研id对应有5个脚本*/
  research_id?: string
  /**@name script_id
脚本id*/
  script_id?: string
  /**@name video_evaluate_info
视频反馈*/
  video_evaluate_info?: string
  app_id?: string
}
export type VideoFeedbackInfoResponse = {
  result_code: string
  result_info: string
}
/**@name VideoFeedbackInfo
VideoFeedbackInfo 视频反馈
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoFeedbackInfo = (
  props: ImplRequest<VideoFeedbackInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/VideoFeedbackInfo",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<VideoFeedbackInfoResponse>
export type DeleteResearchRequest = { app_id?: string; research_id?: string }
export type DeleteResearchResponse = {
  result_code: string
  result_info: string
}
/**@name DeleteResearch
DeleteResearch 删除调研
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteResearch = (
  props: ImplRequest<DeleteResearchRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/DeleteResearch",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<DeleteResearchResponse>
export type UpdateImageProgressTimerRequest = {}
export type UpdateImageProgressTimerResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateImageProgressTimer
================ 定时处理腾讯云数字人的训练任务 ================
UpdateImageProgressTimer 更新形象训练进度
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateImageProgressTimer = (
  props: ImplRequest<UpdateImageProgressTimerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/UpdateImageProgressTimer",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateImageProgressTimerResponse>
export type UpdateVoiceProgressTimerRequest = {}
export type UpdateVoiceProgressTimerResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateVoiceProgressTimer
UpdateVoiceProgressTimer 更新音色训练进度
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateVoiceProgressTimer = (
  props: ImplRequest<UpdateVoiceProgressTimerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/UpdateVoiceProgressTimer",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateVoiceProgressTimerResponse>
export type QueryImageQuotaRequest = {}
export type QueryImageQuotaResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name remain_total_quota
形象训练总剩余额度*/
    remain_total_quota: number
    /**@name remain_personal_quota
形象训练个人剩余额度*/
    remain_personal_quota: number
  }
}
/**@name QueryImageQuota
================ 查询音色和形象训练的配额 ================
QueryImageQuota 查询形象训练配额
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryImageQuota = (
  props: ImplRequest<QueryImageQuotaRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryImageQuota",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        remain_total_quota: "@integer(1, 100)",
        remain_personal_quota: "@integer(1, 100)",
      },
    },
    props,
  ) as ImplResponse<QueryImageQuotaResponse>
export type QueryVoiceQuotaRequest = {}
export type QueryVoiceQuotaResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name remain_total_quota
音色训练剩余总额度*/
    remain_total_quota: number
    /**@name remain_personal_quota
音色训练剩余个人额度*/
    remain_personal_quota: number
  }
}
/**@name QueryVoiceQuota
QueryVoiceQuota 查询音色训练配额
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryVoiceQuota = (
  props: ImplRequest<QueryVoiceQuotaRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryVoiceQuota",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        remain_total_quota: "@integer(1, 100)",
        remain_personal_quota: "@integer(1, 100)",
      },
    },
    props,
  ) as ImplResponse<QueryVoiceQuotaResponse>
export type DescribeVirtualManRequest = {
  virtual_man_key?: string
  anchor_code_list?: string[]
  page_num?: number
  /**@name page_size
页面大小（最大不超过100）*/
  page_size?: number
}
export type DescribeVirtualManResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name total
总数*/
    total: number
    /**@name virtual_man_list
数智人列表*/
    virtual_man_list: {
      /**@name anchor_code
数智人主播Code*/
      anchor_code: string
      /**@name anchor_name
数智人主播名称*/
      anchor_name: string
      /**@name pose_image
数智人姿态图片URL*/
      pose_image: string
      /**@name pose_name
数智人姿态*/
      pose_name: string
      /**@name reference_video_segment_url
小样本选用视频片段URL*/
      reference_video_segment_url: string
      /**@name virtual_man_key
数智人VirtualmanKey，形象唯一标识*/
      virtual_man_key: string
    }[]
  }
}
/**@name DescribeVirtualMan
================ 查询形象信息 ================
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DescribeVirtualMan = (
  props: ImplRequest<DescribeVirtualManRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/DescribeVirtualMan",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        total: "@integer(1, 100)",
        "virtual_man_list|1-2": [
          {
            anchor_code: "数智人主播Code",
            anchor_name: "数智人主播名称",
            pose_image: "数智人姿态图片URL",
            pose_name: "数智人姿态",
            reference_video_segment_url: "小样本选用视频片段URL",
            virtual_man_key: "数智人VirtualmanKey，形象唯一标识",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<DescribeVirtualManResponse>
