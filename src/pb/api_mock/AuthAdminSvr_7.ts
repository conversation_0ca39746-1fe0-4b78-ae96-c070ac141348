/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type AddApiRequest = {
  /**@name system
所属系统*/
  system?: string
  /**@name url
url*/
  url?: string
  apiName?: string
  /**@name server
所属服务*/
  server?: string
  /**@name up
是否启用 1 启用*/
  up?: string
  apiType?: string
}
export type AddApiResponse = { result_code: string; result_info: string }
/**@name AddApi
@summary 增加api接口
@description 增加api接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddApi = (
  props: ImplRequest<AddApiRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Api/AddApi",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<AddApiResponse>
export type QueryApiRequest = {
  /**@name pageNum
查询页数 必填*/
  pageNum?: number
  /**@name pageSize
一页数量 必填*/
  pageSize?: number
  /**@name system
所属系统 必填*/
  system?: string
  /**@name queryDetail
查询细则 选填*/
  queryDetail?: {
    /**@name type
1 按名称查询 2 按照url查询*/
    type?: string
    queryString?: string
  }
}
export type QueryApiResponse = {
  result_code: string
  result_info: string
  data: {
    pageNum: number
    pageSize: number
    totalCount: number
    dataList: {
      id: string
      system: string
      systemName: string
      url: string
      apiName: string
      server: string
      createUser: string
      updateUser: string
      /**@name createTime
毫秒时间戳*/
      createTime: string
      updateTime: string
      /**@name up
是否启用*/
      up: string
      apiType: string
    }[]
  }
}
/**@name QueryApi
@summary 查询api接口
@description 查询api接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryApi = (
  props: ImplRequest<QueryApiRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Api/QueryApi",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        pageNum: "@integer(1, 100)",
        pageSize: "@integer(1, 100)",
        totalCount: "@integer(1, 100)",
        "dataList|1-2": [
          {
            id: "@string",
            system: "@string",
            systemName: "@string",
            url: "@string",
            apiName: "@string",
            server: "@string",
            createUser: "@name",
            updateUser: "@name",
            createTime: "@datetime",
            updateTime: "@datetime",
            up: "是否启用",
            apiType: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryApiResponse>
export type UpdateApiRequest = {
  apiName?: string
  server?: string
  url?: string
  up?: string
  id?: string
  apiType?: string
}
export type UpdateApiResponse = { result_code: string; result_info: string }
/**@name UpdateApi
@summary 更新api接口
@description 更新api接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateApi = (
  props: ImplRequest<UpdateApiRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Api/UpdateApi",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateApiResponse>
export type DeleteApiRequest = { id?: string }
export type DeleteApiResponse = { result_code: string; result_info: string }
/**@name DeleteApi
@summary 删除api接口
@description 删除api接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteApi = (
  props: ImplRequest<DeleteApiRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Api/DeleteApi",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<DeleteApiResponse>
