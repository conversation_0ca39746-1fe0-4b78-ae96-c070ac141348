/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type GetInvitationCodeRequest = {}
export type GetInvitationCodeResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name invitation_code
邀请码*/
    invitation_code: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetInvitationCode
@summary 获取邀请码
@description 获取用户的邀请码
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetInvitationCode = (
  props: ImplRequest<GetInvitationCodeRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Invitation/GetInvitationCode",
    {
      data: { invitation_code: "邀请码" },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<GetInvitationCodeResponse>
export type GetInvitationInfoRequest = {
  /**@name invitation_code
邀请码*/
  invitation_code?: string
}
export type GetInvitationInfoResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name invitation_info
邀请码信息*/
    invitation_info: {
      /**@name invitation_code
邀请码*/
      invitation_code: string
      /**@name nick_name
昵称*/
      nick_name: string
      /**@name avatar_url
头像地址*/
      avatar_url: string
      /**@name app_id
账号ID*/
      app_id: string
    }
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetInvitationInfo
@summary 获取邀请码信息
@description 根据邀请码获取邀请码的详细信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetInvitationInfo = (
  props: ImplRequest<GetInvitationInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Invitation/GetInvitationInfo",
    {
      data: {
        invitation_info: {
          invitation_code: "邀请码",
          nick_name: "昵称",
          avatar_url: "头像地址",
          app_id: "账号ID",
        },
      },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<GetInvitationInfoResponse>
