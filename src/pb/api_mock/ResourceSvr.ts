/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type CancelVideoJobRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type CancelVideoJobResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name CancelVideoJob
@summary CancelVideoJob
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CancelVideoJob = (
  props: ImplRequest<CancelVideoJobRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/CancelVideoJob",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<CancelVideoJobResponse>
export type CreateUserVideoInforRequest = {
  /**@name user_video_object_list
用户视频对象
@description：用户视频存储对象
@example:*/
  user_video_object_list?: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id?: string
    /**@name app_code
应用代码
@description：
@example:*/
    app_code?: string
    /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
    video_id?: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status?: number
    /**@name video_create_type
视频创建类型
@description：
@example: 1
@enum:
1:PPT
2:电商
3:游戏
4:文章
101:直播带货
102:广告素材
201:用户上传视频
202:高光视频
203:高光锦集*/
    video_create_type?: number
    /**@name video_name
视频名称
@description：
@example:*/
    video_name?: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url?: string
    /**@name video_content
视频描述
@description：
@example:*/
    video_content?: string
    /**@name score
得分
@description：
@example:*/
    score?: number
    /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
    video_duration?: string
    /**@name video_size
视频大小
@description：2Gb代表2G
@example:*/
    video_size?: string
    /**@name video_url
视频url地址
@description：
@example:*/
    video_url?: string
    /**@name job_id
任务ID
@description：
@example:*/
    job_id?: string
    /**@name job_name
任务名称
@description：
@example: 王者高光*/
    job_name?: string
    /**@name video_cos_path
视频cos上的相对路径
@description：
@example:*/
    video_cos_path?: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time?: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time?: string
  }[]
}
export type CreateUserVideoInforResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name user_video_object_list
用户视频对象
@description：用户视频存储对象
@example:*/
  user_video_object_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name app_code
应用代码
@description：
@example:*/
    app_code: string
    /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
    video_id: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name video_create_type
视频创建类型
@description：
@example: 1
@enum:
1:PPT
2:电商
3:游戏
4:文章
101:直播带货
102:广告素材
201:用户上传视频
202:高光视频
203:高光锦集*/
    video_create_type: number
    /**@name video_name
视频名称
@description：
@example:*/
    video_name: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url: string
    /**@name video_content
视频描述
@description：
@example:*/
    video_content: string
    /**@name score
得分
@description：
@example:*/
    score: number
    /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
    video_duration: string
    /**@name video_size
视频大小
@description：2Gb代表2G
@example:*/
    video_size: string
    /**@name video_url
视频url地址
@description：
@example:*/
    video_url: string
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_name
任务名称
@description：
@example: 王者高光*/
    job_name: string
    /**@name video_cos_path
视频cos上的相对路径
@description：
@example:*/
    video_cos_path: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }[]
}
/**@name CreateUserVideoInfor
@summary CreateUserVideoInfor
@description  将多个视频资料（最多100）保存到后台，调用方不用填写user_id, app_code、job_id、status等字段；返回时会带回job_id
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateUserVideoInfor = (
  props: ImplRequest<CreateUserVideoInforRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/CreateUserVideoInfor",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      "user_video_object_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          app_code: "应用代码\n@description：\n@example:",
          video_id:
            "视频ID\n@description：视频ID，在视频表中唯一的标识视频\n@example:",
          status: "@integer(1, 100)",
          video_create_type: "@integer(1, 100)",
          video_name: "视频名称\n@description：\n@example:",
          pic_url: "图片url\n@description：\n@example:",
          video_content: "视频描述\n@description：\n@example:",
          score: "@integer(1, 100)",
          video_duration:
            '视频时长\n@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；\n@example:',
          video_size: "视频大小\n@description：2Gb代表2G\n@example:",
          video_url: "视频url地址\n@description：\n@example:",
          job_id: "任务ID\n@description：\n@example:",
          job_name: "任务名称\n@description：\n@example: 王者高光",
          video_cos_path: "视频cos上的相对路径\n@description：\n@example:",
          create_time: "@datetime",
          update_time: "@datetime",
        },
      ],
    },
    props,
  ) as ImplResponse<CreateUserVideoInforResponse>
export type CreateVideoProcJobRequest = {
  /**@name job_type
任务类型
@description：任务类型，包括在线任务和离线任务；
@example:
@enum:
3:音频tts训练任务
101:高光片段生成
102:高光集锦生成
1001:在线客服默认问答匹配text_default_video_match
1:H5转点播视频
2:H5转直播视频
4:音质检查
103:多个视频合并*/
  job_type?: number
  /**@name job_name
任务名称
@description：
@example: 王者高光*/
  job_name?: string
  /**@name video_id_list
视频ID列表
@description：多个video_id通过;进行分割
@example: video_id_1434234;video_id_5345345*/
  video_id_list?: string
  /**@name video_segment_num
生成视频的片段数
@description：
@example: 1*/
  video_segment_num?: number
  /**@name video_slice_job_id
视频切片任务ID
@description：
@example:*/
  video_slice_job_id?: string
  /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
  video_duration?: string
}
export type CreateVideoProcJobResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name job_id
任务ID
@description：
@example:*/
  job_id: string
}
/**@name CreateVideoProcJob
@summary CreateVideoProcJob
@description  job_type=101：高光片段生成， video_id_list：填写高光视频ID，video_segment_num填写片段；job_type=102：高光集锦生成；
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateVideoProcJob = (
  props: ImplRequest<CreateVideoProcJobRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/CreateVideoProcJob",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      job_id: "任务ID\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<CreateVideoProcJobResponse>
export type DelUserVideoInforRequest = {
  /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
  video_id?: string
}
export type DelUserVideoInforResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name DelUserVideoInfor
@summary DelUserVideoInfor
@description  删除用户视频，不会真删除视频，设置标志位，通过url仍然能够访问
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DelUserVideoInfor = (
  props: ImplRequest<DelUserVideoInforRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/DelUserVideoInfor",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<DelUserVideoInforResponse>
export type GetCategoryInfoListRequest = {
  /**@name category_type
品类类型
@description：
@example:*/
  category_type?: string
}
export type GetCategoryInfoListResponse = {
  /**@name category_info_list
资源品类管理配置
@description：
@example:*/
  category_info_list: {
    /**@name category_type
品类类型
@description：
@example:*/
    category_type: string
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name category_level1_name
一级类目名称
@description：
@example:*/
    category_level1_name: string
    /**@name category_level2_name
二级类目名称
@description：
@example:*/
    category_level2_name: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name GetCategoryInfoList
@summary GetCategoryInfoList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetCategoryInfoList = (
  props: ImplRequest<GetCategoryInfoListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetCategoryInfoList",
    {
      "category_info_list|1-2": [
        {
          category_type: "品类类型\n@description：\n@example:",
          category_level1: "一级类目\n@description：\n@example:",
          category_level2: "二级类目\n@description：\n@example:",
          status: "@integer(1, 100)",
          category_level1_name: "一级类目名称\n@description：\n@example:",
          category_level2_name: "二级类目名称\n@description：\n@example:",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetCategoryInfoListResponse>
export type GetCategoryListRequest = {
  /**@name category_level1
一级类目
@description：
@example:*/
  category_level1?: string
}
export type GetCategoryListResponse = {
  /**@name category_management_object_list
品类管理配置
@description：
@example:*/
  category_management_object_list: {
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name GetCategoryList
@summary GetCategoryList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetCategoryList = (
  props: ImplRequest<GetCategoryListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetCategoryList",
    {
      "category_management_object_list|1-2": [
        {
          category_level1: "一级类目\n@description：\n@example:",
          category_level2: "二级类目\n@description：\n@example:",
          status: "@integer(1, 100)",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetCategoryListResponse>
export type GetDipResourceListRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name category_level2
二级类目
@description：
@example:*/
  category_level2?: string
  /**@name category_level1
一级类目
@description：
@example:*/
  category_level1?: string
  /**@name app_code
应用代码
@description：
@example:*/
  app_code?: string
  /**@name use_virtual_man_key
是否使用形象key
@description：
@example:*/
  use_virtual_man_key?: boolean
  /**@name auth_level
权限级别
@description：公共：public，私有：private，全部：为空/all
@example:*/
  auth_level?: string
  /**@name order_by
排序字段
@description：排序的字段
@example:*/
  order_by?: string
}
export type GetDipResourceListResponse = {
  /**@name dip_info_list
用户数字人资源管理
@description：
@example:*/
  dip_info_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name app_code
应用代码
@description：
@example:*/
    app_code: string
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name platform_account_id
平台账号id
@description：
@example:*/
    platform_account_id: string
    /**@name dip_authority
数字人权限
@description：
@example:*/
    dip_authority: number
    /**@name dip_id
数字人id
@description：
@example:*/
    dip_id: string
    /**@name dip_name
数字人名称
@description：
@example:*/
    dip_name: string
    /**@name dip_image
数字人图片
@description：
@example:*/
    dip_image: string
    /**@name dip_feature_keywords
数字人特征关键字
@description：
@example:*/
    dip_feature_keywords: string
    /**@name dip_description
数字人描述
@description：
@example:*/
    dip_description: string
    /**@name virtual_man_key
数字人形象key
@description：
@example:*/
    virtual_man_key: string
    /**@name default_voice_id
默认音色key
@description：
@example:*/
    default_voice_id: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name GetDipResourceList
@summary GetDipResourceList
@description  根据类别查询数字人列表信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetDipResourceList = (
  props: ImplRequest<GetDipResourceListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetDipResourceList",
    {
      "dip_info_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          app_code: "应用代码\n@description：\n@example:",
          platform: "平台\n@description：\n@example:",
          platform_account_id: "平台账号id\n@description：\n@example:",
          dip_authority: "@integer(1, 100)",
          dip_id: "数字人id\n@description：\n@example:",
          dip_name: "数字人名称\n@description：\n@example:",
          dip_image: "数字人图片\n@description：\n@example:",
          dip_feature_keywords: "数字人特征关键字\n@description：\n@example:",
          dip_description: "数字人描述\n@description：\n@example:",
          virtual_man_key: "数字人形象key\n@description：\n@example:",
          default_voice_id: "默认音色key\n@description：\n@example:",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetDipResourceListResponse>
export type GetIndustryCategoryListRequest = {
  /**@name industry_category
行业类目
@description：品类中一级类目
@example: 个人护理、美妆、游戏等*/
  industry_category?: string
}
export type GetIndustryCategoryListResponse = {
  /**@name category_management_object_list
品类管理配置
@description：
@example:*/
  category_management_object_list: {
    /**@name category_level1
一级类目
@description：
@example:*/
    category_level1: string
    /**@name category_level2
二级类目
@description：
@example:*/
    category_level2: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name GetIndustryCategoryList
@summary GetIndustryCategoryList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetIndustryCategoryList = (
  props: ImplRequest<GetIndustryCategoryListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetIndustryCategoryList",
    {
      "category_management_object_list|1-2": [
        {
          category_level1: "一级类目\n@description：\n@example:",
          category_level2: "二级类目\n@description：\n@example:",
          status: "@integer(1, 100)",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetIndustryCategoryListResponse>
export type GetJobVideoListRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type GetJobVideoListResponse = {
  /**@name user_job_video_result_list
用户任务输出视频列表对象
@description：
@example:*/
  user_job_video_result_list: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
    video_id: string
    /**@name video_index_id
视频序号ID
@description：
@example:*/
    video_index_id: number
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name video_name
视频名称
@description：
@example:*/
    video_name: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url: string
    /**@name video_content
视频描述
@description：
@example:*/
    video_content: string
    /**@name score
得分
@description：
@example:*/
    score: number
    /**@name video_url
视频url地址
@description：
@example:*/
    video_url: string
    /**@name video_cos_path
视频cos上的相对路径
@description：
@example:*/
    video_cos_path: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name is_have_save_user_video
是否保存为用户视频
@description：0: 未知状态，用于筛选时使用；1:保存为用户视频；2:未保存为用户视频。默认为2
@example:*/
    is_have_save_user_video: number
    /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
    video_duration: string
    /**@name video_size
视频大小
@description：2Gb代表2G
@example:*/
    video_size: string
    /**@name video_slice_extend_config
视频切片扩展信息
@description：{
"segment_id": 1,
"start_time": "",
"end_time": "0",
"segment_text": "",
"video_text": ""
@example:*/
    video_slice_extend_config: string
  }[]
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name GetJobVideoList
@summary GetJobVideoList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetJobVideoList = (
  props: ImplRequest<GetJobVideoListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetJobVideoList",
    {
      "user_job_video_result_list|1-2": [
        {
          job_id: "任务ID\n@description：\n@example:",
          video_id:
            "视频ID\n@description：视频ID，在视频表中唯一的标识视频\n@example:",
          video_index_id: "@integer(1, 100)",
          status: "@integer(1, 100)",
          video_name: "视频名称\n@description：\n@example:",
          pic_url: "图片url\n@description：\n@example:",
          video_content: "视频描述\n@description：\n@example:",
          score: "@integer(1, 100)",
          video_url: "视频url地址\n@description：\n@example:",
          video_cos_path: "视频cos上的相对路径\n@description：\n@example:",
          create_time: "@datetime",
          update_time: "@datetime",
          is_have_save_user_video: "@integer(1, 100)",
          video_duration:
            '视频时长\n@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；\n@example:',
          video_size: "视频大小\n@description：2Gb代表2G\n@example:",
          video_slice_extend_config:
            '视频切片扩展信息\n@description：{\n"segment_id": 1,\n"start_time": "",\n"end_time": "0",\n"segment_text": "",\n"video_text": ""\n@example:',
        },
      ],
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetJobVideoListResponse>
export type GetScriptCategoryConfigRequest = {
  /**@name category_id
分类id
@description：
@example:*/
  category_id?: string
  /**@name script_type
脚本类型
@description：
@example:*/
  script_type?: string
}
export type GetScriptCategoryConfigResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name script_category_config
智能脚本配置项
@description：
@example:*/
  script_category_config: {
    /**@name category_id
分类id
@description：
@example:*/
    category_id: string
    /**@name script_type
脚本类型
@description：
@example:*/
    script_type: string
    /**@name script_config
脚本配置
@description：
@example:*/
    script_config: string
  }
}
/**@name GetScriptCategoryConfig
@summary GetScriptCategoryConfig
@description  上传视频时，完成视频内容上传到cos后，调用API，将视频资料保存到后台；
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetScriptCategoryConfig = (
  props: ImplRequest<GetScriptCategoryConfigRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetScriptCategoryConfig",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      script_category_config: {
        category_id: "分类id\n@description：\n@example:",
        script_type: "脚本类型\n@description：\n@example:",
        script_config: "脚本配置\n@description：\n@example:",
      },
    },
    props,
  ) as ImplResponse<GetScriptCategoryConfigResponse>
export type GetSystemAudioListRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name app_code
应用代码
@description：
@example:*/
  app_code?: string
}
export type GetSystemAudioListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name audio_info_list
用户音频管理配置
@description：
@example:*/
  audio_info_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name audio_authority
音频权限
@description：
@example:*/
    audio_authority: number
    /**@name audio_name
音频名称
@description：
@example:*/
    audio_name: string
    /**@name image_address
图片地址
@description：
@example:*/
    image_address: string
    /**@name audio_address
音频地址
@description：
@example:*/
    audio_address: string
    /**@name audio_duration
音频时长
@description：
@example:*/
    audio_duration: string
    /**@name audio_feature_keywords
音频关键字
@description：
@example:*/
    audio_feature_keywords: string
    /**@name audio_description
音频描述
@description：
@example:*/
    audio_description: string
    /**@name id
id
@description：
@example:*/
    id: number
    /**@name app_code
应用代码
@description：
@example:*/
    app_code: string
    /**@name audio_ref_id
音频id
@description：
@example:*/
    audio_ref_id: string
    /**@name audio_type
音频类型
@description：
@example:*/
    audio_type: string
  }[]
}
/**@name GetSystemAudioList
@summary GetSystemAudioList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetSystemAudioList = (
  props: ImplRequest<GetSystemAudioListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetSystemAudioList",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      "audio_info_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          platform: "平台\n@description：\n@example:",
          audio_authority: "@integer(1, 100)",
          audio_name: "音频名称\n@description：\n@example:",
          image_address: "图片地址\n@description：\n@example:",
          audio_address: "音频地址\n@description：\n@example:",
          audio_duration: "音频时长\n@description：\n@example:",
          audio_feature_keywords: "音频关键字\n@description：\n@example:",
          audio_description: "音频描述\n@description：\n@example:",
          "id|+1": 1,
          app_code: "应用代码\n@description：\n@example:",
          audio_ref_id: "音频id\n@description：\n@example:",
          audio_type: "音频类型\n@description：\n@example:",
        },
      ],
    },
    props,
  ) as ImplResponse<GetSystemAudioListResponse>
export type GetUserDipInfoListRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
}
export type GetUserDipInfoListResponse = {
  /**@name user_dip_object_list
用户数字人管理配置
@description：
@example:*/
  user_dip_object_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name dip_authority
数字人权限
@description：
@example:*/
    dip_authority: number
    /**@name dip_id
数字人id
@description：
@example:*/
    dip_id: string
    /**@name dip_name
数字人名称
@description：
@example:*/
    dip_name: string
    /**@name role_category
角色分类
@description：
@example:*/
    role_category: number
    /**@name product_category
产品分类
@description：
@example:*/
    product_category: number
    /**@name dip_image
数字人图片
@description：
@example:*/
    dip_image: string
    /**@name dip_feature_keywords
数字人特征关键字
@description：
@example:*/
    dip_feature_keywords: string
    /**@name dip_description
数字人描述
@description：
@example:*/
    dip_description: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name platform_account_id
平台账号id
@description：
@example:*/
    platform_account_id: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
}
/**@name GetUserDipInfoList
@summary GetUserDipInfoList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetUserDipInfoList = (
  props: ImplRequest<GetUserDipInfoListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetUserDipInfoList",
    {
      "user_dip_object_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          platform: "平台\n@description：\n@example:",
          dip_authority: "@integer(1, 100)",
          dip_id: "数字人id\n@description：\n@example:",
          dip_name: "数字人名称\n@description：\n@example:",
          role_category: "@integer(1, 100)",
          product_category: "@integer(1, 100)",
          dip_image: "数字人图片\n@description：\n@example:",
          dip_feature_keywords: "数字人特征关键字\n@description：\n@example:",
          dip_description: "数字人描述\n@description：\n@example:",
          create_time: "@datetime",
          update_time: "@datetime",
          platform_account_id: "平台账号id\n@description：\n@example:",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetUserDipInfoListResponse>
export type GetUserVideoInforListRequest = {
  /**@name video_create_type
视频创建类型
@description：
@example: 1
@enum:
1:PPT
2:电商
3:游戏
4:文章
101:直播带货
102:广告素材
201:用户上传视频
202:高光视频
203:高光锦集*/
  video_create_type?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name page_size
页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name search_key
搜索关键字
@description：
@example:*/
  search_key?: string
}
export type GetUserVideoInforListResponse = {
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name user_video_object_list
用户视频对象
@description：用户视频存储对象
@example:*/
  user_video_object_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name app_code
应用代码
@description：
@example:*/
    app_code: string
    /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
    video_id: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name video_create_type
视频创建类型
@description：
@example: 1
@enum:
1:PPT
2:电商
3:游戏
4:文章
101:直播带货
102:广告素材
201:用户上传视频
202:高光视频
203:高光锦集*/
    video_create_type: number
    /**@name video_name
视频名称
@description：
@example:*/
    video_name: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url: string
    /**@name video_content
视频描述
@description：
@example:*/
    video_content: string
    /**@name score
得分
@description：
@example:*/
    score: number
    /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
    video_duration: string
    /**@name video_size
视频大小
@description：2Gb代表2G
@example:*/
    video_size: string
    /**@name video_url
视频url地址
@description：
@example:*/
    video_url: string
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_name
任务名称
@description：
@example: 王者高光*/
    job_name: string
    /**@name video_cos_path
视频cos上的相对路径
@description：
@example:*/
    video_cos_path: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }[]
}
/**@name GetUserVideoInforList
@summary GetUserVideoInforList
@description  获取用户视频列表，video_create_type=0代表不区分类型，获取用户创建的视频列表；
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetUserVideoInforList = (
  props: ImplRequest<GetUserVideoInforListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetUserVideoInforList",
    {
      query_count: "@integer(1, 100)",
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      "user_video_object_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          app_code: "应用代码\n@description：\n@example:",
          video_id:
            "视频ID\n@description：视频ID，在视频表中唯一的标识视频\n@example:",
          status: "@integer(1, 100)",
          video_create_type: "@integer(1, 100)",
          video_name: "视频名称\n@description：\n@example:",
          pic_url: "图片url\n@description：\n@example:",
          video_content: "视频描述\n@description：\n@example:",
          score: "@integer(1, 100)",
          video_duration:
            '视频时长\n@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；\n@example:',
          video_size: "视频大小\n@description：2Gb代表2G\n@example:",
          video_url: "视频url地址\n@description：\n@example:",
          job_id: "任务ID\n@description：\n@example:",
          job_name: "任务名称\n@description：\n@example: 王者高光",
          video_cos_path: "视频cos上的相对路径\n@description：\n@example:",
          create_time: "@datetime",
          update_time: "@datetime",
        },
      ],
    },
    props,
  ) as ImplResponse<GetUserVideoInforListResponse>
export type GetUserVoiceInfoListRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
}
export type GetUserVoiceInfoListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name user_voice_object_list
用户音色管理配置
@description：
@example:*/
  user_voice_object_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name voice_authority
音色权限
@description：
@example:*/
    voice_authority: number
    /**@name voice_id
音色ID
@description：
@example:*/
    voice_id: string
    /**@name voice_name
音色名称
@description：
@example:*/
    voice_name: string
    /**@name role_category
角色分类
@description：
@example:*/
    role_category: number
    /**@name feature_category
特征分类
@description：
@example:*/
    feature_category: number
    /**@name voice_image
音色图片
@description：
@example:*/
    voice_image: string
    /**@name voice_feature_keywords
音色特征关键字
@description：
@example:*/
    voice_feature_keywords: string
    /**@name voice_description
音色描述
@description：
@example:*/
    voice_description: string
    /**@name audition_address
音色地址
@description：
@example:*/
    audition_address: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name voice_text
音色文本
@description：
@example:*/
    voice_text: string
    /**@name voice_extend_config
音色扩展配置
@description：
@example:*/
    voice_extend_config: string
  }[]
}
/**@name GetUserVoiceInfoList
@summary GetUserVoiceInfoList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetUserVoiceInfoList = (
  props: ImplRequest<GetUserVoiceInfoListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetUserVoiceInfoList",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      "user_voice_object_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          platform: "平台\n@description：\n@example:",
          voice_authority: "@integer(1, 100)",
          voice_id: "音色ID\n@description：\n@example:",
          voice_name: "音色名称\n@description：\n@example:",
          role_category: "@integer(1, 100)",
          feature_category: "@integer(1, 100)",
          voice_image: "音色图片\n@description：\n@example:",
          voice_feature_keywords: "音色特征关键字\n@description：\n@example:",
          voice_description: "音色描述\n@description：\n@example:",
          audition_address: "音色地址\n@description：\n@example:",
          create_time: "@datetime",
          update_time: "@datetime",
          voice_text: "音色文本\n@description：\n@example:",
          voice_extend_config: "音色扩展配置\n@description：\n@example:",
        },
      ],
    },
    props,
  ) as ImplResponse<GetUserVoiceInfoListResponse>
export type GetVideoHistoryJobListRequest = {
  /**@name page_size
页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name job_type
任务类型
@description：任务类型，包括在线任务和离线任务；
@example:
@enum:
3:音频tts训练任务
101:高光片段生成
102:高光集锦生成
1001:在线客服默认问答匹配text_default_video_match
1:H5转点播视频
2:H5转直播视频
4:音质检查
103:多个视频合并*/
  job_type?: number
  /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
  job_status?: number
}
export type GetVideoHistoryJobListResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name history_job_infor_list
用户历史任务
@description：
@example:*/
  history_job_infor_list: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_name
任务名称
@description：
@example: 王者高光*/
    job_name: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
    job_status: number
    /**@name progress_percent
job当前状态处理进度
@description：12.3代表12.3%
@example:*/
    progress_percent: number
    /**@name video_segment_num
返回的真实视频切片数
@description：
@example:*/
    video_segment_num: number
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
}
/**@name GetVideoHistoryJobList
@summary GetVideoHistoryJobList
@description  获取视频历史处理任务列表；job_type：任务类型，101：高光片段生成，102: 高光集锦生成；job_status=0筛选所有状态；job_status=1000查询已成功状态的任务
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVideoHistoryJobList = (
  props: ImplRequest<GetVideoHistoryJobListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetVideoHistoryJobList",
    {
      code: "code\n@description：\n@example:",
      "history_job_infor_list|1-2": [
        {
          job_id: "任务ID\n@description：\n@example:",
          job_name: "任务名称\n@description：\n@example: 王者高光",
          update_time: "@datetime",
          create_time: "@datetime",
          job_status: "@integer(1, 100)",
          progress_percent: "@float(1, 100)",
          video_segment_num: "@integer(1, 100)",
          fail_message_info: "训练失败错误信息\n@description：\n@example:",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      query_count: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<GetVideoHistoryJobListResponse>
export type GetVideoJobDetailRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type GetVideoJobDetailResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name job_infor
用户历史任务
@description：
@example:*/
  job_infor: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_name
任务名称
@description：
@example: 王者高光*/
    job_name: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
    job_status: number
    /**@name progress_percent
job当前状态处理进度
@description：12.3代表12.3%
@example:*/
    progress_percent: number
    /**@name video_segment_num
返回的真实视频切片数
@description：
@example:*/
    video_segment_num: number
    /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
    fail_message_info: string
  }
}
/**@name GetVideoJobDetail
@summary GetVideoJobDetail
@description  查询单个任务的详情信息，用于周期查询job的状态；返回对象的名称为job_infor
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVideoJobDetail = (
  props: ImplRequest<GetVideoJobDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetVideoJobDetail",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      job_infor: {
        job_id: "任务ID\n@description：\n@example:",
        job_name: "任务名称\n@description：\n@example: 王者高光",
        update_time: "@datetime",
        create_time: "@datetime",
        job_status: "@integer(1, 100)",
        progress_percent: "@float(1, 100)",
        video_segment_num: "@integer(1, 100)",
        fail_message_info: "训练失败错误信息\n@description：\n@example:",
      },
    },
    props,
  ) as ImplResponse<GetVideoJobDetailResponse>
export type GetVideoListBySelectorRequest = {
  /**@name selector_query_list
筛选器查询列表
@description：
@example:*/
  selector_query_list?: {
    /**@name selector_id
筛选项ID
@description：
@example:*/
    selector_id?: string
    /**@name selector_value
筛选项的值
@description：
@example:*/
    selector_value?: string
  }[]
  /**@name content_list_type
内容列表类型
@description：
@example: 1
@enum:
1:获取视频模版列表
101:用于视频创建推荐视频列表筛选
3:用于视频创建的视频模版列表筛选器
2:获取直播模板列表
102:用于直播创建推荐视频列表筛选
4:用于直播创建的视频模版列表筛选器*/
  content_list_type?: number
}
export type GetVideoListBySelectorResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name video_info_list
视频信息对象
@description：视频对象
@example:*/
  video_info_list: {
    /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
    video_id: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url: string
    /**@name play_url
视频播放url
@description：
@example:*/
    play_url: string
    /**@name title
标题
@description：
@example:*/
    title: string
    /**@name description
描述
@description：
@example:*/
    description: string
    /**@name video_type
视频类型
@description：
@example:
@enum:
highlight:高光视频
promotion:投流推广视频
broadcase:咨询播报视频
e-commerce:电商投流
ppt-lecturer-training:PPT讲师培训
graph-and-text-lecturer-training:图文讲师培训*/
    video_type: string
    /**@name video_tags
视频标签
@description：多个标签通过;分隔
@example: 美男;程序员*/
    video_tags: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name score
得分
@description：
@example:*/
    score: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name is_have_border
是否带上下边框
@description：
@example: 0
@enum:
0:全部
1:带有
2:不带*/
    is_have_border: number
    /**@name is_have_captions
是否带有字幕
@description：
@example:
@enum:
0:全部
1:带有
2:不带*/
    is_have_captions: number
    /**@name is_have_digital_human
否带有数字人
@description：
@example: 0
@enum:
0:全部
1:带有
2:不带*/
    is_have_digital_human: number
    /**@name video_create_type
视频创建类型
@description：
@example: 1
@enum:
1:PPT
2:电商
3:游戏
4:文章
101:直播带货
102:广告素材
201:用户上传视频
202:高光视频
203:高光锦集*/
    video_create_type: number
    /**@name video_main_type
视频类型
@description：
@example: 1
@enum:
1:视频
2:直播*/
    video_main_type: number
    /**@name scene_specification_id
屏幕规格ID
@description：
@example:
@enum:
1:横屏
2:竖屏*/
    scene_specification_id: number
  }[]
}
/**@name GetVideoListBySelector
@summary GetVideoListBySelector
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVideoListBySelector = (
  props: ImplRequest<GetVideoListBySelectorRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetVideoListBySelector",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      "video_info_list|1-2": [
        {
          video_id:
            "视频ID\n@description：视频ID，在视频表中唯一的标识视频\n@example:",
          pic_url: "图片url\n@description：\n@example:",
          play_url: "视频播放url\n@description：\n@example:",
          title: "标题\n@description：\n@example:",
          description: "描述\n@description：\n@example:",
          video_type:
            "视频类型\n@description：\n@example:\n@enum:\nhighlight:高光视频\npromotion:投流推广视频\nbroadcase:咨询播报视频\ne-commerce:电商投流\nppt-lecturer-training:PPT讲师培训\ngraph-and-text-lecturer-training:图文讲师培训",
          video_tags:
            "视频标签\n@description：多个标签通过;分隔\n@example: 美男;程序员",
          status: "@integer(1, 100)",
          score: "@integer(1, 100)",
          create_time: "@datetime",
          update_time: "@datetime",
          is_have_border: "@integer(1, 100)",
          is_have_captions: "@integer(1, 100)",
          is_have_digital_human: "@integer(1, 100)",
          video_create_type: "@integer(1, 100)",
          video_main_type: "@integer(1, 100)",
          scene_specification_id: "@integer(1, 100)",
        },
      ],
    },
    props,
  ) as ImplResponse<GetVideoListBySelectorResponse>
export type GetVideoTemplateListRequest = {
  /**@name selector_query_list
筛选器查询列表
@description：
@example:*/
  selector_query_list?: {
    /**@name selector_id
筛选项ID
@description：
@example:*/
    selector_id?: string
    /**@name selector_value
筛选项的值
@description：
@example:*/
    selector_value?: string
  }[]
  /**@name page_size
页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name search_key
搜索关键字
@description：
@example:*/
  search_key?: string
  /**@name content_list_type
内容列表类型
@description：
@example: 1
@enum:
1:获取视频模版列表
101:用于视频创建推荐视频列表筛选
3:用于视频创建的视频模版列表筛选器
2:获取直播模板列表
102:用于直播创建推荐视频列表筛选
4:用于直播创建的视频模版列表筛选器*/
  content_list_type?: number
}
export type GetVideoTemplateListResponse = {
  /**@name video_template_list
视频模版
@description：
@example:*/
  video_template_list: {
    /**@name video_template_id
视频模版ID
@description：
@example:*/
    video_template_id: string
    /**@name video_type_id
视频类型ID
@description：
@example:
@enum:
1:规则说明类
2:功能指引类
3:经营指导类*/
    video_type_id: number
    /**@name video_template_name
视频模版名
@description：
@example:*/
    video_template_name: string
    /**@name scene_specification_id
屏幕规格ID
@description：
@example:
@enum:
1:横屏
2:竖屏*/
    scene_specification_id: number
    /**@name video_template_pic_url
视频模版图片url
@description：
@example:*/
    video_template_pic_url: string
    /**@name application_scenarios
应用场景
@description：对应t_video_info
@example:
@enum:
1:PPT
2:电商
3:游戏
4:文章
101:直播带货
102:广告素材*/
    application_scenarios: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name score
得分
@description：
@example:*/
    score: number
    /**@name content_list_type
内容列表类型
@description：
@example: 1
@enum:
1:获取视频模版列表
101:用于视频创建推荐视频列表筛选
3:用于视频创建的视频模版列表筛选器
2:获取直播模板列表
102:用于直播创建推荐视频列表筛选
4:用于直播创建的视频模版列表筛选器*/
    content_list_type: number
    /**@name is_have_border
是否带上下边框
@description：
@example: 0
@enum:
0:全部
1:带有
2:不带*/
    is_have_border: number
    /**@name is_have_captions
是否带有字幕
@description：
@example:
@enum:
0:全部
1:带有
2:不带*/
    is_have_captions: number
    /**@name is_have_digital_human
否带有数字人
@description：
@example: 0
@enum:
0:全部
1:带有
2:不带*/
    is_have_digital_human: number
    /**@name template_main_type
模版主类型
@description：
@example:
@enum:
1:视频
2:直播*/
    template_main_type: number
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
}
/**@name GetVideoTemplateList
@summary GetVideoTemplateList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVideoTemplateList = (
  props: ImplRequest<GetVideoTemplateListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetVideoTemplateList",
    {
      "video_template_list|1-2": [
        {
          video_template_id: "视频模版ID\n@description：\n@example:",
          video_type_id: "@integer(1, 100)",
          video_template_name: "视频模版名\n@description：\n@example:",
          scene_specification_id: "@integer(1, 100)",
          video_template_pic_url: "视频模版图片url\n@description：\n@example:",
          application_scenarios: "@integer(1, 100)",
          create_time: "@datetime",
          update_time: "@datetime",
          score: "@integer(1, 100)",
          content_list_type: "@integer(1, 100)",
          is_have_border: "@integer(1, 100)",
          is_have_captions: "@integer(1, 100)",
          is_have_digital_human: "@integer(1, 100)",
          template_main_type: "@integer(1, 100)",
        },
      ],
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      query_count: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<GetVideoTemplateListResponse>
export type GetVideoTemplateSelectorRequest = {
  /**@name content_list_type
内容列表类型
@description：
@example: 1
@enum:
1:获取视频模版列表
101:用于视频创建推荐视频列表筛选
3:用于视频创建的视频模版列表筛选器
2:获取直播模板列表
102:用于直播创建推荐视频列表筛选
4:用于直播创建的视频模版列表筛选器*/
  content_list_type?: number
}
export type GetVideoTemplateSelectorResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name content_selector_list
内容筛选器列表
@description：
@example:*/
  content_selector_list: {
    /**@name selector_id
筛选项ID
@description：
@example:*/
    selector_id: string
    /**@name selector_name
筛选项名字
@description：
@example:*/
    selector_name: string
    /**@name selector_class_list
筛选类列表
@description：
@example:*/
    selector_class_list: {
      /**@name selector_item_list
筛选项列表
@description：
@example:*/
      selector_item_list: {
        /**@name selector_name
筛选项名字
@description：
@example:*/
        selector_name: string
        /**@name selector_value
筛选项的值
@description：
@example:*/
        selector_value: string
      }[]
      /**@name selector_id
筛选项ID
@description：
@example:*/
      selector_id: string
      /**@name selector_label_name
选择器的label名字
@description：
@example:*/
      selector_label_name: string
    }[]
    /**@name selector_value
筛选项的值
@description：
@example:*/
    selector_value: string
  }[]
  /**@name selector_id
筛选项ID
@description：
@example:*/
  selector_id: string
  /**@name selector_label_name
选择器的label名字
@description：
@example:*/
  selector_label_name: string
}
/**@name GetVideoTemplateSelector
@summary GetVideoTemplateSelector
@description  content_list_type=1：老的视频模版筛选列表；2：老的直播模板列表；3：视频创建视频模版筛选列表，4: 直播创建直播筛选列表；101：表示视频创建视频推荐列表；102：表示直播创建视频推荐列表;
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVideoTemplateSelector = (
  props: ImplRequest<GetVideoTemplateSelectorRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetVideoTemplateSelector",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      "content_selector_list|1-2": [
        {
          selector_id: "筛选项ID\n@description：\n@example:",
          selector_name: "筛选项名字\n@description：\n@example:",
          "selector_class_list|1-2": [
            {
              "selector_item_list|1-2": [
                {
                  selector_name: "筛选项名字\n@description：\n@example:",
                  selector_value: "筛选项的值\n@description：\n@example:",
                },
              ],
              selector_id: "筛选项ID\n@description：\n@example:",
              selector_label_name:
                "选择器的label名字\n@description：\n@example:",
            },
          ],
          selector_value: "筛选项的值\n@description：\n@example:",
        },
      ],
      selector_id: "筛选项ID\n@description：\n@example:",
      selector_label_name: "选择器的label名字\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<GetVideoTemplateSelectorResponse>
export type GetVoiceResourceListRequest = {
  /**@name app_code
应用代码
@description：
@example:*/
  app_code?: string
  /**@name category_level2
二级类目
@description：
@example:*/
  category_level2?: string
  /**@name category_level1
一级类目
@description：
@example:*/
  category_level1?: string
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name auth_level
权限级别
@description：公共：public，私有：private，全部：为空/all
@example:*/
  auth_level?: string
  /**@name order_by
排序字段
@description：排序的字段
@example:*/
  order_by?: string
}
export type GetVoiceResourceListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name voice_info_list
用户音色资源管理
@description：
@example:*/
  voice_info_list: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name app_code
应用代码
@description：
@example:*/
    app_code: string
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name voice_authority
音色权限
@description：
@example:*/
    voice_authority: number
    /**@name voice_id
音色ID
@description：
@example:*/
    voice_id: string
    /**@name voice_name
音色名称
@description：
@example:*/
    voice_name: string
    /**@name voice_image
音色图片
@description：
@example:*/
    voice_image: string
    /**@name voice_feature_keywords
音色特征关键字
@description：
@example:*/
    voice_feature_keywords: string
    /**@name voice_description
音色描述
@description：
@example:*/
    voice_description: string
    /**@name audition_address
音色地址
@description：
@example:*/
    audition_address: string
    /**@name voice_text
音色文本
@description：
@example:*/
    voice_text: string
    /**@name voice_extend_config
音色扩展配置
@description：
@example:*/
    voice_extend_config: string
    /**@name drive_mode
驱动方式
@description：
@example:*/
    drive_mode: string
    /**@name platform_account_id
平台账号id
@description：
@example:*/
    platform_account_id: string
    /**@name gender
性别
@description：性别：0-未知，1-男性，2-女性
@example:*/
    gender: number
  }[]
}
/**@name GetVoiceResourceList
@summary GetVoiceResourceList
@description  根据类别查询音色列表信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetVoiceResourceList = (
  props: ImplRequest<GetVoiceResourceListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/GetVoiceResourceList",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "code\n@description：\n@example:",
      "voice_info_list|1-2": [
        {
          user_id: "用户ID\n@description：\n@example:",
          app_code: "应用代码\n@description：\n@example:",
          platform: "平台\n@description：\n@example:",
          voice_authority: "@integer(1, 100)",
          voice_id: "音色ID\n@description：\n@example:",
          voice_name: "音色名称\n@description：\n@example:",
          voice_image: "音色图片\n@description：\n@example:",
          voice_feature_keywords: "音色特征关键字\n@description：\n@example:",
          voice_description: "音色描述\n@description：\n@example:",
          audition_address: "音色地址\n@description：\n@example:",
          voice_text: "音色文本\n@description：\n@example:",
          voice_extend_config: "音色扩展配置\n@description：\n@example:",
          drive_mode: "驱动方式\n@description：\n@example:",
          platform_account_id: "平台账号id\n@description：\n@example:",
          gender: "@integer(1, 100)",
        },
      ],
    },
    props,
  ) as ImplResponse<GetVoiceResourceListResponse>
export type QueryResourceListRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name app_code
应用代码
@description：
@example:*/
  app_code?: string
  /**@name category_level1
一级类目
@description：
@example:*/
  category_level1?: string
  /**@name category_level2
二级类目
@description：
@example:*/
  category_level2?: string
  /**@name category_type
品类类型
@description：
@example:*/
  category_type?: string
  /**@name page_size
页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
}
export type QueryResourceListResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name resource_info_list
资源列表
@description：
@example:*/
  resource_info_list: {
    /**@name resource_id
资源id
@description：
@example:*/
    resource_id: string
    /**@name image_address
图片地址
@description：
@example:*/
    image_address: string
    /**@name resource_name
资源名称
@description：
@example:*/
    resource_name: string
    /**@name audio_address
音频地址
@description：
@example:*/
    audio_address: string
    /**@name resource_description
资源描述
@description：
@example:*/
    resource_description: string
  }[]
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
}
/**@name QueryResourceList
@summary QueryResourceList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryResourceList = (
  props: ImplRequest<QueryResourceListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/QueryResourceList",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      "resource_info_list|1-2": [
        {
          resource_id: "资源id\n@description：\n@example:",
          image_address: "图片地址\n@description：\n@example:",
          resource_name: "资源名称\n@description：\n@example:",
          audio_address: "音频地址\n@description：\n@example:",
          resource_description: "资源描述\n@description：\n@example:",
        },
      ],
      query_count: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<QueryResourceListResponse>
export type QueryVoiceResourceDetailRequest = {
  /**@name app_code
应用代码
@description：
@example:*/
  app_code?: string
  /**@name voice_id
音色ID
@description：
@example:*/
  voice_id?: string
}
export type QueryVoiceResourceDetailResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name voice_info
用户音色资源管理
@description：
@example:*/
  voice_info: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name app_code
应用代码
@description：
@example:*/
    app_code: string
    /**@name platform
平台
@description：
@example:*/
    platform: string
    /**@name voice_authority
音色权限
@description：
@example:*/
    voice_authority: number
    /**@name voice_id
音色ID
@description：
@example:*/
    voice_id: string
    /**@name voice_name
音色名称
@description：
@example:*/
    voice_name: string
    /**@name voice_image
音色图片
@description：
@example:*/
    voice_image: string
    /**@name voice_feature_keywords
音色特征关键字
@description：
@example:*/
    voice_feature_keywords: string
    /**@name voice_description
音色描述
@description：
@example:*/
    voice_description: string
    /**@name audition_address
音色地址
@description：
@example:*/
    audition_address: string
    /**@name voice_text
音色文本
@description：
@example:*/
    voice_text: string
    /**@name voice_extend_config
音色扩展配置
@description：
@example:*/
    voice_extend_config: string
    /**@name drive_mode
驱动方式
@description：
@example:*/
    drive_mode: string
    /**@name platform_account_id
平台账号id
@description：
@example:*/
    platform_account_id: string
    /**@name gender
性别
@description：性别：0-未知，1-男性，2-女性
@example:*/
    gender: number
  }
}
/**@name QueryVoiceResourceDetail
@summary QueryVoiceResourceDetail
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryVoiceResourceDetail = (
  props: ImplRequest<QueryVoiceResourceDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/QueryVoiceResourceDetail",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      voice_info: {
        user_id: "用户ID\n@description：\n@example:",
        app_code: "应用代码\n@description：\n@example:",
        platform: "平台\n@description：\n@example:",
        voice_authority: "@integer(1, 100)",
        voice_id: "音色ID\n@description：\n@example:",
        voice_name: "音色名称\n@description：\n@example:",
        voice_image: "音色图片\n@description：\n@example:",
        voice_feature_keywords: "音色特征关键字\n@description：\n@example:",
        voice_description: "音色描述\n@description：\n@example:",
        audition_address: "音色地址\n@description：\n@example:",
        voice_text: "音色文本\n@description：\n@example:",
        voice_extend_config: "音色扩展配置\n@description：\n@example:",
        drive_mode: "驱动方式\n@description：\n@example:",
        platform_account_id: "平台账号id\n@description：\n@example:",
        gender: "@integer(1, 100)",
      },
    },
    props,
  ) as ImplResponse<QueryVoiceResourceDetailResponse>
export type SaveJobVideoToUserVideoRequest = {
  /**@name user_job_video_result_list
用户任务输出视频列表对象
@description：
@example:*/
  user_job_video_result_list?: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id?: string
    /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
    video_id?: string
    /**@name video_index_id
视频序号ID
@description：
@example:*/
    video_index_id?: number
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status?: number
    /**@name video_name
视频名称
@description：
@example:*/
    video_name?: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url?: string
    /**@name video_content
视频描述
@description：
@example:*/
    video_content?: string
    /**@name score
得分
@description：
@example:*/
    score?: number
    /**@name video_url
视频url地址
@description：
@example:*/
    video_url?: string
    /**@name video_cos_path
视频cos上的相对路径
@description：
@example:*/
    video_cos_path?: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time?: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time?: string
    /**@name is_have_save_user_video
是否保存为用户视频
@description：0: 未知状态，用于筛选时使用；1:保存为用户视频；2:未保存为用户视频。默认为2
@example:*/
    is_have_save_user_video?: number
    /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
    video_duration?: string
    /**@name video_size
视频大小
@description：2Gb代表2G
@example:*/
    video_size?: string
    /**@name video_slice_extend_config
视频切片扩展信息
@description：{
"segment_id": 1,
"start_time": "",
"end_time": "0",
"segment_text": "",
"video_text": ""
@example:*/
    video_slice_extend_config?: string
  }[]
}
export type SaveJobVideoToUserVideoResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name SaveJobVideoToUserVideo
@summary SaveJobVideoToUserVideo
@description  只需要填写video_id和job_id，其他不用填写
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveJobVideoToUserVideo = (
  props: ImplRequest<SaveJobVideoToUserVideoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/SaveJobVideoToUserVideo",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SaveJobVideoToUserVideoResponse>
export type StoreDipImageResourceRequest = {
  /**@name image_id
数字人形象ID
@description：
@example:*/
  image_id?: string
}
export type StoreDipImageResourceResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name StoreDipImageResource
@summary StoreDipImageResource
@description  数字人形象资源入库
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const StoreDipImageResource = (
  props: ImplRequest<StoreDipImageResourceRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/StoreDipImageResource",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<StoreDipImageResourceResponse>
export type StoreDipVoiceResourceRequest = {
  /**@name voice_task_id
音色任务ID
@description：
@example:*/
  voice_task_id?: string
}
export type StoreDipVoiceResourceResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name StoreDipVoiceResource
@summary StoreDipVoiceResource
@description  数字人音色资源入库
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const StoreDipVoiceResource = (
  props: ImplRequest<StoreDipVoiceResourceRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/StoreDipVoiceResource",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<StoreDipVoiceResourceResponse>
export type UpdateUserVideoInforRequest = {
  /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
  video_id?: string
  /**@name video_name
视频名称
@description：
@example:*/
  video_name?: string
  /**@name video_content
视频描述
@description：
@example:*/
  video_content?: string
}
export type UpdateUserVideoInforResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name UpdateUserVideoInfor
@summary UpdateUserVideoInfor
@description  修改用户视频资料，如果字段为空，代表该字段不修改；
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateUserVideoInfor = (
  props: ImplRequest<UpdateUserVideoInforRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/UpdateUserVideoInfor",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<UpdateUserVideoInforResponse>
export type VideoMakeJobCallbackRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
  /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
  job_status?: number
  /**@name fail_message_info
训练失败错误信息
@description：
@example:*/
  fail_message_info?: string
  /**@name app_id
数字人应用id
@description：和system_code保持一致
@example:*/
  app_id?: string
  /**@name info
任务信息
@description：
@example:*/
  info?: {
    /**@name type
任务类型
@description：任务类型，包括在线任务和离线任务；
@example:
@enum:
3:音频tts训练任务
101:高光片段生成
102:高光集锦生成
1001:在线客服默认问答匹配text_default_video_match
1:H5转点播视频
2:H5转直播视频
4:音质检查
103:多个视频合并*/
    type?: number
  }
  /**@name metadata
视频任务元数据结构体
@description：回调的视频任务metadata数据；user_job_video_result需填写如下字段：video_name、pic_url、video_content、score、video_url、video_duration、video_size
@example:*/
  metadata?: {
    /**@name user_job_video_result_list
用户任务输出视频列表对象
@description：
@example:*/
    user_job_video_result_list?: {
      /**@name job_id
任务ID
@description：
@example:*/
      job_id?: string
      /**@name video_id
视频ID
@description：视频ID，在视频表中唯一的标识视频
@example:*/
      video_id?: string
      /**@name video_index_id
视频序号ID
@description：
@example:*/
      video_index_id?: number
      /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
      status?: number
      /**@name video_name
视频名称
@description：
@example:*/
      video_name?: string
      /**@name pic_url
图片url
@description：
@example:*/
      pic_url?: string
      /**@name video_content
视频描述
@description：
@example:*/
      video_content?: string
      /**@name score
得分
@description：
@example:*/
      score?: number
      /**@name video_url
视频url地址
@description：
@example:*/
      video_url?: string
      /**@name video_cos_path
视频cos上的相对路径
@description：
@example:*/
      video_cos_path?: string
      /**@name create_time
创建时间
@description：
@example:*/
      create_time?: string
      /**@name update_time
更新时间
@description：
@example:*/
      update_time?: string
      /**@name is_have_save_user_video
是否保存为用户视频
@description：0: 未知状态，用于筛选时使用；1:保存为用户视频；2:未保存为用户视频。默认为2
@example:*/
      is_have_save_user_video?: number
      /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
      video_duration?: string
      /**@name video_size
视频大小
@description：2Gb代表2G
@example:*/
      video_size?: string
      /**@name video_slice_extend_config
视频切片扩展信息
@description：{
"segment_id": 1,
"start_time": "",
"end_time": "0",
"segment_text": "",
"video_text": ""
@example:*/
      video_slice_extend_config?: string
    }[]
  }
}
export type VideoMakeJobCallbackResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name VideoMakeJobCallback
@summary VideoMakeJobCallback
@description  回调的视频任务metadata数据；user_job_video_result需填写如下字段：job_id、video_name、pic_url、video_content、score、video_url、video_duration、video_size
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const VideoMakeJobCallback = (
  props: ImplRequest<VideoMakeJobCallbackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.virtual_man_resource_manager.Manager/VideoMakeJobCallback",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<VideoMakeJobCallbackResponse>
