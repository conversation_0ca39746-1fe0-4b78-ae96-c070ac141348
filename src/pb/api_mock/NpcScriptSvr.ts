/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type CreateNpcJobRequest = {
  /**@name model_input_type
模型输入类型
@description：模型输入类型
@example:
@enum:
html:网页类
docx:文档docx类
ppt:ppt类型
doc:文档doc类
gameProm:游戏推广类
eCommerce:电商脚本类
intelligentQA:智能问答类
content:内容脚本类*/
  model_input_type?: string
  /**@name npc_script_input
npc脚本输入
@description：
@example:*/
  npc_script_input?: {
    /**@name npc_comm_header
npc模型服务通用包头
@description：npc通用包头
@example:*/
    npc_comm_header?: {
      /**@name model_type
模型类型
@description：
@example:*/
      model_type?: string
      /**@name model_domain
模型行业类目
@description：
@example:*/
      model_domain?: string
      /**@name model_script_time
脚本时长
@description：脚本时长【单位换成秒】
@example: 100*/
      model_script_time?: string
      /**@name auto_create_script
是否自动创建脚本
@description：true-自动创建
@example:*/
      auto_create_script?: boolean
      /**@name subject_parameters
模型主题参数列表
@description：模型主题参数列表，["剧本大标题", "剧本小标题"]
@example:*/
      subject_parameters?: string[]
      /**@name model_main_points
模型的侧重点列表
@description：
@example:*/
      model_main_points?: string[]
    }
    /**@name npc_e_commerce_input
npc模型电商脚本输入
@description：npc_e_commerce_input
@example:*/
    npc_e_commerce_input?: {
      /**@name commerce_goods
电商商品列表
@description：电商商品列表
@example:*/
      commerce_goods?: {
        /**@name activity
商品节庆活动
@description：
@example:*/
        activity?: string
        /**@name serve
商品服务保障
@description：
@example:*/
        serve?: string
        /**@name reward
商品福利
@description：
@example:*/
        reward?: string
        /**@name seller
商品卖点
@description：
@example:*/
        seller?: string
        /**@name name
商品名称
@description：
@example:*/
        name?: string
        /**@name knowledge
主题介绍
@description：
@example:*/
        knowledge?: string
        /**@name img
图像对象列表
@description：
@example:*/
        img?: {
          /**@name url
内容图片url地址
@description：
@example:*/
          url?: string
        }[]
      }[]
    }
    /**@name npc_game_prom_input
npc模型游戏推广脚本输入
@description：
@example:*/
    npc_game_prom_input?: {
      /**@name themes
游戏推广主题列表
@description：
@example:*/
      themes?: {
        /**@name name
主题名称
@description：
@example:*/
        name?: string
        /**@name knowledge
主题介绍
@description：
@example:*/
        knowledge?: string
      }[]
      /**@name videos
游戏视频列表
@description：
@example:*/
      videos?: {
        /**@name name
视频名称
@description：
@example:*/
        name?: string
        /**@name url
视频url地址
@description：
@example:*/
        url?: string
        /**@name content
视频描述
@description：
@example:*/
        content?: string
        /**@name themes
游戏推广主题列表
@description：
@example:*/
        themes?: NonNullable<
          NonNullable<
            NonNullable<
              CreateNpcJobRequest["npc_script_input"]
            >["npc_game_prom_input"]
          >["themes"]
        >[0][]
      }[]
    }
    /**@name npc_ppt_input
npc大模型ppt输入
@description：
@example:*/
    npc_ppt_input?: {
      /**@name ppt_url
ppt的url地址
@description：
@example:*/
      ppt_url?: string
    }
    /**@name npc_doc_input
npc模型文档输入
@description：
@example:*/
    npc_doc_input?: {
      /**@name file_url
文档等介质的访问地址
@description：
@example:*/
      file_url?: string
    }
    /**@name npc_content_input
npc内容脚本输入
@description：
@example:*/
    npc_content_input?: {
      /**@name content_list
npc内容列表
@description：
@example:*/
      content_list?: {
        /**@name name
内容名称
@description：
@example:*/
        name?: string
        /**@name url
内容图片url地址
@description：
@example:*/
        url?: string
        /**@name detail
内容详情
@description：
@example:*/
        detail?: string
      }[]
    }
  }
}
export type CreateNpcJobResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name data
响应
@description：
@example:*/
  data: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
  }
}
/**@name CreateNpcJob
@summary CreateNpcJob
@description  // @alias 异步创建NPC任务
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateNpcJob = (
  props: ImplRequest<CreateNpcJobRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.npc_job_controller.NpcScriptSvr/CreateNpcJob",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      data: { job_id: "任务ID\n@description：\n@example:" },
    },
    props,
  ) as ImplResponse<CreateNpcJobResponse>
export type QueryNpcJobDataRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type QueryNpcJobDataResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name data
响应
@description：
@example:*/
  data: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
    job_status: number
    /**@name prompt_content
输入内容格式化数据
@description：
@example:*/
    prompt_content: string
    /**@name script_content
剧本信息
@description：
@example:*/
    script_content: string
    /**@name job_stage
任务运行阶段状态
@description：任务阶段
@example:
@enum:
input_content_prompt:输入内容处理阶段
script_generate:剧本生成阶段
children_job_get_video_scripts:子任务获取视频脚本
children_job_videos_merge:子任务合并多个视频为一个*/
    job_stage: string
    /**@name script_element_content
脚本元素内容
@description：
@example:*/
    script_element_content: string
    /**@name info
视频信息对象
@description：
@example:*/
    info: {
      /**@name video_duration
视频时长
@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；
@example:*/
      video_duration: string
      /**@name video_url
视频url地址
@description：
@example:*/
      video_url: string
    }
    /**@name subject_content
脚本主题内容
@description：
@example:*/
    subject_content: string
  }
}
/**@name QueryNpcJobData
@summary QueryNpcJobData
@description  查询NPC任务信息，异步任务需要
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryNpcJobData = (
  props: ImplRequest<QueryNpcJobDataRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.npc_job_controller.NpcScriptSvr/QueryNpcJobData",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      data: {
        job_id: "任务ID\n@description：\n@example:",
        job_status: "@integer(1, 100)",
        prompt_content: "输入内容格式化数据\n@description：\n@example:",
        script_content: "剧本信息\n@description：\n@example:",
        job_stage:
          "任务运行阶段状态\n@description：任务阶段\n@example:\n@enum:\ninput_content_prompt:输入内容处理阶段\nscript_generate:剧本生成阶段\nchildren_job_get_video_scripts:子任务获取视频脚本\nchildren_job_videos_merge:子任务合并多个视频为一个",
        script_element_content: "脚本元素内容\n@description：\n@example:",
        info: {
          video_duration:
            '视频时长\n@description：单位为s，例如“100”代表100s，为空或任何非法整数，将转化为"0"，代表0s；\n@example:',
          video_url: "视频url地址\n@description：\n@example:",
        },
        subject_content: "脚本主题内容\n@description：\n@example:",
      },
    },
    props,
  ) as ImplResponse<QueryNpcJobDataResponse>
export type QueryNpcJobStatusRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context?: string
}
export type QueryNpcJobStatusResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name data
响应
@description：
@example:*/
  data: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
    job_status: number
    /**@name progress_percent
job当前状态处理进度
@description：12.3代表12.3%
@example:*/
    progress_percent: number
    /**@name job_stage
任务运行阶段状态
@description：任务阶段
@example:
@enum:
input_content_prompt:输入内容处理阶段
script_generate:剧本生成阶段
children_job_get_video_scripts:子任务获取视频脚本
children_job_videos_merge:子任务合并多个视频为一个*/
    job_stage: string
    /**@name stage_name
阶段名称
@description：
@example:*/
    stage_name: string
  }
}
/**@name QueryNpcJobStatus
@summary QueryNpcJobStatus
@description  // @alias QueryNpcJobStatus
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryNpcJobStatus = (
  props: ImplRequest<QueryNpcJobStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.npc_job_controller.NpcScriptSvr/QueryNpcJobStatus",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      data: {
        job_id: "任务ID\n@description：\n@example:",
        job_status: "@integer(1, 100)",
        progress_percent: "@float(1, 100)",
        job_stage:
          "任务运行阶段状态\n@description：任务阶段\n@example:\n@enum:\ninput_content_prompt:输入内容处理阶段\nscript_generate:剧本生成阶段\nchildren_job_get_video_scripts:子任务获取视频脚本\nchildren_job_videos_merge:子任务合并多个视频为一个",
        stage_name: "阶段名称\n@description：\n@example:",
      },
    },
    props,
  ) as ImplResponse<QueryNpcJobStatusResponse>
export type StartNpcScriptsTaskRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type StartNpcScriptsTaskResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name data
响应
@description：
@example:*/
  data: {
    /**@name job_id
任务ID
@description：
@example:*/
    job_id: string
    /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败
7:npc任务处理完成*/
    job_status: number
  }
}
/**@name StartNpcScriptsTask
@summary StartNpcScriptsTask
@description  异步化任务调用API，开始npc脚本任务执行// @alias StartNpcScriptsTask
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const StartNpcScriptsTask = (
  props: ImplRequest<StartNpcScriptsTaskRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.npc_job_controller.NpcScriptSvr/StartNpcScriptsTask",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      data: {
        job_id: "任务ID\n@description：\n@example:",
        job_status: "@integer(1, 100)",
      },
    },
    props,
  ) as ImplResponse<StartNpcScriptsTaskResponse>
