/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type GetGoodsDetailRequest = { product_id?: string }
export type GetGoodsDetailResponse = {
  result_code: string
  result_info: string
  data: {
    goods_info_list: {
      product_id: string
      product_name: string
      /**@name price
价格
@description：商品价格，如“拍一发三”、“99元三件”等
@example: "99元三件"*/
      price: string
      /**@name selling_points
商品卖点
@description：商品卖点
@example: 5G连接、超级摄像头、强大的A15 Bionic芯片*/
      selling_points: string
      /**@name user_pain_points
用户痛点
@description：用户痛点
@example: 续航时间短、价格偏高*/
      user_pain_points: string
      /**@name detail_info
商品详细介绍
@description：商品详细介绍
@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"*/
      detail_info: string
      /**@name endorsement
口碑背书
@description：口碑背书
@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"*/
      endorsement: string
      /**@name promotion
促销活动
@description：促销活动
@example: "下单立享9折优惠,赠送无线充电器一个。"*/
      promotion: string
      /**@name after_sale
售后保障
@description：售后保障
@example: "享受2年质保,7天无理由退换货。"*/
      after_sale: string
      /**@name evaluation
商品测评
@description：商品测评
@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"*/
      evaluation: string
      header_imgs: string[]
    }[]
  }
}
/**@name GetGoodsDetail
@summary 获取默认的回复
@alias /api/guanggao_goods_digital_man.DigitalManProc/GetGoodsDetail
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetGoodsDetail = (
  props: ImplRequest<GetGoodsDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GetGoodsDetail",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "goods_info_list|1-2": [
          {
            product_id: "@string",
            product_name: "@string",
            price:
              '价格\n@description：商品价格，如“拍一发三”、“99元三件”等\n@example: "99元三件"',
            selling_points:
              "商品卖点\n@description：商品卖点\n@example: 5G连接、超级摄像头、强大的A15 Bionic芯片",
            user_pain_points:
              "用户痛点\n@description：用户痛点\n@example: 续航时间短、价格偏高",
            detail_info:
              '商品详细介绍\n@description：商品详细介绍\n@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"',
            endorsement:
              '口碑背书\n@description：口碑背书\n@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"',
            promotion:
              '促销活动\n@description：促销活动\n@example: "下单立享9折优惠,赠送无线充电器一个。"',
            after_sale:
              '售后保障\n@description：售后保障\n@example: "享受2年质保,7天无理由退换货。"',
            evaluation:
              '商品测评\n@description：商品测评\n@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"',
            "header_imgs|1-2": ["@string"],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<GetGoodsDetailResponse>
export type ScriptGenerateRequest = {
  live_id?: string
  goods_info_list?: {
    product_id?: string
    product_name?: string
    /**@name price
价格
@description：商品价格，如“拍一发三”、“99元三件”等
@example: "99元三件"*/
    price?: string
    /**@name selling_points
商品卖点
@description：商品卖点
@example: 5G连接、超级摄像头、强大的A15 Bionic芯片*/
    selling_points?: string
    /**@name user_pain_points
用户痛点
@description：用户痛点
@example: 续航时间短、价格偏高*/
    user_pain_points?: string
    /**@name detail_info
商品详细介绍
@description：商品详细介绍
@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"*/
    detail_info?: string
    /**@name endorsement
口碑背书
@description：口碑背书
@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"*/
    endorsement?: string
    /**@name promotion
促销活动
@description：促销活动
@example: "下单立享9折优惠,赠送无线充电器一个。"*/
    promotion?: string
    /**@name after_sale
售后保障
@description：售后保障
@example: "享受2年质保,7天无理由退换货。"*/
    after_sale?: string
    /**@name evaluation
商品测评
@description：商品测评
@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"*/
    evaluation?: string
    header_imgs?: string[]
  }[]
  tone?: string
  style?: string
}
export type ScriptGenerateResponse = {
  result_code: string
  result_info: string
}
/**@name ScriptGenerate
@summary 获取默认的回复
@alias /api/guanggao_goods_digital_man.DigitalManProc/ScriptGenerate
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ScriptGenerate = (
  props: ImplRequest<ScriptGenerateRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/ScriptGenerate",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ScriptGenerateResponse>
export type GetLiveScriptRequest = {
  live_id?: string
  /**@name is_preview
是否拉取预览话术
@description：true:拉取预览话术; false:拉取直播话术
@example: true*/
  is_preview?: boolean
  /**@name last_script_id
确认上一条话术ID
@description：首次拉取为空
@example: 1001*/
  last_script_id?: string
}
export type GetLiveScriptResponse = {
  result_code: string
  result_info: string
  data: {
    script_list: {
      live_id: string
      /**@name script_id
预览话术ID
@description：AI生成的预览话术id,自助上传无
@example: 1001*/
      script_id: string
      /**@name script_content
预览话术
@description：预览话术
@example: 这就是一个卖货的内容话术*/
      script_content: string
      /**@name product_id
物品id
@description：预览话术的物品id
@example: product_id*/
      product_id: string
    }[]
  }
}
/**@name GetLiveScript
@summary 获取默认的回复
@alias /api/guanggao_goods_digital_man.DigitalManProc/ScriptGenerate
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetLiveScript = (
  props: ImplRequest<GetLiveScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GetLiveScript",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "goods_info_list|1-2": [
          {
            product_id: "@string",
            product_name: "@string",
            price:
              '价格\n@description：商品价格，如“拍一发三”、“99元三件”等\n@example: "99元三件"',
            selling_points:
              "商品卖点\n@description：商品卖点\n@example: 5G连接、超级摄像头、强大的A15 Bionic芯片",
            user_pain_points:
              "用户痛点\n@description：用户痛点\n@example: 续航时间短、价格偏高",
            detail_info:
              '商品详细介绍\n@description：商品详细介绍\n@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"',
            endorsement:
              '口碑背书\n@description：口碑背书\n@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"',
            promotion:
              '促销活动\n@description：促销活动\n@example: "下单立享9折优惠,赠送无线充电器一个。"',
            after_sale:
              '售后保障\n@description：售后保障\n@example: "享受2年质保,7天无理由退换货。"',
            evaluation:
              '商品测评\n@description：商品测评\n@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"',
            "header_imgs|1-2": ["@string"],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<GetLiveScriptResponse>
export type ConfirmScriptSeedRequest = {
  live_id?: string
  script_list?: {
    live_id?: string
    /**@name script_id
预览话术ID
@description：AI生成的预览话术id,自助上传无
@example: 1001*/
    script_id?: string
    /**@name script_content
预览话术
@description：预览话术
@example: 这就是一个卖货的内容话术*/
    script_content?: string
    /**@name product_id
物品id
@description：预览话术的物品id
@example: product_id*/
    product_id?: string
  }[]
}
export type ConfirmScriptSeedResponse = {
  result_code: string
  result_info: string
}
/**@name ConfirmScriptSeed
@summary 确认预览话术种子
@alias /api/guanggao_goods_digital_man.DigitalManProc/ConfirmScriptSeed
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ConfirmScriptSeed = (
  props: ImplRequest<ConfirmScriptSeedRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/ConfirmScriptSeed",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ConfirmScriptSeedResponse>
export type BeginLiveCheckRequest = { live_id?: string }
export type BeginLiveCheckResponse = {
  result_code: string
  result_info: string
  data: { check_result: boolean }
}
/**@name BeginLiveCheck
@summary 开播检查
@alias /api/guanggao_goods_digital_man.DigitalManProc/BeginLiveCheck
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const BeginLiveCheck = (
  props: ImplRequest<BeginLiveCheckRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/BeginLiveCheck",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "goods_info_list|1-2": [
          {
            product_id: "@string",
            product_name: "@string",
            price:
              '价格\n@description：商品价格，如“拍一发三”、“99元三件”等\n@example: "99元三件"',
            selling_points:
              "商品卖点\n@description：商品卖点\n@example: 5G连接、超级摄像头、强大的A15 Bionic芯片",
            user_pain_points:
              "用户痛点\n@description：用户痛点\n@example: 续航时间短、价格偏高",
            detail_info:
              '商品详细介绍\n@description：商品详细介绍\n@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"',
            endorsement:
              '口碑背书\n@description：口碑背书\n@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"',
            promotion:
              '促销活动\n@description：促销活动\n@example: "下单立享9折优惠,赠送无线充电器一个。"',
            after_sale:
              '售后保障\n@description：售后保障\n@example: "享受2年质保,7天无理由退换货。"',
            evaluation:
              '商品测评\n@description：商品测评\n@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"',
            "header_imgs|1-2": ["@string"],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<BeginLiveCheckResponse>
export type SyncQaContentRequest = { live_id?: string; app_id?: string }
export type SyncQaContentResponse = { result_code: string; result_info: string }
/**@name SyncQaContent
@summary 同步问答库
@alias /api/guanggao_goods_digital_man.DigitalManProc/SyncQaContent
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SyncQaContent = (
  props: ImplRequest<SyncQaContentRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/SyncQaContent",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SyncQaContentResponse>
/**@name CreateShopRequest
创建小店请求消息*/
export type CreateShopRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name shop_name
小店名称*/
  shop_name?: string
  /**@name shop_app_id
小店ID*/
  shop_app_id?: string
  /**@name shop_app_secret
小店密钥*/
  shop_app_secret?: string
}
/**@name CreateShopResponse
创建小店回复消息*/
export type CreateShopResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  data: {
    /**@name shop_id
小店自增ID*/
    shop_id: string
  }
}
/**@name CreateShop
@summary 创建小店
@alias /api/guanggao_goods_digital_man.DigitalManProc/CreateShop
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateShop = (
  props: ImplRequest<CreateShopRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/CreateShop",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: {
        "goods_info_list|1-2": [
          {
            product_id: "@string",
            product_name: "@string",
            price:
              '价格\n@description：商品价格，如“拍一发三”、“99元三件”等\n@example: "99元三件"',
            selling_points:
              "商品卖点\n@description：商品卖点\n@example: 5G连接、超级摄像头、强大的A15 Bionic芯片",
            user_pain_points:
              "用户痛点\n@description：用户痛点\n@example: 续航时间短、价格偏高",
            detail_info:
              '商品详细介绍\n@description：商品详细介绍\n@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"',
            endorsement:
              '口碑背书\n@description：口碑背书\n@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"',
            promotion:
              '促销活动\n@description：促销活动\n@example: "下单立享9折优惠,赠送无线充电器一个。"',
            after_sale:
              '售后保障\n@description：售后保障\n@example: "享受2年质保,7天无理由退换货。"',
            evaluation:
              '商品测评\n@description：商品测评\n@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"',
            "header_imgs|1-2": ["@string"],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<CreateShopResponse>
/**@name UpdateShopRequest
修改小店请求消息*/
export type UpdateShopRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name shop_id
小店ID*/
  shop_id?: string
  /**@name shop_name
小店名称*/
  shop_name?: string
  /**@name shop_app_id
小店ID*/
  shop_app_id?: string
  /**@name shop_app_secret
小店密钥*/
  shop_app_secret?: string
}
/**@name UpdateShopResponse
修改小店回复消息*/
export type UpdateShopResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name UpdateShop
@summary 修改小店
@alias /api/guanggao_goods_digital_man.DigitalManProc/UpdateShop
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateShop = (
  props: ImplRequest<UpdateShopRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/UpdateShop",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<UpdateShopResponse>
/**@name ListShopsRequest
查询小店请求消息*/
export type ListShopsRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name shop_id
小店ID*/
  shop_id?: string
}
/**@name ListShopsResponse
查询小店回复消息*/
export type ListShopsResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
小店信息列表*/
  data: {
    /**@name shop_id
小店ID*/
    shop_id: string
    /**@name shop_name
小店名称*/
    shop_name: string
    /**@name shop_app_id
小店ID*/
    shop_app_id: string
    /**@name last_sync_time
最新同步时间*/
    last_sync_time: string
  }[]
}
/**@name ListShops
@summary 查询小店
@alias /api/guanggao_goods_digital_man.DigitalManProc/ListShops
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ListShops = (
  props: ImplRequest<ListShopsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/ListShops",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      "data|1-2": [
        {
          shop_id: "小店ID",
          shop_name: "小店名称",
          shop_app_id: "小店ID",
          last_sync_time: "@datetime",
        },
      ],
    },
    props,
  ) as ImplResponse<ListShopsResponse>
/**@name SyncShopProductsRequest
同步小店商品请求消息*/
export type SyncShopProductsRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name shop_id
小店ID*/
  shop_id?: string
}
/**@name SyncShopProductsResponse
同步小店商品回复消息*/
export type SyncShopProductsResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name SyncShopProducts
@summary 同步小店商品
@alias /api/guanggao_goods_digital_man.DigitalManProc/SyncShopProducts
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SyncShopProducts = (
  props: ImplRequest<SyncShopProductsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/SyncShopProducts",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<SyncShopProductsResponse>
/**@name ListProductsRequest
获取商品列表请求消息*/
export type ListProductsRequest = {
  /**@name shop_id
小店ID*/
  shop_id?: string
  /**@name live_room_id
直播间ID*/
  live_room_id?: string
  /**@name keyword
关键词搜索*/
  keyword?: string
  /**@name page
当前页码*/
  page?: number
  /**@name page_size
每页记录数量*/
  page_size?: number
}
/**@name ListProductsResponse
获取商品列表回复消息*/
export type ListProductsResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
商品信息列表*/
  data: {
    /**@name product_list
商品信息列表*/
    product_list: {
      /**@name product_id
商品ID*/
      product_id: string
      /**@name product_type
商品类型*/
      product_type: string
      /**@name first_cate_id
一级行业ID*/
      first_cate_id: string
      /**@name second_cate_id
二级行业ID*/
      second_cate_id: string
      /**@name third_cate_id
三级行业ID*/
      third_cate_id: string
      /**@name header_imgs
商品图片*/
      header_imgs: string[]
      /**@name is_required_fields_filled
是否必填字段都填了*/
      is_required_fields_filled: boolean
      /**@name product_name
商品名称*/
      product_name: string
      /**@name price
商品价格*/
      price: string
      /**@name create_time
商品创建时间*/
      create_time: string
      /**@name common
通用信息*/
      common: {
        /**@name selling_points
商品卖点*/
        selling_points: string
        /**@name user_pain_points
用户痛点*/
        user_pain_points: string
        /**@name detail_info
商品详细介绍*/
        detail_info: string
        /**@name endorsement
口碑背书*/
        endorsement: string
        /**@name promotion
促销信息*/
        promotion: string
        /**@name after_sale
售后保障*/
        after_sale: string
        /**@name evaluation
商品评测*/
        evaluation: string
      }
      /**@name book
图书信息*/
      book: {
        /**@name author
作者*/
        author: string
        /**@name publisher
出版社*/
        publisher: string
        /**@name type
类型*/
        type: string
        /**@name preface
序言*/
        preface: string
        /**@name content
正文*/
        content: string
      }
    }[]
    /**@name page
分页信息*/
    page: {
      /**@name current
当前页码*/
      current: number
      /**@name size
每页记录数量*/
      size: number
      /**@name total
总记录数量*/
      total: number
      /**@name pages
总页面数量*/
      pages: number
    }
  }
}
/**@name ListProducts
@summary 获取商品列表
@alias /api/guanggao_goods_digital_man.DigitalManProc/ListProducts
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ListProducts = (
  props: ImplRequest<ListProductsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/ListProducts",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: {
        "product_list|1-2": [
          {
            product_id: "商品ID",
            product_type: "商品类型",
            first_cate_id: "一级行业ID",
            second_cate_id: "二级行业ID",
            third_cate_id: "三级行业ID",
            "header_imgs|1-2": ["商品图片"],
            is_required_fields_filled: "@boolean",
            product_name: "商品名称",
            price: "商品价格",
            create_time: "@datetime",
            common: {
              selling_points: "商品卖点",
              user_pain_points: "用户痛点",
              detail_info: "商品详细介绍",
              endorsement: "口碑背书",
              promotion: "促销信息",
              after_sale: "售后保障",
              evaluation: "商品评测",
            },
            book: {
              author: "作者",
              publisher: "出版社",
              type: "类型",
              preface: "序言",
              content: "正文",
            },
          },
        ],
        page: {
          current: "@integer(1, 100)",
          size: "@integer(1, 100)",
          total: "@integer(1, 100)",
          pages: "@integer(1, 100)",
        },
      },
    },
    props,
  ) as ImplResponse<ListProductsResponse>
/**@name GetProductDetailRequest
获取商品详情请求消息*/
export type GetProductDetailRequest = {
  /**@name product_id
商品ID*/
  product_id?: string
}
/**@name GetProductDetailResponse
获取商品详情回复消息*/
export type GetProductDetailResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
商品信息*/
  data: {
    /**@name product_id
商品ID*/
    product_id: string
    /**@name product_type
商品类型*/
    product_type: string
    /**@name first_cate_id
一级行业ID*/
    first_cate_id: string
    /**@name second_cate_id
二级行业ID*/
    second_cate_id: string
    /**@name third_cate_id
三级行业ID*/
    third_cate_id: string
    /**@name header_imgs
商品图片*/
    header_imgs: string[]
    /**@name is_required_fields_filled
是否必填字段都填了*/
    is_required_fields_filled: boolean
    /**@name product_name
商品名称*/
    product_name: string
    /**@name price
商品价格*/
    price: string
    /**@name create_time
商品创建时间*/
    create_time: string
    /**@name common
通用信息*/
    common: {
      /**@name selling_points
商品卖点*/
      selling_points: string
      /**@name user_pain_points
用户痛点*/
      user_pain_points: string
      /**@name detail_info
商品详细介绍*/
      detail_info: string
      /**@name endorsement
口碑背书*/
      endorsement: string
      /**@name promotion
促销信息*/
      promotion: string
      /**@name after_sale
售后保障*/
      after_sale: string
      /**@name evaluation
商品评测*/
      evaluation: string
    }
    /**@name book
图书信息*/
    book: {
      /**@name author
作者*/
      author: string
      /**@name publisher
出版社*/
      publisher: string
      /**@name type
类型*/
      type: string
      /**@name preface
序言*/
      preface: string
      /**@name content
正文*/
      content: string
    }
  }
}
/**@name GetProductDetail
@summary 获取商品详情
@alias /api/guanggao_goods_digital_man.DigitalManProc/GetProductDetail
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetProductDetail = (
  props: ImplRequest<GetProductDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GetProductDetail",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: {
        product_id: "商品ID",
        product_type: "商品类型",
        first_cate_id: "一级行业ID",
        second_cate_id: "二级行业ID",
        third_cate_id: "三级行业ID",
        "header_imgs|1-2": ["商品图片"],
        is_required_fields_filled: "@boolean",
        product_name: "商品名称",
        price: "商品价格",
        create_time: "@datetime",
        common: {
          selling_points: "商品卖点",
          user_pain_points: "用户痛点",
          detail_info: "商品详细介绍",
          endorsement: "口碑背书",
          promotion: "促销信息",
          after_sale: "售后保障",
          evaluation: "商品评测",
        },
        book: {
          author: "作者",
          publisher: "出版社",
          type: "类型",
          preface: "序言",
          content: "正文",
        },
      },
    },
    props,
  ) as ImplResponse<GetProductDetailResponse>
/**@name UpdateProductRequest
修改商品详情请求消息*/
export type UpdateProductRequest = {
  /**@name product_id
商品ID*/
  product_id?: string
  /**@name product_name
商品名称*/
  product_name?: string
  /**@name price
商品价格*/
  price?: string
  /**@name common
通用信息*/
  common?: {
    /**@name selling_points
商品卖点*/
    selling_points?: string
    /**@name user_pain_points
用户痛点*/
    user_pain_points?: string
    /**@name detail_info
商品详细介绍*/
    detail_info?: string
    /**@name endorsement
口碑背书*/
    endorsement?: string
    /**@name promotion
促销信息*/
    promotion?: string
    /**@name after_sale
售后保障*/
    after_sale?: string
    /**@name evaluation
商品评测*/
    evaluation?: string
  }
  /**@name book
图书信息*/
  book?: {
    /**@name author
作者*/
    author?: string
    /**@name publisher
出版社*/
    publisher?: string
    /**@name type
类型*/
    type?: string
    /**@name preface
序言*/
    preface?: string
    /**@name content
正文*/
    content?: string
  }
}
/**@name UpdateProductResponse
修改商品详情回复消息*/
export type UpdateProductResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name UpdateProduct
@summary 修改商品详情
@alias /api/guanggao_goods_digital_man.DigitalManProc/UpdateProduct
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateProduct = (
  props: ImplRequest<UpdateProductRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/UpdateProduct",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<UpdateProductResponse>
/**@name ProductFieldsConfigRequest
商品字段配置请求消息*/
export type ProductFieldsConfigRequest = {}
/**@name ProductFieldsConfigResponse
商品字段配置回复消息*/
export type ProductFieldsConfigResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name config_data
字段配置*/
  config_data: string
}
/**@name ProductFieldsConfig
@summary 商品字段配置
@alias /api/guanggao_goods_digital_man.DigitalManProc/ProductFieldsConfig
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ProductFieldsConfig = (
  props: ImplRequest<ProductFieldsConfigRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/ProductFieldsConfig",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      config_data: "字段配置",
    },
    props,
  ) as ImplResponse<ProductFieldsConfigResponse>
/**@name GenerateSeedScriptRequest
生成种子话术请求消息*/
export type GenerateSeedScriptRequest = {
  /**@name live_room_id
直播间ID*/
  live_room_id?: string
  /**@name product_id
商品ID*/
  product_id?: string
}
/**@name GenerateSeedScriptResponse
生成种子话术回复消息*/
export type GenerateSeedScriptResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name GenerateSeedScript
@summary 生成种子话术
@alias /api/guanggao_goods_digital_man.DigitalManProc/GenerateSeedScript
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GenerateSeedScript = (
  props: ImplRequest<GenerateSeedScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GenerateSeedScript",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<GenerateSeedScriptResponse>
/**@name ConfirmSeedScriptRequest
确认种子话术请求消息*/
export type ConfirmSeedScriptRequest = {
  /**@name product_id
商品ID*/
  product_id?: string
  /**@name live_room_id
直播间ID*/
  live_room_id?: string
  /**@name script
完整话术*/
  script?: string
  /**@name script_kv
话术KV*/
  script_kv?: {
    /**@name title
标题*/
    title?: string
    /**@name content
内容*/
    content?: string
  }[]
  /**@name script_source
话术来源*/
  script_source?: string
}
/**@name ConfirmSeedScriptResponse
确认种子话术回复消息*/
export type ConfirmSeedScriptResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name ConfirmSeedScript
@summary 确认种子话术
@alias /api/guanggao_goods_digital_man.DigitalManProc/ConfirmSeedScript
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ConfirmSeedScript = (
  props: ImplRequest<ConfirmSeedScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/ConfirmSeedScript",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<ConfirmSeedScriptResponse>
/**@name GetSeedScriptRequest
查询种子话术请求消息*/
export type GetSeedScriptRequest = {
  /**@name live_room_id
直播间ID*/
  live_room_id?: string
}
/**@name GetSeedScriptResponse
查询种子话术回复消息*/
export type GetSeedScriptResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
话术数据*/
  data: {
    /**@name script
完整话术*/
    script: string
    /**@name script_kv
话术KV
话术生成状态
INITIAL：初始化状态，创建直播间时新插入，历史直播间数据可能存在此状态，新直播间忽略
PRE_SCRIPT_GENERATING：生成中，种子预览话术生成中
PRE_SCRIPT_FINISHED：生成完成，种子预览话术生成完成
PRE_SCRIPT_CONFIRMED：已确认，种子预览话术已确认
PRE_SCRIPT_FAILED：生成失败*/
    script_kv: {
      /**@name title
标题*/
      title: string
      /**@name content
内容*/
      content: string
    }[]
    script_generate_status: string
    /**@name script_source
话术来源*/
    script_source: string
  }
}
/**@name GetSeedScript
@summary 查询种子话术
@alias /api/guanggao_goods_digital_man.DigitalManProc/GetSeedScript
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetSeedScript = (
  props: ImplRequest<GetSeedScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GetSeedScript",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: {
        script: "完整话术",
        "script_kv|1-2": [{ title: "标题", content: "内容" }],
        script_generate_status: "@string",
        script_source: "话术来源",
      },
    },
    props,
  ) as ImplResponse<GetSeedScriptResponse>
/**@name AuditSeedScriptRequest
审核种子话术请求消息*/
export type AuditSeedScriptRequest = {
  /**@name account_id
账户ID，必填*/
  account_id?: string
  /**@name script
话术完整内容，与 script_kv 二选一*/
  script?: string
  /**@name script_kv
KV 结构的话术内容，与 script 二选一*/
  script_kv?: {
    /**@name title
标题*/
    title?: string
    /**@name content
内容*/
    content?: string
  }[]
}
/**@name AuditSeedScriptResponse
确认种子话术回复消息*/
export type AuditSeedScriptResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name AuditSeedScript
@summary 审核种子话术
@alias /api/guanggao_goods_digital_man.DigitalManProc/AuditSeedScript
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AuditSeedScript = (
  props: ImplRequest<AuditSeedScriptRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/AuditSeedScript",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<AuditSeedScriptResponse>
/**@name GenerateMiaowenCodeRequest
生成秒问接入Token请求消息*/
export type GenerateMiaowenCodeRequest = {
  /**@name uri_path
当前反馈页面的uri path*/
  uri_path?: string
}
/**@name GenerateMiaowenCodeResponse
生成秒问接入Token回复消息*/
export type GenerateMiaowenCodeResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
妙问接入Code*/
  data: { code: string }
}
/**@name GenerateMiaowenCode
@summary 生成妙问接入Code
@alias /api/guanggao_goods_digital_man.DigitalManProc/GenerateMiaowenCode
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GenerateMiaowenCode = (
  props: ImplRequest<GenerateMiaowenCodeRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GenerateMiaowenCode",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: {
        "goods_info_list|1-2": [
          {
            product_id: "@string",
            product_name: "@string",
            price:
              '价格\n@description：商品价格，如“拍一发三”、“99元三件”等\n@example: "99元三件"',
            selling_points:
              "商品卖点\n@description：商品卖点\n@example: 5G连接、超级摄像头、强大的A15 Bionic芯片",
            user_pain_points:
              "用户痛点\n@description：用户痛点\n@example: 续航时间短、价格偏高",
            detail_info:
              '商品详细介绍\n@description：商品详细介绍\n@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"',
            endorsement:
              '口碑背书\n@description：口碑背书\n@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"',
            promotion:
              '促销活动\n@description：促销活动\n@example: "下单立享9折优惠,赠送无线充电器一个。"',
            after_sale:
              '售后保障\n@description：售后保障\n@example: "享受2年质保,7天无理由退换货。"',
            evaluation:
              '商品测评\n@description：商品测评\n@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"',
            "header_imgs|1-2": ["@string"],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<GenerateMiaowenCodeResponse>
/**@name GenerateQRCodeRequest
生成公众号二维码请求消息*/
export type GenerateQRCodeRequest = { fan_code?: string }
/**@name GenerateQRCodeResponse
生成公众号二维码回复消息*/
export type GenerateQRCodeResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
公众号二维码信息*/
  data: { qrcode_url: string }
}
/**@name GenerateQRCode
@summary 生成公众号二维码
@alias /api/guanggao_goods_digital_man.DigitalManProc/GenerateQRCode
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GenerateQRCode = (
  props: ImplRequest<GenerateQRCodeRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GenerateQRCode",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: {
        "goods_info_list|1-2": [
          {
            product_id: "@string",
            product_name: "@string",
            price:
              '价格\n@description：商品价格，如“拍一发三”、“99元三件”等\n@example: "99元三件"',
            selling_points:
              "商品卖点\n@description：商品卖点\n@example: 5G连接、超级摄像头、强大的A15 Bionic芯片",
            user_pain_points:
              "用户痛点\n@description：用户痛点\n@example: 续航时间短、价格偏高",
            detail_info:
              '商品详细介绍\n@description：商品详细介绍\n@example: "iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。"',
            endorsement:
              '口碑背书\n@description：口碑背书\n@example: "经多位用户好评,被誉为目前最出色的iPhone手机。"',
            promotion:
              '促销活动\n@description：促销活动\n@example: "下单立享9折优惠,赠送无线充电器一个。"',
            after_sale:
              '售后保障\n@description：售后保障\n@example: "享受2年质保,7天无理由退换货。"',
            evaluation:
              '商品测评\n@description：商品测评\n@example: "专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。"',
            "header_imgs|1-2": ["@string"],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<GenerateQRCodeResponse>
/**@name LiveEventSyncRequest
直播事件同步接口*/
export type LiveEventSyncRequest = {
  /**@name live_room_id
直播间 ID，必填*/
  live_room_id?: string
  /**@name live_session_id
直播场次 ID，选填*/
  live_session_id?: string
  /**@name event_type
直播事件类型，必填*/
  event_type?: string
  /**@name payload
事件内容*/
  payload?: {
    /**@name interaction
互动相关参数，选填*/
    interaction?: {
      /**@name enable_active_danmu
是否开启主动弹幕，必填*/
      enable_active_danmu?: boolean
      /**@name enable_danmu_on_wall
是否开启弹幕上墙，必填*/
      enable_danmu_on_wall?: boolean
    }
  }
}
/**@name LiveEventSyncResponse
直播事件同步接口*/
export type LiveEventSyncResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name LiveEventSync
@summary 直播事件同步接口
@alias /api/guanggao_goods_digital_man.DigitalManProc/LiveEventSync
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const LiveEventSync = (
  props: ImplRequest<LiveEventSyncRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/LiveEventSync",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<LiveEventSyncResponse>
/**@name CreateAccountRequest
创建账户请求消息*/
export type CreateAccountRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name account_type
账户类型*/
  account_type?: string
  /**@name host
主机信息*/
  host?: {
    /**@name appid
应用ID*/
    appid?: string
    /**@name secret
应用密钥*/
    secret?: string
    /**@name uniq_id
视频号唯一 ID*/
    uniq_id?: string
  }
  /**@name shop
店铺信息*/
  shop?: {
    /**@name appid
应用ID*/
    appid?: string
    /**@name secret
应用密钥*/
    secret?: string
  }
}
/**@name CreateAccountResponse
创建账户回复消息*/
export type CreateAccountResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name CreateAccount
@summary 创建账户
@alias /api/guanggao_goods_digital_man.DigitalManProc/CreateAccount
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateAccount = (
  props: ImplRequest<CreateAccountRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/CreateAccount",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<CreateAccountResponse>
/**@name GetAccountInfoRequest
获取账户信息请求消息*/
export type GetAccountInfoRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name account_type
账户类型*/
  account_type?: string
}
/**@name GetAccountInfoResponse
获取账户信息回复消息*/
export type GetAccountInfoResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name account
账户信息*/
  account: {
    /**@name account_id
账户ID*/
    account_id: string
    /**@name account_type
账户类型*/
    account_type: string
    /**@name host
主机信息*/
    host: {
      /**@name appid
应用ID*/
      appid: string
      /**@name secret
应用密钥*/
      secret: string
      /**@name uniq_id
视频号唯一 ID*/
      uniq_id: string
      /**@name nickname
视频号昵称*/
      nickname: string
    }
    /**@name shop
店铺信息*/
    shop: {
      /**@name shop_id
店铺ID*/
      shop_id: string
      /**@name shop_name
店铺名称*/
      shop_name: string
      /**@name head_img_url
店铺头像URL*/
      head_img_url: string
      /**@name appid
店铺应用ID*/
      appid: string
      /**@name secret
店铺应用密钥*/
      secret: string
    }
    /**@name last_sync_time
最新同步时间*/
    last_sync_time: string
  }
}
/**@name GetAccountInfo
@summary 获取账户信息
@alias /api/guanggao_goods_digital_man.DigitalManProc/GetAccountInfo
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetAccountInfo = (
  props: ImplRequest<GetAccountInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GetAccountInfo",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      account: {
        account_id: "账户ID",
        account_type: "账户类型",
        host: { appid: "应用ID", secret: "应用密钥", uniq_id: "视频号唯一 ID" },
        shop: { appid: "应用ID", secret: "应用密钥" },
        last_sync_time: "@datetime",
      },
    },
    props,
  ) as ImplResponse<GetAccountInfoResponse>
/**@name UpdateAccountInfoRequest
更新账户信息请求消息*/
export type UpdateAccountInfoRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name account_type
账户类型*/
  account_type?: string
  /**@name host
达人信息*/
  host?: {
    /**@name appid
应用ID*/
    appid?: string
    /**@name secret
应用密钥*/
    secret?: string
    /**@name uniq_id
视频号唯一 ID*/
    uniq_id?: string
  }
  /**@name shop
店铺信息*/
  shop?: {
    /**@name appid
店铺应用ID*/
    appid?: string
    /**@name secret
店铺应用密钥*/
    secret?: string
  }
}
/**@name UpdateAccountInfoResponse
更新账户信息回复消息*/
export type UpdateAccountInfoResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name UpdateAccountInfo
@summary 更新账户信息
@alias /api/guanggao_goods_digital_man.DigitalManProc/UpdateAccountInfo
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateAccountInfo = (
  props: ImplRequest<UpdateAccountInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/UpdateAccountInfo",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<UpdateAccountInfoResponse>
/**@name SyncProductRequest
同步产品请求消息*/
export type SyncProductRequest = {
  /**@name account_id
账户ID*/
  account_id?: string
  /**@name account_type
账户类型*/
  account_type?: string
}
/**@name SyncProductResponse
同步产品回复消息*/
export type SyncProductResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
}
/**@name SyncProduct
@summary 同步产品
@alias /api/guanggao_goods_digital_man.DigitalManProc/SyncProduct
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SyncProduct = (
  props: ImplRequest<SyncProductRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/SyncProduct",
    { result_code: "返回代码", result_info: "返回信息" },
    props,
  ) as ImplResponse<SyncProductResponse>
/**@name GetUserOpenidRequest
获取用户OpenID请求消息*/
export type GetUserOpenidRequest = {
  /**@name qq
QQ号*/
  qq?: string
  /**@name wx
微信号*/
  wx?: string
}
/**@name GetUserOpenidResponse
获取用户OpenID回复消息*/
export type GetUserOpenidResponse = {
  /**@name result_code
返回代码*/
  result_code: string
  /**@name result_info
返回信息*/
  result_info: string
  /**@name data
数据*/
  data: {
    /**@name qq_openid
QQ OpenID*/
    qq_openid: string
    /**@name wx_openid
微信 OpenID*/
    wx_openid: string
  }
}
/**@name GetUserOpenid
@summary 获取用户OpenID
@alias /api/user.UserProc/GetUserOpenid
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetUserOpenid = (
  props: ImplRequest<GetUserOpenidRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.guanggao_goods_digital_man.DigitalManProc/GetUserOpenid",
    {
      result_code: "返回代码",
      result_info: "返回信息",
      data: { qq_openid: "QQ OpenID", wx_openid: "微信 OpenID" },
    },
    props,
  ) as ImplResponse<GetUserOpenidResponse>
