/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
/**@name ContentListQueryRequest
@description 内容列表查询*/
export type ContentListQueryRequest = {
  condition?: {
    /**@name group
@description 分组*/
    group?: string[]
    /**@name label
@description 标签*/
    label?: { id?: string; name?: string }[]
    /**@name info
@description*/
    info?: string
    /**@name content_id
@description 内容id*/
    content_id?: string[]
    /**@name content_name
@description 内容名称*/
    content_name?: string
    /**@name release_status
@description 发布状态*/
    release_status?: number
    /**@name audit_status
@description 审核状态 904:查询所有完成上线审核内容 90*就是不区分修改的状态*/
    audit_status?: number
    /**@name pf
@description 平台来源*/
    pf?: string
  }
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
}
/**@name ContentListQueryResponse
@description 内容列表查询返回*/
export type ContentListQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    /**@name content_list
@description 结果数据*/
    content_list: {
      /**@name content_id
@description 内容id*/
      content_id: string
      /**@name template_id
@description 模板id*/
      template_id: string
      /**@name content_title
@description 内容标题*/
      content_title: string
      /**@name content_icon
@description 内容缩略图*/
      content_icon: string
      /**@name labels
@description 内容标签列表*/
      labels: { id: string; name: string }[]
      /**@name create_user
@description 创建用户
@example user1*/
      create_user: string
      /**@name modify_user
@description 创建用户
@example user1*/
      modify_user: string
      /**@name create_time
@description 创建时间
@example 1646907507*/
      create_time: number
      /**@name modify_time
@description 更新时间
@example 1646907507*/
      modify_time: number
      /**@name release_time
@description 发布时间
@example 1646907507*/
      release_time: number
      /**@name audit_status
@description 初次审核状态 1:待提交安审 2:安审中 3:上线审核中
4:上线审核已通过 修改后审核状态 101:修改内容待提交安审 102:修改内容安审中
103:修改内容上线审核中 104:修改内容上线审核已通过 审核未通过状态
-1:安审未通过 -2:上线审核未通过 -101:修改内容安审未通过
-102:修改内容上线审核未通过*/
      audit_status: number
      /**@name release_status
@description 页面状态 1:草稿	2:审核中 3:待发布 4:生效中
5:修改待发布，页面生效中 6:已下线*/
      release_status: number
      /**@name url
落地页地址*/
      url: string
      /**@name release_labels
投放标签*/
      release_labels: string[]
      /**@name content_desc
@description 内容描述*/
      content_desc: string
      /**@name reject_reason
@description 人工审核驳回理由。audit_status 是 -2 和 -102 时候需要显示*/
      reject_reason: string
      /**@name share_info
@description 分享信息*/
      share_info: {
        share_title: string
        share_desc: string
        share_poster: string
      }
      /**@name content_os
@description ios  android 系统*/
      content_os: string
      /**@name content_pl
@description 平台 qq，wx*/
      content_pl: string
      is_check_ok: number
      pageId: string
      /**@name ads_info_list
内容页绑定的腾讯广告列表*/
      ads_info_list: {
        account_id: string
        campaigns_id: string
        adgroup_id: string
        ad_id: string
        account_name: string
        campaigns_name: string
        adgroup_name: string
        ad_name: string
      }[]
      terminals: {
        /**@name id
id 枚举值 pc/mobile/miniprogram*/
        id: string
        /**@name name
name PC端/移动端/小程序*/
        name: string
        /**@name authorizer_wxappid
authorizer_wxappid 小程序渠道授权方wxappid*/
        authorizer_wxappid: string
        /**@name authorizer_wxrawid
authorizer_wxrawid 小程序渠道授权方微信原始id*/
        authorizer_wxrawid: string
        /**@name authorizer_build_status
authorizer_build_status 小程序渠道授权方构建状态*/
        authorizer_build_status: number
        /**@name authorizer_build_token
authorizer_build_token 小程序渠道授权方构建token*/
        authorizer_build_token: string
        /**@name authorizer_build_msg
authorizer_build_msg 小程序渠道授权方构建结果信息*/
        authorizer_build_msg: string
        /**@name audit_status
audit_status 审核状态 -102=修改后审核驳回 -2=审核驳回 1=待审核(含撤回审核) 4=审核通过 101=修改待审核 104=修改后审核通过 5=小程序代码审核中 6=小程序代码审核通过 7=小程序审代码核延后 -5=小程序代码审核驳回*/
        audit_status: number
        /**@name wx_auditid
wx_auditid 小程序代码审核id*/
        wx_auditid: string
        /**@name authorizer_pipeline_build_id
authorizer_pipeline_build_id 小程序渠道授权方流水线构建id*/
        authorizer_pipeline_build_id: string
        /**@name wx_audit_errmsg
wx_audit_errmsg 小程序代码审核错误信息*/
        wx_audit_errmsg: string
        /**@name wx_audit_screenshot
wx_audit_screenshot 审核失败的小程序截图示例。用竖线分隔的 media_id 的列表*/
        wx_audit_screenshot: string
        /**@name build_desc
build_desc 构建过程描述*/
        build_desc: string
        /**@name build_progress
build_progress 构建进度，比如: 50%*/
        build_progress: string
      }[]
      /**@name yj_auditid
yj_auditid 页匠审核id*/
      yj_auditid: string
      audit_type: string
    }[]
    /**@name count
数据总量*/
    count: number
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentListQuery
@summary 内容列表查询
@description 根据查询条件筛选满足条件的内容
@alias /api/template_and_component.ContentManageSvr/ContentListQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentListQuery = (
  props: ImplRequest<ContentListQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentListQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          {
            content_id: "内容id",
            template_id: "模板id",
            content_title: "内容标题",
            content_icon: "内容缩略图",
            "labels|1-2": [{ id: "@string", name: "@string" }],
            create_user: "user1",
            modify_user: "user1",
            create_time: 1646907507,
            modify_time: 1646907507,
            release_time: 1646907507,
            audit_status: "@integer(1, 100)",
            release_status: "@integer(1, 100)",
            url: "落地页地址",
            "release_labels|1-2": ["投放标签"],
            content_desc: "内容描述",
            reject_reason:
              "人工审核驳回理由。audit_status 是 -2 和 -102 时候需要显示",
            share_info: {
              share_title: "@string",
              share_desc: "@string",
              share_poster: "@string",
            },
            content_os: "ios  android 系统",
            content_pl: "平台 qq，wx",
            is_check_ok: "@integer(1, 100)",
            pageId: "@string",
            "ads_info_list|1-2": [
              {
                account_id: "@string",
                campaigns_id: "@string",
                adgroup_id: "@string",
                ad_id: "@string",
                account_name: "@string",
                campaigns_name: "@string",
                adgroup_name: "@string",
                ad_name: "@string",
              },
            ],
            "terminals|1-2": [
              {
                id: "id 枚举值 pc/mobile/miniprogram",
                name: "name PC端/移动端/小程序",
                authorizer_wxappid:
                  "authorizer_wxappid 小程序渠道授权方wxappid",
                authorizer_wxrawid:
                  "authorizer_wxrawid 小程序渠道授权方微信原始id",
                authorizer_build_status: "@integer(1, 100)",
                authorizer_build_token:
                  "authorizer_build_token 小程序渠道授权方构建token",
                authorizer_build_msg:
                  "authorizer_build_msg 小程序渠道授权方构建结果信息",
                audit_status: "@integer(1, 100)",
                wx_auditid: "wx_auditid 小程序代码审核id",
                authorizer_pipeline_build_id:
                  "authorizer_pipeline_build_id 小程序渠道授权方流水线构建id",
                wx_audit_errmsg: "wx_audit_errmsg 小程序代码审核错误信息",
                wx_audit_screenshot:
                  "wx_audit_screenshot 审核失败的小程序截图示例。用竖线分隔的 media_id 的列表",
                build_desc: "build_desc 构建过程描述",
                build_progress: "build_progress 构建进度，比如: 50%",
              },
            ],
            yj_auditid: "yj_auditid 页匠审核id",
            audit_type: "@string",
          },
        ],
        count: "@integer(1, 100)",
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentListQueryResponse>
export type ContentDeleteRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
}
export type ContentDeleteResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name content_list
失败列表*/
    content_list: {
      content_id: string
      content_name: string
      terminal_id: string
    }[]
  }
}
/**@name ContentDelete
@summary 内容删除
@description 下线内容
@alias /api/template_and_component.ContentManageSvr/ContentDelete
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentDelete = (
  props: ImplRequest<ContentDeleteRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentDelete",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          {
            content_id: "@string",
            content_name: "@string",
            terminal_id: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<ContentDeleteResponse>
export type ContentReleaseRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
}
export type ContentReleaseResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name content_list
失败列表*/
    content_list: {
      content_id: string
      content_name: string
      terminal_id: string
    }[]
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentRelease
@summary 内容发布到现网
@description 发布内容
@alias /api/template_and_component.ContentManageSvr/ContentRelease
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentRelease = (
  props: ImplRequest<ContentReleaseRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentRelease",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          {
            content_id: "@string",
            content_name: "@string",
            terminal_id: "@string",
          },
        ],
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentReleaseResponse>
export type ContentAuditRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
  audit_type?: string
}
export type ContentAuditResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name content_list
失败列表*/
    content_list: {
      content_id: string
      content_name: string
      terminal_id: string
    }[]
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentAudit
@summary 提交审核
@description 提交审核
@alias /api/template_and_component.ContentManageSvr/ContentAudit
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentAudit = (
  props: ImplRequest<ContentAuditRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentAudit",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          {
            content_id: "@string",
            content_name: "@string",
            terminal_id: "@string",
          },
        ],
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentAuditResponse>
export type ContentCopyRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
}
export type ContentCopyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name ContentCopy
@summary 提交审核
@description 提交审核
@alias /api/template_and_component.ContentManageSvr/ContentCopy
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentCopy = (
  props: ImplRequest<ContentCopyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentCopy",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ContentCopyResponse>
export type ContentOfflineRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
}
export type ContentOfflineResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name content_list
失败列表*/
    content_list: {
      content_id: string
      content_name: string
      terminal_id: string
    }[]
  }
}
/**@name ContentOffline
@summary 内容下线
@description 发布内容
@alias /api/template_and_component.ContentManageSvr/ContentOffline
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentOffline = (
  props: ImplRequest<ContentOfflineRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentOffline",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          {
            content_id: "@string",
            content_name: "@string",
            terminal_id: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<ContentOfflineResponse>
export type ContentUrlQueryRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  /**@name type
@description url类型 preview:预览查看  release:实际内容地址 review:审批预览*/
  type?: string
  review_id?: string
  /**@name page_info_list
@description 2024.02.28 主动添加页面信息,后端保存并返回页面 token,for superfactory*/
  page_info_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name page_order
@description 多页面标识*/
    page_order?: string
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
  }[]
}
/**@name ContentUrlQueryResponse
@description 活动地址查询返回*/
export type ContentUrlQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    url_list: {
      /**@name page_url
@description 页面地址*/
      page_url: string
      /**@name terminal_id
@description 页面终端信息*/
      terminal_id: string
    }[]
    /**@name url_conf
链接有效配置*/
    url_conf: {
      /**@name effective_time
链接有效时间（单位s）  30*/
      effective_time: number
      /**@name effective_times
链接有效次数（） 3*/
      effective_times: number
    }
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentUrlQuery
@summary 内容地址查询
@description 查看内容地址
@alias /api/template_and_component.ContentManageSvr/ContentUrlQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentUrlQuery = (
  props: ImplRequest<ContentUrlQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentUrlQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "url_list|1-2": [{ page_url: "页面地址", terminal_id: "页面终端信息" }],
        url_conf: {
          effective_time: "@integer(1, 100)",
          effective_times: "@integer(1, 100)",
        },
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentUrlQueryResponse>
export type ContentPreviewRequest = {
  /**@name token
@description 内容id*/
  token?: string
}
export type ContentPreviewResponse = {}
/**@name ContentPreview
@summary 内容预览
@description 存储预览内容
@alias /api/template_and_component.ContentManageSvr/ContentPreview
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentPreview = (
  props: ImplRequest<ContentPreviewRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ContentPreview",
    {},
    props,
  ) as ImplResponse<ContentPreviewResponse>
export type MySubmittedReviewsRequest = {
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
  condition?: {
    /**@name review_status
审核进度 Approved(通过) Failed(驳回)  ToBeReviewed(待审核) Reviewed(已审核)
Cancel(撤回)*/
    review_status?: string
    /**@name review_owner
审核操作人 @mine:查询我审核的内容  其它情况为user_id精确匹配*/
    review_owner?: string
    /**@name order
true 为倒序 false 为正序*/
    order?: boolean
    /**@name content_type
审批的内容类型 ,不能设置为 ContentType 类型,因为没有默认值0*/
    content_type?: string
  }
}
export type MySubmittedReviewsResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    count: number
    review_list: {
      /**@name review_id
审核单id*/
      review_id: string
      /**@name content_id
审核单id*/
      content_id: string
      /**@name content_type
内容类型 0:content 1:activity 2:shop
- 0	content	
- 1	activity	
- 2	shop	
- 3	goods	
- 4	origin_page	
- 5	poster	
- 6	goodsv2	
- 11	img	
- 12	text	
- 13	video	
- 14	audio	
- 15	file	
- 16	form*/
      content_type: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 11 | 12 | 13 | 14 | 15 | 16
      /**@name poster_url
缩略图*/
      poster_url: string
      /**@name title
标题*/
      title: string
      /**@name create_user
创建人*/
      create_user: string
      /**@name create_time
审核时间(时间戳)*/
      create_time: number
      /**@name review_status
审核进度 Approved(通过) Failed(驳回)  ToBeReviewed(待审核) Cancel(撤回)*/
      review_status: string
      review_info: string
      preview_url: { url_desc: string; preview_url: string }[]
    }[]
  }
}
/**@name MySubmittedReviews
@summary 我提交的审核
@description 存储预览内容
@alias /api/template_and_component.ContentSvr/SubmittedReviews
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const MySubmittedReviews = (
  props: ImplRequest<MySubmittedReviewsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/MySubmittedReviews",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        count: "@integer(1, 100)",
        "review_list|1-2": [
          {
            review_id: "审核单id",
            content_id: "审核单id",
            content_type: 0,
            poster_url: "缩略图",
            title: "标题",
            create_user: "@name",
            create_time: "@integer(1, 100)",
            review_status:
              "审核进度 Approved(通过) Failed(驳回)  ToBeReviewed(待审核) Cancel(撤回)",
            review_info: "@string",
            "preview_url|1-2": [
              { url_desc: "@string", preview_url: "@string" },
            ],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<MySubmittedReviewsResponse>
export type MyExamineReviewsRequest = {
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
  condition?: {
    /**@name review_status
审核进度 Approved(通过) Failed(驳回)  ToBeReviewed(待审核) Reviewed(已审核)
Cancel(撤回)*/
    review_status?: string
    /**@name review_owner
审核操作人 @mine:查询我审核的内容  其它情况为user_id精确匹配*/
    review_owner?: string
    /**@name order
true 为倒序 false 为正序*/
    order?: boolean
    /**@name content_type
审批的内容类型 ,不能设置为 ContentType 类型,因为没有默认值0*/
    content_type?: string
  }
}
export type MyExamineReviewsResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    count: number
    review_list: {
      /**@name review_id
审核单id*/
      review_id: string
      /**@name content_id
审核单id*/
      content_id: string
      /**@name content_type
内容类型 0:content 1:activity 2:shop
- 0	content	
- 1	activity	
- 2	shop	
- 3	goods	
- 4	origin_page	
- 5	poster	
- 6	goodsv2	
- 11	img	
- 12	text	
- 13	video	
- 14	audio	
- 15	file	
- 16	form*/
      content_type: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 11 | 12 | 13 | 14 | 15 | 16
      /**@name poster_url
缩略图*/
      poster_url: string
      /**@name title
标题*/
      title: string
      /**@name create_user
创建人*/
      create_user: string
      /**@name create_time
审核时间(时间戳)*/
      create_time: number
      /**@name review_status
审核进度 Approved(通过) Failed(驳回)  ToBeReviewed(待审核) Cancel(撤回)*/
      review_status: string
      review_info: string
      preview_url: { url_desc: string; preview_url: string }[]
    }[]
  }
}
/**@name MyExamineReviews
@summary 待我审核的内容
@description 存储预览内容
@alias /api/template_and_component.ContentSvr/MyExamineReviews
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const MyExamineReviews = (
  props: ImplRequest<MyExamineReviewsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/MyExamineReviews",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        count: "@integer(1, 100)",
        "review_list|1-2": [
          {
            review_id: "审核单id",
            content_id: "审核单id",
            content_type: 0,
            poster_url: "缩略图",
            title: "标题",
            create_user: "@name",
            create_time: "@integer(1, 100)",
            review_status:
              "审核进度 Approved(通过) Failed(驳回)  ToBeReviewed(待审核) Cancel(撤回)",
            review_info: "@string",
            "preview_url|1-2": [
              { url_desc: "@string", preview_url: "@string" },
            ],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<MyExamineReviewsResponse>
export type ExamineReviewRequest = {
  review_id?: string[]
  /**@name review_result
审核结果*/
  review_result?: string
  /**@name review_info
审核信息*/
  review_info?: string
  review_type?: string
  content_type?: string
}
export type ExamineReviewResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: { failed_list: string[] }
}
/**@name ExamineReview
@summary 审核操作
@description 审核操作
@alias /api/template_and_component.ContentSvr/ExamineReview
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ExamineReview = (
  props: ImplRequest<ExamineReviewRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/ExamineReview",
    {
      result_code: "0",
      result_info: "ok",
      data: { "failed_list|1-2": ["@string"] },
    },
    props,
  ) as ImplResponse<ExamineReviewResponse>
export type CancelReviewRequest = { review_id?: string; review_type?: string }
export type CancelReviewResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name CancelReview
@summary 撤回审核
@description 撤回审核
@alias /api/template_and_component.ContentSvr/CancelReview
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CancelReview = (
  props: ImplRequest<CancelReviewRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/CancelReview",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<CancelReviewResponse>
export type TaskCreateRequest = {
  content_id?: string
  bindClue?: boolean
  adAccountId?: string
  refCreative?: { use?: boolean; autoHide?: boolean; imageUrl?: string }
}
export type TaskCreateResponse = { result_code: string; result_info: string }
/**@name TaskCreate
@summary 创建投放任务
@description 创建投放任务
@alias /api/template_and_component.ContentManageSvr/TaskCreate
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const TaskCreate = (
  props: ImplRequest<TaskCreateRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/TaskCreate",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<TaskCreateResponse>
export type TaskQueryRequest = { content_id?: string }
export type TaskQueryResponse = {
  result_code: string
  result_info: string
  data: {
    task_info: {
      task_id: string
      /**@name task_status
1:任务执行中  2:任务执行成功 9:没有任务  -1:任务失败 -2:任务失败可重试*/
      task_status: number
      page_id: string
    }
  }
}
/**@name TaskQuery
@summary 创建投放任务
@description 创建投放任务
@alias /api/template_and_component.ContentManageSvr/TaskQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const TaskQuery = (
  props: ImplRequest<TaskQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/TaskQuery",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        task_info: {
          task_id: "@string",
          task_status: "@integer(1, 100)",
          page_id: "@string",
        },
      },
    },
    props,
  ) as ImplResponse<TaskQueryResponse>
export type QueryRelationOfContentAndAppRequest = {
  app_id?: string
  app_type?: string
  content_id?: string[]
}
export type QueryRelationOfContentAndAppResponse = {
  result_code: string
  result_info: string
  data: { relation: { [key: string]: string } }
}
/**@name QueryRelationOfContentAndApp
@summary 查询内容与租户关联关系
@description 查询内容与租户关联关系
@alias
api/template_and_component.ContentManageSvr/QueryRelationOfContentAndApp
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryRelationOfContentAndApp = (
  props: ImplRequest<QueryRelationOfContentAndAppRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/QueryRelationOfContentAndApp",
    {
      result_code: "@string",
      result_info: "@string",
      data: { relation: { Map: "@boolean" } },
    },
    props,
  ) as ImplResponse<QueryRelationOfContentAndAppResponse>
export type SecurityCallBackRequest = {
  content_check_id?: string
  app_type?: string
  app_id?: string
  content_list?: { content_type?: string; content_id?: string }[]
  result_code?: string
  result_msg?: string
  creator_uid?: string
}
export type SecurityCallBackResponse = {
  result_code: string
  result_info: string
}
/**@name SecurityCallBack
@summary 信安审核回调
@description 信安审核回调
@alias /api/template_and_component.ContentManageSvr/SecurityCallBack
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SecurityCallBack = (
  props: ImplRequest<SecurityCallBackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/SecurityCallBack",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SecurityCallBackResponse>
export type CreateAuditRequest = {
  /**@name content_id
内容id，物品的话可以是打包后的groupid*/
  content_id?: string
  content_name?: string
  /**@name content_type
内容类型 0:content 1:activity 2:shop 3:goods 4:originPage
- 0	content	
- 1	activity	
- 2	shop	
- 3	goods	
- 4	origin_page	
- 5	poster	
- 6	goodsv2	
- 11	img	
- 12	text	
- 13	video	
- 14	audio	
- 15	file	
- 16	form*/
  content_type?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 11 | 12 | 13 | 14 | 15 | 16
  /**@name create_user
内容或物品组的创建者，不是提审人*/
  create_user?: string
  create_user_type?: string
  /**@name poster_url
封面缩略图*/
  poster_url?: string
  /**@name preview_url
要审核的链接地址*/
  preview_url?: string[]
}
export type CreateAuditResponse = { result_code: string; result_info: string }
/**@name CreateAudit
@summary 创建审核
@description 创建审核
@alias /api/template_and_component.ContentManageSvr/CreateAudit
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateAudit = (
  props: ImplRequest<CreateAuditRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/CreateAudit",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<CreateAuditResponse>
export type TotalCountRequest = {}
export type TotalCountResponse = {
  result_code: string
  result_info: string
  data: {
    activity_count: string
    shop_count: string
    content_count: string
    good_count: string
    material_count: string
  }
}
/**@name TotalCount
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const TotalCount = (
  props: ImplRequest<TotalCountRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentManageSvr/TotalCount",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        activity_count: "@string",
        shop_count: "@string",
        content_count: "@string",
        good_count: "@string",
        material_count: "@string",
      },
    },
    props,
  ) as ImplResponse<TotalCountResponse>
