/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type NewOriginPageRequest = {
  ad_page_map?: { [key: number]: { xj_page_id_list?: number[] } }
}
export type NewOriginPageResponse = { result_code: string; result_info: string }
/**@name NewOriginPage
@summary 新建一个原生落地页的回调接口
@description 新建一个原生落地页的回调接口
@alias /api/content_manage.OriginPageSvr/NewOriginPage
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const NewOriginPage = (
  props: ImplRequest<NewOriginPageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.OriginPageSvr/NewOriginPage",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<NewOriginPageResponse>
export type OriginPageListRequest = {
  condition?: {
    /**@name canvas_id
查询条件
canvas_id*/
    canvas_id?: string[]
    /**@name origin_page_id
origin_page_id*/
    origin_page_id?: string[]
    /**@name audit_status
audit_status 审核状态
- 0	UNKNOW	未知状态
- 1	NOTINTAUDIT	未发起审核
- 2	SECURITY_INAUDIT	信安审核中
- 3	INAUDIT	审核中
- 4	PASS	审核通过
- 101	EDIT_NOT_IN_AUDIT	修改内容待提交审核
- 102	SECURITY_EDIT_INAUDIT	信安审核中
- 103	EDIT_INAUDIT	
- 104	EDIT_PASS	审核通过
- -1	SECURITY_REJECT	信安审核失败
- -2	REJECT	审核未通过
- -3	EDIT_SECURITY_REJECT	修改内容安审未通过
- -4	EDIT_REJECT	修改内容上线审核未通过*/
    audit_status?: //未知状态
    | 0 //未发起审核
      | 1 //信安审核中
      | 2 //审核中
      | 3 //审核通过
      | 4 //修改内容待提交审核
      | 101 //信安审核中
      | 102
      | 103 //审核通过
      | 104 //信安审核失败
      | -1 //审核未通过
      | -2 //修改内容安审未通过
      | -3 //修改内容上线审核未通过
      | -4
    /**@name advertiser_id
广告主ID*/
    advertiser_id?: string[]
    /**@name page_name
原生页名称,支持模糊查询*/
    page_name?: string
    /**@name need_canvas_info
是否返回canvas_info*/
    need_canvas_info?: boolean
  }
  page_size?: number
  page_num?: number
  /**@name desc
创建时间排序*/
  desc?: boolean
}
export type OriginPageListResponse = {
  result_code: string
  result_info: string
  data: {
    count: number
    page_list: {
      /**@name origin_page_id
原生页id*/
      origin_page_id: string
      origin_page_name: string
      /**@name merchant
商户Id*/
      merchant: string
      /**@name app_id
appId*/
      app_id: string
      /**@name advertiser_id
广告主Id*/
      advertiser_id: string
      /**@name advertiser_name
广告主名称*/
      advertiser_name: string
      /**@name creator
创建人*/
      creator: string
      /**@name audit_status
审核状态
- 0	UNKNOW	未知状态
- 1	NOTINTAUDIT	未发起审核
- 2	SECURITY_INAUDIT	信安审核中
- 3	INAUDIT	审核中
- 4	PASS	审核通过
- 101	EDIT_NOT_IN_AUDIT	修改内容待提交审核
- 102	SECURITY_EDIT_INAUDIT	信安审核中
- 103	EDIT_INAUDIT	
- 104	EDIT_PASS	审核通过
- -1	SECURITY_REJECT	信安审核失败
- -2	REJECT	审核未通过
- -3	EDIT_SECURITY_REJECT	修改内容安审未通过
- -4	EDIT_REJECT	修改内容上线审核未通过*/
      audit_status: //未知状态
      | 0 //未发起审核
        | 1 //信安审核中
        | 2 //审核中
        | 3 //审核通过
        | 4 //修改内容待提交审核
        | 101 //信安审核中
        | 102
        | 103 //审核通过
        | 104 //信安审核失败
        | -1 //审核未通过
        | -2 //修改内容安审未通过
        | -3 //修改内容上线审核未通过
        | -4
      /**@name create_time
新建时间*/
      create_time: string
      /**@name preview_url
预览图地址*/
      preview_url: string
      /**@name h5_addr
用于投放的h5页面的地址*/
      h5_addr: string
      /**@name canvas_id
绑定的canvas_id*/
      canvas_id: string
      /**@name page_elements_json
原生页中的组件列表内容,json存储*/
      page_elements_json: string
      /**@name shared_content_spec_json
分享内容的json数据*/
      shared_content_spec_json: string
      /**@name data_page_views
数据,页面浏览量*/
      data_page_views: number
      /**@name data_conversion_target
数据,转化目标量*/
      data_conversion_target: number
      /**@name audit_failed_msg
审核如果失败的原因*/
      audit_failed_msg: string
      canvas_info: string
      page_id_xj: string
    }[]
  }
}
/**@name OriginPageList
@summary 查询原生落地页信息
@description 查询原生落地页信息
@alias /api/content_manage.OriginPageSvr/OriginPageList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const OriginPageList = (
  props: ImplRequest<OriginPageListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.OriginPageSvr/OriginPageList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        count: "@integer(1, 100)",
        "poster_list|1-2": [
          {
            poster_id: "@string",
            poster_name: "@string",
            poster_url: "海报地址",
            thumbnail: "缩略图地址",
            suffix: "海报格式",
            width: "海报宽度",
            height: "海报长度",
            file_size: "海报文件大小",
            audit_status: 0,
            audit_failed_msg: "审核失败原因",
            publish_status: 0,
            create_time: "@datetime",
            creator: "creator 创建人",
            publish_time: "@datetime",
            publisher: "publisher 最后的发布人",
            "label_list|1-2": [{ id: "@string", name: "@string" }],
            terminal_type: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<OriginPageListResponse>
export type SyncOriginPageRequest = {}
export type SyncOriginPageResponse = {
  result_code: string
  result_info: string
}
/**@name SyncOriginPage
@summary 若是回调失败的话,使用这个接口来拉取同步
@description 若是回调失败的话,使用这个接口来拉取同步
@alias /api/content_manage.OriginPageSvr/SyncOriginPage
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SyncOriginPage = (
  props: ImplRequest<SyncOriginPageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.OriginPageSvr/SyncOriginPage",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SyncOriginPageResponse>
export type SecurityCallBackRequest = {
  content_check_id?: string
  app_type?: string
  app_id?: string
  content_list?: { content_type?: string; content_id?: string }[]
  result_code?: string
  result_msg?: string
  creator_uid?: string
}
export type SecurityCallBackResponse = {
  result_code: string
  result_info: string
}
/**@name SecurityCallBack
@summary 原生页信安审核回调
@description 原生页信安审核回调
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SecurityCallBack = (
  props: ImplRequest<SecurityCallBackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.OriginPageSvr/SecurityCallBack",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SecurityCallBackResponse>
