/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type AddEventRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name terminal_type
终端类型
@description：
@example:
@enum:
pc:pc客户端
server:后台服务器*/
  terminal_type?: string
  /**@name live_events
直播事件对象
@description：
@example:*/
  live_events?: {
    /**@name event_id
子事件id，这个id每个事件唯一
@description：
@example:*/
    event_id?: string
    /**@name event_type
事件类型
@description：
@example:
@enum:
restart:重启事件*/
    event_type?: string
    /**@name zhiyan_start_time
开始时间
@description：
@example:*/
    zhiyan_start_time?: string
    /**@name zhiyan_end_time
结束时间
@description：
@example:*/
    zhiyan_end_time?: string
  }
}
export type AddEventResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name AddEvent
@summary AddEvent
@description  如果增加的是服务端的事件，会直接推送到对应的服务程序；推送使用PushEvent，协议和AddEvent相同
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddEvent = (
  props: ImplRequest<AddEventRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/AddEvent",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<AddEventResponse>
export type EventConfirmRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name event_id
子事件id，这个id每个事件唯一
@description：
@example:*/
  event_id?: string
}
export type EventConfirmResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name EventConfirm
@summary EventConfirm
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const EventConfirm = (
  props: ImplRequest<EventConfirmRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/EventConfirm",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<EventConfirmResponse>
export type GetEventListRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name terminal_type
终端类型
@description：
@example:
@enum:
pc:pc客户端
server:后台服务器*/
  terminal_type?: string
}
export type GetEventListResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name call_interval_second
调用时间间隔
@description：
@example:*/
  call_interval_second: number
  /**@name live_events_list
直播事件列表
@description：
@example:*/
  live_events_list: {
    /**@name event_id
子事件id，这个id每个事件唯一
@description：
@example:*/
    event_id: string
    /**@name event_type
事件类型
@description：
@example:
@enum:
restart:重启事件*/
    event_type: string
    /**@name zhiyan_start_time
开始时间
@description：
@example:*/
    zhiyan_start_time: string
    /**@name zhiyan_end_time
结束时间
@description：
@example:*/
    zhiyan_end_time: string
  }[]
}
/**@name GetEventList
@summary GetEventList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetEventList = (
  props: ImplRequest<GetEventListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/GetEventList",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      call_interval_second: "@integer(1, 100)",
      "live_events_list|1-2": [
        {
          event_id: "子事件id，这个id每个事件唯一\n@description：\n@example:",
          event_type:
            "事件类型\n@description：\n@example:\n@enum:\nrestart:重启事件",
          zhiyan_start_time: "开始时间\n@description：\n@example:",
          zhiyan_end_time: "结束时间\n@description：\n@example:",
        },
      ],
    },
    props,
  ) as ImplResponse<GetEventListResponse>
export type HeartbeatReporterRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name terminal_type
终端类型
@description：
@example:
@enum:
pc:pc客户端
server:后台服务器*/
  terminal_type?: string
  /**@name report
心跳上报map
@description：
@example:*/
  report?: {
    /**@name digital_human_stream_frames_per_second
视频帧率
@description：
@example: 25*/
    digital_human_stream_frames_per_second?: number
  }
}
export type HeartbeatReporterResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name call_interval_second
调用时间间隔
@description：
@example:*/
  call_interval_second: number
}
/**@name HeartbeatReporter
@summary HeartbeatReporter
@description  live_session_id：用来唯一的标识一场会话；
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const HeartbeatReporter = (
  props: ImplRequest<HeartbeatReporterRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/HeartbeatReporter",
    {
      code: "code\n@description：\n@example:",
      message: "错误描述信息\n@description：\n@example:",
      call_interval_second: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<HeartbeatReporterResponse>
