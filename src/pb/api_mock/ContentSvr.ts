/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type ContentCreateRequest = {
  /**@name content_title
@description 内容标题*/
  content_title?: string
  /**@name content_type
@description 内容类型(activity:活动  content:内容页  shop:商城)*/
  content_type?: string
  /**@name activity_id
@description 活动id，非活动场景为空*/
  activity_id?: string
  /**@name poster_url
@description 内容封面*/
  poster_url?: string
  /**@name desc
@description 内容描述*/
  desc?: string
  /**@name template_id
@description 模板id*/
  template_id?: string
  /**@name template_type
@description 模板类型*/
  template_type?: string
  /**@name page_list
@description 页面列表*/
  page_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name html
@description html页内容*/
    html?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name show
@description 多页面标识*/
    show?: number
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
    extend?: { [key: string]: string }
    page_group_id?: number
    page_group_name?: string
    page_group_mode?: string
    page_group_key?: string[]
  }[]
  component_list?: { component_id?: string; component_type?: string }[]
  extend?: { [key: string]: string }
  create_type?: string
  loop_info?: {
    base_time?: { begin_time?: number; end_time?: number }
    loop_list?: NonNullable<
      NonNullable<ContentCreateRequest["loop_info"]>["base_time"]
    >[]
  }
  edit_time?: string
  product_info?: string
  price?: string
  discount?: string
  gift_type?: string
  create_from?: string
  video_set?: { video_definition?: string }
  draft_id?: string
  security_list?: {
    security_id?: string
    /**@name security_type
img,video,audio,text,file,link*/
    security_type?: string
    security_value?: string
    security_name?: string
  }[]
  content_toc_extend?: { [key: string]: string }
}
export type ContentCreateResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    /**@name content_id
@description 内容id*/
    content_id: string
    missing_product_list: { product_id: string; product_name: string }[]
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentCreate
@summary 内容创建
@description 根据模板创建内容内容
@alias /api/page_content.ContentSvr/ContentCreate
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentCreate = (
  props: ImplRequest<ContentCreateRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentCreate",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        content_id: "内容id",
        "missing_product_list|1-2": [
          { product_id: "@string", product_name: "@string" },
        ],
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentCreateResponse>
/**@name ContentDetailQueryRequest
@description 内容详情查询请求*/
export type ContentDetailQueryRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  version?: string
  app_id?: string
  page_group_id?: number
}
/**@name ContentDetailQueryResponse
@description 内容详情查询返回*/
export type ContentDetailQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    /**@name template_id
@description 模板id*/
    template_id: string
    /**@name content_id
@description 模板名*/
    content_id: string
    /**@name content_type
@description 内容类型*/
    content_type: string
    /**@name content_name
@description 模板名*/
    content_name: string
    /**@name content_status
@description 模板名*/
    content_status: string
    /**@name desc
@description 模板描述*/
    desc: string
    /**@name icon_url
@description 图标文件地址*/
    icon_url: string
    /**@name pre_view_url
@description 预览图地址*/
    pre_view_url: string
    /**@name app_id
@description 创建业务*/
    app_id: string
    /**@name create_user
@description 创建用户*/
    create_user: string
    /**@name modify_user
@description 最后修改用户*/
    modify_user: string
    /**@name create_time
@description 创建时间
@example 1646907507*/
    create_time: string
    /**@name modify_time
@description 更新时间
@example 1646907507*/
    modify_time: string
    /**@name page_list
@description 内容列表*/
    page_list: {
      /**@name page_id
@description 页面id(创建时不传)*/
      page_id: string
      /**@name page_name
@description 页面名*/
      page_name: string
      /**@name data
@description 页面协议*/
      data: string
      /**@name html
@description html页内容*/
      html: string
      /**@name status
@description 页面状态
@example 1:有效 0:无效*/
      status: number
      /**@name terminal_id
@description 终端信息*/
      terminal_id: string
      /**@name show
@description 多页面标识*/
      show: number
      /**@name poster_url
@description 页面封面*/
      poster_url: string
      extend: { [key: string]: string }
      page_group_id: number
      page_group_name: string
      page_group_mode: string
      page_group_key: string[]
    }[]
    /**@name component_list
@description 模板组件列表*/
    component_list: {
      /**@name component_id
@description 组件id
@example cmpt123*/
      component_id: string
      /**@name component_in_page_id
@description 在页面协议中的id
@example lib/button*/
      component_in_page_id: string
      /**@name component_name
@description 组件id
@example cmpt123*/
      component_name: string
      /**@name is_necessary
@description 是否必要组件
@example 1*/
      is_necessary: number
      /**@name is_replaceable
@description 是否可替换
@example 1*/
      is_replaceable: number
      /**@name status
@description 状态
@example 1*/
      status: number
      /**@name icon_url
@description 组件icon地址*/
      icon_url: string
      /**@name terminal_id
@description 终端id*/
      terminal_id: string
    }[]
    /**@name component_lib_list
@description 组件库列表*/
    component_lib_list: {
      /**@name meta
@description 素材库地址
@example*/
      meta: string
      /**@name lib_name
@description 素材库名
@example*/
      lib_name: string
      /**@name alias
@description 素材库别名
@example*/
      alias: string
    }[]
    /**@name origin_page
@description 模板内容列表*/
    origin_page: {
      /**@name page_id
@description 页面id（创建时不传）
@example page123*/
      page_id: string
      /**@name page_name
@description 页面名
@example 抽奖页面1*/
      page_name: string
      /**@name data
@description 模板页面协议
@example text*/
      data: string
      /**@name status
@description 模板页面状态
@example 1:有效 0:删除*/
      status: number
      /**@name create_time
@description 创建时间
@example 1646907507*/
      create_time: number
      /**@name modify_time
@description 更新时间
@example 1646907507*/
      modify_time: number
      /**@name terminal_id
@description 终端id*/
      terminal_id: string
      /**@name show
@description 多页面标识*/
      show: number
      /**@name poster_url
@description 页面封面*/
      poster_url: string
    }[]
    content_component_list: { component_id: string; component_type: string }[]
    extend: { [key: string]: string }
    /**@name version
@description 分享信息

ShareInfo share_info = 20;

@description ios  android 系统

string content_os = 21;

@description 平台 qq，wx

string content_pl = 22;

@description 使用的素材列表

repeated string material_list = 23;
int32 is_check_ok = 24;*/
    version: number
    is_lock: string
    loop_info: {
      base_time: { begin_time: number; end_time: number }
      loop_list: ContentDetailQueryResponse["data"]["loop_info"]["base_time"][]
    }
    is_released: boolean
    page_group_id: number
    page_group_name: string
    product_info: string
    price: string
    is_too_cheap: string
    gift_type: string
    video_set: { video_definition: string }
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentDetailQuery
@summary 内容详情查询
@description 查询内容详细信息
@alias /api/page_content.ContentSvr/ContentDetailQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentDetailQuery = (
  props: ImplRequest<ContentDetailQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentDetailQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        template_id: "模板id",
        content_id: "模板名",
        content_type: "内容类型",
        content_name: "模板名",
        content_status: "模板名",
        desc: "模板描述",
        icon_url: "图标文件地址",
        pre_view_url: "预览图地址",
        app_id: "创建业务",
        create_user: "@name",
        modify_user: "@name",
        create_time: "1646907507",
        modify_time: "1646907507",
        "page_list|1-2": [
          {
            page_id: "页面id(创建时不传)",
            page_name: "页面名",
            data: "页面协议",
            html: "html页内容",
            status: "1:有效 0:无效",
            terminal_id: "终端信息",
            show: "@integer(1, 100)",
            poster_url: "页面封面",
            extend: { Map: "@string" },
            page_group_id: "@integer(1, 100)",
            page_group_name: "@string",
            page_group_mode: "@string",
            "page_group_key|1-2": ["@string"],
          },
        ],
        "component_list|1-2": [
          {
            component_id: "cmpt123",
            component_in_page_id: "lib/button",
            component_name: "cmpt123",
            is_necessary: 1,
            is_replaceable: 1,
            status: 1,
            icon_url: "组件icon地址",
            terminal_id: "终端id",
          },
        ],
        "component_lib_list|1-2": [
          { meta: "素材库地址", lib_name: "素材库名", alias: "素材库别名" },
        ],
        "origin_page|1-2": [
          {
            page_id: "page123",
            page_name: "抽奖页面1",
            data: "text",
            status: "1:有效 0:删除",
            create_time: 1646907507,
            modify_time: 1646907507,
            terminal_id: "终端id",
            show: "@integer(1, 100)",
            poster_url: "页面封面",
          },
        ],
        "content_component_list|1-2": [
          { component_id: "@string", component_type: "@string" },
        ],
        extend: { Map: "@string" },
        version: "@integer(1, 100)",
        is_lock: "@string",
        loop_info: {
          base_time: {
            begin_time: "@integer(1, 100)",
            end_time: "@integer(1, 100)",
          },
          "loop_list|1-2": [void 0],
        },
        is_released: "@boolean",
        page_group_id: "@integer(1, 100)",
        page_group_name: "@string",
        product_info: "@string",
        price: "@string",
        is_too_cheap: "@string",
        gift_type: "@string",
        video_set: { video_definition: "@string" },
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentDetailQueryResponse>
/**@name ContentGroupDetailQueryRequest
@description 内容详情查询请求*/
export type ContentGroupDetailQueryRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  version?: string
  app_id?: string
  page_group_id?: number
}
/**@name ContentGroupDetailQueryResponse
@description 内容详情查询返回*/
export type ContentGroupDetailQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    /**@name template_id
@description 模板id*/
    template_id: string
    /**@name content_id
@description 模板名*/
    content_id: string
    /**@name content_type
@description 内容类型*/
    content_type: string
    /**@name content_name
@description 模板名*/
    content_name: string
    /**@name content_status
@description 模板名*/
    content_status: string
    /**@name desc
@description 模板描述*/
    desc: string
    /**@name icon_url
@description 图标文件地址*/
    icon_url: string
    /**@name pre_view_url
@description 预览图地址*/
    pre_view_url: string
    /**@name app_id
@description 创建业务*/
    app_id: string
    /**@name create_user
@description 创建用户*/
    create_user: string
    /**@name modify_user
@description 最后修改用户*/
    modify_user: string
    /**@name create_time
@description 创建时间
@example 1646907507*/
    create_time: string
    /**@name modify_time
@description 更新时间
@example 1646907507*/
    modify_time: string
    /**@name page_list
@description 内容列表*/
    page_list: {
      /**@name page_id
@description 页面id(创建时不传)*/
      page_id: string
      /**@name page_name
@description 页面名*/
      page_name: string
      /**@name data
@description 页面协议*/
      data: string
      /**@name html
@description html页内容*/
      html: string
      /**@name status
@description 页面状态
@example 1:有效 0:无效*/
      status: number
      /**@name terminal_id
@description 终端信息*/
      terminal_id: string
      /**@name show
@description 多页面标识*/
      show: number
      /**@name poster_url
@description 页面封面*/
      poster_url: string
      extend: { [key: string]: string }
      page_group_id: number
      page_group_name: string
      page_group_mode: string
      page_group_key: string[]
    }[]
    /**@name component_list
@description 模板组件列表*/
    component_list: {
      /**@name component_id
@description 组件id
@example cmpt123*/
      component_id: string
      /**@name component_in_page_id
@description 在页面协议中的id
@example lib/button*/
      component_in_page_id: string
      /**@name component_name
@description 组件id
@example cmpt123*/
      component_name: string
      /**@name is_necessary
@description 是否必要组件
@example 1*/
      is_necessary: number
      /**@name is_replaceable
@description 是否可替换
@example 1*/
      is_replaceable: number
      /**@name status
@description 状态
@example 1*/
      status: number
      /**@name icon_url
@description 组件icon地址*/
      icon_url: string
      /**@name terminal_id
@description 终端id*/
      terminal_id: string
    }[]
    /**@name component_lib_list
@description 组件库列表*/
    component_lib_list: {
      /**@name meta
@description 素材库地址
@example*/
      meta: string
      /**@name lib_name
@description 素材库名
@example*/
      lib_name: string
      /**@name alias
@description 素材库别名
@example*/
      alias: string
    }[]
    /**@name origin_page
@description 模板内容列表*/
    origin_page: {
      /**@name page_id
@description 页面id（创建时不传）
@example page123*/
      page_id: string
      /**@name page_name
@description 页面名
@example 抽奖页面1*/
      page_name: string
      /**@name data
@description 模板页面协议
@example text*/
      data: string
      /**@name status
@description 模板页面状态
@example 1:有效 0:删除*/
      status: number
      /**@name create_time
@description 创建时间
@example 1646907507*/
      create_time: number
      /**@name modify_time
@description 更新时间
@example 1646907507*/
      modify_time: number
      /**@name terminal_id
@description 终端id*/
      terminal_id: string
      /**@name show
@description 多页面标识*/
      show: number
      /**@name poster_url
@description 页面封面*/
      poster_url: string
    }[]
    content_component_list: { component_id: string; component_type: string }[]
    extend: { [key: string]: string }
    /**@name version
@description 分享信息

ShareInfo share_info = 20;

@description ios  android 系统

string content_os = 21;

@description 平台 qq，wx

string content_pl = 22;

@description 使用的素材列表

repeated string material_list = 23;
int32 is_check_ok = 24;*/
    version: number
    is_lock: string
    loop_info: {
      base_time: { begin_time: number; end_time: number }
      loop_list: ContentGroupDetailQueryResponse["data"]["loop_info"]["base_time"][]
    }
    is_released: boolean
    page_group_id: number
    page_group_name: string
    product_info: string
    price: string
    is_too_cheap: string
    gift_type: string
    video_set: { video_definition: string }
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentGroupDetailQuery
@summary 内容详情查询
@description 查询内容详细信息
@alias /api/page_content.ContentSvr/ContentGroupDetailQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentGroupDetailQuery = (
  props: ImplRequest<ContentGroupDetailQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentGroupDetailQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        template_id: "模板id",
        content_id: "模板名",
        content_type: "内容类型",
        content_name: "模板名",
        content_status: "模板名",
        desc: "模板描述",
        icon_url: "图标文件地址",
        pre_view_url: "预览图地址",
        app_id: "创建业务",
        create_user: "@name",
        modify_user: "@name",
        create_time: "1646907507",
        modify_time: "1646907507",
        "page_list|1-2": [
          {
            page_id: "页面id(创建时不传)",
            page_name: "页面名",
            data: "页面协议",
            html: "html页内容",
            status: "1:有效 0:无效",
            terminal_id: "终端信息",
            show: "@integer(1, 100)",
            poster_url: "页面封面",
            extend: { Map: "@string" },
            page_group_id: "@integer(1, 100)",
            page_group_name: "@string",
            page_group_mode: "@string",
            "page_group_key|1-2": ["@string"],
          },
        ],
        "component_list|1-2": [
          {
            component_id: "cmpt123",
            component_in_page_id: "lib/button",
            component_name: "cmpt123",
            is_necessary: 1,
            is_replaceable: 1,
            status: 1,
            icon_url: "组件icon地址",
            terminal_id: "终端id",
          },
        ],
        "component_lib_list|1-2": [
          { meta: "素材库地址", lib_name: "素材库名", alias: "素材库别名" },
        ],
        "origin_page|1-2": [
          {
            page_id: "page123",
            page_name: "抽奖页面1",
            data: "text",
            status: "1:有效 0:删除",
            create_time: 1646907507,
            modify_time: 1646907507,
            terminal_id: "终端id",
            show: "@integer(1, 100)",
            poster_url: "页面封面",
          },
        ],
        "content_component_list|1-2": [
          { component_id: "@string", component_type: "@string" },
        ],
        extend: { Map: "@string" },
        version: "@integer(1, 100)",
        is_lock: "@string",
        loop_info: {
          base_time: {
            begin_time: "@integer(1, 100)",
            end_time: "@integer(1, 100)",
          },
          "loop_list|1-2": [void 0],
        },
        is_released: "@boolean",
        page_group_id: "@integer(1, 100)",
        page_group_name: "@string",
        product_info: "@string",
        price: "@string",
        is_too_cheap: "@string",
        gift_type: "@string",
        video_set: { video_definition: "@string" },
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentGroupDetailQueryResponse>
/**@name ContentListQueryRequest
@description 内容列表查询*/
export type ContentListQueryRequest = {
  condition?: {
    /**@name content_id
@description 内容id*/
    content_id?: string[]
    /**@name content_name
@description 内容名称*/
    content_name?: string
    /**@name expandKeys
需要查询的扩展key*/
    expandKeys?: string[]
  }
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
}
/**@name ContentListQueryResponse
@description 内容列表查询返回*/
export type ContentListQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    /**@name content_list
@description 结果数据*/
    content_list: {
      /**@name content_id
@description 内容id*/
      content_id: string
      /**@name template_id
@description 模板id*/
      template_id: string
      /**@name content_title
@description 内容标题*/
      content_title: string
      /**@name content_icon
@description 内容缩略图*/
      content_icon: string
      /**@name create_user
@description 创建用户
@example user1*/
      create_user: string
      /**@name modify_user
@description 创建用户
@example user1*/
      modify_user: string
      /**@name create_time
@description 创建时间
@example 1646907507*/
      create_time: string
      /**@name modify_time
@description 更新时间
@example 1646907507*/
      modify_time: string
      /**@name release_time
@description 发布时间
@example 1646907507*/
      release_time: string
      /**@name content_desc
@description 内容描述*/
      content_desc: string
      /**@name extend
@description 扩展字段*/
      extend: { [key: string]: string }
    }[]
    /**@name count
数据总量*/
    count: number
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentListQuery
@summary 内容列表查询
@description 根据查询条件筛选满足条件的内容
@alias /api/page_content.ContentSvr/ContentListQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentListQuery = (
  props: ImplRequest<ContentListQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentListQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          {
            content_id: "内容id",
            template_id: "模板id",
            content_title: "内容标题",
            content_icon: "内容缩略图",
            create_user: "user1",
            modify_user: "user1",
            create_time: "1646907507",
            modify_time: "1646907507",
            release_time: "1646907507",
            content_desc: "内容描述",
            extend: { Map: "扩展字段" },
          },
        ],
        count: "@integer(1, 100)",
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentListQueryResponse>
export type ContentModifyRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  /**@name content_title
@description 内容标题*/
  content_title?: string
  /**@name template_id
@description 模板id*/
  template_id?: string
  /**@name content_desc
@description 内容描述*/
  content_desc?: string
  /**@name icon_url
@description 图标*/
  icon_url?: string
  /**@name preview_img_url
@description 预览图*/
  preview_img_url?: string
  /**@name poster_url
@description 封面*/
  poster_url?: string
  /**@name page_list
@description 页面列表*/
  page_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name html
@description html页内容*/
    html?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name show
@description 多页面标识*/
    show?: number
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
    extend?: { [key: string]: string }
    page_group_id?: number
    page_group_name?: string
    page_group_mode?: string
    page_group_key?: string[]
  }[]
  component_list?: { component_id?: string; component_type?: string }[]
  extend?: { [key: string]: string }
  /**@name create_type
@description 分享信息

ShareInfo share_info = 11;

@description ios  android 系统

string content_os = 12;

@description 平台 qq，wx

string content_pl = 13;

@description 使用的素材列表

repeated string material_list = 14;
int32 is_check_ok = 15;*/
  create_type?: string
  loop_info?: {
    base_time?: { begin_time?: number; end_time?: number }
    loop_list?: NonNullable<
      NonNullable<ContentModifyRequest["loop_info"]>["base_time"]
    >[]
  }
  edit_time?: string
  product_info?: string
  price?: string
  discount?: string
  gift_type?: string
  create_from?: string
  video_set?: { video_definition?: string }
  security_list?: {
    security_id?: string
    /**@name security_type
img,video,audio,text,file,link*/
    security_type?: string
    security_value?: string
    security_name?: string
  }[]
  content_toc_extend?: { [key: string]: string }
}
/**@name ContentModifyResponse
@description 内容列表查询返回*/
export type ContentModifyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
  data: { missing_product_list: { product_id: string; product_name: string }[] }
}
/**@name ContentModify
@summary 内容修改
@description 修改内容的内容
@alias /api/page_content.ContentSvr/ContentModify
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentModify = (
  props: ImplRequest<ContentModifyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentModify",
    {
      result_code: "0",
      result_info: "ok",
      display_info: "display_info 错误显示信息",
      data: {
        "missing_product_list|1-2": [
          { product_id: "@string", product_name: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<ContentModifyResponse>
export type ContentDeleteRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
}
export type ContentDeleteResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name content_list
失败列表*/
    content_list: { content_id: string; content_name: string }[]
  }
}
/**@name ContentDelete
@summary 内容删除
@description 下线内容
@alias /api/page_content.ContentSvr/ContentDelete
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentDelete = (
  props: ImplRequest<ContentDeleteRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentDelete",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "content_list|1-2": [
          { content_id: "@string", content_name: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<ContentDeleteResponse>
export type ContentExtendReleaseRequest = {
  content_id?: string
  content_type?: string
}
export type ContentExtendReleaseResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name ContentExtendRelease
@summary 内容扩展信息发布
@description 内容扩展信息发布
@alias /api/page_content.ContentSvr/ContentExtendRelease
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentExtendRelease = (
  props: ImplRequest<ContentExtendReleaseRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentExtendRelease",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ContentExtendReleaseResponse>
export type ContentLockRequest = { content_id?: string; lock_status?: string }
export type ContentLockResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name ContentLock
@summary 内容锁定
@description 锁定的内容无法被修改、删除
@alias /api/page_content.ContentSvr/ContentLock
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentLock = (
  props: ImplRequest<ContentLockRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentLock",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ContentLockResponse>
export type ContentCopyRequest = {
  content_list?: {
    /**@name content_id
@description 内容id*/
    content_id?: string
    /**@name terminal_id
@description 终端id*/
    terminal_id?: string
  }[]
  activity_id?: string
  target_app_id?: string
  is_ignore_rule?: string
}
export type ContentCopyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: { content_id: string }
}
/**@name ContentCopy
@summary 内容复制
@description 内容复制
@alias /api/page_content.ContentSvr/ContentCopy
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentCopy = (
  props: ImplRequest<ContentCopyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentCopy",
    { result_code: "0", result_info: "ok", data: { content_id: "@string" } },
    props,
  ) as ImplResponse<ContentCopyResponse>
export type ContentReleaseBackupRequest = {
  content_id?: string
  back_type?: string
}
export type ContentReleaseBackupResponse = {
  result_code: string
  result_info: string
}
/**@name ContentReleaseBackup
@summary 内容回滚
@description 内容回滚
@alias /api/page_content.ContentSvr/ContentReleaseBackup
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentReleaseBackup = (
  props: ImplRequest<ContentReleaseBackupRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentReleaseBackup",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ContentReleaseBackupResponse>
export type ContentRollbackRequest = { content_id?: string; version?: number }
export type ContentRollbackResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name ContentRollback
@summary 内容回滚
@description 内容回滚
@alias /api/page_content.ContentSvr/ContentRollback
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentRollback = (
  props: ImplRequest<ContentRollbackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentRollback",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ContentRollbackResponse>
export type ContentPageGroupQueryRequest = {
  content_id?: string
  version?: string
  app_id?: string
}
export type ContentPageGroupQueryResponse = {
  result_code: string
  result_info: string
  data: {
    list: {
      page_group_id: number
      page_group_name: string
      page_group_url: string
      page_group_mode: string
      page_group_key: string
      page_group_sandbox_url: string
    }[]
    page_group_map: {
      [key: string]: ContentPageGroupQueryResponse["data"]["list"][0]
    }
  }
}
/**@name ContentPageGroupQuery
@summary 内容也分组查询
@description 内容也分组查询
@alias /api/page_content.ContentSvr/ContentPageGroupQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentPageGroupQuery = (
  props: ImplRequest<ContentPageGroupQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentPageGroupQuery",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "list|1-2": [
          {
            page_group_id: "@integer(1, 100)",
            page_group_name: "@string",
            page_group_url: "@string",
            page_group_mode: "@string",
            page_group_key: "@string",
            page_group_sandbox_url: "@string",
          },
        ],
        page_group_map: { Map: void 0 },
      },
    },
    props,
  ) as ImplResponse<ContentPageGroupQueryResponse>
export type ContentMoveCrossEnvRequest = {
  /**@name content_title
@description 内容标题*/
  content_title?: string
  /**@name content_type
@description 内容类型(activity:活动  content:内容页  shop:商城)*/
  content_type?: string
  /**@name activity_id
@description 活动id，非活动场景为空*/
  activity_id?: string
  /**@name poster_url
@description 内容封面*/
  poster_url?: string
  /**@name desc
@description 内容描述*/
  desc?: string
  /**@name template_id
@description 模板id*/
  template_id?: string
  /**@name template_type
@description 模板类型*/
  template_type?: string
  /**@name page_list
@description 页面列表*/
  page_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name html
@description html页内容*/
    html?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name show
@description 多页面标识*/
    show?: number
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
    extend?: { [key: string]: string }
    page_group_id?: number
    page_group_name?: string
    page_group_mode?: string
    page_group_key?: string[]
  }[]
  component_list?: { component_id?: string; component_type?: string }[]
  extend?: { [key: string]: string }
  create_type?: string
  loop_info?: {
    base_time?: { begin_time?: number; end_time?: number }
    loop_list?: NonNullable<
      NonNullable<ContentMoveCrossEnvRequest["loop_info"]>["base_time"]
    >[]
  }
  edit_time?: number
  app_id?: string
  user_id?: string
  user_name?: string
}
export type ContentMoveCrossEnvResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name ContentMoveCrossEnv
@summary 内容创建
@description 根据模板创建内容内容
@alias /api/page_content.ContentSvr/ContentMoveCrossEnv
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentMoveCrossEnv = (
  props: ImplRequest<ContentMoveCrossEnvRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentMoveCrossEnv",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ContentMoveCrossEnvResponse>
export type ContentIdListQueryRequest = {
  condition?: { [key: string]: { value?: string[] } }
}
export type ContentIdListQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    list: {
      /**@name content_id
@description 内容id*/
      content_id: string
      /**@name template_id
@description 模板id*/
      template_id: string
      /**@name content_title
@description 内容标题*/
      content_title: string
      /**@name content_icon
@description 内容缩略图*/
      content_icon: string
      /**@name create_user
@description 创建用户
@example user1*/
      create_user: string
      /**@name modify_user
@description 创建用户
@example user1*/
      modify_user: string
      /**@name create_time
@description 创建时间
@example 1646907507*/
      create_time: number
      /**@name modify_time
@description 更新时间
@example 1646907507*/
      modify_time: number
      /**@name release_time
@description 发布时间
@example 1646907507*/
      release_time: number
      /**@name content_lock
@description 内容锁
@example lock,open*/
      content_lock: string
      /**@name content_desc
@description 内容描述*/
      content_desc: string
      version: number
      extend: { [key: string]: string }
    }[]
  }
}
/**@name ContentIdListQuery
@summary 内容id列表查询
@description 根据查询条件筛选满足条件的内容id
@alias /api/page_content.ContentSvr/ContentIdListQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentIdListQuery = (
  props: ImplRequest<ContentIdListQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentIdListQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "list|1-2": [
          {
            content_id: "内容id",
            template_id: "模板id",
            content_title: "内容标题",
            content_icon: "内容缩略图",
            create_user: "user1",
            modify_user: "user1",
            create_time: 1646907507,
            modify_time: 1646907507,
            release_time: 1646907507,
            content_lock: "lock,open",
            content_desc: "内容描述",
            version: "@integer(1, 100)",
            extend: { Map: "@string" },
          },
        ],
      },
    },
    props,
  ) as ImplResponse<ContentIdListQueryResponse>
export type ContentUrlQueryRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  /**@name page_list
@description 用于直接保存页面内容之后返回页面地址的超级工厂预览需求*/
  page_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name html
@description html页内容*/
    html?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name show
@description 多页面标识*/
    show?: number
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
    extend?: { [key: string]: string }
    page_group_id?: number
    page_group_name?: string
    page_group_mode?: string
    page_group_key?: string[]
  }[]
}
/**@name ContentUrlQueryResponse
@description 活动地址查询返回*/
export type ContentUrlQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    url_list: {
      /**@name page_url
@description 页面地址*/
      page_url: string
      /**@name terminal_id
@description 页面终端信息*/
      terminal_id: string
    }[]
    /**@name url_conf
链接有效配置*/
    url_conf: {
      /**@name effective_time
链接有效时间（单位s）  30*/
      effective_time: number
      /**@name effective_times
链接有效次数（） 3*/
      effective_times: number
    }
  }
}
/**@name ContentUrlQuery
@summary 内容预览地址查询
@description 内容预览地址查询
@alias /api/page_content.ContentSvr/ContentUrlQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentUrlQuery = (
  props: ImplRequest<ContentUrlQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentUrlQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "url_list|1-2": [{ page_url: "页面地址", terminal_id: "页面终端信息" }],
        url_conf: {
          effective_time: "@integer(1, 100)",
          effective_times: "@integer(1, 100)",
        },
      },
    },
    props,
  ) as ImplResponse<ContentUrlQueryResponse>
/**@name AddLanguageRequest
====================多语言==============*/
export type AddLanguageRequest = {
  language_list?: {
    /**@name id
@description 语言id
@example {"id":"en-AU"}*/
    id?: string
    /**@name conf_name
@description 配置名称
@example {"conf_name":"英文"}*/
    conf_name?: string
    /**@name desc
@description 语言描述
@example {"desc":"英文，"}*/
    desc?: string
    /**@name name
@description 语言描述
@example {"name":"English"}*/
    name?: string
  }[]
}
export type AddLanguageResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
}
/**@name AddLanguage
@summary 语种录入
@description 语种语言
@router /api/page_content.ContentSvr/AddLanguage
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddLanguage = (
  props: ImplRequest<AddLanguageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/AddLanguage",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<AddLanguageResponse>
export type QueryLanguageRequest = {
  condition?: {
    /**@name is_history
@description 查询结果是否带历史记录
@example {"is_history": true}*/
    is_history?: boolean
    /**@name history_size
@description 历史记录条数
@example {"history_size": 5}*/
    history_size?: number
  }
}
export type QueryLanguageResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
  data: {
    language_list: {
      /**@name id
@description 语言id
@example {"id":"en-AU"}*/
      id: string
      /**@name conf_name
@description 配置名称
@example {"conf_name":"英文"}*/
      conf_name: string
      /**@name desc
@description 语言描述
@example {"desc":"英文，"}*/
      desc: string
      /**@name name
@description 语言描述
@example {"name":"English"}*/
      name: string
    }[]
    /**@name history_list
@description 历史记录列表*/
    history_list: QueryLanguageResponse["data"]["language_list"][0][]
  }
}
/**@name QueryLanguage
@summary 语种查询
@description 语种词条
@router /api/page_content.ContentSvr/QueryLanguage
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryLanguage = (
  props: ImplRequest<QueryLanguageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryLanguage",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "language_list|1-2": [
          { id: "en-AU", conf_name: "英文", desc: "英文，", name: "English" },
        ],
        "history_list|1-2": [void 0],
      },
    },
    props,
  ) as ImplResponse<QueryLanguageResponse>
export type ModifyLanguageRequest = {
  language_list?: {
    /**@name id
@description 语言id
@example {"id":"en-AU"}*/
    id?: string
    /**@name conf_name
@description 配置名称
@example {"conf_name":"英文"}*/
    conf_name?: string
    /**@name desc
@description 语言描述
@example {"desc":"英文，"}*/
    desc?: string
    /**@name name
@description 语言描述
@example {"name":"English"}*/
    name?: string
  }[]
}
export type ModifyLanguageResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
}
/**@name ModifyLanguage
@summary 语种修改
@description 语种词条
@router /api/page_content.ContentSvr/ModifyLanguage
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ModifyLanguage = (
  props: ImplRequest<ModifyLanguageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ModifyLanguage",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ModifyLanguageResponse>
export type DeleteLanguageRequest = {
  language_list?: {
    /**@name id
@description 语言id
@example {"id":"en-AU"}*/
    id?: string
    /**@name conf_name
@description 配置名称
@example {"conf_name":"英文"}*/
    conf_name?: string
    /**@name desc
@description 语言描述
@example {"desc":"英文，"}*/
    desc?: string
    /**@name name
@description 语言描述
@example {"name":"English"}*/
    name?: string
  }[]
}
export type DeleteLanguageResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
}
/**@name DeleteLanguage
@summary 语种删除
@description 语种删除
@router /api/page_content.ContentSvr/DeleteLanguage
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteLanguage = (
  props: ImplRequest<DeleteLanguageRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/DeleteLanguage",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<DeleteLanguageResponse>
export type AddTranslateKeyRequest = {
  /**@name activity_id
@description 活动id
@example {"activity_id":"activity_132"}*/
  activity_id?: string
  list?: {
    /**@name component_id
@description 组件id
@example {"component_id":"component_123"}*/
    component_id?: string
    /**@name key_id
@description 要被替换的 组件key
@example {"key_id":"16235871_HUIPMP"}*/
    key_id?: string
    /**@name res_value
@description 要被替换的 组件value
@example {"res_key":"好好学习，天天向上"}*/
    res_value?: string
    /**@name res_language
@description 要被替换的语种
@example {"res_language":"ch"}*/
    res_language?: string
    /**@name page_id
@description 词条所在页面id
@example {"page_id":"page_1603918512_GOJWS"}*/
    page_id?: string
    /**@name terminal_id
@description 终端id
@example {"terminal_id":"mobile"}*/
    terminal_id?: string
    /**@name terminal_name
@description 终端名
@example {"terminal_name":"移动端"}*/
    terminal_name?: string
    obj_list?: {
      /**@name language_id
@description 语言id
@example {"language_id":"en-UA"}*/
      language_id?: string
      /**@name value
@description 替换后的值
@example {"value":"good good study,day day up"}*/
      value?: string
    }[]
  }[]
}
export type AddTranslateKeyResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
}
/**@name AddTranslateKey
@summary 翻译词条录入
@description 翻译词条录入
@router /api/page_content.ContentSvr/AddTranslateKey
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddTranslateKey = (
  props: ImplRequest<AddTranslateKeyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/AddTranslateKey",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<AddTranslateKeyResponse>
export type QueryTranslateKeyRequest = {
  /**@name activity_id
@description 活动id
@example {"activity_id":"activity_132"}*/
  activity_id?: string
}
export type QueryTranslateKeyResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
  data: {
    list: {
      /**@name component_id
@description 组件id
@example {"component_id":"component_123"}*/
      component_id: string
      /**@name key_id
@description 要被替换的 组件key
@example {"key_id":"16235871_HUIPMP"}*/
      key_id: string
      /**@name res_value
@description 要被替换的 组件value
@example {"res_key":"好好学习，天天向上"}*/
      res_value: string
      /**@name res_language
@description 要被替换的语种
@example {"res_language":"ch"}*/
      res_language: string
      /**@name page_id
@description 词条所在页面id
@example {"page_id":"page_1603918512_GOJWS"}*/
      page_id: string
      /**@name terminal_id
@description 终端id
@example {"terminal_id":"mobile"}*/
      terminal_id: string
      /**@name terminal_name
@description 终端名
@example {"terminal_name":"移动端"}*/
      terminal_name: string
      obj_list: {
        /**@name language_id
@description 语言id
@example {"language_id":"en-UA"}*/
        language_id: string
        /**@name value
@description 替换后的值
@example {"value":"good good study,day day up"}*/
        value: string
      }[]
    }[]
  }
}
/**@name QueryTranslateKey
@summary 翻译词条查询
@description 翻译词条查询
@router /api/page_content.ContentSvr/QueryTranslateKey
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryTranslateKey = (
  props: ImplRequest<QueryTranslateKeyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryTranslateKey",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "list|1-2": [
          {
            component_id: "component_123",
            key_id: "16235871_HUIPMP",
            res_value: '{"res_key":"好好学习，天天向上"}',
            res_language: "ch",
            page_id: "page_1603918512_GOJWS",
            terminal_id: "mobile",
            terminal_name: "移动端",
            "obj_list|1-2": [
              { language_id: "en-UA", value: "good good study,day day up" },
            ],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryTranslateKeyResponse>
export type QueryTranslateHistoryRequest = {
  /**@name component_id
@description 活动id
@example {"component_id":"component_123"}*/
  component_id?: string
  key_list?: {
    /**@name component_name
@description 组件名
@example {"component_name":""}*/
    component_name?: string
    /**@name key_value
@description 被翻译的文案
@example {"key_value":"好好学习"}*/
    key_value?: string
  }[]
}
export type QueryTranslateHistoryResponse = {
  /**@name result_code
@description 错误码
@example {"result_code":"0"}*/
  result_code: string
  /**@name result_info
@description 错误信息
@example {"result_info":"ok"}*/
  result_info: string
  data: {
    history_list: {
      /**@name component_id
@description 组件id
@example {"component_id":"图片组件"}*/
      component_id: string
      /**@name key_id
@description 要被替换的 组件key
@example {"key_id":"16235871_HUIPMP"}*/
      key_id: string
      /**@name res_value
@description 要被替换的 组件value
@example {"res_key":"好好学习，天天向上"}*/
      res_value: string
      /**@name res_language
@description 要被替换的语种
@example {"res_language":"ch"}*/
      res_language: string
      /**@name page_id
@description 词条所在页面id
@example {"page_id":"page_1603918512_GOJWS"}*/
      page_id: string
      /**@name des_language
@description 语言id
@example {"des_language":"en-UA"}*/
      des_language: string
      /**@name des_value
@description 替换后的值
@example {"des_value":"good good study,day day up"}*/
      des_value: string
    }[]
  }
}
/**@name QueryTranslateHistory
@summary 历史词条查询
@description 翻译词条查询
@router /api/page_content.ContentSvr/QueryTranslateHistory
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryTranslateHistory = (
  props: ImplRequest<QueryTranslateHistoryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryTranslateHistory",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        "history_list|1-2": [
          {
            component_id: "图片组件",
            key_id: "16235871_HUIPMP",
            res_value: '{"res_key":"好好学习，天天向上"}',
            res_language: "ch",
            page_id: "page_1603918512_GOJWS",
            des_language: "en-UA",
            des_value: "good good study,day day up",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryTranslateHistoryResponse>
export type GemsVersionQueryRequest = {}
export type GemsVersionQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: { polyfill: string; react: string; index: string }
}
/**@name GemsVersionQuery
@summary 渲染器版本查询
@description 渲染器版本查询
@alias /api/page_content.ContentSvr/GemsVersionQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GemsVersionQuery = (
  props: ImplRequest<GemsVersionQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/GemsVersionQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: { polyfill: "@string", react: "@string", index: "@string" },
    },
    props,
  ) as ImplResponse<GemsVersionQueryResponse>
export type InjectHtmlInfoQueryRequest = { content_id?: string }
export type InjectHtmlInfoQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: { inject_info: { [key: string]: string } }
}
/**@name InjectHtmlInfoQuery
@summary 查询html需要注入的信息
@description 查询html需要注入的信息
@alias /api/page_content.ContentSvr/InjectHtmlInfoQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const InjectHtmlInfoQuery = (
  props: ImplRequest<InjectHtmlInfoQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/InjectHtmlInfoQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: { inject_info: { Map: "@string" } },
    },
    props,
  ) as ImplResponse<InjectHtmlInfoQueryResponse>
export type ContentExtendModifyRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  extend?: { [key: string]: string }
}
export type ContentExtendModifyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name ContentExtendModify
@summary 内容扩展修改
@description 修改内容的扩展字段
@alias /api/page_content.ContentSvr/ContentExtendModify
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentExtendModify = (
  props: ImplRequest<ContentExtendModifyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentExtendModify",
    { result_code: "0", result_info: "ok" },
    props,
  ) as ImplResponse<ContentExtendModifyResponse>
export type ContentPosterModifyRequest = {
  content_id?: string
  poster_url?: string
}
export type ContentPosterModifyResponse = {
  result_code: string
  result_info: string
}
/**@name ContentPosterModify
@summary 修改封面接口
@description 修改封面接口
@alias /api/page_content.ContentSvr/ContentPosterModify
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentPosterModify = (
  props: ImplRequest<ContentPosterModifyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentPosterModify",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ContentPosterModifyResponse>
export type GetDistributionIdRequest = {
  normal_act?: number
  share_act?: number
  model?: number
}
export type GetDistributionIdResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    id: string
    normal_list: string[]
    share_list: string[]
    model_list: string[]
  }
}
/**@name GetDistributionId
@summary 获取id
@description 修改内容的扩展字段
@alias /api/page_content.ContentSvr/GetDistributionId
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetDistributionId = (
  props: ImplRequest<GetDistributionIdRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/GetDistributionId",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        id: "@string",
        "normal_list|1-2": ["@string"],
        "share_list|1-2": ["@string"],
        "model_list|1-2": ["@string"],
      },
    },
    props,
  ) as ImplResponse<GetDistributionIdResponse>
export type CheckActivityStatusRequest = { activity_id?: string }
export type CheckActivityStatusResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name is_release
是否发布正式环境*/
    is_release: string
    /**@name is_overdue
活动是否过期*/
    is_overdue: string
    /**@name verify_is_release
是否发布体验环境*/
    verify_is_release: string
  }
}
/**@name CheckActivityStatus
@summary 校验活动是否发布过正式环境
@description 校验活动是否发布过正式环境
@alias /api/page_content.ContentSvr/IsActivityReleased
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CheckActivityStatus = (
  props: ImplRequest<CheckActivityStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/CheckActivityStatus",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        is_release: "是否发布正式环境",
        is_overdue: "活动是否过期",
        verify_is_release: "是否发布体验环境",
      },
    },
    props,
  ) as ImplResponse<CheckActivityStatusResponse>
export type WechatMiniProgramPreviewRequest = {
  /**@name app_id
@description 页匠的app_id*/
  app_id?: string
  /**@name auth_app_id
@description 授权应用的app_id*/
  auth_app_id?: string
  /**@name page_protocol
@description 页面协议*/
  page_protocol?: string
}
export type WechatMiniProgramPreviewResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name build_id
小程序构建id*/
    build_id: string
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name WechatMiniProgramPreview
@summary 小程序预览
@description 小程序预览内容
@alias /api/page_content.ContentSvr/WechatMiniProgramPreview
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const WechatMiniProgramPreview = (
  props: ImplRequest<WechatMiniProgramPreviewRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/WechatMiniProgramPreview",
    {
      result_code: "0",
      result_info: "ok",
      data: { build_id: "小程序构建id" },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<WechatMiniProgramPreviewResponse>
export type PreBuildInfoQueryRequest = {
  /**@name build_id
小程序构建id，已废弃，使用build_token查询对应信息*/
  build_id?: string
  /**@name build_token
构建token*/
  build_token?: string
}
export type PreBuildInfoQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name auth_app_id
@description 授权应用的app_id*/
    auth_app_id: string
    /**@name page_protocol
@description 页面协议*/
    page_protocol: string
    /**@name private_key
模板appid对应的私钥*/
    private_key: string
    /**@name template_app_id
@description 模板app_id*/
    template_app_id: string
    /**@name build_result_callback_url
@description 构建结果回调地址*/
    build_result_callback_url: string
    /**@name param
@description 流水线回调拼在地址后面的参数*/
    param: string
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name PreBuildInfoQuery
@summary 构建前置查询
@description 构建前置查询
@alias /api/page_content.ContentSvr/PreBuildInfoQuery
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const PreBuildInfoQuery = (
  props: ImplRequest<PreBuildInfoQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/PreBuildInfoQuery",
    {
      result_code: "0",
      result_info: "ok",
      data: { build_id: "小程序构建id" },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<PreBuildInfoQueryResponse>
export type QueryBuildStateRequest = {
  /**@name build_id
小程序构建id*/
  build_id?: string
  /**@name app_id
@description 页匠的app_id*/
  app_id?: string
  /**@name auth_app_id
@description 授权应用的app_id*/
  auth_app_id?: string
}
export type QueryBuildStateResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name build_state
@description 构建状态 1为构建中 2为已完成 3为失败*/
    build_state: number
    /**@name qr_code
@description 小程序二维码*/
    qr_code: string
    /**@name build_errmsg
构建错误信息*/
    build_errmsg: string
    /**@name build_desc
build_desc 构建过程描述*/
    build_desc: string
    /**@name build_progress
build_progress 构建进度*/
    build_progress: string
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name QueryBuildState
@summary 构建状态查询
@description 构建状态查询
@alias /api/page_content.ContentSvr/QueryBuildState
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryBuildState = (
  props: ImplRequest<QueryBuildStateRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryBuildState",
    {
      result_code: "0",
      result_info: "ok",
      data: { build_id: "小程序构建id" },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<QueryBuildStateResponse>
export type BuildResultCallbackRequest = {
  /**@name build_id
小程序构建id，已废弃，使用build_token查询对应信息*/
  build_id?: string
  /**@name qr_code
@description 小程序二维码*/
  qr_code?: string
  /**@name app_id
@description 页匠的app_id*/
  app_id?: string
  /**@name auth_app_id
@description 授权应用的app_id*/
  auth_app_id?: string
  /**@name build_token
构建token*/
  build_token?: string
  /**@name build_state
@description 构建状态*/
  build_state?: number
  /**@name build_errmsg
构建错误信息*/
  build_errmsg?: string
  /**@name build_desc
build_desc 构建过程描述*/
  build_desc?: string
  /**@name build_progress
build_progress 构建进度
@example 50%*/
  build_progress?: string
}
export type BuildResultCallbackResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name BuildResultCallback
@summary 构建回调接口
@description 构建回调接口
@alias /api/page_content.ContentSvr/BuildResultCallback
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const BuildResultCallback = (
  props: ImplRequest<BuildResultCallbackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/BuildResultCallback",
    {
      result_code: "0",
      result_info: "ok",
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<BuildResultCallbackResponse>
/**@name RecvLandunBuildResRequest
RecvLandunBuildResRequest 接收蓝盾构建结果请求 https://iwiki.woa.com/pages/viewpage.action?pageId=36336328*/
export type RecvLandunBuildResRequest = {
  /**@name event
event 事件类型*/
  event?: string
  /**@name data
data 数据*/
  data?: {
    /**@name projectId
projectId 工程id*/
    projectId?: string
    /**@name pipelineId
pipelineId 流水线id*/
    pipelineId?: string
    /**@name buildId
buildId 构建id*/
    buildId?: string
    /**@name stageId
stageId 阶段id*/
    stageId?: string
    /**@name taskId
taskId 任务id*/
    taskId?: string
    /**@name trigger
trigger 触发来源*/
    trigger?: string
    /**@name pipelineName
pipelineName 流水线名称*/
    pipelineName?: string
    /**@name userId
userId 用户id*/
    userId?: string
    /**@name triggerUser
triggerUser 触发用户*/
    triggerUser?: string
    /**@name cancelUserId
cancelUserId 取消用户*/
    cancelUserId?: string
    /**@name status
status 状态*/
    status?: string
    /**@name startTime
startTime 启动时间*/
    startTime?: string
    /**@name endTime
endTime 结束时间*/
    endTime?: string
    model?: {
      stages?: {
        /**@name stageName
stageName 阶段名*/
        stageName?: string
        /**@name name
name*/
        name?: string
        /**@name status
status 状态*/
        status?: string
        /**@name startTime
startTime 启动时间戳*/
        startTime?: string
        /**@name endTime
endTime 截止时间戳*/
        endTime?: string
        /**@name jobs
jobs 工作列表*/
        jobs?: {
          /**@name jobName
jobName 工作名称*/
          jobName?: string
          /**@name status
status 状态*/
          status?: string
          /**@name startTime
startTime 启动时间戳*/
          startTime?: string
          /**@name endTime
endTime 截止时间戳*/
          endTime?: string
          /**@name tasks
tasks 任务列表*/
          tasks?: {
            /**@name taskId
taskId 任务id*/
            taskId?: string
            /**@name taskName
taskName 任务名称*/
            taskName?: string
            /**@name atomCode
atomCode*/
            atomCode?: string
            /**@name status
status 状态*/
            status?: string
            /**@name startTime
startTime 启动时间戳*/
            startTime?: string
            /**@name endTime
endTime 截止时间戳*/
            endTime?: string
          }[]
        }[]
      }[]
    }
  }
}
/**@name RecvLandunBuildResResponse
RecvLandunBuildResReply 接收蓝盾构建结果应答*/
export type RecvLandunBuildResResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name RecvLandunBuildRes
@summary 接收蓝盾构建结果
@description 接收蓝盾构建结果
@alias /api/page_content.ContentSvr/RecvLandunBuildRes
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const RecvLandunBuildRes = (
  props: ImplRequest<RecvLandunBuildResRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/RecvLandunBuildRes",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<RecvLandunBuildResResponse>
/**@name MiniprogramProtoStoreRequest
MiniprogramProtoStoreRequest 小程序构建协议存储请求*/
export type MiniprogramProtoStoreRequest = {
  /**@name content_id
content_id 内容id*/
  content_id?: string
  /**@name content_type
content_type 内容类型 activity=活动 content=内容页 shop=商城*/
  content_type?: string
  /**@name page_protocol
page_protocol 小程序落地页协议*/
  page_protocol?: string
  /**@name authorizer_app_id
authorizer_app_id 授权方应用id*/
  authorizer_app_id?: string
  /**@name authorizer_app_raw_id
authorizer_app_raw_id 授权方应用原始id*/
  authorizer_app_raw_id?: string
}
/**@name MiniprogramProtoStoreResponse
MiniprogramProtoStoreReply 小程序构建协议存储应答*/
export type MiniprogramProtoStoreResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name MiniprogramProtoStore
MiniprogramProtoStore 小程序构建协议存储
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const MiniprogramProtoStore = (
  props: ImplRequest<MiniprogramProtoStoreRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/MiniprogramProtoStore",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<MiniprogramProtoStoreResponse>
/**@name MiniprogramProtoQueryRequest
MiniprogramProtoQueryRequest 小程序构建协议查询请求*/
export type MiniprogramProtoQueryRequest = {
  /**@name content_ids
内容id列表*/
  content_ids?: string[]
  /**@name authorizer_app_id
授权方微信appid*/
  authorizer_app_id?: string
}
/**@name MiniprogramProtoQueryResponse
MiniprogramProtoQueryReply 小程序构建协议查询请求*/
export type MiniprogramProtoQueryResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  /**@name data
data 返回数据*/
  data: {
    page_protos: {
      /**@name content_id
content_id 内容id*/
      content_id: string
      /**@name content_type
content_type 内容类型*/
      content_type: string
      /**@name page_proto
page_proto 落地页协议*/
      page_proto: string
      /**@name authorizer_app_id
authorizer_app_id 授权方微信应用id*/
      authorizer_app_id: string
      /**@name authorizer_app_raw_id
authorizer_app_raw_id 授权方微信原始id*/
      authorizer_app_raw_id: string
      /**@name ext1
ext1 扩展信息*/
      ext1: string
    }[]
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name MiniprogramProtoQuery
MiniprogramProtoQuery 小程序构建协议查询
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const MiniprogramProtoQuery = (
  props: ImplRequest<MiniprogramProtoQueryRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/MiniprogramProtoQuery",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: { build_id: "小程序构建id" },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<MiniprogramProtoQueryResponse>
export type ContentCreateXYXRequest = {
  /**@name content_title
@description 内容标题*/
  content_title?: string
  /**@name content_type
@description 内容类型(activity:活动  content:内容页  shop:商城)*/
  content_type?: string
  /**@name activity_id
@description 活动id，非活动场景为空*/
  activity_id?: string
  /**@name poster_url
@description 内容封面*/
  poster_url?: string
  /**@name desc
@description 内容描述*/
  desc?: string
  /**@name template_id
@description 模板id*/
  template_id?: string
  /**@name template_type
@description 模板类型*/
  template_type?: string
  /**@name page_list
@description 页面列表*/
  page_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name html
@description html页内容*/
    html?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name show
@description 多页面标识*/
    show?: number
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
    extend?: { [key: string]: string }
    page_group_id?: number
    page_group_name?: string
    page_group_mode?: string
    page_group_key?: string[]
  }[]
  component_list?: { component_id?: string; component_type?: string }[]
  extend?: { [key: string]: string }
  create_type?: string
  loop_info?: {
    base_time?: { begin_time?: number; end_time?: number }
    loop_list?: NonNullable<
      NonNullable<ContentCreateXYXRequest["loop_info"]>["base_time"]
    >[]
  }
  edit_time?: string
  product_info?: string
  price?: string
  discount?: string
  gift_type?: string
  create_from?: string
  video_set?: { video_definition?: string }
  draft_id?: string
  security_list?: {
    security_id?: string
    /**@name security_type
img,video,audio,text,file,link*/
    security_type?: string
    security_value?: string
    security_name?: string
  }[]
  content_toc_extend?: { [key: string]: string }
}
export type ContentCreateXYXResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    /**@name content_id
@description 内容id*/
    content_id: string
    missing_product_list: { product_id: string; product_name: string }[]
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentCreateXYX
@summary 内容创建
@description 根据模板创建内容内容
@alias /api/page_content.ContentSvr/ContentCreate
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentCreateXYX = (
  props: ImplRequest<ContentCreateXYXRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentCreateXYX",
    {
      result_code: "0",
      result_info: "ok",
      data: {
        content_id: "内容id",
        "missing_product_list|1-2": [
          { product_id: "@string", product_name: "@string" },
        ],
      },
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentCreateXYXResponse>
export type ContentModifyXYXRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  /**@name content_title
@description 内容标题*/
  content_title?: string
  /**@name template_id
@description 模板id*/
  template_id?: string
  /**@name content_desc
@description 内容描述*/
  content_desc?: string
  /**@name icon_url
@description 图标*/
  icon_url?: string
  /**@name preview_img_url
@description 预览图*/
  preview_img_url?: string
  /**@name poster_url
@description 封面*/
  poster_url?: string
  /**@name page_list
@description 页面列表*/
  page_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name html
@description html页内容*/
    html?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name show
@description 多页面标识*/
    show?: number
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
    extend?: { [key: string]: string }
    page_group_id?: number
    page_group_name?: string
    page_group_mode?: string
    page_group_key?: string[]
  }[]
  component_list?: { component_id?: string; component_type?: string }[]
  extend?: { [key: string]: string }
  /**@name create_type
@description 分享信息

ShareInfo share_info = 11;

@description ios  android 系统

string content_os = 12;

@description 平台 qq，wx

string content_pl = 13;

@description 使用的素材列表

repeated string material_list = 14;
int32 is_check_ok = 15;*/
  create_type?: string
  loop_info?: {
    base_time?: { begin_time?: number; end_time?: number }
    loop_list?: NonNullable<
      NonNullable<ContentModifyXYXRequest["loop_info"]>["base_time"]
    >[]
  }
  edit_time?: string
  product_info?: string
  price?: string
  discount?: string
  gift_type?: string
  create_from?: string
  video_set?: { video_definition?: string }
  security_list?: {
    security_id?: string
    /**@name security_type
img,video,audio,text,file,link*/
    security_type?: string
    security_value?: string
    security_name?: string
  }[]
  content_toc_extend?: { [key: string]: string }
}
/**@name ContentModifyXYXResponse
@description 内容列表查询返回*/
export type ContentModifyXYXResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
  data: { missing_product_list: { product_id: string; product_name: string }[] }
}
/**@name ContentModifyXYX
@summary 内容修改
@description 修改内容的内容
@alias /api/page_content.ContentSvr/ContentModify
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentModifyXYX = (
  props: ImplRequest<ContentModifyXYXRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentModifyXYX",
    {
      result_code: "0",
      result_info: "ok",
      display_info: "display_info 错误显示信息",
      data: {
        "missing_product_list|1-2": [
          { product_id: "@string", product_name: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<ContentModifyXYXResponse>
export type ContentRuleCopyRequest = {
  default_app_id?: string
  rule_id_list?: string[]
}
export type ContentRuleCopyResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  data: { replace_map: { [key: string]: string } }
}
/**@name ContentRuleCopy
@summary 内容复制
@description 内容复制
@alias /api/page_content.ContentSvr/ContentCopy
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentRuleCopy = (
  props: ImplRequest<ContentRuleCopyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ContentRuleCopy",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: { build_id: "小程序构建id" },
    },
    props,
  ) as ImplResponse<ContentRuleCopyResponse>
export type CreateDraftRequest = {
  draft_name?: string
  template_id?: string
  template_name?: string
  draft_cover?: string
  draft_info?: string
  is_private?: string
}
export type CreateDraftResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  data: { draft_id: string }
}
/**@name CreateDraft
@summary 创建草稿
@description 创建草稿
@alias /api/page_content.ContentSvr/CreateDraft
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateDraft = (
  props: ImplRequest<CreateDraftRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/CreateDraft",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: { build_id: "小程序构建id" },
    },
    props,
  ) as ImplResponse<CreateDraftResponse>
export type ModifyDraftRequest = {
  draft_id?: string
  draft_name?: string
  template_id?: string
  template_name?: string
  draft_cover?: string
  draft_info?: string
  is_private?: string
}
export type ModifyDraftResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
}
/**@name ModifyDraft
@summary 修改草稿
@description 修改草稿
@alias /api/page_content.ContentSvr/ModifyDraft
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ModifyDraft = (
  props: ImplRequest<ModifyDraftRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/ModifyDraft",
    { result_code: "result_code 结果码", result_info: "result_info 结果信息" },
    props,
  ) as ImplResponse<ModifyDraftResponse>
export type QueryDraftListRequest = {
  draft_id_or_name?: string
  /**@name is_query_myself
"true"查询登录用户自己 其他值都为"false"*/
  is_query_myself?: string
  page_size?: string
  page_num?: string
}
export type QueryDraftListResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  data: {
    draft_list: {
      draft_id: string
      draft_name: string
      template_id: string
      template_name: string
      draft_cover: string
      is_private: string
      /**@name status
1:草稿状态  2:草稿已创建活动*/
      status: string
      create_user: string
      modify_user: string
      create_time: string
      modify_time: string
    }[]
    total: string
  }
}
/**@name QueryDraftList
@summary 查询草稿列表
@description 查询草稿列表
@alias /api/page_content.ContentSvr/QueryDraftList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryDraftList = (
  props: ImplRequest<QueryDraftListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryDraftList",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: {
        "draft_list|1-2": [
          {
            draft_id: "@string",
            draft_name: "@string",
            template_id: "@string",
            template_name: "@string",
            draft_cover: "@string",
            is_private: "@string",
            status: "1:草稿状态  2:草稿已创建活动",
            create_user: "@name",
            modify_user: "@name",
            create_time: "@string",
            modify_time: "@string",
          },
        ],
        total: "@string",
      },
    },
    props,
  ) as ImplResponse<QueryDraftListResponse>
export type QueryDraftDetailRequest = { draft_id?: string }
export type QueryDraftDetailResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  data: {
    draft_id: string
    draft_name: string
    template_id: string
    template_name: string
    draft_cover: string
    draft_info: string
    is_private: string
    /**@name status
1:草稿状态  2:草稿已创建活动*/
    status: string
    create_user: string
    modify_user: string
    create_time: string
    modify_time: string
  }
}
/**@name QueryDraftDetail
@summary 查询草稿详情
@description 查询草稿详情
@alias /api/page_content.ContentSvr/QueryDraftDetail
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryDraftDetail = (
  props: ImplRequest<QueryDraftDetailRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryDraftDetail",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: {
        draft_id: "@string",
        draft_name: "@string",
        template_id: "@string",
        template_name: "@string",
        draft_cover: "@string",
        draft_info: "@string",
        is_private: "@string",
        status: "1:草稿状态  2:草稿已创建活动",
        create_user: "@name",
        modify_user: "@name",
        create_time: "@string",
        modify_time: "@string",
      },
    },
    props,
  ) as ImplResponse<QueryDraftDetailResponse>
export type DeleteDraftRequest = { draft_id?: string }
export type DeleteDraftResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
}
/**@name DeleteDraft
@summary 删除草稿
@description 删除草稿
@alias /api/page_content.ContentSvr/DeleteDraft
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteDraft = (
  props: ImplRequest<DeleteDraftRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/DeleteDraft",
    { result_code: "result_code 结果码", result_info: "result_info 结果信息" },
    props,
  ) as ImplResponse<DeleteDraftResponse>
export type QueryEditorBetweenTwoVersionRequest = {
  content_id?: string
  begin_version?: string
  end_version?: string
}
export type QueryEditorBetweenTwoVersionResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  data: { version_list: { version: number; user_id: string }[] }
}
/**@name QueryEditorBetweenTwoVersion
@summary 查询两个版本间的修改用户
@description 查询两个版本间的修改用户
@alias /api/page_content.ContentSvr/QueryEditorBetweenTwoVersion
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryEditorBetweenTwoVersion = (
  props: ImplRequest<QueryEditorBetweenTwoVersionRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryEditorBetweenTwoVersion",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: {
        "version_list|1-2": [
          { version: "@integer(1, 100)", user_id: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryEditorBetweenTwoVersionResponse>
export type QueryContentTerminalAndGroupRequest = { content_id?: string }
export type QueryContentTerminalAndGroupResponse = {
  /**@name result_code
result_code 结果码*/
  result_code: string
  /**@name result_info
result_info 结果信息*/
  result_info: string
  data: {
    content_info: { content_name: string; content_type: string }
    terminal_and_group: {
      [key: string]: {
        group_info_list: { group_id: string; group_name: string }[]
      }
    }
  }
}
/**@name QueryContentTerminalAndGroup
@summary 查询内容详情
@description 查询内容详情
@alias /api/page_content.ContentSvr/QueryContentTerminalAndGroup
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryContentTerminalAndGroup = (
  props: ImplRequest<QueryContentTerminalAndGroupRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.page_content.ContentSvr/QueryContentTerminalAndGroup",
    {
      result_code: "result_code 结果码",
      result_info: "result_info 结果信息",
      data: {
        content_info: { content_name: "@string", content_type: "@string" },
        terminal_and_group: { Map: void 0 },
      },
    },
    props,
  ) as ImplResponse<QueryContentTerminalAndGroupResponse>
