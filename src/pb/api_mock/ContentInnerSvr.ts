/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type ContentRegisterRequest = {
  content_id?: string
  content_name?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
}
export type ContentRegisterResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentRegister
@summary 商铺注册
@description 商铺注册
@alias /api/content_manage.ContentSvr/ContentRegister
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentRegister = (
  props: ImplRequest<ContentRegisterRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentInnerSvr/ContentRegister",
    {
      result_code: "0",
      result_info: "ok",
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentRegisterResponse>
export type ContentInfoModifyRequest = {
  content_id?: string
  content_name?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
}
export type ContentInfoModifyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentInfoModify
@summary 商铺注册
@description 商铺注册
@alias /api/content_manage.ContentSvr/ContentInfoModify
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ContentInfoModify = (
  props: ImplRequest<ContentInfoModifyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.ContentInnerSvr/ContentInfoModify",
    {
      result_code: "0",
      result_info: "ok",
      display_info: "display_info 错误显示信息",
    },
    props,
  ) as ImplResponse<ContentInfoModifyResponse>
