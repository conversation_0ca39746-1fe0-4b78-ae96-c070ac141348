/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type TranslateRequest = {
  /**@name app_id
unsafe_str 应用id, 如avatar*/
  app_id?: string
  /**@name source_lang
unsafe_str 使用ISO-639-1标准，如en/zh/fr/ar/ja/ko/es/tr*/
  source_lang?: string
  /**@name source_seg
unsafe_str*/
  source_seg?: string
  /**@name target_lang
unsafe_str 使用ISO-639-1标准，如en/zh/fr/ar/ja/ko/es/tr*/
  target_lang?: string
  stream?: boolean
}
export type TranslateResponse = {
  /**@name code
tRPC数据校验模块 开启自动数据校验。限制传入参数只能为tsecstr默认安全类型*/
  code: number
  msg: string
  target_seg: string
}
/**@name Translate
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const Translate = (
  props: ImplRequest<TranslateRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.midas_oversea.intelli_app.MultilingualService/Translate",
    { code: "@integer(1, 100)", msg: "@string", target_seg: "@string" },
    props,
  ) as ImplResponse<TranslateResponse>
