/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type SetRequest = { key?: string; value?: string }
export type SetResponse = {
  result_code: string
  result_info: string
  url: string
}
/**@name Set
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const Set = (
  props: ImplRequest<SetRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.cos_store.CosStoreSvr/Set",
    { result_code: "@string", result_info: "@string", url: "@string" },
    props,
  ) as ImplResponse<SetResponse>
export type SetBase64Request = {
  /**@name key
上传至cos路径*/
  key?: string
  /**@name value
读取文件后二进制流进行base64编码*/
  value?: string
}
export type SetBase64Response = {
  result_code: string
  result_info: string
  url: string
}
/**@name SetBase64
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SetBase64 = (
  props: ImplRequest<SetBase64Request>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.cos_store.CosStoreSvr/SetBase64",
    { result_code: "@string", result_info: "@string", url: "@string" },
    props,
  ) as ImplResponse<SetBase64Response>
export type GetRequest = { key?: string }
export type GetResponse = {
  result_code: string
  result_info: string
  data: { value: string }
}
/**@name Get
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const Get = (
  props: ImplRequest<GetRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.cos_store.CosStoreSvr/Get",
    {
      result_code: "@string",
      result_info: "@string",
      data: { value: "@string" },
    },
    props,
  ) as ImplResponse<GetResponse>
export type GetBase64Request = { key?: string }
export type GetBase64Response = {
  result_code: string
  result_info: string
  data: {
    /**@name value
读取文件后二进制流进行base64编码*/
    value: string
  }
}
/**@name GetBase64
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetBase64 = (
  props: ImplRequest<GetBase64Request>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.cos_store.CosStoreSvr/GetBase64",
    {
      result_code: "@string",
      result_info: "@string",
      data: { value: "读取文件后二进制流进行base64编码" },
    },
    props,
  ) as ImplResponse<GetBase64Response>
export type GetTemporaryKeyRequest = { upload_path?: string }
export type GetTemporaryKeyResponse = {
  result_code: string
  result_info: string
  data: {
    tmp_secret_id: string
    tmp_secret_key: string
    session_token: string
    upload_path: string
    cos_url: string
    cdn_url: string
  }
}
/**@name GetTemporaryKey
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetTemporaryKey = (
  props: ImplRequest<GetTemporaryKeyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.cos_store.CosStoreSvr/GetTemporaryKey",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        tmp_secret_id: "@string",
        tmp_secret_key: "@string",
        session_token: "@string",
        upload_path: "@string",
        cos_url: "@string",
        cdn_url: "@string",
      },
    },
    props,
  ) as ImplResponse<GetTemporaryKeyResponse>
