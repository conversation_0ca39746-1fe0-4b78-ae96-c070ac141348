/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type CreateLiveAnswerRequest = {
  /**@name answer
答案
@description：
@example:*/
  answer?: string
  /**@name answer_type
话术类型
@description：
@example:
@enum:
1:弹幕
2:B端用户新增
3:暂停节点*/
  answer_type?: number
  /**@name target_answer_id
目标话术id
@description：
@example:*/
  target_answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type CreateLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id: string
}
/**@name CreateLiveAnswer
@summary CreateLiveAnswer
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateLiveAnswer = (
  props: ImplRequest<CreateLiveAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/CreateLiveAnswer",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      answer_id: "话术id\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<CreateLiveAnswerResponse>
export type DeleteLiveAnswerRequest = {
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type DeleteLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name DeleteLiveAnswer
@summary DeleteLiveAnswer
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteLiveAnswer = (
  props: ImplRequest<DeleteLiveAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/DeleteLiveAnswer",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<DeleteLiveAnswerResponse>
export type GetDefaultLiveAnswerListRequest = {
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name page_size
每页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
}
export type GetDefaultLiveAnswerListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name records
默认问答库内容
@description：
@example:*/
  records: {
    /**@name group_type
分组类型
@description：
@example:*/
    group_type: number
    /**@name group_id
分组ID
@description：
@example:*/
    group_id: string
    /**@name qa_content_id
问答库内容ID
@description：
@example:*/
    qa_content_id: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }[]
}
/**@name GetDefaultLiveAnswerList
@summary GetDefaultLiveAnswerList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetDefaultLiveAnswerList = (
  props: ImplRequest<GetDefaultLiveAnswerListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/GetDefaultLiveAnswerList",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      query_count: "@integer(1, 100)",
      "records|1-2": [
        {
          group_type: "@integer(1, 100)",
          group_id: "分组ID\n@description：\n@example:",
          qa_content_id: "问答库内容ID\n@description：\n@example:",
          question: "问题\n@description：\n@example:",
          answer: "答案\n@description：\n@example:",
          update_time: "@datetime",
        },
      ],
    },
    props,
  ) as ImplResponse<GetDefaultLiveAnswerListResponse>
export type GetLiveAnswerListRequest = {
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name count
数量
@description：数量
@example:*/
  count?: number
}
export type GetLiveAnswerListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name has_next
是否还有数据
@description：
@example:*/
  has_next: number
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name records
直播互动最终话术
@description：
@example:*/
  records: {
    /**@name live_id
直播ID
@description：
@example:*/
    live_id: string
    /**@name answer_id
话术id
@description：
@example:*/
    answer_id: string
    /**@name answer_sort
话术主排序
@description：
@example:*/
    answer_sort: string
    /**@name answer_sub_sort
话术次排序
@description：
@example:*/
    answer_sub_sort: number
    /**@name danmu_id
直播弹幕ID
@description：
@example:*/
    danmu_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name answer_type
话术类型
@description：
@example:
@enum:
1:弹幕
2:B端用户新增
3:暂停节点*/
    answer_type: number
  }[]
}
/**@name GetLiveAnswerList
@summary GetLiveAnswerList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetLiveAnswerList = (
  props: ImplRequest<GetLiveAnswerListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/GetLiveAnswerList",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      has_next: "@integer(1, 100)",
      context:
        "前后端约定的上下文字符串\n@description：该字段由服务端返回，客户端原封不动的带上上一次的context\n@example:",
      live_id: "直播ID\n@description：\n@example:",
      query_count: "@integer(1, 100)",
      "records|1-2": [
        {
          live_id: "直播ID\n@description：\n@example:",
          answer_id: "话术id\n@description：\n@example:",
          answer_sort: "话术主排序\n@description：\n@example:",
          answer_sub_sort: "@integer(1, 100)",
          danmu_id: "直播弹幕ID\n@description：\n@example:",
          user_id: "用户ID\n@description：\n@example:",
          user_nick: "用户昵称\n@description：\n@example:",
          question: "问题\n@description：\n@example:",
          answer: "答案\n@description：\n@example:",
          status: "@integer(1, 100)",
          create_time: "@datetime",
          answer_type: "@integer(1, 100)",
        },
      ],
    },
    props,
  ) as ImplResponse<GetLiveAnswerListResponse>
export type GetLiveAnswerScriptListRequest = {
  /**@name count
数量
@description：数量
@example:*/
  count?: number
  /**@name node_id
直播节点ID
@description：
@example:*/
  node_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context?: string
  /**@name live_start_time
直播开始时间
@description：
@example:*/
  live_start_time?: string
}
export type GetLiveAnswerScriptListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name has_next
是否还有数据
@description：
@example:*/
  has_next: number
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name records
直播互动最终话术
@description：
@example:*/
  records: {
    /**@name live_id
直播ID
@description：
@example:*/
    live_id: string
    /**@name answer_id
话术id
@description：
@example:*/
    answer_id: string
    /**@name answer_sort
话术主排序
@description：
@example:*/
    answer_sort: string
    /**@name answer_sub_sort
话术次排序
@description：
@example:*/
    answer_sub_sort: number
    /**@name danmu_id
直播弹幕ID
@description：
@example:*/
    danmu_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name answer_type
话术类型
@description：
@example:
@enum:
1:弹幕
2:B端用户新增
3:暂停节点*/
    answer_type: number
  }[]
}
/**@name GetLiveAnswerScriptList
@summary GetLiveAnswerScriptList
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetLiveAnswerScriptList = (
  props: ImplRequest<GetLiveAnswerScriptListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/GetLiveAnswerScriptList",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
      has_next: "@integer(1, 100)",
      context:
        "前后端约定的上下文字符串\n@description：该字段由服务端返回，客户端原封不动的带上上一次的context\n@example:",
      live_id: "直播ID\n@description：\n@example:",
      query_count: "@integer(1, 100)",
      "records|1-2": [
        {
          live_id: "直播ID\n@description：\n@example:",
          answer_id: "话术id\n@description：\n@example:",
          answer_sort: "话术主排序\n@description：\n@example:",
          answer_sub_sort: "@integer(1, 100)",
          danmu_id: "直播弹幕ID\n@description：\n@example:",
          user_id: "用户ID\n@description：\n@example:",
          user_nick: "用户昵称\n@description：\n@example:",
          question: "问题\n@description：\n@example:",
          answer: "答案\n@description：\n@example:",
          status: "@integer(1, 100)",
          create_time: "@datetime",
          answer_type: "@integer(1, 100)",
        },
      ],
    },
    props,
  ) as ImplResponse<GetLiveAnswerScriptListResponse>
export type SaveDanmuToLiveAnswerRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name user_nick
用户昵称
@description：
@example:*/
  user_nick?: string
  /**@name insert_time
插入时间
@description：
@example:*/
  insert_time?: string
  /**@name answer
答案
@description：
@example:*/
  answer?: string
  /**@name question
问题
@description：
@example:*/
  question?: string
  /**@name danmu_id
直播弹幕ID
@description：
@example:*/
  danmu_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type SaveDanmuToLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SaveDanmuToLiveAnswer
@summary SaveDanmuToLiveAnswer
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SaveDanmuToLiveAnswer = (
  props: ImplRequest<SaveDanmuToLiveAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/SaveDanmuToLiveAnswer",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SaveDanmuToLiveAnswerResponse>
export type SetLiveAnswerStatusRequest = {
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name node_id
直播节点ID
@description：
@example:*/
  node_id?: string
  /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
  status?: number
}
export type SetLiveAnswerStatusResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SetLiveAnswerStatus
@summary SetLiveAnswerStatus
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SetLiveAnswerStatus = (
  props: ImplRequest<SetLiveAnswerStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/SetLiveAnswerStatus",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SetLiveAnswerStatusResponse>
export type SortLiveAnswerRequest = {
  /**@name target_answer_id
目标话术id
@description：
@example:*/
  target_answer_id?: string
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type SortLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SortLiveAnswer
@summary SortLiveAnswer
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SortLiveAnswer = (
  props: ImplRequest<SortLiveAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/SortLiveAnswer",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<SortLiveAnswerResponse>
export type UpdateLiveAnswerRequest = {
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name answer
答案
@description：
@example:*/
  answer?: string
}
export type UpdateLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateLiveAnswer
@summary UpdateLiveAnswer
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateLiveAnswer = (
  props: ImplRequest<UpdateLiveAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/UpdateLiveAnswer",
    {
      message: "错误描述信息\n@description：\n@example:",
      code: "状态码\n@description：\n@example:",
    },
    props,
  ) as ImplResponse<UpdateLiveAnswerResponse>
