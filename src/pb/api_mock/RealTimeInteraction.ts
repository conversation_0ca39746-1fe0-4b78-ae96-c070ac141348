/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type GetDefaultAnswerRequest = { live_id?: string }
export type GetDefaultAnswerResponse = {
  result_code: string
  result_info: string
  data: {
    live_id: string
    get_default_answer_list: {
      node_id: string
      answers: { q: string; a: string }[]
    }[]
  }
}
/**@name GetDefaultAnswer
@summary 获取默认的回复
@alias /api/interaction.Service/GetDefaultAnswer
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetDefaultAnswer = (
  props: ImplRequest<GetDefaultAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/GetDefaultAnswer",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        live_id: "@string",
        "get_default_answer_list|1-2": [
          {
            node_id: "@string",
            "answers|1-2": [{ q: "@string", a: "@string" }],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<GetDefaultAnswerResponse>
export type SetDefaultAnswerRequest = {
  live_id?: string
  set_default_answer_list?: {
    node_id?: string
    answers?: { q?: string; a?: string }[]
  }[]
}
export type SetDefaultAnswerResponse = {
  result_code: string
  result_info: string
}
/**@name SetDefaultAnswer
@summary 设置默认的播报规则
@alias /api/interaction.Service/SetDefaultAnswer
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SetDefaultAnswer = (
  props: ImplRequest<SetDefaultAnswerRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/SetDefaultAnswer",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SetDefaultAnswerResponse>
export type GetDmAnswerListRequest = { live_id?: string; node_id?: string }
export type GetDmAnswerListResponse = {
  result_code: string
  result_info: string
  data: {
    dm_answer_list: {
      live_id: string
      node_id: string
      danmu_id: string
      user_id: string
      user_nick: string
      question: string
      answer: string
      status: number
      insert_time: string
      /**@name answer_method
问答回复方式
@description：dman:数字人回复, barrage:弹幕回复
@example: dman*/
      answer_method: string
    }[]
  }
}
/**@name GetDmAnswerList
@summary 获取弹幕的列表
@alias /api/interaction.Service/GetDmAnswerList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetDmAnswerList = (
  props: ImplRequest<GetDmAnswerListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/GetDmAnswerList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        live_id: "@string",
        "get_default_answer_list|1-2": [
          {
            node_id: "@string",
            "answers|1-2": [{ q: "@string", a: "@string" }],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<GetDmAnswerListResponse>
export type SetDmAnswerStatusRequest = { live_id?: string; danmu_id?: string }
export type SetDmAnswerStatusResponse = {
  result_code: string
  result_info: string
}
/**@name SetDmAnswerStatus
@summary 设置弹幕的播报状态
@alias /api/interaction.Service/SetDmAnswerStatus
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SetDmAnswerStatus = (
  props: ImplRequest<SetDmAnswerStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/SetDmAnswerStatus",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SetDmAnswerStatusResponse>
export type QueryLivesQuestionInfoRequest = { live_id_list?: string[] }
export type QueryLivesQuestionInfoResponse = {
  result_code: string
  result_info: string
  data: { live_question_info_list: { live_id: string; question_num: number }[] }
}
/**@name QueryLivesQuestionInfo
@summary 查直播间提问信息
@alias /api/interaction.Service/QueryLivesQuestionInfo
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryLivesQuestionInfo = (
  props: ImplRequest<QueryLivesQuestionInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/QueryLivesQuestionInfo",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        live_id: "@string",
        "get_default_answer_list|1-2": [
          {
            node_id: "@string",
            "answers|1-2": [{ q: "@string", a: "@string" }],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryLivesQuestionInfoResponse>
export type QueryLiveDmAnswerListRequest = {
  live_id?: string
  /**@name query_page_num
查询分页单页数量
@description：
@example:*/
  query_page_num?: number
  /**@name query_page_size
分页查询页数
@description：
@example:*/
  query_page_size?: number
}
export type QueryLiveDmAnswerListResponse = {
  result_code: string
  result_info: string
  data: {
    answer_total_num: number
    dm_answer_list: {
      live_id: string
      node_id: string
      danmu_id: string
      user_id: string
      user_nick: string
      question: string
      answer: string
      status: number
      insert_time: string
      need_answer: number
      /**@name answer_method
问答回复方式
@description：dman:数字人回复, barrage:弹幕回复
@example: dman*/
      answer_method: string
      /**@name answer_source
答案来源
@description: manual_edit:人工编辑, large_model:大模型生成
@example: dman*/
      answer_source: string
      answer_time: string
      /**@name in_answer_question_library
是否在问题库
@description: 0:非问题库问题, 1:问题库问题
@example: dman*/
      in_answer_question_library: number
      /**@name answer_group_name
匹配到问答库的库名称
@description: 知识库名称
@example: 上架规则*/
      answer_group_name: string
    }[]
  }
}
/**@name QueryLiveDmAnswerList
@summary 获取直播间问答列表
@alias /api/interaction.Service/QueryLiveDmAnswerList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryLiveDmAnswerList = (
  props: ImplRequest<QueryLiveDmAnswerListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/QueryLiveDmAnswerList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        live_id: "@string",
        "get_default_answer_list|1-2": [
          {
            node_id: "@string",
            "answers|1-2": [{ q: "@string", a: "@string" }],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryLiveDmAnswerListResponse>
export type ModifyLiveDmAnswerInfoRequest = {
  live_id?: string
  danmu_id?: string
  answer?: string
  /**@name answer_status
播报状态
@description: 1:表示已播报; 2:表示未播报; 3:编辑中; -1:表示无效
@example: dman*/
  answer_status?: number
  /**@name need_answer
是否需要回答
@description: "true":需要回答; "false":不需要回答
@example: dman*/
  need_answer?: string
  /**@name answer_method
问答回复方式
@description：dman:数字人回复, barrage:弹幕回复
@example: dman*/
  answer_method?: string
}
export type ModifyLiveDmAnswerInfoResponse = {
  result_code: string
  result_info: string
}
/**@name ModifyLiveDmAnswerInfo
@summary 获取直播间问答列表
@alias /api/interaction.Service/ModifyLiveDmAnswerInfo
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ModifyLiveDmAnswerInfo = (
  props: ImplRequest<ModifyLiveDmAnswerInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/ModifyLiveDmAnswerInfo",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ModifyLiveDmAnswerInfoResponse>
export type QueryLiveDmAnswerStatusRequest = {
  live_id?: string
  danmu_id_list?: string[]
}
export type QueryLiveDmAnswerStatusResponse = {
  result_code: string
  result_info: string
  data: {
    dm_answer_status_list: {
      live_id: string
      danmu_id: string
      /**@name status
播报状态
@description: 1:表示已播报; 2:表示未播报; -1:表示无效
@example: dman*/
      status: number
      answer_time: string
    }[]
  }
}
/**@name QueryLiveDmAnswerStatus
@summary 批量查直播间评论的回答状态及时间
@alias /api/interaction.Service/QueryLiveDmAnswerStatus
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryLiveDmAnswerStatus = (
  props: ImplRequest<QueryLiveDmAnswerStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/QueryLiveDmAnswerStatus",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        live_id: "@string",
        "get_default_answer_list|1-2": [
          {
            node_id: "@string",
            "answers|1-2": [{ q: "@string", a: "@string" }],
          },
        ],
      },
    },
    props,
  ) as ImplResponse<QueryLiveDmAnswerStatusResponse>
export type UpdateLiveResourceUsingRequest = {
  /**@name app_id
应用id
@description: 目前不填*/
  app_id?: string
  /**@name resource_id
资源id*/
  resource_id?: string
  /**@name resource_type
资源类型
@description: dman:数字人, tts: tts*/
  resource_type?: string
  /**@name user_id
使用的用户id
@description: 备注性质, 无实际用途*/
  user_id?: string
  /**@name live_id
使用的直播id
@description: 备注性质, 无实际用途*/
  live_id?: string
  /**@name use_scene
使用场景
@description: live:直播间, editor:编辑器*/
  use_scene?: string
  /**@name extend
扩展信息
@description: 建议以json格式存放扩展信息, 1024长度*/
  extend?: string
}
export type UpdateLiveResourceUsingResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateLiveResourceUsing
@summary 更新资源使用状态
@alias /api/interaction.Service/UpdateLiveResourceUsing
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateLiveResourceUsing = (
  props: ImplRequest<UpdateLiveResourceUsingRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/UpdateLiveResourceUsing",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateLiveResourceUsingResponse>
export type ClearLiveResourceUsingRequest = {
  /**@name app_id
应用id
@description: 目前不填*/
  app_id?: string
  /**@name resource_id
资源id*/
  resource_id?: string
  /**@name resource_type
资源类型
@description: dman:数字人, tts: tts*/
  resource_type?: string
}
export type ClearLiveResourceUsingResponse = {
  result_code: string
  result_info: string
}
/**@name ClearLiveResourceUsing
@summary 清理资源使用状态
@alias /api/interaction.Service/ClearLiveResourceUsing
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const ClearLiveResourceUsing = (
  props: ImplRequest<ClearLiveResourceUsingRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/ClearLiveResourceUsing",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<ClearLiveResourceUsingResponse>
export type QueryLiveResourceUsingRequest = {
  /**@name app_id
应用id
@description: 目前不填*/
  app_id?: string
}
export type QueryLiveResourceUsingResponse = {
  result_code: string
  result_info: string
  data: {
    app_id: string
    resource_id: string
    resource_type: string
    user_id: string
    live_id: string
    use_scene: string
    extend: string
    status: number
    last_use_time: string
    create_time: string
    update_time: string
  }[]
}
/**@name QueryLiveResourceUsing
@summary 获取资源使用状态
@alias /api/interaction.Service/QueryLiveResourceUsing
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryLiveResourceUsing = (
  props: ImplRequest<QueryLiveResourceUsingRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.interaction.Service/QueryLiveResourceUsing",
    {
      result_code: "@string",
      result_info: "@string",
      "data|1-2": [
        {
          app_id: "@string",
          resource_id: "@string",
          resource_type: "@string",
          user_id: "@string",
          live_id: "@string",
          use_scene: "@string",
          extend: "@string",
          status: "@integer(1, 100)",
          last_use_time: "@datetime",
          create_time: "@datetime",
          update_time: "@datetime",
        },
      ],
    },
    props,
  ) as ImplResponse<QueryLiveResourceUsingResponse>
