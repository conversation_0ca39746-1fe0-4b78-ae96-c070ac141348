/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type GenerateQRCodeRequest = {}
export type GenerateQRCodeResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
  /**@name token
生成二维码的token*/
  token: string
}
/**@name GenerateQRCode
@summary 生成二维码
@description 生成二维码
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GenerateQRCode = (
  props: ImplRequest<GenerateQRCodeRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/GenerateQRCode",
    {
      result_code: "返回结果代码",
      result_info: "返回结果信息",
      token: "生成二维码的token",
    },
    props,
  ) as ImplResponse<GenerateQRCodeResponse>
export type CheckQRCodeStatusRequest = {
  /**@name token
请求参数：二维码的token*/
  token?: string
}
export type CheckQRCodeStatusResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
  /**@name status
状态: 0:未知, 1:未扫码, 2:已扫码待确认, 3:扫码登陆完成, 4:二维码已失效*/
  status: number
}
/**@name CheckQRCodeStatus
@summary 查询二维码状态
@description 查询二维码状态
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CheckQRCodeStatus = (
  props: ImplRequest<CheckQRCodeStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/CheckQRCodeStatus",
    {
      result_code: "返回结果代码",
      result_info: "返回结果信息",
      status: "@integer(1, 100)",
    },
    props,
  ) as ImplResponse<CheckQRCodeStatusResponse>
export type RegisterAppRequest = {
  /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
  app_type?: string
  /**@name phone_number
手机号码*/
  phone_number?: string
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
  /**@name business_license_cos
营业执照COS地址*/
  business_license_cos?: string
  /**@name api_app_id
API应用ID*/
  api_app_id?: string
  /**@name api_secret
API密钥*/
  api_secret?: string
  /**@name token
达人注册扫码的token*/
  token?: string
}
export type RegisterAppResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_id
账号ID*/
    app_id: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name RegisterApp
@summary 注册应用
@description 注册达人应用
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const RegisterApp = (
  props: ImplRequest<RegisterAppRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/RegisterApp",
    {
      data: { app_id: "账号ID" },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<RegisterAppResponse>
export type AccountListRequest = {
  /**@name nick_name
名称模糊搜索*/
  nick_name?: string
  /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
  status?: string
  /**@name page_num
页码*/
  page_num?: number
  /**@name page_size
每页大小*/
  page_size?: number
}
export type AccountListResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name count
账号数量*/
    count: number
    /**@name account_info_list
账号信息列表*/
    account_info_list: {
      /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
      app_type: string
      /**@name nick_name
昵称*/
      nick_name: string
      /**@name avatar_url
头像地址*/
      avatar_url: string
      /**@name role_ids
角色名称: admin:管理员, collaborator:协作者*/
      role_ids: string[]
      /**@name business_license_cos
营业执照COS地址*/
      business_license_cos: string
      /**@name api_app_id
API应用ID*/
      api_app_id: string
      /**@name api_secret
API密钥*/
      api_secret: string
      /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
      status: string
      /**@name app_id
账号ID*/
      app_id: string
      /**@name outer_id
账号ID*/
      outer_id: string
      /**@name subject_name
主体名称*/
      subject_name: string
      /**@name wx_id
微信ID*/
      wx_id: string
      /**@name qq_number
QQ号码*/
      qq_number: string
      /**@name phone_number
手机号码*/
      phone_number: string
      /**@name approve_id
审批ID*/
      approve_id: string
      /**@name comments
审核意见*/
      comments: string
    }[]
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name AccountList
@summary 查询账号列表
@description 根据昵称模糊搜索账号信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AccountList = (
  props: ImplRequest<AccountListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/AccountList",
    {
      data: {
        count: "@integer(1, 100)",
        "account_info_list|1-2": [
          {
            app_type: "小店：WX_SHOP 达人：WX_CHANNEL",
            nick_name: "昵称",
            avatar_url: "头像地址",
            "role_ids|1-2": ["角色名称: admin:管理员, collaborator:协作者"],
            business_license_cos: "营业执照COS地址",
            api_app_id: "API应用ID",
            api_secret: "API密钥",
            status: "审核状态: APPROVED, PENDING, REJECTED",
            app_id: "账号ID",
            outer_id: "账号ID",
            subject_name: "主体名称",
            wx_id: "微信ID",
            qq_number: "QQ号码",
            phone_number: "手机号码",
            approve_id: "审批ID",
            comments: "审核意见",
          },
        ],
      },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<AccountListResponse>
export type GetAccountInfoRequest = {
  /**@name app_id
账号ID*/
  app_id?: string
}
export type GetAccountInfoResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
    app_type: string
    /**@name nick_name
昵称*/
    nick_name: string
    /**@name avatar_url
头像地址*/
    avatar_url: string
    /**@name role_ids
角色名称: admin:管理员, collaborator:协作者*/
    role_ids: string[]
    /**@name business_license_cos
营业执照COS地址*/
    business_license_cos: string
    /**@name api_app_id
API应用ID*/
    api_app_id: string
    /**@name api_secret
API密钥*/
    api_secret: string
    /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
    status: string
    /**@name app_id
账号ID*/
    app_id: string
    /**@name outer_id
账号ID*/
    outer_id: string
    /**@name subject_name
主体名称*/
    subject_name: string
    /**@name wx_id
微信ID*/
    wx_id: string
    /**@name qq_number
QQ号码*/
    qq_number: string
    /**@name phone_number
手机号码*/
    phone_number: string
    /**@name approve_id
审批ID*/
    approve_id: string
    /**@name comments
审核意见*/
    comments: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetAccountInfo
@summary 获取账号信息
@description 根据账号ID获取账号详细信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetAccountInfo = (
  props: ImplRequest<GetAccountInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/GetAccountInfo",
    {
      data: {
        app_type: "小店：WX_SHOP 达人：WX_CHANNEL",
        nick_name: "昵称",
        avatar_url: "头像地址",
        "role_ids|1-2": ["角色名称: admin:管理员, collaborator:协作者"],
        business_license_cos: "营业执照COS地址",
        api_app_id: "API应用ID",
        api_secret: "API密钥",
        status: "审核状态: APPROVED, PENDING, REJECTED",
        app_id: "账号ID",
        outer_id: "账号ID",
        subject_name: "主体名称",
        wx_id: "微信ID",
        qq_number: "QQ号码",
        phone_number: "手机号码",
        approve_id: "审批ID",
        comments: "审核意见",
      },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<GetAccountInfoResponse>
export type EditAccountInfoRequest = {
  /**@name app_id
账号ID*/
  app_id?: string
  /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
  app_type?: string
  /**@name phone_number
手机号码*/
  phone_number?: string
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
  /**@name business_license_cos
营业执照COS地址*/
  business_license_cos?: string
  /**@name api_app_id
API应用ID*/
  api_app_id?: string
  /**@name api_secret
API密钥*/
  api_secret?: string
  /**@name token
达人注册扫码的token*/
  token?: string
}
export type EditAccountInfoResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_id
账号ID*/
    app_id: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name EditAccountInfo
@summary 编辑账号信息
@description 根据提供的参数编辑账号详细信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const EditAccountInfo = (
  props: ImplRequest<EditAccountInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/EditAccountInfo",
    {
      data: { app_id: "账号ID" },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<EditAccountInfoResponse>
export type CheckIsRegisterRequest = {}
export type CheckIsRegisterResponse = {
  /**@name is_register
是否注册*/
  is_register: boolean
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name CheckIsRegister
@summary 检查是否注册
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CheckIsRegister = (
  props: ImplRequest<CheckIsRegisterRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/CheckIsRegister",
    {
      is_register: "@boolean",
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<CheckIsRegisterResponse>
export type SubmitApplyRequest = {
  /**@name invitation_code
邀请码*/
  invitation_code?: string
  /**@name phone_number
手机号码*/
  phone_number?: string
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
}
export type SubmitApplyResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_id
账号ID*/
    app_id: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name SubmitApply
@summary 提交申请
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SubmitApply = (
  props: ImplRequest<SubmitApplyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/SubmitApply",
    {
      data: { app_id: "账号ID" },
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<SubmitApplyResponse>
export type CheckLoginOpenIdRequest = {
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
}
export type CheckLoginOpenIdResponse = {
  /**@name result_code
0校验通过 非0校验不通过*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name CheckLoginOpenId
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CheckLoginOpenId = (
  props: ImplRequest<CheckLoginOpenIdRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/CheckLoginOpenId",
    { result_code: "0校验通过 非0校验不通过", result_info: "返回结果信息" },
    props,
  ) as ImplResponse<CheckLoginOpenIdResponse>
export type AppListRequest = {
  /**@name nick_name
名称模糊搜索*/
  nick_name?: string
  /**@name app_id
app_id模糊搜索*/
  app_id?: string
}
export type AppListResponse = {
  result_code: string
  result_info: string
  data: { appId: string; appName: string; picUrl: string }[]
}
/**@name AppList
@summary 查询账号列表
@description
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AppList = (
  props: ImplRequest<AppListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Register/AppList",
    {
      result_code: "@string",
      result_info: "@string",
      "data|1-2": [{ appId: "@string", appName: "@string", picUrl: "@string" }],
    },
    props,
  ) as ImplResponse<AppListResponse>
