/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type QueryRoleRequest = {
  /**@name role_id
user_id的列表，传值的话就是查询指定范围的用户信息*/
  role_id?: string[]
  page_num?: number
  page_size?: number
}
export type QueryRoleResponse = {
  result_code: string
  result_info: string
  data: {
    total_size: number
    role_list: {
      sys_code: string
      merchant: string
      app_id: string
      role_id: string
      role_name: string
      creator: string
      role_description: string
      bind_user_count: number
      create_time: string
      resource_id_list: string[]
      user_id_list: string[]
    }[]
  }
}
/**@name QueryRole
@summary 查询用户信息
@description 查询用户信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryRole = (
  props: ImplRequest<QueryRoleRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Role/QueryRole",
    {
      result_code: "@string",
      result_info: "@string",
      data: { token: "@string" },
    },
    props,
  ) as ImplResponse<QueryRoleResponse>
export type AddRoleRequest = {
  role_name?: string
  role_description?: string
  resource_id_list?: string[]
}
export type AddRoleResponse = { result_code: string; result_info: string }
/**@name AddRole
@summary 新增角色信息
@description 新增角色信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddRole = (
  props: ImplRequest<AddRoleRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Role/AddRole",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<AddRoleResponse>
export type UpdateRoleRequest = {
  role_id?: string
  role_name?: string
  role_description?: string
  resource_id_list?: string[]
}
export type UpdateRoleResponse = { result_code: string; result_info: string }
/**@name UpdateRole
@summary 更新角色信息
@description 更新角色信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateRole = (
  props: ImplRequest<UpdateRoleRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Role/UpdateRole",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateRoleResponse>
export type DeleteRoleRequest = { role_id?: string }
export type DeleteRoleResponse = { result_code: string; result_info: string }
/**@name DeleteRole
@summary 删除角色信息
@description 删除角色信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteRole = (
  props: ImplRequest<DeleteRoleRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Role/DeleteRole",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<DeleteRoleResponse>
