/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type CreateMediaJobsRequest = {
  options?: {
    /**@name tag
Optional tag for the job*/
    tag?: string
    /**@name input
Input for the job*/
    input?: {
      /**@name object
The input object*/
      object?: string
    }
    /**@name operation
Operation to perform*/
    operation?: {
      /**@name output
Output of the operation*/
      output?: {
        /**@name object
The output object*/
        object?: string
      }
      /**@name template_id
ID of the template to use*/
      template_id?: string
    }
    /**@name queue_type
Type of queue*/
    queue_type?: string
  }
}
export type CreateMediaJobsResponse = {
  result_code: string
  result_info: string
  result: {
    /**@name jobs_detail
Details of the jobs created*/
    jobs_detail: {
      /**@name code
Code indicating the result of the job*/
      code: string
      /**@name message
Message providing additional information*/
      message: string
      /**@name job_id
Unique identifier for the job*/
      job_id: string
      /**@name tag
Tag associated with the job*/
      tag: string
      /**@name progress
Progress of the job*/
      progress: string
      /**@name state
Current state of the job*/
      state: string
      /**@name creation_time
Time when the job was created*/
      creation_time: string
      /**@name start_time
Time when the job started*/
      start_time: string
      /**@name end_time
Time when the job ended*/
      end_time: string
      /**@name queue_id
Identifier for the queue*/
      queue_id: string
      /**@name input
Input for the job*/
      input: {
        /**@name object
The input object*/
        object: string
      }
      /**@name operation
Operation performed on the job*/
      operation: {
        /**@name output
Output of the operation*/
        output: {
          /**@name object
The output object*/
          object: string
        }
        /**@name template_id
ID of the template to use*/
        template_id: string
      }
    }
  }
}
/**@name CreateMediaJobs
================ 视频转码 ================
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CreateMediaJobs = (
  props: ImplRequest<CreateMediaJobsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.VideoTranscodeSvr/CreateMediaJobs",
    {
      result_code: "@string",
      result_info: "@string",
      result: {
        jobs_detail: {
          code: "Code indicating the result of the job",
          message: "Message providing additional information",
          job_id: "Unique identifier for the job",
          tag: "Tag associated with the job",
          progress: "Progress of the job",
          state: "Current state of the job",
          creation_time: "@datetime",
          start_time: "@datetime",
          end_time: "@datetime",
          queue_id: "Identifier for the queue",
          input: { object: "The input object" },
          operation: {
            output: { object: "The output object" },
            template_id: "ID of the template to use",
          },
        },
      },
    },
    props,
  ) as ImplResponse<CreateMediaJobsResponse>
export type DescribeMediaJobsRequest = { job_id?: string }
export type DescribeMediaJobsResponse = {
  result_code: string
  result_info: string
  result: {
    /**@name jobs_detail
Details of the jobs created*/
    jobs_detail: {
      /**@name code
Code indicating the result of the job*/
      code: string
      /**@name message
Message providing additional information*/
      message: string
      /**@name job_id
Unique identifier for the job*/
      job_id: string
      /**@name tag
Tag associated with the job*/
      tag: string
      /**@name progress
Progress of the job*/
      progress: string
      /**@name state
Current state of the job*/
      state: string
      /**@name creation_time
Time when the job was created*/
      creation_time: string
      /**@name start_time
Time when the job started*/
      start_time: string
      /**@name end_time
Time when the job ended*/
      end_time: string
      /**@name queue_id
Identifier for the queue*/
      queue_id: string
      /**@name input
Input for the job*/
      input: {
        /**@name object
The input object*/
        object: string
      }
      /**@name operation
Operation performed on the job*/
      operation: {
        /**@name output
Output of the operation*/
        output: {
          /**@name object
The output object*/
          object: string
        }
        /**@name template_id
ID of the template to use*/
        template_id: string
      }
    }
  }
}
/**@name DescribeMediaJobs
DescribeMediaJobs 查询视频转码任务
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DescribeMediaJobs = (
  props: ImplRequest<DescribeMediaJobsRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.VideoTranscodeSvr/DescribeMediaJobs",
    {
      result_code: "@string",
      result_info: "@string",
      result: {
        jobs_detail: {
          code: "Code indicating the result of the job",
          message: "Message providing additional information",
          job_id: "Unique identifier for the job",
          tag: "Tag associated with the job",
          progress: "Progress of the job",
          state: "Current state of the job",
          creation_time: "@datetime",
          start_time: "@datetime",
          end_time: "@datetime",
          queue_id: "Identifier for the queue",
          input: { object: "The input object" },
          operation: {
            output: { object: "The output object" },
            template_id: "ID of the template to use",
          },
        },
      },
    },
    props,
  ) as ImplResponse<DescribeMediaJobsResponse>
export type GetMediaInfoRequest = { object?: string }
export type GetMediaInfoResponse = {
  result_code: string
  result_info: string
  result: {
    /**@name media_info
Corresponds to MediaInfo*/
    media_info: {
      /**@name stream
Corresponds to Stream*/
      stream: {
        /**@name video
A list of Video structures*/
        video: {
          /**@name codec_long_name
Corresponds to CodecLongName*/
          codec_long_name: string
          /**@name codec_name
Corresponds to CodecName*/
          codec_name: string
        }[]
      }
    }
  }
}
/**@name GetMediaInfo
GetMediaInfo 获取视频信息
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetMediaInfo = (
  props: ImplRequest<GetMediaInfoRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.meta_feedback_svr.VideoTranscodeSvr/GetMediaInfo",
    {
      result_code: "@string",
      result_info: "@string",
      result: {
        media_info: {
          stream: {
            "video|1-2": [
              {
                codec_long_name: "Corresponds to CodecLongName",
                codec_name: "Corresponds to CodecName",
              },
            ],
          },
        },
      },
    },
    props,
  ) as ImplResponse<GetMediaInfoResponse>
