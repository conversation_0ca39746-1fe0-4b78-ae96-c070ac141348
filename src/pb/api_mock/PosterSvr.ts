/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type NewPosterRequest = {
  poster_info?: {
    poster_id?: string
    poster_name?: string
    /**@name poster_url
海报地址*/
    poster_url?: string
    /**@name thumbnail
缩略图地址*/
    thumbnail?: string
    /**@name suffix
海报格式*/
    suffix?: string
    /**@name width
海报宽度*/
    width?: string
    /**@name height
海报长度*/
    height?: string
    /**@name file_size
海报文件大小*/
    file_size?: string
    /**@name audit_status
audit_status 审核状态
- 0	UNKNOW	未知状态
- 1	NOTINTAUDIT	未发起审核
- 2	SECURITY_INAUDIT	信安审核中
- 3	INAUDIT	审核中
- 4	PASS	审核通过
- 101	EDIT_NOT_IN_AUDIT	修改内容待提交审核
- 102	SECURITY_EDIT_INAUDIT	信安审核中
- 103	EDIT_INAUDIT	
- 104	EDIT_PASS	审核通过
- -1	SECURITY_REJECT	信安审核失败
- -2	REJECT	审核未通过
- -3	EDIT_SECURITY_REJECT	修改内容安审未通过
- -4	EDIT_REJECT	修改内容上线审核未通过*/
    audit_status?: //未知状态
    | 0 //未发起审核
      | 1 //信安审核中
      | 2 //审核中
      | 3 //审核通过
      | 4 //修改内容待提交审核
      | 101 //信安审核中
      | 102
      | 103 //审核通过
      | 104 //信安审核失败
      | -1 //审核未通过
      | -2 //修改内容安审未通过
      | -3 //修改内容上线审核未通过
      | -4
    /**@name audit_failed_msg
审核失败原因*/
    audit_failed_msg?: string
    /**@name publish_status
publish_status 发布状态
- 0	PUBLISH_UNKNOW	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
    /**@name create_time
create_time 创建时间*/
    create_time?: string
    /**@name creator
creator 创建人*/
    creator?: string
    /**@name publish_time
publish_time 最后的发布时间*/
    publish_time?: string
    /**@name publisher
publisher 最后的发布人*/
    publisher?: string
    /**@name label_list
海报绑定的标签信息*/
    label_list?: { id?: string; name?: string }[]
    terminal_type?: string
  }
}
export type NewPosterResponse = { result_code: string; result_info: string }
/**@name NewPoster
@summary 新建一个海报接口
@description 新建一个海报接口
@alias /api/content_manage.PosterSvr/NewPoster
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const NewPoster = (
  props: ImplRequest<NewPosterRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/NewPoster",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<NewPosterResponse>
export type PosterListRequest = {
  condition?: {
    /**@name poster_id
查询条件
poster_id*/
    poster_id?: string[]
    /**@name poster_name
poster_name 海报名称*/
    poster_name?: string
    /**@name audit_status
audit_status 审核状态
- 0	UNKNOW	未知状态
- 1	NOTINTAUDIT	未发起审核
- 2	SECURITY_INAUDIT	信安审核中
- 3	INAUDIT	审核中
- 4	PASS	审核通过
- 101	EDIT_NOT_IN_AUDIT	修改内容待提交审核
- 102	SECURITY_EDIT_INAUDIT	信安审核中
- 103	EDIT_INAUDIT	
- 104	EDIT_PASS	审核通过
- -1	SECURITY_REJECT	信安审核失败
- -2	REJECT	审核未通过
- -3	EDIT_SECURITY_REJECT	修改内容安审未通过
- -4	EDIT_REJECT	修改内容上线审核未通过*/
    audit_status?: //未知状态
    (| 0 //未发起审核
      | 1 //信安审核中
      | 2 //审核中
      | 3 //审核通过
      | 4 //修改内容待提交审核
      | 101 //信安审核中
      | 102
      | 103 //审核通过
      | 104 //信安审核失败
      | -1 //审核未通过
      | -2 //修改内容安审未通过
      | -3 //修改内容上线审核未通过
      | -4
    )[]
    /**@name publish_status
publish_status 发布状态
- 0	PUBLISH_UNKNOW	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
    /**@name group_id
group_id 根据指定分组查询,@all代表全部,@ungroup代表未分组*/
    group_id?: string
  }
  page_size?: number
  page_num?: number
  /**@name desc
创建时间排序*/
  desc?: boolean
}
export type PosterListResponse = {
  result_code: string
  result_info: string
  data: {
    count: number
    poster_list: {
      poster_id: string
      poster_name: string
      /**@name poster_url
海报地址*/
      poster_url: string
      /**@name thumbnail
缩略图地址*/
      thumbnail: string
      /**@name suffix
海报格式*/
      suffix: string
      /**@name width
海报宽度*/
      width: string
      /**@name height
海报长度*/
      height: string
      /**@name file_size
海报文件大小*/
      file_size: string
      /**@name audit_status
audit_status 审核状态
- 0	UNKNOW	未知状态
- 1	NOTINTAUDIT	未发起审核
- 2	SECURITY_INAUDIT	信安审核中
- 3	INAUDIT	审核中
- 4	PASS	审核通过
- 101	EDIT_NOT_IN_AUDIT	修改内容待提交审核
- 102	SECURITY_EDIT_INAUDIT	信安审核中
- 103	EDIT_INAUDIT	
- 104	EDIT_PASS	审核通过
- -1	SECURITY_REJECT	信安审核失败
- -2	REJECT	审核未通过
- -3	EDIT_SECURITY_REJECT	修改内容安审未通过
- -4	EDIT_REJECT	修改内容上线审核未通过*/
      audit_status: //未知状态
      | 0 //未发起审核
        | 1 //信安审核中
        | 2 //审核中
        | 3 //审核通过
        | 4 //修改内容待提交审核
        | 101 //信安审核中
        | 102
        | 103 //审核通过
        | 104 //信安审核失败
        | -1 //审核未通过
        | -2 //修改内容安审未通过
        | -3 //修改内容上线审核未通过
        | -4
      /**@name audit_failed_msg
审核失败原因*/
      audit_failed_msg: string
      /**@name publish_status
publish_status 发布状态
- 0	PUBLISH_UNKNOW	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
      publish_status: //未知状态
      | 0 //草稿
        | 1 //审核中
        | 2 //待发布
        | 3 //已发布
        | 4 //已下线
        | 6
      /**@name create_time
create_time 创建时间*/
      create_time: string
      /**@name creator
creator 创建人*/
      creator: string
      /**@name publish_time
publish_time 最后的发布时间*/
      publish_time: string
      /**@name publisher
publisher 最后的发布人*/
      publisher: string
      /**@name label_list
海报绑定的标签信息*/
      label_list: { id: string; name: string }[]
      terminal_type: string
    }[]
  }
}
/**@name PosterList
@summary 查询海报信息列表
@description 查询海报信息列表
@alias /api/content_manage.PosterSvr/PosterList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const PosterList = (
  props: ImplRequest<PosterListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/PosterList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        count: "@integer(1, 100)",
        "poster_list|1-2": [
          {
            poster_id: "@string",
            poster_name: "@string",
            poster_url: "海报地址",
            thumbnail: "缩略图地址",
            suffix: "海报格式",
            width: "海报宽度",
            height: "海报长度",
            file_size: "海报文件大小",
            audit_status: 0,
            audit_failed_msg: "审核失败原因",
            publish_status: 0,
            create_time: "@datetime",
            creator: "creator 创建人",
            publish_time: "@datetime",
            publisher: "publisher 最后的发布人",
            "label_list|1-2": [{ id: "@string", name: "@string" }],
            terminal_type: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<PosterListResponse>
export type UpdatePosterRequest = {
  poster_info?: {
    poster_id?: string
    poster_name?: string
    /**@name poster_url
海报地址*/
    poster_url?: string
    /**@name thumbnail
缩略图地址*/
    thumbnail?: string
    /**@name suffix
海报格式*/
    suffix?: string
    /**@name width
海报宽度*/
    width?: string
    /**@name height
海报长度*/
    height?: string
    /**@name file_size
海报文件大小*/
    file_size?: string
    /**@name audit_status
audit_status 审核状态
- 0	UNKNOW	未知状态
- 1	NOTINTAUDIT	未发起审核
- 2	SECURITY_INAUDIT	信安审核中
- 3	INAUDIT	审核中
- 4	PASS	审核通过
- 101	EDIT_NOT_IN_AUDIT	修改内容待提交审核
- 102	SECURITY_EDIT_INAUDIT	信安审核中
- 103	EDIT_INAUDIT	
- 104	EDIT_PASS	审核通过
- -1	SECURITY_REJECT	信安审核失败
- -2	REJECT	审核未通过
- -3	EDIT_SECURITY_REJECT	修改内容安审未通过
- -4	EDIT_REJECT	修改内容上线审核未通过*/
    audit_status?: //未知状态
    | 0 //未发起审核
      | 1 //信安审核中
      | 2 //审核中
      | 3 //审核通过
      | 4 //修改内容待提交审核
      | 101 //信安审核中
      | 102
      | 103 //审核通过
      | 104 //信安审核失败
      | -1 //审核未通过
      | -2 //修改内容安审未通过
      | -3 //修改内容上线审核未通过
      | -4
    /**@name audit_failed_msg
审核失败原因*/
    audit_failed_msg?: string
    /**@name publish_status
publish_status 发布状态
- 0	PUBLISH_UNKNOW	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
    /**@name create_time
create_time 创建时间*/
    create_time?: string
    /**@name creator
creator 创建人*/
    creator?: string
    /**@name publish_time
publish_time 最后的发布时间*/
    publish_time?: string
    /**@name publisher
publisher 最后的发布人*/
    publisher?: string
    /**@name label_list
海报绑定的标签信息*/
    label_list?: { id?: string; name?: string }[]
    terminal_type?: string
  }
}
export type UpdatePosterResponse = { result_code: string; result_info: string }
/**@name UpdatePoster
@summary 更新海报信息
@description 更新海报信息
@alias /api/content_manage.PosterSvr/UpdatePoster
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdatePoster = (
  props: ImplRequest<UpdatePosterRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/UpdatePoster",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdatePosterResponse>
export type PosterAuditRequest = { poster_id_list?: string[] }
export type PosterAuditResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name poster_fail_list
失败列表*/
    poster_fail_list: { poster_id: string; poster_name: string }[]
  }
}
/**@name PosterAudit
@summary 更新海报信息
@description 更新海报信息
@alias /api/content_manage.PosterSvr/PosterAudit
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const PosterAudit = (
  props: ImplRequest<PosterAuditRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/PosterAudit",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "poster_fail_list|1-2": [
          { poster_id: "@string", poster_name: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<PosterAuditResponse>
export type PosterPublishRequest = { poster_id_list?: string[] }
export type PosterPublishResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name poster_fail_list
失败列表*/
    poster_fail_list: { poster_id: string; poster_name: string }[]
  }
}
/**@name PosterPublish
@summary 更新海报信息
@description 更新海报信息
@alias /api/content_manage.PosterSvr/PosterPublish
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const PosterPublish = (
  props: ImplRequest<PosterPublishRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/PosterPublish",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "poster_fail_list|1-2": [
          { poster_id: "@string", poster_name: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<PosterPublishResponse>
export type PosterDeleteRequest = { poster_id_list?: string[] }
export type PosterDeleteResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name poster_fail_list
失败列表*/
    poster_fail_list: { poster_id: string; poster_name: string }[]
  }
}
/**@name PosterDelete
@summary 删除海报信息
@description 删除海报信息
@alias /api/content_manage.PosterSvr/PosterDelete
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const PosterDelete = (
  props: ImplRequest<PosterDeleteRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/PosterDelete",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "poster_fail_list|1-2": [
          { poster_id: "@string", poster_name: "@string" },
        ],
      },
    },
    props,
  ) as ImplResponse<PosterDeleteResponse>
export type PosterCopyRequest = { poster_id?: string[] }
export type PosterCopyResponse = {
  result_code: string
  result_info: string
  data: { new_poster_id: string }
}
/**@name PosterCopy
@summary 复制海报信息
@description 复制海报信息
@alias /api/content_manage.PosterSvr/PosterCopy
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const PosterCopy = (
  props: ImplRequest<PosterCopyRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/PosterCopy",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        count: "@integer(1, 100)",
        "poster_list|1-2": [
          {
            poster_id: "@string",
            poster_name: "@string",
            poster_url: "海报地址",
            thumbnail: "缩略图地址",
            suffix: "海报格式",
            width: "海报宽度",
            height: "海报长度",
            file_size: "海报文件大小",
            audit_status: 0,
            audit_failed_msg: "审核失败原因",
            publish_status: 0,
            create_time: "@datetime",
            creator: "creator 创建人",
            publish_time: "@datetime",
            publisher: "publisher 最后的发布人",
            "label_list|1-2": [{ id: "@string", name: "@string" }],
            terminal_type: "@string",
          },
        ],
      },
    },
    props,
  ) as ImplResponse<PosterCopyResponse>
export type SecurityCallBackRequest = {
  content_check_id?: string
  app_type?: string
  app_id?: string
  content_list?: { content_type?: string; content_id?: string }[]
  result_code?: string
  result_msg?: string
  creator_uid?: string
}
export type SecurityCallBackResponse = {
  result_code: string
  result_info: string
}
/**@name SecurityCallBack
@summary 原生页信安审核回调
@description 原生页信安审核回调
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SecurityCallBack = (
  props: ImplRequest<SecurityCallBackRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.content_manage.PosterSvr/SecurityCallBack",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<SecurityCallBackResponse>
