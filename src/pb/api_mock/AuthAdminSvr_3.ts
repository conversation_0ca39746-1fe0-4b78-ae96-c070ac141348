/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type SignAgreementRequest = {
  /**@name agreement_id
秒播产品协议:ADMUSE_AGREEMENT*/
  agreement_id?: string
}
export type SignAgreementResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name SignAgreement
@summary 签署协议
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const SignAgreement = (
  props: ImplRequest<SignAgreementRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Agreement/SignAgreement",
    { result_code: "返回结果代码", result_info: "返回结果信息" },
    props,
  ) as ImplResponse<SignAgreementResponse>
export type CheckAgreementStatusRequest = {
  /**@name agreement_id
秒播产品协议:ADMUSE_AGREEMENT*/
  agreement_id?: string
}
export type CheckAgreementStatusResponse = {
  /**@name is_signed
是否签署*/
  is_signed: boolean
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name CheckAgreementStatus
@summary 查询用户是否签署协议
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CheckAgreementStatus = (
  props: ImplRequest<CheckAgreementStatusRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Agreement/CheckAgreementStatus",
    {
      is_signed: "@boolean",
      result_code: "返回结果代码",
      result_info: "返回结果信息",
    },
    props,
  ) as ImplResponse<CheckAgreementStatusResponse>
