/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type NewSessionRequest = {
  /**@name session_type
暂时没用到*/
  session_type?: string
}
export type NewSessionResponse = {
  result_code: string
  result_info: string
  data: { session_id: string }
}
/**@name NewSession
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const NewSession = (
  props: ImplRequest<NewSessionRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.client_copilot.Service/NewSession",
    {
      result_code: "@string",
      result_info: "@string",
      data: { session_id: "@string" },
    },
    props,
  ) as ImplResponse<NewSessionResponse>
export type CheckSessionRequest = { session_id?: string }
export type CheckSessionResponse = {
  result_code: string
  result_info: string
  data: { session_id: string; session_data: string }
}
/**@name CheckSession
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const CheckSession = (
  props: ImplRequest<CheckSessionRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.client_copilot.Service/CheckSession",
    {
      result_code: "@string",
      result_info: "@string",
      data: { session_id: "@string", session_data: "@string" },
    },
    props,
  ) as ImplResponse<CheckSessionResponse>
export type GetConfigRequest = { config_id?: string }
export type GetConfigResponse = {
  result_code: string
  result_info: string
  data: { config_id: string; config_data: string }
}
/**@name GetConfig
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetConfig = (
  props: ImplRequest<GetConfigRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.tencent.client_copilot.Service/GetConfig",
    {
      result_code: "@string",
      result_info: "@string",
      data: { config_id: "@string", config_data: "@string" },
    },
    props,
  ) as ImplResponse<GetConfigResponse>
