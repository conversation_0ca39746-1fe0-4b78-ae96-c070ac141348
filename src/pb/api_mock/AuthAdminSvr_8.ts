/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import Mock from 'mockjs';
import {ImplRespond,type ImplServer} from '../config';
const ImplMockServer = (URL: string, MockStruct: unknown, Req: unknown): unknown => {
  const result = ImplRespond(Mock.mock(MockStruct));
  if (typeof window.console?.groupCollapsed === 'function'){
    console.groupCollapsed('[PbMock] Mock Request', URL);
    console.log('URL', URL);
    console.log('Request', Req);
    console.trace('Response', result);
    console.groupEnd();
  }else{
    console.log('[PbMock] Mock Request', URL);
    console.log('Request', Req);
    console.warn('Response', result);
  }
  return result;
};
export type AddResourceRequest = {
  /**@name system
必填*/
  system?: string
  /**@name resourceType
1 菜单 2 功能 必填*/
  resourceType?: string
  /**@name resourceName
必填*/
  resourceName?: string
  /**@name resourceCode
必填*/
  resourceCode?: string
  /**@name url
resourceType=1 必填*/
  url?: string
  /**@name show
必填*/
  show?: string
  /**@name apiList
api列表*/
  apiList?: string[]
  /**@name parentId
必填 一级菜单传 -1*/
  parentId?: string
  priority?: number
}
export type AddResourceResponse = { result_code: string; result_info: string }
/**@name AddResource
@summary 增加resource接口
@description 增加rsource接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AddResource = (
  props: ImplRequest<AddResourceRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/AddResource",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<AddResourceResponse>
export type DeleteResourceRequest = { id?: string }
export type DeleteResourceResponse = {
  result_code: string
  result_info: string
}
/**@name DeleteResource
@summary 删除resource接口
@description 删除rsource接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const DeleteResource = (
  props: ImplRequest<DeleteResourceRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/DeleteResource",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<DeleteResourceResponse>
export type UpdateResourceRequest = {
  /**@name resourceName
必填*/
  resourceName?: string
  url?: string
  /**@name show
必填*/
  show?: string
  /**@name apiList
api列表 选填*/
  apiList?: string[]
  /**@name parentId
必填 一级菜单传 -1*/
  parentId?: string
  /**@name id
必填*/
  id?: string
  /**@name resourceType
资源类型*/
  resourceType?: string
  priority?: number
}
export type UpdateResourceResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateResource
@summary 修改resource接口
@description 修改rsource接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const UpdateResource = (
  props: ImplRequest<UpdateResourceRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/UpdateResource",
    { result_code: "@string", result_info: "@string" },
    props,
  ) as ImplResponse<UpdateResourceResponse>
export type QueryResourceRequest = { system?: string }
export type QueryResourceResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name system
必填*/
    system: string
    /**@name resourceType
1 菜单 2 功能 必填*/
    resourceType: string
    /**@name resourceName
必填*/
    resourceName: string
    /**@name resourceCode
必填*/
    resourceCode: string
    /**@name url
resourceType=1 必填*/
    url: string
    /**@name show
必填*/
    show: string
    /**@name apiList
api列表*/
    apiList: string[]
    /**@name parentId
必填 一级菜单传 -1*/
    parentId: string
    childList: QueryResourceResponse["data"][0][]
    createUser: string
    updateUser: string
    createTime: string
    updateTime: string
    id: string
    priority: number
  }[]
}
/**@name QueryResource
@summary 查询resource接口
@description 查询rsource接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const QueryResource = (
  props: ImplRequest<QueryResourceRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/QueryResource",
    {
      result_code: "@string",
      result_info: "@string",
      "data|1-2": [
        {
          system: "必填",
          resourceType: "1 菜单 2 功能 必填",
          resourceName: "必填",
          resourceCode: "必填",
          url: "resourceType=1 必填",
          show: "必填",
          "apiList|1-2": ["api列表"],
          parentId: "必填 一级菜单传 -1",
          "childList|1-2": [void 0],
          createUser: "@name",
          updateUser: "@name",
          createTime: "@datetime",
          updateTime: "@datetime",
          id: "@string",
          priority: "@integer(1, 100)",
        },
      ],
    },
    props,
  ) as ImplResponse<QueryResourceResponse>
export type AuthCheckRequest = {
  url?: string
  system?: string
  resourceIds?: string[]
}
export type AuthCheckResponse = {
  result_code: string
  result_info: string
  data: { hasAuth: boolean }
}
/**@name AuthCheck
@summary 权限确认接口
@description 权限确认接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const AuthCheck = (
  props: ImplRequest<AuthCheckRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/AuthCheck",
    {
      result_code: "@string",
      result_info: "@string",
      data: { hasAuth: "@boolean" },
    },
    props,
  ) as ImplResponse<AuthCheckResponse>
export type GetMenuListRequest = { resourceIds?: string[]; system?: string }
export type GetMenuListResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name system
必填*/
    system: string
    /**@name resourceType
1 菜单 2 功能 必填*/
    resourceType: string
    /**@name resourceName
必填*/
    resourceName: string
    /**@name resourceCode
必填*/
    resourceCode: string
    /**@name url
resourceType=1 必填*/
    url: string
    /**@name show
必填*/
    show: string
    /**@name apiList
api列表*/
    apiList: string[]
    /**@name parentId
必填 一级菜单传 -1*/
    parentId: string
    childList: GetMenuListResponse["data"][0][]
    createUser: string
    updateUser: string
    createTime: string
    updateTime: string
    id: string
    priority: number
  }[]
}
/**@name GetMenuList
@summary 获取用户菜单接口
@description 获取用户菜单接口
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetMenuList = (
  props: ImplRequest<GetMenuListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/GetMenuList",
    {
      result_code: "@string",
      result_info: "@string",
      "data|1-2": [
        {
          system: "必填",
          resourceType: "1 菜单 2 功能 必填",
          resourceName: "必填",
          resourceCode: "必填",
          url: "resourceType=1 必填",
          show: "必填",
          "apiList|1-2": ["api列表"],
          parentId: "必填 一级菜单传 -1",
          "childList|1-2": [void 0],
          createUser: "@name",
          updateUser: "@name",
          createTime: "@datetime",
          updateTime: "@datetime",
          id: "@string",
          priority: "@integer(1, 100)",
        },
      ],
    },
    props,
  ) as ImplResponse<GetMenuListResponse>
export type GetResourceListRequest = { sysCode?: string }
export type GetResourceListResponse = {
  result_code: string
  result_info: string
  data: {
    allNodes: string[]
    componentNodes: string[]
    excludeComponentNodes: string[]
    productOperateNodes: string[]
    tmpOperateNodes: string[]
    financialNodes: string[]
  }
}
/**@name GetResourceList
@deprecated 【PbMock】此方法为mock请求，标记为弃用只是为了提醒开发避免带入现网环境，正常使用mock请求时请无视弃用警告。
如果需要真实请求接口，请将import语句的api_mock改为api。*/
export const GetResourceList = (
  props: ImplRequest<GetResourceListRequest>,
  ..._options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplMockServer(
    "/trpc.dip.admin.Resource/GetResourceList",
    {
      result_code: "@string",
      result_info: "@string",
      data: {
        "allNodes|1-2": ["@string"],
        "componentNodes|1-2": ["@string"],
        "excludeComponentNodes|1-2": ["@string"],
        "productOperateNodes|1-2": ["@string"],
        "tmpOperateNodes|1-2": ["@string"],
        "financialNodes|1-2": ["@string"],
      },
    },
    props,
  ) as ImplResponse<GetResourceListResponse>
