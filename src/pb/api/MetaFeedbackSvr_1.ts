/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type CreateMediaJobsRequest = {
  options?: {
    /**@name tag
Optional tag for the job*/
    tag?: string
    /**@name input
Input for the job*/
    input?: {
      /**@name object
The input object*/
      object?: string
    }
    /**@name operation
Operation to perform*/
    operation?: {
      /**@name output
Output of the operation*/
      output?: {
        /**@name object
The output object*/
        object?: string
      }
      /**@name template_id
ID of the template to use*/
      template_id?: string
    }
    /**@name queue_type
Type of queue*/
    queue_type?: string
  }
}
export type CreateMediaJobsResponse = {
  result_code: string
  result_info: string
  result: {
    /**@name jobs_detail
Details of the jobs created*/
    jobs_detail: {
      /**@name code
Code indicating the result of the job*/
      code: string
      /**@name message
Message providing additional information*/
      message: string
      /**@name job_id
Unique identifier for the job*/
      job_id: string
      /**@name tag
Tag associated with the job*/
      tag: string
      /**@name progress
Progress of the job*/
      progress: string
      /**@name state
Current state of the job*/
      state: string
      /**@name creation_time
Time when the job was created*/
      creation_time: string
      /**@name start_time
Time when the job started*/
      start_time: string
      /**@name end_time
Time when the job ended*/
      end_time: string
      /**@name queue_id
Identifier for the queue*/
      queue_id: string
      /**@name input
Input for the job*/
      input: {
        /**@name object
The input object*/
        object: string
      }
      /**@name operation
Operation performed on the job*/
      operation: {
        /**@name output
Output of the operation*/
        output: {
          /**@name object
The output object*/
          object: string
        }
        /**@name template_id
ID of the template to use*/
        template_id: string
      }
    }
  }
}
/**@name CreateMediaJobs
================ 视频转码 ================*/
export const CreateMediaJobs = (
  props: ImplRequest<CreateMediaJobsRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.VideoTranscodeSvr/CreateMediaJobs",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateMediaJobsResponse>
export type DescribeMediaJobsRequest = { job_id?: string }
export type DescribeMediaJobsResponse = {
  result_code: string
  result_info: string
  result: {
    /**@name jobs_detail
Details of the jobs created*/
    jobs_detail: {
      /**@name code
Code indicating the result of the job*/
      code: string
      /**@name message
Message providing additional information*/
      message: string
      /**@name job_id
Unique identifier for the job*/
      job_id: string
      /**@name tag
Tag associated with the job*/
      tag: string
      /**@name progress
Progress of the job*/
      progress: string
      /**@name state
Current state of the job*/
      state: string
      /**@name creation_time
Time when the job was created*/
      creation_time: string
      /**@name start_time
Time when the job started*/
      start_time: string
      /**@name end_time
Time when the job ended*/
      end_time: string
      /**@name queue_id
Identifier for the queue*/
      queue_id: string
      /**@name input
Input for the job*/
      input: {
        /**@name object
The input object*/
        object: string
      }
      /**@name operation
Operation performed on the job*/
      operation: {
        /**@name output
Output of the operation*/
        output: {
          /**@name object
The output object*/
          object: string
        }
        /**@name template_id
ID of the template to use*/
        template_id: string
      }
    }
  }
}
/**@name DescribeMediaJobs
DescribeMediaJobs 查询视频转码任务*/
export const DescribeMediaJobs = (
  props: ImplRequest<DescribeMediaJobsRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.VideoTranscodeSvr/DescribeMediaJobs",
    null,
    props,
    ...options,
  ) as ImplResponse<DescribeMediaJobsResponse>
export type GetMediaInfoRequest = { object?: string }
export type GetMediaInfoResponse = {
  result_code: string
  result_info: string
  result: {
    /**@name media_info
Corresponds to MediaInfo*/
    media_info: {
      /**@name stream
Corresponds to Stream*/
      stream: {
        /**@name video
A list of Video structures*/
        video: {
          /**@name codec_long_name
Corresponds to CodecLongName*/
          codec_long_name: string
          /**@name codec_name
Corresponds to CodecName*/
          codec_name: string
        }[]
      }
    }
  }
}
/**@name GetMediaInfo
GetMediaInfo 获取视频信息*/
export const GetMediaInfo = (
  props: ImplRequest<GetMediaInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.VideoTranscodeSvr/GetMediaInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<GetMediaInfoResponse>
