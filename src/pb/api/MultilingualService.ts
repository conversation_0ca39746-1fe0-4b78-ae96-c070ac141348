/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type TranslateRequest = {
  /**@name app_id
unsafe_str 应用id, 如avatar*/
  app_id?: string
  /**@name source_lang
unsafe_str 使用ISO-639-1标准，如en/zh/fr/ar/ja/ko/es/tr*/
  source_lang?: string
  /**@name source_seg
unsafe_str*/
  source_seg?: string
  /**@name target_lang
unsafe_str 使用ISO-639-1标准，如en/zh/fr/ar/ja/ko/es/tr*/
  target_lang?: string
  stream?: boolean
}
export type TranslateResponse = {
  /**@name code
tRPC数据校验模块 开启自动数据校验。限制传入参数只能为tsecstr默认安全类型*/
  code: number
  msg: string
  target_seg: string
}
export const Translate = (
  props: ImplRequest<TranslateRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.midas_oversea.intelli_app.MultilingualService/Translate",
    null,
    props,
    ...options,
  ) as ImplResponse<TranslateResponse>
