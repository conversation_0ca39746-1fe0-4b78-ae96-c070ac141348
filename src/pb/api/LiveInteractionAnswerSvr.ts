/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type CreateLiveAnswerRequest = {
  /**@name answer
答案
@description：
@example:*/
  answer?: string
  /**@name answer_type
话术类型
@description：
@example:
@enum:
1:弹幕
2:B端用户新增
3:暂停节点*/
  answer_type?: number
  /**@name target_answer_id
目标话术id
@description：
@example:*/
  target_answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type CreateLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id: string
}
/**@name CreateLiveAnswer
@summary CreateLiveAnswer
@description*/
export const CreateLiveAnswer = (
  props: ImplRequest<CreateLiveAnswerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/CreateLiveAnswer",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateLiveAnswerResponse>
export type DeleteLiveAnswerRequest = {
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type DeleteLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name DeleteLiveAnswer
@summary DeleteLiveAnswer
@description*/
export const DeleteLiveAnswer = (
  props: ImplRequest<DeleteLiveAnswerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/DeleteLiveAnswer",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteLiveAnswerResponse>
export type GetDefaultLiveAnswerListRequest = {
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name page_size
每页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
}
export type GetDefaultLiveAnswerListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name records
默认问答库内容
@description：
@example:*/
  records: {
    /**@name group_type
分组类型
@description：
@example:*/
    group_type: number
    /**@name group_id
分组ID
@description：
@example:*/
    group_id: string
    /**@name qa_content_id
问答库内容ID
@description：
@example:*/
    qa_content_id: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }[]
}
/**@name GetDefaultLiveAnswerList
@summary GetDefaultLiveAnswerList
@description*/
export const GetDefaultLiveAnswerList = (
  props: ImplRequest<GetDefaultLiveAnswerListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/GetDefaultLiveAnswerList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetDefaultLiveAnswerListResponse>
export type GetLiveAnswerListRequest = {
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name count
数量
@description：数量
@example:*/
  count?: number
}
export type GetLiveAnswerListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name has_next
是否还有数据
@description：
@example:*/
  has_next: number
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name records
直播互动最终话术
@description：
@example:*/
  records: {
    /**@name live_id
直播ID
@description：
@example:*/
    live_id: string
    /**@name answer_id
话术id
@description：
@example:*/
    answer_id: string
    /**@name answer_sort
话术主排序
@description：
@example:*/
    answer_sort: string
    /**@name answer_sub_sort
话术次排序
@description：
@example:*/
    answer_sub_sort: number
    /**@name danmu_id
直播弹幕ID
@description：
@example:*/
    danmu_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name answer_type
话术类型
@description：
@example:
@enum:
1:弹幕
2:B端用户新增
3:暂停节点*/
    answer_type: number
  }[]
}
/**@name GetLiveAnswerList
@summary GetLiveAnswerList
@description*/
export const GetLiveAnswerList = (
  props: ImplRequest<GetLiveAnswerListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/GetLiveAnswerList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetLiveAnswerListResponse>
export type GetLiveAnswerScriptListRequest = {
  /**@name count
数量
@description：数量
@example:*/
  count?: number
  /**@name node_id
直播节点ID
@description：
@example:*/
  node_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context?: string
  /**@name live_start_time
直播开始时间
@description：
@example:*/
  live_start_time?: string
}
export type GetLiveAnswerScriptListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name has_next
是否还有数据
@description：
@example:*/
  has_next: number
  /**@name context
前后端约定的上下文字符串
@description：该字段由服务端返回，客户端原封不动的带上上一次的context
@example:*/
  context: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
  /**@name records
直播互动最终话术
@description：
@example:*/
  records: {
    /**@name live_id
直播ID
@description：
@example:*/
    live_id: string
    /**@name answer_id
话术id
@description：
@example:*/
    answer_id: string
    /**@name answer_sort
话术主排序
@description：
@example:*/
    answer_sort: string
    /**@name answer_sub_sort
话术次排序
@description：
@example:*/
    answer_sub_sort: number
    /**@name danmu_id
直播弹幕ID
@description：
@example:*/
    danmu_id: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_nick
用户昵称
@description：
@example:*/
    user_nick: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name answer_type
话术类型
@description：
@example:
@enum:
1:弹幕
2:B端用户新增
3:暂停节点*/
    answer_type: number
  }[]
}
/**@name GetLiveAnswerScriptList
@summary GetLiveAnswerScriptList
@description*/
export const GetLiveAnswerScriptList = (
  props: ImplRequest<GetLiveAnswerScriptListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/GetLiveAnswerScriptList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetLiveAnswerScriptListResponse>
export type SaveDanmuToLiveAnswerRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name user_nick
用户昵称
@description：
@example:*/
  user_nick?: string
  /**@name insert_time
插入时间
@description：
@example:*/
  insert_time?: string
  /**@name answer
答案
@description：
@example:*/
  answer?: string
  /**@name question
问题
@description：
@example:*/
  question?: string
  /**@name danmu_id
直播弹幕ID
@description：
@example:*/
  danmu_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type SaveDanmuToLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SaveDanmuToLiveAnswer
@summary SaveDanmuToLiveAnswer
@description*/
export const SaveDanmuToLiveAnswer = (
  props: ImplRequest<SaveDanmuToLiveAnswerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/SaveDanmuToLiveAnswer",
    null,
    props,
    ...options,
  ) as ImplResponse<SaveDanmuToLiveAnswerResponse>
export type SetLiveAnswerStatusRequest = {
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name node_id
直播节点ID
@description：
@example:*/
  node_id?: string
  /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
  status?: number
}
export type SetLiveAnswerStatusResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SetLiveAnswerStatus
@summary SetLiveAnswerStatus
@description*/
export const SetLiveAnswerStatus = (
  props: ImplRequest<SetLiveAnswerStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/SetLiveAnswerStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<SetLiveAnswerStatusResponse>
export type SortLiveAnswerRequest = {
  /**@name target_answer_id
目标话术id
@description：
@example:*/
  target_answer_id?: string
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type SortLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name SortLiveAnswer
@summary SortLiveAnswer
@description*/
export const SortLiveAnswer = (
  props: ImplRequest<SortLiveAnswerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/SortLiveAnswer",
    null,
    props,
    ...options,
  ) as ImplResponse<SortLiveAnswerResponse>
export type UpdateLiveAnswerRequest = {
  /**@name answer_id
话术id
@description：
@example:*/
  answer_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name answer
答案
@description：
@example:*/
  answer?: string
}
export type UpdateLiveAnswerResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateLiveAnswer
@summary UpdateLiveAnswer
@description*/
export const UpdateLiveAnswer = (
  props: ImplRequest<UpdateLiveAnswerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.live_interaction_answer_manager.Service/UpdateLiveAnswer",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateLiveAnswerResponse>
