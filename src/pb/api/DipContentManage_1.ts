/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type ContentRegisterRequest = {
  content_id?: string
  content_name?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
}
export type ContentRegisterResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentRegister
@summary 商铺注册
@description 商铺注册
@alias /api/content_manage.ContentSvr/ContentRegister*/
export const ContentRegister = (
  props: ImplRequest<ContentRegisterRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.ContentInnerSvr/ContentRegister",
    null,
    props,
    ...options,
  ) as ImplResponse<ContentRegisterResponse>
export type ContentInfoModifyRequest = {
  content_id?: string
  content_name?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
}
export type ContentInfoModifyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name ContentInfoModify
@summary 商铺注册
@description 商铺注册
@alias /api/content_manage.ContentSvr/ContentInfoModify*/
export const ContentInfoModify = (
  props: ImplRequest<ContentInfoModifyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.ContentInnerSvr/ContentInfoModify",
    null,
    props,
    ...options,
  ) as ImplResponse<ContentInfoModifyResponse>
