/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type ContentPreviewRequest = {
  /**@name token
@description 内容id*/
  token?: string
}
export type ContentPreviewResponse = {}
/**@name ContentPreview
@summary 内容预览
@description 存储预览内容
@alias /api/page_content.ContentSvr/ContentPreview*/
export const ContentPreview = (
  props: ImplRequest<ContentPreviewRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.page_content.PreviewSvr/ContentPreview",
    null,
    props,
    ...options,
  ) as ImplResponse<ContentPreviewResponse>
