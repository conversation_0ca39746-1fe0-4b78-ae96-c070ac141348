/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type GenerateQRCodeRequest = {}
export type GenerateQRCodeResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
  /**@name token
生成二维码的token*/
  token: string
}
/**@name GenerateQRCode
@summary 生成二维码
@description 生成二维码*/
export const GenerateQRCode = (
  props: ImplRequest<GenerateQRCodeRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/GenerateQRCode",
    null,
    props,
    ...options,
  ) as ImplResponse<GenerateQRCodeResponse>
export type CheckQRCodeStatusRequest = {
  /**@name token
请求参数：二维码的token*/
  token?: string
}
export type CheckQRCodeStatusResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
  /**@name status
状态: 0:未知, 1:未扫码, 2:已扫码待确认, 3:扫码登陆完成, 4:二维码已失效*/
  status: number
}
/**@name CheckQRCodeStatus
@summary 查询二维码状态
@description 查询二维码状态*/
export const CheckQRCodeStatus = (
  props: ImplRequest<CheckQRCodeStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/CheckQRCodeStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckQRCodeStatusResponse>
export type RegisterAppRequest = {
  /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
  app_type?: string
  /**@name phone_number
手机号码*/
  phone_number?: string
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
  /**@name business_license_cos
营业执照COS地址*/
  business_license_cos?: string
  /**@name api_app_id
API应用ID*/
  api_app_id?: string
  /**@name api_secret
API密钥*/
  api_secret?: string
  /**@name token
达人注册扫码的token*/
  token?: string
}
export type RegisterAppResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_id
账号ID*/
    app_id: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name RegisterApp
@summary 注册应用
@description 注册达人应用*/
export const RegisterApp = (
  props: ImplRequest<RegisterAppRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/RegisterApp",
    null,
    props,
    ...options,
  ) as ImplResponse<RegisterAppResponse>
export type AccountListRequest = {
  /**@name nick_name
名称模糊搜索*/
  nick_name?: string
  /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
  status?: string
  /**@name page_num
页码*/
  page_num?: number
  /**@name page_size
每页大小*/
  page_size?: number
}
export type AccountListResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name count
账号数量*/
    count: number
    /**@name account_info_list
账号信息列表*/
    account_info_list: {
      /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
      app_type: string
      /**@name nick_name
昵称*/
      nick_name: string
      /**@name avatar_url
头像地址*/
      avatar_url: string
      /**@name role_ids
角色名称: admin:管理员, collaborator:协作者*/
      role_ids: string[]
      /**@name business_license_cos
营业执照COS地址*/
      business_license_cos: string
      /**@name api_app_id
API应用ID*/
      api_app_id: string
      /**@name api_secret
API密钥*/
      api_secret: string
      /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
      status: string
      /**@name app_id
账号ID*/
      app_id: string
      /**@name outer_id
账号ID*/
      outer_id: string
      /**@name subject_name
主体名称*/
      subject_name: string
      /**@name wx_id
微信ID*/
      wx_id: string
      /**@name qq_number
QQ号码*/
      qq_number: string
      /**@name phone_number
手机号码*/
      phone_number: string
      /**@name approve_id
审批ID*/
      approve_id: string
      /**@name comments
审核意见*/
      comments: string
    }[]
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name AccountList
@summary 查询账号列表
@description 根据昵称模糊搜索账号信息*/
export const AccountList = (
  props: ImplRequest<AccountListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/AccountList",
    null,
    props,
    ...options,
  ) as ImplResponse<AccountListResponse>
export type GetAccountInfoRequest = {
  /**@name app_id
账号ID*/
  app_id?: string
}
export type GetAccountInfoResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
    app_type: string
    /**@name nick_name
昵称*/
    nick_name: string
    /**@name avatar_url
头像地址*/
    avatar_url: string
    /**@name role_ids
角色名称: admin:管理员, collaborator:协作者*/
    role_ids: string[]
    /**@name business_license_cos
营业执照COS地址*/
    business_license_cos: string
    /**@name api_app_id
API应用ID*/
    api_app_id: string
    /**@name api_secret
API密钥*/
    api_secret: string
    /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
    status: string
    /**@name app_id
账号ID*/
    app_id: string
    /**@name outer_id
账号ID*/
    outer_id: string
    /**@name subject_name
主体名称*/
    subject_name: string
    /**@name wx_id
微信ID*/
    wx_id: string
    /**@name qq_number
QQ号码*/
    qq_number: string
    /**@name phone_number
手机号码*/
    phone_number: string
    /**@name approve_id
审批ID*/
    approve_id: string
    /**@name comments
审核意见*/
    comments: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetAccountInfo
@summary 获取账号信息
@description 根据账号ID获取账号详细信息*/
export const GetAccountInfo = (
  props: ImplRequest<GetAccountInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/GetAccountInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<GetAccountInfoResponse>
export type EditAccountInfoRequest = {
  /**@name app_id
账号ID*/
  app_id?: string
  /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
  app_type?: string
  /**@name phone_number
手机号码*/
  phone_number?: string
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
  /**@name business_license_cos
营业执照COS地址*/
  business_license_cos?: string
  /**@name api_app_id
API应用ID*/
  api_app_id?: string
  /**@name api_secret
API密钥*/
  api_secret?: string
  /**@name token
达人注册扫码的token*/
  token?: string
}
export type EditAccountInfoResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_id
账号ID*/
    app_id: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name EditAccountInfo
@summary 编辑账号信息
@description 根据提供的参数编辑账号详细信息*/
export const EditAccountInfo = (
  props: ImplRequest<EditAccountInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/EditAccountInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<EditAccountInfoResponse>
export type CheckIsRegisterRequest = {}
export type CheckIsRegisterResponse = {
  /**@name is_register
是否注册*/
  is_register: boolean
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name CheckIsRegister
@summary 检查是否注册*/
export const CheckIsRegister = (
  props: ImplRequest<CheckIsRegisterRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/CheckIsRegister",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckIsRegisterResponse>
export type SubmitApplyRequest = {
  /**@name invitation_code
邀请码*/
  invitation_code?: string
  /**@name phone_number
手机号码*/
  phone_number?: string
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
}
export type SubmitApplyResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name app_id
账号ID*/
    app_id: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name SubmitApply
@summary 提交申请*/
export const SubmitApply = (
  props: ImplRequest<SubmitApplyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/SubmitApply",
    null,
    props,
    ...options,
  ) as ImplResponse<SubmitApplyResponse>
export type CheckLoginOpenIdRequest = {
  /**@name wx_id
微信ID*/
  wx_id?: string
  /**@name qq_number
QQ号码*/
  qq_number?: string
}
export type CheckLoginOpenIdResponse = {
  /**@name result_code
0校验通过 非0校验不通过*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
export const CheckLoginOpenId = (
  props: ImplRequest<CheckLoginOpenIdRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/CheckLoginOpenId",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckLoginOpenIdResponse>
export type AppListRequest = {
  /**@name nick_name
名称模糊搜索*/
  nick_name?: string
  /**@name app_id
app_id模糊搜索*/
  app_id?: string
}
export type AppListResponse = {
  result_code: string
  result_info: string
  data: { appId: string; appName: string; picUrl: string }[]
}
/**@name AppList
@summary 查询账号列表
@description*/
export const AppList = (
  props: ImplRequest<AppListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Register/AppList",
    null,
    props,
    ...options,
  ) as ImplResponse<AppListResponse>
