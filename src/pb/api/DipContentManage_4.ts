/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type NewMetaLiveRequest = {
  content_id?: string
  content_name?: string
  content_type?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
}
export type NewMetaLiveResponse = { result_code: string; result_info: string }
/**@name NewMetaLive
@summary 新建一个数字人直播信息
@description 新建一个海报接口
@alias /api/content_manage.MetaLiveSvr/NewMetaLive*/
export const NewMetaLive = (
  props: ImplRequest<NewMetaLiveRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/NewMetaLive",
    null,
    props,
    ...options,
  ) as ImplResponse<NewMetaLiveResponse>
export type UpdateMetaLiveRequest = {
  content_id?: string
  content_name?: string
  content_type?: string
  template_id?: string
  create_user?: string
  modify_user?: string
  create_time?: number
  modify_time?: number
  terminal_id?: string[]
  is_check?: number
  extend_info?: { [key: string]: string }
}
export type UpdateMetaLiveResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateMetaLive
@summary 更新数字人直播信息
@description 更新海报信息
@alias /api/content_manage.MetaLiveSvr/UpdateMetaLive*/
export const UpdateMetaLive = (
  props: ImplRequest<UpdateMetaLiveRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/UpdateMetaLive",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateMetaLiveResponse>
export type MetaLiveListRequest = {
  meta_live_name_or_id?: string
  meta_live_status?: string
  /**@name content_type
content_type 内容类型 ppt_live script_live*/
  content_type?: string
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
}
export type MetaLiveListResponse = {
  result_code: string
  result_info: string
  data: {
    meta_live_list: {
      meta_live_id: string
      meta_live_name: string
      meta_live_poster: string
      /**@name content_type
content_type 内容类型 ppt_live script_live*/
      content_type: string
      /**@name meta_live_status
meta_live_status 直播状态  1页面未发布，2未开播，3直播中，4直播失败，5直播结束中*/
      meta_live_status: string
      create_user: string
      create_time: string
      modify_user: string
      modify_time: string
      meta_live_url: string
      /**@name barrage_crawl_status
0:未开始; 1:抓取中; 2:已下播；3:异常退出*/
      barrage_crawl_status: string
      /**@name last_start_live_time
直播开始时间*/
      last_start_live_time: string
    }[]
    count: string
  }
}
/**@name MetaLiveList
@summary 查询数字人直播信息列表
@description 查询数字人直播信息列表
@alias /api/content_manage.MetaLiveSvr/MetaLiveList*/
export const MetaLiveList = (
  props: ImplRequest<MetaLiveListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveList",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveListResponse>
export type MetaLiveDeleteRequest = { meta_live_id?: string }
export type MetaLiveDeleteResponse = {
  result_code: string
  result_info: string
}
/**@name MetaLiveDelete
@summary 删除数字人直播信息
@description 删除数字人直播信息
@alias /api/content_manage.MetaLiveSvr/MetaLiveDelete*/
export const MetaLiveDelete = (
  props: ImplRequest<MetaLiveDeleteRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveDelete",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveDeleteResponse>
export type MetaLiveCopyRequest = { meta_live_id?: string }
export type MetaLiveCopyResponse = { result_code: string; result_info: string }
/**@name MetaLiveCopy
@summary 复制数字人直播信息
@description 复制数字人直播信息
@alias /api/content_manage.MetaLiveSvr/MetaLiveCopy*/
export const MetaLiveCopy = (
  props: ImplRequest<MetaLiveCopyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveCopy",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveCopyResponse>
export type MetaLiveReleaseRequest = { meta_live_id?: string }
export type MetaLiveReleaseResponse = {
  result_code: string
  result_info: string
}
/**@name MetaLiveRelease
@summary 发布数字人直播信息
@description 发布数字人直播信息
@alias /api/content_manage.MetaLiveSvr/MetaLiveRelease*/
export const MetaLiveRelease = (
  props: ImplRequest<MetaLiveReleaseRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveRelease",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveReleaseResponse>
export type MetaLiveUrlQueryRequest = {
  /**@name content_id
@description 内容id*/
  content_id?: string
  /**@name type
@description url类型 preview:预览查看  release:实际内容地址 review:审批预览*/
  type?: string
  review_id?: string
  /**@name page_info_list
@description 2024.02.28 主动添加页面信息,后端保存并返回页面 token,for superfactory*/
  page_info_list?: {
    /**@name page_id
@description 页面id(创建时不传)*/
    page_id?: string
    /**@name page_name
@description 页面名*/
    page_name?: string
    /**@name data
@description 页面协议*/
    data?: string
    /**@name status
@description 页面状态
@example 1:有效 0:无效*/
    status?: number
    /**@name terminal_id
@description 终端信息*/
    terminal_id?: string
    /**@name page_order
@description 多页面标识*/
    page_order?: string
    /**@name poster_url
@description 页面封面*/
    poster_url?: string
  }[]
}
/**@name MetaLiveUrlQueryResponse
@description 活动地址查询返回*/
export type MetaLiveUrlQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name data
@description 结果数据*/
  data: {
    url_list: {
      /**@name page_url
@description 页面地址*/
      page_url: string
      /**@name terminal_id
@description 页面终端信息*/
      terminal_id: string
    }[]
    /**@name url_conf
链接有效配置*/
    url_conf: {
      /**@name effective_time
链接有效时间（单位s）  30*/
      effective_time: number
      /**@name effective_times
链接有效次数（） 3*/
      effective_times: number
    }
  }
  /**@name display_info
display_info 错误显示信息*/
  display_info: string
}
/**@name MetaLiveUrlQuery
@summary 内容地址查询
@description 查看内容地址
@alias /api/content_manage.MetaLiveSvr/MetaLiveUrlQuery*/
export const MetaLiveUrlQuery = (
  props: ImplRequest<MetaLiveUrlQueryRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveUrlQuery",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveUrlQueryResponse>
export type MetaLivePrePushRequest = {
  meta_live_id?: string
  /**@name check_list
检查项列表*/
  check_list?: string[]
  /**@name virtual_man_live_data
数字人列表*/
  virtual_man_live_data?: {
    /**@name platform_account_id
平台账号id*/
    platform_account_id?: string
    /**@name virtualman_key
数字人形象key*/
    virtualman_key?: string
    /**@name platform
所属平台*/
    platform?: string
  }[]
  /**@name tts_live_data
数字人列表*/
  tts_live_data?: {
    /**@name platform_account_id
平台账号id*/
    platform_account_id?: string
    /**@name voice_id
音色id*/
    voice_id?: string
    /**@name platform
所属平台*/
    platform?: string
  }[]
}
export type MetaLivePrePushResponse = {
  result_code: string
  result_info: string
  data: { check_result: { key: string; code: string; info: string }[] }
}
/**@name MetaLivePrePush
@summary 数字人推流的预检查
@description 数字人推流的预检查
@alias /api/content_manage.MetaLiveSvr/MetaLivePrePush*/
export const MetaLivePrePush = (
  props: ImplRequest<MetaLivePrePushRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLivePrePush",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLivePrePushResponse>
export type MetaLivePushRequest = {
  meta_live_id?: string
  /**@name dst_push_stream_url
推流地址*/
  dst_push_stream_url?: string
  /**@name dst_push_secret
推流秘钥*/
  dst_push_secret?: string
  /**@name is_crawl_barrage
是否爬取弹幕 "true" 开启爬取弹幕*/
  is_crawl_barrage?: string
  /**@name virtual_man_live_data
虚拟人直播数据*/
  virtual_man_live_data?: {
    /**@name platform_account_id
平台账号id*/
    platform_account_id?: string
    /**@name virtualman_key
数字人形象key*/
    virtualman_key?: string
    /**@name platform
所属平台*/
    platform?: string
  }[]
  /**@name is_local_living
是否本地开播 "true" 是本地开播*/
  is_local_living?: string
  /**@name watch_push_stream_url
监流地址, 推送一路流给watch_url，如果dst和watch相同，只推1路流*/
  watch_push_stream_url?: string
  /**@name watch_push_secret
监流秘钥*/
  watch_push_secret?: string
}
export type MetaLivePushResponse = {
  result_code: string
  result_info: string
  data: { url: string }
}
/**@name MetaLivePush
@summary 数字人推流
@description 数字人推流
@alias /api/content_manage.MetaLiveSvr/MetaLivePush*/
export const MetaLivePush = (
  props: ImplRequest<MetaLivePushRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLivePush",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLivePushResponse>
export type MetaLiveStopRequest = { meta_live_id?: string }
export type MetaLiveStopResponse = { result_code: string; result_info: string }
/**@name MetaLiveStop
@summary 数字人停止推流
@description 数字人停止推流
@alias /api/content_manage.MetaLiveSvr/MetaLiveStop*/
export const MetaLiveStop = (
  props: ImplRequest<MetaLiveStopRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveStop",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveStopResponse>
export type GetLoginQRCodeRequest = {
  meta_live_id?: string
  resource_id?: string
  features?: string
}
export type GetLoginQRCodeResponse = {
  result_code: string
  result_info: string
  data: { qr_code: string }
}
/**@name GetLoginQRCode
@summary 获取登录二维码
@description 获取登录二维码
@alias /api/content_manage.MetaLiveSvr/GetLoginQRCode*/
export const GetLoginQRCode = (
  props: ImplRequest<GetLoginQRCodeRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/GetLoginQRCode",
    null,
    props,
    ...options,
  ) as ImplResponse<GetLoginQRCodeResponse>
export type QueryQRCodeStatusRequest = { meta_live_id?: string }
export type QueryQRCodeStatusResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name status
- 0	UNKNOWN	
- 1	NOT_SCANNED	
- 2	SCANNED_PENDING_CONFIRMATION	
- 3	LOGGED_IN	
- 4	QR_CODE_EXPIRED*/
    status: 0 | 1 | 2 | 3 | 4
  }
}
/**@name QueryQRCodeStatus
@summary 查询二维码登录状态
@description 查询二维码登录状态
@alias /api/content_manage.MetaLiveSvr/QueryQRCodeStatus*/
export const QueryQRCodeStatus = (
  props: ImplRequest<QueryQRCodeStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/QueryQRCodeStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryQRCodeStatusResponse>
export type GetMetaLiveStatusRequest = { meta_live_id?: string }
export type GetMetaLiveStatusResponse = {
  result_code: string
  result_info: string
  data: { meta_live_status: string }
}
/**@name GetMetaLiveStatus
@summary 查询直播间的直播状态
@description 查询直播间的直播状态
@alias /api/content_manage.MetaLiveSvr/GetMetaLiveStatus*/
export const GetMetaLiveStatus = (
  props: ImplRequest<GetMetaLiveStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/GetMetaLiveStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<GetMetaLiveStatusResponse>
export type CheckBarrageStatusRequest = { meta_live_id?: string }
export type CheckBarrageStatusResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name check_barrage_status
1:成功 2:直播间状态校验失败 3:未知错误*/
    check_barrage_status: number
  }
}
/**@name CheckBarrageStatus
@summary 弹幕通路检测
@description CheckBarrageStatus
@alias /api/content_manage.MetaLiveSvr/CheckBarrageStatus*/
export const CheckBarrageStatus = (
  props: ImplRequest<CheckBarrageStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/CheckBarrageStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckBarrageStatusResponse>
export type CrawlBarrageRequest = { meta_live_id?: string }
export type CrawlBarrageResponse = { result_code: string; result_info: string }
/**@name CrawlBarrage
@summary 启动弹幕抓取
@description CrawlBarrage
@alias /api/content_manage.MetaLiveSvr/CrawlBarrage*/
export const CrawlBarrage = (
  props: ImplRequest<CrawlBarrageRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/CrawlBarrage",
    null,
    props,
    ...options,
  ) as ImplResponse<CrawlBarrageResponse>
export type CheckMetaLiveStatusRequest = {
  meta_live_id?: string
  /**@name virtual_man_live_data
虚拟人直播数据*/
  virtual_man_live_data?: {
    /**@name platform_account_id
平台账号id*/
    platform_account_id?: string
    /**@name virtualman_key
数字人形象key*/
    virtualman_key?: string
    /**@name platform
所属平台*/
    platform?: string
  }[]
}
export type CheckMetaLiveStatusResponse = {
  result_code: string
  result_info: string
}
/**@name CheckMetaLiveStatus
@summary 开播状态检查
@description CheckMetaLiveStatus
@alias /api/content_manage.MetaLiveSvr/CheckMetaLiveStatus*/
export const CheckMetaLiveStatus = (
  props: ImplRequest<CheckMetaLiveStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/CheckMetaLiveStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckMetaLiveStatusResponse>
export type GetPushUrlRequest = { meta_live_id?: string }
export type GetPushUrlResponse = {
  result_code: string
  result_info: string
  push_url: string
}
/**@name GetPushUrl
@summary 获取推流URL
@description GetPushUrl
@alias /api/content_manage.MetaLiveSvr/GetPushUrl*/
export const GetPushUrl = (
  props: ImplRequest<GetPushUrlRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/GetPushUrl",
    null,
    props,
    ...options,
  ) as ImplResponse<GetPushUrlResponse>
export type GetLiveUrlRequest = { meta_live_id?: string }
export type GetLiveUrlResponse = {
  result_code: string
  result_info: string
  live_url: string
}
/**@name GetLiveUrl
@summary 获取拉流URL
@description GetLiveUrl
@alias /api/content_manage.MetaLiveSvr/GetLiveUrl*/
export const GetLiveUrl = (
  props: ImplRequest<GetLiveUrlRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/GetLiveUrl",
    null,
    props,
    ...options,
  ) as ImplResponse<GetLiveUrlResponse>
export type LocalLiveHeartbeatRequest = { meta_live_id?: string }
export type LocalLiveHeartbeatResponse = {
  result_code: string
  result_info: string
}
/**@name LocalLiveHeartbeat
@summary 本地开播的心跳
@description LocalLiveHeartbeat
@alias /api/content_manage.MetaLiveSvr/LocalLiveHeartbeat*/
export const LocalLiveHeartbeat = (
  props: ImplRequest<LocalLiveHeartbeatRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/LocalLiveHeartbeat",
    null,
    props,
    ...options,
  ) as ImplResponse<LocalLiveHeartbeatResponse>
export type MetaLiveDetailRequest = { meta_live_id?: string }
export type MetaLiveDetailResponse = {
  result_code: string
  result_info: string
  /**@name data
直接使用MetaLiveList接口的结构*/
  data: {
    meta_live_id: string
    meta_live_name: string
    meta_live_poster: string
    /**@name content_type
content_type 内容类型 ppt_live script_live*/
    content_type: string
    /**@name meta_live_status
meta_live_status 直播状态  1页面未发布，2未开播，3直播中，4直播失败，5直播结束中*/
    meta_live_status: string
    create_user: string
    create_time: string
    modify_user: string
    modify_time: string
    meta_live_url: string
    /**@name barrage_crawl_status
0:未开始; 1:抓取中; 2:已下播；3:异常退出*/
    barrage_crawl_status: string
    /**@name last_start_live_time
直播开始时间*/
    last_start_live_time: string
  }
}
/**@name MetaLiveDetail
@summary 返回数字人直播的单条详情
@description 返回数字人直播的单条详情
@alias /api/content_manage.MetaLiveSvr/MetaLiveDetail*/
export const MetaLiveDetail = (
  props: ImplRequest<MetaLiveDetailRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveDetail",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveDetailResponse>
export type CheckVirtualManLiveRequest = {
  /**@name virtual_man_live_check_data
校验虚拟人是否占用*/
  virtual_man_live_check_data?: {
    /**@name platform_account_id
平台账号id*/
    platform_account_id?: string
    /**@name virtualman_key
数字人形象key*/
    virtualman_key?: string
    /**@name platform
所属平台*/
    platform?: string
  }[]
}
export type CheckVirtualManLiveResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name virtual_man_live_list
被占用的虚拟人*/
    virtual_man_live_list: {
      /**@name platform_account_id
平台账号id*/
      platform_account_id: string
      /**@name virtualman_key
数字人形象key*/
      virtualman_key: string
      /**@name platform
所属平台*/
      platform: string
    }[]
  }
}
/**@name CheckVirtualManLive
@summary 数字人占用检查
@description CheckVirtualManLive
@alias /api/content_manage.MetaLiveSvr/CheckVirtualManLive*/
export const CheckVirtualManLive = (
  props: ImplRequest<CheckVirtualManLiveRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/CheckVirtualManLive",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckVirtualManLiveResponse>
/**@name LiveResourceListRequest
LiveResourceListRequest 数字人直播平台列表请求*/
export type LiveResourceListRequest = {}
/**@name LiveResourceListResponse
LiveResourceListReply 数字人直播平台列表应答*/
export type LiveResourceListResponse = {
  result_code: string
  result_info: string
  resources: {
    /**@name id
平台id*/
    id: string
    /**@name name
平台名称*/
    name: string
  }[]
}
/**@name LiveResourceList
@summary 返回数字人直播平台列表
@description 返回数字人直播平台列表
@alias /api/content_manage.MetaLiveSvr/LiveResourceList*/
export const LiveResourceList = (
  props: ImplRequest<LiveResourceListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/LiveResourceList",
    null,
    props,
    ...options,
  ) as ImplResponse<LiveResourceListResponse>
export type MetaLiveStartRequest = { meta_live_id?: string }
export type MetaLiveStartResponse = { result_code: string; result_info: string }
/**@name MetaLiveStart
@summary 数字人启动推流
@description 数字人启动推流
@alias /api/content_manage.MetaLiveSvr/MetaLiveStart*/
export const MetaLiveStart = (
  props: ImplRequest<MetaLiveStartRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveStart",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveStartResponse>
export type MetaLiveRevertRequest = { meta_live_id?: string }
export type MetaLiveRevertResponse = {
  result_code: string
  result_info: string
}
/**@name MetaLiveRevert
@summary 数字人直播数据冲正
@description 数字人直播数据冲正
@alias /api/content_manage.MetaLiveSvr/MetaLiveRevert*/
export const MetaLiveRevert = (
  props: ImplRequest<MetaLiveRevertRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.content_manage.MetaLiveSvr/MetaLiveRevert",
    null,
    props,
    ...options,
  ) as ImplResponse<MetaLiveRevertResponse>
