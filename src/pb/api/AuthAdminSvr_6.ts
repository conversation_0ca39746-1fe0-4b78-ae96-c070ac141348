/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type QueryRoleRequest = {
  /**@name role_id
user_id的列表，传值的话就是查询指定范围的用户信息*/
  role_id?: string[]
  page_num?: number
  page_size?: number
}
export type QueryRoleResponse = {
  result_code: string
  result_info: string
  data: {
    total_size: number
    role_list: {
      sys_code: string
      merchant: string
      app_id: string
      role_id: string
      role_name: string
      creator: string
      role_description: string
      bind_user_count: number
      create_time: string
      resource_id_list: string[]
      user_id_list: string[]
    }[]
  }
}
/**@name QueryRole
@summary 查询用户信息
@description 查询用户信息*/
export const QueryRole = (
  props: ImplRequest<QueryRoleRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Role/QueryRole",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryRoleResponse>
export type AddRoleRequest = {
  role_name?: string
  role_description?: string
  resource_id_list?: string[]
}
export type AddRoleResponse = { result_code: string; result_info: string }
/**@name AddRole
@summary 新增角色信息
@description 新增角色信息*/
export const AddRole = (
  props: ImplRequest<AddRoleRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Role/AddRole",
    null,
    props,
    ...options,
  ) as ImplResponse<AddRoleResponse>
export type UpdateRoleRequest = {
  role_id?: string
  role_name?: string
  role_description?: string
  resource_id_list?: string[]
}
export type UpdateRoleResponse = { result_code: string; result_info: string }
/**@name UpdateRole
@summary 更新角色信息
@description 更新角色信息*/
export const UpdateRole = (
  props: ImplRequest<UpdateRoleRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Role/UpdateRole",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateRoleResponse>
export type DeleteRoleRequest = { role_id?: string }
export type DeleteRoleResponse = { result_code: string; result_info: string }
/**@name DeleteRole
@summary 删除角色信息
@description 删除角色信息*/
export const DeleteRole = (
  props: ImplRequest<DeleteRoleRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Role/DeleteRole",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteRoleResponse>
