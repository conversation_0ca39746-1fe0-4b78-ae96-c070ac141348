/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type NewSessionRequest = {
  /**@name session_type
暂时没用到*/
  session_type?: string
}
export type NewSessionResponse = {
  result_code: string
  result_info: string
  data: { session_id: string }
}
export const NewSession = (
  props: ImplRequest<NewSessionRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.client_copilot.Service/NewSession",
    null,
    props,
    ...options,
  ) as ImplResponse<NewSessionResponse>
export type CheckSessionRequest = { session_id?: string }
export type CheckSessionResponse = {
  result_code: string
  result_info: string
  data: { session_id: string; session_data: string }
}
export const CheckSession = (
  props: ImplRequest<CheckSessionRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.client_copilot.Service/CheckSession",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckSessionResponse>
export type GetConfigRequest = { config_id?: string }
export type GetConfigResponse = {
  result_code: string
  result_info: string
  data: { config_id: string; config_data: string }
}
export const GetConfig = (
  props: ImplRequest<GetConfigRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.client_copilot.Service/GetConfig",
    null,
    props,
    ...options,
  ) as ImplResponse<GetConfigResponse>
