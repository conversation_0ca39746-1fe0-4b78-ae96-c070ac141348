/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type MaterialUploadRequest = {
  materialInfo?: {
    id?: string
    /**@name url
文件地址*/
    url?: string
    /**@name cover_url
封面图片*/
    cover_url?: string
    /**@name name
素材名*/
    name?: string
    /**@name type
素材类型 img,video,audio*/
    type?: string
    /**@name material_status
素材状态*/
    material_status?: string
    /**@name group
分组*/
    group?: string
    /**@name source
素材来源*/
    source?: string
    /**@name length
长度*/
    length?: number
    /**@name width
宽度*/
    width?: number
    /**@name file_size
文件大小*/
    file_size?: string
    /**@name play_time
播放时长*/
    play_time?: string
    /**@name video_scale
比例*/
    video_scale?: string
    /**@name create_time
创建时间*/
    create_time?: number
    /**@name suffix
文件后缀*/
    suffix?: string
    /**@name group_list
新增的时候指定分组ID*/
    group_list?: { group_id?: string; group_name?: string }[]
    /**@name publish_status
- 0	PUBLISH_UNKNOWN	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
    /**@name form_json_schema
素材保存的json内容*/
    form_json_schema?: string
    /**@name form_tag_schema
表单素材绑定的标签信息*/
    form_tag_schema?: string
    /**@name release_status
投放状态,0未投放,1正在投放*/
    release_status?: number
    /**@name form_submitted
表单是否被提交过,0:未被提交,1:被提交过*/
    form_submitted?: number
    /**@name audit_info
审核信息,如果人工审核失败的话,会返回失败的原因*/
    audit_info?: string
    tag_list?: { tag_id?: string; tag_name?: string }[]
    ad_info_list?: {
      account_id?: string
      campaigns_id?: string
      adgroup_id?: string
      ad_id?: string
      account_name?: string
      campaigns_name?: string
      adgroup_name?: string
      ad_name?: string
    }[]
  }[]
  /**@name disable_audit
设置为true就不提交安审,用于保存素材不提交的场景中*/
  disable_audit?: boolean
}
export type MaterialUploadResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: { upload_failed_name_list: string[]; upload_success_id_list: string[] }
}
/**@name MaterialUpload
@summary 素材上传(前端)
@description 素材上传(前端上传至cos，将url传给后端)
@alias /api/material.MaterialSvr/MaterialUpload*/
export const MaterialUpload = (
  props: ImplRequest<MaterialUploadRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialUpload",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialUploadResponse>
export type MaterialModifyRequest = {
  materialInfo?: {
    id?: string
    /**@name url
文件地址*/
    url?: string
    /**@name cover_url
封面图片*/
    cover_url?: string
    /**@name name
素材名*/
    name?: string
    /**@name type
素材类型 img,video,audio*/
    type?: string
    /**@name material_status
素材状态*/
    material_status?: string
    /**@name group
分组*/
    group?: string
    /**@name source
素材来源*/
    source?: string
    /**@name length
长度*/
    length?: number
    /**@name width
宽度*/
    width?: number
    /**@name file_size
文件大小*/
    file_size?: string
    /**@name play_time
播放时长*/
    play_time?: string
    /**@name video_scale
比例*/
    video_scale?: string
    /**@name create_time
创建时间*/
    create_time?: number
    /**@name suffix
文件后缀*/
    suffix?: string
    /**@name group_list
新增的时候指定分组ID*/
    group_list?: { group_id?: string; group_name?: string }[]
    /**@name publish_status
- 0	PUBLISH_UNKNOWN	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
    /**@name form_json_schema
素材保存的json内容*/
    form_json_schema?: string
    /**@name form_tag_schema
表单素材绑定的标签信息*/
    form_tag_schema?: string
    /**@name release_status
投放状态,0未投放,1正在投放*/
    release_status?: number
    /**@name form_submitted
表单是否被提交过,0:未被提交,1:被提交过*/
    form_submitted?: number
    /**@name audit_info
审核信息,如果人工审核失败的话,会返回失败的原因*/
    audit_info?: string
    tag_list?: { tag_id?: string; tag_name?: string }[]
    ad_info_list?: {
      account_id?: string
      campaigns_id?: string
      adgroup_id?: string
      ad_id?: string
      account_name?: string
      campaigns_name?: string
      adgroup_name?: string
      ad_name?: string
    }[]
  }
  /**@name disable_audit
设置为true就不提交安审,用于保存素材不提交的场景中*/
  disable_audit?: boolean
}
export type MaterialModifyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name MaterialModify
@summary 素材修改
@description 素材名，封面的修改
@alias /api/material.MaterialSvr/MaterialModify*/
export const MaterialModify = (
  props: ImplRequest<MaterialModifyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialModify",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialModifyResponse>
export type MaterialWithFileUploadRequest = {
  materialInfo?: {
    id?: string
    /**@name url
文件地址*/
    url?: string
    /**@name cover_url
封面图片*/
    cover_url?: string
    /**@name name
素材名*/
    name?: string
    /**@name type
素材类型 img,video,audio*/
    type?: string
    /**@name material_status
素材状态*/
    material_status?: string
    /**@name group
分组*/
    group?: string
    /**@name source
素材来源*/
    source?: string
    /**@name length
长度*/
    length?: number
    /**@name width
宽度*/
    width?: number
    /**@name file_size
文件大小*/
    file_size?: string
    /**@name play_time
播放时长*/
    play_time?: string
    /**@name video_scale
比例*/
    video_scale?: string
    /**@name create_time
创建时间*/
    create_time?: number
    /**@name suffix
文件后缀*/
    suffix?: string
    /**@name group_list
新增的时候指定分组ID*/
    group_list?: { group_id?: string; group_name?: string }[]
    /**@name publish_status
- 0	PUBLISH_UNKNOWN	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
    /**@name form_json_schema
素材保存的json内容*/
    form_json_schema?: string
    /**@name form_tag_schema
表单素材绑定的标签信息*/
    form_tag_schema?: string
    /**@name release_status
投放状态,0未投放,1正在投放*/
    release_status?: number
    /**@name form_submitted
表单是否被提交过,0:未被提交,1:被提交过*/
    form_submitted?: number
    /**@name audit_info
审核信息,如果人工审核失败的话,会返回失败的原因*/
    audit_info?: string
    tag_list?: { tag_id?: string; tag_name?: string }[]
    ad_info_list?: {
      account_id?: string
      campaigns_id?: string
      adgroup_id?: string
      ad_id?: string
      account_name?: string
      campaigns_name?: string
      adgroup_name?: string
      ad_name?: string
    }[]
  }
  file_data?: string
}
export type MaterialWithFileUploadResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    /**@name url
@description 文件访问地址
@example
https://mpgo-pageb-dev-**********.cos.ap-guangzhou.myqcloud.com/Midas/164238574534075638.jpeg*/
    url: string
  }
}
/**@name MaterialWithFileUpload
@summary 素材上传(后端)
@description 素材上传(后端上传至cos，将url传给前端)
@alias /api/material.MaterialSvr/MaterialUpload*/
export const MaterialWithFileUpload = (
  props: ImplRequest<MaterialWithFileUploadRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialWithFileUpload",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialWithFileUploadResponse>
export type MaterialDeleteRequest = { material_id_list?: string[] }
export type MaterialDeleteResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
}
/**@name MaterialDelete
@summary 素材删除
@description 素材删除
@alias /api/material.MaterialSvr/MaterialDelete*/
export const MaterialDelete = (
  props: ImplRequest<MaterialDeleteRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialDelete",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialDeleteResponse>
export type MaterialListQueryRequest = {
  /**@name type
素材类型 img:图片  audio:音频 video:视频*/
  type?: string
  group?: string
  /**@name page_size
@description 分页容量
@example 5*/
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
  condition?: {
    /**@name name
名称或id*/
    name?: string
    /**@name source
来源*/
    source?: string
    /**@name width
尺寸*/
    width?: string
    length?: string
    /**@name material_status
素材审核状态*/
    material_status?: string[]
    create_time?: string[]
    url_list?: string[]
    /**@name desc_time_sort
创建时间排序*/
    desc_time_sort?: boolean
    /**@name material_id_list
只有在group参数为空值才有效*/
    material_id_list?: string[]
    /**@name publish_status
- 0	PUBLISH_UNKNOWN	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
    publish_status?: //未知状态
    | 0 //草稿
      | 1 //审核中
      | 2 //待发布
      | 3 //已发布
      | 4 //已下线
      | 6
  }
}
export type MaterialListQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    material_list: {
      id: string
      /**@name url
文件地址*/
      url: string
      /**@name cover_url
封面图片*/
      cover_url: string
      /**@name name
素材名*/
      name: string
      /**@name type
素材类型 img,video,audio*/
      type: string
      /**@name material_status
素材状态*/
      material_status: string
      /**@name group
分组*/
      group: string
      /**@name source
素材来源*/
      source: string
      /**@name length
长度*/
      length: number
      /**@name width
宽度*/
      width: number
      /**@name file_size
文件大小*/
      file_size: string
      /**@name play_time
播放时长*/
      play_time: string
      /**@name video_scale
比例*/
      video_scale: string
      /**@name create_time
创建时间*/
      create_time: number
      /**@name suffix
文件后缀*/
      suffix: string
      /**@name group_list
新增的时候指定分组ID*/
      group_list: { group_id: string; group_name: string }[]
      /**@name publish_status
- 0	PUBLISH_UNKNOWN	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
      publish_status: //未知状态
      | 0 //草稿
        | 1 //审核中
        | 2 //待发布
        | 3 //已发布
        | 4 //已下线
        | 6
      /**@name form_json_schema
素材保存的json内容*/
      form_json_schema: string
      /**@name form_tag_schema
表单素材绑定的标签信息*/
      form_tag_schema: string
      /**@name release_status
投放状态,0未投放,1正在投放*/
      release_status: number
      /**@name form_submitted
表单是否被提交过,0:未被提交,1:被提交过*/
      form_submitted: number
      /**@name audit_info
审核信息,如果人工审核失败的话,会返回失败的原因*/
      audit_info: string
      tag_list: { tag_id: string; tag_name: string }[]
      ad_info_list: {
        account_id: string
        campaigns_id: string
        adgroup_id: string
        ad_id: string
        account_name: string
        campaigns_name: string
        adgroup_name: string
        ad_name: string
      }[]
    }[]
    count: number
  }
}
/**@name MaterialListQuery
@summary 素材上传
@description 素材上传
@alias /api/material.MaterialSvr/MaterialListQuery*/
export const MaterialListQuery = (
  props: ImplRequest<MaterialListQueryRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialListQuery",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialListQueryResponse>
export type MaterialUrlQueryRequest = { material_url_list?: string[] }
export type MaterialUrlQueryResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    material_map: {
      [key: string]: {
        id: string
        /**@name url
文件地址*/
        url: string
        /**@name cover_url
封面图片*/
        cover_url: string
        /**@name name
素材名*/
        name: string
        /**@name type
素材类型 img,video,audio*/
        type: string
        /**@name material_status
素材状态*/
        material_status: string
        /**@name group
分组*/
        group: string
        /**@name source
素材来源*/
        source: string
        /**@name length
长度*/
        length: number
        /**@name width
宽度*/
        width: number
        /**@name file_size
文件大小*/
        file_size: string
        /**@name play_time
播放时长*/
        play_time: string
        /**@name video_scale
比例*/
        video_scale: string
        /**@name create_time
创建时间*/
        create_time: number
        /**@name suffix
文件后缀*/
        suffix: string
        /**@name group_list
新增的时候指定分组ID*/
        group_list: { group_id: string; group_name: string }[]
        /**@name publish_status
- 0	PUBLISH_UNKNOWN	未知状态
- 1	PUBLISH_DRAFT	草稿
- 2	PUBLISH_IN_AUDIT	审核中
- 3	PUBLISH_TO_PUB	待发布
- 4	PUBLISH_PUB	已发布
- 6	PUBLISH_OFFLINE	已下线*/
        publish_status: //未知状态
        | 0 //草稿
          | 1 //审核中
          | 2 //待发布
          | 3 //已发布
          | 4 //已下线
          | 6
        /**@name form_json_schema
素材保存的json内容*/
        form_json_schema: string
        /**@name form_tag_schema
表单素材绑定的标签信息*/
        form_tag_schema: string
        /**@name release_status
投放状态,0未投放,1正在投放*/
        release_status: number
        /**@name form_submitted
表单是否被提交过,0:未被提交,1:被提交过*/
        form_submitted: number
        /**@name audit_info
审核信息,如果人工审核失败的话,会返回失败的原因*/
        audit_info: string
        tag_list: { tag_id: string; tag_name: string }[]
        ad_info_list: {
          account_id: string
          campaigns_id: string
          adgroup_id: string
          ad_id: string
          account_name: string
          campaigns_name: string
          adgroup_name: string
          ad_name: string
        }[]
      }
    }
  }
}
/**@name MaterialUrlQuery
@summary 根据素材的URL查询素材的ID
@description 根据素材的URL查询素材的ID
@alias /api/material.MaterialSvr/MaterialIdQuery*/
export const MaterialUrlQuery = (
  props: ImplRequest<MaterialUrlQueryRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialUrlQuery",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialUrlQueryResponse>
export type GetTmpCOSSecretKeyRequest = {
  /**@name file_root_type
- 0	FileRootType_Material	
- 1	FileRootType_UserPack	
- 2	FileRootType_Pipeline*/
  file_root_type?: 0 | 1 | 2
}
export type GetTmpCOSSecretKeyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    tmp_secret_id: string
    tmp_secret_key: string
    session_token: string
    upload_path: string
    cos_url: string
  }
}
/**@name GetTmpCOSSecretKey
@summary 获取临时cos密钥
@description 获取临时cos密钥
@alias /api/material.MaterialSvr/GetTmpCOSSecretKey*/
export const GetTmpCOSSecretKey = (
  props: ImplRequest<GetTmpCOSSecretKeyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/GetTmpCOSSecretKey",
    null,
    props,
    ...options,
  ) as ImplResponse<GetTmpCOSSecretKeyResponse>
export type MaterialPublishRequest = { material_id_list?: string[] }
export type MaterialPublishResponse = {
  result_code: string
  result_info: string
  data: { failed_id_list: string[] }
}
/**@name MaterialPublish
@summary 发布素材
@description 发布素材
@alias /api/material.MaterialSvr/MaterialPublishRequest*/
export const MaterialPublish = (
  props: ImplRequest<MaterialPublishRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialPublish",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialPublishResponse>
export type GenUserTokenRequest = { gen_new?: boolean }
export type GenUserTokenResponse = {
  result_code: string
  result_info: string
  data: { user_token: string }
}
/**@name GenUserToken
@summary 获取临时cos密钥
@description 获取临时cos密钥
@alias /api/material.MaterialSvr/GetPsdCosSecretKey*/
export const GenUserToken = (
  props: ImplRequest<GenUserTokenRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/GenUserToken",
    null,
    props,
    ...options,
  ) as ImplResponse<GenUserTokenResponse>
export type GetPsdCosSecretKeyRequest = {
  /**@name user_token
@description 用户的user_token*/
  user_token?: string
}
export type GetPsdCosSecretKeyResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    tmp_secret_id: string
    tmp_secret_key: string
    session_token: string
    upload_path: string
    cos_url: string
  }
}
/**@name GetPsdCosSecretKey
@summary 获取临时cos密钥
@description 获取临时cos密钥
@alias /api/material.MaterialSvr/GetPsdCosSecretKey*/
export const GetPsdCosSecretKey = (
  props: ImplRequest<GetPsdCosSecretKeyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/GetPsdCosSecretKey",
    null,
    props,
    ...options,
  ) as ImplResponse<GetPsdCosSecretKeyResponse>
export type GetZhihuiTokenRequest = {}
export type GetZhihuiTokenResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  data: {
    app_id: string
    channel: string
    stamp: string
    /**@name timestamp
timestamp时间戳
@example "1629711353224"*/
    timestamp: string
    scope: string[]
    /**@name token
查询的到的token*/
    token: string
  }
}
/**@name GetZhihuiToken
@summary 查询得到泰山智绘的token
@description 查询得到泰山智绘的token
@alias /api/material.MaterialSvr/GetZhihuiToken*/
export const GetZhihuiToken = (
  props: ImplRequest<GetZhihuiTokenRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/GetZhihuiToken",
    null,
    props,
    ...options,
  ) as ImplResponse<GetZhihuiTokenResponse>
export type StoreZhihuiMaterialRequest = {
  /**@name name
素材的名称
@example 56a4419401b62dcda242595e4f32114a.png*/
  name?: string
  /**@name url
素材的地址
@example
//static.taishan.qq.com/editor/material/56a4419401b62dcda242595e4f32114a.png*/
  url?: string
  /**@name thumb_url
素材缩略图的地址
@example
https://static.taishan.qq.com/editor/material/56a4419401b62dcda242595e4f32114a.png?imageMogr2/thumbnail/!50p*/
  thumb_url?: string
  width?: number
  height?: number
  /**@name source
素材来源信息
@example "智绘生成"*/
  source?: string
}
export type StoreZhihuiMaterialResponse = {
  result_code: string
  result_info: string
}
/**@name StoreZhihuiMaterial
@summary 转存智绘生成的素材图片
@description 转存智绘生成的素材图片
@alias /api/material.MaterialSvr/StoreZhihuiMaterial*/
export const StoreZhihuiMaterial = (
  props: ImplRequest<StoreZhihuiMaterialRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/StoreZhihuiMaterial",
    null,
    props,
    ...options,
  ) as ImplResponse<StoreZhihuiMaterialResponse>
export type SecurityCallBackRequest = {
  content_check_id?: string
  app_type?: string
  app_id?: string
  content_list?: { content_type?: string; content_id?: string }[]
  result_code?: string
  result_msg?: string
  creator_uid?: string
}
export type SecurityCallBackResponse = {
  result_code: string
  result_info: string
}
/**@name SecurityCallBack
@summary 信安审核回调
@description 信安审核回调
@alias /api/material.MaterialSvr/SecurityCallBack*/
export const SecurityCallBack = (
  props: ImplRequest<SecurityCallBackRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/SecurityCallBack",
    null,
    props,
    ...options,
  ) as ImplResponse<SecurityCallBackResponse>
export type FormCorpWxTagGroupListRequest = {}
export type FormCorpWxTagGroupListResponse = {
  result_code: string
  result_info: string
  data: {
    group_list: { group_id: string; group_name: string; total_count: string }[]
  }
}
/**@name FormCorpWxTagGroupList
@summary 素材表单的企微标签分组接口
@description 素材表单的企微标签分组接口
@alias /api/material.MaterialSvr/FormCorpWxTagGroupList*/
export const FormCorpWxTagGroupList = (
  props: ImplRequest<FormCorpWxTagGroupListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/FormCorpWxTagGroupList",
    null,
    props,
    ...options,
  ) as ImplResponse<FormCorpWxTagGroupListResponse>
export type FormCorpWxTagListRequest = {
  group_id?: string
  search_value?: string
}
export type FormCorpWxTagListResponse = {
  result_code: string
  result_info: string
  data: { group_id: string; tag_list: { tag_id: string; tag_name: string }[] }
}
/**@name FormCorpWxTagList
@summary 表单素材的企微标签列表接口
@description 表单素材的企微标签列表接口
@alias /api/material.MaterialSvr/FormCorpWxTagList*/
export const FormCorpWxTagList = (
  props: ImplRequest<FormCorpWxTagListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/FormCorpWxTagList",
    null,
    props,
    ...options,
  ) as ImplResponse<FormCorpWxTagListResponse>
export type UserFormCommitListRequest = {
  page_size?: number
  /**@name page_num
@description 页码
@example 1*/
  page_num?: number
  condition?: {
    /**@name form_id_list
表单Id*/
    form_id_list?: string[]
    /**@name content_id_list
表单绑定的内容Id*/
    content_id_list?: string[]
  }
  create_time_desc?: boolean
}
export type UserFormCommitListResponse = {
  result_code: string
  result_info: string
  data: {
    form_schema_map: { [key: string]: string }
    user_form_commit_list: {
      form_id: string
      open_id: string
      union_id: string
      user_form_commit: string
      create_time: string
      form_json_schema: string
      content_id: string
    }[]
    total: string
  }
}
/**@name UserFormCommitList
@summary 用户表单提交列表接口
@description 用户表单提交列表接口
@alias /api/material.MaterialSvr/UserFormCommitList*/
export const UserFormCommitList = (
  props: ImplRequest<UserFormCommitListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/UserFormCommitList",
    null,
    props,
    ...options,
  ) as ImplResponse<UserFormCommitListResponse>
export type UserFormContentListRequest = { form_id_list?: string[] }
export type UserFormContentListResponse = {
  result_code: string
  result_info: string
  data: {
    content_id_map: {
      [key: string]: {
        content_list: { content_id: string; content_title: string }[]
      }
    }
  }
}
/**@name UserFormContentList
@summary 用户提交表单绑定的内容列表接口
@description 用户提交表单绑定的内容列表接口
@alias /api/material.MaterialSvr/UserFormCommitList*/
export const UserFormContentList = (
  props: ImplRequest<UserFormContentListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/UserFormContentList",
    null,
    props,
    ...options,
  ) as ImplResponse<UserFormContentListResponse>
export type MaterialListRequest = {
  /**@name systemCode
@description 调用系统-必填
@example default ma*/
  systemCode?: string
  /**@name materialType
@description 素材类型
@example image--图片 video--视频 link--网页 nativePage-原生页 text-文本
file-文件 poster-海报*/
  materialType?: string
  /**@name categoryId
@description 素材分组id 非必填
@example*/
  categoryId?: string
  /**@name materialName
@description 素材名称-支持模糊搜索 非必填
@example*/
  materialName?: string
  /**@name pageSize
@description 分页大小 必填
@example 10*/
  pageSize?: number
  /**@name pageNum
@description 第几页 必填
@example 1*/
  pageNum?: number
  /**@name tenantId
@description 租户id
@example*/
  tenantId?: string
  /**@name filter
@description 查询过滤条件
@example*/
  filter?: {
    /**@name image
@description 图片过滤参数 若materialType == image 则为必填
@example 1*/
    image?: {
      /**@name imageSizeType
@description 图片大小单位 MB KB
@example MB*/
      imageSizeType?: string
      /**@name imageSize
@description 图片大小-配合imageSizeType确定大小
@example 100*/
      imageSize?: number
      /**@name imageType
@description 图片类型 JPG PNG
@example JPG*/
      imageType?: string[]
      /**@name imageResolution
@description 图片尺寸
@example 1440*1080*/
      imageResolution?: {
        /**@name imageWidth
@description 图片宽度
@example 1080*/
        imageWidth?: number
        /**@name imageLength
@description 图片长度
@example 1440*/
        imageLength?: number
      }
    }
    /**@name video
@description 视频过滤参数 若materialType == video 则为必填
@example*/
    video?: {
      /**@name videoSizeType
@description 视频大小单位 MB KB
@example MB*/
      videoSizeType?: string
      /**@name videoSize
@description 视频大小-配合videoSizeType确定大小
@example 100*/
      videoSize?: number
      /**@name videoType
@description 视频类型 MP4
@example MP4*/
      videoType?: string
      /**@name videoLength
@description 视频长度 单位为秒
@example 30*/
      videoLength?: number
    }
    /**@name link
@description 视频过滤参数 若materialType == link 则为必填
@example*/
    link?: {
      /**@name form_type
表单类型数据
- 0	FORM_UNKNOWN	
- 1	MA	
- 2	XJ	
- 3	CUSTOM*/
      form_type?: 0 | 1 | 2 | 3
    }
  }
  /**@name adsAccount
@description 广告账号id
@example*/
  adsAccount?: string
  /**@name materialIdList
@description materialId过滤
@example*/
  materialIdList?: string[]
}
export type MaterialListResponse = {
  /**@name result_code
@description 结果码
@example 0*/
  result_code: string
  /**@name result_info
@description 结果信息
@example ok*/
  result_info: string
  /**@name totalSize
@description 总大小
@example 100*/
  totalSize: number
  data: {
    /**@name materialId
@description 素材ID
@example*/
    materialId: string
    /**@name materialName
@description 素材名称
@example*/
    materialName: string
    /**@name materialType
@description 素材类型
@example image--图片 video--视频 link--网页*/
    materialType: string
    /**@name materialUrl
@description 素材地址
@example cdn素材地址*/
    materialUrl: string
    /**@name image
@description 图片详情 若materialType == image
@example*/
    image: {
      /**@name imageSizeType
@description 图片大小单位 MB KB
@example MB*/
      imageSizeType: string
      /**@name imageSize
@description 图片大小-配合imageSizeType确定大小
@example 100*/
      imageSize: number
      /**@name imageType
@description 图片类型 JPG PNG
@example JPG*/
      imageType: string
      /**@name imageResolution
@description 图片尺寸
@example 1440*1080*/
      imageResolution: {
        /**@name imageWidth
@description 图片宽度
@example 1080*/
        imageWidth: number
        /**@name imageLength
@description 图片长度
@example 1440*/
        imageLength: number
      }
    }
    /**@name video
@description 视频过滤参数 若materialType == video 则为必填
@example*/
    video: {
      /**@name videoSizeType
@description 视频大小单位 MB KB
@example MB*/
      videoSizeType: string
      /**@name videoSize
@description 视频大小-配合videoSizeType确定大小
@example 100*/
      videoSize: number
      /**@name videoType
@description 视频类型 MP4
@example MP4*/
      videoType: string
      /**@name videoLength
@description 视频长度 单位为秒
@example 30*/
      videoLength: number
      /**@name picUrl
@description 视频头图
@example*/
      picUrl: string
    }
    /**@name link
@description 视频过滤参数 若materialType == link 则为必填
@example*/
    link: {
      /**@name linkTitle
@description 网页标题名称
@example*/
      linkTitle: string
      /**@name linkPicURL
@description 图文消息封面的url
@example*/
      linkPicURL: string
      /**@name linkDescription
@description 图文消息的描述
@example*/
      linkDescription: string
      /**@name linkURL
@description 图文消息的链接
@example*/
      linkURL: string
      /**@name linkWithMAForm
@description 内容页是否使用了MA表单
@example*/
      linkWithMAForm: boolean
      /**@name form_type
@description 内容页使用的表单类型
@example
- 0	FORM_UNKNOWN	
- 1	MA	
- 2	XJ	
- 3	CUSTOM*/
      form_type: 0 | 1 | 2 | 3
    }
    nativePage: {
      pageTitle: string
      pagePicurl: string
      pageDesc: string
      pageURL: string
    }
    text: {
      textTitle: string
      /**@name textContent
文本内容*/
      textContent: string
    }
    file: {
      /**@name fileSizeType
@description 文件大小单位 MB KB
@example MB*/
      fileSizeType: string
      /**@name fileSize
@description 文件大小-配合fileSizeType确定大小
@example 100*/
      fileSize: number
      /**@name fileType
@description 文件类型 JPG PNG
@example JPG*/
      fileType: string
    }
    poster: {
      /**@name posterSizeType
@description 海报图片大小单位 MB KB
@example MB*/
      posterSizeType: string
      /**@name posterSize
@description 海报图片大小-配合imageSizeType确定大小
@example 100*/
      posterSize: number
      /**@name posterType
@description 海报图片类型 JPG PNG
@example JPG*/
      posterType: string
      /**@name posterResolution
@description 海报图片尺寸
@example 1440*1080*/
      posterResolution: {
        /**@name posterWidth
@description 图片宽度
@example 1080*/
        posterWidth: number
        /**@name posterLength
@description 图片长度
@example 1440*/
        posterLength: number
      }
    }
    /**@name createTime
@description 创建时间
@example 2006-01-02 15:04:05*/
    createTime: string
  }[]
}
/**@name MaterialList
@summary 素材列表接口
@description 素材列表接口
@alias /maapi/material.MaterialSvr/MaterialList*/
export const MaterialList = (
  props: ImplRequest<MaterialListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialList",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialListResponse>
export type QueryWeishenCategoryListRequest = {}
export type QueryWeishenCategoryListResponse = {
  result_code: string
  result_info: string
  data: {
    category_list: {
      id: string
      parent_id: string
      name: string
      /**@name level
分类等级*/
      level: number
      media_type: number
      /**@name children_category_list
子集*/
      children_category_list: QueryWeishenCategoryListResponse["data"]["category_list"][0][]
    }[]
  }
}
/**@name QueryWeishenCategoryList
@summary 查询有群微盛的分组
@description 查询有群微盛的分组
@alias /api/material.MaterialSvr/QueryWeishenCategoryList*/
export const QueryWeishenCategoryList = (
  props: ImplRequest<QueryWeishenCategoryListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/QueryWeishenCategoryList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryWeishenCategoryListResponse>
export type EnableMaterialSyncToYouqunRequest = {}
export type EnableMaterialSyncToYouqunResponse = {
  result_code: string
  result_info: string
  data: { enable_youqun: boolean }
}
/**@name EnableMaterialSyncToYouqun
@summary 判断租户是否开通了有群微盛能力
@description 判断租户是否开通了有群微盛能力
@alias /api/material.MaterialSvr/EnableMaterialSyncToYouqun*/
export const EnableMaterialSyncToYouqun = (
  props: ImplRequest<EnableMaterialSyncToYouqunRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/EnableMaterialSyncToYouqun",
    null,
    props,
    ...options,
  ) as ImplResponse<EnableMaterialSyncToYouqunResponse>
export type SyncMaterialToYouqunRequest = {
  /**@name type
素材类型*/
  type?: string
  /**@name material_id_list
需要同步的素材信息*/
  material_id_list?: string[]
}
export type SyncMaterialToYouqunResponse = {
  result_code: string
  result_info: string
  data: { failed_id_list: string[] }
}
/**@name SyncMaterialToYouqun
@summary 同步素材到有群微盛
@description 同步素材到有群微盛
@alias /api/material.MaterialSvr/MaterialListYouqun*/
export const SyncMaterialToYouqun = (
  props: ImplRequest<SyncMaterialToYouqunRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/SyncMaterialToYouqun",
    null,
    props,
    ...options,
  ) as ImplResponse<SyncMaterialToYouqunResponse>
export type SyncMaterialToAdsRequest = {
  /**@name type
素材类型*/
  type?: string
  /**@name material_id_list
需要同步的素材信息*/
  material_id_list?: string[]
  /**@name account_id
广告账户id*/
  account_id?: string
}
export type SyncMaterialToAdsResponse = {
  result_code: string
  result_info: string
}
/**@name SyncMaterialToAds
@summary 同步素材到腾讯广告
@description 同步素材到腾讯广告
@alias /api/material.MaterialSvr/SyncMaterialToAds*/
export const SyncMaterialToAds = (
  props: ImplRequest<SyncMaterialToAdsRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/SyncMaterialToAds",
    null,
    props,
    ...options,
  ) as ImplResponse<SyncMaterialToAdsResponse>
export type QueryMaterialMerchantConfigRequest = {}
export type QueryMaterialMerchantConfigResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name enable_youqun
租户是否开启了同步微盛有群素材*/
    enable_youqun: boolean
    /**@name enable_material_audit
租户是否需要素材开启人工审核*/
    enable_material_audit: boolean
    /**@name enable_ads_material_sync
租户是否开启素材同步腾讯广告的能力*/
    enable_ads_material_sync: boolean
    /**@name enable_ads_data_reports
租户是否开启素材绑定腾讯广告的能力*/
    enable_ads_data_reports: boolean
  }
}
/**@name QueryMaterialMerchantConfig
@summary 查询素材服务下租户维度的一些配置
@description 查询素材服务下租户维度的一些配置
@alias /api/material.MaterialSvr/MaterialListYouqun*/
export const QueryMaterialMerchantConfig = (
  props: ImplRequest<QueryMaterialMerchantConfigRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/QueryMaterialMerchantConfig",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryMaterialMerchantConfigResponse>
export type AuditMaterialRequest = {
  material_id?: string
  audit_result?: string
  audit_info?: string
}
export type AuditMaterialResponse = { result_code: string; result_info: string }
/**@name AuditMaterial
@summary 审核素材的回调
@description 审核素材的回调
@alias /api/material.MaterialSvr/AuditMaterial*/
export const AuditMaterial = (
  props: ImplRequest<AuditMaterialRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/AuditMaterial",
    null,
    props,
    ...options,
  ) as ImplResponse<AuditMaterialResponse>
export type MaterialAdsListRequest = {
  /**@name account_id
广告主ID*/
  account_id?: string
  campaigns_id?: string
  adgroup_id?: string
  ad_id?: string
  /**@name name
通用的name模糊搜索字段*/
  name?: string
  page?: string
  page_size?: string
}
export type MaterialAdsListResponse = {
  result_code: string
  result_info: string
  data: {
    total_size: string
    ad_info_list: {
      account_id: string
      campaigns_id: string
      adgroup_id: string
      ad_id: string
      account_name: string
      campaigns_name: string
      adgroup_name: string
      ad_name: string
    }[]
  }
}
/**@name MaterialAdsList
@summary 查询广告列表接口
@description 查询广告列表接口
@alias /api/material.MaterialSvr/MaterialAdsList*/
export const MaterialAdsList = (
  props: ImplRequest<MaterialAdsListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialAdsList",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialAdsListResponse>
export type MaterialAdsBindRequest = {
  ad_info_list?: {
    account_id?: string
    campaigns_id?: string
    adgroup_id?: string
    ad_id?: string
    account_name?: string
    campaigns_name?: string
    adgroup_name?: string
    ad_name?: string
  }[]
  material_id?: string
  content_type?: string
}
export type MaterialAdsBindResponse = {
  result_code: string
  result_info: string
}
/**@name MaterialAdsBind
@summary 绑定素材广告
@description 绑定素材广告
@alias /api/material.MaterialSvr/MaterialAdsBind*/
export const MaterialAdsBind = (
  props: ImplRequest<MaterialAdsBindRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/MaterialAdsBind",
    null,
    props,
    ...options,
  ) as ImplResponse<MaterialAdsBindResponse>
export type QueryMaterialAdsBindRequest = {
  content_type?: string
  id_list?: string[]
}
export type QueryMaterialAdsBindResponse = {
  result_code: string
  result_info: string
  content_ads_bind_map: {
    [key: string]: {
      ad_info_list: {
        account_id: string
        campaigns_id: string
        adgroup_id: string
        ad_id: string
        account_name: string
        campaigns_name: string
        adgroup_name: string
        ad_name: string
      }[]
    }
  }
}
/**@name QueryMaterialAdsBind
@summary 查询素材广告绑定关系
@description 查询素材广告绑定关系
@alias /api/material.MaterialSvr/QueryMaterialAdsBind*/
export const QueryMaterialAdsBind = (
  props: ImplRequest<QueryMaterialAdsBindRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/QueryMaterialAdsBind",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryMaterialAdsBindResponse>
export type StartAdsDataCollectRequest = {}
export type StartAdsDataCollectResponse = {
  result_code: string
  result_info: string
}
/**@name StartAdsDataCollect
@summary 触发腾讯广告数据收集
@description 触发腾讯广告数据收集
@alias /api/material.MaterialSvr/StartAdsDataCollect*/
export const StartAdsDataCollect = (
  props: ImplRequest<StartAdsDataCollectRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.material.MaterialSvr/StartAdsDataCollect",
    null,
    props,
    ...options,
  ) as ImplResponse<StartAdsDataCollectResponse>
