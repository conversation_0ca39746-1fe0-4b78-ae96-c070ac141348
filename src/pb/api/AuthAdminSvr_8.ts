/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type AddResourceRequest = {
  /**@name system
必填*/
  system?: string
  /**@name resourceType
1 菜单 2 功能 必填*/
  resourceType?: string
  /**@name resourceName
必填*/
  resourceName?: string
  /**@name resourceCode
必填*/
  resourceCode?: string
  /**@name url
resourceType=1 必填*/
  url?: string
  /**@name show
必填*/
  show?: string
  /**@name apiList
api列表*/
  apiList?: string[]
  /**@name parentId
必填 一级菜单传 -1*/
  parentId?: string
  priority?: number
}
export type AddResourceResponse = { result_code: string; result_info: string }
/**@name AddResource
@summary 增加resource接口
@description 增加rsource接口*/
export const AddResource = (
  props: ImplRequest<AddResourceRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/AddResource",
    null,
    props,
    ...options,
  ) as ImplResponse<AddResourceResponse>
export type DeleteResourceRequest = { id?: string }
export type DeleteResourceResponse = {
  result_code: string
  result_info: string
}
/**@name DeleteResource
@summary 删除resource接口
@description 删除rsource接口*/
export const DeleteResource = (
  props: ImplRequest<DeleteResourceRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/DeleteResource",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteResourceResponse>
export type UpdateResourceRequest = {
  /**@name resourceName
必填*/
  resourceName?: string
  url?: string
  /**@name show
必填*/
  show?: string
  /**@name apiList
api列表 选填*/
  apiList?: string[]
  /**@name parentId
必填 一级菜单传 -1*/
  parentId?: string
  /**@name id
必填*/
  id?: string
  /**@name resourceType
资源类型*/
  resourceType?: string
  priority?: number
}
export type UpdateResourceResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateResource
@summary 修改resource接口
@description 修改rsource接口*/
export const UpdateResource = (
  props: ImplRequest<UpdateResourceRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/UpdateResource",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateResourceResponse>
export type QueryResourceRequest = { system?: string }
export type QueryResourceResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name system
必填*/
    system: string
    /**@name resourceType
1 菜单 2 功能 必填*/
    resourceType: string
    /**@name resourceName
必填*/
    resourceName: string
    /**@name resourceCode
必填*/
    resourceCode: string
    /**@name url
resourceType=1 必填*/
    url: string
    /**@name show
必填*/
    show: string
    /**@name apiList
api列表*/
    apiList: string[]
    /**@name parentId
必填 一级菜单传 -1*/
    parentId: string
    childList: QueryResourceResponse["data"][0][]
    createUser: string
    updateUser: string
    createTime: string
    updateTime: string
    id: string
    priority: number
  }[]
}
/**@name QueryResource
@summary 查询resource接口
@description 查询rsource接口*/
export const QueryResource = (
  props: ImplRequest<QueryResourceRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/QueryResource",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryResourceResponse>
export type AuthCheckRequest = {
  url?: string
  system?: string
  resourceIds?: string[]
}
export type AuthCheckResponse = {
  result_code: string
  result_info: string
  data: { hasAuth: boolean }
}
/**@name AuthCheck
@summary 权限确认接口
@description 权限确认接口*/
export const AuthCheck = (
  props: ImplRequest<AuthCheckRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/AuthCheck",
    null,
    props,
    ...options,
  ) as ImplResponse<AuthCheckResponse>
export type GetMenuListRequest = { resourceIds?: string[]; system?: string }
export type GetMenuListResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name system
必填*/
    system: string
    /**@name resourceType
1 菜单 2 功能 必填*/
    resourceType: string
    /**@name resourceName
必填*/
    resourceName: string
    /**@name resourceCode
必填*/
    resourceCode: string
    /**@name url
resourceType=1 必填*/
    url: string
    /**@name show
必填*/
    show: string
    /**@name apiList
api列表*/
    apiList: string[]
    /**@name parentId
必填 一级菜单传 -1*/
    parentId: string
    childList: GetMenuListResponse["data"][0][]
    createUser: string
    updateUser: string
    createTime: string
    updateTime: string
    id: string
    priority: number
  }[]
}
/**@name GetMenuList
@summary 获取用户菜单接口
@description 获取用户菜单接口*/
export const GetMenuList = (
  props: ImplRequest<GetMenuListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/GetMenuList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetMenuListResponse>
export type GetResourceListRequest = { sysCode?: string }
export type GetResourceListResponse = {
  result_code: string
  result_info: string
  data: {
    allNodes: string[]
    componentNodes: string[]
    excludeComponentNodes: string[]
    productOperateNodes: string[]
    tmpOperateNodes: string[]
    financialNodes: string[]
  }
}
export const GetResourceList = (
  props: ImplRequest<GetResourceListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Resource/GetResourceList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetResourceListResponse>
