/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type SetRequest = { key?: string; value?: string }
export type SetResponse = {
  result_code: string
  result_info: string
  url: string
}
export const Set = (
  props: ImplRequest<SetRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.cos_store.CosStoreSvr/Set",
    null,
    props,
    ...options,
  ) as ImplResponse<SetResponse>
export type SetBase64Request = {
  /**@name key
上传至cos路径*/
  key?: string
  /**@name value
读取文件后二进制流进行base64编码*/
  value?: string
}
export type SetBase64Response = {
  result_code: string
  result_info: string
  url: string
}
export const SetBase64 = (
  props: ImplRequest<SetBase64Request>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.cos_store.CosStoreSvr/SetBase64",
    null,
    props,
    ...options,
  ) as ImplResponse<SetBase64Response>
export type GetRequest = { key?: string }
export type GetResponse = {
  result_code: string
  result_info: string
  data: { value: string }
}
export const Get = (
  props: ImplRequest<GetRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.cos_store.CosStoreSvr/Get",
    null,
    props,
    ...options,
  ) as ImplResponse<GetResponse>
export type GetBase64Request = { key?: string }
export type GetBase64Response = {
  result_code: string
  result_info: string
  data: {
    /**@name value
读取文件后二进制流进行base64编码*/
    value: string
  }
}
export const GetBase64 = (
  props: ImplRequest<GetBase64Request>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.cos_store.CosStoreSvr/GetBase64",
    null,
    props,
    ...options,
  ) as ImplResponse<GetBase64Response>
export type GetTemporaryKeyRequest = { upload_path?: string }
export type GetTemporaryKeyResponse = {
  result_code: string
  result_info: string
  data: {
    tmp_secret_id: string
    tmp_secret_key: string
    session_token: string
    upload_path: string
    cos_url: string
    cdn_url: string
  }
}
export const GetTemporaryKey = (
  props: ImplRequest<GetTemporaryKeyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.cos_store.CosStoreSvr/GetTemporaryKey",
    null,
    props,
    ...options,
  ) as ImplResponse<GetTemporaryKeyResponse>
