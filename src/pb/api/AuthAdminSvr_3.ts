/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type SignAgreementRequest = {
  /**@name agreement_id
秒播产品协议:ADMUSE_AGREEMENT*/
  agreement_id?: string
}
export type SignAgreementResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name SignAgreement
@summary 签署协议*/
export const SignAgreement = (
  props: ImplRequest<SignAgreementRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Agreement/SignAgreement",
    null,
    props,
    ...options,
  ) as ImplResponse<SignAgreementResponse>
export type CheckAgreementStatusRequest = {
  /**@name agreement_id
秒播产品协议:ADMUSE_AGREEMENT*/
  agreement_id?: string
}
export type CheckAgreementStatusResponse = {
  /**@name is_signed
是否签署*/
  is_signed: boolean
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name CheckAgreementStatus
@summary 查询用户是否签署协议*/
export const CheckAgreementStatus = (
  props: ImplRequest<CheckAgreementStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Agreement/CheckAgreementStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckAgreementStatusResponse>
