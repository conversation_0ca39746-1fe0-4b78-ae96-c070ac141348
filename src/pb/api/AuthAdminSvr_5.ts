/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type UserLoginCheckRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  user_id?: string
  env_id?: string
}
export type UserLoginCheckResponse = {
  result_code: string
  result_info: string
}
/**@name UserLoginCheck
@summary 查询用户信息
@description 查询用户信息*/
export const UserLoginCheck = (
  props: ImplRequest<UserLoginCheckRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/UserLoginCheck",
    null,
    props,
    ...options,
  ) as ImplResponse<UserLoginCheckResponse>
export type UserLoginRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  user_id?: string
  user_pwd?: string
}
export type UserLoginResponse = {
  result_code: string
  result_info: string
  data: { token: string }
}
/**@name UserLogin
@summary 查询用户信息
@description 查询用户信息*/
export const UserLogin = (
  props: ImplRequest<UserLoginRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/UserLogin",
    null,
    props,
    ...options,
  ) as ImplResponse<UserLoginResponse>
export type UserLoginVerifyRequest = { token?: string }
export type UserLoginVerifyResponse = {
  result_code: string
  result_info: string
  data: {
    sys_code: string
    merchant: string
    /**@name app_id
appid,在TMC中是租户ID,在midas中是商户ID*/
    app_id: string
    user_id: string
    nick_name: string
    account_type: string
    account_id: string
  }
}
/**@name UserLoginVerify
@summary 查询用户信息
@description 查询用户信息*/
export const UserLoginVerify = (
  props: ImplRequest<UserLoginVerifyRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/UserLoginVerify",
    null,
    props,
    ...options,
  ) as ImplResponse<UserLoginVerifyResponse>
export type QueryUserResourcesRequest = { user_id_list?: string[] }
export type QueryUserResourcesResponse = {
  result_code: string
  result_info: string
  data: { user_resource: { [key: string]: string } }
}
/**@name QueryUserResources
@summary 查询用户所有的资源列表
@description 查询用户所有的资源列表*/
export const QueryUserResources = (
  props: ImplRequest<QueryUserResourcesRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/QueryUserResources",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryUserResourcesResponse>
export type QueryUserRequest = {
  /**@name user_id
user_id的列表，传值的话就是查询指定范围的用户信息*/
  user_id?: string[]
  page_num?: number
  page_size?: number
}
export type QueryUserResponse = {
  result_code: string
  result_info: string
  data: {
    total_size: number
    user_list: {
      sys_code: string
      merchant: string
      app_id: string
      sys_name: string
      user_id: string
      role_info_list: {
        sys_code: string
        merchant: string
        app_id: string
        role_id: string
        role_name: string
        creator: string
        role_description: string
        bind_user_count: number
        create_time: string
        resource_id_list: string[]
        user_id_list: string[]
      }[]
      creator_id: string
      creator_name: string
      create_time: string
    }[]
  }
}
/**@name QueryUser
@summary 查询用户信息
@description 查询用户信息*/
export const QueryUser = (
  props: ImplRequest<QueryUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/QueryUser",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryUserResponse>
export type AddUserRequest = {
  user_id?: string
  user_pwd?: string
  role_id_list?: string[]
}
export type AddUserResponse = { result_code: string; result_info: string }
/**@name AddUser
@summary 新增用户信息
@description 新增用户信息*/
export const AddUser = (
  props: ImplRequest<AddUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/AddUser",
    null,
    props,
    ...options,
  ) as ImplResponse<AddUserResponse>
export type UpdateUserRequest = {
  user_id?: string
  user_pwd?: string
  role_id_list?: string[]
}
export type UpdateUserResponse = { result_code: string; result_info: string }
/**@name UpdateUser
@summary 更新用户信息
@description 更新用户信息*/
export const UpdateUser = (
  props: ImplRequest<UpdateUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/UpdateUser",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateUserResponse>
export type DeleteUserRequest = { user_id?: string }
export type DeleteUserResponse = { result_code: string; result_info: string }
/**@name DeleteUser
@summary 删除用户信息
@description 删除用户信息*/
export const DeleteUser = (
  props: ImplRequest<DeleteUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/DeleteUser",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteUserResponse>
export type InitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<InitAppUserRequest["super_admin"]>
  common?: NonNullable<InitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<InitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<InitAppUserRequest["super_admin"]>
  financial?: NonNullable<InitAppUserRequest["super_admin"]>
}
export type InitAppUserResponse = { result_code: string; result_info: string }
/**@name InitAppUser
@summary 初始化应用用户信息
@description 初始化应用用户信息*/
export const InitAppUser = (
  props: ImplRequest<InitAppUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/InitAppUser",
    null,
    props,
    ...options,
  ) as ImplResponse<InitAppUserResponse>
export type TryInitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<TryInitAppUserRequest["super_admin"]>
  common?: NonNullable<TryInitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<TryInitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<TryInitAppUserRequest["super_admin"]>
  financial?: NonNullable<TryInitAppUserRequest["super_admin"]>
}
export type TryInitAppUserResponse = {
  result_code: string
  result_info: string
}
/**@name TryInitAppUser
@summary 初始化应用用户信息Try
@description 初始化应用用户信息Try*/
export const TryInitAppUser = (
  props: ImplRequest<TryInitAppUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/TryInitAppUser",
    null,
    props,
    ...options,
  ) as ImplResponse<TryInitAppUserResponse>
export type ConfirmInitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  common?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
  financial?: NonNullable<ConfirmInitAppUserRequest["super_admin"]>
}
export type ConfirmInitAppUserResponse = {
  result_code: string
  result_info: string
}
/**@name ConfirmInitAppUser
@summary 初始化应用用户信息Confirm
@description 初始化应用用户信息Confirm*/
export const ConfirmInitAppUser = (
  props: ImplRequest<ConfirmInitAppUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/ConfirmInitAppUser",
    null,
    props,
    ...options,
  ) as ImplResponse<ConfirmInitAppUserResponse>
export type CancelInitAppUserRequest = {
  sys_code?: string
  merchant?: string
  app_id?: string
  uid?: string
  super_admin?: { user_id_list?: string[]; resource_id_list?: string[] }
  admin?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  common?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  productOperate?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  tempOperate?: NonNullable<CancelInitAppUserRequest["super_admin"]>
  financial?: NonNullable<CancelInitAppUserRequest["super_admin"]>
}
export type CancelInitAppUserResponse = {
  result_code: string
  result_info: string
}
/**@name CancelInitAppUser
@summary 初始化应用用户信息Cancel
@description 初始化应用用户信息Cancel*/
export const CancelInitAppUser = (
  props: ImplRequest<CancelInitAppUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/CancelInitAppUser",
    null,
    props,
    ...options,
  ) as ImplResponse<CancelInitAppUserResponse>
export type QueryUserAppListRequest = {}
export type QueryUserAppListResponse = {
  result_code: string
  result_info: string
  data: { merchant_map: { [key: string]: { app_id: string[] } } }
}
/**@name QueryUserAppList
@summary 初始化应用用户信息Cancel
@description 初始化应用用户信息Cancel*/
export const QueryUserAppList = (
  props: ImplRequest<QueryUserAppListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/QueryUserAppList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryUserAppListResponse>
export type QueryAppUserRequest = { appId?: string }
export type QueryAppUserResponse = {
  result_code: string
  result_info: string
  data: { appId: string; merchantId: string; uid: string; sysCode: string }[]
}
export const QueryAppUser = (
  props: ImplRequest<QueryAppUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.User/QueryAppUser",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryAppUserResponse>
