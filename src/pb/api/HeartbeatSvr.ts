/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type AddEventRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name terminal_type
终端类型
@description：
@example:
@enum:
pc:pc客户端
server:后台服务器*/
  terminal_type?: string
  /**@name live_events
直播事件对象
@description：
@example:*/
  live_events?: {
    /**@name event_id
子事件id，这个id每个事件唯一
@description：
@example:*/
    event_id?: string
    /**@name event_type
事件类型
@description：
@example:
@enum:
restart:重启事件*/
    event_type?: string
    /**@name zhiyan_start_time
开始时间
@description：
@example:*/
    zhiyan_start_time?: string
    /**@name zhiyan_end_time
结束时间
@description：
@example:*/
    zhiyan_end_time?: string
  }
}
export type AddEventResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name AddEvent
@summary AddEvent
@description  如果增加的是服务端的事件，会直接推送到对应的服务程序；推送使用PushEvent，协议和AddEvent相同*/
export const AddEvent = (
  props: ImplRequest<AddEventRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/AddEvent",
    null,
    props,
    ...options,
  ) as ImplResponse<AddEventResponse>
export type EventConfirmRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name event_id
子事件id，这个id每个事件唯一
@description：
@example:*/
  event_id?: string
}
export type EventConfirmResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name EventConfirm
@summary EventConfirm
@description*/
export const EventConfirm = (
  props: ImplRequest<EventConfirmRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/EventConfirm",
    null,
    props,
    ...options,
  ) as ImplResponse<EventConfirmResponse>
export type GetEventListRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name terminal_type
终端类型
@description：
@example:
@enum:
pc:pc客户端
server:后台服务器*/
  terminal_type?: string
}
export type GetEventListResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name call_interval_second
调用时间间隔
@description：
@example:*/
  call_interval_second: number
  /**@name live_events_list
直播事件列表
@description：
@example:*/
  live_events_list: {
    /**@name event_id
子事件id，这个id每个事件唯一
@description：
@example:*/
    event_id: string
    /**@name event_type
事件类型
@description：
@example:
@enum:
restart:重启事件*/
    event_type: string
    /**@name zhiyan_start_time
开始时间
@description：
@example:*/
    zhiyan_start_time: string
    /**@name zhiyan_end_time
结束时间
@description：
@example:*/
    zhiyan_end_time: string
  }[]
}
/**@name GetEventList
@summary GetEventList
@description*/
export const GetEventList = (
  props: ImplRequest<GetEventListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/GetEventList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetEventListResponse>
export type HeartbeatReporterRequest = {
  /**@name live_id
live_id
@description：
@example:*/
  live_id?: string
  /**@name live_session_id
直播会话id
@description：
@example:*/
  live_session_id?: string
  /**@name terminal_type
终端类型
@description：
@example:
@enum:
pc:pc客户端
server:后台服务器*/
  terminal_type?: string
  /**@name report
心跳上报map
@description：
@example:*/
  report?: {
    /**@name digital_human_stream_frames_per_second
视频帧率
@description：
@example: 25*/
    digital_human_stream_frames_per_second?: number
  }
}
export type HeartbeatReporterResponse = {
  /**@name code
code
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name call_interval_second
调用时间间隔
@description：
@example:*/
  call_interval_second: number
}
/**@name HeartbeatReporter
@summary HeartbeatReporter
@description  live_session_id：用来唯一的标识一场会话；*/
export const HeartbeatReporter = (
  props: ImplRequest<HeartbeatReporterRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.dip_heartbeat_svr.Service/HeartbeatReporter",
    null,
    props,
    ...options,
  ) as ImplResponse<HeartbeatReporterResponse>
