/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type QueryAccountListRequest = {
  /**@name nick_name
名称模糊搜索*/
  nick_name?: string
  /**@name app_type
小店类型: WX_SHOP, 达人: WX_CHANNEL*/
  app_type?: string
  /**@name page_num
页码*/
  page_num?: number
  /**@name page_size
每页大小*/
  page_size?: number
}
export type QueryAccountListResponse = {
  data: {
    /**@name count
账号数量*/
    count: number
    /**@name account_info_list
账号信息列表*/
    account_info_list: {
      /**@name app_type
小店：WX_SHOP 达人：WX_CHANNEL*/
      app_type: string
      /**@name nick_name
昵称*/
      nick_name: string
      /**@name avatar_url
头像地址*/
      avatar_url: string
      /**@name role_ids
角色名称: admin:管理员, collaborator:协作者*/
      role_ids: string[]
      /**@name business_license_cos
营业执照COS地址*/
      business_license_cos: string
      /**@name api_app_id
API应用ID*/
      api_app_id: string
      /**@name api_secret
API密钥*/
      api_secret: string
      /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
      status: string
      /**@name app_id
账号ID*/
      app_id: string
      /**@name outer_id
账号ID*/
      outer_id: string
      /**@name subject_name
主体名称*/
      subject_name: string
      /**@name wx_id
微信ID*/
      wx_id: string
      /**@name qq_number
QQ号码*/
      qq_number: string
      /**@name phone_number
手机号码*/
      phone_number: string
      /**@name approve_id
审批ID*/
      approve_id: string
      /**@name comments
审核意见*/
      comments: string
    }[]
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name QueryAccountList
@summary 查询账号列表
@description 根据名称模糊搜索和类型获取账号信息*/
export const QueryAccountList = (
  props: ImplRequest<QueryAccountListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Operator/QueryAccountList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryAccountListResponse>
export type QueryUserInfoListRequest = {
  /**@name app_id
账号ID*/
  app_id?: string
  /**@name role_id
角色ID: 管理员/运营人员*/
  role_id?: string
  /**@name user_nick_name
用户昵称*/
  user_nick_name?: string
  /**@name page_num
页码*/
  page_num?: number
  /**@name page_size
每页大小*/
  page_size?: number
}
export type QueryUserInfoListResponse = {
  data: {
    /**@name count
用户数量*/
    count: number
    /**@name user_info_list
用户信息列表*/
    user_info_list: {
      /**@name user_id
用户ID*/
      user_id: string
      /**@name user_nick_name
用户昵称*/
      user_nick_name: string
      /**@name phone_number
手机号码*/
      phone_number: string
      /**@name wx_id
微信ID*/
      wx_id: string
      /**@name qq_number
QQ号码*/
      qq_number: string
      /**@name role_ids
角色名称: admin:管理员, collaborator:协作者*/
      role_ids: string[]
    }[]
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name QueryUserInfoList
@summary 查询用户信息列表
@description 根据账号ID和角色ID获取用户信息*/
export const QueryUserInfoList = (
  props: ImplRequest<QueryUserInfoListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Operator/QueryUserInfoList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryUserInfoListResponse>
export type QueryApprovalInfoListRequest = {
  /**@name approve_type
审核类型: REGISTRATION (注册申请)*/
  approve_type?: string
  /**@name app_type
小店类型: WX_SHOP, 达人: WX_CHANNEL*/
  app_type?: string
  /**@name nick_name
名称模糊搜索*/
  nick_name?: string
  /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
  status?: string[]
  /**@name page_num
页码*/
  page_num?: number
  /**@name page_size
每页大小*/
  page_size?: number
}
export type QueryApprovalInfoListResponse = {
  data: {
    /**@name count
审批信息数量*/
    count: number
    /**@name registration_approve_info_list
注册申请审批信息列表*/
    registration_approve_info_list: {
      /**@name approve_id
审批ID*/
      approve_id: string
      /**@name user_id
用户ID*/
      user_id: string
      /**@name app_id
账号ID*/
      app_id: string
      /**@name user_nick_name
用户昵称*/
      user_nick_name: string
      /**@name user_avatar_url
用户头像*/
      user_avatar_url: string
      /**@name business_license_cos
营业执照COS地址*/
      business_license_cos: string
      /**@name app_type
小店类型: WX_SHOP, 达人: WX_CHANNEL*/
      app_type: string
      /**@name outer_id
小店ID或者视频号ID*/
      outer_id: string
      /**@name phone_number
手机号码*/
      phone_number: string
      /**@name wx_id
微信ID*/
      wx_id: string
      /**@name qq_number
QQ号码*/
      qq_number: string
      /**@name subject_name
主体名称*/
      subject_name: string
      /**@name comments
审核意见*/
      comments: string
      createTime: string
      /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
      status: string
      nickname: string
    }[]
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name QueryApprovalInfoList
@summary 查询审批信息列表
@description 根据小店类型、名称模糊搜索和审批状态获取审批信息*/
export const QueryApprovalInfoList = (
  props: ImplRequest<QueryApprovalInfoListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Operator/QueryApprovalInfoList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryApprovalInfoListResponse>
export type SubmitApproveRequest = {
  /**@name approve_id
审核ID*/
  approve_id?: string
  /**@name status
审核状态: APPROVED, PENDING, REJECTED*/
  status?: string
  /**@name comments
审核意见*/
  comments?: string
}
export type SubmitApproveResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name SubmitApprove
@summary 提交审批结果
@description 根据审批ID提交审批状态和审核意见*/
export const SubmitApprove = (
  props: ImplRequest<SubmitApproveRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Operator/SubmitApprove",
    null,
    props,
    ...options,
  ) as ImplResponse<SubmitApproveResponse>
export type GetLoginUserInfoRequest = {}
export type GetLoginUserInfoResponse = {
  /**@name staff_name
名字*/
  staff_name: string
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetLoginUserInfo
@summary 获取登陆用户信息
@description 获取登陆用户信息*/
export const GetLoginUserInfo = (
  props: ImplRequest<GetLoginUserInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Operator/GetLoginUserInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<GetLoginUserInfoResponse>
export type ToolRequest = { input?: string }
export type ToolResponse = {
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name Tool
@summary 刷数工具
@description 刷数工具*/
export const Tool = (
  props: ImplRequest<ToolRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Operator/Tool",
    null,
    props,
    ...options,
  ) as ImplResponse<ToolResponse>
