/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type AddApiRequest = {
  /**@name system
所属系统*/
  system?: string
  /**@name url
url*/
  url?: string
  apiName?: string
  /**@name server
所属服务*/
  server?: string
  /**@name up
是否启用 1 启用*/
  up?: string
  apiType?: string
}
export type AddApiResponse = { result_code: string; result_info: string }
/**@name AddApi
@summary 增加api接口
@description 增加api接口*/
export const AddApi = (
  props: ImplRequest<AddApiRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Api/AddApi",
    null,
    props,
    ...options,
  ) as ImplResponse<AddApiResponse>
export type QueryApiRequest = {
  /**@name pageNum
查询页数 必填*/
  pageNum?: number
  /**@name pageSize
一页数量 必填*/
  pageSize?: number
  /**@name system
所属系统 必填*/
  system?: string
  /**@name queryDetail
查询细则 选填*/
  queryDetail?: {
    /**@name type
1 按名称查询 2 按照url查询*/
    type?: string
    queryString?: string
  }
}
export type QueryApiResponse = {
  result_code: string
  result_info: string
  data: {
    pageNum: number
    pageSize: number
    totalCount: number
    dataList: {
      id: string
      system: string
      systemName: string
      url: string
      apiName: string
      server: string
      createUser: string
      updateUser: string
      /**@name createTime
毫秒时间戳*/
      createTime: string
      updateTime: string
      /**@name up
是否启用*/
      up: string
      apiType: string
    }[]
  }
}
/**@name QueryApi
@summary 查询api接口
@description 查询api接口*/
export const QueryApi = (
  props: ImplRequest<QueryApiRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Api/QueryApi",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryApiResponse>
export type UpdateApiRequest = {
  apiName?: string
  server?: string
  url?: string
  up?: string
  id?: string
  apiType?: string
}
export type UpdateApiResponse = { result_code: string; result_info: string }
/**@name UpdateApi
@summary 更新api接口
@description 更新api接口*/
export const UpdateApi = (
  props: ImplRequest<UpdateApiRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Api/UpdateApi",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateApiResponse>
export type DeleteApiRequest = { id?: string }
export type DeleteApiResponse = { result_code: string; result_info: string }
/**@name DeleteApi
@summary 删除api接口
@description 删除api接口*/
export const DeleteApi = (
  props: ImplRequest<DeleteApiRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Api/DeleteApi",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteApiResponse>
