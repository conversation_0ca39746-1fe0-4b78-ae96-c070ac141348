/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type SaveScriptRequest = {
  /**@name research_name
调研名称*/
  research_name?: string
  /**@name script_info
脚本详情列表 为5个脚本*/
  script_info?: string[]
  type?: string
  app_id?: string
  /**@name source
创建来源*/
  source?: string
  /**@name nodes_count
每个脚本的环节数, 数组数量和script_info要一致*/
  nodes_count?: number[]
  /**@name application_scenarios
应用场景, 与视频和直播的application_scenarios对应*/
  application_scenarios?: string
  /**@name hidden
是否隐藏 0:可见 1:隐藏*/
  hidden?: number
}
export type SaveScriptResponse = {
  result_code: string
  result_info: string
  data: {
    script_list: {
      /**@name research_id
调研id   1个调研id对应有5个脚本*/
      research_id: string
      /**@name research_name
调研名称*/
      research_name: string
      /**@name script_id
脚本id*/
      script_id: string
      script_info: string
      app_id: string
      /**@name nodes_count
本脚本的环节数*/
      nodes_count: number
      /**@name application_scenarios
应用场景, 与视频和直播的application_scenarios对应*/
      application_scenarios: string
    }[]
  }
}
/**@name SaveScript
================ 脚本相关 ================
SaveScript 保存脚本信息*/
export const SaveScript = (
  props: ImplRequest<SaveScriptRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/SaveScript",
    null,
    props,
    ...options,
  ) as ImplResponse<SaveScriptResponse>
export type ModifyScriptRequest = {
  /**@name research_id
调研id   1个调研id对应有5个脚本*/
  research_id?: string
  /**@name script_id
脚本id*/
  script_id?: string
  script_info?: string
  app_id?: string
  nodes_count?: number
}
export type ModifyScriptResponse = { result_code: string; result_info: string }
/**@name ModifyScript
ModifyScript 修改脚本信息*/
export const ModifyScript = (
  props: ImplRequest<ModifyScriptRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/ModifyScript",
    null,
    props,
    ...options,
  ) as ImplResponse<ModifyScriptResponse>
export type ModifyOneResearchRequest = {
  /**@name research_id
调研名称*/
  research_id?: string
  /**@name script_info
脚本信息 仅支持修改一条*/
  script_info?: string
  type?: string
  app_id?: string
  nodes_count?: number
}
export type ModifyOneResearchResponse = {
  result_code: string
  result_info: string
}
/**@name ModifyOneResearch
ModifyScript 修改脚本信息*/
export const ModifyOneResearch = (
  props: ImplRequest<ModifyOneResearchRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/ModifyOneResearch",
    null,
    props,
    ...options,
  ) as ImplResponse<ModifyOneResearchResponse>
export type QueryResearchListRequest = { type?: string; app_id?: string }
export type QueryResearchListResponse = {
  result_code: string
  result_info: string
  data: {
    research_list: {
      research_id: string
      research_name: string
      create_time: string
      app_id: string
    }[]
  }
}
/**@name QueryResearchList
QueryResearchList 查询调研列表*/
export const QueryResearchList = (
  props: ImplRequest<QueryResearchListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryResearchList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryResearchListResponse>
export type QueryOnlyResearchListRequest = {
  app_id?: string
  type?: string
  /**@name search_key
搜索关键词*/
  search_key?: string
  /**@name page_no
分页*/
  page_no?: number
  /**@name page_size
每页大小*/
  page_size?: number
  /**@name application_scenarios
可选，应用场景。与视频和直播的application_scenarios对应*/
  application_scenarios?: string
  /**@name show_hidden
可选，是否显示隐藏的记录。0：只显示可见记录，1：只显示隐藏记录  2：显示所有记录*/
  show_hidden?: number
}
export type QueryOnlyResearchListResponse = {
  result_code: string
  result_info: string
  data: {
    page_no: number
    page_size: number
    research_list: {
      app_id: string
      type: string
      research_id: string
      research_name: string
      user_id: string
      source: string
      scripts_count: number
      all_nodes_count: number
      status: number
      create_time: string
      update_time: string
      application_scenarios: string
      hidden: number
    }[]
    search_key: string
    /**@name all_count
记录总数*/
    all_count: number
  }
}
/**@name QueryOnlyResearchList
QueryOnlyResearchList 只查询调研列表，新接口*/
export const QueryOnlyResearchList = (
  props: ImplRequest<QueryOnlyResearchListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryOnlyResearchList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryOnlyResearchListResponse>
export type QueryScriptListRequest = { research_id?: string; app_id?: string }
export type QueryScriptListResponse = {
  result_code: string
  result_info: string
  data: {
    script_list: {
      /**@name research_id
调研id   1个调研id对应有5个脚本*/
      research_id: string
      /**@name research_name
调研名称*/
      research_name: string
      /**@name script_id
脚本id*/
      script_id: string
      script_info: string
      app_id: string
      /**@name nodes_count
本脚本的环节数*/
      nodes_count: number
      /**@name application_scenarios
应用场景, 与视频和直播的application_scenarios对应*/
      application_scenarios: string
    }[]
    count: number
  }
}
/**@name QueryScriptList
QueryScriptList 查询脚本列表*/
export const QueryScriptList = (
  props: ImplRequest<QueryScriptListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryScriptList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryScriptListResponse>
export type ScriptEvaluateInfoRequest = {
  /**@name research_id
调研id   1个调研id对应有5个脚本*/
  research_id?: string
  /**@name script_id
脚本id*/
  script_id?: string
  /**@name evaluate
赞/踩  nice/bad*/
  evaluate?: string
  /**@name evaluate_info
赞/踩 原因*/
  evaluate_info?: string
  app_id?: string
}
export type ScriptEvaluateInfoResponse = {
  result_code: string
  result_info: string
}
/**@name ScriptEvaluateInfo
ScriptEvaluateInfo 脚本反馈 赞/踩 反馈具体信息*/
export const ScriptEvaluateInfo = (
  props: ImplRequest<ScriptEvaluateInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/ScriptEvaluateInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<ScriptEvaluateInfoResponse>
export type VideoFeedbackInfoRequest = {
  /**@name research_id
调研id   1个调研id对应有5个脚本*/
  research_id?: string
  /**@name script_id
脚本id*/
  script_id?: string
  /**@name video_evaluate_info
视频反馈*/
  video_evaluate_info?: string
  app_id?: string
}
export type VideoFeedbackInfoResponse = {
  result_code: string
  result_info: string
}
/**@name VideoFeedbackInfo
VideoFeedbackInfo 视频反馈*/
export const VideoFeedbackInfo = (
  props: ImplRequest<VideoFeedbackInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/VideoFeedbackInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<VideoFeedbackInfoResponse>
export type DeleteResearchRequest = { app_id?: string; research_id?: string }
export type DeleteResearchResponse = {
  result_code: string
  result_info: string
}
/**@name DeleteResearch
DeleteResearch 删除调研*/
export const DeleteResearch = (
  props: ImplRequest<DeleteResearchRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/DeleteResearch",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteResearchResponse>
export type UpdateImageProgressTimerRequest = {}
export type UpdateImageProgressTimerResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateImageProgressTimer
================ 定时处理腾讯云数字人的训练任务 ================
UpdateImageProgressTimer 更新形象训练进度*/
export const UpdateImageProgressTimer = (
  props: ImplRequest<UpdateImageProgressTimerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/UpdateImageProgressTimer",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateImageProgressTimerResponse>
export type UpdateVoiceProgressTimerRequest = {}
export type UpdateVoiceProgressTimerResponse = {
  result_code: string
  result_info: string
}
/**@name UpdateVoiceProgressTimer
UpdateVoiceProgressTimer 更新音色训练进度*/
export const UpdateVoiceProgressTimer = (
  props: ImplRequest<UpdateVoiceProgressTimerRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/UpdateVoiceProgressTimer",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateVoiceProgressTimerResponse>
export type QueryImageQuotaRequest = {}
export type QueryImageQuotaResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name remain_total_quota
形象训练总剩余额度*/
    remain_total_quota: number
    /**@name remain_personal_quota
形象训练个人剩余额度*/
    remain_personal_quota: number
  }
}
/**@name QueryImageQuota
================ 查询音色和形象训练的配额 ================
QueryImageQuota 查询形象训练配额*/
export const QueryImageQuota = (
  props: ImplRequest<QueryImageQuotaRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryImageQuota",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryImageQuotaResponse>
export type QueryVoiceQuotaRequest = {}
export type QueryVoiceQuotaResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name remain_total_quota
音色训练剩余总额度*/
    remain_total_quota: number
    /**@name remain_personal_quota
音色训练剩余个人额度*/
    remain_personal_quota: number
  }
}
/**@name QueryVoiceQuota
QueryVoiceQuota 查询音色训练配额*/
export const QueryVoiceQuota = (
  props: ImplRequest<QueryVoiceQuotaRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/QueryVoiceQuota",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryVoiceQuotaResponse>
export type DescribeVirtualManRequest = {
  virtual_man_key?: string
  anchor_code_list?: string[]
  page_num?: number
  /**@name page_size
页面大小（最大不超过100）*/
  page_size?: number
}
export type DescribeVirtualManResponse = {
  result_code: string
  result_info: string
  data: {
    /**@name total
总数*/
    total: number
    /**@name virtual_man_list
数智人列表*/
    virtual_man_list: {
      /**@name anchor_code
数智人主播Code*/
      anchor_code: string
      /**@name anchor_name
数智人主播名称*/
      anchor_name: string
      /**@name pose_image
数智人姿态图片URL*/
      pose_image: string
      /**@name pose_name
数智人姿态*/
      pose_name: string
      /**@name reference_video_segment_url
小样本选用视频片段URL*/
      reference_video_segment_url: string
      /**@name virtual_man_key
数智人VirtualmanKey，形象唯一标识*/
      virtual_man_key: string
    }[]
  }
}
/**@name DescribeVirtualMan
================ 查询形象信息 ================*/
export const DescribeVirtualMan = (
  props: ImplRequest<DescribeVirtualManRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.tencent.meta_feedback_svr.MetaFeedbackSvr/DescribeVirtualMan",
    null,
    props,
    ...options,
  ) as ImplResponse<DescribeVirtualManResponse>
