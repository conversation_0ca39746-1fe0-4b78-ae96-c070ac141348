/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type CheckSameUserRequest = {
  /**@name open_id
微信侧openid
@description：
@example:*/
  open_id?: string
}
export type CheckSameUserResponse = {
  /**@name is_same_user
是否同一用户
@description：
@example:*/
  is_same_user: boolean
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name CheckSameUser
@summary CheckSameUser
@description  传入微信侧openid转为腾讯充值openid后与登录态传入用户对比是否同一用户*/
export const CheckSameUser = (
  props: ImplRequest<CheckSameUserRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CheckSameUser",
    null,
    props,
    ...options,
  ) as ImplResponse<CheckSameUserResponse>
export type CreateMultiQaContentIdRequest = {
  /**@name qa_content_object_list
问答库内容对象
@description：
@example:*/
  qa_content_object_list?: {
    /**@name qa_content_id
问答库内容ID
@description：
@example:*/
    qa_content_id?: string
    /**@name question
问题
@description：
@example:*/
    question?: string
    /**@name answer
答案
@description：
@example:*/
    answer?: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time?: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time?: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id?: string
    /**@name group_type
分组类型
@description：
@example:*/
    group_type?: number
    /**@name group_id
分组ID
@description：
@example:*/
    group_id?: string
  }[]
  /**@name is_force_coverage
强制覆盖当前分组中的数据
@description：
@example:*/
  is_force_coverage?: boolean
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
}
export type CreateMultiQaContentIdResponse = {
  /**@name qa_content_object_list
问答库内容对象
@description：
@example:*/
  qa_content_object_list: {
    /**@name qa_content_id
问答库内容ID
@description：
@example:*/
    qa_content_id: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name group_type
分组类型
@description：
@example:*/
    group_type: number
    /**@name group_id
分组ID
@description：
@example:*/
    group_id: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name CreateMultiQaContentId
@summary CreateMultiQaContentId
@description*/
export const CreateMultiQaContentId = (
  props: ImplRequest<CreateMultiQaContentIdRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CreateMultiQaContentId",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateMultiQaContentIdResponse>
export type CreateNpcJobRequest = {
  /**@name npc_id
数字人模型ID
@description：
@example:*/
  npc_id?: string
  /**@name model_input_content
模型输入内容
@description：
@example:*/
  model_input_content?: string
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type CreateNpcJobResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败*/
  job_status: number
  /**@name job_id
任务ID
@description：
@example:*/
  job_id: string
}
/**@name CreateNpcJob
@summary CreateNpcJob
@description*/
export const CreateNpcJob = (
  props: ImplRequest<CreateNpcJobRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CreateNpcJob",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateNpcJobResponse>
export type CreateNpcJobSyncRequest = {
  /**@name model_input_content
模型输入内容
@description：
@example:*/
  model_input_content?: string
  /**@name npc_id
数字人模型ID
@description：
@example:*/
  npc_id?: string
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type CreateNpcJobSyncResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败*/
  job_status: number
  /**@name job_id
任务ID
@description：
@example:*/
  job_id: string
  /**@name model_return_content
模型返回内容
@description：
@example:*/
  model_return_content: string
}
/**@name CreateNpcJobSync
@summary CreateNpcJob_sync
@description*/
export const CreateNpcJobSync = (
  props: ImplRequest<CreateNpcJobSyncRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CreateNpcJobSync",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateNpcJobSyncResponse>
export type CreateQaContentIdRequest = {
  /**@name question
问题
@description：
@example:*/
  question?: string
  /**@name answer
答案
@description：
@example:*/
  answer?: string
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
}
export type CreateQaContentIdResponse = {
  /**@name qa_content_id
问答库内容ID
@description：
@example:*/
  qa_content_id: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name CreateQaContentId
@summary CreateQaContentId
@description*/
export const CreateQaContentId = (
  props: ImplRequest<CreateQaContentIdRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CreateQaContentId",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateQaContentIdResponse>
export type CreateUserGroupRequest = {
  /**@name sort
排序
@description：
@example:*/
  sort?: number
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name group_name
分组名
@description：
@example:*/
  group_name?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
}
export type CreateUserGroupResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name group_id
分组ID
@description：
@example:*/
  group_id: string
}
/**@name CreateUserGroup
@summary CreateUserGroup
@description  group_type取值：1=问答库，2=问答库草稿，4=AMS用户可见模版*/
export const CreateUserGroup = (
  props: ImplRequest<CreateUserGroupRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CreateUserGroup",
    null,
    props,
    ...options,
  ) as ImplResponse<CreateUserGroupResponse>
export type CustomTokenLoginRequest = {
  /**@name custom_token
自定义登录token
@description：
@example:*/
  custom_token?: string
}
export type CustomTokenLoginResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name custom_login_state
自定义的登录态
@description：
@example:*/
  custom_login_state: string
  /**@name user_id
用户ID
@description：
@example:*/
  user_id: string
}
/**@name CustomTokenLogin
@summary CustomTokenLogin
@description  返回的custom_login_state是个json格式字符串，是后续调接口的登录态*/
export const CustomTokenLogin = (
  props: ImplRequest<CustomTokenLoginRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/CustomTokenLogin",
    null,
    props,
    ...options,
  ) as ImplResponse<CustomTokenLoginResponse>
export type DeleteQaByContentIdRequest = {
  /**@name qa_content_id
问答库内容ID
@description：
@example:*/
  qa_content_id?: string
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
}
export type DeleteQaByContentIdResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name DeleteQaByContentId
@summary DeleteQaByContentId
@description*/
export const DeleteQaByContentId = (
  props: ImplRequest<DeleteQaByContentIdRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/DeleteQaByContentId",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteQaByContentIdResponse>
export type DeleteUserGroupRequest = {
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
}
export type DeleteUserGroupResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name DeleteUserGroup
@summary DeleteUserGroup
@description*/
export const DeleteUserGroup = (
  props: ImplRequest<DeleteUserGroupRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/DeleteUserGroup",
    null,
    props,
    ...options,
  ) as ImplResponse<DeleteUserGroupResponse>
export type GetCustomTokenStatusRequest = {
  /**@name custom_token
自定义登录token
@description：
@example:*/
  custom_token?: string
}
export type GetCustomTokenStatusResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name status
自定义登录态的扫码状态
@description：
@example:*/
  status: string
}
/**@name GetCustomTokenStatus
@summary GetCustomTokenStatus
@description  status的取值是VALID=小程序码有效（待扫码）、USED=小程序码已被扫码（已扫码）、INVALID=小程序码已失效（已过期）*/
export const GetCustomTokenStatus = (
  props: ImplRequest<GetCustomTokenStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetCustomTokenStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<GetCustomTokenStatusResponse>
export type GetLiveExtendConfigItemsRequest = {
  /**@name node_id
直播节点ID
@description：
@example:*/
  node_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type GetLiveExtendConfigItemsResponse = {
  /**@name records
直播扩展配置项
@description：
@example:*/
  records: {
    /**@name node_id
直播节点ID
@description：
@example:*/
    node_id: string
    /**@name config_item_id
配置项ID
@description：
@example:*/
    config_item_id: string
    /**@name config_item_value
配置项值
@description：
@example:*/
    config_item_value: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name live_id
直播ID
@description：
@example:*/
    live_id: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name GetLiveExtendConfigItems
@summary GetLiveExtendConfigItems
@description*/
export const GetLiveExtendConfigItems = (
  props: ImplRequest<GetLiveExtendConfigItemsRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetLiveExtendConfigItems",
    null,
    props,
    ...options,
  ) as ImplResponse<GetLiveExtendConfigItemsResponse>
export type GetMicrosoftIssueTokenRequest = {
  /**@name token_type
token的类型
@description：
@example:*/
  token_type?: string
}
export type GetMicrosoftIssueTokenResponse = {
  /**@name accesstoken
accesstoken
@description：
@example:*/
  accesstoken: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name GetMicrosoftIssueToken
@summary GetMicrosoftIssueToken
@description  获取微软的IssueToken凭证*/
export const GetMicrosoftIssueToken = (
  props: ImplRequest<GetMicrosoftIssueTokenRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetMicrosoftIssueToken",
    null,
    props,
    ...options,
  ) as ImplResponse<GetMicrosoftIssueTokenResponse>
export type GetPPtContentRequest = {
  /**@name ppt_url
ppt的url地址
@description：
@example:*/
  ppt_url?: string
}
export type GetPPtContentResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name records
ppt页的列表
@description：
@example:*/
  records: {
    /**@name page_id
ppt的页码id
@description：
@example:*/
    page_id: string
    /**@name pic_url
图片url
@description：
@example:*/
    pic_url: string
    /**@name ocr_content
ocr提取的内容
@description：对ppt的pic_url通过ocr算法提取的内容
@example:*/
    ocr_content: string
    /**@name ppt_content
ppt的内容
@description：ppt通过标签解析的内容
@example:*/
    ppt_content: string
  }[]
}
/**@name GetPPtContent
@summary GetPPtContent
@description*/
export const GetPPtContent = (
  props: ImplRequest<GetPPtContentRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetPPtContent",
    null,
    props,
    ...options,
  ) as ImplResponse<GetPPtContentResponse>
export type GetTencentCloudSignRequest = {
  /**@name query
腾讯云query
@description：
@example:*/
  query?: string
  /**@name appkey
腾讯云key
@description：
@example:*/
  appkey?: string
  /**@name token_type
token的类型
@description：
@example:*/
  token_type?: string
}
export type GetTencentCloudSignResponse = {
  /**@name signature
签名
@description：
@example:*/
  signature: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name GetTencentCloudSign
@summary GetTencentCloudSign
@description  token_type为"shuzhiren"，代表腾讯云数智人的签名；token_type = "tts"，代表腾讯云tts接口的签名*/
export const GetTencentCloudSign = (
  props: ImplRequest<GetTencentCloudSignRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetTencentCloudSign",
    null,
    props,
    ...options,
  ) as ImplResponse<GetTencentCloudSignResponse>
export type GetUnlimitedQRCodeRequest = {
  /**@name scene
场景信息
@description：
@example:*/
  scene?: string
}
export type GetUnlimitedQRCodeResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name qr_code
二维码
@description：
@example:*/
  qr_code: string
  /**@name custom_token
自定义登录token
@description：
@example:*/
  custom_token: string
}
/**@name GetUnlimitedQRCode
@summary GetUnlimitedQRCode
@description  获取微信小程序二维码。scene是用户的openid，小程序扫码后会判断扫码用户与pc管理台用户是不是同一个用户。*/
export const GetUnlimitedQRCode = (
  props: ImplRequest<GetUnlimitedQRCodeRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetUnlimitedQRCode",
    null,
    props,
    ...options,
  ) as ImplResponse<GetUnlimitedQRCodeResponse>
export type GetUserGroupListRequest = {
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name group_name
分组名
@description：
@example:*/
  group_name?: string
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name page_size
每页大小
@description：每页大小
@example:*/
  page_size?: number
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
}
export type GetUserGroupListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name records
用户分组对象
@description：
@example:*/
  records: {
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name user_type
用户平台类型
@description：
@example:*/
    user_type: number
    /**@name group_type
分组类型
@description：
@example:*/
    group_type: number
    /**@name group_id
分组ID
@description：
@example:*/
    group_id: string
    /**@name group_name
分组名
@description：
@example:*/
    group_name: string
    /**@name sort
排序
@description：
@example:*/
    sort: number
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
  }[]
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
}
/**@name GetUserGroupList
@summary GetUserGroupList
@description  获取用户账户下Group列表，group_type必填；group_name可选，如果填写代表根据group_namej进行过滤；group_id可选，如果填写代表根据group_id进行过滤；user_id通过用户登录态获取，不用放到Request中；page_size默认为100*/
export const GetUserGroupList = (
  props: ImplRequest<GetUserGroupListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetUserGroupList",
    null,
    props,
    ...options,
  ) as ImplResponse<GetUserGroupListResponse>
export type GetZhiyingAccesstokenRequest = {
  /**@name userid
智影userid
@description：
@example:*/
  userid?: string
  /**@name expiry
智影expirynum
@description：
@example:*/
  expiry?: number
  /**@name appid
智影appid
@description：
@example:*/
  appid?: string
}
export type GetZhiyingAccesstokenResponse = {
  /**@name expiry_time
智影过期时间
@description：
@example:*/
  expiry_time: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name accesstoken
accesstoken
@description：
@example:*/
  accesstoken: string
}
/**@name GetZhiyingAccesstoken
@summary GetZhiyingAccesstoken
@description*/
export const GetZhiyingAccesstoken = (
  props: ImplRequest<GetZhiyingAccesstokenRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/GetZhiyingAccesstoken",
    null,
    props,
    ...options,
  ) as ImplResponse<GetZhiyingAccesstokenResponse>
export type PromoteProductRequest = {
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
  /**@name product_id
物品id
@description：
@example:*/
  product_id?: string
  /**@name promote_scene
推广场景
@description：
@example:
@enum:
0:未知
1:讲解
2:结束讲解*/
  promote_scene?: number
}
export type PromoteProductResponse = {
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
}
/**@name PromoteProduct
@summary PromoteProduct
@description  小黄车上架讲解及结束讲解*/
export const PromoteProduct = (
  props: ImplRequest<PromoteProductRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/PromoteProduct",
    null,
    props,
    ...options,
  ) as ImplResponse<PromoteProductResponse>
export type QueryLastestDanmuInfoListRequest = {
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
}
export type QueryLastestDanmuInfoListResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name danmu_reply_info_list
弹幕回复信息列表
@description：
@example:*/
  danmu_reply_info_list: {
    /**@name reply_id
弹幕回复ID
@description：
@example:*/
    reply_id: string
    /**@name npc_name
ncp名字
@description：
@example:*/
    npc_name: string
    /**@name content
内容
@description：
@example:*/
    content: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name danmu_id
直播弹幕ID
@description：
@example:*/
    danmu_id: string
  }[]
  /**@name danmu_info_list
弹幕信息列表
@description：
@example:*/
  danmu_info_list: {
    /**@name danmu_id
直播弹幕ID
@description：
@example:*/
    danmu_id: string
    /**@name live_user_id
直播用户ID
@description：
@example:*/
    live_user_id: string
    /**@name pubsub_time
发表时间
@description：
@example:*/
    pubsub_time: number
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name content
内容
@description：
@example:*/
    content: string
    /**@name status
状态
@description：
@example:
@enum:
-1:无效
1:有效
2:已处理*/
    status: number
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
  }[]
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name QueryLastestDanmuInfoList
@summary QueryLastestDanmuInfoList
@description  查询最新弹幕列表// @alias QueryLastestDanmuInfoList*/
export const QueryLastestDanmuInfoList = (
  props: ImplRequest<QueryLastestDanmuInfoListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/QueryLastestDanmuInfoList",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryLastestDanmuInfoListResponse>
export type QueryNpcJobRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type QueryNpcJobResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name model_return_content
模型返回内容
@description：
@example:*/
  model_return_content: string
  /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败*/
  job_status: number
  /**@name job_id
任务ID
@description：
@example:*/
  job_id: string
}
/**@name QueryNpcJob
@summary QueryNpcJob
@description*/
export const QueryNpcJob = (
  props: ImplRequest<QueryNpcJobRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/QueryNpcJob",
    null,
    props,
    ...options,
  ) as ImplResponse<QueryNpcJobResponse>
export type SearchQaContentListRequest = {
  /**@name search_key
搜索关键字
@description：
@example:*/
  search_key?: string
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name page_num
页码
@description：页码
@example:*/
  page_num?: number
  /**@name page_size
每页大小
@description：每页大小
@example:*/
  page_size?: number
}
export type SearchQaContentListResponse = {
  /**@name records
问答库内容对象
@description：
@example:*/
  records: {
    /**@name qa_content_id
问答库内容ID
@description：
@example:*/
    qa_content_id: string
    /**@name question
问题
@description：
@example:*/
    question: string
    /**@name answer
答案
@description：
@example:*/
    answer: string
    /**@name update_time
更新时间
@description：
@example:*/
    update_time: string
    /**@name create_time
创建时间
@description：
@example:*/
    create_time: string
    /**@name user_id
用户ID
@description：
@example:*/
    user_id: string
    /**@name group_type
分组类型
@description：
@example:*/
    group_type: number
    /**@name group_id
分组ID
@description：
@example:*/
    group_id: string
  }[]
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
  /**@name query_count
总数
@description：总数
@example:*/
  query_count: number
}
/**@name SearchQaContentList
@summary SearchQaContentList
@description*/
export const SearchQaContentList = (
  props: ImplRequest<SearchQaContentListRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/SearchQaContentList",
    null,
    props,
    ...options,
  ) as ImplResponse<SearchQaContentListResponse>
export type StopNpcJobRequest = {
  /**@name job_id
任务ID
@description：
@example:*/
  job_id?: string
}
export type StopNpcJobResponse = {
  /**@name job_status
job的状态
@description：
@example:
@enum:
1:任务完成
2:执行中
-1:无效
3:手动停止
4:任务失败*/
  job_status: number
  /**@name job_id
任务ID
@description：
@example:*/
  job_id: string
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name StopNpcJob
@summary StopNpcJob
@description*/
export const StopNpcJob = (
  props: ImplRequest<StopNpcJobRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/StopNpcJob",
    null,
    props,
    ...options,
  ) as ImplResponse<StopNpcJobResponse>
export type TransUserGroupTypeRequest = {
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
}
export type TransUserGroupTypeResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name TransUserGroupType
@summary TransUserGroupType
@description  group_type = 1: 代表用户正式问答库分组；group_type = 2: 代表问答库草稿箱分组；可以将group_type = 2 转化为group_type =1，反之不行；*/
export const TransUserGroupType = (
  props: ImplRequest<TransUserGroupTypeRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/TransUserGroupType",
    null,
    props,
    ...options,
  ) as ImplResponse<TransUserGroupTypeResponse>
export type UpdateDanmuHasProcStatusRequest = {
  /**@name danmu_id
直播弹幕ID
@description：
@example:*/
  danmu_id?: string
}
export type UpdateDanmuHasProcStatusResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateDanmuHasProcStatus
@summary UpdateDanmuHasProcStatus
@description  更新弹幕状态// @alias UpdateDanmuHasProcStatus*/
export const UpdateDanmuHasProcStatus = (
  props: ImplRequest<UpdateDanmuHasProcStatusRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/UpdateDanmuHasProcStatus",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateDanmuHasProcStatusResponse>
export type UpdateLiveExtendConfigItemRequest = {
  /**@name config_item_value
配置项值
@description：
@example:*/
  config_item_value?: string
  /**@name config_item_id
配置项ID
@description：
@example:*/
  config_item_id?: string
  /**@name node_id
直播节点ID
@description：
@example:*/
  node_id?: string
  /**@name live_id
直播ID
@description：
@example:*/
  live_id?: string
}
export type UpdateLiveExtendConfigItemResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateLiveExtendConfigItem
@summary UpdateLiveExtendConfigItem
@description  如果不存在，则插入记录；否则更新该配置项*/
export const UpdateLiveExtendConfigItem = (
  props: ImplRequest<UpdateLiveExtendConfigItemRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/UpdateLiveExtendConfigItem",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateLiveExtendConfigItemResponse>
export type UpdateQaContentRequest = {
  /**@name answer
答案
@description：
@example:*/
  answer?: string
  /**@name question
问题
@description：
@example:*/
  question?: string
  /**@name qa_content_id
问答库内容ID
@description：
@example:*/
  qa_content_id?: string
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
}
export type UpdateQaContentResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateQaContent
@summary UpdateQaContent
@description*/
export const UpdateQaContent = (
  props: ImplRequest<UpdateQaContentRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/UpdateQaContent",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateQaContentResponse>
export type UpdateUserGroupInforRequest = {
  /**@name sort
排序
@description：
@example:*/
  sort?: number
  /**@name group_name
分组名
@description：
@example:*/
  group_name?: string
  /**@name group_id
分组ID
@description：
@example:*/
  group_id?: string
  /**@name group_type
分组类型
@description：
@example:*/
  group_type?: number
  /**@name user_id
用户ID
@description：
@example:*/
  user_id?: string
}
export type UpdateUserGroupInforResponse = {
  /**@name message
错误描述信息
@description：
@example:*/
  message: string
  /**@name code
状态码
@description：
@example:*/
  code: string
}
/**@name UpdateUserGroupInfor
@summary UpdateUserGroupInfor
@description*/
export const UpdateUserGroupInfor = (
  props: ImplRequest<UpdateUserGroupInforRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.manager_sys_api.Development/UpdateUserGroupInfor",
    null,
    props,
    ...options,
  ) as ImplResponse<UpdateUserGroupInforResponse>
