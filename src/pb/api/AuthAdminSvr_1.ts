/* eslint-disable */
import type {ImplRequest, ImplResponse} from '../config';
import {ImplServer} from '../config';
export type GetInvitationCodeRequest = {}
export type GetInvitationCodeResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name invitation_code
邀请码*/
    invitation_code: string
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetInvitationCode
@summary 获取邀请码
@description 获取用户的邀请码*/
export const GetInvitationCode = (
  props: ImplRequest<GetInvitationCodeRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Invitation/GetInvitationCode",
    null,
    props,
    ...options,
  ) as ImplResponse<GetInvitationCodeResponse>
export type GetInvitationInfoRequest = {
  /**@name invitation_code
邀请码*/
  invitation_code?: string
}
export type GetInvitationInfoResponse = {
  /**@name data
返回的数据*/
  data: {
    /**@name invitation_info
邀请码信息*/
    invitation_info: {
      /**@name invitation_code
邀请码*/
      invitation_code: string
      /**@name nick_name
昵称*/
      nick_name: string
      /**@name avatar_url
头像地址*/
      avatar_url: string
      /**@name app_id
账号ID*/
      app_id: string
    }
  }
  /**@name result_code
返回结果代码*/
  result_code: string
  /**@name result_info
返回结果信息*/
  result_info: string
}
/**@name GetInvitationInfo
@summary 获取邀请码信息
@description 根据邀请码获取邀请码的详细信息*/
export const GetInvitationInfo = (
  props: ImplRequest<GetInvitationInfoRequest>,
  ...options: Parameters<typeof ImplServer> extends [
    unknown,
    unknown,
    unknown,
    ...infer R,
  ]
    ? R
    : []
) =>
  ImplServer(
    "/trpc.dip.admin.Invitation/GetInvitationInfo",
    null,
    props,
    ...options,
  ) as ImplResponse<GetInvitationInfoResponse>
