import { <PERSON>gin<PERSON>pi<PERSON>tom } from '@/model/api';
import { ClientLive } from '@/model/clientLive';
import { LoginStateAtom } from '@/model/login';
import { runningInClient } from '@/utils/electron';
import to from 'await-to-js';
import axios, { AxiosResponse } from 'axios';
import Mock from 'mockjs';
import { getRecoil } from 'recoil-nexus';
import { MessagePlugin } from 'tdesign-react';
import { MatchedGlobalConfigItem } from './../configs/global_runtime';
import { UserInfoAtom } from '@/model/user';
export type ImplRequest<T> = T;
export type ImplResponse<T> = Promise<T extends { data: infer P } ? P : T>;

const CLIENT_WHITE_LIST: string[] = [
  'content_manage.MetaLiveSvr/LocalLiveHeartbeat',
  'dip_heartbeat_svr.Service/GetEventList',
  'dip_heartbeat_svr.Service/EventConfirm',
];
const NO_LOGIN_WHITE_LIST: string[] = ['client_copilot.Service/GetConfig'];

export const queryRealUrl = (url: string) => {
  return url.split('/api/')[1];
};

export const getBaseUrl = () => {
  return (
    MatchedGlobalConfigItem.apiBaseUri ||
    import.meta.env.VITE_API_BASE_URL ||
    `//${location.host}`
  );
};
export class RespError<T = unknown> extends Error {
  public resultCode: string;
  public resultInfo: string;
  public requestId: string;
  public data: T;
  public displayInfo: string;

  constructor(resp: Response<T>) {
    super(`请求失败：Code:${resp.result_code},Message:${resp.result_info}`);
    this.resultCode = resp.result_code || resp.code;
    this.resultInfo = resp.result_info;
    this.data = resp.data;
    this.displayInfo = resp.display_info;
    this.requestId = resp['x-request-id'];
    Reflect.setPrototypeOf(this, new.target.prototype); // 兼容 ES5 并且可以使用instanceof
  }
  public static is(err: Error): err is RespError {
    return err instanceof RespError;
  }
}
// 返回结构体
export interface Response<T = unknown> {
  result_code: string;
  result_info: string;
  display_info: string;
  'x-request-id': string;
  data: T;
}
/**
 * 获取网络请求函数的请求类型
 */
export type ReqType<T extends (...args: never[]) => unknown> = T extends (
  args: infer P
) => unknown
  ? P
  : never;

// 获取接口的返回类型
export type RespType<T extends (...args: never[]) => unknown> = T extends (
  ...args: never[]
) => ImplResponse<infer P>
  ? P extends { data: infer Q }
    ? Q
    : P
  : never;

const ImplServer = async (
  URL: string,
  MockStruct: unknown,
  Req: unknown,
  NeedMock = false
): Promise<unknown> => {
  const handleResponse = (res: Response): Promise<unknown> => {
    const result_code = res.result_code || res.code;
    // 在这里做统一错误处理
    if (result_code === '0' || result_code === 'SUCCESS') {
      return Promise.resolve(res.data ?? res); // 只返回有效数据
    }
    if (
      result_code === '400004' ||
      result_code === '100033' ||
      result_code === '400002'
    ) {
      MessagePlugin.error({
        content: '当前应用权限校验失败',
      });
    }
    if (['100003', '100009', '100016', '10015'].indexOf(result_code) > -1) {
      // withoutLogin();
      (async () => {
        const loginApi = getRecoil(LoginApiAtom);
        if (loginApi) {
          const [, beforeLoginResult] = await to(
            loginApi.fetchLoginInfo({
              noCache: true,
            })
          );
          let loginState = null;
          if (!beforeLoginResult) {
            await loginApi.goToLogin();
            const [, result] = await to(loginApi.fetchLoginInfo());
            if (result) {
              loginState = result.loginSession;
            }
          } else {
            loginState = beforeLoginResult.loginSession;
          }
          if (loginState) {
            return;
          }
          const [loginErr] = await to(loginApi.goToLogin());
          !loginErr &&
            (await to(
              loginApi.fetchLoginInfo({
                noCache: true,
              })
            ));
        }
      })();

      return Promise.reject(new Error('您的登录已失效，请重新登录'));
    }

    /**
     * 如果有错误信息，直接弹出错误信息，同时将错误信息返回给调用者，调用者可以不进行处理
     * 但这里还是需要 reject
     * 或许有更好的做法
     */
    if ('display_info' in res && res.display_info) {
      MessagePlugin.error({
        content: res.display_info,
      });
      return Promise.reject(new RespError(res));
    }

    // 其他的错误交给模块自己处理
    return Promise.reject(new RespError(res));
  };

  if (NeedMock) {
    const supportsGroup = typeof console?.groupCollapsed === 'function';
    if (NeedMock) {
      const result = Mock.mock(MockStruct);
      result.result_code = '0';
      result.result_info = 'ok';
      supportsGroup && console.groupCollapsed('Mock:', URL);
      console.log('URL', URL);
      console.log('Request', Req);
      console.trace('Mock Response', result);
      supportsGroup && console.groupEnd();
      return Promise.resolve(result.data ?? result);
    }
    if (typeof NeedMock === 'object') {
      const result = Mock.mock(NeedMock);
      supportsGroup && console.groupCollapsed('Mock:', URL);
      console.log('URL', URL);
      console.log('Request', Req);
      console.trace('Mock Response', result);
      supportsGroup && console.groupEnd();
      if ('result_code' in result) {
        return handleResponse(result as Response);
      }
      return handleResponse({
        result_code: '0',
        'x-request-id': '',
        result_info: 'ok',
        data: result,
        display_info: '',
      });
    }
  }

  let url = URL;
  if (url.indexOf('/trpc.tencent.') !== -1) {
    url = url.replace(/\/trpc.tencent./, '/api/');
  } else if (url.indexOf('/trpc.pagedoo.') !== -1) {
    url = url.replace(/\/trpc.pagedoo./, '/api/');
  } else if (url.indexOf('/trpc.dip.') !== -1) {
    url = url.replace(/\/trpc.dip./, '/api/');
  }
  console.log('--url: ', url);
  /**
   * 生成 UUID，用于后端快速定位请求
   */
  const uuid = getUUID();

  return axios
    .post(url, Req, {
      timeout: 600000,
      baseURL: CLIENT_WHITE_LIST.includes(queryRealUrl(url))
        ? import.meta.env.VITE_CLIENT_API_BASE_URL
        : getBaseUrl(),
      withCredentials: true,
      headers: {
        'x-request-id': uuid,
        'x-tencent-login-check': await getTencentLoginCheck(url),
      },
    })
    .catch((err: Error) => {
      // 网络错误
      // MessagePlugin.error({ content: '网络异常，请稍后再试！' });
      console.error(err);
      return Promise.reject(err);
    })
    .then((res: AxiosResponse<Response>) => {
      // tRPC-GO ResultCode 31 implement me报错 意思是接口未实现
      // tRPC-GO ResultCode 141 context deadline exceeded, cost:x.0µs 超时报错
      // 在这里做统一错误处理
      return Promise.resolve(res.data); // 只返回有效数据
    })
    .then(handleResponse);
};

const ImplRespond = <T extends Response<unknown>>(
  res: ImplRequest<T>
): ImplResponse<T> => {
  // 在这里做接口返回的错误处理
  // tRPC-GO ResultCode 31 implement me报错 意思是接口未实现
  // tRPC-GO ResultCode 141 context deadline exceeded, cost:x.0µs 超时报错
  // if (res.code !== 0) return Promise.reject("请求失败！");

  // 在这里处理数据只返回有效数据
  return Promise.resolve(res.data) as ImplResponse<T>;
};

export { ImplRespond, ImplServer };

export const getTencentLoginCheck = async (url: string) => {
  // 这里增加不需要登录的接口判断
  let loginState = getRecoil(LoginStateAtom);
  /** B 管理台接口强制要求登录态 */
  if (
    !loginState &&
    !NO_LOGIN_WHITE_LIST.includes(queryRealUrl(url)) &&
    !CLIENT_WHITE_LIST.includes(queryRealUrl(url))
  ) {
    const loginApi = getRecoil(LoginApiAtom);
    if (!loginApi) throw new Error('loginApi is null');

    const [, beforeLoginResult] = await to(loginApi.fetchLoginInfo());
    if (!beforeLoginResult) {
      await loginApi.goToLogin();
      const [, result] = await to(loginApi.fetchLoginInfo());
      if (result) {
        loginState = result.loginSession;
      }
    } else {
      loginState = beforeLoginResult.loginSession;
    }
  }

  let xTencentLoginCheck = loginState?.strinifyLoginInfo || '';
  // 指定客户端接口且在开播过程需要c_sid登录态
  if (runningInClient) {
    const pushTaskConfig = getRecoil(ClientLive);
    if (
      pushTaskConfig.length &&
      CLIENT_WHITE_LIST.includes(queryRealUrl(url))
    ) {
      const extraData = JSON.parse(pushTaskConfig[0].config.extra || '{}');
      const loginInfo = JSON.parse(loginState?.strinifyLoginInfo || '{}');
      xTencentLoginCheck = JSON.stringify({
        ...loginInfo,
        client_sid: extraData.clientSid,
      });
    }
  }
  return xTencentLoginCheck;
};

export function getUUID(): string {
  let d = Date.now();
  if (
    typeof performance !== 'undefined' &&
    typeof performance.now === 'function'
  ) {
    d += performance.now(); // use high-precision timer if available
  }
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
}
