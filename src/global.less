.pagedoo-meta-live-global {

  //  按钮
  .gradient-default {
    font-size: 14px;
    font-weight: 400;
    border: none;
    background: linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 100%);

    .t-button__text {
      color: rgba(0, 0, 0, 0.9);
      //color: rgba(1, 83, 255, 1);
    }

    &:hover {
      // filter: brightness(1.1);
      background: linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 30%);
    }

    // &:active {
    //   filter: brightness(0.9);
    // }
  }


  .gradient-primary {
    font-size: 14px;
    border: none;
    background: linear-gradient(88deg,
        #0153ff -0.01%,
        #2e7ffd 49.89%,
        #c1a3fd 100%);

    .t-button__text {
      color: #fff;
    }

    &.t-is-disabled {
      opacity: 0.32;
    }

    &:hover {
      filter: brightness(1.1);
    }

    &:active {
      filter: brightness(0.9);
    }
  }

  .gradient-default_checkbox {
    &.t-checkbox.t-is-checked .t-checkbox__input {
      background: linear-gradient(88deg,
          #0153ff -0.01%,
          #2e7ffd 49.89%,
          #c1a3fd 100%) !important;
      border: none !important;
      transition: background 0.2s cubic-bezier(0.82, 0, 1, 0.9) !important;

      &::after {
        top: 7px;
        left: 4px;
      }
    }
  }

  //  文案
  .gradient-primary-text {
    background: linear-gradient(89deg, #0153ff -0.01%, #8649ff 147.74%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.meta-live-gradient-default-button {
  &:extend(.pagedoo-meta-live-global .gradient-default);

  .t-button__text {
    color: rgba(0, 0, 0, 0.9);
    //color: rgba(1, 83, 255, 1);
  }

  &:hover {
    filter: brightness(1.1);
  }

  &:active {
    filter: brightness(0.9);
  }
}

.meta-live-gradient-light-button {
  &:extend(.pagedoo-meta-live-global .gradient-primary);

  background: var(--454,
      linear-gradient(88deg, #c2d6ff -0.01%, #cde0ff 49.89%, #f0e9ff 99.99%));

  .t-button__text {
    color: #0153ff;
  }
}

.btn-background-primary {
  background: linear-gradient(88.08deg,
      #0153ff -0.01%,
      #2e7ffd 49.89%,
      #c1a3fd 99.99%);
}

.btn-background-brand2 {
  background: var(--454,
      linear-gradient(88deg, #c2d6ff -0.01%, #cde0ff 49.89%, #f0e9ff 99.99%));
}

.btn-background-brand1 {
  background: var(--brand-brand1-light1,
      linear-gradient(88deg, #e0eaff 0%, #e2efff 46.96%, #f5f3ff 99.81%));
}

.btn-background-light {
  background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
}

.btn-background-grey {
  background: var(--Gray-Gray2-normal,
      linear-gradient(83deg, #f6f7fb -0.02%, #fbf8fb 99.98%));
}

// 预览弹窗
.previewVideo-dialog {
  .t-dialog--default {
    padding: 0px;

    .t-dialog__header {
      padding: 20px;
      border-bottom: 1px solid #e8e8e8;
    }

    .t-dialog__body {
      margin: 20px;
      width: 820px;
      height: 460px;
      background: rgba(19, 21, 27, 1);
      border-radius: 4px;

      .video {
        width: 100%;
        height: 100%;
      }
    }

    .t-dialog__footer {
      padding: 20px;
    }
  }
}

.t-steps-item--process {
  .t-steps-item__icon--number {
    border: none;
    background: linear-gradient(88deg,
        #0153ff -0.01%,
        #2e7ffd 49.89%,
        #c1a3fd 100%);
  }

  .t-steps-item__title {
    background: linear-gradient(89deg, #0153ff -0.01%, #8649ff 147.74%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.tdesign {
  &-disable__ripple {
    .t-ripple {
      display: none !important;
      opacity: 0 !important;
    }
  }
}