/**
 * <AUTHOR>
 * @date 2025/2/27 17:00
 * @desc MuseTopBar
 */

import React from 'react';
import { MuseNavBar } from '@/pages/MuseLogin/MuseNavBar';
import { useNavigate } from 'react-router-dom';
import { LOGIN_STEP } from '@/pages/MuseLogin';
import { useRecoilState } from 'recoil';
import { LoginApiAtom } from '@/model/api';

export function MuseTopBar() {
  const navigate = useNavigate();
  const [loginApi] = useRecoilState(LoginApiAtom);
  return (
    <MuseNavBar
      logoUrl="https://avatarcdn.pay.qq.com/material/c71b2b525eed25b268a2951d001cfcea.png"
      useTransitionStyle
      onRegister={() => {
        navigate(`/?step=${LOGIN_STEP.REGISTER}`);
      }}
      onLoginOut={() => {
        loginApi?.goToLoginOut({
          disableRedirect: true,
        });
        navigate(`/`);
      }}
    />
  );
}
