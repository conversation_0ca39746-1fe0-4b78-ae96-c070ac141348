import React, { useEffect, useState } from 'react';
import { Menu, Button } from 'tdesign-react';
import { ViewListIcon } from 'tdesign-icons-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
const { MenuGroup, MenuItem } = Menu;
export const {
  layoutConf: { LEFT_MENU_LIST },
} = MatchedGlobalConfigItem;

function matchMenuActivityValue(value: string) {
  let resValue = '';
  LEFT_MENU_LIST.map((menu) => {
    menu.children.map((sub_menu) => {
      const matchList = [...(sub_menu?.matchPath || []), sub_menu.value];
      if (matchList.includes(value)) {
        resValue = sub_menu.value;
      }
    });
  });
  return resValue;
}

function LeftMenu() {
  const navigate = useNavigate();
  const [value, setValue] = useState('');
  const [collapsed, setCollapsed] = useState(false);

  const handleChange = (link: string) => {
    navigate(link);
    setValue(link);
  };

  const location = useLocation();

  useEffect(() => {
    setValue(matchMenuActivityValue(location.pathname));
  }, [location]);

  return (
    <Menu
      value={value}
      // width="330"
      style={{ borderRight: '1px solid #eee' }}
      onChange={(value) => handleChange(value as string)}
      collapsed={collapsed}
      operations={
        <Button
          variant="text"
          shape="square"
          icon={<ViewListIcon />}
          onClick={() => setCollapsed(!collapsed)}
        />
      }
    >
      {LEFT_MENU_LIST.map((menu) => (
        <MenuGroup title={menu.title} key={menu.title}>
          {menu.children.map((sub_menu) => (
            <MenuItem
              value={sub_menu.value}
              icon={
                value === sub_menu.value ? sub_menu.selectedIcon : sub_menu.icon
              }
              key={sub_menu.value}
            >
              {sub_menu.title}
            </MenuItem>
          ))}
        </MenuGroup>
      ))}
      {/* <MenuGroup title="直播">
        <MenuItem value="/live-template" icon={<PlayCircleIcon />}>
          直播创建
        </MenuItem>
        <MenuItem value="/live-list" icon={<PlayCircleStrokeIcon />}>
          直播管理
        </MenuItem>
      </MenuGroup>
      <MenuGroup title="短视频">
        <MenuItem value="/video-template" icon={<VideoCamera2Icon />}>
          视频创建
        </MenuItem>
        <MenuItem value="/video-list" icon={<VideoLibraryIcon />}>
          视频管理
        </MenuItem>
      </MenuGroup>
      <MenuGroup title="数字人">
        <MenuItem value="/virtual-image" icon={<GiftIcon />}>
          形象定制
        </MenuItem>
        <MenuItem value="/voice-list" icon={<SensorsIcon />}>
          声音定制
        </MenuItem>
      </MenuGroup>
      <MenuGroup title="素材">
        <MenuItem value="/script-list" icon={<Dividers1Icon />}>
          脚本
        </MenuItem>
        <MenuItem value="/qa-lib" icon={<QuestionnaireIcon />}>
          互动问答
        </MenuItem>
      </MenuGroup> */}
    </Menu>
  );
}

export default LeftMenu;
