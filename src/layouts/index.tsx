/**
 * <AUTHOR>
 * @date 2024/4/5 14:11
 * @desc 布局
 */

import React, { PropsWithChildren, Suspense, useContext } from 'react';
import { Outlet } from 'react-router-dom';
import { useRecoilState } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { Topbar } from '@/components/Layout';
import { useMount } from 'ahooks';
import { LoginContext } from '@/hooks/login';
import './index.less';
import to from 'await-to-js';
import LeftMenu from '@/layouts/LeftMenu';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { Loading } from 'tdesign-react';
import { css } from '@emotion/react';
import TaskNotifier from '@/pages/videoMaterialList/components/TaskNotifier';
import { MuseTopBar } from '@/layouts/MuseTopBar';
import { UserInfoAtom } from '@/model/user';
import { useMuseRouterDefend } from '@/pages/MuseLogin/hooks/useMuseRouterDefend';
import { useAccountInit } from '@/pages/MuseLogin/hooks/useAccountInit';
import { useAvatarRouterDefend } from '@/pages/Index/ApplicationSelect/useAvatarRouterDefend';

type IProps = Pick<ILoginWrapperProps, 'disableAutoLogin' | 'renderCustom'>;

const className = 'pagedoo-avatar-layout-comp';

export function Layouts() {
  const { layoutConf } = MatchedGlobalConfigItem;
  const [userState] = useRecoilState(UserInfoAtom);
  return (
    <div
      className={`${className} pagedoo-meta-live-global`}
      style={{ display: 'flex', height: '100vh', flexDirection: 'column' }}
    >
      {import.meta.env.VITE_RUNNING_SYSTEM === 'AD' ? (
        <MuseTopBar />
      ) : (
        <Topbar />
      )}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          background:
            'linear-gradient(85.79deg, #F5F6FA 23.6%, #FDF8FA 142.16%)',
        }}
      >
        <div className={`${className}-default-content`}>
          {layoutConf.leftMenu && <LeftMenu />}
          <div
            className="slot-wrap"
            style={{
              position: 'relative',
            }}
          >
            <Suspense
              fallback={
                <Loading
                  css={css`
                    top: 50%;
                    position: absolute;
                    transform: translateX(-50%) translateY(-50%);
                    left: 50%;
                  `}
                />
              }
            >
              <Outlet key={userState?.adExtend?.account_id ?? 'default'} />
            </Suspense>
          </div>
        </div>
        <TaskNotifier />
      </div>
    </div>
  );
}

export default function LayoutsWithLogin(props: IProps) {
  const { disableAutoLogin, renderCustom } = props;
  return (
    <LoginWrapper
      disableAutoLogin={disableAutoLogin}
      renderCustom={renderCustom}
    >
      <Layouts />
    </LoginWrapper>
  );
}
LayoutsWithLogin.displayName = 'LayoutsWithLogin';

export interface ILoginWrapperProps {
  // 是否禁用自动登录跳转
  disableAutoLogin?: boolean;
  /**
   * 优先渲染，如果返回null继续走原来的逻辑
   * @returns
   */
  renderCustom?: (old: React.ReactNode) => React.ReactNode;
}

export function LoginWrapper(props: PropsWithChildren<ILoginWrapperProps>) {
  const { children, disableAutoLogin, renderCustom } = props;
  const [state] = useRecoilState(LoginStateAtom);
  const loginContext = useContext(LoginContext);

  const allowRender = state || disableAutoLogin;

  // 广告路由守卫
  useMuseRouterDefend();
  // 聚合版路由守卫
  useAvatarRouterDefend();
  // 广告账号初始化
  useAccountInit();

  useMount(async () => {
    if (!loginContext || loginContext.isFetchingLogin) return;
    if (state) return;
    const [err] = await to(loginContext?.fetchLoginInfo());
    if (err) {
      // 报错跳转登录
      !disableAutoLogin && loginContext.goToLogin();
    }
  });
  const el = allowRender
    ? children || (
        <Suspense
          fallback={
            <Loading
              css={css`
                top: 50%;
                position: absolute;
                transform: translateX(-50%) translateY(-50%);
                left: 50%;
              `}
            />
          }
        >
          <Outlet />
        </Suspense>
      )
    : null;
  if (typeof renderCustom === 'function' && allowRender)
    return renderCustom(el);
  // return allowRender ? children || <Outlet /> : null;
  return el;
}

LoginWrapper.displayName = 'LoginWrapper';
