@import "../components/Style/mixins/_scrollbar.less";

.pagedoo-avatar-layout-comp {

  &-default-content {
    height: 100%;
    display: flex;

    .slot-wrap {
      padding: 20px;
      flex: 1;
      height: 100%;
      overflow: auto;
      .scrollbar();
    }
    .main_content {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .t-menu-group__title:empty {
      display: none;
    }

    // 侧边栏收起里，隐藏底部图标按钮的上边框
    .t-default-menu.t-is-collapsed .t-menu__operations:not(:empty) {
      border-top-color: transparent;
    }

    // fix the sidebar selection color error
    .t-menu__content {
      &::selection {
        background-color: #1890ff; // chrome browser default selection color
      }
    }
  }
}

.t-default-menu .t-menu__item.t-is-active:not(.t-is-opened) {
  color: var(--brand-brand7-, #0047F9);
}
