/**
 * 覆盖类型，覆盖对象中某个 key 的类型
 *
 * @example
 * type A = { a: number, b: string };
 * type B = { b: boolean, c: string };
 * type C = Override<A, B>; // { a: number, b: boolean, c: string }
 */
export type Override<P, S> = Omit<P, keyof S> & S;

/**
 * 枚举转数组
 *
 * 该类型用于将枚举类型转换为一个包含枚举值的数组类型。
 *
 * @example
 * enum Colors {
 *   Red = 'Red',
 *   Green = 'Green',
 *   Blue = 'Blue'
 * }
 * type ColorsArray = EnumToArray<typeof Colors>; // ["Red", "Green", "Blue"]
 */
export type EnumToArray<T extends object> = T[keyof T][];

/**
 * 枚举转联合类型
 *
 * 该类型用于将枚举类型转换为联合类型。
 *
 * @example
 * enum Status {
 *   Active = 'active',
 *   Inactive = 'inactive',
 *   Pending = 'pending'
 * }
 * type StatusUnion = EnumToUnion<typeof Status>; // 'active' | 'inactive' | 'pending'
 */
export type EnumToUnion<T extends object> = T[keyof T];

/**
 * 可选属性
 *
 * 将类型的所有属性变为可选。
 *
 * @example
 * type A = { a: number, b: string };
 * type PartialA = PartialProps<A>; // { a?: number, b?: string }
 */
export type PartialProps<T> = {
  [P in keyof T]?: T[P];
};

/**
 * 必选属性
 *
 * 将类型的所有属性变为必选。
 *
 * @example
 * type A = { a?: number, b?: string };
 * type RequiredA = RequiredProps<A>; // { a: number, b: string }
 */
export type RequiredProps<T> = {
  [P in keyof T]-?: T[P];
};

/**
 * 只读属性
 *
 * 将类型的所有属性变为只读。
 *
 * @example
 * type A = { a: number, b: string };
 * type ReadonlyA = ReadonlyProps<A>; // { readonly a: number, readonly b: string }
 */
export type ReadonlyProps<T> = {
  readonly [P in keyof T]: T[P];
};

/**
 * 将只读变可改
 */
export type Mutable<T> = {
  -readonly [P in keyof T]: T[P];
};
/** 提取组件Props类型 */
export type PickComponentProps<T extends React.FC<any>> = T extends React.FC<
  infer R
>
  ? R
  : never;
