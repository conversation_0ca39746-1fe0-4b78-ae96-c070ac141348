import { QAGenerateRequest, QAGenerateResponse } from '@/type/pagedoo';
import { Development } from '@/pb/pb';
import { uuid } from '@tencent/midas-util';

const requestAI = <Req, Res>(
  npc_id: string
): ((request: Req) => Promise<Res>) & { npc_id: string } => {
  const fn = async (request: Req): Promise<Res> => {
    const res = await Development.CreateNpcJobSync({
      npc_id,
      model_input_content: JSON.stringify(request),
      job_id: uuid(),
    });
    return JSON.parse(res.model_return_content);
  };
  fn.npc_id = npc_id;
  return fn;
};

export const AIService = {
  // QA问答库
  pagedoo_0039: requestAI<QAGenerateRequest, QAGenerateResponse>(
    'pagedoo_0039'
  ),
};
