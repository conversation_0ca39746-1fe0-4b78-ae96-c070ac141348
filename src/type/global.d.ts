import { MATERIAL_TYPE } from '@/configs/upload';
import { LogMethod } from '@/utils/monitor/typings';
import { PageEditorRef } from '@tencent/pagedoo-editor';
import type Aegis from 'aegis-web-sdk';
import { PlayConfig } from '@/type/pagedoo';
import type COS from 'cos-js-sdk-v5';
import type { IProductEditorDrawerInstance } from '@/pages/ADPages/ProductLib/ProductEditDrawer';
import { AvatarClientBridge } from '@tencent/avatar-client-bridge-type';

declare global {
  interface Window {
    __page_editor: {
      refHandle(): PageEditorRef;
    };
    __pagedoo_play: {
      getPlayConfig: () => PlayConfig;
      setPlayConfig: (config: PlayConfig) => void;
    };
    avatarClientBridge?: AvatarClientBridge;
  }
}

declare global {
  interface Window {
    __pagedoo_api?:
      | IPagedooToBMaterialsWindowApi
      | IPagedooTOCMaterialWindowApi;

    // 用于本地调试素材库使用
    __material_debug?: {
      openLocalEditor: (url: string) => void;
    };

    Aegis?: typeof Aegis;
  }
}

interface IPagedooCommonMaterialWindowApi {
  reportLog: LogMethod;
}

// B端暴露给素材库的api
interface IPagedooToBMaterialsWindowApi
  extends IPagedooCommonMaterialWindowApi {
  // 上传文件返回文件的cdn地址
  uploadFile: (
    type: MATERIAL_TYPE,
    file: File,
    options?: {
      // 自定义文件名
      fileName?: string;
      // 上传进度回调
      onProgress?: (progress: {
        loaded: number;
        total: number;
        speed: number;
        percent: number;
      }) => void;
      abortSignal?: AbortSignal;
      onReady?: () => void;
      // 需要执行转码前进行回调
      onBeforeTranscode?: (data: {
        type: MATERIAL_TYPE;
        inputPath: string;
        codec: string;
      }) => void;
      onTranscodeProgress?: (data: {
        type: MATERIAL_TYPE;
        progress: number;
        inputPath: string;
        outputPath: string;
      }) => void;
    }
  ) => Promise<{
    remoteUri: string;
    key: string;
    resultCode: string;
  }>;
  /**
   *  获取管理台B端登录态
   * @returns
   * @throws
   */
  getLogin: (options?: { force?: boolean }) => Promise<{
    stringifyLoginInfo: string;
    userId: string;
  }>;
  /**
   *  获取管理台B端登录态 同步版本 Sync调用
   */
  getLoginSync: (options?: {}) => {
    stringifyLoginInfo: string;
    userId: string;
  };

  // 打开管理台页面
  openPage: (page: string) => Promise<void>;

  adProductEditorApi?: IProductEditorDrawerInstance | null;
}

// C端暴露给素材库的api
type IPagedooTOCMaterialWindowApi = IPagedooCommonMaterialWindowApi;
