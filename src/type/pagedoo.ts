// noinspection NonAsciiCharacters

import {
  PagedooMultiImage,
  PagedooSingleImage,
  PagedooSingleVideo,
  SingleVersionPage,
} from '@tencent/pagedoo-library';
import type { PagedooPlayConfig } from '@tencent/pagedoo-time-navigator/es/type';
import { LiveEditorOrigin } from '@/pages/Editor/common/openEditor';
import {
  ADLiveExtendFormValue,
  ProductOption,
} from '@/pages/ADMetaList/ADSelectProductModal';
import { DipListItem } from '@/type/api';
import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';
import { BaseScript } from '@/components/ScriptForm/type';

export interface QAGenerateRequest {
  input_string: string;
  question_list: string[];
  // question: string;
}

export interface QAGenerateResponse {
  answer: string;
  generated_replies?: string[];
}

export const DEFAULT_SCRIPT_VIEWS: BaseScript['views'][number] = {
  viewType: '概述',
  viewName: '',
  viewContent: '',
  duration: '',
  viewMainText: '',
  speech: '',
  flowerText: '',
  isInteractive: '是',
  imageList: [],
};

export type resourceItem = {
  resource_id: string;
  resource_name: string;
  resource_description: string;
  image_address: string;
  audio_address: string;
  duration?: string;
};

export type LineItem = {
  画面名称: string;
  台词文案: string;
};

/**
 * Script 结构体
 * @description 适用于模板生成器入参，将模板组成通用的Script对象送入模板生成器可得到一个模板实例。业务字段请使用可选字段！！！！！
 * 请勿增加必填字段 增加的字段请使用可选字段
 * @deprecated 已弃用
 */
export type Script = {
  // npcId
  npcId: string;
  // 全局背景图片
  backgroundImage: PagedooSingleImage;
  // 全局话术文本
  globalSpeech?: string;
  // 全局配置
  globalField?: unknown;
  // 画面
  views: {
    /**
     * 请勿增加必填字段 增加的字段请使用可选字段
     */
    画面类型: '封面' | '概述' | '分点概述' | 'QA' | '结束语' | '结尾' | string;
    画面名称: '开场' | string;
    // 画面大纲
    画面内容: '亲爱的姐妹们，大家好！欢迎来到我们的直播间！' | string;
    时长: '00:00-00:10' | string;
    台词文案:
      | '今天我要给大家安利一款真正值得信赖的大品牌抗皱面膜——敷尔佳抗皱面膜！'
      | string;
    // 花字
    文本贴纸: string;
    // 是否是互动画面
    互动画面: '是' | '否';
    // 互动开始文案
    互动开始: string;
    // 互动结束文案
    互动结束: string;
    // 商品图片
    productImage: PagedooSingleImage;
    // 背景图片
    backgroundImage: PagedooSingleImage;
    // 前景图片
    foregroundImage?: PagedooSingleImage;
    // 视频
    video?: PagedooSingleVideo;
    // 数字人
    virtualman?: {
      key: string;
      label: string;
    };
    subVirtualman?: {
      key: string;
      label: string;
    };
    // 数字人类型（口播或互动）
    type?: 'default' | 'comment';
    // 转场图片
    transitionImage?: PagedooSingleImage;
    // 触发 Midasbuy 事件
    event:
      | 'PUSH_LIVE_POP_AD_STOP'
      | 'PUSH_LIVE_POP_AD_START'
      | 'START_LUCKY_BAG_LOTTERY'
      | '';
    // 事件参数
    eventParameter: {
      mount?: Record<string, unknown>;
      unmount?: Record<string, unknown>;
    };
    luckyBagEventParameter?: {
      mount?: Record<string, unknown>;
    };
    // 塔西音色情绪
    soundemotion?:
      | '默认'
      | '互动引导'
      | '下播'
      | '转折'
      | '打招呼'
      | '打游戏ing'
      | '赢了游戏'
      | '输了游戏';
    // 画面标题
    title?: string;
    // 关键短句
    summary?: string;
    // 画面正文
    画面正文: string;
    /**
     * 请勿增加必填字段 增加的字段请使用可选字段
     */
    // 画面类型目前只有讲师有
    //   配图
    imageList?: PagedooMultiImage;
    // 资源
    resources?: Record<string, Array<resourceItem>>;
  }[];
  /**
   * 请勿增加必填字段 增加的字段请使用可选字段
   */
  type: LiveEditorOrigin;
  // 分辨率
  size: [width: number, height: number];
  // 广告的扩展数据 type为ad_script时候有
  adExtendData?: {
    productList?: ProductOption[];
    liveMode?: ADLiveExtendFormValue['liveMode'];
    templateId: string;
    // 直播id
    liveId: string;
    // 从形象库选择并创建直播间时设置的默认数字人信息，生成模版时需要对数字人配置进行替换
    defaultDipItem?: DipListItem;
    defaultVoiceItem?: IVoiceItem;
    /**
     * 用于附加到组件的数据
     */
    updateComponentData?: Record<string, Record<string, unknown>>;
  };
  // 游戏推广的扩展数据 type为game_script时候有
  gamePromotionExtendData?: {
    // 台词列表
    lineList?: LineItem[];
  };
};

export type PlayConfig = PagedooPlayConfig<{
  // 短视频id/直播间id 在没有生成id时使用undefined或空字符串
  id?: string;
  // 画面宽px，画面高px
  size?: [number, number];
}>;

export interface PlayService {
  getPlayScript(script: BaseScript): Promise<PlayConfig>;

  // 通过脚本获取播放的配置
  getPageSchema(script: Script): Promise<{
    global: {
      pages: [{ page_id: string; data: SingleVersionPage }];
      'pagedoo-play-script': PlayConfig;
      [x: string]: unknown;
    };
  }>;

  getPageSchemaFromConfig(playConfig: PlayConfig): Promise<{
    global: {
      pages: [{ page_id: string; data: SingleVersionPage }];
      'pagedoo-play-script': PlayConfig;
      [x: string]: unknown;
    };
  }>;

  // 通过脚本获取播放第一帧的global
  getPageSchemaShot(script: Script): Promise<{
    global: {
      pages: [{ page_id: string; data: SingleVersionPage }];
      [x: string]: unknown;
    };
  }>;

  getPageSchemaShotFromConfig(playScript: PlayConfig): Promise<{
    global: {
      pages: [{ page_id: string; data: SingleVersionPage }];
      [x: string]: unknown;
    };
  }>;
  // 通过脚本和模板 id 生成协议
  genPlayScriptByTemplate({
    script,
    templateId,
    isLive,
    isVideo,
  }: {
    script: BaseScript;
    templateId: string;
    isLive?: boolean;
    isVideo?: boolean;
  }): Promise<PlayConfig>;
}
