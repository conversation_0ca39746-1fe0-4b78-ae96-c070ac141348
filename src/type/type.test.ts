import {
  Override,
  EnumToArray,
  EnumToUnion,
  PartialProps,
  RequiredProps,
  ReadonlyProps,
} from './common';

// Utility type to assert that two types are equal
type AssertEqual<T, Expected> = T extends Expected
  ? Expected extends T
    ? true
    : false
  : false;

// Override类型测试
type TestOverrideA = { a: number; b: string };
type TestOverrideB = { b: boolean; c: string };
type TestOverrideC = Override<TestOverrideA, TestOverrideB>; // { a: number, b: boolean, c: string }

type TestOverrideCExpected = { a: number; b: boolean; c: string };
type TestOverrideCResult = AssertEqual<TestOverrideC, TestOverrideCExpected>;
const testOverrideCResult: TestOverrideCResult = true; // If not `true`, TypeScript will show a type error

// EnumToArray类型测试
enum TestColors {
  Red = 'Red',
  Green = 'Green',
  Blue = 'Blue',
}
type TestColorsArray = EnumToArray<typeof TestColors>; // ["Red", "Green", "Blue"]

type TestColorsArrayExpected = (typeof TestColors)[keyof typeof TestColors][];
type TestColorsArrayResult = AssertEqual<
  TestColorsArray,
  TestColorsArrayExpected
>;
const testColorsArrayResult: TestColorsArrayResult = true; // If not `true`, TypeScript will show a type error

// EnumToUnion类型测试
enum TestStatus {
  Active = 'active',
  Inactive = 'inactive',
  Pending = 'pending',
}
type TestStatusUnion = EnumToUnion<typeof TestStatus>; // 'active' | 'inactive' | 'pending'

type TestStatusUnionExpected =
  | TestStatus.Active
  | TestStatus.Inactive
  | TestStatus.Pending;

type TestStatusUnionResult = AssertEqual<
  TestStatusUnion,
  TestStatusUnionExpected
>;
const testStatusUnionResult: TestStatusUnionResult = true; // If not `true`, TypeScript will show a type error

// PartialProps类型测试
type TestPartial = PartialProps<{ a: number; b: string }>; // { a?: number, b?: string }

type TestPartialExpected = { a?: number; b?: string };
type TestPartialResult = AssertEqual<TestPartial, TestPartialExpected>;
const testPartialResult: TestPartialResult = true; // If not `true`, TypeScript will show a type error

// RequiredProps类型测试
type TestRequired = RequiredProps<{ a?: number; b?: string }>; // { a: number, b: string }

type TestRequiredExpected = { a: number; b: string };
type TestRequiredResult = AssertEqual<TestRequired, TestRequiredExpected>;
const testRequiredResult: TestRequiredResult = true; // If not `true`, TypeScript will show a type error

// ReadonlyProps类型测试
type TestReadonly = ReadonlyProps<{ a: number; b: string }>; // { readonly a: number, readonly b: string }

type TestReadonlyExpected = { readonly a: number; readonly b: string };
type TestReadonlyResult = AssertEqual<TestReadonly, TestReadonlyExpected>;
const testReadonlyResult: TestReadonlyResult = true; // If not `true`, TypeScript will show a type error
