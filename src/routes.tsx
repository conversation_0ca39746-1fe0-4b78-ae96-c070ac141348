import LayoutsWithLogin, { LoginWrapper } from '@/layouts';
import { lazy } from 'react';
import { RouteObject, useRoutes } from 'react-router-dom';
import { VideoList } from '@/pages/VideoList';
import { VideoTemplate } from '@/pages/VideoTemplate';
import { IndexPage } from '@/pages/Index';
import { LiveList } from '@/pages/LiveList';
const EditorEntry = lazy(() => import('@/pages/Editor/SimpleEditor'));
const Question = lazy(() => import('@/pages/Question'));
const QALib = lazy(() => import('@/pages/QALib'));
const ScriptList = lazy(() => import('@/pages/ScriptList'));
const CreateScript = lazy(() => import('@/pages/ScriptList/Create'));
const VirtualImageAdd = lazy(() => import('@/pages/VirtualImage/VirtualAdd'));
const VoiceAdd = lazy(() => import('@/pages/VoiceList/VoiceAdd'));
const ImageDetail = lazy(() => import('@/pages/VirtualImage/ImageDetail'));
const LiveTemplate = lazy(() => import('@/pages/LiveTemplate'));
const VisionCreate = lazy(() => import('@/pages/ScriptList/VisionCreate'));
const Workbench = lazy(() => import('@/pages/Workbench'));
const WorkbenchVideo = lazy(() => import('@/pages/WorkbenchVideo'));
const VirtualImageWrapper = lazy(() => import('@/pages/VirtualImage/wrapper'));
const VoiceListWrapper = lazy(() => import('@/pages/VoiceList/wrapper'));
const VideoMaterialList = lazy(() => import('@/pages/videoMaterialList'));
const VideoMaterialCreate = lazy(
  () => import('@/pages/videoMaterialList/create')
);
const VideoTaskResult = lazy(() => import('@/pages/VideoTaskResult'));

export const routes: RouteObject[] = [
  {
    path: '/',
    element: <IndexPage />,
  },
  {
    path: '/Editor',
    element: <LoginWrapper disableAutoLogin />,
    children: [
      {
        path: '',
        element: <EditorEntry />,
      },
    ],
    handle: {
      reportPageId: 'editor',
    },
  },
  {
    path: '/question',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <Question />,
        // element: <LiveCreate />,
      },
    ],
    handle: {
      reportPageId: 'ResearchPage',
    },
  },
  {
    path: '/live-template',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <LiveTemplate />,
      },
    ],
    handle: {
      reportPageId: 'ResearchPage',
    },
  },
  {
    path: '/qa-lib',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <QALib />,
      },
    ],
    handle: {
      reportPageId: 'qa-lib',
    },
  },
  {
    path: '/video-template',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VideoTemplate />,
      },
    ],
    handle: {
      reportPageId: 'VideoTemplate',
    },
  },
  {
    path: '/video-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VideoList />,
      },
    ],
    handle: {
      reportPageId: 'VideoList',
    },
  },
  {
    path: '/script-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <ScriptList />,
      },
      {
        path: 'create',
        element: <CreateScript />,
      },
      {
        path: 'vision/create',
        element: <VisionCreate />,
      },
    ],
    handle: {
      reportPageId: 'script-list',
    },
  },
  {
    path: '/virtual-image',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VirtualImageWrapper />,
      },
      {
        path: 'detail/:imageId',
        element: <ImageDetail />,
      },
      {
        path: 'add',
        element: <VirtualImageAdd />,
      },
    ],
    handle: {
      reportPageId: 'virtualImage',
    },
  },
  {
    path: '/voice-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VoiceListWrapper />,
      },
      {
        path: 'add',
        element: <VoiceAdd />,
      },
    ],
    handle: {
      reportPageId: 'voiceList',
    },
  },
  {
    path: '/live-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <LiveList />,
      },
    ],
    handle: {
      reportPageId: 'LiveList',
    },
  },
  {
    path: '/workbench',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <Workbench />,
      },
    ],
    handle: {
      reportPageId: 'workbench',
    },
  },
  {
    path: '/workbench-create',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <WorkbenchVideo />,
      },
    ],
    handle: {
      reportPageId: 'workbenchVideo',
    },
  },
  {
    path: '/videoMaterial-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VideoMaterialList />,
      },
    ],
    handle: {
      reportPageId: 'videoMaterialList',
    },
  },
  {
    path: '/videoMaterial-create',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VideoMaterialCreate />,
      },
    ],
    handle: {
      reportPageId: 'videoMaterialCreate',
    },
  },
  {
    path: '/videoMaterial-result',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VideoTaskResult />,
      },
    ],
    handle: {
      reportPageId: 'videoMaterialCreate',
    },
  },
];

function MyRouter() {
  return useRoutes(routes);
}

export default MyRouter;
