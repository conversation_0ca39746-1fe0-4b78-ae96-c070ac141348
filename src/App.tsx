import React from 'react';
import { RecoilRoot } from 'recoil';
import { HashRouter } from 'react-router-dom';
import 'tdesign-react/es/style/index.css';
import MyRouter from '@routes';
import RecoilNexus from 'recoil-nexus';
import GlobalDataWrapper from './components/Global';
import { ClientDialog } from '@/components/ClientDialog';

function App() {
  return (
    <RecoilRoot>
      <RecoilNexus />
      <ClientDialog />
      <HashRouter>
        <GlobalDataWrapper>
          <MyRouter />
        </GlobalDataWrapper>
      </HashRouter>
    </RecoilRoot>
  );
}

export default App;
