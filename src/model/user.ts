import { atom } from 'recoil';

export interface IUserInfoAtom {
  avatar: string;
  nickname: string;
  // 聚合版扩展信息
  pagedooExtend?: {
    account_id: string;
  };
  // 广告扩展用户信息
  adExtend?: {
    account_id: string;
    login_type: 'qq' | 'wx' | 'qq_error';
    roles: string[];
    appType: string;
  };
}

export const UserInfoAtom = atom<IUserInfoAtom | undefined>({
  key: 'UserInfo',
  default: undefined,
});
