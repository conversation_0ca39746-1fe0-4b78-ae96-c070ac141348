import { LoginTypes } from '@/utils/login/typings';
import { SessionParam } from '@tencent/midas-util/lib/session';
import { atom } from 'recoil';

export interface ILoginSession extends SessionParam<string, string> {
  offer_id: string;
  openid: string;
  app_id: string;
  type?: LoginTypes;
  strinifyLoginInfo: string;
}

export const LoginStateAtom = atom<ILoginSession | undefined>({
  key: 'LoginState',
  default: undefined,
});
