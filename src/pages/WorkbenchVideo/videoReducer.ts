import { ContextProps } from './videoContext';

export const initialState: ContextProps['state'] = {
  create: {},
  templateSelect: {},
  scriptInfoCollect: {},
};
type State = typeof initialState;

export const ACTION = {
  GET: 'GET',
  SET: 'SET',
  CREATE: 'CREATE',
  TEMPLATE_SELECT: 'TEMPLATE_SELECT',
  SCRIPT_INFO_COLLECT: 'SCRIPT_INFO_COLLECT',
} as const;

type GETAction = {
  type: typeof ACTION.GET;
};

type SetAction = {
  type: typeof ACTION.SET;
  payload: Partial<State>;
};

type CreateAction = {
  type: typeof ACTION.CREATE;
  payload: Partial<State['create']>;
};

type TemplateSelectAction = {
  type: typeof ACTION.TEMPLATE_SELECT;
  payload: Partial<State['templateSelect']>;
};

type scriptInfoCollectAction = {
  type: typeof ACTION.SCRIPT_INFO_COLLECT;
  payload: Partial<State['scriptInfoCollect']>;
};

export type Action =
  | GETAction
  | SetAction
  | CreateAction
  | TemplateSelectAction
  | scriptInfoCollectAction;

export const reducer = (state: State, action: Action) => {
  switch (action.type) {
    case ACTION.GET:
      return { ...state };
    case ACTION.SET:
      return { ...state, ...action.payload };
    case ACTION.CREATE:
      return { ...state, create: action.payload };
    case ACTION.TEMPLATE_SELECT:
      return {
        ...state,
        templateSelect: action.payload,
      };
    case ACTION.SCRIPT_INFO_COLLECT:
      return {
        ...state,
        scriptInfoCollect: action.payload,
      };
    default:
      throw new Error();
  }
};
