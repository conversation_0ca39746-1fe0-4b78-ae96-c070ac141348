@import "../../components/Style/mixins/_scrollbar.less";
@mainMinWidth: 960px;
@mainMaxWidth: 1440px;

.workbenchVideo-page {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    height: 100%;
    background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
    min-width: @mainMinWidth;
    max-width: @mainMaxWidth;

    .workbenchVideo-page-step {
        margin: 33px 121px 28px 78px;

        .t-steps .t-steps-item--process .t-steps-item__icon--number {
            background: linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
            border: none;
        }
    }

    .workbenchVideo-page-content {
        flex: 1;
        overflow: auto;
        border-radius: 8px;
        .scrollbar();
    }

    .workbenchVideo-page-footer {
        height: 80px;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        padding-left: 20px;
        box-sizing: border-box;
        margin-top: 10px;
        border-radius: 8px;

        .t-button + .t-button {
            margin-left: 16px;
        }

        .gradient-primary {
            width: 150px;
        }

        .gradient-default {
            width: 110px;
        }
    }
}