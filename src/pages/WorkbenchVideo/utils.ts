import { getParams } from '@/utils/url';
import { CONTETNT_TYPE } from '../Workbench/constants';
import { IVideoCreateMode } from './components/create/utils';

export const stepsList = [
  { title: '创建方式&类型选择', value: 'create' },
  { title: '模版选择', value: 'templateSelect' },
  { title: '脚本信息收集', value: 'scriptInfoCollect' },
  { title: '效果预览', value: 'preview' },
];

/**
 * 获取页面类型
 * @returns 页面类型
 */
export const getContentType = () => {
  const { contentType } = getParams();
  return contentType;
};

// 转换step1选择的视频创建方式兼容旧接口
// export const transform_application_scenarios: {
//   [key in CONTETNT_TYPE]: {
//     [key: string]: string;
//   };
// } = {
//   video: {
//     [IVideoCreateMode.VIDEO]: '3',
//     [IVideoCreateMode.PRODUCT]: '2',
//     [IVideoCreateMode.PPT]: '1',
//     [IVideoCreateMode.ARTICLE]: '1',
//   },
//   live: {
//     [IVideoCreateMode.CONTENT]: '101',
//     [IVideoCreateMode.PRODUCT]: '101',
//   },
// };

// export const transformKey = (type: CONTETNT_TYPE, old: string) => {
//   return transform_application_scenarios[type][old];
// };
