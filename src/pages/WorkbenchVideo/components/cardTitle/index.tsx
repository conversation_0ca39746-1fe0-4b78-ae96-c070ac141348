import React from 'react';
import classnames from 'classnames';
import './index.less';

export interface CardTitleProps {
  title: string;
  desc?: string;
  direction?: 'row' | 'column';
  fontSize?: number;
  className?: string;
}
function CardTitle({
  title,
  desc,
  direction = 'column',
  fontSize = 16,
  className,
}: CardTitleProps) {
  return (
    <div
      className={classnames('components-cardTitle-contanier', className)}
      style={{ display: 'flex', flexDirection: direction, fontSize }}
    >
      <div className="title">{title}</div>
      {desc && <div className={`desc desc-${direction}`}>{desc}</div>}
    </div>
  );
}

export default CardTitle;
