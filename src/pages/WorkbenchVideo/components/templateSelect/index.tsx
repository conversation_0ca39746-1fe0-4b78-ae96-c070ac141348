import React, { useContext, useEffect, useState } from 'react';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import './index.less';
import CardTitle from '../cardTitle';
import TypeSelect from './typeSelect';
import {
  content_list_type_map,
  getVideoTemplateList,
  GetVideoTemplateSelector,
} from './utils';
import { TemplateDataItem } from '@/hooks/template';
import TemplateList from './templateList';
import { VideoContext } from '../../videoContext';
import { useDeepCompareEffect, useEventListener } from 'ahooks';
import { message } from 'tdesign-react';
import { FactoryStep } from '../../constants';

export interface TemplateSelectProps {
  type: CONTETNT_TYPE;
  setCurStep: React.Dispatch<React.SetStateAction<FactoryStep>>;
  nextBtnRef: React.RefObject<HTMLButtonElement>;
}

export interface selectorItem {
  selector_name: string;
  selector_value: string;
}
export interface selectorMap {
  [key: string]: selectorItem[];
}
function TemplateSelect({ type, nextBtnRef, setCurStep }: TemplateSelectProps) {
  const {
    state: { create, templateSelect },
    setTemplateSelect,
  } = useContext(VideoContext);
  const [selectorMap, setSelectorMap] = useState<selectorMap>();
  const [templateList, setTemplateList] = useState<TemplateDataItem[]>([]);
  useEffect(() => {
    (async () => {
      const templateListData = await getVideoTemplateList({
        content_list_type: content_list_type_map.createTemplate[type],
        selectorValue: {
          ...(type === CONTETNT_TYPE.LIVE
            ? {}
            : templateSelect?.selectorValue || {}),
          application_scenarios: create?.videoCreateMode
            ? create?.videoCreateMode
            : '0',
        },
      });
      setTemplateList(templateListData);
    })();
  }, [type, templateSelect, create]);
  useDeepCompareEffect(() => {
    (async () => {
      try {
        const data = await GetVideoTemplateSelector({
          stepType: 'createTemplate',
          type,
        });
        setSelectorMap(data);
      } catch (error) {}
    })();
  }, [type]);
  useEventListener(
    'click',
    async () => {
      if (!templateSelect?.templateId) {
        void message.error('请选择模版');
        return;
      }
      setCurStep((data) => data + 1);
    },
    { target: nextBtnRef }
  );
  return (
    <div className="components-templateSelect">
      <CardTitle
        title="请选择您合适的视频模版"
        desc="基于您希望智能视频中应该具备的要素和样式进行选择"
      />
      {type === CONTETNT_TYPE.VIDEO && (
        <>
          <TypeSelect
            typeList={selectorMap?.scene_specification_id || []}
            cbData={(val) =>
              setTemplateSelect({
                ...templateSelect,
                selectorValue: {
                  ...(templateSelect?.selectorValue || {}),
                  scene_specification_id: val.selector_value,
                },
              })
            }
            defaultType={templateSelect?.selectorValue?.scene_specification_id}
          />
          <div className="typeList-box">
            <TypeSelect
              typeList={selectorMap?.is_have_border || []}
              cbData={(val) =>
                setTemplateSelect({
                  ...templateSelect,
                  selectorValue: {
                    ...(templateSelect?.selectorValue || {}),
                    is_have_border: val.selector_value,
                  },
                })
              }
              typeSelectStyle={{ marginLeft: 20 }}
              defaultType={templateSelect?.selectorValue?.is_have_border}
            />
            <div className="line" />
            <TypeSelect
              typeList={selectorMap?.is_have_captions || []}
              cbData={(val) =>
                setTemplateSelect({
                  ...templateSelect,
                  selectorValue: {
                    ...(templateSelect?.selectorValue || {}),
                    is_have_captions: val.selector_value,
                  },
                })
              }
              defaultType={templateSelect?.selectorValue?.is_have_captions}
            />
          </div>
          <TypeSelect
            typeList={selectorMap?.is_have_digital_human || []}
            cbData={(val) =>
              setTemplateSelect({
                ...templateSelect,
                selectorValue: {
                  ...(templateSelect?.selectorValue || {}),
                  is_have_digital_human: val.selector_value,
                },
              })
            }
            defaultType={templateSelect?.selectorValue?.is_have_digital_human}
          />
        </>
      )}
      <TemplateList
        templateList={templateList}
        templateId={templateSelect?.templateId}
        cbData={(val) =>
          setTemplateSelect({
            ...templateSelect,
            templateId: val.id,
            templatePosterUrl: val.poster_url,
          })
        }
      />
    </div>
  );
}

export default TemplateSelect;
