import React, { useState } from 'react';
import activeIcon from '../assets/active.png';
import classnames from 'classnames';
import './index.less';
import { selectorItem } from '../index';

export interface TypeSelectProps {
  defaultType?: string;
  typeList: selectorItem[];
  cbData: (val: selectorItem) => void;
  className?: string;
  extraTypeList?: Array<selectorItem>;
  typeSelectStyle?: React.CSSProperties;
  typeSelectItemStyle?: React.CSSProperties;
}

function TemplateSelect({
  defaultType,
  typeList,
  cbData,
  className,
  extraTypeList = [],
  typeSelectItemStyle,
  typeSelectStyle,
}: TypeSelectProps) {
  const [selectType, setSelectType] = useState(defaultType || '0');
  return (
    <div
      className={classnames('templateSelect-typeSelect', className)}
      style={{ ...(typeSelectStyle || {}) }}
    >
      {Array.isArray(typeList) &&
        [...(extraTypeList || []), ...typeList].map((item) => (
          <div
            className={`typeSelectItem ${
              item.selector_value === selectType ? 'active-typeSelectItem' : ''
            }`}
            style={{ ...(typeSelectItemStyle || {}) }}
            onClick={() => {
              cbData(item);
              setSelectType(item.selector_value);
            }}
          >
            <svg
              className="selected-arrow"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M2 7L5 10.5L11.5 4" strokeLinejoin="round" />
            </svg>

            {item.selector_name}
          </div>
        ))}
    </div>
  );
}

export default TemplateSelect;
