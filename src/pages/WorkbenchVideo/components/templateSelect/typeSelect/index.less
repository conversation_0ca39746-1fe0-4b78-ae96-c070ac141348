@import "../../../../../components/Style/mixins/variables.less";

.typeSelectItem {
    padding: 8px 10px;
    min-width: 112px;
    // text-align: center;
    border-radius: 50px;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.6);
    margin-right: 8px;
    cursor: pointer;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition:
      --filter-btn-linear-color-1 0.2s ease-in-out,
      --filter-btn-linear-color-2 0.2s ease-in-out,
      --filter-btn-linear-color-3 0.2s ease-in-out,
    ;
  .selected-arrow {
      width: 0;
      margin-right: 0;
      transition: all 0.2s;
  
      path {
        stroke: currentColor;
        stroke-dasharray: 14;
        stroke-dashoffset: 0;
      }
  
      @keyframes dash {
        from {
          stroke-dashoffset: 14;
        }
  
        to {
          stroke-dashoffset: 0;
        }
      }
    }
}

.templateSelect-typeSelect {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .typeSelectItem {
    background-image: linear-gradient(82.56deg, var(--filter-btn-linear-color-1) 0%, var(--filter-btn-linear-color-2) 50%, var(--filter-btn-linear-color-3) 100%);
    // padding: 8px 32px;
    // border-radius: 50px;
    // font-family: PingFang SC;
    // font-size: 14px;
    // font-weight: 400;
    // line-height: 22px;
    // color: rgba(0, 0, 0, 0.6);
    // margin-right: 8px;
    // cursor: pointer;
    // margin-bottom: 12px;
    // display: flex;
    // align-items: center;
    // transition: 
    //     --filter-btn-linear-color-1 0.2s ease-in-out,
    //     --filter-btn-linear-color-2 0.2s ease-in-out,
    //     --filter-btn-linear-color-3 0.2s ease-in-out,
    // ;

    &:hover {
      --filter-btn-linear-color-1: @filterBtnLinearHoverColor1;
      --filter-btn-linear-color-2: @filterBtnLinearHoverColor2;
      --filter-btn-linear-color-3: @filterBtnLinearHoverColor3;
    }

    &.active-typeSelectItem {
      --filter-btn-linear-color-1: @filterBtnLinearHoverColor1;
      --filter-btn-linear-color-2: @filterBtnLinearHoverColor2;
      --filter-btn-linear-color-3: @filterBtnLinearHoverColor3;
      font-weight: 600;
      color: rgba(0, 71, 249, 1);
  
      .selected-arrow {
        width: 14px;
        margin-right: 6px;
  
        path {
          animation: dash .3s linear;
          animation-fill-mode: both;
        }
      }
    }

    .activeIcon {
      width: 16px;
      height: 16px;
      margin-right: 3px;
    }
  }

}

.typeList-box {
  .typeSelectItem {
    // background-image: linear-gradient(87.64deg, 
    // var(--filter-btn-linear-color-1, '#fff') 0%, 
    // var(--filter-btn-linear-color-2, '#fff') 50%, 
    // var(--filter-btn-linear-color-3, '#fff') 100%);
    --filter-btn-linear-color-1: #fff;
    --filter-btn-linear-color-2: #fff;
    --filter-btn-linear-color-3: #fff;

    &.active-typeSelectItem {
      --filter-btn-linear-color-1: @filterBtnLinearHoverColor1;
      --filter-btn-linear-color-2: @filterBtnLinearHoverColor2;
      --filter-btn-linear-color-3: @filterBtnLinearHoverColor3;
    }
  }
}