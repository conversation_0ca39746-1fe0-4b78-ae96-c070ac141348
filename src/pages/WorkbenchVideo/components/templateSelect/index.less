.components-templateSelect {
  width: 100%;
  padding: 20px;
  padding-bottom: 8px;
  border-radius: 8px;
  background-color: #ffffff;
  //height: 100%;
  display: flex;
  flex-direction: column;

  .typeList-box {
    width: calc(100% + 40px);
    background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
    display: flex;
    flex-wrap: wrap;
    padding-top: 12px;
    margin-left: -20px;
    margin-bottom: 12px;

    .line {
      width: 1px;
      height: 22px;
      margin: 5px 8px 0px 0px;
      background-image: url(./assets/line.png);
    }
  }
}
