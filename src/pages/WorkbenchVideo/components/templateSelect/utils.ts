import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { ResourceSvr } from '@/pb/pb';
import { IVideoCreateMode, transformMap } from '../create/utils';
import { CardListItem } from '../create/cardList';
import { selectorMap } from './index';

export const is_have_captions_list = [
  { selector_value: '0', selector_name: '全部' },
  { selector_value: '1', selector_name: '带字幕' },
  { selector_value: '2', selector_name: '不带字幕' },
];

export const is_have_digital_human_list = [
  { selector_value: '0', selector_name: '全部' },
  { selector_value: '1', selector_name: '带数字人' },
  { selector_value: '2', selector_name: '不带数字人' },
];

export const getVideoTemplateList = async ({
  content_list_type,
  selectorValue,
}: {
  content_list_type: number;
  selectorValue?: Record<string, string>;
}) => {
  try {
    const selector_query_list = selectorValue
      ? Object.entries(selectorValue).map(([k, v]) => ({
          selector_id: k,
          selector_value: v,
        }))
      : [];
    const data = await ResourceSvr.GetVideoTemplateList({
      selector_query_list,
      page_num: 1,
      page_size: 999,
      search_key: '',
      content_list_type,
    });
    const templateListData = (data?.video_template_list || []).map((i) => ({
      id: i.video_template_id,
      name: i.video_template_name,
      poster_url: i.video_template_pic_url,
      original_data: i,
    }));
    return templateListData;
  } catch (error) {
    return [];
  }
};

export const content_list_type_map = {
  createRecommend: {
    video: 101, // 表示视频创建视频推荐列表
    live: 102, // 表示直播创建视频推荐列表
  },
  createTemplate: {
    video: 3, // 视频创建视频模版筛选列表
    live: 4, // 直播创建直播筛选列表
  },
};
export interface IGetVideoTemplateSelector1 {
  stepType: 'createRecommend';
  type: CONTETNT_TYPE;
}
export interface IGetVideoTemplateSelector2 {
  stepType: 'createTemplate';
  type: CONTETNT_TYPE;
}
export function GetVideoTemplateSelector({
  stepType,
  type,
}: IGetVideoTemplateSelector1): Promise<Array<CardListItem>>;
export function GetVideoTemplateSelector({
  stepType,
  type,
}: IGetVideoTemplateSelector2): Promise<selectorMap>;
export async function GetVideoTemplateSelector({
  stepType,
  type,
}: IGetVideoTemplateSelector1 | IGetVideoTemplateSelector2): Promise<
  Array<CardListItem> | selectorMap | undefined
> {
  try {
    const data = await ResourceSvr.GetVideoTemplateSelector({
      content_list_type: content_list_type_map[stepType][type],
    });
    // to do 过滤掉直播-商品带货
    if (stepType === 'createRecommend') {
      // to do 先过滤掉ppt和文章
      const selectorClassList = (data?.content_selector_list || []).filter(
        (item) =>
          !(
            [IVideoCreateMode.PRODUCT].includes(
              item.selector_value as IVideoCreateMode
            ) && type === CONTETNT_TYPE.LIVE
          )
      );
      const datalist = selectorClassList.map((item) => {
        const {
          selector_name,
          selector_value,
          selector_id,
          selector_class_list,
        } = item;
        const findVideoTypeData = selector_class_list.find(
          (item) => item.selector_id === 'video_type'
        );
        return {
          selector_id,
          selector_value,
          ...(transformMap[type][selector_value] || {}),
          selector_name:
            transformMap[type][selector_value]?.selector_name || selector_name,
          selector_item_list: findVideoTypeData?.selector_item_list.map(
            (item) => ({
              selector_id: findVideoTypeData?.selector_id,
              ...item,
              ...(transformMap[type][item.selector_value] || {}),
            })
          ),
        };
      });
      return datalist;
    }
    if (stepType === 'createTemplate') {
      const selectorClassList =
        data?.content_selector_list?.[0]?.selector_class_list;
      const createTemplateMap: {
        [key: string]: {
          selector_name: string;
          selector_value: string;
        }[];
      } = {};
      (selectorClassList || []).forEach((item) => {
        const { selector_id, selector_item_list } = item;
        createTemplateMap[selector_id] = selector_item_list;
      });
      return createTemplateMap;
    }
  } catch (error) {
    return stepType === 'createTemplate' ? {} : [];
  }
}

export const GetVideoListBySelector = async ({
  content_list_type,
  selectorValue,
}: {
  content_list_type: number;
  selectorValue?: Record<string, string>;
}) => {
  try {
    const selector_query_list = selectorValue
      ? Object.entries(selectorValue).map(([k, v]) => ({
          selector_id: k,
          selector_value: v,
        }))
      : [];
    const data = await ResourceSvr.GetVideoListBySelector({
      selector_query_list,
      content_list_type,
    });
    return data?.video_info_list || [];
  } catch (error) {
    return [];
  }
};
