import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import './index.less';
import { TemplateDataItem } from '@/hooks/template';
import { Empty } from '@/components/Empty';

export interface TemplateListProps {
  templateList: TemplateDataItem[];
  cbData: (val: TemplateDataItem) => void;
  templateId?: string; // 默认选中的模版
  rowLens?: number; // 一行放几个 默认5个
  className?: string;
}

function TemplateList({
  templateList,
  rowLens = 5,
  cbData,
  templateId,
  className,
}: TemplateListProps) {
  const [selectTemplateId, setSelectTemplateId] = useState<string>();

  useEffect(() => {
    if (templateId) setSelectTemplateId(templateId);
  }, [templateId]);
  return (
    <div className={classnames('templateSelect-templateList', className)}>
      {Array.isArray(templateList) && !!templateList.length ? (
        templateList.map((item) => (
          <div
            className={`templateListItem ${
              selectTemplateId === item.id ? 'active-templateListItem' : ''
            }`}
            style={{ width: `calc(${100 / rowLens}% - 15px)` }}
            key={item.id}
            onClick={() => {
              setSelectTemplateId(item.id);
              cbData(item);
            }}
          >
            <div
              className="templateListItem-img"
              style={{
                background: `url(${item.poster_url})`,
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'contain',
                backgroundPosition: 'center',
                backgroundColor: '#000000',
              }}
            />
            <div className="itemContent">
              <div className="name">{item.name}</div>
            </div>
          </div>
        ))
      ) : (
        <div className="flex-center w-full min-h-[150px]">
          <Empty>暂无数据</Empty>
        </div>
      )}
    </div>
  );
}

export default TemplateList;
