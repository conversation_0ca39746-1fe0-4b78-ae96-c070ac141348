.templateSelect-templateList {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  //overflow: auto;
  align-items: flex-start;

  .templateListItem {
    margin: 2px 10px 10px 2px;
    margin-bottom: 12px;
    border-radius: 8px;
    background-color: #ffffff;
    cursor: pointer;

    .templateListItem-img {
      border-radius: 8px 8px 0px 0px;
      width: 100%;
      aspect-ratio: 9 / 16;
    }

    .itemContent {
      height: 3.2em;
      padding: 4px 10px;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: linear-gradient(97.99deg, #EBF4FF 12.3%, #F8F8FF 99.99%);
      border-radius: 0px 0px 8px 8px;
      .name {
        font-family: PingFang SC;
        font-weight: 400;
        // 最多显示2行
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
  }

  .videoListItem:nth-child(5n + 5) {
    margin-right: 0px;
  }

  .active-templateListItem {
    outline: 2px solid #0153FF;

    .itemContent {
      background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%);
      .name {
        color: #ffffff;
      }
    }
  }
}
