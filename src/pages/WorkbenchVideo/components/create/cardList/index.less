.create-cardList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;

    .cardListItem {
        height: 82px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
        border-radius: 8px;
        position: relative;
        cursor: pointer;
        box-sizing: border-box;
        margin-bottom: 12px;
        overflow: hidden;


        &:hover {
            .itemIcon {
                transform: scale(1.1) translateY(-2px);
            }
        }

        .itemContent {
            position: relative;
            margin: 0 20px;
            z-index: 1;

            .title {
                font-family: PingFang SC;
                font-size: 14px;
                font-weight: 500;
                line-height: 19.6px;
                color: rgba(0, 0, 0, 0.9);
            }

            .desc {
                font-family: PingFang SC;
                font-size: 14px;
                font-weight: 400;
                line-height: 22px;
                color: rgba(0, 0, 0, 0.4);
                margin-top: 8px;
            }
        }

        .itemIcon {
            width: 80px;
            height: 72px;
            position: absolute;
            right: 10px;
            bottom: -4px;
            transition: all 0.2s ease-in-out;
        }
        .selectedMark {
            width: 18px;
            height: 18px;
            position: absolute;
            right: -1px;
            top: -1px;
        }
    }
    .active-cardListItem {
        outline: 2px solid #5B4DFF;
    }
    .moreTypeItem {
        height: 82px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        border-radius: 8px;
        margin-bottom: 12px;
        color: rgba(0, 0, 0, 0.26);
    }
}
