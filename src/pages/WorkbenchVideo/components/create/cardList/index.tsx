import React, { useEffect, useState } from 'react';
import selectedMark from '../assets/selectedMark.png';
import classnames from 'classnames';
import './index.less';

export interface CardListItem {
  selector_id: string;
  selector_name: string;
  selector_value: string;
  selector_desc: string;
  icon: string;
  selector_item_list?: CardListItem[];
}
export interface CardListProps {
  cardList: CardListItem[];
  cbData: (val: CardListItem) => void;
  value?: string; // 默认选中的数据
  rowMaxLens?: number; // 一行最多放几个 默认最多4个
  isShowMoreType?: boolean; // 是否显示更多类型 默认不显示
  className?: string;
  iconStyle?: React.CSSProperties;
  cardListItemStyle?: React.CSSProperties;
  onChange?: (value: string) => void; // 用于form表单回调
}

function CardList({
  cardList,
  cbData,
  rowMaxLens = 4,
  isShowMoreType,
  iconStyle,
  className,
  cardListItemStyle,
  value,
  onChange,
}: CardListProps) {
  const [selectData, setSelectData] = useState<string>();
  const [rowLens, setRowLens] = useState<number>(rowMaxLens);
  useEffect(() => {
    const extraLens = isShowMoreType ? 1 : 0;
    if (cardList.length + extraLens < rowMaxLens) {
      setRowLens(cardList.length + extraLens);
    } else {
      setRowLens(rowMaxLens);
    }
  }, [cardList, rowMaxLens, isShowMoreType]);

  useEffect(() => {
    if (value) setSelectData(value);
  }, [value]);
  return (
    <div className={classnames('create-cardList', className)}>
      {Array.isArray(cardList) &&
        cardList.map((item) => (
          <div
            className={`cardListItem ${
              selectData === item.selector_value ? 'active-cardListItem' : ''
            }`}
            style={{
              width: `calc(${100 / rowLens}% - 16px)`,
              ...(cardListItemStyle || {}),
            }}
            key={item.selector_value}
            onClick={() => {
              if (item.selector_value !== selectData) {
                setSelectData(item.selector_value);
                cbData(item);
                onChange?.(item.selector_value);
              }
            }}
          >
            <div className="itemContent">
              <div className="title">{item.selector_name}</div>
              <div className="desc">{item.selector_desc}</div>
            </div>
            {item.icon && (
              <img
                src={item.icon}
                alt=""
                className="itemIcon"
                style={{ ...(iconStyle || {}) }}
              />
            )}
            {selectData === item.selector_value && (
              <img src={selectedMark} alt="" className="selectedMark" />
            )}
          </div>
        ))}
      {isShowMoreType && (
        <div
          className="moreTypeItem"
          style={{ width: `calc(${100 / rowLens}% - 16px)`, cursor: 'default' }}
        >
          敬请期待更多类型
        </div>
      )}
    </div>
  );
}

export default CardList;
