import React, { useContext, useEffect, useState } from 'react';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import './index.less';
import CardTitle from '../cardTitle';
import CardList, { CardListItem } from './cardList';
import VideoList from './videoList';
import { VideoContext } from '../../videoContext';
import {
  content_list_type_map,
  GetVideoListBySelector,
  GetVideoTemplateSelector,
} from '../templateSelect/utils';
import { RespType } from '@/pb/config';
import { ResourceSvr } from '@/pb/pb';
import { useDeepCompareEffect } from 'ahooks';

export interface CreateProps {
  type: CONTETNT_TYPE;
}

export type IVideoInfoListItem = RespType<
  typeof ResourceSvr.GetVideoListBySelector
>['video_info_list'][number];

function Create({ type }: CreateProps) {
  const {
    state: { create },
    setCreateInfo,
    setTemplateSelect,
  } = useContext(VideoContext);
  const [videoCreateModeList, setVideoCreateModeList] = useState<
    CardListItem[]
  >([]);
  const [videoTypeList, setVideoTypeList] = useState<CardListItem[]>([]);
  const [videoInfoList, setVideoInfoList] = useState<IVideoInfoListItem[]>([]);
  useDeepCompareEffect(() => {
    (async () => {
      try {
        const data = await GetVideoTemplateSelector({
          stepType: 'createRecommend',
          type,
        });
        const { videoCreateMode } = create || {};
        setVideoCreateModeList(data);
        if (videoCreateMode) {
          const findData = data.find(
            (item) => item.selector_value === videoCreateMode
          );
          setVideoTypeList(findData?.selector_item_list || []);
        } else {
          setVideoTypeList(data[0]?.selector_item_list || []);
        }
        if (!videoCreateMode) {
          const {
            selector_value: videoCreateMode_selector_value,
            selector_id: videoCreateMode_selector_id,
          } = data[0] || {};
          const {
            selector_value: videoType_selector_value,
            selector_id: videoType_selector_id,
          } = data[0]?.selector_item_list?.[0] || {};
          if (videoType_selector_id && videoType_selector_value) {
            setCreateInfo({
              ...create,
              videoCreateMode: videoCreateMode_selector_value,
              videoType: videoType_selector_value,
              selectorValue: {
                ...(create?.selectorValue || {}),
                [videoCreateMode_selector_id]: videoCreateMode_selector_value,
                [videoType_selector_id]: videoType_selector_value,
              },
            });
          }
        }
      } catch (error) {}
    })();
  }, [type]);
  useEffect(() => {
    (async () => {
      try {
        const data = await GetVideoListBySelector({
          selectorValue: create?.selectorValue,
          content_list_type: content_list_type_map.createRecommend[type],
        });
        setVideoInfoList(data);
      } catch (error) {}
    })();
  }, [create?.selectorValue, type]);
  return (
    <div className="components-create">
      <div className="create-method-card">
        <CardTitle
          title="请选择您合适的创建方式"
          desc={
            type === CONTETNT_TYPE.VIDEO
              ? '基于您的素材类型选择适合您的创建方式'
              : '基于您的电商带货方式选择适合您的创建方式'
          }
        />
        <CardList
          cardList={videoCreateModeList}
          isShowMoreType={videoCreateModeList.length < 2}
          cbData={(val) => {
            // 切换创建方式清空step2内容
            setTemplateSelect({});
            setCreateInfo({
              ...create,
              videoCreateMode: val.selector_value,
              videoType: val?.selector_item_list?.[0]?.selector_value,
              selectorValue: {
                ...(create?.selectorValue || {}),
                [val.selector_id]: val.selector_value,
                video_type: val?.selector_item_list?.[0]?.selector_value || '',
              },
              videoId: '',
            });
            setVideoTypeList(val?.selector_item_list || []);
          }}
          value={create?.videoCreateMode}
        />
      </div>
      <div className="video-type-card">
        <CardTitle
          title="请选择您需要的视频类型"
          desc="基于您希望实现的营销功能选择相应的视频类型"
        />
        <CardList
          cardList={videoTypeList}
          isShowMoreType={videoTypeList.length < 2}
          iconStyle={{ width: '88px', height: '70px' }}
          cardListItemStyle={{
            background: 'linear-gradient(270deg, #FFF8F8 0%, #FFEFEF 100%)',
          }}
          cbData={(val) => {
            // 切换创建方式清空step2内容
            setTemplateSelect({});
            setCreateInfo({
              ...create,
              videoType: val.selector_value,
              selectorValue: {
                ...(create?.selectorValue || {}),
                [val.selector_id]: val.selector_value,
              },
              videoId: '',
            });
          }}
          value={create?.selectorValue?.video_type}
        />
        <div className="video-template">
          <CardTitle
            title="您可以选择下方视频作为参考进行创建：（可不选）"
            desc={
              type === CONTETNT_TYPE.VIDEO
                ? '选择您喜欢的视频模板风格'
                : '选择您喜欢的直播模板风格'
            }
            direction="row"
            fontSize={14}
          />
          <VideoList
            videoList={videoInfoList}
            videoId={create?.videoId}
            cbData={(val) => {
              const {
                video_id,
                is_have_border,
                is_have_captions,
                is_have_digital_human,
                scene_specification_id,
              } = val || {};
              // 如果选择的视频不一样  重置step2
              if (create?.videoId !== video_id) {
                setTemplateSelect({
                  selectorValue: {
                    is_have_border: is_have_border
                      ? String(is_have_border)
                      : '',
                    is_have_digital_human: is_have_digital_human
                      ? String(is_have_digital_human)
                      : '',
                    is_have_captions: is_have_captions
                      ? String(is_have_captions)
                      : '',
                    scene_specification_id: scene_specification_id
                      ? String(scene_specification_id)
                      : '',
                  },
                });
              }
              setCreateInfo({ ...create, videoId: video_id || '' });
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default Create;
