import videoIcon from './assets/videoIcon.png';
import productIcon from './assets/productIcon.png';
import pptIcon from './assets/pptIcon.png';
import articleIcon from './assets/articleIcon.png';
import highVideoIcon from './assets/highVideoIcon.png';
import consultVideoIcon from './assets/consultVideoIcon.png';
import promotVideoIcon from './assets/promotVideoIcon.png';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';

export enum IVideoCreateMode {
  VIDEO = '3', // 视频
  PRODUCT = '2', // 商品
  PPT = '1', // PPT
  ARTICLE = '4', // 文章
  CONTENT = '101', // 内容
}

export enum IVideoType {
  HIGHLIGHT = 'highlight', // 游戏推广-高光视频
  PROMOTION = 'promotion', // 游戏推广-投流
  BROADCASE = 'broadcase', // 游戏推广-咨询播报
  E_COMMERCE = 'e-commerce', // 电商投流
  PPT_LECTURER_TRAINING = 'ppt-lecturer-training', // PPT讲师培训
  GRAPH_AND_TEXT_LECTURER_TRAINING = 'graph-and-text-lecturer-training', // 图文讲师培训
  CONTENT = 'content', // 直播-内容主题类视频
  GOODS = 'goods', // 直播-挂货类视频
}

export const transformMap: {
  [key in CONTETNT_TYPE]: {
    [key: string]: {
      icon: string;
      selector_name: string;
      selector_desc: string;
    };
  };
} = {
  [CONTETNT_TYPE.VIDEO]: {
    [IVideoCreateMode.VIDEO]: {
      icon: videoIcon,
      selector_name: '基于视频创建',
      selector_desc: '基于您上传的视频片段创建',
    },
    [IVideoCreateMode.PRODUCT]: {
      icon: productIcon,
      selector_name: '基于商品创建',
      selector_desc: '基于您提供的商品信息创建',
    },
    [IVideoCreateMode.PPT]: {
      icon: pptIcon,
      selector_name: '基于PPT创建',
      selector_desc: '基于您上传的PPT内容创建',
    },
    [IVideoCreateMode.ARTICLE]: {
      icon: articleIcon,
      selector_name: '基于文章创建',
      selector_desc: '基于您上传的文章内容创建',
    },
    [IVideoType.HIGHLIGHT]: {
      icon: highVideoIcon,
      selector_name: '高光视频',
      selector_desc: '对视频中的高光时刻进行展示',
    },
    [IVideoType.PROMOTION]: {
      icon: promotVideoIcon,
      selector_name: '投流推广视频',
      selector_desc: '对视频中的内容进行渠道投流',
    },
    [IVideoType.BROADCASE]: {
      icon: consultVideoIcon,
      selector_name: '资讯播报视频',
      selector_desc: '对视频中的内容进行资讯播报',
    },
    [IVideoType.E_COMMERCE]: {
      icon: promotVideoIcon,
      selector_name: '电商投流',
      selector_desc: '实现商品的电商投流营销目标',
    },
    [IVideoType.PPT_LECTURER_TRAINING]: {
      icon: promotVideoIcon,
      selector_name: 'PPT讲师',
      selector_desc: '实现PPT内容的视频讲解',
    },
    [IVideoType.GRAPH_AND_TEXT_LECTURER_TRAINING]: {
      icon: promotVideoIcon,
      selector_name: '图文讲师',
      selector_desc: '实现文章内容的视频讲解',
    },
  },
  [CONTETNT_TYPE.LIVE]: {
    [IVideoCreateMode.CONTENT]: {
      icon: articleIcon,
      selector_name: '基于内容主题创建',
      selector_desc: '主题宣讲的方式实现营销目标',
    },
    [IVideoCreateMode.PRODUCT]: {
      icon: productIcon,
      selector_name: '基于商品创建',
      selector_desc: '商品挂货的方式实现营销目标',
    },
    [IVideoType.CONTENT]: {
      icon: promotVideoIcon,
      selector_name: '内容带货视频',
      selector_desc: '对带货商品进行内容宣讲',
    },
    [IVideoType.GOODS]: {
      icon: promotVideoIcon,
      selector_name: '商品带货视频',
      selector_desc: '对带货商品进行挂货营销',
    },
  },
};

export const transformVideoTypeMap = {
  [IVideoType.HIGHLIGHT]: '高光集锦',
  [IVideoType.PROMOTION]: '广告投流',
  [IVideoType.BROADCASE]: '资讯播报',
  [IVideoType.E_COMMERCE]: '广告投流',
  [IVideoType.PPT_LECTURER_TRAINING]: 'PPT讲师',
  [IVideoType.GRAPH_AND_TEXT_LECTURER_TRAINING]: '图文讲师',
  [IVideoType.CONTENT]: '内容带货',
  [IVideoType.GOODS]: '广告投流',
};
