import React, { useCallback, useEffect, useState } from 'react';
import classnames from 'classnames';
import './index.less';
import { IVideoInfoListItem } from '..';
import { Empty } from '@/components/Empty';

export interface VideoListProps {
  videoList: IVideoInfoListItem[];
  cbData: (val?: IVideoInfoListItem) => void;
  videoId?: string; // 默认选中的数据
  rowLens?: number; // 一行放几个 默认4个
  className?: string;
  isUncheck?: boolean; // 是否支持取消选中当前的数据 默认支持
}

function VideoList({
  videoList,
  rowLens = 4,
  cbData,
  videoId,
  className,
  isUncheck = true,
}: VideoListProps) {
  const [selectVideoId, setSelectVideoId] = useState<string>();

  const controlPlay = useCallback(
    (videos: HTMLCollectionOf<HTMLVideoElement>) => {
      for (let i = 0; i < videos.length; i++) {
        videos[i]?.addEventListener('play', () => pauseAll(i, videos));
      }
    },
    []
  );
  const pauseAll = (
    index: number,
    videos: HTMLCollectionOf<HTMLVideoElement>
  ) => {
    for (let i = 0; i < videos.length; i++) {
      if (i !== index && videos[i]) {
        videos[i].currentTime = 0;
        videos[i].pause();
      }
    }
  };
  useEffect(() => {
    if (Array.isArray(videoList) && !!videoList.length) {
      const videos = document.getElementsByTagName('video');
      setTimeout(() => {
        controlPlay(videos);
      });
    }
  }, [videoList, controlPlay]);
  useEffect(() => {
    setSelectVideoId(videoId);
  }, [videoId]);
  return (
    <div className={classnames('create-videoList', className)}>
      {Array.isArray(videoList) && !!videoList.length ? (
        videoList.map((item) => (
          <div
            className={`videoListItem ${
              selectVideoId === item.video_id ? 'active-videoListItem' : ''
            }`}
            style={{ width: `calc(${100 / rowLens}% - 15px)` }}
            key={item.video_id}
            onClick={() => {
              if (isUncheck && selectVideoId === item.video_id) {
                setSelectVideoId(undefined);
                cbData();
                return;
              }
              setSelectVideoId(item.video_id);
              cbData(item);
            }}
          >
            <video
              src={item.play_url}
              className="video"
              controls
              autoPlay={false}
              controlsList="nodownload noremoteplayback noplaybackrate nofullscreen"
              poster={item.pic_url}
              disablePictureInPicture
              disableRemotePlayback
            />
            <div className="itemContent">
              <div className="name">{item.title}</div>
              <div className="desc">{item.description}</div>
              <div className="tag-contanier">
                {item.video_tags.split(';').map((tag: string) => (
                  <div className="tag">{tag}</div>
                ))}
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="flex-center h-full w-full" style={{ height: 200 }}>
          <Empty>暂无数据</Empty>
        </div>
      )}
    </div>
  );
}

export default VideoList;
