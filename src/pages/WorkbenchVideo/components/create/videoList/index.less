.create-videoList {
  display: flex;
  flex-wrap: wrap;

  .videoListItem {
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    background-color: #ffffff;
    cursor: pointer;

    .video {
      background-color: #000000;
      width: 100%;
      min-height: 360px;
      border-radius: 8px 8px 0px 0px;
    }

    .itemContent {
      padding: 16px;

      .name,
      .desc {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .name {
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 400;
        line-height: 22.4px;
        color: rgba(0, 0, 0, 0.9);
      }

      .desc {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.4);
        margin: 8px 0px;
      }

      .tag-contanier {
        display: flex;
        flex-wrap: wrap;

        .tag {
          padding: 4px 8px;
          font-family: PingFang SC;
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          border-radius: 90px;
          background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
    }
  }

  .videoListItem:nth-child(4n + 4) {
    margin-right: 0px;
  }

  .active-videoListItem {
    outline: 2px solid #0153FF;
    background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%);

    .itemContent {
      .name {
        color: rgba(255, 255, 255);
      }

      .desc {
        color: rgba(255, 255, 255, 0.55);
      }

      .tag-contanier {
        .tag {
          background: linear-gradient(87.64deg, rgba(224, 234, 255, 0.3) 0%, rgba(226, 239, 255, 0.3) 46.96%, rgba(245, 243, 255, 0.3) 99.81%);
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }
}