.components-create {
    .create-method-card {
        width: 100%;
        padding: 20px;
        padding-bottom: 8px;
        border-radius: 8px;
        background-color: #ffffff;
    }
    .video-type-card {
        width: 100%;
        padding: 20px;
        border-radius: 8px;
        background-color: #ffffff;
        margin-top: 16px;
    }
    .video-template {
        padding: 20px;
        padding-bottom: 0px;
        border-radius: 8px;
        background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
    }
}
