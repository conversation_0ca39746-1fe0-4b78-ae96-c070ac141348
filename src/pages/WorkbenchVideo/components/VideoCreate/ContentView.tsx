import {
  DownloadIcon,
  Edit1Icon,
  PlayCircleStrokeIcon,
} from 'tdesign-icons-react';
import { AdjustIcon } from './Icon';
import { useMemoizedFn, useRequest, useToggle } from 'ahooks';
import React, { Fragment, useContext, useMemo, useState } from 'react';
import { Drawer, MessagePlugin } from 'tdesign-react';
import { getContentType } from '../../utils';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { getOriginByType, openEditor } from '@/pages/Editor/common/openEditor';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { VideoContext } from '../../videoContext';
import { ScriptContent } from '@/pages/ScriptList/VisionCreate/ScriptContent';
import CreateScript from '@/pages/ScriptList/Create/index';
import { Preview } from './Preview';
import { LiveSaveComposeDialog } from '@/pages/Editor/components/LiveSaveButton/components/LiveSaveComposeDialog';
import { useNavigate } from 'react-router-dom';
import { freshSave } from '../../editor-action';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import { MetaFeedbackSvr } from '@/pb/pb';
import { FormValues } from '@/pages/Editor/components/LiveSaveButton/components/LiveConfigSaveDialog';
import { cloneDeep, get } from 'lodash-es';
import { VideoLocalViewer } from '@/components/VideoLocalViewer';
import { sleep } from '@/utils/sleep';
import ReactDOM from 'react-dom';
import { screenShot } from '@/pages/Editor/common/screenshot';
import { handleScreenShot } from '@/pages/Editor/common/handleScreenShot';
import { IVideoCreateMode } from '../create/utils';
import { MetaPushContext } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { MetaPushStepModal } from '@/pages/ADMetaList/MetaPushStepModal';

const TemplateId = '03E4392E-EA7B-C6CF-D1FE-5BA36ACD9415';
const scriptId = 'research1723466075129311473_QUGRBH';

const buttonCls =
  'w-[256px] py-[8px] gap-[4px] flex flex-row items-center justify-center cursor-pointer bg-[#fff] rounder-[4px]';

export function ContentView() {
  const navigator = useNavigate();

  const [drawerVisible, { toggle: toggleScriptDrawer }] = useToggle();
  const [liveDialog, { toggle: toggleDialog }] = useToggle();
  const [videoSaveDialog, { toggle: toggleSave }] = useToggle();
  // 保存当前脚本实例 id
  const [scriptIdForce, setScriptIdForce] = useState(undefined);
  // 直播id
  const [liveId, setLiveId] = useState('');
  // 控制截图 iframe 实例
  const [shoting, setShoting] = useState(true);
  const type = getContentType() as CONTETNT_TYPE;

  const {
    state: { create, scriptInfoCollect, templateSelect },
    setScriptInfo,
  } = useContext(VideoContext);

  const { runAsync: saveScriptReq, loading: scriptSaving } = useRequest(
    MetaFeedbackSvr.SaveScript,
    { manual: true }
  );

  const originType = useMemo(() => {
    if (!create?.videoCreateMode) return 'text';
    return getOriginByType({
      contentType: type,
      application_scenarios: create?.videoCreateMode,
    });
  }, [create?.videoCreateMode, type]);

  /**
   * 进入编辑器
   */
  const gotoEditor = useMemoizedFn(async () => {
    if (!create?.videoCreateMode) return;
    const r = await saveScriptReq({
      // 默认保存动作的 name，同步
      research_name: '隐藏',
      script_info: [JSON.stringify(scriptInfoCollect.scriptInfo)],
      type,
      application_scenarios: create.videoCreateMode,
      source: originType,
      nodes_count: [scriptInfoCollect.scriptInfo.views.length],
      hidden: 1,
    });
    const scriptId = get(r, 'script_list[0].research_id');

    openEditor({
      system: EditorSystemMap.META_HUMAN,
      origin: originType,
      templateId: templateSelect?.templateId,
      contentType: type, // 调试临时使用
      scriptId,
    });
  });

  /**
   * 截图方法
   */
  const { data: postUrl, loading: isSnapshotLoading } = useRequest(
    async () => {
      let postUrl = templateSelect?.templatePosterUrl || '';
      let timer;
      try {
        // 先等待 5500ms 这个时间很随意，为了尽可能保证数字人加载出来
        // 现在导演组件内有预加载 逻辑，没有play也会加载，因此前面等待的时间可以长一些
        // 等待iframe加载完
        console.log('!!!等待 iframe 加载 5000ms');
        await sleep(5000);
        const previewContainer = document.getElementById(
          'preview-container'
        ) as HTMLIFrameElement;
        // 不能用 querySelector，这里要获取到动态节点
        const previewIframe = previewContainer.getElementsByTagName('iframe');
        console.log('!!!iframe 加载 完成，开始截图前的静音操作');
        // (previewIframe[0] as any).muted = true;
        timer = setInterval(() => {
          const needMutedPlayElements =
            previewIframe[0].contentWindow?.document.querySelectorAll(
              'video,audio'
            );
          needMutedPlayElements?.forEach((item: any) => {
            item.muted = true;
          });
        }, 20);

        (previewIframe[0].contentWindow as any).__pagedoo_script_play.play();
        console.log('!!!执行 play 方法，等待 1000ms');

        // 等待播放
        await sleep(1000);

        const originalBody = previewIframe[0].contentDocument?.body;
        console.log('!!!! 开始截图');
        const base64PostUrl = await screenShot(originalBody);
        console.log('!!!! 截图完成');
        setShoting(false);

        if (
          postUrl &&
          /data:image\/(png|jpg|jpeg|gif|bmp);base64/.test(base64PostUrl)
        ) {
          console.log('!!!上传图片到 cdn');
          postUrl = await handleScreenShot({ imageBase64: base64PostUrl });
          console.log('!!!上传成功', postUrl);
        }
      } catch (e) {
        console.error('截图失败，将采用默认模版封面图', e);
      } finally {
        setShoting(false);
        clearInterval(timer);
        return postUrl;
      }
    },
    {
      refreshDeps: [scriptInfoCollect.scriptInfo],
    }
  );

  // 保存脚本
  const {
    runAsync: saveContent,
    data: saveContentData,
    loading: contentSaving,
  } = useRequest(
    async (v: FormValues) => {
      if (!templateSelect?.templateId || !postUrl) {
        MessagePlugin.error('封面图生成失败');
        return {
          contentId: undefined,
        };
      }

      return freshSave({
        templateId: templateSelect?.templateId,
        script: scriptInfoCollect.scriptInfo || gameLandScapeScript,
        contentType: type,
        origin_type: originType,
        contentName: v.contentName,
        posterUrl: postUrl,
        videoPosterInfo: v.videoPosterInfo,
      });
    },
    { manual: true }
  );

  const liveStart = useMemoizedFn(async () => {
    if (!liveId) {
      const { contentId } = await saveContent({
        contentName: '直播视频',
        videoPosterInfo: undefined,
        contentType: 'live',
      });
      if (!contentId) {
        MessagePlugin.error('直播保存失败');
        return;
      }
      setLiveId(contentId);
    }
    toggleDialog();
  });

  const direction = useMemo(() => {
    return (
      scriptInfoCollect.scriptInfo.size[0] >
      scriptInfoCollect.scriptInfo.size[1]
    );
  }, [scriptInfoCollect.scriptInfo.size]);

  return (
    <div className="w-[288px] overflow-hidden rounded-8">
      <Preview
        scriptLoading={scriptSaving || contentSaving || isSnapshotLoading}
        posterUrl={postUrl}
      />
      <div className="p-16 flex flex-col gap-[8px] bg-gradient-to-r from-[#EBF4FF] to-[#F8F8FF]">
        <Fragment x-if={type === CONTETNT_TYPE.LIVE}>
          <LiveStart onClick={liveStart} />
        </Fragment>
        <Fragment x-else>
          <VideoDownLoad onClick={toggleSave} />
        </Fragment>
        <div
          className={buttonCls}
          onClick={() => {
            toggleScriptDrawer();
          }}
        >
          <AdjustIcon />
          查看/调整脚本
        </div>
        <div className={buttonCls} onClick={gotoEditor}>
          <Edit1Icon style={{ color: '#0009' }} />
          进入编辑器编辑
        </div>
      </div>
      {/* 抽屉 */}
      {drawerVisible && (
        <Drawer
          header="查看/编辑脚本"
          visible={drawerVisible}
          placement="right"
          onClose={toggleScriptDrawer}
          size="1200px"
          footer={null}
        >
          {create?.videoCreateMode === IVideoCreateMode.VIDEO ? (
            <ScriptContent
              researchId={scriptIdForce}
              receiveScript={scriptInfoCollect.scriptInfo}
              saveButtonText="保存并重新生成视频"
              actionValidate="script;back;"
              backText="取消"
              cancelCallback={toggleScriptDrawer}
              saveCallback={(v) => {
                setShoting(true);
                setScriptIdForce(v.researchId);

                setScriptInfo({
                  ...scriptInfoCollect,
                  scriptInfo: JSON.parse(v.scriptInfo),
                });
                toggleScriptDrawer();
              }}
              from={`${type}__${create?.videoCreateMode}`}
            />
          ) : (
            <CreateScript
              researchId={scriptIdForce}
              receiveScript={scriptInfoCollect.scriptInfo}
              saveButtonText="重新生成视频"
              backText="取消"
              saveCallback={(v) => {
                setShoting(true);
                setScriptIdForce(v.researchId);
                setScriptInfo({
                  ...scriptInfoCollect,
                  scriptInfo: JSON.parse(v.scriptInfo),
                });
                toggleScriptDrawer();
              }}
              from={`${type}__${create?.videoCreateMode}`}
              cancelCallback={() => toggleScriptDrawer()}
            />
          )}
        </Drawer>
      )}
      <MetaPushContext
        x-if={type === CONTETNT_TYPE.LIVE && liveDialog && liveId}
        liveId={liveId}
      >
        <MetaPushStepModal liveId={liveId} onClose={toggleDialog} />
      </MetaPushContext>
      <Fragment x-if={type === CONTETNT_TYPE.VIDEO}>
        <LiveSaveComposeDialog
          visible={videoSaveDialog}
          onClose={toggleSave}
          onNavigatePage={() => {
            navigator('/video-list');
          }}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          onSave={(v) => {
            // setShoting(true);
            return saveContent(v);
          }}
          direction={direction ? 'horizontal' : 'vertical'}
          contentType={type}
          getContentInfo={async () => {
            if (!saveContentData?.contentId) {
              throw new Error('get contentId err');
            }
            return {
              contentId: saveContentData?.contentId,
            };
          }}
        />
      </Fragment>
      <Fragment x-if={scriptInfoCollect.scriptInfo && shoting}>
        {ReactDOM.createPortal(
          <VideoLocalViewer
            templateId={
              templateSelect?.templateId ||
              '03E4392E-EA7B-C6CF-D1FE-5BA36ACD9415'
            }
            script={cloneDeep(scriptInfoCollect.scriptInfo)}
            type={type}
            width={direction ? 821 : scriptInfoCollect.scriptInfo.size[0]}
            height={direction ? 420 : scriptInfoCollect.scriptInfo.size[1]}
            injectStyle={{
              zIndex: '-100',
              position: 'absolute',
              top: 0,
              left: 0,
            }}
            speaker={false}
          />,
          document.body
        )}
      </Fragment>
    </div>
  );
}

function VideoDownLoad(props: any) {
  return (
    <div className={buttonCls} {...props}>
      <DownloadIcon style={{ color: '#0009' }} />
      保存视频
    </div>
  );
}

function LiveStart(props: any) {
  return (
    <div className={buttonCls} {...props}>
      <PlayCircleStrokeIcon />
      立即开播
    </div>
  );
}
