import { useHover, useMemoizedFn } from 'ahooks';
import { AudioSvg } from './AudioSvg';
import { useContext, useMemo, useRef, useState } from 'react';
import { VideoLocalViewer } from '@/components/VideoLocalViewer';
import { Dialog, MessagePlugin } from 'tdesign-react';
import { getContentType } from '../../utils';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { Loading } from '@/components/Loading';
import { Script } from '@/type/pagedoo';
import { VideoContext } from '../../videoContext';

interface IProps {
  scriptLoading?: boolean;
  fetchScript?: Script;
  posterUrl?: string;
}

export function Preview(props: IProps) {
  const { scriptLoading = false, posterUrl } = props;
  const hoverRef = useRef(null);
  const isHovering = useHover(hoverRef);
  const [previewDialog, setPreviewDialog] = useState(false);

  const type = getContentType() as CONTETNT_TYPE;
  const { state: videoContextState } = useContext(VideoContext);
  const { scriptInfo } = videoContextState.scriptInfoCollect;

  const direction = useMemo(() => {
    return scriptInfo.size[0] > scriptInfo.size[1];
  }, [scriptInfo.size]);
  /**
   * 预览
   */
  const onPreview = useMemoizedFn(() => {
    if (!videoContextState.templateSelect?.templateId) {
      MessagePlugin.error('未选择模板');
      return;
    }
    if (!scriptInfo) return;

    setPreviewDialog(true);
  });

  return (
    <div ref={hoverRef} className="relative h-[360px]">
      <>
        <div className="bg-[black] flex justify-center items-center h-full">
          <img
            className={`${direction ? 'w-full' : 'h-full'}`}
            src={posterUrl}
            alt=""
          />
        </div>
        {scriptLoading ? (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col justify-center items-center">
            <Loading />
            <span className="text-[#fff]">正在基于脚本生成视频中...</span>
          </div>
        ) : null}
        {!scriptLoading && isHovering && (
          <div
            className="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center"
            onClick={onPreview}
          >
            <div className="text-white text-6xl bg-transparent border-none cursor-pointer">
              <AudioSvg />
            </div>
          </div>
        )}
      </>

      <Dialog
        destroyOnClose
        width="auto"
        placement="center"
        header="视频预览"
        visible={previewDialog}
        onClose={() => {
          setPreviewDialog(false);
        }}
        footer={null}
      >
        <VideoLocalViewer
          templateId={videoContextState.templateSelect?.templateId ?? ''}
          script={scriptInfo}
          type={type}
          width={direction ? 984 : scriptInfo.size[0]}
          height={direction ? 553 : scriptInfo.size[1]}
        />
      </Dialog>
    </div>
  );
}
