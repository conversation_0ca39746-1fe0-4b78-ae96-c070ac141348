/* eslint-disable @typescript-eslint/ban-ts-comment */
/**
 * <AUTHOR>
 * @date 2024/8/16 下午4:32
 * @desc index
 */

import React, { useCallback, useContext, useMemo, useRef } from 'react';
import {
  IScriptFormRef,
  ScriptForm,
  ScriptResult,
} from '@/components/ScriptForm';
import { MessagePlugin } from 'tdesign-react';
import { useEventListener } from 'ahooks';
import { FactoryStep } from '@/pages/WorkbenchVideo/constants';
import { VideoContext } from '@/pages/WorkbenchVideo/videoContext';
import { getContentType } from '@/pages/WorkbenchVideo/utils';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { CONFIG } from '@/pages/ScriptList/Create';
import genScriptInfo from '@/pages/ScriptList/VisionCreate/genScriptInfo';
import {
  BaseScript,
  IScriptForm,
  MaterialView,
  <PERSON>riptView,
} from '@/components/ScriptForm/type';
import { transformVideoTypeMap } from '@/pages/WorkbenchVideo/components/create/utils';
import { getAllResource } from '@/components/ScriptForm/utils';
import { CONTENT_TYPE_MAP } from '@/const/common';

interface IProps {
  setCurStep: (step: number) => void;
  nextBtnRef: React.RefObject<HTMLButtonElement>;
}

export function ScriptInput(props: IProps) {
  const { nextBtnRef, setCurStep } = props;
  const formRef = useRef<IScriptFormRef>(null);
  const {
    state: { create },
    setScriptInfo,
  } = useContext(VideoContext);

  const type = getContentType() as CONTETNT_TYPE;

  const initFormData = useMemo<Partial<IScriptForm>>(() => {
    if (!create?.videoType) {
      return {};
    }
    return {
      type: transformVideoTypeMap[
        create.videoType as keyof typeof transformVideoTypeMap
      ],
    };
  }, [create?.videoType]);

  const application_scenarios = useMemo(() => {
    if (!create?.videoCreateMode) {
      return '';
    }
    return create.videoCreateMode;
    // return transformKey(type, create.videoCreateMode);
  }, [create?.videoCreateMode]);

  const onCreateFail = useCallback(() => {
    void MessagePlugin.error('脚本解析失败');
  }, []);

  const onCreateSuccess = useCallback(
    (scriptRes: ScriptResult) => {
      let res: BaseScript;
      if (
        application_scenarios === CONTENT_TYPE_MAP.Video.children.Vision.value
      ) {
        //   游戏推广
        res = genScriptInfo({
          views: scriptRes.scriptViews,
          globalField: scriptRes.scriptGlobalData,
          globalSpeech: (scriptRes.scriptViews || [])
            .map((view: ScriptView) => view.speech)
            .join('\n'),
        });
        res.globalField = {
          subTitle: '',
          title: '',
          ...res.globalField,
          globalResourceList: getAllResource(scriptRes.scriptElements),
        };
      } else {
        // 电商
        res = CONFIG[
          application_scenarios as unknown as keyof typeof CONFIG
        ].getScriptInfo(scriptRes.scriptViews, scriptRes.scriptGlobalData);
      }
      setScriptInfo({ scriptInfo: res });
      setCurStep(FactoryStep.PREVIEW);
    },
    [application_scenarios, setCurStep, setScriptInfo]
  );

  useEventListener(
    'click',
    async () => {
      await formRef.current?.submit();
    },
    { target: nextBtnRef }
  );

  if (!type || !application_scenarios) {
    void MessagePlugin.error('参数错误');
  }

  return (
    <ScriptForm
      ref={formRef}
      type={type}
      initFormData={initFormData}
      scenarios={application_scenarios}
      onCreateSuccess={onCreateSuccess}
      onCreateFail={onCreateFail}
    />
  );
}
