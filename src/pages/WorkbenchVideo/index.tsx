import { Page } from '@/components/Layout/Page';
import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Breadcrumb, Button, Steps } from 'tdesign-react';
import { getContentType, stepsList } from './utils';
import './index.less';
import VideoProvider from './videoContext';
import { FactoryStep, TYPE_MAP } from './constants';
import { CONTETNT_TYPE } from '../Workbench/constants';
import { VideoCreate } from './components/VideoCreate';
import TemplateSelect from './components/templateSelect';
import Create from './components/create';
import { ScriptInput } from '@/pages/WorkbenchVideo/components/ScriptInput';

const classPrefix = 'workbenchVideo-page';
const { BreadcrumbItem } = Breadcrumb;
const { StepItem } = Steps;

function WorkbenchVideo() {
  const navigate = useNavigate();
  const nextStepLoadingRef = useRef(false);
  const [nextStepFn, setNextStepFn] = useState<{
    cbFn: (type: 'success' | 'error') => void;
  }>();
  const [curStep, setCurStep] = useState(FactoryStep.TYPE_SELECT);
  const contentType = getContentType() as CONTETNT_TYPE;
  const nextBtnRef = useRef<HTMLButtonElement>(null);

  return (
    <VideoProvider>
      <Page
        title={`创建${contentType === CONTETNT_TYPE.VIDEO ? '视频' : '直播间'}`}
        breadCrumb={
          <Breadcrumb separator=">" maxItemWidth="140px">
            <BreadcrumbItem onClick={() => navigate('/workbench')}>
              工作台
            </BreadcrumbItem>
            <BreadcrumbItem>{TYPE_MAP[contentType].title}</BreadcrumbItem>
          </Breadcrumb>
        }
      >
        <div className={classPrefix}>
          <div className={`${classPrefix}-step`}>
            <Steps layout="horizontal" current={curStep}>
              {stepsList.map((item) => (
                <StepItem
                  key={item.value}
                  title={
                    <span style={{ fontWeight: '500' }}>{item.title}</span>
                  }
                />
              ))}
            </Steps>
          </div>
          <div className={`${classPrefix}-content`}>
            {curStep === FactoryStep.TYPE_SELECT && (
              <Create type={contentType} />
            )}
            {curStep === FactoryStep.TEMPLATE_SELECT && (
              <TemplateSelect
                type={contentType}
                nextBtnRef={nextBtnRef}
                setCurStep={setCurStep}
              />
            )}
            {curStep === FactoryStep.SCRIPT_COLLECT && (
              <ScriptInput nextBtnRef={nextBtnRef} setCurStep={setCurStep} />
            )}
            {curStep === FactoryStep.PREVIEW && <VideoCreate />}
          </div>
          {curStep !== FactoryStep.PREVIEW && (
            <div className={`${classPrefix}-footer`}>
              <Button
                ref={nextBtnRef}
                size="large"
                theme="primary"
                className="gradient-primary"
                onClick={() => {
                  if (curStep === FactoryStep.TYPE_SELECT) {
                    setCurStep((data) => data + 1);
                  }
                  // else if (!nextStepLoadingRef.current) {
                  //   nextStepLoadingRef.current = true;
                  //   setNextStepFn({
                  //     cbFn: (type) => {
                  //       nextStepLoadingRef.current = false;
                  //       setNextStepFn(undefined);
                  //       if (type === 'success') {
                  //         setCurStep((data) => data + 1);
                  //       }
                  //     },
                  //   });
                  // }
                }}
              >
                下一步
              </Button>
              {!!curStep && (
                <Button
                  size="large"
                  theme="default"
                  className="gradient-default"
                  onClick={() => setCurStep((data) => data - 1)}
                >
                  上一步
                </Button>
              )}
              <Button
                size="large"
                theme="default"
                className="gradient-default"
                onClick={() => navigate('/workbench')}
              >
                取消
              </Button>
            </div>
          )}
        </div>
      </Page>
    </VideoProvider>
  );
}
export default WorkbenchVideo;
