import React, { createContext, Dispatch, useMemo, useReducer } from 'react';
import { reducer, initialState, Action } from './videoReducer';
import { BaseScript } from '@/components/ScriptForm/type';

interface IContextState {
  create?: {
    selectorValue?: Record<string, string>; // 筛选条件
    videoCreateMode?: string; // 视频创建方式
    videoType?: string; // 视频类型
    videoId?: string; // 视频id
  };
  templateSelect?: {
    selectorValue?: Record<string, string>; // 筛选条件
    templateId?: string; // 模版id
    templatePosterUrl?: string; // 模版封面图
  };
  scriptInfoCollect: {
    scriptInfo: BaseScript; // 脚本信息
  };
}

// to do 定义每个组件值类型
export interface ContextProps {
  state: IContextState;
  dispatch: Dispatch<Action>;
  setScriptInfo: (v: IContextState['scriptInfoCollect']) => void;
  setTemplateSelect: (v: IContextState['templateSelect']) => void;
  setCreateInfo: (v: IContextState['create']) => void;
}

const defaultContext: ContextProps = {
  state: initialState,
  dispatch: () => void 0,
  setScriptInfo: () => void 0,
  setTemplateSelect: () => void 0,
  setCreateInfo: () => void 0,
};

export const VideoContext = createContext<ContextProps>(defaultContext);

function VideoProvider(props: { children: React.ReactNode }) {
  const { children } = props;
  const [state, dispatch] = useReducer(reducer, initialState);

  const providerValue = useMemo(() => {
    const setCreateInfo = (v: IContextState['create']) =>
      dispatch({ type: 'CREATE', payload: v });

    const setScriptInfo = (v: IContextState['scriptInfoCollect']) =>
      dispatch({ type: 'SCRIPT_INFO_COLLECT', payload: v });

    const setTemplateSelect = (v: IContextState['templateSelect']) =>
      dispatch({ type: 'TEMPLATE_SELECT', payload: v });

    return {
      state,
      dispatch,
      setScriptInfo,
      setTemplateSelect,
      setCreateInfo,
    };
  }, [state, dispatch]);

  return (
    <VideoContext.Provider value={providerValue}>
      {children}
    </VideoContext.Provider>
  );
}

export default VideoProvider;
