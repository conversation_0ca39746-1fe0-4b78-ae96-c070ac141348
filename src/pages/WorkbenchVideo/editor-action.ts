import { ContentSvr } from '@/pb/pb';
import { playService } from '@/utils/play-template';
import { CONTETNT_TYPE } from '../Workbench/constants';
import { generateHtml } from '../Editor/common/editor';
import { generateHtmlPage } from '../Editor/MetaHumanLive/defaultOptions';
import { FormValues } from '../Editor/components/LiveSaveButton/components/LiveConfigSaveDialog';
import { BaseScript } from '@/components/ScriptForm/type';

interface ISaveProps {
  contentName: string; // title
  /**
   * 资源类型
   */
  contentType: string; // type
  origin_type: string; // origin
  /**
   * 脚本 -模板
   */
  script: BaseScript;
  scriptId?: string;
  templateId: string;
  /**
   * 封面
   */
  posterUrl: string;
  videoPosterInfo: FormValues['videoPosterInfo'];
}

export const freshSave = async (options: ISaveProps) => {
  const {
    scriptId = 'EMPTY',
    templateId,
    script,
    contentType,
    contentName,
    origin_type,
    posterUrl,
    videoPosterInfo,
  } = options;

  const playScript = await playService.genPlayScriptByTemplate({
    script,
    templateId,
    isLive: contentType === CONTETNT_TYPE.LIVE,
    isVideo: contentType === CONTETNT_TYPE.VIDEO,
  });
  const mergeFormatType = `${origin_type}_${contentType}`;

  const extendData = {
    scriptId,
    is_check_ok: `1`,
    // 脚本协议
    playScript: JSON.stringify(playScript),
    pagodooQA: '',
    ...videoPosterInfo,
  };

  const contentProtocol = {
    component_list: [],
    content_title: contentName,
    // page_list Html 在录制时有用
    page_list: [{ html: '' }],
    poster_url: posterUrl,
  };

  // 新增
  const createRes = await ContentSvr.ContentCreate({
    ...contentProtocol,
    content_type: mergeFormatType,
    extend: extendData,
  } as any);

  const html = await generateHtml({
    insertData: {
      'pagedoo-play-script': JSON.parse(
        (extendData?.playScript as string) || '{}'
      ),
      'pagedoo-qa': JSON.parse((extendData?.pagodooQA as string) || '{}'),
      'pagedoo-live': {
        id: createRes.content_id,
      },
      pages: generateHtmlPage,
    },
    pageList: [],
    componentLibList: [],
    uin: createRes.content_id,
  });

  // 录制会用到 html
  contentProtocol.page_list[0].html = html;
  await ContentSvr.ContentModify({
    ...contentProtocol,
    content_type: contentType,
    content_id: createRes.content_id,
    extend: extendData,
  } as any);

  return { contentId: createRes.content_id };
};
