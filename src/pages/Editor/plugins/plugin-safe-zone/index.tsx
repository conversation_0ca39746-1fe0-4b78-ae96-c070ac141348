import { waitPreviewCanvasReady } from '@/pages/Editor/common/editor-helper';
import { SafeZone } from './SafeZone';
import { EditorPluginCreator } from '@/pages/Editor/plugins/plugin-typings';
import { createRoot } from 'react-dom/client';
import patchStyles from './safe-zone-patch.module.less';

export const pluginSafeZone: EditorPluginCreator<{
  width: number;
  [k: string]: any;
}> = async ({ editorRef, ...options }) => {
  const editorRoot = document.getElementById('simple-editor-root');
  if (!editorRoot) {
    console.warn('安全区插件只能用于SimpleEditor');
    return;
  }

  editorRoot.className = `${editorRoot.className} ${patchStyles.patchSafeZone}`;
  const renderSafeZone = (renderOptions: { previewEl: HTMLElement }) => {
    const pageEditorWrap = editorRoot.querySelector<HTMLElement>(
      '.__page-editor-wrap'
    );
    if (!pageEditorWrap) return;
    const pagedooCanvas = document.querySelector(
      '.pagedoo-canvas'
    ) as HTMLElement;
    if (!pagedooCanvas) return;
    const div = document.createElement('div')!;
    div.className = '__plugin_safe_zone';
    div.style.width = '0';
    div.style.height = '0';
    const root = createRoot(div);
    root.render(
      <SafeZone
        previewEl={renderOptions.previewEl}
        switchContainer={pagedooCanvas}
        safeAreaProps={{
          width: options.width,
        }}
      />
    );
  };

  waitPreviewCanvasReady().then(({ previewEl }) => {
    const deviceContainer = editorRoot.querySelector(
      '.device-simulator-container'
    ) as HTMLElement;
    if (deviceContainer) {
      console.log(
        'deviceContainer width ',
        deviceContainer.getBoundingClientRect().width
      );
      deviceContainer.style.left = 'calc(50% - 90% / 2)';
      deviceContainer.style.visibility = 'visible';
    }
    renderSafeZone({
      previewEl,
    });
  });
};
