import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { HelpCircleFilledIcon } from 'tdesign-icons-react';
import { Switch, Tooltip } from 'tdesign-react';
import Styles from './safe-zone.module.less';

export interface ISafeZoneProps {
  /**
   * 安全区开关的容器
   */
  switchContainer: HTMLElement;

  previewEl: HTMLElement;

  safeAreaProps?: Pick<ISafeAreaProps, 'width'>;
}

interface ISafeAreaProps {
  width?: number;
  position: 'left' | 'right';
}
function SafeArea(props: ISafeAreaProps) {
  const { width = 40, position } = props;
  return (
    <section
      className={Styles.safeArea}
      style={{
        width: `${width}px`,
        [position]: 0,
      }}
    />
  );
}

function SafeSwitch(
  props: {
    show: boolean;
    onChange: (show: boolean) => void;
  } & Pick<React.HTMLAttributes<HTMLElement>, 'style'>
) {
  const { show, onChange, style } = props;
  return (
    <section
      className="flex items-center gap-x-2 justify-end"
      style={{
        ...style,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <section
        onClick={() => {
          onChange(!show);
        }}
        className="flex items-center cursor-pointer gap-x-2"
      >
        <Switch size="small" value={show} />
        <span className="leading-5 text-xs">展示安全区</span>
      </section>
      <section className="flex">
        <Tooltip
          content="因手机屏幕尺寸不同，画布两侧的安全区在部分手机上无法完全显示"
          overlayInnerStyle={{
            width: '248px',
            padding: '11px 15px',
          }}
          theme="light"
          placement="top-right"
        >
          <HelpCircleFilledIcon
            style={{
              color: 'rgba(137, 139, 143, 1)',
              width: '16px',
              height: '16px',
            }}
            className="cursor-pointer"
            width={20}
            height={20}
          />
        </Tooltip>
      </section>
    </section>
  );
}

export function SafeZone(props: ISafeZoneProps) {
  const { switchContainer, previewEl, safeAreaProps } = props;

  const [show, setShow] = useState(true);

  return (
    <>
      {switchContainer &&
        createPortal(
          <SafeSwitch
            show={show}
            onChange={setShow}
            style={{
              marginBottom: '20px',
              marginRight: '20px',
              height: '16px',
            }}
          />,
          switchContainer
        )}
      {previewEl && show && (
        <>
          {createPortal(
            <SafeArea {...safeAreaProps} position="left" />,
            previewEl
          )}
          {createPortal(
            <SafeArea {...safeAreaProps} position="right" />,
            previewEl
          )}
        </>
      )}
    </>
  );
}
