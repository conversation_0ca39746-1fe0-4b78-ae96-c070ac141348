import { adComponentLimitationConfig } from '@/pages/Editor/ADLive/simple-editor-config/custom';
import { EditorPluginCreator } from '@/pages/Editor/plugins/plugin-typings';
import { PageEditorRef } from '@tencent/page_editor';
import { MessagePlugin } from 'tdesign-react';

type DateFormRemove = {
  __component_id: string;
};
type HookData = [
  number?,
  {
    // 组件id
    id: string;
    materials: string;
    // 组件名称
    name: string;
  }?
];

const NotAllowMultipleComponentNames: string[] = [
  'Virtualman',
  'LiveSound',
  'LiveSpeechAD',
  'BackgroundMusic',
  'LiveQA',
  'LiveBkg',
];
const NotAllowRemoveComponentNames: string[] = [
  'Virtualman',
  // 'LiveSound',
  'LiveSpeechAD',
  'LiveQA',
  'LiveBkg',
];

const getComponentNameFromHook = (data: HookData) => {
  if (!Array.isArray(data)) return '';
  if (data.length < 2) return '';
  const [, info] = data;
  if (!info) return '';
  const { name } = info;
  return name;
};

export const pluginADEditorHooks: EditorPluginCreator<unknown> = async ({
  editorRef: pageEditorRef,
}) => {
  document.addEventListener(
    'keydown',
    (e) => {
      console.log('keydown event', e);
    },
    {
      capture: true,
    }
  );
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  // await pageEditorRef.handler.setGlobalData('__meta', {
  //   template: {
  //     id: 'test',
  //     limitedComponent: [
  //       {
  //         name: 'Virtualman',
  //         maxInstance: 1,
  //         disableRemove: true,
  //       },
  //       {
  //         name: 'LiveSpeech',
  //         disableRemove: true,
  //       },
  //     ],
  //   },
  // } as PagedooEditorGlobalData['__meta']);

  pageEditorRef.handler.addHook('removeComponent', async (args) => {
    const [key] = (args as null) || [];
    console.log('remove component key ', key);

    const data: DateFormRemove = (await pageEditorRef.handler.getDataForm(
      key
    )) as DateFormRemove;
    const { __component_id } = data;
    if (
      __component_id &&
      NotAllowRemoveComponentNames.indexOf(__component_id) > -1
    ) {
      MessagePlugin.info('该组件不能删除哦，否则会影响直播间的正常运行');
      return false;
    }
    return true;
  });
  pageEditorRef.handler.addHook('clearContainer', () => {
    MessagePlugin.info('组件不能清空哦，否则会影响直播间的正常运行');
    return false;
  });
  pageEditorRef.handler.addHook('void', async (params: unknown[]) => {
    const action = params?.[0];
    if (action === 'deleteMultiComponent') {
      const keys: number[] = (params?.[1] || []) as number[];
      if (keys?.length === 1) {
        const { views } = await pageEditorRef.handler.getComponentViews();
        const [key] = keys;
        const target = views.filter((view) => view.key === key)[0];
        if (target) {
          if (/gems-materials-pagedoo-base.*\/Background/.test(target.id)) {
            throw new Error('不允许删除Background');
          }
          const limit = adComponentLimitationConfig.filter(
            (cfg) => cfg.name === target.name
          )[0];
          if (!limit) return true;
          if (limit.disableRemove) {
            MessagePlugin.info('该组件不能删除哦，否则会影响直播间的正常运行');
            throw new Error(`不允许删除组件${target.name}`);
          }
        }
      }
      MessagePlugin.info('组件不能清空哦，否则会影响直播间的正常运行');
      throw new Error('action not allow');
    }
    return true;
  });
  pageEditorRef.handler.addHook('addChildComponent', async (data) => {
    if (!pageEditorRef) return false;
    const name = getComponentNameFromHook(data);
    const { views } = await pageEditorRef.handler.getComponentViews();
    if (
      views.some((view) => view.name === name) &&
      NotAllowMultipleComponentNames.indexOf(name) > -1
    ) {
      MessagePlugin.info(
        '该直播间已存在此组件，如需修改组件配置，请点击右侧“该页面组件列表”中对应的组件，并在右侧编辑区修改'
      );
      return false;
    }
    return true;
  });
};
