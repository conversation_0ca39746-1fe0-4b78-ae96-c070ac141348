import { Script } from './../../../../type/pagedoo';
import {
  ADComponentsNameEnum,
  CompLiveSpeechAD,
} from './../../ADLive/components';
import { walkTimelineComponent } from './../../common/updateComponent';
import { BaseScript } from '@/components/ScriptForm/type';
import { PlayConfig } from '@/type/pagedoo';

/**
 *
 * @param playConfig 时间轨协议
 * @param script 脚本
 */
export const adUpdateComponent = (
  playConfig: PlayConfig,
  script: BaseScript | Script
) => {
  return walkTimelineComponent(
    playConfig,
    (comp) => {
      if (
        comp.id.includes(ADComponentsNameEnum.Speech) &&
        script.adExtendData?.updateComponentData?.[ADComponentsNameEnum.Speech]
      ) {
        const updateConfig: Partial<CompLiveSpeechAD['speechConfig']> =
          script.adExtendData?.updateComponentData?.[
            ADComponentsNameEnum.Speech
          ]?.speechConfig || {};
        const data = comp.data as CompLiveSpeechAD;
        if (data?.speechConfig) {
          data.speechConfig = {
            ...data.speechConfig,
            ...updateConfig,
          };
        }
      }
    },
    {
      noClone: true,
    }
  );
};
