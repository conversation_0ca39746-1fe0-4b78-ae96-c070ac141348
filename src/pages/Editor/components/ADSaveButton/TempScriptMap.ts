import { PlayConfig } from '@/type/pagedoo';
import { MaterialsAvatar } from '@/utils/play-component';
import { format, livePlugin } from '@/utils/template/common';
import { BaseScript } from '@/components/ScriptForm/type';
export const TEMP_ID_MAP: Record<string, string> = {
  10000146140508: 'mildewCleaner',
};
// if (import.meta.env.VITE_ENV === 'dev') {
//   TEMP_ID_MAP['10000127467748'] = 'mildewCleaner';
// }
export const tempScriptMap: Record<
  string,
  (options: { script: BaseScript }) => PlayConfig
> = {
  mildewCleaner: ({ script }) => {
    let config: PlayConfig = {
      __config: {
        anchorTime: 0,
        position: {
          x: 0,
          y: 0,
        },
        scaleX: 80,
        selectedID: [],
      },
      fragment: [
        {
          __config: {
            name: '',
          },
          id: 'F918D589-C981-4224-B000-C8B201451121',
          offset: 0,
        },
      ],
      timeline: [
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/LiveBkg`,
                name: 'LiveBkg',
                key: 1001,
                style: {},
                commonStyle: {
                  position: {
                    top: 2.0539125955565396e-15,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 100,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/0892356b4556bbbfec4e04c6fd67c51d.jpg',
                      type: 'img',
                      name: '背景.jpg',
                      width: 1701,
                      length: 3024,
                    },
                  ],
                  presetBkg: [
                    {
                      url: 'https://pagedoo.pay.qq.com/material/@platform/a27f2dc5a90b4b2df19214dabfbff638.png',
                      name: '通用演播厅',
                      type: 'img',
                      width: 768,
                      length: 1536,
                    },
                  ],
                  mode: 'custom',
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/0892356b4556bbbfec4e04c6fd67c51d.jpg',
                      type: 'img',
                      name: '背景.jpg',
                      width: 1701,
                      length: 3024,
                    },
                  ],
                  miaosiAIBkg: {
                    productImg: [
                      {
                        url: '',
                        name: '商品特写',
                        type: 'img',
                        width: 750,
                        length: 622,
                      },
                    ],
                    img: [
                      {
                        url: '',
                        name: 'AI背景',
                        type: 'img',
                        width: 750,
                        length: 622,
                      },
                    ],
                    showProduct: true,
                    creativeText: '',
                    negativePrompt: '',
                    showNegativePrompt: false,
                    moveTargetRefCSS: '',
                  },
                  __component_name: '直播间背景(LiveBkg)',
                  __component_sub_name: '直播间背景',
                  __component_id: 'LiveBkg',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
              duration: 20000,
              id: '34A5401C-A693-44CB-96E1-BFC85BBBA1B4',
              key: 1001,
              offset: 0,
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: '19B4A35A-7981-4694-BD51-6251856EFB4F',
              key: 1040,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveVideo`,
                name: 'LiveVideo',
                key: 1040,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 13.129201916571795,
                    bottom: 0,
                    left: 45.73338667605841,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      top: 'percent',
                      right: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 50,
                    autoHeight: true,
                    height: 80,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  video: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/6a593fecda11c6d241508307168e9485.mp4',
                      type: 'video',
                      name: 'WeChat_20240824165731.mp4',
                    },
                  ],
                  volume: 100,
                  isLoop: true,
                  __mode: 'add',
                  __component_name: '视频',
                  __component_sub_name: '视频',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/Virtualman`,
                name: 'Virtualman',
                key: 1002,
                style: {},
                commonStyle: {
                  position: {
                    top: 10.28454150131457,
                    bottom: 0,
                    left: -1.531740223600042,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 91.90441341600254,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                chosen: false,
                data: {
                  _v: 4,
                  virtualMan: {
                    key: 'e53b41bbe1c7e87f82f970b0f1e4b68a',
                    asset: true,
                    appkey: '1576283ab39a49419138d5ee8ee40586',
                    type: 'qq',
                    enabled: true,
                    img: 'https://pagedoo.pay.qq.com/material/@platform/5113f171ef152dfedcff0032580b1cd2.png',
                    label: '小爱-条纹毛衣-双耳环白色桌子',
                    desc: '',
                    chromaKey: 0.5,
                  },
                  liveID: '',
                  isWave: true,
                  customScript: false,
                  type: 'text',
                  isSegmentTexts: true,
                  voiceConfig: {
                    currentVoiceItem: {
                      platform: 'custom_tts',
                      speed: 1,
                      style: '',
                      voiceExtendConfig:
                        '{"character":"xinhua","emotion":"default"}',
                      voiceId: '92b98570-bba0-4c4a-be27-16276a2c7a42',
                      driverMode: 'voice',
                    },
                  },
                  __component_name: '主播',
                  __component_sub_name: '主播',
                  __component_id: 'Virtualman',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                  __env: 'ams',
                  __vman_linkage_lock: false,
                  keyLight: {},
                },
                actions: [],
              },
              duration: 20000,
              id: 'D39FCB6B-DC7E-4679-B5D8-0C5ED9A009DD',
              key: 1002,
              offset: 0,
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/LiveSpeechAD`,
                name: 'LiveSpeechAD',
                key: 1030,
                style: {},
                commonStyle: {
                  position: {
                    top: -5000,
                    left: -5000,
                    bottom: 0,
                    right: 0,
                    type: 'absolute',
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  speechConfig: {
                    type: 'aigc',
                    aiSpeechInfo: {
                      speechStyle: '',
                      speechTone: '',
                      speechText: '',
                      scriptId: '',
                      productId: '',
                    },
                    goodsInfoList: script.adExtendData?.productList || [],
                    customSpeechInfo: {
                      fileName: '',
                      size: 0,
                      speechText: '',
                    },
                  },
                  height: 0,
                  __component_name: '直播话术(LiveSpeechAD)',
                  __component_sub_name: '直播话术',
                  __component_id: 'LiveSpeechAD',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
              duration: 20000,
              id: '62A3A2FC-0B8D-4B22-8C5A-D51476257FF9',
              key: 1030,
              offset: 0,
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/LiveQA`,
                name: 'LiveQA',
                key: 1031,
                style: {},
                commonStyle: {
                  position: {
                    top: -5000,
                    left: -5000,
                    bottom: 0,
                    right: 0,
                    type: 'absolute',
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  qaConfig: {
                    qaList: [],
                  },
                  height: 0,
                  __component_name: '直播问答库(LiveQA)',
                  __component_sub_name: '直播问答库',
                  __component_id: 'LiveQA',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
              duration: 20000,
              id: '4219FABE-695D-497D-A41E-A2264953B2BB',
              key: 1031,
              offset: 0,
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/BackgroundMusic`,
                name: 'BackgroundMusic',
                key: 1032,
                style: {},
                commonStyle: {
                  position: {
                    top: -5000,
                    left: -5000,
                    bottom: 0,
                    right: 0,
                    type: 'absolute',
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  dataConf: {
                    volume: 20,
                    backgroundMusicList: [],
                    isLoop: true,
                  },
                  height: 0,
                  __component_name: '直播背景音乐(BackgroundMusic)',
                  __component_sub_name: '直播背景音乐',
                  __component_id: 'BackgroundMusic',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
              duration: 20000,
              id: '4A92D7CA-8A7E-41E8-8A61-E58E37738FA2',
              key: 1032,
              offset: 0,
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/LiveProduct`,
                name: 'LiveProduct',
                key: 1033,
                style: {},
                commonStyle: {
                  position: {
                    top: -5000,
                    left: -5000,
                    bottom: 0,
                    right: 0,
                    type: 'absolute',
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  product: '10000146140508',
                  __component_name: '直播间商品',
                  __component_sub_name: '直播间商品',
                  __component_id: 'LiveProduct',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
              duration: 20000,
              id: '52C1AD4C-E32F-492A-BE66-DA4149D0F8D5',
              key: 1033,
              offset: 0,
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: '9F6CADB1-1D62-4E64-8DB6-72EC00792939',
              key: 1034,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveImage`,
                name: 'LiveImage',
                key: 1034,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: -1.4223302076286108,
                    bottom: 0,
                    left: 9.190441341600254,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 81.83869194663083,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/6bcc1bcaaaa7fba9dcb23c6ca9781cfd.png',
                      type: 'img',
                      name: '标题.png',
                      width: 1633,
                      length: 660,
                    },
                  ],
                  mode: 'customBkg',
                  presetBkg: [],
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/6bcc1bcaaaa7fba9dcb23c6ca9781cfd.png',
                      type: 'img',
                      name: '标题.png',
                      width: 1633,
                      length: 660,
                    },
                  ],
                  animationConfig: {
                    animation: 'none',
                    speed: 3,
                    isFilter: false,
                    blur: 5,
                  },
                  __mode: 'add',
                  __component_name: '贴片',
                  __component_sub_name: '贴片',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: '1BE426AF-0E2C-4B41-8076-634989128362',
              key: 1035,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveImage`,
                name: 'LiveImage',
                key: 1035,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 32.38536472754375,
                    bottom: 0,
                    left: -3.501120511085811,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 41.79462610108687,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/61d969a6ff23b34b0b462f46a68cd441.png',
                      type: 'img',
                      name: '权威检测报告.png',
                      width: 600,
                      length: 660,
                    },
                  ],
                  mode: 'customBkg',
                  presetBkg: [],
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/61d969a6ff23b34b0b462f46a68cd441.png',
                      type: 'img',
                      name: '权威检测报告.png',
                      width: 600,
                      length: 660,
                    },
                  ],
                  animationConfig: {
                    animation: 'none',
                    speed: 3,
                    isFilter: false,
                    blur: 5,
                  },
                  __mode: 'add',
                  __component_name: '贴片',
                  __component_sub_name: '贴片',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: 'F6B343E4-D7AD-4DA8-8CD9-6D6DDF9BFA24',
              key: 1039,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveImage`,
                name: 'LiveImage',
                key: 1039,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 65.42718955091608,
                    bottom: 0,
                    left: -0.6564600958285891,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 101.75131485343138,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/6dd20e7ec092c0a0161d929c5f9c675a.png',
                      type: 'img',
                      name: '下贴片.png',
                      width: 1701,
                      length: 805,
                    },
                  ],
                  mode: 'customBkg',
                  presetBkg: [],
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/6dd20e7ec092c0a0161d929c5f9c675a.png',
                      type: 'img',
                      name: '下贴片.png',
                      width: 1701,
                      length: 805,
                    },
                  ],
                  animationConfig: {
                    animation: 'none',
                    speed: 3,
                    isFilter: false,
                    blur: 5,
                  },
                  __mode: 'add',
                  __component_name: '贴片',
                  __component_sub_name: '贴片',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: 'B779BC49-2F2F-4AF6-A5D0-E864663D60A6',
              key: 1036,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveImage`,
                name: 'LiveImage',
                key: 1036,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 50.65683739477283,
                    bottom: 0,
                    left: -3.9387605749715373,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 43.10754629274405,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/2502cbe087019cc09649853672006a94.png',
                      type: 'img',
                      name: '无毒无甲醛.png',
                      width: 600,
                      length: 660,
                    },
                  ],
                  mode: 'customBkg',
                  presetBkg: [],
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/2502cbe087019cc09649853672006a94.png',
                      type: 'img',
                      name: '无毒无甲醛.png',
                      width: 600,
                      length: 660,
                    },
                  ],
                  animationConfig: {
                    animation: 'none',
                    speed: 3,
                    isFilter: false,
                    blur: 5,
                  },
                  __mode: 'add',
                  __component_name: '贴片',
                  __component_sub_name: '贴片',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: '33E94A3C-B050-4C54-B87A-9A4A32C35911',
              key: 1037,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveImage`,
                name: 'LiveImage',
                key: 1037,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 57.877898448887315,
                    bottom: 0,
                    left: 55.580288113487256,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      top: 'percent',
                      right: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 50,
                    autoHeight: true,
                    height: 80,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/c844b170aa6296531ed0246e9aa51eb5.png',
                      type: 'img',
                      name: '产品图.png',
                      width: 1286,
                      length: 682,
                    },
                  ],
                  mode: 'customBkg',
                  presetBkg: [],
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/c844b170aa6296531ed0246e9aa51eb5.png',
                      type: 'img',
                      name: '产品图.png',
                      width: 1286,
                      length: 682,
                    },
                  ],
                  animationConfig: {
                    animation: 'none',
                    speed: 3,
                    isFilter: false,
                    blur: 5,
                  },
                  __mode: 'add',
                  __component_name: '贴片',
                  __component_sub_name: '贴片',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: '120D4EC8-B7C2-4AD2-BAA5-65715E369DD6',
              key: 1038,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveImage`,
                name: 'LiveImage',
                key: 1038,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 9.628081405485982,
                    bottom: 0,
                    left: -6.345780926343033,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      top: 'percent',
                      right: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 50,
                    autoHeight: true,
                    height: 80,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/4c2ce0337eeaf8d67d1b37c5d98a5ed1.png',
                      type: 'img',
                      name: '专属价贴片.png',
                      width: 600,
                      length: 660,
                    },
                  ],
                  mode: 'customBkg',
                  presetBkg: [],
                  customBkg: [
                    {
                      url: 'https://avatarcdnams.pay.qq.com/material/4c2ce0337eeaf8d67d1b37c5d98a5ed1.png',
                      type: 'img',
                      name: '专属价贴片.png',
                      width: 600,
                      length: 660,
                    },
                  ],
                  animationConfig: {
                    animation: 'none',
                    speed: 3,
                    isFilter: false,
                    blur: 5,
                  },
                  __mode: 'add',
                  __component_name: '贴片',
                  __component_sub_name: '贴片',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              hidden: false,
              id: 'CE068205-2F13-4C38-B738-69DB765DE69E',
              key: 1041,
              offset: 0,
              duration: 20000,
              component: {
                id: `component/${MaterialsAvatar}/LiveText`,
                name: 'LiveText',
                key: 1041,
                style: {},
                commonStyle: {
                  transform: {
                    rotate: 0,
                    opacity: 1,
                    scale: 1,
                    radius: 0,
                  },
                  position: {
                    top: 36.76188359536864,
                    bottom: 0,
                    left: 33.26087114301664,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      top: 'percent',
                      right: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 1,
                    autoHeight: true,
                    height: 80,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  fontStyle: {
                    text: '主\n播\nAI\n分\n身',
                    rem: 50,
                    useImage: 0,
                    lineHeight: 1,
                    textAlign: 'left',
                    fontFamily: ['PingFang SC'],
                    letterSpacing: 0,
                    fontSize: 10,
                    width: 0,
                    color: {
                      color: 'rgba(238, 240, 241, 0.8)',
                      realColor: 'rgba(238, 240, 241, 0.8)',
                      show: true,
                    },
                    fontWeight: 600,
                    shadow: false,
                    ellipsis: 0,
                  },
                  text: '主\n播\nAI\n分\n身',
                  animationConfig: {
                    animation: 'none',
                    speed: 40,
                  },
                  __component_name: '文本',
                  __component_sub_name: '文本',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
            },
          ],
        },
        {
          __config: {
            height: 40,
          },
          node: [
            {
              __config: {
                thumbnail: '',
                title: '',
                type: 'component',
              },
              actualDuration: 0,
              component: {
                id: `component/${MaterialsAvatar}/LiveIdentify`,
                name: 'LiveIdentify',
                key: 88248,
                style: {},
                commonStyle: {
                  position: {
                    top: 31.868131868131865,
                    bottom: 0,
                    left: 79.12087912087912,
                    right: 0,
                    outset: 'TopLeft',
                    unit: {
                      left: 'percent',
                      right: 'percent',
                      top: 'percent',
                      bottom: 'percent',
                    },
                    type: 'absolute',
                  },
                  size: {
                    autoWidth: false,
                    width: 8.791208791208792,
                    autoHeight: true,
                    height: 100,
                    widthUnit: 'percent',
                    heightUnit: 'percent',
                  },
                },
                wrapperStyle: {},
                data: {
                  _v: 0,
                  img: [
                    {
                      url: 'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
                      name: '直播间头图',
                      type: 'img',
                      width: 750,
                      length: 750,
                      __resourceId: 'purple_3',
                    },
                  ],
                  corner: 0,
                  componentStyle: [],
                  height: 0,
                  __component_name: '主播AI分身(LiveIdentify)',
                  __component_sub_name: '主播AI分身',
                  __component_id: 'LiveIdentify',
                  __component_mutex_data: '',
                  __pagedoo_i18n: {},
                },
                actions: [],
              },
              duration: 20000,
              id: '61FCA6FD-6ED7-434D-A064-60A054B485DD',
              key: 88248,
              offset: 0,
            },
          ],
        },
      ],
      version: 1,
    };
    livePlugin(config);
    config = format(config);
    return config;
  },
};
