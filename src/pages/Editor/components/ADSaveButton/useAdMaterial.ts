import EventBus from '@tencent/eventbus';
import { Event } from '@tencent/pagedoo-editor';
import { useMount } from 'ahooks';
import { useCallback } from 'react';
import { MessagePlugin } from 'tdesign-react';

const useAdEventBus = () => {
  const listen = useCallback(async () => {
    const handleStateChange = async () => {
      const { eventBus } = (window as any).__editorState.globalData;
      if (!eventBus) return;
      Event.off('onEditorStateChange', handleStateChange);
      const hanleErrMessage = (msg: string) => {
        MessagePlugin.error(msg);
      };
      eventBus.on('VirtualmanErrorMsg', hanleErrMessage);
    };
    Event.on('onEditorStateChange', handleStateChange);
  }, []);
  return { listen };
};

export const useAdMaterials = () => {
  const { listen } = useAdEventBus();

  useMount(() => {
    listen();
  });
};
