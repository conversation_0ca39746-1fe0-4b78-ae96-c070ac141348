/**
 * <AUTHOR>
 * @date 2024/5/27 下午2:52
 * @desc index
 */

import { ADLiveDefaultContentTitle } from '@/pages/Editor/ADLive/defaultOptions';
import { useAdMaterials } from '@/pages/Editor/components/ADSaveButton/useAdMaterial';
import {
  EditorInitMetadata,
  InitialScriptData,
  MetaEditorExtendData,
  SaveLiveFunction,
} from '@/pages/Editor/recoil/store';
import { RespError } from '@/pb/config';
import { PlayConfig } from '@/type/pagedoo';
import { playService } from '@/utils/play-template';
import { templateNameMap } from '@/utils/template/live/constants';
import { getParams } from '@/utils/url';
import { editorHandler, PageEditorRef } from '@tencent/pagedoo-editor';
import { cloneDeep } from 'lodash-es';
import { useCallback, useEffect, useState } from 'react';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { CheckIcon } from 'tdesign-icons-react';
import {
  Button,
  Dialog,
  Form,
  Input,
  MessagePlugin,
  Popconfirm,
  Space,
} from 'tdesign-react';
import { queryScriptList } from '@/components/ScriptForm/utils';
import { tempScriptMap } from './TempScriptMap';
import { updateVersion } from '@/pages/Editor/common/updateComponent';
import { useEditorReady } from '@/pages/Editor/hooks/useEditorReady';
import { adUpdateComponent } from '@/pages/Editor/components/ADSaveButton/adUpdateComponent';
import { ContentDetailQuery } from '@/pb/api/ContentSvr';
import { CheckMetaLiveStatus } from '@/pb/api/MetaLiveSvr';
import { checkPlayConfig } from '@/components/LiveQRCode/common';
import { BaseScript } from '@/components/ScriptForm/type';

const { FormItem } = Form;

export function ADSaveButton() {
  const [form] = Form.useForm();
  const [dialogVisible, setDialogVisible] = useState(false);
  const [popConfirmVisible, setPopConfirmVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const setSaveLive = useSetRecoilState(SaveLiveFunction);
  const [metaEditorExtendData, setMetaEditorExtendData] =
    useRecoilState(MetaEditorExtendData);
  // const [userInfo, setUserInfo] = useRecoilState(UserInfoAtom);
  const [value, setValue] = useState('');
  const [disabled, setDisabled] = useState(false);
  const setInitialScriptData = useSetRecoilState<BaseScript>(InitialScriptData);
  useAdMaterials();
  const setEditorInitMetadata = useSetRecoilState(EditorInitMetadata);
  const { ready } = useEditorReady();

  // const account_id = useMemo(
  //   () => userInfo?.adExtend?.account_id || '',
  //   [userInfo]
  // );
  const initScriptList = useCallback(
    async (scriptId: string, fn?: Function) => {
      try {
        const scriptRes = await queryScriptList(scriptId);
        const scriptInfoRes = JSON.parse(
          scriptRes.script_list[0].script_info || '{}'
        ) as BaseScript;
        setInitialScriptData(scriptInfoRes);
        fn?.(scriptInfoRes);
        // scriptInfoRes.adExtendData = {
        //   templateId: records.length ? records[0].group_id : 'skincare',
        // };
        // if (!scriptInfoRes.adExtendData.templateId) {
        //   const { records } = await Development.GetUserGroupList({
        //     group_type: 4,
        //     user_id: account_id,
        //   });
        //   scriptInfoRes.adExtendData.templateId = records.length
        //     ? records[0].group_id
        //     : 'skincare';
        // }
        const update = () => {
          const size = scriptInfoRes.size || [455, 812];
          const [width, height] = size;
          setEditorInitMetadata({
            size: {
              width,
              height,
            },
          });
          setPreviewDevice(size);
        };
        update();
        setTimeout(update, 200);
        setTimeout(update, 500);
        setTimeout(update, 1000);
        setTimeout(update, 2000);
        setTimeout(update, 5000);
        return scriptInfoRes;
      } catch (error) {
        console.error('error:', error);
        throw error;
      }
    },
    [setEditorInitMetadata, setInitialScriptData]
  );

  const initFromContentId = useCallback(
    async (contentId: string) => {
      try {
        // let liveExtendData: MetaLiveExtendFormValue | undefined = undefined;
        const contentData = await ContentDetailQuery({
          content_id: contentId,
        });
        const { qaGroupId } = contentData.extend;

        // 初始化扩展值
        setMetaEditorExtendData({
          contentName: contentData.content_name,
          contentType: contentData.content_type,
          qaGroupId,
          // metaLiveExtendData: liveExtendData,
        });
        setValue(contentData.content_name);

        await (window.__page_editor.refHandle() as PageEditorRef).waitReady();
        const contentMsg = await (
          window.__page_editor.refHandle() as PageEditorRef
        ).getEditorContent();
        contentMsg.content_title = contentData.content_name;
        await (
          window.__page_editor.refHandle() as PageEditorRef
        ).setEditorContent(contentMsg);

        const { scriptId } = contentData.extend;
        if (!scriptId) {
          void MessagePlugin.error('获取脚本ID失败');
          return;
        }
        if (!location.hash.includes('scriptId')) {
          // 将scriptId加入hash，不支持
          const newHash = `${location.hash}&scriptId=${scriptId}`;
          history.replaceState(null, '', newHash);
          // location.hash = `${location.hash}&scriptId=${scriptId}`;
        }
        // 查询到的脚本
        const scriptResp = await initScriptList(scriptId);

        await editorHandler.setGlobalData('pagedoo-live', { id: contentId });

        // 从内容详情中获取脚本
        const script = JSON.parse(contentData.extend.playScript) as PlayConfig;
        // 编辑情况下直接刷到最新的组件版本
        let updateScript = updateVersion(script);
        updateScript = adUpdateComponent(updateScript, scriptResp);
        console.log('-> 编辑器更新后时间轨协议', updateScript);
        await setPlayConfig(updateScript);
      } catch (e) {
        setDisabled(true);
        console.error(e);
        if (e instanceof RespError) {
          const msg =
            e.resultCode === '1101015'
              ? '正在直播中，查询失败'
              : '查询直播详情失败';
          void MessagePlugin.error(msg);
        } else {
          void MessagePlugin.error('查询直播详情失败');
        }
      }
    },
    [initScriptList, setMetaEditorExtendData]
  );

  // const isFirstLive = useMemo(() => {
  //   // 是否是首次创建直播间
  //   const { contentId, presetId } = getParams();
  //   return presetId && !contentId;
  // }, []);

  useEffect(() => {
    const { scriptId, contentId } = getParams();
    // 脚本跳转来的
    if (!contentId && scriptId) {
      const changeMetaEditorExtendData = (scriptInfoRes: BaseScript) => {
        setMetaEditorExtendData((pre) => ({
          ...pre,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          contentName: templateNameMap[scriptInfoRes.adExtendData!.templateId],
        }));
      };
      initScriptList(scriptId, changeMetaEditorExtendData).then((script) => {
        // 临时逻辑：针对特定商品id，使用与之对应的炫酷的直播间装修
        if (
          script?.adExtendData &&
          Object.keys(tempScriptMap).includes(script.adExtendData.templateId)
        ) {
          setPlayConfig(
            tempScriptMap[script.adExtendData.templateId]({ script })
          )
            .then()
            .catch((e) => {
              console.error(e, '设置脚本失败');
            });
        } else if (script) {
          playService
            .getPlayScript(script)
            .then((playScript) => {
              setPlayConfig(playScript).then();
            })
            .catch((e) => {
              console.error(e, '设置脚本失败');
            });
        }
      });
    } else if (contentId) {
      initFromContentId(contentId).then();
    }
  }, [initFromContentId, initScriptList, setMetaEditorExtendData]);

  const setPlayConfig = async (playScript: PlayConfig) => {
    const cloneScript = cloneDeep(playScript);
    await editorHandler.setGlobalData('pagedoo-play-script', cloneScript);
    const update = () => {
      console.log('setPlayConfig------->');
      window.__pagedoo_play?.setPlayConfig?.(cloneDeep(cloneScript));
    };
    update();
  };

  const setPreviewDevice = async (size: [width: number, height: number]) => {
    const [width, height] = size;
    await editorHandler.setPreviewDevice(['', width, height, '']);
  };

  const save = useCallback(
    async (contentName: string) => {
      console.debug('handleSave', contentName);
      if (!contentName) {
        void MessagePlugin.error('请输入直播间名称');
        return;
      }
      const loadingMessage = await MessagePlugin.loading('保存发布中...', 0);

      try {
        const { qaGroupId } = metaEditorExtendData;
        // if (!qaGroupId) {
        //   const res = await Development.CreateUserGroup({
        //     group_name: contentName,
        //     group_type: QA_GROUP_TYPE,
        //   });
        //   qaGroupId = res.group_id;
        // }
        const origin = getParams().origin || '';
        setMetaEditorExtendData({
          contentName,
          contentType: `${origin}_live`,
          qaGroupId: qaGroupId || '',
        });
        const handler = async () => {
          setLoading(true);
          // 保存
          const saveRes = await (
            window.__page_editor.refHandle() as PageEditorRef
          ).saveContent();
          console.debug(saveRes, 'saveRes');

          if (saveRes instanceof Error) {
            console.log('saveRes-Error', saveRes);
          } else {
            loadingMessage.close();
            setLoading(false);
            return {};
          }
        };
        return await handler();
      } catch (e) {
        MessagePlugin.closeAll();
        console.error(e, '操作失败');
        void MessagePlugin.error(e instanceof Error ? e.message : '操作失败');
      } finally {
        setPopConfirmVisible(false);
        setDialogVisible(false);
        loadingMessage.close();
        setLoading(false);
      }
    },
    [metaEditorExtendData, setMetaEditorExtendData]
  );

  const handleClick = useCallback(
    async (bypassVmanCheck = false) => {
      if (!bypassVmanCheck) {
        try {
          await checkUseVman();
        } catch (e) {
          console.error(e, '数字人被占用');
          if (e instanceof RespError) {
            if (e.resultCode === '1126001') {
              setPopConfirmVisible(true);
              return;
            }
          }
        }
      }
      const contentMsg = await (
        window.__page_editor.refHandle() as PageEditorRef
      ).getEditorContent();
      console.debug('contentMsg:', contentMsg);
      if (
        !contentMsg.content_title ||
        contentMsg.content_title === ADLiveDefaultContentTitle
      ) {
        setPopConfirmVisible(false);
        setDialogVisible(true);
      } else {
        void save(contentMsg.content_title);
      }
    },
    [save]
  );

  const checkUseVman = async () => {
    const playScript = (await editorHandler.getGlobalData(
      'pagedoo-play-script'
    )) as PlayConfig;
    const { virtualmanMetaLiveData } = checkPlayConfig(playScript);
    return CheckMetaLiveStatus({
      meta_live_id: '',
      virtual_man_live_data: virtualmanMetaLiveData.map((item) => ({
        platform: item.platform || '',
        platform_account_id: item.platformAcctId || '',
        virtualman_key: item.virtualManKey || '',
      })),
    });
  };

  useEffect(() => {
    const handleSaveLive = async (value: string) => {
      setValue(value);
      await save(value);
    };
    setSaveLive({ saveLive: handleSaveLive });
  }, [save, setSaveLive]);

  return (
    <div className="pagedoo-meta-live-global">
      <Popconfirm
        showArrow={false}
        theme="warning"
        // content="您选择的主播正在别的直播间开播中，需要等该主播下播后才能在您的直播间开播"
        content={
          <>
            <div className="font-semibold text-black leading-[20px]">提示</div>
            <div className="text-desc">
              您选择的主播正在别的直播间开播中，需要等该主播下播后才能在您的直播间开播
            </div>
          </>
        }
        visible={popConfirmVisible}
        confirmBtn={
          <span className="pagedoo-meta-live-global">
            <Button
              className="gradient-primary"
              loading={loading}
              onClick={() => {
                handleClick(true).then();
              }}
            >
              继续保存
            </Button>
          </span>
        }
        cancelBtn={
          <span className="pagedoo-meta-live-global">
            <Button
              className="gradient-default"
              theme="default"
              onClick={() => {
                setPopConfirmVisible(false);
              }}
            >
              取消并返回
            </Button>
          </span>
        }
      >
        <Button
          className="gradient-primary"
          icon={<CheckIcon />}
          disabled={disabled || !ready}
          onClick={() => {
            handleClick().then();
          }}
        >
          确认，去开播
        </Button>
      </Popconfirm>

      <Dialog
        visible={dialogVisible}
        onClose={() => {
          setDialogVisible(false);
        }}
        width={532}
        header="保存"
        closeOnOverlayClick={false}
        placement="center"
        className="pagedoo-meta-live-global"
        footer={
          <div className="mt-20 flex justify-end relative">
            <Space>
              <Button
                theme="default"
                className="gradient-default"
                onClick={() => {
                  setDialogVisible(false);
                }}
              >
                取消
              </Button>
              <Button
                loading={loading}
                onClick={() => {
                  const values = form.getFieldsValue(true) as {
                    contentName: string;
                  };
                  save(values.contentName).then();
                }}
                className="gradient-primary"
              >
                保存
              </Button>
            </Space>
          </div>
        }
      >
        <Form
          form={form}
          colon
          rules={{
            contentName: [
              {
                required: true,
                message: '请输入直播间名称',
                type: 'error',
              },
            ],
          }}
          labelWidth="106px"
          labelAlign="left"
        >
          <FormItem label="直播间名称" name="contentName" initialData={value}>
            <Input style={{ width: '100%' }} placeholder="请输入直播间名称" />
          </FormItem>
        </Form>
      </Dialog>
    </div>
  );
}
