/**
 * <AUTHOR>
 * @date 2024/5/3 下午3:53
 * @desc index
 */

import React, { useCallback, useEffect } from 'react';
import { MessagePlugin } from 'tdesign-react';
import { ContentSvr, Development, MetaFeedbackSvr } from '@/pb/pb';
import { getParams } from '@/utils/url';
import { PlayConfig, Script } from '@/type/pagedoo';
import { playService } from '@/utils/play';
import { editor<PERSON>andler } from '@tencent/pagedoo-editor';
import { useRecoilState, useSetRecoilState } from 'recoil';
import {
  EditorInitMetadata,
  InitialScriptData,
  MetaEditorExtendData,
} from '@/pages/Editor/recoil/store';
import { cloneDeep, isEqual } from 'lodash-es';
import {
  LANDSCAPE_DEFAULT_SIZE,
  TEXT_DEFAULT_SIZE,
} from '@/pages/Question/constant';
import { MetaLiveExtendFormValue } from '@/pages/Editor/components/LiveSaveButton/MetaLiveExtendForm';
import { EMPTY_SCRIPT_RESEARCH_ID } from '@/pages/ScriptList/const';
import { getTemplateInfo } from '@/utils/template/common';
import { ExtractAtomType } from '@/utils/type-util';
import { useLatest } from 'ahooks';
import { queryScriptList } from '@/components/ScriptForm/utils';
import { updateVersion } from '@/pages/Editor/common/updateComponent';

export function LiveScriptDrawer() {
  const [metaEditorExtendData, setMetaEditorExtendData] =
    useRecoilState(MetaEditorExtendData);
  const setEditorInitMetadata = useSetRecoilState(EditorInitMetadata);
  const setInitialScriptData = useSetRecoilState<Script>(InitialScriptData);
  const latestEditorExtendData = useLatest(metaEditorExtendData);

  const initScriptList = useCallback(
    async (scriptId: string, isFirstLive = false) => {
      let scriptRes = null;
      try {
        if (scriptId !== EMPTY_SCRIPT_RESEARCH_ID) {
          scriptRes = await queryScriptList(scriptId);
        }
        const templateInfo = getTemplateInfo();
        const { deviceSize } = getParams();
        const paramDeviceSize = {
          '1': LANDSCAPE_DEFAULT_SIZE,
          '2': TEXT_DEFAULT_SIZE,
        }[deviceSize];
        const scriptInfoRes = JSON.parse(
          scriptRes?.script_list?.[0]?.script_info || '{}'
        ) as Script;
        setInitialScriptData(scriptInfoRes);
        // 从空白页创建模版，设置模版名字
        isFirstLive &&
          templateInfo?.name &&
          setMetaEditorExtendData((pre) => ({
            ...pre,
            contentName: templateInfo.name!,
          }));
        const update = () => {
          // 优先获取脚本尺寸，然后看有没有模板尺寸，然后看url上是否指定了尺寸，最后走默认尺寸
          const sizeFromExtend = latestEditorExtendData.current.sizeInfo
            ? [
                latestEditorExtendData.current.sizeInfo.width,
                latestEditorExtendData.current.sizeInfo.height,
              ]
            : null;
          const size =
            scriptInfoRes.size ||
            templateInfo?.size ||
            paramDeviceSize ||
            sizeFromExtend ||
            TEXT_DEFAULT_SIZE;
          const [width, height] = size;
          setEditorInitMetadata({
            size: {
              width,
              height,
            },
          });
          setPreviewDevice(size);
        };
        update();
        setTimeout(update, 200);
        setTimeout(update, 500);
        setTimeout(update, 1000);
        setTimeout(update, 2000);
        setTimeout(update, 5000);
        return scriptInfoRes;
      } catch (error) {
        console.error('error:', error);
      }
    },
    [
      latestEditorExtendData,
      setEditorInitMetadata,
      setInitialScriptData,
      setMetaEditorExtendData,
    ]
  );

  const initFromContentId = useCallback(
    async (contentId: string) => {
      try {
        let liveExtendData: MetaLiveExtendFormValue | undefined = undefined;
        const contentData = await ContentSvr.ContentDetailQuery({
          content_id: contentId,
        });

        if (contentData.content_type.includes('live')) {
          const res = await Development.GetLiveExtendConfigItems({
            live_id: contentId,
            node_id: 'global',
          });
          const initData = res.records.find(
            (item) => item.config_item_id === 'MetaLiveExtendFormValue'
          );
          if (initData) {
            liveExtendData = JSON.parse(initData.config_item_value);
          }
        }
        const extendFields = contentData.extend || {};
        let sizeInfo: { width: number; height: number } | undefined;
        if (extendFields.video_play_size) {
          try {
            const size = JSON.parse(extendFields.video_play_size) as [
              width: number,
              height: number
            ];
            if (Array.isArray(size) && size.length === 2) {
              sizeInfo = {
                width: size[0],
                height: size[1],
              };
            }
          } catch {}
        }
        // 初始化扩展值
        setMetaEditorExtendData({
          contentName: contentData.content_name,
          contentType: contentData.content_type,
          metaLiveExtendData: liveExtendData,
          // 从扩展字段解析封面信息
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          videoPosterInfo: {
            videoCustomPosterUrl: extendFields.videoCustomPosterUrl,
            videoPosterType: extendFields.videoPosterType,
          } as ExtractAtomType<typeof MetaEditorExtendData>['videoPosterInfo'],
          ...(sizeInfo
            ? {
                sizeInfo,
              }
            : null),
        });

        const { scriptId, pagodooQA } = contentData.extend;
        if (!scriptId) {
          void MessagePlugin.error('获取脚本ID失败');
          return;
        }
        if (!location.hash.includes('scriptId')) {
          // 将scriptId加入hash，不支持
          const newHash = `${location.hash}&scriptId=${scriptId}`;
          history.replaceState(null, '', newHash);
          // location.hash = `${location.hash}&scriptId=${scriptId}`;
        }
        await initScriptList(scriptId);

        if (pagodooQA) {
          editorHandler.setGlobalData(
            'pagedoo-qa',
            JSON.parse(pagodooQA || '{}')
          );
        }

        editorHandler.setGlobalData('pagedoo-live', { id: contentId });

        // 从内容详情中获取脚本
        const script = JSON.parse(contentData.extend.playScript) as PlayConfig;
        // 编辑情况下直接刷到最新的组件版本
        const updateScript = await updateVersion(script);
        if (!isEqual(updateScript, script)) {
          void MessagePlugin.info('当前组件已更新');
        }
        await setPlayConfig(updateScript);
      } catch (e) {
        console.error('error:', e);
        void MessagePlugin.error('获取内容详情失败');
      }
    },
    [initScriptList, setMetaEditorExtendData]
  );

  useEffect(() => {
    const { scriptId, contentId } = getParams();
    // 脚本跳转来的
    if (!contentId && scriptId) {
      initScriptList(scriptId, true).then((script) => {
        if (script) {
          playService
            .getPlayScript(script)
            .then((playScript) => {
              return setPlayConfig(playScript);
            })
            .then(() => MessagePlugin.info('设置脚本成功'))
            .catch((e) => {
              console.error(e, '设置脚本失败');
            });
        }
      });
    } else if (contentId) {
      initFromContentId(contentId).then(() => {
        initScriptList(scriptId, true).then((script) => {
          if (script) {
            playService
              .getPlayScript(script)
              .then((playScript) => {
                return setPlayConfig(playScript);
              })
              .then(() => MessagePlugin.info('设置脚本成功'))
              .catch((e) => {
                console.error(e, '设置脚本失败');
              });
          }
        });
      });
    }
  }, [initFromContentId, initScriptList]);

  const setPlayConfig = async (playScript: PlayConfig) => {
    const cloneScript = cloneDeep(playScript);
    editorHandler.setGlobalData('pagedoo-play-script', cloneScript);
    const update = () => {
      console.log('setPlayConfig------->');
      window.__pagedoo_play?.setPlayConfig?.(cloneDeep(cloneScript));
    };
    update();
    // setTimeout(update, 1000);
    // setTimeout(update, 2000);
    // setTimeout(update, 3000);
    // setTimeout(update, 4000);
    // setTimeout(update, 5000);
  };

  const setPreviewDevice = async (size: [width: number, height: number]) => {
    const [width, height] = size;
    editorHandler.setPreviewDevice(['', width, height, '']);
  };

  return <></>;
}
