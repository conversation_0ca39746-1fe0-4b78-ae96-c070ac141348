/**
 * <AUTHOR>
 * @date 2024/5/17 上午11:32
 * @desc index
 */

import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
} from 'react';
import './index.less';
import { SlideSelector } from '@/components/SlideSelector';
import { Checkbox, Form, InputNumber, Popup, Select } from 'tdesign-react';
import { ChevronRightIcon, HelpCircleIcon } from 'tdesign-icons-react';
import {
  QA_GROUP_TYPE,
  useGroupRequest,
} from '@/pages/QALib/hooks/useGroupRequest';
import { useNavigate } from 'react-router-dom';

const { Option } = Select;

const { FormItem } = Form;

export interface MetaLiveExtendFormValue {
  // 答案来源
  answerType: 'AI' | 'qaLib';
  // 问题库选择
  selectQALib?: string[];
  // 只回答问题库范围内问题 ['onlyAnswerInLib']表示true, []表示false
  answerConfig?: string[];
  // 互动类型
  interaction: 'limitTime' | 'limitCount' | 'noLimit';
  // 指定时长
  limitTime?: number;
  // 指定问题数
  limitCount?: number;
  // 过滤弹幕已回答问题 ['filterAnsweredQuestion'] 表示true, []表示false
  filterConfig: string[];
}

export interface IMetaLiveExtendFormRef {
  submit: () => Promise<MetaLiveExtendFormValue | false>;
  validate: () => Promise<boolean>;
}

interface IProps {
  initValue?: MetaLiveExtendFormValue;
}

export const MetaLiveExtendForm = forwardRef<IMetaLiveExtendFormRef, IProps>(
  (props, ref) => {
    const { initValue } = props;
    const [form] = Form.useForm();
    const { records } = useGroupRequest({
      queryParamsDefault: {
        groupType: QA_GROUP_TYPE,
      },
    });

    const options = useMemo(() => {
      return records.map((item) => ({
        label: item.group_name,
        value: item.group_id,
      }));
    }, [records]);

    useEffect(() => {
      if (initValue) {
        form.setFieldsValue(initValue);
      }
    }, [form, initValue]);

    const handleSubmit = async () => {
      const res = await form.validate();
      if (res === true) {
        return form.getFieldsValue(true) as MetaLiveExtendFormValue;
      }
      return false;
    };

    const handleValidate = async () => {
      const res = await form.validate();
      return res === true;
    };

    useImperativeHandle(ref, () => ({
      async submit() {
        return await handleSubmit();
      },
      async validate() {
        return await handleValidate();
      },
    }));

    const linkToQALib = () => {
      console.log('前往编辑问答库');
      // 跳转问答库
      window.open(`#/qa-lib`);
    };
    return (
      <div
        className="meta-live-extend-form-comp"
        style={{ borderTop: 'unset', marginTop: 'unset' }}
      >
        <header>
          <span className="meta-live-extend-form-comp-title mr-8">
            互动配置
          </span>
          <span className="meta-live-extend-form-comp-desc">
            本直播包含互动环节，请完善以下内容
          </span>
        </header>

        <div className="p-12">
          <Form form={form} colon labelWidth="106px" labelAlign="top">
            <FormItem
              label="互动开启方式"
              name="reactionType"
              initialData="custom"
              rules={[
                {
                  required: true,
                  message: '互动开启方式必填',
                  type: 'warning',
                },
              ]}
            >
              <SlideSelector
                border
                options={[
                  {
                    name: '指定环节开始互动',
                    value: 'custom',
                  },
                  {
                    name: '自动即时互动',
                    value: 'auto',
                    disable: true,
                    tips: '敬请期待',
                  },
                ]}
              />
            </FormItem>
            <FormItem
              label="答案来源"
              name="answerType"
              initialData="qaLib"
              rules={[
                { required: true, message: '答案来源必填', type: 'warning' },
              ]}
            >
              <SlideSelector
                border
                options={[
                  {
                    name: '大模型智能回复',
                    value: 'AI',
                  },
                  {
                    name: '绑定问答库',
                    value: 'qaLib',
                  },
                ]}
              />
            </FormItem>

            <FormItem
              shouldUpdate={(prev, next) => prev.answerType !== next.answerType}
            >
              {/* extend*/}
              {({ getFieldValue }) => {
                const answerType = getFieldValue(
                  'answerType'
                ) as MetaLiveExtendFormValue['answerType'];
                return answerType === 'qaLib' ? (
                  <>
                    <FormItem
                      label="问题库选择"
                      name="selectQALib"
                      initialData={
                        initValue?.selectQALib ? initValue.selectQALib : []
                      }
                      rules={[
                        {
                          required: true,
                          message: '问题库必填',
                          type: 'warning',
                        },
                      ]}
                    >
                      <Select placeholder="请选择问题库" multiple>
                        {options.map((item) => (
                          <Option
                            value={item.value}
                            label={item.label}
                            key={item.value}
                          />
                        ))}
                      </Select>
                    </FormItem>
                    <div className="flex items-center">
                      <div
                        className="t-form__label t-form__label--colon t-form__label--left"
                        style={{ width: '106px' }}
                      />
                      <FormItem
                        name="answerConfig"
                        className="relative"
                        style={{
                          width: `calc(100% - 106px)`,
                          marginBottom: '16px',
                        }}
                        initialData={
                          initValue?.answerConfig ? initValue.answerConfig : []
                        }
                      >
                        <Checkbox.Group>
                          <Checkbox value="onlyAnswerInLib">
                            只回答问题库范围内问题
                          </Checkbox>
                        </Checkbox.Group>

                        <div
                          className="flex items-center cursor-pointer absolute"
                          style={{ right: 0 }}
                        >
                          <span
                            className="gradient-primary-text"
                            onClick={linkToQALib}
                          >
                            前往编辑问答库
                          </span>
                          <ChevronRightIcon style={{ color: '#0853FF' }} />
                        </div>
                      </FormItem>
                    </div>
                  </>
                ) : (
                  <div
                    style={{
                      paddingLeft: '106px',
                      marginBottom: '16px',
                      color: 'rgba(0, 0, 0, 0.26)',
                    }}
                  >
                    所有提问都将由混元大模型生成答案
                  </div>
                );
              }}
            </FormItem>
            <FormItem
              label="互动时长"
              name="interaction"
              initialData="limitTime"
              rules={[
                { required: true, message: '互动时长必填', type: 'warning' },
              ]}
            >
              <SlideSelector
                border
                options={[
                  {
                    name: '指定时长',
                    value: 'limitTime',
                  },
                  {
                    name: '指定问题数',
                    value: 'limitCount',
                  },
                  {
                    name: '回复全部',
                    value: 'noLimit',
                  },
                  {
                    name: '手动停止',
                    value: 'manualStop',
                  },
                ]}
              />
            </FormItem>
            <FormItem
              shouldUpdate={(prev, next) =>
                prev.interaction !== next.interaction
              }
            >
              {/* extend*/}
              {({ getFieldValue }) => {
                const interaction = getFieldValue(
                  'interaction'
                ) as MetaLiveExtendFormValue['interaction'];
                // if (interaction === 'noLimit') return <></>;
                return (
                  <>
                    <FormItem
                      label={null}
                      name="limitTime"
                      initialData={
                        initValue?.limitTime ? initValue.limitTime : 30
                      }
                      style={{
                        display: interaction === 'limitTime' ? 'block' : 'none',
                      }}
                    >
                      {/* <div*/}
                      {/*  className="t-form__label t-form__label--colon t-form__label--left"*/}
                      {/*  style={{ width: '106px' }}*/}
                      {/*/ >*/}
                      <InputNumber
                        style={{ width: `calc(100% - 106px)` }}
                        size="medium"
                        theme="row"
                        placeholder="请输入"
                        suffix={<span>分钟</span>}
                      />
                    </FormItem>
                    <FormItem
                      label={null}
                      name="limitCount"
                      initialData={
                        initValue?.limitCount ? initValue.limitCount : 30
                      }
                      style={{
                        display:
                          interaction === 'limitCount' ? 'block' : 'none',
                      }}
                    >
                      {/* <div*/}
                      {/*  className="t-form__label t-form__label--colon t-form__label--left"*/}
                      {/*  style={{ width: '106px' }}*/}
                      {/*/ >*/}
                      <InputNumber
                        style={{ width: `calc(100% - 106px)` }}
                        size="medium"
                        theme="row"
                        placeholder="请输入"
                        suffix={<span>条</span>}
                      />
                    </FormItem>
                  </>
                );
              }}
            </FormItem>

            <div className="flex items-center">
              {/* <div*/}
              {/*  className="t-form__label t-form__label--colon t-form__label--left"*/}
              {/*  style={{ width: '106px' }}*/}
              {/*/ >*/}
              <FormItem
                name="filterConfig"
                className="relative"
                style={{ width: `calc(100% - 106px)` }}
              >
                <Checkbox.Group>
                  <Checkbox value="filterAnsweredQuestion">
                    过滤弹幕已回复问题
                  </Checkbox>
                </Checkbox.Group>

                <div className="flex items-center">
                  <Popup
                    trigger="hover"
                    placement="right"
                    showArrow
                    content="即评论区已经文字答复过的问题，不再进行数字人口播回复"
                  >
                    <HelpCircleIcon style={{ marginLeft: '4px' }} />
                  </Popup>
                </div>
              </FormItem>
            </div>
          </Form>
        </div>
      </div>
    );
  }
);
