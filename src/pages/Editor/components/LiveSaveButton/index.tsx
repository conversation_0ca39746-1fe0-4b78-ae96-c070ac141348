/**
 * <AUTHOR>
 * @date 2024/5/2 下午2:59
 * @desc index
 */

import { CONTENT_TYPE_MAP } from '@/const/common';
import { LiveSaveComposeDialog } from '@/pages/Editor/components/LiveSaveButton/components/LiveSaveComposeDialog';
import { editorInitMetaInfoSelector } from '@/pages/Editor/recoil/selector';
import {
  EditorInitMetadata,
  MetaEditorExtendData,
  SaveLiveFunction,
} from '@/pages/Editor/recoil/store';
import { getParam } from '@tencent/midas-util';
import {
  PageEditorRef,
  handler as editorHandler,
} from '@tencent/pagedoo-editor';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { CheckIcon } from 'tdesign-icons-react';
import { But<PERSON>, Form, MessagePlugin } from 'tdesign-react';
import { FormValues } from './components/LiveConfigSaveDialog';
import { ExtractAtomType } from '@/utils/type-util';
import { useEditorReady } from '@/pages/Editor/hooks/useEditorReady';

enum StepEnum {
  // 初始化
  INIT = 'init',
  // 保存
  SAVE = 'save',
  // 视频制作
  VIDEO_MAKE_CONFIRM = 'video_make_confirm',

  // 视频制作中阶段
  VIDEO_MAKE_PENDING = 'video_make_pending',
}

export enum ContentTypeMap {
  SHORT_VIDEO = 'video',
  META_LIVE = 'live',
}

export const ContentConfigMap = {
  [CONTENT_TYPE_MAP.Live.value]: {
    title: '直播间',
    action: '开播',
  },
  [CONTENT_TYPE_MAP.Video.value]: {
    title: '短视频',
    action: '视频下载',
  },
};

export function LiveSaveButton() {
  const [visible, setVisible] = useState(false);
  const [selectVal, setSelectVal] = useState<FormValues['contentType']>(
    CONTENT_TYPE_MAP.Live.value
  );
  const [loading, setLoading] = useState(false);
  const { ready } = useEditorReady();
  const [metaEditorExtendData, setMetaEditorExtendData] =
    useRecoilState(MetaEditorExtendData);
  const setSaveLive = useSetRecoilState(SaveLiveFunction);
  const editorInitMetadata = useRecoilValue(EditorInitMetadata);
  const navigate = useNavigate();
  const paramContentType = getParam('contentType');

  const commonEditorExtendData = useMemo<
    Partial<ExtractAtomType<typeof MetaEditorExtendData>>
  >(() => {
    return {
      sizeInfo: editorInitMetadata.size,
    };
  }, [editorInitMetadata]);

  const linkToList = useCallback(() => {
    // if (!metaEditorExtendData.contentType) {
    //   return;
    // }
    // const type = metaEditorExtendData.contentType.split('_')[1];
    //
    // const origin = getParams().origin || '';
    //   FIXME: 等统一
    try {
      if (paramContentType === CONTENT_TYPE_MAP.Video.value) {
        navigate('/video-list', {
          replace: true,
        });
        return;
      }

      navigate(`/live-list`, {
        replace: true,
      });
    } finally {
      location.reload();
    }
  }, [navigate, paramContentType]);
  const save = useCallback(async () => {
    const loadingMessage = await MessagePlugin.loading('保存发布中...', 0);
    try {
      const handler = async () => {
        setLoading(true);
        // 保存
        const saveRes = await (
          window.__page_editor.refHandle() as PageEditorRef
        ).saveContent();
        console.debug(saveRes, 'saveRes');

        if (saveRes instanceof Error) {
          console.log('saveRes-Error', saveRes);
          throw saveRes;
        } else {
          loadingMessage.close();
          setLoading(false);
          // setStatus('done');
          return {};
        }
      };
      return await handler();
    } catch (e) {
      MessagePlugin.closeAll();
      console.error(e, '操作失败');
      void MessagePlugin.error(e instanceof Error ? e.message : '操作失败');
      throw e;
    } finally {
      loadingMessage.close();
      setLoading(false);
    }
  }, []);

  const handleSubmit = useCallback(
    async (values: FormValues) => {
      setSelectVal(values.contentType);
      setMetaEditorExtendData((i) => ({
        ...i,
        ...commonEditorExtendData,
        contentName: values.contentName,
        videoPosterInfo: values?.videoPosterInfo ?? undefined,
      }));
      await save();
    },
    [commonEditorExtendData, save, setMetaEditorExtendData]
  );

  useEffect(() => {
    const handleSaveLive = async (value: string) => {
      const content_type =
        metaEditorExtendData.contentType.split('_')?.[1] ||
        paramContentType ||
        CONTENT_TYPE_MAP.Live.value;
      setSelectVal(content_type as FormValues['contentType']);
      setMetaEditorExtendData((i) => ({
        ...i,
        ...commonEditorExtendData,
        contentName: value,
        contentType: content_type,
      }));
      await save();
      linkToList();
    };
    setSaveLive({
      saveLive: handleSaveLive,
    });
  }, [
    setSaveLive,
    save,
    handleSubmit,
    linkToList,
    metaEditorExtendData,
    paramContentType,
    setMetaEditorExtendData,
    commonEditorExtendData,
  ]);

  return (
    <div className="pagedoo-meta-live-global">
      <Button
        className="gradient-primary"
        icon={<CheckIcon />}
        onClick={() => {
          // setStep(StepEnum.SAVE);
          setVisible(true);
        }}
        disabled={visible || !ready}
      >
        保存
      </Button>
      <LiveSaveComposeDialog
        visible={visible}
        onClose={() => setVisible(false)}
        onNavigatePage={linkToList}
        onSave={handleSubmit}
        contentType={
          metaEditorExtendData.contentType.split('_')?.[1] ||
          paramContentType ||
          CONTENT_TYPE_MAP.Live.value
        }
        contentName={metaEditorExtendData.contentName || ''}
        getContentInfo={async () => {
          const pagedooLive = (await editorHandler.getGlobalData(
            'pagedoo-live'
          )) as { id?: string };
          if (!pagedooLive.id) throw new Error('get contentId err');
          return {
            contentId: pagedooLive.id,
          };
        }}
        defaultVideoPosterInfo={metaEditorExtendData.videoPosterInfo}
      />
    </div>
  );
}
