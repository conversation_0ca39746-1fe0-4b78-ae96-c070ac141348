import { VideoMakerForm } from '@/components/VideoMaker';
import { editorInitMetaInfoSelector } from '@/pages/Editor/recoil/selector';
import { VideoSvr } from '@/pb/pb';
import to from 'await-to-js';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import { TimeFilledIcon } from 'tdesign-icons-react';
import { Button, Dialog, MessagePlugin, Space } from 'tdesign-react';
import {
  FormValues,
  ILiveConfigSaveDialogProps,
  LiveConfigSaveDialog,
} from './LiveConfigSaveDialog';

export interface ILiveSaveComposeDialogProps
  extends Pick<
    ILiveConfigSaveDialogProps,
    'contentName' | 'contentType' | 'defaultVideoPosterInfo'
  > {
  visible: boolean;
  onClose: () => void;
  // 触发保存
  onSave: (values: FormValues) => Promise<void>;
  // 触发页面导航
  onNavigatePage: () => void;
  // 获取制作内容，需要返回内容id
  getContentInfo: () => Promise<{ contentId: string }>;
  direction?: 'horizontal' | 'vertical';
}
enum StepEnum {
  // 初始化
  INIT = 'init',
  // 保存
  SAVE = 'save',
  // 视频制作
  VIDEO_MAKE_CONFIRM = 'video_make_confirm',

  // 视频制作中阶段
  VIDEO_MAKE_PENDING = 'video_make_pending',
}

// 整合直播/视频保存流程的弹框
export function LiveSaveComposeDialog(props: ILiveSaveComposeDialogProps) {
  const {
    onClose,
    visible,
    onSave,
    onNavigatePage,
    getContentInfo,
    contentType,
    contentName,
    defaultVideoPosterInfo,
    direction,
  } = props;
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<StepEnum>(StepEnum.INIT);
  const editorMetadata = useRecoilValue(editorInitMetaInfoSelector);
  const navigate = useNavigate();
  useMemo(() => {
    setStep(visible ? StepEnum.SAVE : StepEnum.INIT);
  }, [visible]);
  const gotoRoute = (path: string) => {
    navigate(path, {
      replace: true,
    });
    location.reload();
  };
  return (
    <div className="pagedoo-meta-live-global">
      <LiveConfigSaveDialog
        // 当前编辑器方向
        visible={step === StepEnum.SAVE}
        contentType={contentType}
        onGotoIndex={() => {
          try {
            // 跨域检测时会抛出安全错误
            if (window.opener && !window.opener.closed) {
              window.close();
            } else {
              gotoRoute(contentType === 'video' ? '/video-list' : '/live-list');
            }
          } catch (error) {
            console.warn('跨域检测限制:', error);
            gotoRoute(contentType === 'video' ? '/video-list' : '/live-list');
          }
        }}
        onGoToList={onNavigatePage}
        onSubmit={async (values) => {
          try {
            setLoading(true);
            return await onSave(values);
          } finally {
            setLoading(false);
          }
        }}
        handleClose={onClose}
        contentName={contentName}
        onTriggerMakeVideo={() => {
          setStep(StepEnum.VIDEO_MAKE_CONFIRM);
        }}
        loading={loading}
        defaultVideoPosterInfo={defaultVideoPosterInfo}
      />
      <Dialog
        visible={[
          StepEnum.VIDEO_MAKE_CONFIRM,
          StepEnum.VIDEO_MAKE_PENDING,
        ].includes(step)}
        destroyOnClose
        onClose={onClose}
        header="制作视频"
        footer={null}
        closeOnEscKeydown={false}
        closeOnOverlayClick={false}
        closeBtn={step === StepEnum.VIDEO_MAKE_PENDING ? null : true}
      >
        <div className="pagedoo-meta-live-global">
          {step === StepEnum.VIDEO_MAKE_CONFIRM && (
            <>
              <VideoMakerForm
                defaultDirection={
                  direction ? direction : editorMetadata.orientation
                }
                hideOrientationSelector
                onSubmit={async (data) => {
                  // const contentId
                  (async () => {
                    setLoading(true);
                    const [contentErr, info] = await to(getContentInfo());
                    if (contentErr) {
                      MessagePlugin.error('获取内容信息失败');
                      return;
                    }
                    const { contentId } = info;
                    const [err] = await to(
                      VideoSvr.VideoRelease({
                        video_id: contentId,
                        video_format: data.format,
                        video_definition: data.definition,
                      })
                    );
                    if (err) {
                      MessagePlugin.error(`制作视频失败：${err.message}`);
                      return;
                    }
                    setStep(StepEnum.VIDEO_MAKE_PENDING);
                  })().finally(() => {
                    setLoading(false);
                  });
                }}
              >
                {(instance) => {
                  return (
                    <div className="mt-28 flex justify-end relative">
                      <Space>
                        <Button
                          theme="default"
                          className="gradient-default"
                          onClick={onClose}
                          disabled={loading}
                        >
                          取消
                        </Button>
                        <Button
                          loading={loading}
                          onClick={() => {
                            // 触发表单提交
                            instance.submit();
                          }}
                          className="gradient-primary"
                        >
                          开始制作
                        </Button>
                      </Space>
                    </div>
                  );
                }}
              </VideoMakerForm>
            </>
          )}
          {step === StepEnum.VIDEO_MAKE_PENDING && (
            <div className="my-16 flex items-center flex-col gap-5">
              <TimeFilledIcon
                width={39}
                height={39}
                style={{
                  color: '#96BBF8',
                  width: '39px',
                  height: '39px',
                }}
              />
              <h3 className="font-semibold">视频制作中</h3>
              <span>
                您可以前往
                <a className="text-primary" onClick={onNavigatePage}>
                  视频管理页
                </a>
                查看进度
              </span>
              <div className="flex gap-7 justify-between">
                <Button
                  theme="default"
                  className="gradient-default"
                  onClick={onNavigatePage}
                >
                  前往视频管理
                </Button>
                <Button
                  onClick={() => {
                    gotoRoute('/');
                  }}
                  className="gradient-primary"
                >
                  返回首页
                </Button>
              </div>
            </div>
          )}
        </div>
      </Dialog>
    </div>
  );
}
