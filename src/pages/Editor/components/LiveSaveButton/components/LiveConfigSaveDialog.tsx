import ActionSuccess from '@/assets/images/create-condition-success.png';
import { SlideSelector } from '@/components/SlideSelector';
import { MATERIAL_TYPE } from '@/configs/upload';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { uploadRequest } from '@/utils/cos';
import { useLatest, useMount } from 'ahooks';
import to from 'await-to-js';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Button,
  Dialog,
  Form,
  FormRules,
  Input,
  Loading,
  Radio,
  Space,
  Upload,
  UploadFile,
} from 'tdesign-react';
import { UploadRef } from 'tdesign-react/es/upload/interface';
import './index.less';

export interface ILiveConfigSaveDialogProps {
  visible: boolean;
  contentType: string;
  contentName?: string;
  handleClose: () => void;
  onGoToList: () => void;
  // 返回首页回调
  onGotoIndex: () => void;
  onSubmit: (value: FormValues) => Promise<void>;
  // 触发视频制作
  onTriggerMakeVideo: () => void;
  loading?: boolean;
  defaultVideoPosterInfo?: FormValues['videoPosterInfo'];
}
export interface FormValues {
  contentType: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'];
  contentName: string;
  // 以下字段在 contentType为video时有效
  videoPosterInfo?: {
    videoPosterType?: 'custom' | 'first_frame';
    videoCustomPosterUrl?: string;
  };
}

const { FormItem } = Form;
export const ContentConfigMap = {
  [CONTENT_TYPE_MAP.Live.value]: {
    title: '直播间',
    action: '开播',
  },
  [CONTENT_TYPE_MAP.Video.value]: {
    title: '视频',
    action: '视频制作与下载',
  },
};

function VideoPosterField(props: {
  value?: FormValues['videoPosterInfo'] | undefined;
  onChange?: (value: FormValues['videoPosterInfo']) => void;
  disabled?: boolean;
  videoTitle?: string;
}) {
  const { value, onChange, videoTitle, disabled } = props;
  const posterType: Required<FormValues>['videoPosterInfo']['videoPosterType'] =
    value?.videoPosterType || 'first_frame';
  console.log('posterType ', posterType);
  const uploadInstanceRef = useRef<UploadRef>(null);
  const latestTitle = useLatest(videoTitle);
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [uplState, setUplState] = useState(false);
  const [downloadingPoster, setDownloadingPoster] = useState(false);
  useMount(() => {
    if (value?.videoPosterType === 'custom' && value?.videoCustomPosterUrl) {
      (async () => {
        setDownloadingPoster(true);
        // 初始化有图片地址，需要构造UploadFile 结构，要把图片文件转换成File
        const [err, resp] = await to(
          fetch(value.videoCustomPosterUrl as string)
        );
        // 下载失败就忽略
        if (err) return;
        // resp.body;
        const buffer = await resp?.arrayBuffer();
        if (buffer) {
          const raw = new File([buffer], `${latestTitle.current}封面`, {});
          const file: UploadFile = {
            raw,
            name: raw.name,
            size: raw.size,
            status: 'success',
            url: value.videoCustomPosterUrl,
          };
          setFiles([file]);
        }
      })().finally(() => {
        setDownloadingPoster(false);
      });
    }
  });

  let customUpload;
  if (posterType === 'custom') {
    customUpload = downloadingPoster ? (
      <div className="flex justify-center">
        <Loading />
      </div>
    ) : (
      <div className="flex flex-row gap-[10px]">
        <Upload
          theme="image"
          autoUpload
          draggable
          disabled={disabled}
          className={uplState ? '' : 'upload-custom-live'}
          accept=".jpg,.png"
          ref={uploadInstanceRef}
          requestMethod={async (files) => {
            setUplState(true);
            const [err, result] = await to(
              uploadRequest(
                MATERIAL_TYPE.IMAGE,
                (files as UploadFile).raw!,
                0,
                () => {
                  return;
                },
                {
                  onProgress: ({ percent }) => {
                    uploadInstanceRef?.current?.uploadFilePercent({
                      file: files,
                      percent: Math.round(percent * 1000),
                    });
                  },
                }
              )
            );
            if (err)
              return {
                status: 'fail',
                response: {},
              };
            return {
              status: result.code === 200 ? 'success' : 'fail',
              response: {
                url: result.url,
                cosKey: result.key,
              },
            };
          }}
          onSuccess={(context) => {
            if (context.response?.url) {
              let url = context.response?.url;
              if (!/^https/.test(url)) {
                url = `https:${url}`;
              }
              onChange?.({
                ...value,
                videoCustomPosterUrl: url,
              });
            }
          }}
          onRemove={() => {
            setUplState(false);
          }}
          onChange={(v) => {
            setFiles(v);
            setUplState(true);
          }}
          files={files}
          locale={{
            triggerUploadText: {
              normal: '添加图片',
            },
          }}
        />
        <div
          x-if={!uplState}
          className="flex flex-col justify-center gap-[2px]"
        >
          <span className="font-bold">选择封面图片进行上传</span>
          <span className="text-[12px] text-[#999999]">
            大小不超过xxx；格式为jpg、png
          </span>
        </div>
      </div>
    );
  }
  return (
    <div className="flex flex-col gap-y-[16px]">
      <Radio.Group
        className="default_radio_style w-full"
        value={posterType}
        variant="default-filled"
        options={[
          {
            label: '使用视频首帧',
            value: 'first_frame',
          },
          {
            label: '上传封面图片',
            value: 'custom',
          },
        ]}
        onChange={async (val) => {
          onChange?.({
            ...value,
            videoPosterType:
              val as Required<FormValues>['videoPosterInfo']['videoPosterType'],
            ...(val !== 'custom'
              ? {
                  videoCustomPosterUrl: '',
                }
              : undefined),
          });
        }}
      />
      {posterType === 'first_frame' && (
        <span
          className="font-normal"
          style={{
            color: 'rgba(0, 0, 0, 0.4)',
            fontSize: '12px',
          }}
        >
          将自动截取视频的首帧画面作为视频封面，可前往管理列表查看效果
        </span>
      )}
      {customUpload}
    </div>
  );
}

export function LiveConfigSaveDialog(props: ILiveConfigSaveDialogProps) {
  const {
    visible,
    handleClose,
    contentType,
    contentName,
    onGoToList,
    onGotoIndex,
    onSubmit,
    loading,
    onTriggerMakeVideo,
    defaultVideoPosterInfo,
  } = props;
  const [form] = Form.useForm();
  const [status, setStatus] = useState<'init' | 'done'>('init');
  const [selectedContentType, setSelectedContentType] =
    useState<FormValues['contentType']>();

  const [disabledForm, setDisabledForm] = useState(false);

  const rules: FormRules<FormValues> = {
    contentType: [{ required: true, message: '请选择发布类型', type: 'error' }],
    contentName: [
      { required: true, message: '请输入视频/直播间名称', type: 'error' },
    ],
    videoPosterInfo: [
      {
        // required: true,
        validator: (val: Required<FormValues>['videoPosterInfo']) => {
          if (val.videoPosterType === 'custom' && !val.videoCustomPosterUrl) {
            return {
              result: false,
              message: '请上传封面',
              type: 'error',
            };
          }
          return {
            result: true,
            message: '',
            type: 'success',
          };
        },
        // validator: (val) => {
        //   const formData = form.getFieldsValue(true);
        //   form.setValidateMessage({
        //     videoPosterType: [
        //       {
        //         type: 'error',
        //         message: '必须传一张封面图',
        //       },
        //     ],
        //   });
        //   return {
        //     result: false,
        //     type: 'error',
        //     message: '必须上传图片',
        //   };
        // },
        trigger: 'submit',
      },
    ],
  };

  const handleSubmit = useCallback(async () => {
    const validate = await form.validate();
    if (validate !== true) {
      return;
    }
    const values = form.getFieldsValue(true) as FormValues;
    setSelectedContentType(values.contentType);
    setDisabledForm(true);
    const [err] = await to(onSubmit(values));
    if (!err) {
      setStatus('done');
    }
    setDisabledForm(false);
  }, [form, onSubmit]);

  useEffect(() => {
    if (!visible) {
      setStatus('init');
    }
  }, [visible]);

  return (
    <Dialog
      header={status === 'init' ? '保存' : null}
      visible={visible}
      confirmOnEnter
      footer={null}
      width={532}
      destroyOnClose
      onClose={handleClose}
      closeOnOverlayClick={false}
      placement="center"
    >
      <div className="pagedoo-meta-live-global">
        {status === 'init' ? (
          <div>
            <Form
              form={form}
              colon
              rules={rules}
              labelWidth="106px"
              labelAlign="left"
              disabled={disabledForm}
              onValuesChange={(values: Partial<FormValues>) => {
                console.log('表单values变化 ', values);
                if (values.videoPosterInfo) {
                  if (
                    values.videoPosterInfo.videoPosterType === 'custom' &&
                    values.videoPosterInfo.videoCustomPosterUrl
                  ) {
                    form.setValidateMessage({
                      videoPosterInfo: [
                        {
                          type: 'success' as unknown as any,
                          message: '',
                        },
                      ],
                    });
                  }
                }
              }}
            >
              <FormItem
                label="发布类型"
                name="contentType"
                style={{
                  visibility: 'hidden',
                  opacity: 0,
                  width: 0,
                  height: 0,
                  overflow: 'hidden',
                }}
                initialData={contentType}
              >
                <SlideSelector
                  // 有类型之后锁定类型
                  disabled={!!contentType}
                  options={[
                    {
                      name: '直播间',
                      value: CONTENT_TYPE_MAP.Live.value,
                    },
                    {
                      name: '短视频',
                      value: CONTENT_TYPE_MAP.Video.value,
                    },
                  ]}
                />
              </FormItem>
              <FormItem
                shouldUpdate={(prev, next) =>
                  prev.contentType !== next.contentType
                }
              >
                {/* extend*/}
                {({ getFieldValue }) => {
                  const contentType = getFieldValue(
                    'contentType'
                  ) as FormValues['contentType'];
                  console.log(contentType, 'contentType');
                  return contentType === CONTENT_TYPE_MAP.Live.value ? (
                    <div key="live">
                      <FormItem
                        label="直播间名称"
                        name="contentName"
                        initialData={contentName || ''}
                      >
                        <Input
                          style={{ width: '100%' }}
                          placeholder="请输入直播间名称"
                        />
                      </FormItem>
                    </div>
                  ) : (
                    <FormItem
                      key="video"
                      label="视频名称"
                      name="contentName"
                      initialData={contentName || ''}
                    >
                      <Input
                        style={{ width: '100%' }}
                        placeholder="请输入视频名称"
                      />
                    </FormItem>
                  );
                }}
              </FormItem>
              <FormItem
                shouldUpdate={(prev, next) =>
                  prev.contentType !== next.contentType ||
                  prev.videoPosterInfo !== next.videoPosterInfo
                }
                // name="videoPosterType"
                // label="视频封面"
                // rules={[
                //   {
                //     required: true,
                //   },
                // ]}
              >
                {({ getFieldValue }) => {
                  if (contentType !== CONTENT_TYPE_MAP.Video.value)
                    return <></>;
                  const contentName =
                    (getFieldValue('contentName') as string) || '视频';
                  return (
                    <FormItem
                      name="videoPosterInfo"
                      label="视频封面"
                      initialData={
                        defaultVideoPosterInfo ??
                        ({
                          videoCustomPosterUrl: '',
                          videoPosterType: 'first_frame',
                        } satisfies FormValues['videoPosterInfo'])
                      }
                      requiredMark
                    >
                      <VideoPosterField videoTitle={contentName} />
                    </FormItem>
                  );
                }}
              </FormItem>
            </Form>

            <div className="mt-28 flex justify-end relative">
              <Space>
                <Button
                  theme="default"
                  className="gradient-default"
                  onClick={handleClose}
                >
                  取消
                </Button>
                <Button
                  loading={loading}
                  onClick={handleSubmit}
                  className="gradient-primary"
                >
                  确定
                </Button>
              </Space>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center pt-12">
            <img
              style={{ height: '74px', width: '88px' }}
              src={ActionSuccess}
              alt=""
            />
            <div className="my-16 flex items-center flex-col gap-5">
              <h3>配置成功</h3>
              <span>
                您可以前往
                <a className="text-primary" onClick={onGoToList}>
                  {ContentConfigMap[selectedContentType!]?.title || ''}管理{' '}
                </a>
                进行{ContentConfigMap[selectedContentType!]?.action || '查看'}
              </span>
            </div>
            <div className="flex justify-between gap-3">
              {selectedContentType === CONTENT_TYPE_MAP.Video.value && (
                <Button
                  className="gradient-default"
                  theme="default"
                  onClick={onTriggerMakeVideo}
                >
                  制作视频
                </Button>
              )}

              <Button className="gradient-primary" onClick={onGotoIndex}>
                返回首页
              </Button>
            </div>
          </div>
        )}
      </div>
    </Dialog>
  );
}
