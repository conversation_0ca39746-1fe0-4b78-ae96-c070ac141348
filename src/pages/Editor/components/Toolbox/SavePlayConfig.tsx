// 用于保存时间轨协议
import { ToolboxContext } from '@/pages/Editor/components/Toolbox/context';
import uuid from '@tencent/midas-util/lib/uuid';
import { useMemoizedFn } from 'ahooks';
import { useContext } from 'react';
import { FileExportIcon } from 'tdesign-icons-react';
import { Button } from 'tdesign-react';

export function SavePlayConfig() {
  const { editorRef } = useContext(ToolboxContext);
  const getPlayConfig = () => {
    const config = window.__pagedoo_play.getPlayConfig();
    return JSON.stringify(config);
  };
  const getId = async () => {
    const pagedooLive: { id: string } = (await editorRef?.handler.getGlobalData(
      'pagedoo-live'
    )) as any;
    let id = '';
    if (pagedooLive) {
      id = pagedooLive.id || '';
    }
    if (!id) {
      id = uuid();
    }
    return id;
  };
  const handleExport = useMemoizedFn(async () => {
    const playConfigStr = getPlayConfig();
    const id = await getId();
    const downloadUrl = window.URL.createObjectURL(
      new Blob([playConfigStr], {
        type: 'text/plain',
      })
    );
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = `${id}.json`;
    a.click();
    window.URL.revokeObjectURL(downloadUrl);
  });

  return (
    <Button
      variant="outline"
      size="large"
      style={{ height: 120 }}
      theme="primary"
      onClick={handleExport}
    >
      <div className="flex flex-col items-center gap-2">
        <FileExportIcon />
        <div>导出配置</div>
      </div>
    </Button>
  );
}
