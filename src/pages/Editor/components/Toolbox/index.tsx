import { useEffect, useMemo, useState } from 'react';
import {
  PagedooEditorPolyfillProps,
  PagedooEditorPolyfillRef,
} from '@tencent/pagedoo-editor-type';
import { ArrowUpCircleIcon, ToolsIcon } from 'tdesign-icons-react';
import { Button, Dialog, Table } from 'tdesign-react';
import { Component } from '@tencent/pagedoo-library';
import { MaterialsAvatar, MaterialsBase } from '@/utils/play-component';
import { cloneDeep } from 'lodash';
import { isEqual } from 'lodash-es';
import { useEditorReady } from '@/pages/Editor/hooks/useEditorReady';
import { useControllableValue, useLatest } from 'ahooks';
import { SavePlayConfig } from './SavePlayConfig';
import {
  IToolboxContextValue,
  ToolboxContext,
} from '@/pages/Editor/components/Toolbox/context';

export function ToolboxTrigger(props: {
  /** 编辑器API */
  editorRef: PagedooEditorPolyfillRef;
}) {
  const { editorRef } = props;
  const { ready } = useEditorReady();
  const [showToolbox, setShowToolbox] = useState(false);
  const latestShowToolbox = useLatest(showToolbox);

  const memoContextValue = useMemo<IToolboxContextValue>(
    () => ({
      editorRef,
    }),
    [editorRef]
  );
  useEffect(() => {
    if (!ready) return;
    if (!editorRef) return;
    let cancelTimeout: NodeJS.Timeout;
    let count = 0;
    const tryOpenToolbox = () => {
      clearTimeout(cancelTimeout);
      if (count >= 5) {
        count = 0;
        // 展示toolbox
        setShowToolbox(true);
        return;
      }
      count += 1;
      cancelTimeout = setTimeout(() => (count = 0), 2000);
    };
    // 监听键盘事件，触发toolbox打开
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.shiftKey && e.altKey) {
        e.stopPropagation();
        if (!latestShowToolbox.current) tryOpenToolbox();
        return;
      }
    };
    document.addEventListener('keydown', handleKeydown, true);
    return () => {
      document.removeEventListener('keydown', handleKeydown, true);
      clearTimeout(cancelTimeout);
    };
  }, [editorRef, latestShowToolbox, ready]);

  return (
    <ToolboxContext.Provider value={memoContextValue}>
      <Toolbox visible={showToolbox} setVisible={setShowToolbox} />
    </ToolboxContext.Provider>
  );
}

export function Toolbox(props: {
  visible?: boolean;
  setVisible?: (s: boolean) => void;
}) {
  const [visible, setVisible] = useControllableValue(props, {
    defaultValue: false,
    trigger: 'setVisible',
    valuePropName: 'visible',
  });
  // const

  const { dom: updateVersionDom, open: openUpdateVersion } = useUpdateVersion();
  return (
    <>
      {updateVersionDom}
      {/* <Button
        icon={<ToolsIcon />}
        onClick={() => setVisible(true)}
        variant="outline"
      >
        工具箱
      </Button> */}
      <Dialog
        visible={visible}
        closeBtn
        confirmBtn={null}
        cancelBtn={null}
        closeOnEscKeydown
        closeOnOverlayClick
        header="工具箱"
        mode="modal"
        onClose={() => setVisible(false)}
        // placement="top"
        preventScrollThrough
        showOverlay
        theme="default"
      >
        <div style={{ display: 'flex', paddingTop: 20 }}>
          {/* <Button
            variant="outline"
            size="large"
            style={{ height: 120 }}
            theme="primary"
            onClick={() => {
              setVisible(false);
              openUpdateVersion();
            }}
          >
            <div>
              <ArrowUpCircleIcon />
              <div>更新组件版本</div>
            </div>
          </Button> */}
          <SavePlayConfig />
        </div>
      </Dialog>
    </>
  );
}
const mapChildren = (
  components: Component[] | undefined,
  handle: (comp: Component) => void
) => {
  if (!components) return;
  for (const component of components) {
    handle(component);
    mapChildren(component.children, handle);
  }
};
type VersionItem = {
  // 当前素材库id xxx-xxx@xxx
  id: string;
  // 当前名称
  name: string;
  // 当前版本号
  version: string;
  // 已使用
  used: Component[];
  // 当前最新版本
  latest: string;
  // 触发更新
  update: () => void;
};
const updateVersion = async (list: VersionItem[]) => {
  const raw = window.__pagedoo_play.getPlayConfig();
  // // 更新content
  const content = cloneDeep(raw);
  for (const pagedooPlayTimeline of content.timeline)
    for (const playNode of pagedooPlayTimeline.node)
      mapChildren([playNode.component as Component], (component) => {
        const arr = component.id.split('/');
        const findLatest = list.find((item) => item.id === arr[1]);
        if (findLatest?.latest) {
          arr[1] = `${findLatest.name}@${findLatest.latest}`;
          component.id = arr.join('/');
        }
      });
  if (isEqual(raw, content)) return;
  window.__pagedoo_play.setPlayConfig(content);
};

// 更新素材库版本
const useUpdateVersion = () => {
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState<VersionItem[]>([]);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!visible) return;
    const list: VersionItem[] = [];
    // const content = window.__page_editor.refHandle().getEditorContentSync();
    const handleComponent = (component: Component) => {
      const materials = component.id.split('/')[1];
      const item = list.find((item) => item.id === materials);
      if (item) {
        item.used.push(component);
        return;
      }
      const info = {
        id: materials,
        name: materials.split('@')[0],
        version: materials.split('@')[1],
        latest: '',
        used: [component],
        async update() {
          await updateVersion([info]);
          setCount((count) => count + 1);
        },
      };
      list.push(info);
    };
    const content = cloneDeep(window.__pagedoo_play.getPlayConfig());
    for (const pagedooPlayTimeline of content.timeline)
      for (const playNode of pagedooPlayTimeline.node)
        mapChildren([playNode.component as Component], handleComponent);
    // for (const page of content.page_list)handleComponent(page.data.components);
    for (const item of list) {
      const name = item.id.split('@')[0];
      const latestVersion = [MaterialsAvatar, MaterialsBase].find((item) =>
        item.startsWith(`${name}@`)
      );
      if (latestVersion) item.latest = latestVersion.split('@')[1];
    }
    setList(list);
  }, [visible, count]);
  return {
    open: () => setVisible(true),
    dom: (
      <Dialog
        header="更新组件版本"
        visible={visible}
        confirmBtn="全部更新并关闭"
        onConfirm={async () => {
          await updateVersion(list);
          setVisible(false);
        }}
        onClose={() => setVisible(false)}
        closeOnEscKeydown
        closeOnOverlayClick
        width={1000}
      >
        <Table
          rowKey="id"
          data={list}
          columns={[
            {
              colKey: 'name',
              title: '素材库名',
            },
            {
              colKey: 'version',
              title: '当前版本',
              width: 120,
              render({ row, rowIndex }) {
                if (rowIndex === -1) return '当前版本';
                if (row.latest === row.version) return row.version;
                return <span style={{ color: 'green' }}>{row.version}</span>;
              },
            },
            {
              colKey: 'latest',
              title: '最新版本',
              width: 120,
              render({ row, rowIndex }) {
                if (rowIndex === -1) return '最新版本';
                if (row.latest === row.version) return row.latest;
                return <span style={{ color: 'green' }}>{row.latest}</span>;
              },
            },
            {
              colKey: 'usedCount',
              title: '使用次数',
              width: 120,
              render({ row, rowIndex }) {
                if (rowIndex === -1) return '使用次数';
                return `${row.used?.length}次`;
              },
            },
            {
              colKey: 'operation',
              title: '操作',
              width: 180,
              render: ({ row, rowIndex }) => {
                if (rowIndex === -1) return '操作';
                if (row.version === row.latest)
                  return <Button disabled>已是最新</Button>;
                return (
                  <Button onClick={row.update} disabled={!row.latest}>
                    更新
                  </Button>
                );
              },
            },
          ]}
        />
      </Dialog>
    ),
  };
};
