@prefix: simple-editor;

.@{prefix} {
    &-root {
        svg {
            vertical-align: baseline;
        }
    }

}

#simple-editor-root {
    .pagedoo-canvas {
        height: 100%;
        margin-left: 20px;
        margin-right: 20px;
        .device-simulator-container{
            padding: 16px 20px;
            overflow-x: hidden!important;
            .device-simulator {
                    box-shadow: none;
                    
                    transform: scale(1) !important;
                    margin-top: 28px !important;

                    [style^="position: absolute; width: fit-content;"] {
                        display: none;
                    }
                }
        }
        .editor-preview{
            border-radius: 8px;
        }
        &>div {

            height: 100%;
        }
    }
    
    .right-panel{
        &>div:last-child{
            width: 520px;
        }
    }
        

    .divider-box {
        [class*="_library_container_"] {
            margin-right: 0 !important;
            flex: 1;
            .pop{
                flex: 1;
            }
        }
        [class*="_layer_container"] {
            margin-left: 0 !important;
            flex: 1;
        }

        [class*="_pane_container"] {
            width: 100% !important;
            ::-webkit-scrollbar {
                    display: none;
                }
        }

        [data-component-selector="pop_container-form"] {
            width: auto !important;
            ::-webkit-scrollbar {
                display: none;
                }
        }
    }

}