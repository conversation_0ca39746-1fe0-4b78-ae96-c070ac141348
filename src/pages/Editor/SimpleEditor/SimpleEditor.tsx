import {
  AutoScale,
  ComponentListFixedItem,
  ConfigRenderFn,
  handler,
  IPagedooEditorComponentLibraryProps,
  IPagedooEditorComponentListProps,
  ITypesDataProps,
  PagedooCoreRoot,
  PagedooEditorCanvas,
  PagedooEditorComponentLibrary,
  PagedooEditorComponentList,
  PagedooEditorFormConfigPanel,
  PagedooEditorHeader,
  PagedooEditorPilot,
  PagedooEditorPolyfillProps,
  PagedooEditorPolyfillRef,
  useComponentOperator,
} from '@tencent/pagedoo-editor';
import React, {
  FC,
  forwardRef,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
// import { PagedooCoreRoot } from '@tencent/pagedoo-editor-core/es/index';
import { ISimpleEditorConfig } from '@/pages/Editor/common/type';
import {
  IMetaLiveExtendFormRef,
  MetaLiveExtendForm,
} from '@/pages/Editor/components/LiveSaveButton/MetaLiveExtendForm';
import { ToolboxTrigger } from '@/pages/Editor/components/Toolbox';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { TimeAxis } from '@/pages/Editor/MetaHumanLive/TimeAxis';
import { MetaEditorExtendData } from '@/pages/Editor/recoil/store';
import { MaterialsAvatar } from '@/utils/play-component';
import { getParams } from '@/utils/url';
import { css } from '@emotion/react';
import '@tencent/pagedoo-editor/dist/style.css';
import { DividerBox } from 'rc-dock';
import 'rc-dock/dist/rc-dock.css';
import { useSearchParams } from 'react-router-dom';
import { useRecoilState } from 'recoil';
import { getRecoil } from 'recoil-nexus';
import { Button, MessagePlugin } from 'tdesign-react';
import './simple-editor.less';
import TemplateModal from './TemplateModal/TemplateModal';
import { findComponent } from './util';

export type childrenRenderType = {
  headerLeftRender: FC;
  libraryConfigRender: ConfigRenderFn | undefined;
};
export interface SimpleEditorProps {
  ref: React.RefObject<PagedooEditorPolyfillRef>;
  client: (
    editorRef: React.RefObject<PagedooEditorPolyfillRef>
  ) => PagedooEditorPolyfillProps['client'];
  xNode: PagedooEditorPolyfillProps['xNode'];
  typesData: ITypesDataProps[];
  children?: childrenRenderType;
  customActionHandler?: IPagedooEditorComponentLibraryProps['customActionHandler'];
  itemRender?: IPagedooEditorComponentLibraryProps['itemRender'];
  onAfterAdd?: IPagedooEditorComponentLibraryProps['onAfterAdd'];
  onBeforeAdd?: IPagedooEditorComponentLibraryProps['onAfterAdd'];
  onReady?: (refHandle: PagedooEditorPolyfillRef) => Promise<void>;
  groupList?: IPagedooEditorComponentLibraryProps['groupList'];
  componentListProps?: IPagedooEditorComponentListProps;
  simpleEditorConfig?: ISimpleEditorConfig;
}

const SimpleEditor: React.ForwardRefRenderFunction<
  PagedooEditorPolyfillRef,
  SimpleEditorProps
> = function (
  {
    client,
    xNode,
    typesData,
    children,
    customActionHandler,
    onAfterAdd,
    itemRender,
    onBeforeAdd,
    onReady,
    groupList = [],
    componentListProps,
    simpleEditorConfig,
  },
  ref
) {
  const simpleEditorRef = useRef<PagedooEditorPolyfillRef | null>(null);
  const [canvasReady, setCanvasReady] = useState(false);
  const [showTimeAxis, setShowTimeAxis] = useState(false);
  const [resizeStart, setResizeStart] = useState(false);
  const metaEditorExtendData = getRecoil(MetaEditorExtendData);
  const sizeInfo = metaEditorExtendData?.sizeInfo;
  const [searchParams] = useSearchParams();
  const [editorKey, setEditorKey] = useState(0);
  const [enableToolboxTrigger, setEnableToolboxTrigger] = useState(false);
  let isHorizontal = searchParams.get('deviceSize') === '1';
  if (sizeInfo) {
    isHorizontal = sizeInfo.width > sizeInfo.height;
  }

  const reloadEditor = () => {
    setEditorKey((prev) => prev + 1);
  };
  (window as any).__reloadEditor = reloadEditor;

  const handleReady = useCallback(
    (refHandle: PagedooEditorPolyfillRef) => {
      (async () => {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        // await refHandle.handler.setGlobalData('__meta', {
        //   template: {
        //     id: 'test',
        //     limitedComponent: [
        //       {
        //         name: 'Virtualman',
        //         maxInstance: 1,
        //         disableRemove: true,
        //       },
        //       {
        //         name: 'LiveSpeech',
        //         disableRemove: true,
        //       },
        //     ],
        //   },
        // } as PagedooEditorGlobalData['__meta']);

        await onReady?.(refHandle);

        // refHandle.handler.addHook('setPageContent', (data) => {
        //   console.log('SimpleEditor 被设置页面协议', data);
        //   return true;
        // });
        setCanvasReady(true);
      })();
    },
    [onReady]
  );

  const memoClient = useMemo(() => {
    return client(simpleEditorRef);
  }, [client]);

  useEffect(() => {
    if (!canvasReady) return;
    const timeout = setTimeout(() => {
      setShowTimeAxis(true);
    }, 3000);
    return () => {
      clearTimeout(timeout);
    };
  }, [canvasReady]);

  const leftBarWidth = isHorizontal ? 570 : 750;

  const updateZoom = () => {
    const containerDom = document.querySelector(
      '.device-simulator-container'
    ) as HTMLDivElement;
    if (containerDom) {
      const simulatorDom = containerDom.querySelector(
        '.device-simulator'
      ) as HTMLDivElement;
      if (simulatorDom) {
        // 要考虑实际padding，和模拟器头部高度
        const zoom = Math.min(
          (containerDom.clientWidth - 40) / simulatorDom.clientWidth,
          (containerDom.clientHeight - 80) / simulatorDom.clientHeight
        );
        (
          simulatorDom.style as CSSStyleDeclaration & { zoom: string }
        ).zoom = `${zoom}`;
        return true;
      }
    }
    return false;
  };

  const isInit = useRef(true);
  const requestIdleId = useRef<number>();
  let timmer: undefined | number | NodeJS.Timeout;
  const resizeCount = useRef(0);
  const retryUpdateZoom = (time?: number) => {
    if (time) {
      resizeCount.current = time;
    }
    updateZoom();
    resizeCount.current = resizeCount.current - 1;
    if (resizeCount.current > 0) {
      clearTimeout(timmer);
      timmer = setTimeout(retryUpdateZoom, 500);
    }
  };

  useLayoutEffect(() => {
    const hasSet = updateZoom();
    if (hasSet && isInit.current) {
      isInit.current = false;
      // 这里调整到位需要一点时间
      retryUpdateZoom(10);
    } else {
      retryUpdateZoom(2);
    }
    if (window.__iframeWindow) {
      requestIdleId.current && cancelIdleCallback(requestIdleId.current);
      requestIdleId.current = requestIdleCallback(() => {
        const backgroundComponent =
          window.__iframeWindow.document.querySelector(
            '[data-module-id="base_background"]'
          ) as HTMLDivElement;
        if (backgroundComponent) {
          backgroundComponent.style.background =
            'linear-gradient(83deg, #F6F7FB -0.02%, #FBF8FB 99.98%)';
        }
        const emptyComponent = window.__iframeWindow.document.querySelector(
          'body>div[style^="position: fixed; z-index: 1;"]'
        ) as HTMLDivElement;
        if (emptyComponent) {
          emptyComponent.style.background =
            'linear-gradient(83deg, #F6F7FB -0.02%, #FBF8FB 99.98%)';
        }
      });
    }
    return () => {
      requestIdleId.current && cancelIdleCallback(requestIdleId.current);
      clearTimeout(timmer);
    };
  });
  const system = searchParams.get('system') as EditorSystemMap;
  const [divideDomHeights, setRootDomHigh] = useState<string[]>([]);
  const dividerBoxRef = useRef<DividerBox>(null);
  const handleTimeAxisVisibleChange = (visible: boolean) => {
    // console.log('visible', visible, divideDomHeights);
    const rootDom = document.querySelector(
      '#simple-editor-root'
    ) as HTMLDivElement;
    if (rootDom && dividerBoxRef.current) {
      const dividerStyle = dividerBoxRef.current._ref.style;
      if (visible) {
        rootDom.style.height = divideDomHeights[0];
        dividerStyle.height = divideDomHeights[1];
        dividerStyle.minHeight = divideDomHeights[2] || dividerStyle.minHeight;
        dividerStyle.maxHeight = divideDomHeights[3] || dividerStyle.maxHeight;
      } else {
        setRootDomHigh([
          rootDom.style.height,
          dividerStyle.height || '100%',
          dividerStyle.minHeight,
          dividerStyle.maxHeight,
        ]);
        rootDom.style.height = '100%';
        dividerStyle.height = '10px';
        dividerStyle.minHeight = '10px';
        dividerStyle.maxHeight = '10px';
      }
      retryUpdateZoom(2);
    }
  };

  return (
    <>
      {memoClient ? (
        <>
          <PagedooCoreRoot>
            <PagedooEditorPilot
              ref={(ref) => {
                simpleEditorRef.current = ref;
                setEnableToolboxTrigger(true);
              }}
              client={memoClient}
              xNode={xNode}
            >
              <AutoScale width={1920} height={1050}>
                <DividerBox
                  mode="vertical"
                  style={{
                    width: 'calc(100%)',
                    height: 'calc(100%)',
                  }}
                  onMouseDown={(ev) => {
                    const target = ev.target as HTMLElement;
                    if (target?.matches('.dock-divider')) {
                      setResizeStart(true);
                    }
                  }}
                  onMouseUp={() => {
                    setResizeStart(false);
                    retryUpdateZoom(4);
                  }}
                >
                  <div
                    id="simple-editor-root"
                    className="simple-editor-root"
                    css={css`
                      display: flex;
                      flex-direction: column;
                      height: 100%;
                      background: linear-gradient(
                        85.79deg,
                        #f2f5ff 23.6%,
                        #fdf8fa 142.16%
                      );
                      overflow: hidden;
                    `}
                  >
                    <div>
                      <PagedooEditorHeader
                        leftNode={
                          <TemplateModal
                            changeDisabled={system === 'meta_human'}
                            simpleEditorConfig={simpleEditorConfig}
                          />
                        }
                      />
                    </div>
                    <DividerBox
                      mode="horizontal"
                      id="simple-editor-main"
                      style={{
                        display: 'flex',
                        flex: 1,
                        height: '0',
                        position: 'relative',
                      }}
                    >
                      <DividerBox
                        mode="horizontal"
                        style={{
                          maxWidth: leftBarWidth,
                          minWidth: 400,
                          width: leftBarWidth,
                        }}
                      >
                        {simpleEditorConfig?.leftRender?.() || (
                          <PagedooEditorComponentLibrary
                            typesData={typesData}
                            customActionHandler={customActionHandler}
                            groupList={groupList}
                            defaultSelectedId={`component/${MaterialsAvatar}/LiveImage`}
                            globalComponents={['LiveSpeech', 'qa-config']}
                          >
                            {children?.libraryConfigRender
                              ? { configRender: children.libraryConfigRender }
                              : undefined}
                          </PagedooEditorComponentLibrary>
                        )}

                        {/* </div> */}
                      </DividerBox>
                      <div
                        className="pagedoo-canvas"
                        style={{ position: 'relative' }}
                      >
                        {resizeStart && (
                          <div
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              zIndex: 2,
                            }}
                          />
                        )}
                        <PagedooEditorCanvas onReady={handleReady} />
                      </div>

                      <div
                        className="right-panel"
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          maxWidth: 720,
                          minWidth: 420,
                          width: 720,
                        }}
                      >
                        {system === 'meta_human' && <PagedooMetaHumanConfig />}
                        {system === 'ad_live' && (
                          <>
                            <PagedooEditorComponentList
                              {...componentListProps}
                              defaultSelectedName="Virtualman"
                            />
                            <PagedooEditorFormConfigPanel />
                          </>
                        )}
                      </div>
                    </DividerBox>
                  </div>
                  <DividerBox
                    ref={dividerBoxRef}
                    mode="vertical"
                    style={{
                      height: 288,
                      maxHeight: 400,
                      minHeight: 229,
                      display: showTimeAxis ? 'block' : 'none',
                    }}
                  >
                    <div
                      id="meta-human-live-time-placeholder"
                      style={{ height: '100%' }}
                    />
                  </DividerBox>
                </DividerBox>
              </AutoScale>
            </PagedooEditorPilot>
            {showTimeAxis && (
              <TimeAxis onVisibleChange={handleTimeAxisVisibleChange} />
            )}
          </PagedooCoreRoot>
          <ToolboxTrigger
            x-if={enableToolboxTrigger}
            editorRef={simpleEditorRef.current!}
          />
        </>
      ) : (
        <></>
      )}
    </>
  );
};
function PagedooMetaHumanConfig() {
  const { addChildComponent, selectComponent } = useComponentOperator();

  return (
    <>
      <PagedooEditorComponentList
        defaultSelectedName="LiveSpeech"
        fixedListPostRender={({
          selectedComponent,
          setVirtualComponent,
          cancelSelectedComponent,
        }) => {
          return (
            <>
              <ComponentListFixedItem
                active={!!selectedComponent?.id.endsWith('/LiveSpeech')}
                data={{
                  icon: (
                    <svg
                      width="25"
                      height="25"
                      viewBox="0 0 25 25"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M17.1877 12.5C17.1877 15.0888 15.0891 17.1875 12.5002 17.1875C9.91139 17.1875 7.81273 15.0888 7.81273 12.5C7.81273 9.91117 9.91139 7.8125 12.5002 7.8125C15.0891 7.8125 17.1877 9.91117 17.1877 12.5ZM15.6252 12.5C15.6252 10.7741 14.2261 9.375 12.5002 9.375C10.7743 9.375 9.37523 10.7741 9.37523 12.5C9.37523 14.2259 10.7743 15.625 12.5002 15.625C14.2261 15.625 15.6252 14.2259 15.6252 12.5Z"
                        fill="black"
                        fillOpacity="0.9"
                      />
                      <path
                        d="M12.5002 1.95312L21.9724 7.22656V17.7734L12.5002 23.0469L3.02808 17.7734V7.22656L12.5002 1.95312ZM4.59058 8.145V16.855L12.5002 21.2585L20.4099 16.855V8.145L12.5002 3.74145L4.59058 8.145Z"
                        fill="black"
                        fillOpacity="0.9"
                      />
                    </svg>
                  ),
                  title: '话术配置',
                  id: selectedComponent?.id,
                }}
                onClick={async (data) => {
                  // 寻找话术组件
                  console.log(data, 'data');
                  const pageContent = await handler.getPageContent();

                  const component = findComponent(
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    pageContent.components,
                    (comp) => comp.id.endsWith('/LiveSpeech')
                  );
                  if (component) {
                    console.log(component, 'componentcomponent');
                    // await selectComponent(undefined);
                    // await sleep(0);
                    setTimeout(() => {
                      void selectComponent(component.key);
                    });
                    return;
                  }
                  await addChildComponent({
                    id: `component/${MaterialsAvatar}/LiveSpeech`,
                    commonStyle: undefined as any,
                    data: {
                      _v: 0,
                      speechData: {
                        type: 'text',
                        text: '',
                      },
                      speechDrive: {
                        type: 'virtualman',
                      },
                    },
                    componentName: '通用话术组件',
                  });
                  // setVirtualComponent({
                  //   commonStyle: {},
                  //   data: {},
                  //   id: data.id,
                  //   key: -9999,
                  //   name: data.id,
                  // });
                }}
              />
              <ComponentListFixedItem
                active={selectedComponent?.id === 'qa-config'}
                data={{
                  icon: (
                    <svg
                      width="25"
                      height="25"
                      viewBox="0 0 25 25"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M17.1877 12.5C17.1877 15.0888 15.0891 17.1875 12.5002 17.1875C9.91139 17.1875 7.81273 15.0888 7.81273 12.5C7.81273 9.91117 9.91139 7.8125 12.5002 7.8125C15.0891 7.8125 17.1877 9.91117 17.1877 12.5ZM15.6252 12.5C15.6252 10.7741 14.2261 9.375 12.5002 9.375C10.7743 9.375 9.37523 10.7741 9.37523 12.5C9.37523 14.2259 10.7743 15.625 12.5002 15.625C14.2261 15.625 15.6252 14.2259 15.6252 12.5Z"
                        fill="black"
                        fillOpacity="0.9"
                      />
                      <path
                        d="M12.5002 1.95312L21.9724 7.22656V17.7734L12.5002 23.0469L3.02808 17.7734V7.22656L12.5002 1.95312ZM4.59058 8.145V16.855L12.5002 21.2585L20.4099 16.855V8.145L12.5002 3.74145L4.59058 8.145Z"
                        fill="black"
                        fillOpacity="0.9"
                      />
                    </svg>
                  ),
                  title: '问答配置',
                  id: 'qa-config',
                }}
                onClick={(data) => {
                  setVirtualComponent({
                    commonStyle: {},
                    data: {},
                    id: data.id,
                    key: -9999,
                    name: data.id,
                  });
                }}
              />
            </>
          );
        }}
      />
      <PagedooEditorFormConfigPanel
        // eslint-disable-next-line react/no-unstable-nested-components
        virtualDatFormRender={(selectedComponent) => {
          if (selectedComponent.selectedComponent?.id === 'qa-config') {
            return <QAForm />;
          }

          return <div>Empty</div>;
        }}
      />
    </>
  );
}
function QAForm() {
  const [metaEditorExtendData, setMetaEditorExtendData] =
    useRecoilState(MetaEditorExtendData);
  const metaLiveExtendFormRef = useRef<IMetaLiveExtendFormRef>(null);
  const handleSubmit = async () => {
    const metaLiveExtendData = await metaLiveExtendFormRef.current?.submit();
    if (!metaLiveExtendData) {
      void MessagePlugin.warning('请补充直播信息');
      return;
    }
    console.debug(metaLiveExtendData, 'metaLiveData');
    const origin = getParams().origin || '';
    const contentType = getParams().contentType || '';
    setMetaEditorExtendData((i) => ({
      ...i,
      contentType: `${origin}_${contentType}`,
      metaLiveExtendData,
    }));
    void MessagePlugin.success('设置成功，保存发布后生效！');
  };
  return (
    <div style={{ padding: 16, height: '100%', overflowY: 'auto' }}>
      <div style={{ fontSize: 16, fontWeight: 600, lineHeight: '24px' }}>
        弹幕互动配置
      </div>
      <MetaLiveExtendForm
        ref={metaLiveExtendFormRef}
        initValue={metaEditorExtendData.metaLiveExtendData}
      />
      <Button onClick={handleSubmit} className="gradient-primary">
        确定
      </Button>
    </div>
  );
}

export default forwardRef(SimpleEditor);
