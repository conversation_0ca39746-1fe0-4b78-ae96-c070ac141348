import { css } from '@emotion/react';
import { useCallback, useEffect, useState } from 'react';
import { useRecoilValue } from 'recoil';
import { Dialog, Input } from 'tdesign-react';
import { MetaEditorExtendData, SaveLiveFunction } from '../../recoil/store';
import { useCategory, usePageStateChange } from './useCategory';
import Popconfirm from 'tdesign-react/es/popconfirm/Popconfirm';
import Modal from './Modal';
import { EditorSystemMap } from '../../editorConfig';
import { useSearchParams } from 'react-router-dom';
import { ISimpleEditorConfig } from '@/pages/Editor/common/type';

// interface ModalProps {}
type templateType = {
  templateId: string;
  title: string;
  src: string;
  desc: string;
};

export interface ITemplateModalProps {
  changeDisabled?: boolean;
  simpleEditorConfig?: ISimpleEditorConfig;
}
const TemplateModal = function (props: ITemplateModalProps) {
  const [visible, setVisible] = useState(false);
  // const { queryTemplateList, templateList, setTemplateList, moreList } =
  //   useTemplateList();
  const metaEditorExtendData = useRecoilValue(MetaEditorExtendData);
  const {
    firstLevel,
    secondLevel,
    firstOptions,
    secondOptions,
    setFirstLevel,
    setSecondLevel,
  } = useCategory();
  const { saveLive } = useRecoilValue(SaveLiveFunction);
  const savedMetaEditorExtendData = useRecoilValue(MetaEditorExtendData);
  const [title, setTitle] = useState<string>('');
  const pageState = usePageStateChange();
  const [searchParams] = useSearchParams();
  const system = searchParams.get('system') as EditorSystemMap;

  const autoChangeTitle = useCallback(async () => {
    setTitle(metaEditorExtendData.contentName);
  }, [metaEditorExtendData]);
  useEffect(() => {
    autoChangeTitle();
  }, [autoChangeTitle]);

  useEffect(() => {
    if (savedMetaEditorExtendData) {
      setTitle(savedMetaEditorExtendData.contentName || '');
    }
  }, [savedMetaEditorExtendData]);

  return (
    <div
      css={css`
        display: flex;
        align-items: center;
      `}
    >
      {pageState ? (
        <Popconfirm
          showArrow
          content={
            <>
              <p>提示</p>
              <Input
                placeholder={`请输入${
                  system === 'ad_live' ? '直播间' : ''
                }名称`}
                value={title}
                style={{ width: '320px' }}
                onChange={(value) => {
                  setTitle(value);
                }}
              />
              <p
                style={{
                  marginTop: '20px',
                  color: 'rgba(0,0,0,0.6)',
                  fontSize: '12px',
                  lineHeight: '20px',
                }}
              >
                当前页面有修改，但是您尚未保存，是否先保存再离开？
              </p>
            </>
          }
          cancelBtn={
            <button
              onClick={() => {
                window.close();
              }}
              css={css`
                background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
                width: 84px;
                height: 24px;
                border-radius: 4px;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.9);
              `}
            >
              直接离开
            </button>
          }
          confirmBtn={
            <button
              onClick={() => {
                setVisible(false);
                saveLive(title).then();
              }}
              css={css`
                width: 84px;
                height: 24px;
                border-radius: 4px;
                color: #ffffff;
                font-size: 12px;
                background: linear-gradient(
                  88.08deg,
                  #0153ff -0.01%,
                  #2e7ffd 49.89%,
                  #c1a3fd 99.99%
                );
              `}
            >
              保存并离开
            </button>
          }
        >
          <svg
            style={{ margin: 2, cursor: 'pointer' }}
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M4.91121 8.51281C4.92234 8.52398 4.93374 8.53471 4.94539 8.54499L10.1691 13.788C10.4507 14.0707 10.9072 14.0707 11.1888 13.788C11.4704 13.5054 11.4704 13.0472 11.1888 12.7645L6.44185 8L11.1888 3.23546C11.4704 2.95283 11.4704 2.4946 11.1888 2.21197C10.9072 1.92934 10.4507 1.92934 10.1691 2.21197L4.94539 7.45501C4.93374 7.46529 4.92234 7.47602 4.91121 7.48719C4.77042 7.6285 4.70002 7.81372 4.70002 7.99893L4.70002 8.00107C4.70002 8.18628 4.77042 8.3715 4.91121 8.51281Z"
              fill="black"
              fillOpacity="0.9"
            />
          </svg>
        </Popconfirm>
      ) : (
        <svg
          onClick={() => {
            window.close();
          }}
          style={{ margin: 2, cursor: 'pointer' }}
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M4.91121 8.51281C4.92234 8.52398 4.93374 8.53471 4.94539 8.54499L10.1691 13.788C10.4507 14.0707 10.9072 14.0707 11.1888 13.788C11.4704 13.5054 11.4704 13.0472 11.1888 12.7645L6.44185 8L11.1888 3.23546C11.4704 2.95283 11.4704 2.4946 11.1888 2.21197C10.9072 1.92934 10.4507 1.92934 10.1691 2.21197L4.94539 7.45501C4.93374 7.46529 4.92234 7.47602 4.91121 7.48719C4.77042 7.6285 4.70002 7.81372 4.70002 7.99893L4.70002 8.00107C4.70002 8.18628 4.77042 8.3715 4.91121 8.51281Z"
            fill="black"
            fillOpacity="0.9"
          />
        </svg>
      )}
      <span
        style={{
          margin: '8px',
          fontSize: 20,
          fontWeight: 500,
          color: 'rgba(0,0,0,0.9)',
        }}
      >
        {title ? title : '无模版'}
      </span>
      {!props.changeDisabled && (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          onClick={() => {
            setVisible(true);
          }}
          style={{ cursor: 'pointer' }}
        >
          <path
            d="M1.25024 6.00005L13.0082 6.00005L9.16404 2.25121L9.86221 1.53528L14.5334 6.09061C14.8738 6.4225 14.6388 7.00005 14.1634 7.00005L1.25024 7.00005V6.00005Z"
            fill={!visible ? 'black' : '#0047F9'}
            fillOpacity="0.9"
          />
          <path
            d="M14.75 10.0001L3.02026 10.0001L6.83053 13.6005L6.14372 14.3273L1.47454 9.91528C1.1261 9.58601 1.35906 9.00005 1.83855 9.00005L14.75 9.00005V10.0001Z"
            fill={!visible ? 'black' : '#0047F9'}
            fillOpacity="0.9"
          />
        </svg>
      )}
      <Dialog
        attach="#simple-editor-main"
        showInAttachedElement
        style={{
          width: '631px',
          height: '875px',
          padding: '18px',
          position: 'absolute',
          left: '20px',
          top: '20px',
        }}
        closeBtn
        visible={visible && system === 'ad_live'}
        header={
          <span style={{ color: '#000000E5', fontSize: '16px' }}>
            {system === 'ad_live' ? '直播间模版' : '模版'}
          </span>
        }
        footer={null}
        onClose={() => setVisible(false)}
      >
        {firstLevel && secondLevel && (
          <Modal
            firstLevel={firstLevel}
            secondLevel={secondLevel}
            firstOptions={firstOptions}
            secondOptions={secondOptions}
            changeFirstLevel={(value) => setFirstLevel(value)}
            changeSecondLevel={(value) => setSecondLevel(value)}
            changeTitle={(value) => setTitle(value)}
            changeVisible={() => {
              setVisible(false);
            }}
            onTemplateChanged={(templateId) =>
              props.simpleEditorConfig?.onTemplateChanged?.(templateId)
            }
          />
        )}
      </Dialog>
    </div>
  );
};

export default TemplateModal;
