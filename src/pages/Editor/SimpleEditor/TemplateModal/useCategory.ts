import { useCallback, useEffect, useRef, useState } from 'react';
import { IIFlattenSelectOption } from '../../../../components/FlattenSelect';
import { ResourceSvr } from '@/pb/pb';
import to from 'await-to-js';
import { Event, PageEditorProps } from '@tencent/pagedoo-editor';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
export type templateType = {
  templateId: string;
  title: string;
  src: string;
  desc: string;
};
export const pageSize = 16;
export const globalNameMap: Map<string, string> = new Map();
export const useTemplateList = () => {
  const [templateList, setTemplateList] = useState<templateType[]>();
  const [loading, setLoading] = useState<boolean>(false); // 是否在加载数据
  const moreList = useRef<boolean>(true);
  const queryTemplateList = useCallback(
    /**
     * 根据一二级分类查询模版列表
     * @param pageNum 模版页数
     * @param categoryLevel1 一级分类
     * @param categoryLevel2 二级分类
     */
    async (pageNum: number, categoryLevel1 = '', categoryLevel2 = '') => {
      setLoading(true);
      const [err, res] = await to(
        ResourceSvr.QueryResourceList({
          category_type: 'template',
          category_level1: categoryLevel1 === '' ? 'all' : categoryLevel1,
          category_level2: categoryLevel2 === '' ? 'all' : categoryLevel2,
          user_id: '',
          app_code: MatchedGlobalConfigItem.appcode,
          // app_code:
          //   import.meta.env.VITE_ENV === 'dev'
          //     ? 'pagedoo'
          //     : MatchedGlobalConfigItem.appcode,
          // app_code: 'pagedoo',
          page_size: pageSize,
          page_num: pageNum,
        })
      );
      setLoading(false);
      if (err || !res) {
        setTemplateList([]);
        console.error('获取模版列表失败', err);
      }
      if (res!.query_count) {
        moreList.current = true;
      } else {
        moreList.current = false;
      }
      const temps: templateType[] = res!.resource_info_list.map((resource) => ({
        templateId: resource.resource_id,
        title: resource.resource_name,
        src: resource.image_address,
        desc: resource.resource_description || '无描述',
      }));
      setTemplateList(temps);
    },
    []
  );
  return {
    queryTemplateList,
    templateList,
    setTemplateList,
    moreList,
    loading,
  };
};

export const useCategory = () => {
  const categoryRef = useRef<Map<string, string[]>>(); // 模版分类
  const [loading, setLoading] = useState<boolean>(false);
  const [firstLevel, setFirstLevel] = useState<string>(''); // 一级分类
  const [secondLevel, setSecondLevel] = useState<string>('all'); // 二级分类
  const [firstOptions, setFirstOptions] =
    useState<IIFlattenSelectOption<string>[]>();
  const [secondOptions, setSecondOptions] =
    useState<IIFlattenSelectOption<string>[]>();

  const queryTemplateType = useCallback(async () => {
    /**
     * 查询一二级分类目录,设置一级目录options
     */
    setLoading(true);
    const [err, res] = await to(
      ResourceSvr.GetCategoryInfoList({
        category_type: 'template',
      })
    );
    setLoading(false);
    if (err || !res) {
      throw new Error('获取模版分类失败', err);
    }
    const categoryMap = new Map<string, string[]>();
    categoryMap.set('all', []);
    globalNameMap.set('all', '全部');
    res.category_info_list.forEach((category) => {
      // 为全部一级目录添加二级
      if (!categoryMap.get('all')?.includes(category.category_level2)) {
        categoryMap.get('all')?.push(category.category_level2);
      }
      // 初始化一级目录
      if (!categoryMap.has(category.category_level1))
        categoryMap.set(category.category_level1, []);
      // 添加二级目录
      if (
        !categoryMap
          .get(category.category_level1)
          ?.includes(category.category_level2)
      ) {
        categoryMap
          .get(category.category_level1)
          ?.push(category.category_level2);
      }
      // 中英文映射
      !globalNameMap.has(category.category_level1) &&
        globalNameMap.set(
          category.category_level1,
          category.category_level1_name
        );
      !globalNameMap.has(category.category_level2) &&
        globalNameMap.set(
          category.category_level2,
          category.category_level2_name
        );
    });
    categoryRef.current = categoryMap;
    // 设置第一类型options
    const options = Array.from(categoryMap.keys()).map((firstLevel) => ({
      value: firstLevel as string,
      label: globalNameMap.get(firstLevel) as string,
      key: firstLevel as string,
    }));
    setFirstOptions(options);
    setFirstLevel('all');
  }, []);

  useEffect(() => {
    if (firstLevel && categoryRef.current && globalNameMap) {
      const options: IIFlattenSelectOption[] = categoryRef.current
        .get(firstLevel)!
        .map((secondLevel) => ({
          value: secondLevel as string,
          label: globalNameMap!.get(secondLevel) as string,
          key: secondLevel as string,
          style: {
            width: `${(globalNameMap!.get(secondLevel)?.length || 2) * 24}px`,
          },
        }));
      options.unshift({ value: 'all', label: '全部', key: 'all' });
      setSecondOptions(options);
      setSecondLevel('all');
    }
  }, [firstLevel]);

  useEffect(() => {
    queryTemplateType();
  }, [queryTemplateType]);
  return {
    firstLevel,
    secondLevel,
    firstOptions,
    secondOptions,
    setFirstLevel,
    setSecondLevel,
    loading,
  };
};

export const usePageStateChange = () => {
  const [pageState, setPageState] = useState<boolean>(true);

  /**
   * 等待页面加载完成后(判断是否加载完成逻辑是  设置定时器判断两秒内是否有改动)，监听页面是否有改动
   */
  useEffect(() => {
    let timer: NodeJS.Timeout;
    function setPageChangeState() {
      setPageState(true);
      console.log('__改变了');
      Event.off('onChange', setPageChangeState);
    }
    function storePageChange() {
      clearTimeout(timer);
      timer = setTimeout(() => {
        Event.off('onChange', storePageChange);
        Event.on('onChange', setPageChangeState);
        setPageState(false);
      }, 2000);
    }
    Event.on('onChange', storePageChange);
    return () => Event.off('onChange', storePageChange);
  }, []);
  return pageState;
};
