import { css } from '@emotion/react';
import { FunctionComponent, useCallback, useState, WheelEvent } from 'react';
import { <PERSON>ton, Dialog, Divider, Image, Loading } from 'tdesign-react';
import {
  FlattenSelect,
  IIFlattenSelectOption,
} from '../../../../components/FlattenSelect';

import Popconfirm from 'tdesign-react/es/popconfirm/Popconfirm';
import { Empty } from '@/components/Empty';
import { pageSize, useTemplateList } from './useCategory';
import { cloneDeep } from 'lodash-es';
import { useRecoilValue } from 'recoil';
import { PlayConfig, Script } from '@/type/pagedoo';
import { playService } from '@/utils/play-template';
import { useDeepCompareEffect } from 'ahooks';
import { InitialScriptData } from '../../recoil/store';
import { sleep } from '@/utils/sleep';
// interface ModalProps {}
type templateType = {
  templateId: string;
  title: string;
  src: string;
  desc: string;
};
interface ModalProps {
  firstOptions: IIFlattenSelectOption<string>[] | undefined;
  secondOptions: IIFlattenSelectOption<string>[] | undefined;
  firstLevel: string;
  secondLevel: string;
  changeFirstLevel(value: string): void;
  changeSecondLevel(value: string): void;
  changeTitle(value: string): void;
  changeVisible(): void;

  onTemplateChanged?: (templateId: string) => void;
}
const content = (
  <>
    <p
      css={css`
        font-size: 14px;
        font-weight: 600;
      `}
    >
      提示
    </p>
    <p
      css={css`
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
      `}
    >
      当前直播间已有配置好的组件，更换模版将完全覆盖您已配置的内容，是否继续更换
    </p>
  </>
);
const Modal: FunctionComponent<ModalProps> = function ({
  firstOptions,
  secondOptions,
  firstLevel,
  changeFirstLevel,
  secondLevel,
  changeSecondLevel,
  changeVisible,
  changeTitle,
  onTemplateChanged,
}) {
  const [selectTemplate, setSelectTemplate] = useState<templateType>();
  const InitialScript = useRecoilValue<Script>(InitialScriptData);
  const editorHandler = window.handler;
  const { queryTemplateList, templateList, setTemplateList, moreList } =
    useTemplateList();
  const setPlayConfig = useCallback(
    /**
     * 修改时间轨协议
     * @param playScript 模版对应的时间轨
     */
    async (playScript: PlayConfig) => {
      const cloneScript = cloneDeep(playScript);
      editorHandler.setGlobalData('pagedoo-play-script', cloneScript);
      const update = () => {
        console.log('setPlayConfig------->', cloneScript);
        window.__pagedoo_play?.setPlayConfig?.(cloneDeep(cloneScript));
      };
      update();
    },
    [editorHandler]
  );
  const getPlayConfig = (): PlayConfig => {
    const content = cloneDeep(window.__pagedoo_play.getPlayConfig());
    return content;
  };
  const onTemplateClick = useCallback(
    /**
     * 点击模版修改时间轨协议
     * @param templateId 模版id
     */
    async (templateId: string) => {
      const script = { ...InitialScript };
      if (script.adExtendData) {
        script.adExtendData = { ...script.adExtendData, templateId };
      }
      playService.getPlayScript(script).then(async (playScript) => {
        const current = getPlayConfig();
        current.timeline = [];
        setPlayConfig(current);
        await sleep(1000);
        setPlayConfig(playScript);
        setTimeout(() => {
          onTemplateChanged?.(templateId);
        }, 3000);
      });
      // 将选中的模版放到首位
      if (templateList?.length) {
        const selectIndex = templateList.findIndex(
          (template) => template.templateId === templateId
        );
        const newTemplateList = [
          templateList[selectIndex],
          ...templateList.slice(0, selectIndex),
          ...templateList.slice(selectIndex + 1),
        ];
        setTemplateList(newTemplateList);
      }
      changeVisible();
    },
    [
      InitialScript,
      templateList,
      changeVisible,
      setPlayConfig,
      onTemplateChanged,
      setTemplateList,
    ]
  );
  /**
   * 滚动时动态加载模版列表
   * @param event 滚动事件event
   */
  const handleScroll = useCallback(
    (event: WheelEvent<HTMLDivElement>): void => {
      const { currentTarget } = event;
      const { scrollTop, offsetHeight, scrollHeight } = currentTarget;
      const scrollBottom = scrollHeight - scrollTop - offsetHeight;
      if (!(scrollBottom > 100) && templateList && moreList) {
        queryTemplateList(
          templateList.length / pageSize + 1,
          firstLevel,
          secondLevel
        );
      }
    },
    [firstLevel, secondLevel, queryTemplateList, templateList, moreList]
  );

  /**
   * 更改一二级目录时修改模版列表
   */
  useDeepCompareEffect(() => {
    queryTemplateList(1, firstLevel, secondLevel);
  }, [queryTemplateList, firstLevel, secondLevel]);

  return (
    <div
      css={css`
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        box-sizing: border-box;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      `}
    >
      <div
        css={css`
          margin: 10px 0 40px 0;
        `}
      >
        {firstOptions && (
          <FlattenSelect
            value={firstLevel}
            onChange={async (value) => changeFirstLevel(value as string)}
            options={firstOptions}
          />
        )}
        <Divider style={{ margin: '12px' }} />
        {secondOptions && (
          <FlattenSelect
            value={secondLevel}
            onChange={async (value) => changeSecondLevel(value as string)}
            options={secondOptions}
          />
        )}
      </div>
      <div
        className="grid grid-cols-4 gap-[16px]"
        onScroll={(event) => {
          handleScroll(event as any);
        }}
        style={{ overflowX: 'hidden', overflowY: 'scroll', height: '600px' }}
      >
        {templateList?.length ? (
          templateList.map((temp) => {
            return (
              <div
                css={css`
                  width: 130px;
                  height: 230px;
                  border-radius: 4px;
                  position: relative;
                  overflow: hidden;
                  &:hover {
                    .layer {
                      display: flex;
                    }
                  }
                `}
              >
                <Image
                  style={{ width: '100%', height: '230px' }}
                  src={temp?.src}
                  fit="cover"
                  loading={<Loading />}
                />
                <div
                  css={css`
                    width: 100%;
                    height: 65px;
                    position: absolute;
                    bottom: 0;
                    display: flex;
                    align-items: center;
                    padding: 5px;
                    flex-direction: column;
                    z-index: 8;
                    background: linear-gradient(
                      97.99deg,
                      #ebf4ff 12.3%,
                      #f8f8ff 99.99%
                    );
                  `}
                >
                  <span
                    style={{
                      fontSize: '14px',
                      color: '#000A29',
                      fontWeight: '400',
                    }}
                  >
                    {temp.title}
                  </span>
                  <span
                    css={css`
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 2; /* 显示两行文本 */
                      -webkit-box-orient: vertical; /* 垂直排列文本 */
                    `}
                    style={{
                      fontSize: '12px',
                      color: 'rgba(0,0,0,0.4)',
                      lineHeight: '14px',
                    }}
                  >
                    {temp.desc}
                  </span>
                </div>
                <div
                  className="layer"
                  css={css`
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    z-index: 10;
                    background-color: rgba(0, 0, 0, 0.6);
                    display: ${selectTemplate?.templateId === temp.templateId
                      ? 'flex'
                      : 'none'};
                    justify-content: center;
                    align-items: center;
                  `}
                >
                  <Popconfirm
                    theme="default"
                    content={content}
                    destroyOnClose
                    onVisibleChange={(visible) => {
                      if (!visible) {
                        setSelectTemplate(undefined);
                      }
                    }}
                    showArrow
                    confirmBtn={
                      <span>
                        <button
                          onClick={() => {
                            onTemplateClick(
                              selectTemplate?.templateId as string
                            );
                            setSelectTemplate(undefined);
                            changeTitle(selectTemplate?.title || '');
                          }}
                          css={css`
                            display: inline-block;
                            background: linear-gradient(
                              84.64deg,
                              #f4f6ff 0%,
                              #faf5fc 100%
                            );
                            cursor: pointer;
                            width: 72px;
                            height: 24px;
                            border-radius: 50px;
                            line-height: 24px;
                            fontsize: 12px;
                          `}
                        >
                          继续更换
                        </button>
                      </span>
                    }
                    cancelBtn={
                      <span>
                        <button
                          onClick={() => {
                            setSelectTemplate(undefined);
                          }}
                          css={css`
                            display: inline-block;
                            background: linear-gradient(
                              88.08deg,
                              #0153ff -0.01%,
                              #2e7ffd 49.89%,
                              #c1a3fd 99.99%
                            );
                            cursor: pointer;
                            width: 72px;
                            height: 24px;
                            border-radius: 50px;
                            line-height: 24px;
                            color: #ffffff;
                            fontsize: 12px;
                          `}
                        >
                          取消更换
                        </button>
                      </span>
                    }
                  >
                    <div
                      onClick={() => {
                        setSelectTemplate(temp);
                        editorHandler
                          .getGlobalData('pagedoo-play-script')
                          .then((res: { timeline: unknown[] }) => {
                            // 时间轨为空，空白页直接套模版，不用二次确认
                            if (!res.timeline.length) {
                              onTemplateClick(temp.templateId);
                            }
                          });
                      }}
                      css={css`
                        width: 91px;
                        height: 32px;
                        background: linear-gradient(
                          88.08deg,
                          #0153ff -0.01%,
                          #2e7ffd 49.89%,
                          #c1a3fd 99.99%
                        );
                        cursor: pointer;
                        text-align: center;
                        border-radius: 4px;
                      `}
                    >
                      <span
                        css={css`
                          line-height: 32px;
                          color: #ffffff;
                          font-size: 14px;
                        `}
                      >
                        使用此模版
                      </span>
                    </div>
                  </Popconfirm>
                </div>
              </div>
            );
          })
        ) : (
          <Empty />
        )}
      </div>
    </div>
  );
};

export default Modal;
