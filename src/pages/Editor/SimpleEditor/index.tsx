/**
 * <AUTHOR>
 * @date 2024/5/1 下午9:15
 * @desc index
 */

import { useMonitor } from '@/hooks/useMonitor';
import { LoginApiAtom } from '@/model/api';
import { LoginStateAtom } from '@/model/login';
import { EditorConfigMap, EditorSystemMap } from '@/pages/Editor/editorConfig';
import { xNode } from '@/pages/Editor/EditorXNode';
import { useEditorMaterialMatch } from '@/pages/Editor/hooks/useEditorMaterialMatch';
import { useObserveTitle } from '@/pages/Editor/hooks/useObserveTitle';
import { createPagedooApi } from '@/pages/Editor/pagedoo-api';
import { styleInject } from '@/utils/style';
import {
  Event,
  PageEditorRef,
  editorEvent,
  PagedooEditorGlobalData,
  PagedooEditorPolyfillProps,
  PagedooEditorPolyfillRef,
} from '@tencent/pagedoo-editor';

import { useBrowserSupport } from '@/pages/Editor/hooks/useBrowserSupport';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSearchParams } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import SimpleEditor, {
  childrenRenderType,
  SimpleEditorProps,
} from './SimpleEditor';
import { MaterialsAvatar } from '@/utils/play-component';
import cloneDeep from 'lodash-es/cloneDeep';
import { screenShot } from '../common/screenshot';
import { SimpleEditorConfigContext } from '@/pages/Editor/SimpleEditor/context';
function Empty() {
  return null;
}
export function EditorEntry() {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<PagedooEditorPolyfillRef>(null);
  const [searchParams] = useSearchParams();
  const loginApi = useRecoilValue(LoginApiAtom);
  const loginState = useRecoilValue(LoginStateAtom);
  const { monitor } = useMonitor();
  const { pagedooApiExtend } = useContext(SimpleEditorConfigContext) || {};
  console.log('pagedooApiExtend ', pagedooApiExtend);

  // const [, setEditorRefProvider] = useRecoilState(editorRefProvider);
  const [client, setClient] = useState<
    | ((
        editorRef: React.RefObject<PageEditorRef>
      ) => PagedooEditorPolyfillProps['client'])
    | null
  >(null);
  // const [typesData, setTypesData] = useState<ITypesDataProps[]>();
  const { support } = useBrowserSupport({
    onConfirm: useCallback(() => {
      window.close();
    }, []),
  });

  // 监听回写gemsApi导致的document title变化
  useObserveTitle();

  // console.log(editorRef?.current, 'editorRef?.current');
  // 处理素材库匹配提示
  useEditorMaterialMatch();

  // 处理窗口变化
  // const handleResize = useCallback(() => {
  //   if (!wrapperRef.current) return;
  //   // const clientHeight =
  //   //   document.documentElement.clientHeight || window.innerHeight;
  //   // (wrapperRef.current.style as any).zoom =
  //   //   Math.min(clientHeight, 1000) / 1000;
  //   wrapperRef.current.style.height = `100%`;
  // }, []);
  // useEffect(() => {
  //   if (!wrapperRef.current) return;
  //   handleResize();
  //   window.addEventListener('resize', handleResize);
  //   return () => {
  //     window.removeEventListener('resize', handleResize);
  //   };
  // }, [handleResize, wrapperRef]);

  useEffect(() => {
    if (!editorRef.current) return;
    const editorHandler: PageEditorRef = editorRef.current;
    if (editorHandler) {
      editorHandler.setFinishValid(true).then();
    }
  }, [editorRef]);

  // useEffect(() => {
  //   const timeId = setInterval(() => {
  //     if (editorRef.current) {
  //       setEditorRefProvider(editorRef.current);
  //       clearInterval(timeId);
  //     }
  //   }, 30);
  //   return () => clearInterval(timeId);
  // }, [editorRef, setEditorRefProvider]);
  // 编辑器类型匹配
  const system = searchParams.get('system') as EditorSystemMap;
  const editorConfig = EditorConfigMap[system];
  useEffect(() => {
    setClient(() => EditorConfigMap[system].client ?? null);
    // setTypesData(EditorConfigMap[system].typesData);
  }, [system]);
  useEffect(() => styleInject('.is-page_gmcaV{display:none;}'), []);

  // 注入pagedoo api 用于给素材库使用
  useEffect(() => {
    const destroy = createPagedooApi({
      loginApi,
      loginState,
      monitorInstance: monitor,
      extends: pagedooApiExtend,
    });
    const hanldeEditorChange = (data: { materialsStatus: 'success' }) => {
      if (data.materialsStatus === 'success') {
        document.body.querySelectorAll('iframe').forEach((el) => {
          // console.log('iframe', el, el.className, el.contentWindow);
          if (
            (el.parentNode as HTMLElement)?.className === 'editor-preview' &&
            el.contentWindow
          ) {
            Event.off('onPageChange', hanldeEditorChange);
            if (!('__pagedoo_api' in el.contentWindow)) {
              el.contentWindow.__pagedoo_api = window.__pagedoo_api;
              // 劫持网络请求到主页面
              (el.contentWindow as any).XMLHttpRequest = window.XMLHttpRequest;
              (el.contentWindow as any).fetch = window.fetch;
            }
          }
        });
      }
    };
    if (client) {
      Event.on('onEditorStateChange', hanldeEditorChange);
    }
    return () => {
      Event.off('onEditorStateChange', hanldeEditorChange);
      destroy();
    };
  }, [client, loginApi, loginState, monitor, pagedooApiExtend]);

  const memoChildren = useMemo<childrenRenderType>(
    () => ({
      // eslint-disable-next-line react/no-unstable-nested-components
      libraryConfigRender:
        editorConfig.simpleEditor.componentLibraryChildren?.configRender,
      headerLeftRender: Empty,
    }),
    [editorConfig.simpleEditor.componentLibraryChildren?.configRender]
  );

  const handleReady = useCallback<Required<SimpleEditorProps>['onReady']>(
    async (refHandle) => {
      const { simpleEditor } = editorConfig;
      if (Array.isArray(simpleEditor?.componentLimitationConfig)) {
        const meta = '__meta';
        const globalDataMeta: PagedooEditorGlobalData['__meta'] =
          (await refHandle.handler.getGlobalData(
            meta
          )) as PagedooEditorGlobalData['__meta'];
        const newMeta: Required<PagedooEditorGlobalData>['__meta'] =
          globalDataMeta ? cloneDeep(globalDataMeta) : {};
        newMeta.template = {
          ...newMeta.template,
          id: newMeta.template?.id || '',
          limitedComponent: simpleEditor.componentLimitationConfig || [],
        };
        refHandle.handler.setGlobalData(meta, newMeta);
      }
      editorEvent.on('onSelect', (data) => {
        console.log('编辑器选中 ', data);
      });
    },
    [editorConfig]
  );

  return useMemo(() => {
    return (
      support && (
        <div ref={wrapperRef} className="outer" style={{ height: '100%' }}>
          {client && editorConfig.simpleEditor ? (
            <SimpleEditor
              client={client}
              xNode={xNode}
              ref={editorRef}
              typesData={editorConfig.simpleEditor.candidateComponents}
              customActionHandler={
                editorConfig.simpleEditor.customActionHandler
              }
              onReady={handleReady}
              groupList={editorConfig.simpleEditor.groupList || []}
              componentListProps={
                editorConfig.simpleEditor.componentListProps || {}
              }
              simpleEditorConfig={editorConfig.simpleEditor}
            >
              {memoChildren}
            </SimpleEditor>
          ) : (
            <></>
          )}
        </div>
      )
    );
  }, [support, client, editorConfig.simpleEditor, handleReady, memoChildren]);
}

export default EditorEntry;
