// // 实现素材库的热更新，仅在本地开发模式下才会加载

// declare global {
//   interface Window {
//     hmr: () => Promise<void>;
//   }
// }
// const searchParams = new URLSearchParams(import.meta.url);
// // 管理台调试使用本地编辑器
// const PORT = searchParams.get('port') || '5678';
// const interval = parseInt(searchParams.get('interval') || '2000', 10);
// // PORT填写本地素材库的端口
// // whistle需要增加相应代理
// // https://0.0.0.0:5678 http://0.0.0.0:5678 resCors://enable
// window.hmr = async () => {
//   // 管理台重新加载编辑器素材库脚本
//   if (!((window as any).__gems_preview && (window as any).handler)) return;
//   const materials = [
//     ...new Set(
//       [...(window as any).__gems_preview.materialsMap.components.keys()].map(
//         (i) => i.split('/')[1]
//       )
//     ),
//   ];
//   console.log('准备重新加载素材库：', materials);
//   await (window as any).handler.initMaterials(
//     await Promise.all(
//       materials.map(async (i) => {
//         if (i.startsWith('gems-materials-pagedoo-base')) {
//           return {
//             meta: 'https://pagedoo-dev-1258344706.cos.ap-guangzhou.myqcloud.com/cdn/materials/pagedoo-base/manifest.json',
//             name: 'gems-materials-pagedoo-base',
//             alias: i,
//           };
//         }
//         const { meta, name, alias } = await (
//           window as any
//         ).__gems_preview.loaderMaterials(i);
//         return { meta, name, alias: '' };
//       })
//     )
//   );
// };
// (async () => {
//   let msg = '';
//   // 非编辑器界面不注入
//   if (!location.hash.startsWith('#/Editor')) return;
//   const oldHmr = window.hmr;
//   const run = async () => {
//     if (window.hmr !== oldHmr) return;
//     try {
//       const ping = await (
//         await fetch(`https://0.0.0.0:${PORT}/__ping_gems_dev?msg=${msg}`)
//       ).json();
//       if (window.hmr !== oldHmr) return;
//       console.log(ping, 'ping');
//       msg = ping.msg;
//       if (msg === 'reload') console.log('重新构建素材库');
//       else if (msg === 'ok') {
//         console.log('构建完成！开始更新素材库...');
//         await window.hmr();
//         console.log('素材库加载完成！');
//       }
//     } catch (e) {
//       msg = 'disconnect';
//     }
//     setTimeout(run, interval);
//   };
//   setTimeout(run, interval);
// })();

export {};
