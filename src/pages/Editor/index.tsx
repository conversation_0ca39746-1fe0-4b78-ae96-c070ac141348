/**
 * <AUTHOR>
 * @date 2024/5/1 下午9:15
 * @desc index
 */

import { useMonitor } from '@/hooks/useMonitor';
import { LoginApiAtom } from '@/model/api';
import { LoginStateAtom } from '@/model/login';
import { EditorConfigMap, EditorSystemMap } from '@/pages/Editor/editorConfig';
import { xNode } from '@/pages/Editor/EditorXNode';
import { useEditorMaterialMatch } from '@/pages/Editor/hooks/useEditorMaterialMatch';
import { useObserveTitle } from '@/pages/Editor/hooks/useObserveTitle';
import { createPagedooApi } from '@/pages/Editor/pagedoo-api';
import { styleInject } from '@/utils/style';
import {
  Event,
  EditorImpl,
  PageEditor,
  PageEditorRef,
} from '@tencent/pagedoo-editor';

import { useBrowserSupport } from '@/pages/Editor/hooks/useBrowserSupport';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSearchParams } from 'react-router-dom';
import { useRecoilValue } from 'recoil';

export function EditorEntry() {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<PageEditorRef>(null);
  const [searchParams] = useSearchParams();
  const loginApi = useRecoilValue(LoginApiAtom);
  const loginState = useRecoilValue(LoginStateAtom);
  const { monitor } = useMonitor();

  // const [, setEditorRefProvider] = useRecoilState(editorRefProvider);
  const [client, setClient] = useState<
    ((editorRef: React.RefObject<PageEditorRef>) => EditorImpl) | null
  >(null);

  const { support } = useBrowserSupport({
    onConfirm: useCallback(() => {
      window.close();
    }, []),
  });

  // 监听回写gemsApi导致的document title变化
  useObserveTitle();

  // console.log(editorRef?.current, 'editorRef?.current');
  // 处理素材库匹配提示
  useEditorMaterialMatch();

  // 处理窗口变化
  const handleResize = useCallback(() => {
    if (!wrapperRef.current) return;
    const clientHeight =
      document.documentElement.clientHeight || window.innerHeight;
    wrapperRef.current.style.height = `${clientHeight}px`;
  }, []);
  useEffect(() => {
    if (!wrapperRef.current) return;
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [handleResize, wrapperRef]);

  useEffect(() => {
    if (!editorRef.current) return;
    const editorHandler: PageEditorRef = editorRef.current;
    if (editorHandler) {
      editorHandler.setFinishValid(true).then();
    }
  }, [editorRef]);

  // useEffect(() => {
  //   const timeId = setInterval(() => {
  //     if (editorRef.current) {
  //       setEditorRefProvider(editorRef.current);
  //       clearInterval(timeId);
  //     }
  //   }, 30);
  //   return () => clearInterval(timeId);
  // }, [editorRef, setEditorRefProvider]);
  // 编辑器类型匹配
  const system = searchParams.get('system') as EditorSystemMap;
  useEffect(() => {
    setClient(() => EditorConfigMap[system].client ?? null);
  }, [system]);
  useEffect(() => styleInject('.is-page_gmcaV{display:none;}'), []);

  // 注入pagedoo api 用于给素材库使用
  useEffect(() => {
    const destroy = createPagedooApi({
      loginApi,
      loginState,
      monitorInstance: monitor,
    });
    const hanldeEditorChange = (data: { materialsStatus: 'success' }) => {
      if (data.materialsStatus === 'success') {
        document.body.querySelectorAll('iframe').forEach((el) => {
          if (
            (el.parentNode as HTMLElement)?.className === 'editor-preview' &&
            el.contentWindow
          ) {
            Event.off('onPageChange', hanldeEditorChange);
            el.contentWindow.__pagedoo_api = window.__pagedoo_api;
          }
        });
      }
    };
    if (client) {
      Event.on('onEditorStateChange', hanldeEditorChange);
    }
    return () => {
      Event.off('onEditorStateChange', hanldeEditorChange);
      destroy();
    };
  }, [client, loginApi, loginState, monitor]);

  return useMemo(() => {
    return (
      support && (
        <div ref={wrapperRef} className="outer">
          {client ? (
            <PageEditor
              client={client(editorRef)}
              xNode={xNode}
              ref={editorRef}
            />
          ) : (
            <></>
          )}
        </div>
      )
    );
  }, [client, support]);
}

export default EditorEntry;
