import { EditorInitMetadata } from './store';
import { selector } from 'recoil';

export interface IEditorInitMetadataSelectorData {
  orientation: 'vertical' | 'horizontal';
  isHorizontal: boolean;
  isVertical: boolean;
}
export const editorInitMetaInfoSelector =
  selector<IEditorInitMetadataSelectorData>({
    key: 'editorInitMetaInfoSelector',
    get: ({ get }) => {
      const info = get(EditorInitMetadata);
      const { size } = info;
      const isHorizon = size.width > size.height;
      const data: IEditorInitMetadataSelectorData = {
        orientation: isHorizon ? 'horizontal' : 'vertical',
        isHorizontal: isHorizon,
        isVertical: !isHorizon,
      };
      return data;
    },
  });
