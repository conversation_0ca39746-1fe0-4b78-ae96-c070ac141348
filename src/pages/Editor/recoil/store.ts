/**
 * <AUTHOR>
 * @date 2024/5/2 下午4:18
 * @desc store
 */
import { atom } from 'recoil';
import { MetaLiveExtendFormValue } from '@/pages/Editor/components/LiveSaveButton/MetaLiveExtendForm';
import { BaseScript } from '@/components/ScriptForm/type';

export const MetaEditorExtendData = atom<{
  // contentType不使用
  contentType: string;
  contentName: string;
  qaGroupId?: string;
  metaLiveExtendData?: MetaLiveExtendFormValue;
  // 视频制作封面信息，仅在contentType为video时使用
  videoPosterInfo?: {
    videoPosterType?: 'custom' | 'first_frame';
    videoCustomPosterUrl?: string;
  };
  // 编辑器画布尺寸信息
  sizeInfo?: {
    width: number;
    height: number;
  };
}>({
  key: 'MetaEditorExtendData',
  default: {
    contentType: '',
    contentName: '',
  },
});

export const SaveLiveFunction = atom<{
  saveLive(value: string): Promise<void>;
}>({
  key: 'SaveLiveFunction',
  default: {
    async saveLive(value: string) {
      console.log(value);
    },
  },
});

export const InitialScriptData = atom<BaseScript>({
  key: 'InitialScriptData',
  default: {} as BaseScript,
});

// 编辑器初始化元数据，用于存储一些尺寸信息，横竖屏等
export const EditorInitMetadata = atom<{
  size: {
    width: number;
    height: number;
  };
}>({
  key: 'EditorInitMetadata',
  default: {
    size: {
      width: 0,
      height: 0,
    },
  },
});
