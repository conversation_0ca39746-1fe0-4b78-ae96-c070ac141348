.meta-human-live-time-axis {
  //position: fixed;
  //width: calc(100% - 288px - 335px);
  //height: 21px;
  //bottom: -292px;
  //left: 288px;
  position: relative;
  z-index: 2;
  height: 0;
  transition: height 0.5s ease-in-out;

  // &--active {
  //   //设计稿高度288px
  //   height: 288px;
  // }

  //.title {
  //  height: 60px;
  //  line-height: 60px;
  //  display: flex;
  //  align-items: center;
  //  justify-content: center;
  //  background-color: #ffffff;
  //  text-align: center;
  //  cursor: pointer;
  //}

  .wrap {
    background-color: #ffffff;
    // height: 288px;

    .header {
      height: 57px;
      width: 100%;
      border-bottom: 1px solid #f5f0f0;
      background-color: #ffffff;
      position: relative;

      .icon {
        background-color: #f5f0f0;
        width: 42px;
        height: 20px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 0 10px 10px;
        cursor: pointer;
      }

      .splice-zone {
        position: absolute;
        display: flex;
        align-items: center;
        top: 14px;
        right: 12px;
        cursor: pointer;
      }

      .step {
        position: absolute;
        bottom: 0;
        left: 12px;
      }
    }

    .script-track {
      //padding: 20px;
      height: calc(100% - 37px);
      //background-color: indianred;
    }
  }
}
