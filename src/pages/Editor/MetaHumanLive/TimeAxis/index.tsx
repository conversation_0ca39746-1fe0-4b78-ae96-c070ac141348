/**
 * <AUTHOR>
 * @date 2024/3/19 22:05
 * @desc index
 */

import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import './index.less';
import { TimeNavigator } from '@tencent/pagedoo-time-navigator';
import {
  PagedooPlayTimeline,
  PlayNode,
  TimeNavigatorRef,
} from '@tencent/pagedoo-time-navigator/dist/es/type';
import {
  editorEvent,
  editorHandler,
  Event,
  getSelectedComponentInstance,
  handler,
} from '@tencent/pagedoo-editor';
import { SingleVersionPage } from '@tencent/gems-shared';
import { uuid } from '@tencent/midas-util';
import { cloneDeep } from 'lodash';
import { ErrorBoundary } from 'react-error-boundary';
import { useDebounceFn, useLatest, useThrottleFn } from 'ahooks';
import { isEqual } from 'lodash-es';
import { DrawerStatus } from '../../common/editorDrawer';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { useSearchParams } from 'react-router-dom';
import { sleep } from '@/utils/sleep';
import { PlayConfig } from '@/type/pagedoo';
import { useRecoilState } from 'recoil';
import { TimeAxisSharedAtom } from './store';
// import { AIDemo } from '@/pages/Editor/MetaHumanLive/TimeAxis/ai';
// import type { EventBus } from '@tencent/eventbus';
// import { getTShareAtom, setTValue } from '@tencent/tstate';
const findNodeByTimeKey = (value: PlayConfig, time: number, key: number) => {
  for (const line of value.timeline) {
    const result = line.node.find(
      (node) =>
        node.key === key &&
        time >= node.offset &&
        time < node.offset + node.duration
    );
    if (result) return result;
  }
};

const findNodeByTime = (value: PlayConfig, time: number) => {
  const result: {
    node: PlayNode;
    // 所属轨道
    lineIndex: number;
    // 所属轨道中节点索引
    nodeIndex: number;
  }[] = [];
  for (let i = 0, len = value.timeline.length; i < len; i++) {
    const line = value.timeline[i];
    let nodeIdx = -1;
    const n = line.node.find((node, i) => {
      if (time >= node.offset && time < node.offset + node.duration) {
        nodeIdx = i;
        return true;
      }
      return false;
    });

    if (n)
      result.push({
        node: n,
        lineIndex: i,
        nodeIndex: nodeIdx,
      });
  }
  return result;
};
const findNodeByID = (value: PlayConfig, id: string) => {
  for (const line of value.timeline) {
    const node = line.node.find((node) => node.id === id);
    if (node) return node;
  }
};

const getAllComponent = (script: PlayConfig): string[] => {
  const arr: string[] = [];
  for (const pagedooPlayTimeline of script.timeline) {
    for (const node of pagedooPlayTimeline.node) {
      if (!arr.includes(node.component.id)) {
        arr.push(node.component.id);
      }
    }
  }
  return arr;
};

const getGemsPreview = ():
  | {
      ensureComponent(id: string): Promise<void>;
      ensurePageContent(page: SingleVersionPage): Promise<void>;
    }
  | undefined => {
  const win = window as {
    __gems_preview?: unknown;
  };
  if (win.__gems_preview) return win.__gems_preview as never;
  try {
    const win = parent as {
      __gems_preview?: unknown;
    };
    if (win.__gems_preview) return win.__gems_preview as never;
  } catch {}
};

export interface TimeAxisProps extends Record<string, unknown> {
  onVisibleChange?: (visible: boolean) => void;
  // 新增组件时，默认时长
  defaultDuration?: number;
}

export function TimeAxis(props: TimeAxisProps) {
  const [visible, setVisible] = React.useState(false);
  const [searchParams] = useSearchParams();
  const system = searchParams.get('system') as EditorSystemMap;
  const [, setTimeAxisSharedAtom] = useRecoilState(TimeAxisSharedAtom);
  const { defaultDuration = 20000 } = props;
  // const system = 'meta_human';

  useEffect(() => {
    if (system === 'ad_live' && visible) setVisible(false);
  }, [visible, system]);
  useEffect(() => {
    // 广告不展示时间轴
    if (system === 'ad_live') return;
    const i = setInterval(() => {
      if (!document.querySelector<HTMLDivElement>('.device-simulator')) return;
      clearInterval(i);
      setVisible(true);
    }, 1000);
    return () => {
      clearInterval(i);
    };
  }, [system]);
  useEffect(() => {
    if (system === 'ad_live') return;
    if (!visible) return;
    handler.setZoom(60);
    // const simulator =
    //   document.querySelector<HTMLDivElement>('.device-simulator');
    // if (!simulator) return;
    // simulator.style.top = '-180px';

    return () => {
      // if (simulator) simulator.style.top = '';
      handler.setZoom(100);
    };
  }, [visible, system]);
  const trigger = () => {
    setVisible(!visible);
  };
  useEffect(() => {
    props.onVisibleChange?.(visible);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);
  const [state, setState] = useState<PlayConfig>();
  const latestState = useLatest(state);
  // const [schema, setSchema] = useState<SingleVersionPage>();
  const latestSchema = useRef<SingleVersionPage>();
  const loading = useRef(true);
  const dragPos = useRef<{ x: number; y: number; time: number }>(null);
  /** 同步编辑器变化到时间轴*/
  const { run: handleUserSchemaChangeRun } = useDebounceFn(
    async (page: SingleVersionPage) => {
      if (!state || loading.current) return;
      // schema更新 重新设置state
      const components = (page as SingleVersionPage).components?.[0]?.children;
      if (!components) return;
      const playConfig: PlayConfig = cloneDeep(state);
      const time = state.__config.anchorTime;
      let change = false;
      for (const component of components) {
        const node = findNodeByTimeKey(playConfig, time, component.key);
        // console.log('playConfig, time, component.key', playConfig, time, component.key, '===', node);
        if (!ref?.findNodeByKey(component.key)) {
          /** 插入的场景*/
          change = true;
          console.log(playConfig, '添加node', dragPos.current, component);
          // 需要添加node
          if (dragPos.current) {
            // && Date.now() - dragPos.current.time < 1E3
            ref.addComponent(dragPos.current.x, dragPos.current.y, {
              __config: { thumbnail: '', title: '', type: 'component' },
              actualDuration: 0,
              hidden: false,
              id: uuid(),
              key: component.key,
              offset: time,
              duration: defaultDuration,
              component: component as any,
            });
            dragPos.current = null;
            return;
          }
          playConfig.timeline.push({
            __config: { height: 40 },
            node: [
              {
                __config: { thumbnail: '', title: '', type: 'component' },
                actualDuration: 0,
                hidden: false,
                id: uuid(),
                key: component.key,
                offset: time,
                duration: defaultDuration,
                component: component as any,
              },
            ],
          });

          // // todo 最近时间轴添加 而不是新增时间轴
        } else if (node) {
          // 如果data不一样需要修改data
          if (!isEqual(node.component, component)) {
            change = true;
            node.component = component as any;
            // console.log('修改组件component', component);
          }
        }
      }
      const currentPlayNodes = findNodeByTime(playConfig, time);
      let isDelete = false;
      for (const playNode of currentPlayNodes) {
        const { node } = playNode;
        // 如果时间轴上有 但是components没有了需要删除
        if (!components.find((i) => i.key === node.key)) {
          change = true;
          console.log('删除node', playNode);
          if (!isDelete) {
            isDelete = true;
          }
          const line = playConfig.timeline.find((line) =>
            line.node.includes(node)
          );
          if (!line) continue;
          line.node = line.node.filter((i) => i !== node);
        }
      }
      // 按照组件顺序创建新的临时的轴
      // 现有组件时间轴顺序
      const componentKeys: number[] = components.map((c) => c.key);
      if (
        !isDelete &&
        !isEqual(
          componentKeys,
          currentPlayNodes.map((playNode) => playNode.node.key)
        ) &&
        // 组件没有新增或者删除
        componentKeys.length === currentPlayNodes.length
      ) {
        let tempLine: PagedooPlayTimeline[] = [];
        const coordinate: Record<number, { lineIdx: number }> = {};
        change = true;
        console.log(
          'handleUserSchemaChangeRun',
          '新组件顺序',
          componentKeys,
          '旧时间轴上的节点',
          currentPlayNodes
        );
        for (const component of components) {
          componentKeys.push(component.key);
          const node = currentPlayNodes.find((playNode) => {
            if (playNode.node.key === component.key) {
              coordinate[component.key] = {
                lineIdx: playNode.lineIndex,
              };
              return true;
            }
            return false;
          });
          if (node) {
            // 创建新轴，顺序与 components 相同
            tempLine.push({
              __config: { height: 40 },
              node: [cloneDeep(node.node)],
            });
          }
        }
        let oldIdx = 0;
        let startIdx = 0;
        // 从timeline开头进行遍历，找到能放的下节点位置
        while (oldIdx < currentPlayNodes.length && startIdx < tempLine.length) {
          const oldNode = currentPlayNodes[oldIdx];
          const newNode = tempLine[startIdx].node[0];
          // 旧节点所在时间轨
          const timeline = playConfig.timeline[oldNode.lineIndex];
          let aligned = false;
          if (
            newNode.offset === oldNode.node.offset &&
            newNode.duration === oldNode.node.duration
          ) {
            // 节点是对齐的
            aligned = true;
            timeline.node[oldNode.nodeIndex] = newNode;
            console.log(
              'handleUserSchemaChangeRun',
              '对齐节点放入',
              `索引: timeline: ${oldNode.lineIndex} node: ${oldNode.nodeIndex} \n`,
              '旧：',
              oldNode.node,
              '\n',
              '新',
              newNode
            );
            startIdx += 1;
          }
          if (!aligned) {
            // 当前时间轨无法对齐，需要清理掉旧节点
            timeline.node.splice(oldNode.nodeIndex, 1);
            console.log(
              'handleUserSchemaChangeRun',
              '新节点无法对齐，清理',
              `索引: timeline: ${oldNode.lineIndex} node: ${oldNode.nodeIndex} \n`,
              '旧: ',
              oldNode.node,
              '\n',
              '新',
              newNode
            );
          }
          oldIdx += 1;
        }
        // startIdx开始的节点，没有被放入对齐处理，要创建新的时间轴
        tempLine = tempLine.slice(startIdx);

        playConfig.timeline.push(...tempLine);
        console.log(
          'handleUserSchemaChangeRun',
          '需要新增的timeline ',
          tempLine
        );
      }

      if (change) {
        console.log('chachangenge', playConfig);
        setState(playConfig);
      }
    },
    {
      wait: 100,
    }
  );

  useEffect(() => {
    if (!searchParams) {
      return;
    }
    console.log('pagedoo-play-script1');
    handler.getGlobalData('pagedoo-play-script').then(async (res) => {
      console.log('pagedoo-play-script2', res);
      // 初始化
      // await sleep(200);
      setState((v) => {
        const initState = cloneDeep(v || (res as PlayConfig)) || {
          __config: {
            anchorTime: 0,
            position: { x: 0, y: 0 },
            scaleX: 50,
            selectedID: [],
          },
          fragment: [{ __config: { name: '' }, id: uuid(), offset: 0 }],
          timeline: [],
          version: 1,
        };
        if (searchParams.get('templateId')) {
          //   模板的情况，调整非UI组件时间轴位置
          initState.timeline = [...initState.timeline].sort(
            (a: any, b: any) => {
              return /Speech|Sound/i.test(JSON.stringify(b)) ? -1 : 0;
            }
          );
        }
        return initState;
      });
    });
  }, [searchParams]);
  useEffect(() => {
    const normalPage = handlePage(
      window.__page_editor.refHandle().getEditorContentSync().page_list[0].data
    );
    latestSchema.current = normalPage;
    handleUserSchemaChangeRun(normalPage);
    // handler.getPageContent().then((page) => {
    //   // setSchema(page as SingleVersionPage);
    //   const normalPage = handlePage(page);
    //   console.log(normalPage, 'normalPagenormalPagenormalPage init');
    //   latestSchema.current = normalPage;
    //   handleUserSchemaChangeRun(normalPage);
    // });
    const fn = Event.on('onChange', (page) => {
      console.log(Date.now(), page, 'onChange1; [DDDDE] ');
      const normalPage = handlePage(page);
      if (!isEqual(latestSchema.current, normalPage)) {
        // 说明是由于用户自行修改了协议 不是由于时间轴修改
        // setSchema(page as unknown as SingleVersionPage);
        console.log(
          '用户自行修改了协议 不是由于时间轴修改 page==>',
          cloneDeep(latestSchema.current),
          normalPage
        );
        latestSchema.current = normalPage;
        handleUserSchemaChangeRun(normalPage);
      } else console.log('debub same');
    });
    return () => {
      fn();
    };
  }, [handleUserSchemaChangeRun]);
  // schema改变时触发state改变

  // useEffect(() => {
  //   console.log(schema, 'schema');
  // }, [schema]);
  // useEffect(() => {
  //   if (!state) return;
  //   // const components = (schema as SingleVersionPage).components?.[0]?.children;
  //   const time = state.__config.anchorTime;
  //   const components = getPagedooSchemaComponents(state, time);
  //   if (!components) return;
  //   const playConfig: PagedooPlayConfig = {
  //     ...state,
  //     // timeline: [],
  //   };
  //
  //   // for (const line of state.timeline) {
  //   //   const nodeIndex = line.node.findIndex((node) => time >= node.offset && time < node.offset + node.duration);
  //   //   if (nodeIndex === -1) continue;
  //   //   const node = line.node[nodeIndex];
  //   //   const component = components.find((i) => i.key === node.key);
  //   //   line.node[nodeIndex] = {
  //   //     ...node,
  //   //     component: component as any,
  //   //   };
  //   // }
  //   let change = false;
  //   for (const component of components) {
  //     const node = findNodeByTimeKey(playConfig, time, component.key);
  //     console.log('playConfig, time, component.key', playConfig, time, component.key, '===', node);
  //     if (!node) {
  //       change = true;
  //       console.log(playConfig, '添加node', component);
  //       // 需要添加node
  //       playConfig.timeline.push({
  //         __config: { height: 40 },
  //         node: [
  //           {
  //             __config: { thumbnail: '', title: '', type: 'component' },
  //             actualDuration: 0,
  //             hidden: false,
  //             id: uuid(),
  //             key: component.key,
  //             offset: time,
  //             duration: 20000,
  //             component: component as any,
  //           },
  //         ],
  //       });
  //     }
  //   }
  //   if (change) setState(playConfig);
  // }, [state]);
  const selectedID = state?.__config.selectedID;
  const anchorTime = state?.__config.anchorTime;
  // 避免选择切换过程中的中间态
  const selectingRef = useRef(false);
  const selectTimeoutRef = useRef<number | NodeJS.Timeout>();
  const [ref, setRef] = useState<TimeNavigatorRef | null>(null);
  useEffect(() => {
    if (!state) return;
    if (selectedID?.length !== 1) return;
    const node = findNodeByID(state, selectedID[0]);
    if (!node) return;
    if (anchorTime === undefined) return;
    if (anchorTime >= node.offset && anchorTime < node.offset + node.duration) {
      selectingRef.current = true;
      clearTimeout(selectTimeoutRef.current);
      selectTimeoutRef.current = setTimeout(() => {
        handler.selectComponent(node.key);
        selectingRef.current = false;
      }, 500);
      // (async () => {
      //   if (handler.getSelectedComponentSync()?.key === node.key) {
      //     await handler.selectComponent();
      //   }
      // })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedID?.[0]]);
  useEffect(() => {
    const fn = (data: any) => {
      if (selectingRef.current || !ref || system === 'ad_live') {
        return;
      }
      const selected = getSelectedComponentInstance(data);
      if (!selected) {
        return;
      }
      if (selected?.key > 0) {
        ref.selectedByKey(selected?.key);
      }
    };
    editorEvent.on('onSelect', fn);
    return () => {
      editorEvent.off('onSelect', fn);
    };
  }, [ref, system]);
  const saveConfig = useThrottleFn(
    () => {
      handler.setGlobalData('pagedoo-play-script', state);
      console.log('setGlobalData==>', state, new Error().stack);
    },
    {
      leading: true,
    }
  );
  // 监听播放暂停
  useEffect(() => {
    const fn = async () => {
      const playing = ref?.playing;
      if (playing !== void 0) {
        const pagedooLive =
          (await editorHandler.getGlobalData('pagedoo-live')) ?? {};

        console.debug(ref?.playing, 'TimeNavigatorRef.playing');
        await editorHandler.setGlobalData('pagedoo-live', {
          ...pagedooLive,
          paused: !playing,
        });
      }
    };
    fn().then();
  }, [ref?.playing]);

  useEffect(() => {
    saveConfig.run();
  }, [saveConfig, state]);
  window.__pagedoo_play = {
    getPlayConfig: () => latestState.current!,
    setPlayConfig: setState,
    addComponent: (pos) => {
      dragPos.current = pos;
    },
  };

  // useEffect(() => {
  //   // console.log(window.opener, 'opener post');
  //   window.opener?.postMessage({ type: 'pagedoo_fetch_script' }, '*');
  //   const fn = (event: MessageEvent) => {
  //     const { data } = event;
  //     if (data.type === 'pagedoo_post_script') {
  //       // console.log(data, 'pagedoo_post_script1');
  //       const update = () => {
  //         setState(cloneDeep(data.script as PagedooPlayConfig));
  //       };
  //       // window.update = update;
  //       update();
  //       // setTimeout(update, 1000);
  //       // setTimeout(update, 2000);
  //       // setTimeout(update, 3000);
  //       // setTimeout(update, 4000);
  //       // setTimeout(update, 5000);
  //     }
  //   };
  //   window.addEventListener('message', fn);
  //   return () => window.removeEventListener('message', fn);
  // }, []);
  // useEffect(() => {
  //   const fn = () =>
  //     handler.setPreviewDevice([
  //       'iPhone X',
  //       375,
  //       812,
  //       'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
  //     ]);
  //   fn();
  //   setTimeout(fn, 1000);
  //   setTimeout(fn, 2000);
  //   setTimeout(fn, 3000);
  //   setTimeout(fn, 4000);
  //   setTimeout(fn, 5000);
  // }, []);

  const [editorDrawerIsOpen, setEditorDrawerIsOpen] = useState<boolean>(false);
  // 监听抽屉状态变化处理handle
  const onDrawerChangeHandle = (res: any) => {
    setEditorDrawerIsOpen(res.status === DrawerStatus.open);
  };

  useEffect(() => {
    // 监听抽屉状态变化
    window.__page_editor
      .refHandle()
      .pageEditorEvent()
      .on('rightLayerHandlerChanged', onDrawerChangeHandle);

    return () => {
      window.__page_editor
        .refHandle()
        .pageEditorEvent()
        .off('rightLayerHandlerChanged', onDrawerChangeHandle);
    };
  }, []);
  // console.log('state,statstateestatestate', state);
  return ReactDOM.createPortal(
    <div
      className={`meta-human-live-time-axis ${
        visible ? 'meta-human-live-time-axis--active' : ''
      }`}
      style={{
        // left: editorDrawerIsOpen ? '0px' : '288px',
        display: system === 'ad_live' ? 'none' : 'block',
        height: '100%',
      }}
    >
      {visible && (
        // <div className="title text-desc" onClick={trigger}>
        //   <span className="mr-4">脚本轨道</span>
        //   <ChevronUpIcon />
        // </div>
        <div
          onClick={trigger}
          style={{
            position: 'absolute',
            top: -21,
            width: 56,
            height: 21,
            left: 0,
            right: 0,
            margin: '0 auto',
            cursor: 'pointer',
            zIndex: 1,
          }}
        >
          <svg
            width="56"
            height="21"
            viewBox="0 0 56 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 16C0 7.16344 7.16344 0 16 0H40C48.8366 0 56 7.16344 56 16V21H0V16Z"
              fill="white"
            />
            <path
              d="M25.8258 8.70325C25.6128 8.53021 25.3077 8.53021 25.0947 8.70325V8.70325C24.8092 8.93522 24.8092 9.37104 25.0947 9.60302L28.37 12.2642C28.7374 12.5627 29.2638 12.5627 29.6312 12.2642L32.9065 9.60302C33.1921 9.37104 33.1921 8.93522 32.9065 8.70324V8.70324C32.6936 8.53021 32.3885 8.53021 32.1755 8.70325L29.0006 11.2828L25.8258 8.70325ZM25.8258 12.7657C25.6128 12.5927 25.3077 12.5927 25.0947 12.7657V12.7657C24.8092 12.9977 24.8092 13.4335 25.0947 13.6655L28.37 16.3267C28.7374 16.6252 29.2638 16.6252 29.6312 16.3267L32.9065 13.6655C33.1921 13.4335 33.1921 12.9977 32.9065 12.7657V12.7657C32.6936 12.5927 32.3885 12.5927 32.1755 12.7657L29.0006 15.3453L25.8258 12.7657Z"
              fill="black"
              fillOpacity="0.9"
            />
          </svg>
        </div>
      )}
      {!visible && (
        // <div className="title text-desc" onClick={trigger}>
        //   <span className="mr-4">脚本轨道</span>
        //   <ChevronUpIcon />
        // </div>
        <div
          onClick={trigger}
          style={{
            position: 'absolute',
            top: -21,
            width: 70,
            height: 21,
            left: 0,
            right: 0,
            margin: '0 auto',
            cursor: 'pointer',
            zIndex: 1,
          }}
        >
          <svg
            width="70"
            height="21"
            viewBox="0 0 70 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 16C0 7.16344 7.16344 0 16 0H54C62.8366 0 70 7.16344 70 16V21H0V16Z"
              fill="white"
            />
            <path
              d="M22.648 5.316H23.488V7.512H25.612V14.652C25.612 15.06 25.72 15.264 25.96 15.264H26.272C26.464 15.264 26.584 15.084 26.632 14.748C26.668 14.172 26.692 13.548 26.692 12.888L27.46 13.128C27.4 14.28 27.34 14.988 27.28 15.252C27.16 15.768 26.824 16.032 26.272 16.032H25.84C25.144 16.032 24.808 15.612 24.808 14.772V8.292H23.488V9.216C23.452 12.384 22.696 14.688 21.232 16.152L20.608 15.612C21.952 14.28 22.624 12.144 22.648 9.216V8.292H21.448V7.512H22.648V5.316ZM18.916 8.496H19.756V10.296H21.28V11.076H19.756V12.708C20.356 12.588 20.908 12.468 21.436 12.324V13.14C20.896 13.284 20.332 13.416 19.756 13.536V16.128H18.916V13.704C18.172 13.824 17.392 13.944 16.576 14.052L16.468 13.212C17.332 13.116 18.148 12.996 18.916 12.864V11.076H16.888L16.708 10.308C17.068 9.6 17.392 8.7 17.704 7.62H16.48V6.792H17.92C18.04 6.288 18.172 5.76 18.292 5.208L19.108 5.34C19 5.844 18.892 6.336 18.772 6.792H21.052V7.62H18.556C18.268 8.628 17.944 9.528 17.608 10.296H18.916V8.496ZM31.996 6.528H33.988C33.796 6.156 33.58 5.808 33.34 5.484L34.144 5.184C34.408 5.568 34.66 6.012 34.876 6.528H36.292C36.58 6.096 36.844 5.64 37.06 5.16L37.9 5.448C37.696 5.856 37.48 6.216 37.24 6.528H39.28V7.332H35.92C35.836 7.668 35.728 7.992 35.608 8.304H38.464V14.088H32.776V8.304H34.696C34.828 7.98 34.924 7.656 34.996 7.332H31.996V6.528ZM37.648 13.32V12.324H33.592V13.32H37.648ZM33.592 11.628H37.648V10.716H33.592V11.628ZM33.592 10.02H37.648V9.072H33.592V10.02ZM29.512 5.388C30.34 6.012 31.036 6.636 31.6 7.26L30.988 7.86C30.484 7.26 29.8 6.624 28.924 5.952L29.512 5.388ZM36.508 15.984C35.86 15.984 35.152 15.972 34.384 15.96C33.616 15.948 32.992 15.876 32.512 15.744C32.032 15.6 31.612 15.3 31.252 14.868C31.084 14.664 30.928 14.568 30.784 14.568C30.496 14.568 30.016 15.108 29.332 16.212L28.684 15.636C29.344 14.604 29.92 13.992 30.412 13.8V10.152H28.648V9.36H31.216V13.848C31.312 13.92 31.408 14.016 31.504 14.124C31.792 14.472 32.104 14.724 32.428 14.88C32.8 15.048 33.34 15.144 34.048 15.168C34.672 15.18 35.452 15.192 36.388 15.192C36.952 15.192 37.528 15.18 38.116 15.168C38.692 15.156 39.148 15.144 39.46 15.12L39.256 15.984H36.508Z"
              fill="black"
              fillOpacity="0.6"
            />
            <path
              d="M54.1742 14.2968C54.3872 14.4698 54.6923 14.4698 54.9053 14.2968C55.1908 14.0648 55.1908 13.629 54.9053 13.397L51.63 10.7358C51.2626 10.4373 50.7362 10.4373 50.3688 10.7358L47.0935 13.397C46.8079 13.629 46.8079 14.0648 47.0935 14.2968C47.3064 14.4698 47.6115 14.4698 47.8245 14.2968L50.9994 11.7172L54.1742 14.2968ZM54.1742 10.2343C54.3872 10.4073 54.6923 10.4073 54.9053 10.2343C55.1908 10.0023 55.1908 9.56652 54.9053 9.33454L51.63 6.67334C51.2626 6.37483 50.7362 6.37482 50.3688 6.67334L47.0935 9.33454C46.8079 9.56652 46.8079 10.0023 47.0935 10.2343C47.3064 10.4073 47.6115 10.4073 47.8245 10.2343L50.9994 7.65475L54.1742 10.2343Z"
              fill="black"
              fillOpacity="0.9"
            />
          </svg>
        </div>
      )}
      {/* <Component />*/}

      <ErrorBoundary
        fallbackRender={({ error, resetErrorBoundary }) => {
          return (
            <div role="alert">
              <p>Something went wrong:</p>
              <pre>{error.message}</pre>
              <button onClick={resetErrorBoundary}>Try again</button>
            </div>
          );
        }}
      >
        <div className="wrap" style={{ height: '100%' }}>
          <header
            className="header"
            style={{ height: '100%', display: 'block' }}
          >
            {/* <div className="icon" onClick={trigger}>*/}
            {/*  <svg*/}
            {/*    width="16"*/}
            {/*    height="13"*/}
            {/*    viewBox="0 0 16 13"*/}
            {/*    fill="none"*/}
            {/*    xmlns="http://www.w3.org/2000/svg"*/}
            {/*  >*/}
            {/*    <path*/}
            {/*      d="M-21 10C-21 1.16344 -13.8366 -6 -5 -6H19C27.8366 -6 35 1.16344 35 10V15H-21V10Z"*/}
            {/*      fill="#D9D9D9"*/}
            {/*    />*/}
            {/*    <path*/}
            {/*      d="M4.82579 2.70325C4.61282 2.53021 4.30769 2.53021 4.09472 2.70325C3.80921 2.93522 3.80921 3.37104 4.09472 3.60302L7.37004 6.26422C7.73745 6.56273 8.26382 6.56273 8.63123 6.26422L11.9065 3.60302C12.1921 3.37104 12.1921 2.93522 11.9065 2.70324C11.6936 2.53021 11.3885 2.53021 11.1755 2.70325L8.00064 5.28281L4.82579 2.70325ZM4.82579 6.76569C4.61282 6.59265 4.30769 6.59265 4.09472 6.76569C3.80921 6.99767 3.80921 7.43348 4.09472 7.66546L7.37004 10.3267C7.73745 10.6252 8.26382 10.6252 8.63123 10.3267L11.9065 7.66546C12.1921 7.43348 12.1921 6.99767 11.9065 6.76569C11.6936 6.59265 11.3885 6.59265 11.1755 6.76569L8.00064 9.34525L4.82579 6.76569Z"*/}
            {/*      fill="black"*/}
            {/*      fillOpacity="0.9"*/}
            {/*    />*/}
            {/*  </svg>*/}
            {/* </div>*/}
            {/* {state && (*/}
            {/*  <AIDemo*/}
            {/*    valueData={*/}
            {/*      latestState as React.MutableRefObject<PagedooPlayConfig>*/}
            {/*    }*/}
            {/*    onChange={setState}*/}
            {/*  />*/}
            {/* )}*/}
            {state && (
              <TimeNavigator
                ref={setRef}
                value={state}
                onChange={setState}
                onPageChange={async (components) => {
                  console.log(
                    '[DDDDE]  onPageChange componentscomponentscomponents',
                    components
                  );
                  if (!latestSchema.current) return;
                  const newContent: SingleVersionPage = cloneDeep(
                    latestSchema.current
                  );
                  if (!newContent.components[0]) return;
                  newContent.components[0].children = components as any;
                  const page = newContent;
                  // 获取state所有页面协议
                  const gemsPreview = getGemsPreview();
                  if (gemsPreview) {
                    await gemsPreview.ensurePageContent(page);
                    for (const componentID of getAllComponent(state)) {
                      await gemsPreview.ensureComponent(componentID);
                    }
                    setTimeAxisSharedAtom((prev) => ({
                      ...prev,
                      componentLoaded: true,
                    }));
                  }
                  console.log(
                    Date.now(),
                    page,
                    'await handler.setPageContent(page)1; [DDDDE] '
                  );
                  latestSchema.current = handlePage(page);
                  await sleep(0);
                  await handler.setPageContent(page);
                  console.log(
                    Date.now(),
                    page,
                    'await handler.setPageContent(page)2; [DDDDE] '
                  );
                  await sleep(0);
                  console.log(
                    Date.now(),
                    page,
                    'await handler.setPageContent(page)3; [DDDDE] '
                  );
                  setTimeout(() => {
                    loading.current = false;
                  }, 500);
                }}
              />
            )}
          </header>
        </div>
      </ErrorBoundary>
    </div>,
    document.querySelector('#meta-human-live-time-placeholder')!
  );
}

const handlePage = <T extends SingleVersionPage>(page: T) => {
  if (!page) return page;
  const newPage = cloneDeep(page);
  const deepDelete = <T extends Record<any, any>>(obj: T): T => {
    if (typeof obj !== 'object' || !obj || Object.isFrozen(obj)) return obj;
    Object.keys(obj).forEach((key) => {
      if (key === '__pagedoo_play_node') {
        delete obj[key];
      } else if (typeof obj[key] === 'object') {
        deepDelete(obj[key]);
      }
    });
    return obj;
  };
  return deepDelete(newPage);
};
