// bkg
import { CommonStyle } from '@tencent/gems-shared';
import { MaterialsAvatar } from '@/utils/play-component';
const right = {
  transform: {
    rotate: 0,
    opacity: 1,
    scale: 1,
    radius: 0,
  },
  position: {
    top: 33.15649867374005,
    bottom: 0,
    left: 35.01326259946949,
    right: 0,
    outset: 'TopLeft',
    unit: {
      left: 'percent',
      right: 'percent',
      top: 'percent',
      bottom: 'percent',
    },
    type: 'absolute',
  },
  size: {
    autoWidth: false,
    width: 75.06631299734748,
    autoHeight: true,
    height: 100,
    widthUnit: 'percent',
    heightUnit: 'percent',
  },
} as CommonStyle;
export const virtualman1811 = (
  text: string,
  commonStyle: CommonStyle = right
) => {
  return {
    id: `component/${MaterialsAvatar}/Virtualman`,
    name: 'Virtualman',
    key: 1811,
    style: {},
    commonStyle,
    wrapperStyle: {},
    chosen: false,
    data: {
      _v: 2,
      keyLight: {
        enabled: true,
        tolerance: 0.2433,
      },
      virtualMan: {
        id: '6fd8dbb32b2b4673a9891624e0b86cad',
        type: 'qq',
      },
      liveID: 'live',
      customScript: false,
      type: 'text',
      text,
      __component_name: '数字人',
      __component_sub_name: '数字人',
      __component_id: 'Virtualman',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};
export const img1881 = (url: string) => {
  return {
    id: 'component/gems-materials-pagedoo-base@1712583171/Image',
    name: 'Image',
    key: 1881,
    style: {},
    commonStyle: {
      size: {
        autoWidth: false,
        width: 100,
        autoHeight: true,
        height: 80,
        heightUnit: 'percent',
        widthUnit: 'percent',
      },
      position: {
        top: 6,
        left: 0,
        bottom: 0,
        right: 0,
        type: 'absolute',
        outset: 'TopLeft',
        unit: {
          left: 'percent',
          right: 'percent',
          top: 'percent',
          bottom: 'percent',
        },
      },
    },
    wrapperStyle: {},
    data: {
      dataConf: {
        href: '',
        imgMaterials: [
          {
            url,
          },
        ],
      },
      customStyle: 'opacity: 1',
      domEvent: { enable: false },
      fixPosition: false,
      __component_name: '图片',
      __component_sub_name: '',
      __component_id: 'Image',
      __component_mutex_data: '',
    },
    actions: [],
  };
};
export const img1882 = (url: string, commonStyle: CommonStyle) => {
  return {
    id: 'component/gems-materials-pagedoo-base@1712583171/Image',
    name: 'Image',
    key: 1882,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      dataConf: {
        href: '',
        imgMaterials: [
          {
            url,
          },
        ],
      },
      customStyle: 'opacity: 1',
      domEvent: { enable: false },
      fixPosition: false,
      __component_name: '图片',
      __component_sub_name: '',
      __component_id: 'Image',
      __component_mutex_data: '',
    },
    actions: [],
  };
};
export const textComp = (text: string) => ({
  id: 'component/gems-materials-pagedoo-base@1712583171/Text',
  name: 'Text',
  key: 1880,
  style: {},
  commonStyle: {
    size: { autoWidth: false, width: 1, autoHeight: true, height: 80 },
    transform: { rotate: 0, opacity: 1, scale: 1, radius: 0 },
    position: {
      top: 74.57525382668919,
      bottom: 0,
      left: 9.16069683167946,
      right: 0,
      outset: 'TopLeft',
      unit: {
        bottom: 'percent',
        left: 'percent',
        right: 'percent',
        top: 'percent',
      },
      type: 'absolute',
    },
  },
  wrapperStyle: {},
  data: {
    styleConf: {
      cmsBackgroundColor: { color: '', show: true, realColor: '' },
      textStyle: {
        fontFamily: [],
        fontSize: 16,
        letterSpacing: 0,
        color: {
          color: 'rgba(255,255,255,1)',
          show: true,
          realColor: 'rgba(255,255,255,1)',
        },
        text,
        rem: 50,
        width: 307,
        lineHeight: 1,
        useImage: 2,
        textAlign: 'center',
        ellipsis: 0,
      },
      event: { enable: false },
    },
    dataConf: { content: text, link: '' },
    customStyle: 'opacity: 1;',
    fixPosition: false,
    _v: 1,
    __component_name: text,
    __component_sub_name: '',
    __component_id: 'Text',
    __component_mutex_data: '',
  },
  actions: [],
});
