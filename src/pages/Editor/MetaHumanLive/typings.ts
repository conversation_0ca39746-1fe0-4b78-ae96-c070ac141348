/**
 * 直播间统计数据上报
 */
import { PushTaskConfig } from '@tencent/avatar-client-bridge-type/es';

export enum PUSH_METHOD {
  // 视频号开播
  LIVE_PLATFORM = 'direct-live-platform',
  // 客户端开播
  CLIENT = 'client',
  // 腾讯云开播
  LIVE_HOSTED = 'direct-live-hosted',
}
export interface IMetaLiveExtendData {
  // 开播方式
  pushMethod: PUSH_METHOD;
  rtmp_push_url?: string;
  rtmp_pull_url?: string;
  clientMeta?: {
    /**
     * 客户端sid
     */
    clientSid: string;
    /**
     * 客户端生成liveSessionId
     */
    clientLiveSessionId: string;
    /**
     * 客户端支持的特性
     */
    featureFlags?: {
      /**
       * 是否支持重启
       */
      supportRestart: boolean;
    };
    /**
     * 用户端唯一机器id
     */
    machineId?: string;
    /**
     * 客户端版本号
     */
    clientVersion?: string;
  };
  runningInClient?: boolean;
  renderType?: Required<PushTaskConfig>['renderType'];
}
