/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
/**
 * <AUTHOR>
 * @desc 数字人编辑器接入业务逻辑
 */

import {
  EditorImpl,
  InitPageType,
  PageEditorRef,
  PagedooEditorPolyfillProps,
} from '@tencent/pagedoo-editor';
import { handleEditorBack } from '@/pages/Editor/common/handleEditorBack';
import {
  handleInit,
  handleSave,
  saveMetaLiveExtend,
} from '@/pages/Editor/MetaHumanLive/handleLifeCycle';
import { IEditorData } from '@/pages/Editor/common/type';
import React from 'react';
import { setEditorContent } from '@/pages/Editor/common/editor';
import {
  LiveContentType,
  liveEditorDefaultOptions,
} from '@/pages/Editor/MetaHumanLive/defaultOptions';
import { handleScreenShot } from '@/pages/Editor/common/handleScreenShot';
import { MessagePlugin } from 'tdesign-react';
import { getRecoil } from 'recoil-nexus';
import { MetaEditorExtendData } from '@/pages/Editor/recoil/store';
import { getParams } from '@/utils/url';
import { Script } from '@/type/pagedoo';
import { MetaFeedbackSvr } from '@/pb/pb';
import { MaterialsBase } from '@/utils/play-component';
import { screenPlugin, screenShot } from '../common/screenshot';

const editorData: IEditorData = {
  // 素材库信息
  componentLibList: [],
};

export type SaveOriginProps = { content_data: InitPageType['content_data'] };

// 编辑器接入
export const metaHumanLiveClient: (
  editorRef: React.RefObject<PageEditorRef>
) => PagedooEditorPolyfillProps['client'] = (editorRef) => ({
  // --- 初始化
  async InitPage() {
    if (!editorRef.current) return {} as any;
    console.log('进入直播编辑器初始化');

    try {
      const { componentGroupList, componentLibList } = await handleInit();
      // 保存当前素材库信息
      editorData.componentLibList = componentLibList;
      const queryEditorInitOptions1 = queryEditorInitOptions({
        componentGroupList,
        componentLibList,
      });
      console.log('queryEditorInitOptions1:', queryEditorInitOptions1);
      return queryEditorInitOptions1;
    } catch (error) {
      console.error('--error:', error);
      void MessagePlugin.error('页面数据初始化失败');
      return {} as any;
    }
  },

  // --- 保存草稿接口
  async Save(data) {
    if (!data) {
      throw new Error('save data is undefined', data);
    }
    if (!data.content_data?.page_list?.length) {
      throw new Error('请添加页面');
    }
    const editorHandler: PageEditorRef = window.__page_editor.refHandle();
    if (!editorHandler) return {} as any;
    // 将page-editor中的截屏图片url替换
    await screenPlugin(data as SaveOriginProps);
    const saveExtendData = getRecoil(MetaEditorExtendData);
    console.log('saveExtendData:', saveExtendData);
    const { scriptId, origin, contentId, contentType } = getParams();
    const contentTypeStr = (
      contentId ? origin : `${origin}_${contentType}`
    ) as LiveContentType;
    if (!contentTypeStr) {
      void MessagePlugin.error('保存失败，未选择发布类型');
      throw new Error('保存失败，未选择发布类型');
    }

    if (!scriptId) {
      void MessagePlugin.error('保存失败，未找到脚本');
      return;
    }

    try {
      // 获取处理后的页面协议数据
      const contentProtocol = await queryEditorControl(
        data as SaveOriginProps,
        saveExtendData.contentName
      );
      if (!contentProtocol) {
        console.error('处理后的数据为', contentProtocol);
        return false;
      }
      const playScript = await editorHandler.handler.getGlobalData(
        'pagedoo-play-script'
      );
      if (!playScript) throw new Error('保存协议失败，请重试');
      // 处理setting数据
      const extendData = {
        // 支持视频录制封面
        ...saveExtendData.videoPosterInfo,
        scriptId,
        is_check_ok: `1`,
        playScript: JSON.stringify(playScript),
        pagodooQA:
          JSON.stringify(
            await editorHandler.handler.getGlobalData('pagedoo-qa')
          ) || '',
        // 后台会处理该特定字段
        ...(saveExtendData.sizeInfo
          ? {
              video_play_size: JSON.stringify([
                saveExtendData.sizeInfo.width,
                saveExtendData.sizeInfo.height,
              ]),
            }
          : null),
      };

      // 处理回显page_id和截图;
      const { pageList, contentId } = await handleSave({
        componentLibList: editorData.componentLibList,
        contentProtocol,
        extendData,
        contentType: contentTypeStr,
      });
      if (
        contentTypeStr.includes('live') &&
        saveExtendData.metaLiveExtendData
      ) {
        await saveMetaLiveExtend(contentId, saveExtendData.metaLiveExtendData);
      }
      await setEditorContent({ editorHandler, pageList });
      return true;
    } catch (error) {
      console.error('失败:', error);
      throw new Error('保存失败');
    }
  },

  // --- 返回按钮
  async OnBack() {
    // 编辑器协议返回，【！】勿修改
    return {};
  },

  // -- 【ignore】
  async OnTemplateChange() {
    return {} as any;
  },

  // --- 预览【ignore】
  async Preview() {
    return {
      url: '',
      description: '',
      effective_times: 0,
    };
  },

  // --- 完成并提审【ignore】
  async Finish() {
    return true;
  },
});

/**
 * 获取编辑器初始化配置
 * @param componentGroupList 组件信息
 * @param componentLibList 使用素材库信息
 */
export const queryEditorInitOptions: (arg: {
  componentGroupList: any;
  // componentGroupList: ComponentGroupType;
  componentLibList: InitPageType['component_lib_list'];
}) => InitPageType = ({ componentGroupList, componentLibList }) => {
  const queryOptions:
    | Pick<
        InitPageType,
        'content_data' | 'components' | 'component_lib_list' | 'toolbar'
      >
    | any = {};
  queryOptions.content_data = {
    page_list: [
      {
        data: {
          components: [
            {
              id: `component/${MaterialsBase}/Background`,
              key: 0,
              name: 'Background',
              style: {},
              commonStyle: {},
              wrapperStyle: {},
              actions: [],
              children: [],
              data: {
                __component_name: '背景设置',
                backgroundConf: {
                  type: 'color',
                  backgroundColor: {
                    color: '',
                    show: true,
                    realColor: 'unset',
                  },
                },
              },
            },
          ],
          plugins: [],
          version: {
            versionId: 'v606055e',
            versionName: 'default',
          },
        },
        page_id: 'index',
      },
    ],
  };
  queryOptions.components = componentGroupList;
  queryOptions.component_lib_list = componentLibList;
  return {
    // 返回合并后的编辑器配置
    ...liveEditorDefaultOptions,
    ...queryOptions,
  };
};

/**
 * @param editorData 页面协议
 * @param contentName 内容名称
 */
export const queryEditorControl = async (
  editorData: SaveOriginProps,
  contentName?: string
) => {
  const pageListData = [];
  let instancePosterUrl = '';

  for (const page of editorData.content_data.page_list) {
    const { terminal_type, ...resetList } = page;
    // 当前页面的封面图片
    let poster_url = page.poster_img;
    // 上传图片到cos
    if (
      poster_url &&
      /data:image\/(png|jpg|jpeg|gif|bmp);base64/.test(poster_url)
    ) {
      poster_url = await handleScreenShot({ imageBase64: poster_url });
      // !instancePosterUrl && (instancePosterUrl = poster_url);
    }
    instancePosterUrl = poster_url || '';

    pageListData?.push({
      ...resetList,
      html: '',
      poster_url,
      show: resetList?.show ?? 1,
      terminal_id: terminal_type || 'mobile',
    });
  }

  return {
    // 封面图
    poster_url: instancePosterUrl,
    // 页面协议
    page_list: pageListData,
    // 组件列表， 这里目前没有需要检查组件的逻辑
    component_list: [],
    // 内容标题
    content_title: contentName || editorData.content_data.content_title,
  };
};

/**
 * 将页面协议中的封面字段处理，让编辑器显示页面预览图
 * @param pageList 页面协议列表
 */
// const handlePosterUrl2PosterImg = (pageList: any) => {
//   // 将poster_url转换为poster_img
//   return pageList.map((page: any) => {
//     return {
//       ...page,
//       poster_img: page.poster_url,
//     };
//   });
// };
