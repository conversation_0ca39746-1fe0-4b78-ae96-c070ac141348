import html2canvas from 'html2canvas';

self.onmessage = async function (event) {
  const { dom } = event.data;
  const componentViews = (dom as HTMLElement).querySelectorAll(
    '.editor-preview-component-view'
  );
  const canvasTasks: Promise<HTMLCanvasElement>[] = [];
  if (componentViews?.length) {
    Array.from(componentViews).forEach((view) => {
      canvasTasks.push(html2canvas(view as HTMLElement, { useCORS: true }));
    });
    const res = await Promise.all(canvasTasks);
    const canvasList: string[] = [];
    res.forEach((canvas) => {
      const uri = canvas.toDataURL('image/png');
      canvasList.push(uri);
    });
    self.postMessage(canvasList);
  }
};
