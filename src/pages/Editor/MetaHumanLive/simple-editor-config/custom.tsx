import { IPagedooEditorComponentLibraryProps } from '@tencent/pagedoo-editor';

export const metaHumanCustomActionHandler: IPagedooEditorComponentLibraryProps['customActionHandler'] =
  async ({ item, addComponent, openConfigPanel }) => {
    const component = await addComponent({
      item,
      data: undefined,
      commonStyle: {},
    });
    if (!component) throw new Error('组件实例化失败');
    await openConfigPanel(component?.key);
  };
