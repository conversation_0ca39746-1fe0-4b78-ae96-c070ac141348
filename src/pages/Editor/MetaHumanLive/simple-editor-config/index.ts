import { ISimpleEditorConfig } from '@/pages/Editor/common/type';
import {
  metaHumanEditorComponentGroupList,
  NormalCandidateComponents,
} from './candidate-components';
import { metaHumanCustomActionHandler } from './custom';

export const MetaHumanSimpleEditorConfig: ISimpleEditorConfig = {
  candidateComponents: NormalCandidateComponents,
  customActionHandler: metaHumanCustomActionHandler,
  componentLibraryChildren: undefined,
  componentLimitationConfig: [],
  // 组件分类列表枚举
  groupList: metaHumanEditorComponentGroupList,
  componentListProps: {},
};
