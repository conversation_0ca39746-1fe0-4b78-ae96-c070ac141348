import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { serializeParam } from '@tencent/midas-util';
import { GeneralContentSvr } from '@/pb/pb';
import { CONTENT_TYPE_MAP } from '@/const/common';

export const ORIGIN_TYPE = {
  // 电商
  E_COMMERCE: 'text',
  // PPT (原讲师课程)
  PPT: 'ppt',
  // 广告脚本
  AD_SCRIPT: 'ad_script',
  // 游戏推广
  GAME_PROMOTION: 'gameprom',
  TXCZ_LIVE: 'txcz-live',
  // 文章
  DOC: 'doc',
  // 直播-内容带货
  CONTENT: 'buygoods', // 原来是 content
} as const;

export type LiveEditorOrigin = (typeof ORIGIN_TYPE)[keyof typeof ORIGIN_TYPE];

export function openEditor(
  params: {
    system: EditorSystemMap;
    origin: LiveEditorOrigin;
    contentType: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'];
    appId?: string;
    contentId?: string;
    scriptId?: string;
    presetId?: string;
    //   模版相关
    templateType?: string;
    templateId?: string;
    // 用户选择的画面尺寸(目前只在不选择模板同时不选择脚本的时候使用)
    deviceSize?: string;
  }
  // env?: number
) {
  let envParam = '';
  // const envEnum = env || import.meta.env.VITE_SANDBOX_ENV;
  if (import.meta.env.VITE_ENV === 'dev') {
    envParam = `?sandbox=2`;
  }

  const str = serializeParam({
    // system_code: import.meta.env.VITE_RUNNING_SYSTEM,
    ...params,
  });
  window.open(`${envParam}#/Editor/?${str}`);
  // window.open(`#/Editor/?${str}`);
}

export const getPresetId = async () => {
  const res = await GeneralContentSvr.GetDistributionId({
    model: 1,
  });
  return res.model_list[0] || '';
};

// 通过类型获取origin值 TODO: 没添加全，视频暂时是准确的，直播不准
export function getOriginByType(props: {
  contentType: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'];
  application_scenarios: string;
}): LiveEditorOrigin {
  const { contentType, application_scenarios } = props;
  switch (contentType) {
    case CONTENT_TYPE_MAP.Video.value:
      if (
        application_scenarios === CONTENT_TYPE_MAP.Video.children.Course.value
      ) {
        return ORIGIN_TYPE.PPT;
      }
      if (
        application_scenarios ===
        CONTENT_TYPE_MAP.Video.children.ECommerce.value
      ) {
        return ORIGIN_TYPE.E_COMMERCE;
      }
      if (
        application_scenarios === CONTENT_TYPE_MAP.Video.children.Vision.value
      ) {
        return ORIGIN_TYPE.GAME_PROMOTION;
      }
      if (
        application_scenarios === CONTENT_TYPE_MAP.Video.children.Article.value
      ) {
        return ORIGIN_TYPE.DOC;
      }
      return ORIGIN_TYPE.E_COMMERCE;
    case CONTENT_TYPE_MAP.Live.value:
      if (application_scenarios === CONTENT_TYPE_MAP.Live.children.AD.value) {
        return ORIGIN_TYPE.AD_SCRIPT;
      }
      if (application_scenarios === CONTENT_TYPE_MAP.Live.children.Live.value) {
        return ORIGIN_TYPE.CONTENT;
      }
      return ORIGIN_TYPE.E_COMMERCE;
    default:
      return ORIGIN_TYPE.E_COMMERCE;
  }
}
