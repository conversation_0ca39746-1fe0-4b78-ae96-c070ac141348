export type DrawerStatusType = 'closed' | 'open' | 'hidden';
export const DrawerStatus = {
  open: 'open' as DrawerStatusType,
  closed: 'closed' as DrawerStatusType,
  hidden: 'hidden' as DrawerStatusType,
};

const setDrawerStatus = async (status: DrawerStatusType) => {
  await window.__page_editor.refHandle().setRightLayerHandlerData({
    widthMap: {
      closed: 336,
      open: 1048,
      hidden: 336,
    },
    status,
  });
};

// 打开规则配置面板
export const openDrawer = async () => {
  await setDrawerStatus(DrawerStatus.open);
};

// 打开规则配置面板
export const closeDrawer = async () => {
  await setDrawerStatus(DrawerStatus.closed);
};

// 隐藏规则配置面板
export const hideDrawer = async () => {
  await setDrawerStatus(DrawerStatus.hidden);
};
