/**
 * <AUTHOR>
 * @date 2024/5/1 下午10:10
 * @desc loader
 */

export const generateLoader = ():
  | {
      type: 'match';
      match: string;
      replacer: string;
    }[]
  | undefined => {
  const domain = '//dev-pagedoo.pay.qq.com'; // COS_CONFIG.cdnBaseUrl; // 域名 区分环境
  const replacer = `https:${domain}/cdn/materials/\${1}/component-\${2}@\${3}/manifest.json`;
  return [
    {
      type: 'match',
      match: '(.*?):component-(.*?)@(.*)',
      replacer,
    },
    {
      type: 'match',
      match: '(.+)-(.+)-(.+)-(.+)-(.+)@(\\d+)',
      replacer: `https:${domain}/cdn/module/\${1}-\${2}-\${3}-\${4}-\${5}@\${6}/manifest.json`,
    },
    {
      type: 'match',
      match: '(.*?)@(.*)',
      replacer: `https:${domain}/cdn/materials/dist/\${0}/manifest.json`,
    },
  ];
};
