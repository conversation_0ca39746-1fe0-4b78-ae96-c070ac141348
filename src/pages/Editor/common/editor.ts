import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { generateLoader } from '@/pages/Editor/common/loader';
import { ReqType, RespType } from '@/pb/config';
import { ContentSvr } from '@/pb/pb';
import monitorLib from '@lib-dist/material-monitor.umd?raw';
import {
  MaterialsInfo,
  InitPageType,
  PageEditorRef,
} from '@tencent/pagedoo-editor';
import { generateLegacyHTML } from '@tencent/gems-html-generator';

/**
 * @description: 回显page_id和截图;
 * @param editorHandler
 * @param pageList
 * @param contentName
 */
export const setEditorContent = async ({
  editorHand<PERSON>,
  pageList,
  contentName,
}: {
  editorHandler: PageEditorRef;
  pageList: RespType<typeof ContentSvr.ContentDetailQuery>['page_list'];
  contentName?: string;
}) => {
  const editorContent = await editorHandler.getEditorContent();
  if (contentName) {
    editorContent.content_title = contentName;
  }
  await editorHandler.setEditorContent({
    ...editorContent,
    page_list: pageList.map((page, index) => {
      return {
        ...editorContent.page_list[index],
        page_id: page.page_id || '',
        poster_img: page.poster_url || '',
      };
    }),
  });
};

/**
 *
 * @param insertData 注入数据集合
 * @param pageList 页面列表
 * @param componentLibList 组件库资源列表
 */
export const generateHtml = async ({
  insertData,
  pageList,
  componentLibList,
  uin,
}: {
  insertData: Record<string, unknown>;
  pageList: ReqType<(typeof ContentSvr)['ContentCreate']>['page_list'];
  componentLibList: InitPageType['component_lib_list'];
  // 唯一id
  uin?: string;
}) => {
  const renderer = await ContentSvr.GemsVersionQuery({});
  const { monitorConf } = MatchedGlobalConfigItem.runtimeConf;
  const env =
    import.meta.env.VITE_ENV === 'production' ? 'production' : 'development';
  return await generateLegacyHTML({
    title: pageList?.[0]?.page_name || '',
    fastBoot: false,
    filePath: {
      'index.js': renderer.index,
      'polyfills-legacy.js': renderer.polyfill,
      'index-legacy.js': renderer.react,
      react: {
        'react.js': `${renderer.index
          .split('/')
          .slice(0, -1)
          .join('/')}/react.production.min.js`,
        'react-dom.js': `${renderer.index
          .split('/')
          .slice(0, -1)
          .join('/')}/react-dom.production.min.js`,
      },
    },
    global: {
      // pages: pageList,
      ...insertData,
    },
    inject: {
      head: {
        after: [
          `<script src="https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js"></script>`,
          `<script>${monitorLib}</script>`,
          `<script>
        (function(){
          var __monitor_instance = window.materialMonitor.getMonitor({
            aegisReportId: "${monitorConf.aegisReportId}",
            env: "${env}",
            uin: '${uin || ''}'
          });
          window.__pagedoo_api = {
            reportLog: function _reportLog(level, data){
              __monitor_instance?.log?.(level, data);
            } 
          };
        })();
          </script>`,
        ].join(''),
      },
    },
    materials: componentLibList.map((lib) => {
      return {
        meta: lib.meta,
        type: lib.type,
        alias: lib.alias,
        name: lib.lib_name,
      };
    }) as MaterialsInfo[],
    //   .concat({
    //   meta: `https:${import.meta.env.VITE_UPLOAD_CDN_BASE_URL}/cdn/materials/pagedoo-base/manifest.json`,
    //   alias: 'gems-materials-pagedoo-base',
    //   name: 'gems-materials-pagedoo-base',
    // }),
    // schema: JSON.parse(pageList?.[0].data || '{}'),
    loader: generateLoader(),
  });
};
