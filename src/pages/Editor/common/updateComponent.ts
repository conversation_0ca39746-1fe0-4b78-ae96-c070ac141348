/**
 * <AUTHOR>
 * @date 2024/9/25 下午5:00
 * @desc updateComponent
 */
import { PlayConfig } from '@/type/pagedoo';
import { cloneDeep } from 'lodash-es';
import { Component } from '@tencent/pagedoo-library';
import { MaterialsAvatar, MaterialsBase } from '@/utils/play-component';

const mapChildren = (
  components: Component[] | undefined,
  handle: (comp: Component) => void
) => {
  if (!components) return;
  for (const component of components) {
    handle(component);
    mapChildren(component.children, handle);
  }
};

export const updateVersion = (script: PlayConfig) => {
  const content = cloneDeep(script);
  for (const pagedooPlayTimeline of content.timeline)
    for (const playNode of pagedooPlayTimeline.node)
      mapChildren([playNode.component as Component], (component) => {
        const arr = component.id.split('/');
        const material = arr[1].split('@')[0];
        const latestVersion = [MaterialsAvatar, MaterialsBase].find((item) =>
          item.startsWith(`${material}@`)
        );
        if (latestVersion) {
          arr[1] = latestVersion;
          // eslint-disable-next-line no-param-reassign
          component.id = arr.join('/');
        }
      });

  return content;
};

/**
 * 遍历时间轨组件
 */
export const walkTimelineComponent = (
  script: PlayConfig,
  handle: (comp: Component) => void,
  options?: {
    noClone?: boolean;
  }
) => {
  const { noClone = false } = options ?? {};
  const content = noClone ? script : cloneDeep(script);
  for (const pagedooPlayTimeline of content.timeline)
    for (const playNode of pagedooPlayTimeline.node)
      mapChildren([playNode.component as Component], handle);

  return content;
};
