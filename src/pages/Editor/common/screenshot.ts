import html2canvas from 'html2canvas';
import { SaveOriginProps } from '../MetaHumanLive';
import { sleep } from '@/utils/sleep';

const nameMap = new Map<string, string>();
const regexp = /(https.*(font\d+)\.(\w+))/;

export type screenShotPluginType = (
  documentClone: Document,
  referenceElement: HTMLElement
) => any;

/**
 * 对传入的dom进行截屏返回图片url，默认截屏iframe
 * @param dom 需要截屏的dom
 * @param plugins 修改documentClone的插件
 * @returns 图片url
 */
export const screenShot = async (
  dom?: HTMLElement,
  plugins: screenShotPluginType[] = []
): Promise<string> => {
  const canvasIframe = document.querySelector('.editor-preview>iframe');
  const iframeWindow = (canvasIframe as HTMLIFrameElement)
    ?.contentDocument as Document;
  // const bodyClient = canvasIframe!.getBoundingClientRect();
  if (!dom && !iframeWindow) {
    console.debug('没有截屏的dom');
    return '';
  }
  if (!dom) {
    dom = iframeWindow.body;
  }
  const componentViews = [dom];
  const canvasTasks = componentViews.map(
    (view) =>
      new Promise<HTMLCanvasElement>((resolve, reject) => {
        return html2canvas(view as HTMLElement, {
          useCORS: true,
          // canvas: canvasShot,
          onclone: async (documentClone, referenceElement) => {
            try {
              plugins.push(
                ...[
                  applyFontsPlugin,
                  removeGreenPlugin,
                  adjustBorderImagePlugin,
                  hideEditorElementPlugin,
                ]
              );
              await plugins.reduce(async (prev, pl) => {
                await prev;
                await pl(documentClone, dom);
                return Promise.resolve();
              }, Promise.resolve());
            } catch (err) {
              reject(err);
            }
          },
        })
          .then(resolve)
          .catch(reject);
      })
  );

  const res = await Promise.all(canvasTasks);
  // 预览canvas
  // generateImageView(res);
  return res[0].toDataURL('image/png');
};

function getPixelColor(video: any, x: number, y: number) {
  const d2Canvas = document.createElement('canvas');
  document.body.appendChild(d2Canvas);
  d2Canvas.width = 2;
  d2Canvas.height = 2;
  const ctx = d2Canvas.getContext('2d') as any;
  ctx.drawImage(video, 0, 0, d2Canvas.width, d2Canvas.height);
  const imageData = ctx.getImageData(x, y, 1, 1).data;
  document.body.removeChild(d2Canvas);
  return rgbToHex(imageData);
}

function rgbToHex(imageData: any) {
  const r = imageData[0].toString(16).padStart(2, '0');
  const g = imageData[1].toString(16).padStart(2, '0');
  const b = imageData[2].toString(16).padStart(2, '0');
  return `#${r}${g}${b}`;
}
/**
 * 拷贝canvas
 * @param c canvas
 */
function copyCanvas(c: HTMLCanvasElement) {
  const newC = document.createElement('canvas');
  newC.width = c.width;
  newC.height = c.height;
  const newCtx = newC.getContext('2d');
  newCtx?.drawImage(c, 0, 0, newC.width, newC.height);
  return newC;
}

/**
 * 移除canvas中的绿幕
 * @param origin 需要移除绿幕的canvas
 */
function removeGreen(
  origin: HTMLCanvasElement,
  createChromaRender: (props: any) => any
) {
  // 取绿幕像素值
  const pixelColor = getPixelColor(origin, 0, 0);
  if (pixelColor !== '#000000') {
    const canvas = document.createElement('canvas');
    const render = createChromaRender({
      width: origin.width,
      height: origin.height,
      color: pixelColor,
      canvas,
    });
    render(origin);
    // 拷贝webgl像素，因：异步任务后webgl缓冲区会清空
    const newCanvas = copyCanvas(canvas);
    const parent = origin.parentElement as HTMLElement;
    if (parent) {
      parent.removeChild(origin);
      parent.append(newCanvas);
    }
    return newCanvas;
  }
  return origin;
}

// html2Canvas不支持border-image，平替该效果
function adjustBorderImagePlugin(doc: Document) {
  doc.querySelectorAll('*').forEach((el) => {
    const element = el as HTMLElement;
    const computedStyle = window.getComputedStyle(element);
    if (
      computedStyle.borderImage.includes('url') &&
      computedStyle.backgroundImage === 'none'
    ) {
      const imageUrl = computedStyle.borderImage.split(' ')[0];
      const { borderWidth } = computedStyle;
      element.style.border = 'none';
      element.style.background = `no-repeat ${imageUrl}`;
      element.style.padding = `${borderWidth}`;
      element.style.backgroundSize = 'cover';
    }
  });
}

/**
 * 遍历查找到数字人video对应的canvas，移除实景数字人身上的绿幕
 * @param doc 需要移除数字人绿幕的文档，html2canvas克隆后的document
 */
async function removeGreenPlugin(doc: Document, referenceElement: HTMLElement) {
  // 若无createChromaRender方法返回
  const iframeWindow = referenceElement.ownerDocument.defaultView as any;
  if (!iframeWindow?.__createChromaRender) return;
  doc.querySelectorAll('canvas').forEach(async (el, index) => {
    const nextSibling = el.nextElementSibling as HTMLElement;
    if (
      nextSibling &&
      nextSibling.tagName === 'CANVAS' &&
      nextSibling.style.opacity === '1'
    ) {
      removeGreen(el, iframeWindow.__createChromaRender);
    }
  });
}

/**
 * 根据map，替换doc的font-family
 * @param doc html2canvas克隆后的document
 * @param nameMap 需要替换的name映射
 */
async function renameFontFamiliesByNameMap(
  doc: Document,
  nameMap: Map<string, string>
) {
  // 遍历文档中的所有元素
  doc.querySelectorAll('*').forEach((el) => {
    const element = el as HTMLElement;
    if (element.style.fontFamily && regexp.test(element.style.fontFamily)) {
      // 如果包含，则根据nameMap更改fontFamily的值
      const oldFamily = element.style.fontFamily.split(',');
      const newFamily = oldFamily
        .map((family) => {
          const match = regexp.exec(family);
          if (match) {
            return nameMap.get(`${match[2]}-${match[3]}`);
          }
          return family;
        })
        .join(',');
      element.style.fontFamily = newFamily;
      // console.log('字体', element.style.fontFamily);
    }
  });
}

/**
 * 截屏时字体加载到document上，因为html2canvas的绘制逻辑运行在document上，但字体都在iframe上，需要移植
 * @param doc 源文档,html2canvas克隆的documentClone
 * @param referenceElement 目标文档
 */
async function applyFontsPlugin(doc: Document, referenceElement: HTMLElement) {
  const origin = referenceElement.ownerDocument;
  const target = document;
  if (origin === target) return;
  try {
    const fonts = Array.from(origin.fonts.keys());
    console.debug('开始获取字体', fonts);
    const fontsLoad = fonts.map(
      (font, index) =>
        new Promise((resolve, reject) => {
          const match = regexp.exec(font.family);
          if (match) {
            console.log(decodeURIComponent(match[1]));
            if (nameMap.has(`${match[2]}-${match[3]}`)) {
              resolve(0);
              return;
            }
            nameMap.set(
              `${match[2]}-${match[3]}`,
              `custom-${match[2]}-${match[3]}`
            );
            const fontFace = new FontFace(
              `custom-${match[2]}-${match[3]}`,
              `url('${decodeURIComponent(match[1])}')`
            );
            // 加载字体文件
            fontFace
              .load()
              .then((res) => {
                // 将字体样式添加到文档
                target.fonts.add(fontFace);
                resolve(res);
              })
              .catch(reject);
            return;
          }
          // 不是远程字体，不用加载
          resolve(0);
        })
    );
    await Promise.all([...fontsLoad]);
    console.debug('字体获取完成', Array.from(target.fonts.keys()));
    await renameFontFamiliesByNameMap(doc, nameMap);
    await sleep(100);
    return true;
  } catch (err) {
    console.debug('部分字体加载失败', err);
    // 字体加载失败，不需要中断截屏
    return false;
  }
}

/**
 * 隐藏编辑器注入画布区的相关元素
 */
async function hideEditorElementPlugin(doc: Document) {
  const injectStyle = doc.createElement('style');
  const css = `
    .editor-preview-component-mask,.plugin-tooltip,.plugin-tooltip-resize-popup{
      display: none !important;
    }
  `;
  injectStyle.innerHTML = css;
  doc.head.appendChild(injectStyle);
}

/**
 * 替换截屏图片url
 * @param editorData 编辑器保存时回调数据
 */
export const screenPlugin = async (editorData: SaveOriginProps) => {
  const poster_url = await screenShot();
  for (const page of editorData.content_data.page_list) {
    page.poster_img = poster_url || page.poster_img;
  }
};
(window as any).screenShot = screenShot;

// async function transformIframe(
//   dom: Document | HTMLElement,
//   style: { top?: string; left?: string; width: string; height: string }
// ) {
//   const { width, height } = style;
//   const iframe = document.createElement('iframe');
//   iframe.style.width = `${width}`;
//   iframe.style.height = `${height}`;
//   iframe.style.position = 'absolute';
//   iframe.style.top = style?.top || '0';
//   iframe.style.left = style?.left || '0';
//   iframe.className = 'screen-shot';
//   if ((dom as Document).contentType === 'text/html') {
//     const serializer = new XMLSerializer();
//     const elementString = serializer.serializeToString(dom);
//     const blob = new Blob([elementString], { type: 'text/html' });
//     const url = URL.createObjectURL(blob);
//     iframe.src = url;
//   }
//   return iframe;
// }
// 预览documentClone
// const cloneIframe = await transformIframe(documentClone, {
//   left: '0',
//   top: '0',
//   width: `${bodyClient.width}px`,
//   height: `${bodyClient.height}px`,
// });
// document.body.append(cloneIframe);

function generateImageView(res: HTMLCanvasElement[]) {
  const canvasList: string[] = [];
  res.forEach((canvas) => {
    // document.body.appendChild(canvas);
    const uri = canvas.toDataURL('image/png');
    canvasList.push(uri);
  });
  const overView = document.createElement('div');
  canvasList.forEach((uri) => {
    const img = new Image(200);
    img.src = uri;
    overView.appendChild(img);
  });
  overView.style.position = 'absolute';
  overView.style.top = '0';
  overView.style.display = 'grid';
  overView.className = 'screen-shot';
  document.body.append(overView);
}

function removeNodeList(elements: HTMLElement[]) {
  elements.forEach((element) => {
    element.parentNode?.removeChild(element);
  });
}
export const removeShot = async () => {
  removeNodeList(Array.from(document.querySelectorAll('.screen-shot')));
};

(window as any).removeShot = removeShot;
