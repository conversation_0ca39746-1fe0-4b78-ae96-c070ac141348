import { Event } from '@tencent/pagedoo-editor';
import type { EditorState } from '@tencent/pagedoo-editor/es/gems-editor/store';

export interface IWaitPreviewCanvasReadyResult {
  iframe: HTMLIFrameElement;
  previewEl: HTMLElement;
  data: EditorState;
}
/**
 * 等待画布区 iframe 准备好
 */
export const waitPreviewCanvasReady = async () => {
  return new Promise<IWaitPreviewCanvasReadyResult>((resolve) => {
    const onPreviewCanvasReady = async (data: EditorState) => {
      document.body.querySelectorAll('iframe').forEach((el) => {
        if (
          (el.parentNode as HTMLElement)?.className === 'editor-preview' &&
          el.contentWindow
        ) {
          Event.off('onEditorStateChange', onPreviewCanvasReady);
          resolve({
            iframe: el,
            previewEl: el.parentNode as HTMLElement,
            data,
          });
        }
      });
    };
    Event.on('onEditorStateChange', onPreviewCanvasReady);
  });
};
