/**
 * <AUTHOR>
 * @date 2024/5/5 下午6:09
 * @desc queryImageMsg
 */

//
export const getImageHeightAndWidth = (url: string) => {
  return new Promise<{ width: number; height: number }>((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = (err) => {
      reject(err);
    };
    img.src = url;
  });
};
