import type { TimeAxisProps } from '@/pages/Editor/MetaHumanLive/TimeAxis';
import { RespType } from '@/pb/config';
import { ContentSvr } from '@/pb/pb';
import { DeepRequire } from '@/utils/type-util';
import { ID } from '@tencent/gems-shared';
import {
  InitPageType,
  PagedooEditorGlobalData,
  IPagedooEditorComponentLibraryProps,
  ITypesDataProps,
  IPagedooEditorComponentListProps,
} from '@tencent/pagedoo-editor';
import React from 'react';

export type IContentData = RespType<(typeof ContentSvr)['ContentDetailQuery']>;

export interface IEditorComponent {
  component_id?: ID;
  component_type?: string;
}

export interface IEditorData {
  componentLibList: InitPageType['component_lib_list'];
  componentGroupList?: ComponentGroupType;
  componentIdList?: IEditorComponent[];
  // 区分预览环境字段
  isPreview?: boolean;
  // 打开编辑器的时间戳
  openEditorTime?: number;
}

export type ComponentGroupType = {
  name: string;
  id: string;
  is_closed: boolean;
  components: {
    name: string;
    id: string;
    icon: string;
    disabled: string;
    components: {
      component_id: string;
      id: string;
      name: string;
      desc: string;
      col: number;
      is_float: boolean;
      poster_url: string;
      mutex_desc: string;
      mutex_info: {
        mutex_type: string;
        mutex_id: string;
      }[];
      disabled: string;
      terminal_type?: string;
    }[];
  }[];
}[];

export interface ISimpleEditorConfig {
  candidateComponents: ITypesDataProps[];
  customActionHandler?: IPagedooEditorComponentLibraryProps['customActionHandler'];
  itemRender?: IPagedooEditorComponentLibraryProps['itemRender'];
  onAfterAdd?: IPagedooEditorComponentLibraryProps['onAfterAdd'];
  onBeforeAdd?: IPagedooEditorComponentLibraryProps['onBeforeAdd'];
  componentLibraryChildren?: IPagedooEditorComponentLibraryProps['children'];
  componentLimitationConfig?: DeepRequire<PagedooEditorGlobalData>['__meta']['template']['limitedComponent'];
  groupList?: IPagedooEditorComponentLibraryProps['groupList'];
  componentListProps?: IPagedooEditorComponentListProps;
  leftRender?: () => React.ReactNode;
  timeAxisProps?: Pick<TimeAxisProps, 'defaultDuration'>;
  onTemplateChanged?: (templateId: string) => void;
}
