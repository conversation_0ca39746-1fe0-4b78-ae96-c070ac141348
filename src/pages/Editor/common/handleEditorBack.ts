/**
 * @param hasOpenerHref 从父页面打开的页面，返回时跳转链接
 * @param href 直接打开编辑器，返回时跳转链接
 * <AUTHOR>
 */
export const handleEditorBack = ({
  hasOpenerHref,
  href,
  reload = true,
}: {
  hasOpenerHref: string;
  href: string;
  reload?: boolean;
}) => {
  // 判断是否有上一页，从管理台进编辑器的
  if (window.opener) {
    try {
      window.opener.location.href = hasOpenerHref;
      setTimeout(() => {
        reload && window.opener.location.reload();
        window.close();
      }, 20);
    } catch (error) {
      console.log('error:', error);
      window.close();
    }
  } else {
    // 直接打开编辑器的情况
    location.href = href;
  }
};
