import { dataURLtoBlob, fileMD5, uploadFileRequest } from '@/utils/cos';
import { COS_CONFIG } from '@/configs/upload';

/**
 * <AUTHOR>
 * @date 2022/12/11 10:26
 * @desc handleScreenShot
 */

export const handleScreenShot = async ({
  imageBase64,
  imageType = 'png',
}: {
  imageBase64: string;
  imageType?: string;
}) => {
  const imageBlob = dataURLtoBlob(imageBase64);
  const cosData = await fileMD5(imageBlob, 2097152)
    .then(async (md5) => {
      return await uploadFileRequest({
        bucket: '',
        region: '',
        name: `${md5}.${imageType}`,
        file: imageBlob,
        protocol: 'https:',
      });
    })
    .catch((error) => {
      console.error('error;', error);
      throw new Error('页面缩略图上传失败');
    });
  console.log('!!!cosData', cosData);
  return cosData?.url
    .replace(COS_CONFIG.cosBaseUrl, COS_CONFIG.cdnBaseUrl)
    .replace('/cdn', '');
};
