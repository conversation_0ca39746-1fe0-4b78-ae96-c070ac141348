import { InitPageType, PageEditorRef } from '@tencent/pagedoo-editor';
import { ToolbarType } from '@/pages/Editor/common/const';

export const banToolbarButton = async ({
  editorHandler,
  banList,
}: {
  editorHandler: PageEditorRef;
  banList: Array<keyof typeof ToolbarType> | 'all';
}) => {
  const toolData = await editorHandler.getToolbar();
  // 获取要禁用的按钮map
  const banListMap = Array.isArray(banList)
    ? banList.map((item) => ToolbarType[item])
    : 'all';
  // 延迟执行
  setTimeout(() => {
    editorHandler.setToolbar(
      toolData.map((tool) => ({
        ...tool,
        disabled: banListMap === 'all' || banListMap.includes(tool.type),
      }))
    );
  }, 100);
};

export const banToolBarOptions = ({
  toolbarOptions,
  banList,
}: {
  toolbarOptions: InitPageType['toolbar'];
  banList: Array<keyof typeof ToolbarType> | 'all';
}): InitPageType['toolbar'] => {
  // 获取要禁用的按钮map
  const banListMap = Array.isArray(banList)
    ? banList.map((item) => ToolbarType[item])
    : 'all';

  return toolbarOptions.map((tool) => ({
    ...tool,
    disabled: banListMap === 'all' || banListMap.includes(tool.type),
  }));
};
