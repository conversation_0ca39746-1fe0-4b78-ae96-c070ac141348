import { ADLiveClient } from '@/pages/Editor/ADLive';
import { adSimpleEditorConfig } from '@/pages/Editor/ADLive/simple-editor-config';
import { metaHumanLiveClient } from '@/pages/Editor/MetaHumanLive';
import { MetaHumanSimpleEditorConfig } from '@/pages/Editor/MetaHumanLive/simple-editor-config';

export enum EditorSystemMap {
  META_HUMAN = 'meta_human',
  AD_LIVE = 'ad_live',
}

export const EditorConfigMap = {
  [EditorSystemMap.META_HUMAN]: {
    pageKey: 'contentId',
    client: metaHumanLiveClient,
    documentTitle: '直播编辑器',
    simpleEditor: MetaHumanSimpleEditorConfig,
    // simpleEditor: {
    //   candidateComponents: NormalCandidateComponents,
    //   customActionHandler: metaHumanCustomActionHandler,
    //   componentLibraryChildren: undefined,
    //   componentLimitationConfig: [],
    //   groupList: [],
    // },
    // typesData: returnTypesData('meta_human'),
  },
  [EditorSystemMap.AD_LIVE]: {
    pageKey: 'contentId',
    client: ADLiveClient,
    documentTitle: '直播编辑器',
    simpleEditor: adSimpleEditorConfig,
    // typesData: returnTypesData('ad_live'),
  },
};
