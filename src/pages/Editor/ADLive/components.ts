/**
 * <AUTHOR>
 * @date 2024/5/30 下午8:01
 * @desc components
 */
import { ComponentGroupType } from '@/pages/Editor/common/type';
import { MaterialsAvatar } from '@/utils/play-component';
import { ProductOption } from '@/pages/ADMetaList/ADSelectProductModal';
import thumbnailBgMusic from '@/assets/images/thumbnail-bg-music.png';
import thumbnailVirtualman from '@/assets/images/thumbnail-virtualman.png';
import thumbnailQA from '@/assets/images/thumbnail-qa.png';
import thumbnailBg from '@/assets/images/thumbnail-live-bg.png';
import thumbnailSpeech from '@/assets/images/thumbnail-live-speech.png';
import thumbnailVoice from '@/assets/images/thumbnail-live-voice.png';
import thumbnailVideo from '@/assets/images/thumbnail-furnish-video.png';
import thumbnailImage from '@/assets/images/thumbnail-furnish-image.png';
import { IVoiceConfig } from '@/utils/template/plugins/scriptVirtualManPlugin';
import { ConfirmSeedScriptRequest } from '@/pb/api/DigitalManProc';

export const ADEditorComponents: ComponentGroupType = [
  {
    id: '90001',
    name: '直播组件',
    is_closed: false,
    components: [
      {
        id: '101',
        name: '数字人',
        icon: 'shuziren',
        disabled: '',
        components: [
          {
            name: '数字人',
            desc: '数字人',
            col: 12,
            is_float: false,
            poster_url: thumbnailVirtualman,
            mutex_info: [],
            id: '202403291333374521196032_mobile1011',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/Virtualman`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '102',
        name: '直播话术',
        icon: 'zhibohuashu',
        disabled: '',
        components: [
          {
            name: '直播话术',
            desc: 'LiveSpeechAD',
            col: 12,
            is_float: false,
            poster_url: thumbnailSpeech,
            mutex_info: [],
            id: '202403291333374521196032_mobile1021',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveSpeechAD`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '103',
        name: '直播音色',
        icon: 'yinse',
        disabled: '',
        components: [
          {
            name: '直播音色',
            desc: 'LiveSound',
            col: 12,
            is_float: false,
            poster_url: thumbnailVoice,
            mutex_info: [],
            id: '202403291333374521196032_mobile1031',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveSound`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '104',
        name: '背景音乐',
        icon: 'yinle',
        disabled: '',
        components: [
          {
            name: '直播背景音乐',
            desc: 'BackgroundMusic',
            col: 12,
            is_float: false,
            poster_url: thumbnailBgMusic,
            // poster_url:
            //   'https://dev-avatarcdn.pay.qq.com/material/52d91611de684313970569999e60add1.png',
            mutex_info: [],
            id: '202403291333374521196032_mobile1041',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/BackgroundMusic`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '105',
        name: '问答库',
        icon: 'wenda',
        disabled: '',
        components: [
          {
            name: '直播问答库',
            desc: 'LiveQA',
            col: 12,
            is_float: false,
            poster_url: thumbnailQA,
            mutex_info: [],
            id: '202403291333374521196032_mobile1051',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveQA`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '106',
        name: '装修组件',
        icon: 'beijing',
        disabled: '',
        components: [
          {
            name: '直播间视频',
            desc: 'LiveVideo',
            col: 12,
            is_float: false,
            poster_url: thumbnailVideo,
            mutex_info: [],
            id: '202403291333374521196032_mobile1061',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveVideo`,
            mutex_desc: '',
          },
          {
            name: '直播间背景',
            desc: '直播间背景',
            col: 12,
            is_float: false,
            poster_url: thumbnailBg,
            mutex_info: [],
            id: '202403291333374521196032_mobile1062',
            terminal_type: 'common',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveBkg`,
            mutex_desc: '',
          },
          {
            name: '直播间贴片',
            desc: '直播间贴片',
            col: 12,
            is_float: false,
            poster_url: thumbnailImage,
            mutex_info: [],
            id: '202403291333374521196032_mobile1063',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveImage`,
            mutex_desc: '',
          },
          {
            name: '直播间文本',
            desc: '直播间文本',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: '202403291333374521196032_mobile1064',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveText`,
            mutex_desc: '',
          },
        ],
      },
    ],
  },
];

export type CompLiveSpeechAD = {
  speechConfig: {
    goodsInfoList: ProductOption[];
    confirmedScriptList: [
      {
        live_id: string;
        script_content: string;
        script_id: string;
        product_id: string;
      }
    ];
    /**
     * 临时保存用于确认种子话术的字段
     */
    __tempConfirmedScriptList: Pick<
      ConfirmSeedScriptRequest,
      'script_kv' | 'script' | 'live_room_id' | 'script_source'
    >[];

    version?: 'v2';
  };
};

export type CompLiveQA = {
  qaConfig: {
    qaList: {
      id: string;
      // 问题内容
      question: string;

      // 回答
      answer: string;
    }[];
    bulletStatus: {
      activeBullet: boolean;
      bulletOnWall: boolean;
    };

    // 问答库组件会在页面协议添加qaGroupId
    qaGroupId: string;
  };
};

export type CompDataVirtualMan = {
  virtualMan: {
    appkey?: string;
    key: string;
    label: string;
  };
  voiceConfig: IVoiceConfig;
};

export const ADComponentsNameEnum = {
  // 广告话术组件
  Speech: 'LiveSpeechAD',
};
