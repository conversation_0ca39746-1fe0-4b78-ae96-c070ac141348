import { InitPageType } from '@tencent/pagedoo-editor';
import { MaterialsAvatar, MaterialsBase } from '@/utils/play-component';

export const ADLiveDefaultContentTitle = '未命名';

export const ADLiveEditorDefaultOptions: Omit<
  InitPageType,
  'content_data' | 'components' | 'component_lib_list' | 'template_data'
> = {
  server_time: Date.now() / 1000,
  container_component_id: `component/${MaterialsBase}/Background`,
  max_pages: 99,
  container_component_cname: '背景设置',
  invisible_component_id: [],
  default_content_title: ADLiveDefaultContentTitle,
  default_show_page: 0,
  disable_ui: 0,
  toolbar: [
    // {
    //   type: 0,
    //   title: '时间轴',
    //   style: 'display:inline-block',
    //   disabled: false,
    //   x_node_id: 'TimeAxis',
    //   x_node_popup_id: '',
    // },
    // {
    //   type: 0,
    //   title: '工具箱',
    //   style: 'display:inline-block',
    //   disabled: false,
    //   x_node_id: 'Toolbox',
    //   x_node_popup_id: '',
    // },
    {
      type: 0,
      title: '保存按钮',
      style: 'display:inline-block',
      disabled: false,
      x_node_id: 'ADSaveButton',
      x_node_popup_id: '',
    },
  ],
  modal_config: {
    back_modal: {
      title: '确定退出当前页面？',
      message: '当前页面有修改，但是您尚未保存，是否先保存再离开？',
      class_name: '',
      cancel_btn_text: '直接离开',
      confirm_btn_text: '保存并离开',
    },
  },
};

export const generateHtmlPage = [
  {
    data: {
      components: [
        {
          id: `component/${MaterialsBase}/Background`,
          key: 0,
          name: 'Background',
          style: {},
          commonStyle: {},
          wrapperStyle: {},
          data: {
            __component_name: '背景设置',
            backgroundConf: {
              type: 'color',
              backgroundColor: {
                color: '',
                show: true,
                realColor: 'unset',
              },
            },
          },
          actions: [],
          children: [
            {
              id: `component/${MaterialsAvatar}/Director`,
              name: 'Director',
              key: 1,
              style: {},
              commonStyle: {},
              wrapperStyle: {},
              chosen: false,
              data: {
                _v: 0,
                play: true,
                __component_name: '导演(Director)',
                __component_sub_name: '导演组件 负责脚本执行，渲染画面',
                __component_id: 'Director',
                __component_mutex_data: '',
                __pagedoo_i18n: {},
              },
              actions: [],
            },
          ],
        },
      ],
      plugins: [],
      version: {
        versionId: 'v606055e',
        versionName: 'default',
      },
    },
    page_id: 'index',
  },
];
