import { ISimpleEditorConfig } from '@/pages/Editor/common/type';
import { handler } from '@tencent/pagedoo-editor';
import { ComponentInstance } from '@tencent/pagedoo-library';
import { adLeftRender } from './ADLeftRenderer';
import { AdCandidateComponents } from './candidate-components';
import {
  adComponentLibraryChildren,
  adComponentLimitationConfig,
  adComponentListProps,
  adCustomActionHandler,
  adOnAfterAdd,
} from './custom';

export const adSimpleEditorConfig: ISimpleEditorConfig = {
  candidateComponents: AdCandidateComponents,
  customActionHandler: adCustomActionHandler,
  componentLibraryChildren: adComponentLibraryChildren,
  componentLimitationConfig: adComponentLimitationConfig,
  // componentLibrary添加组件完成后事件
  onAfterAdd: adOnAfterAdd,
  // 组件分类列表枚举
  // groupList: adEditorComponentGroupList,
  componentListProps: adComponentListProps,
  leftRender: adLeftRender,
  onTemplateChanged: async () => {
    debugger;
    const currentPageContent = await handler.getPageContent();
    const contents =
      currentPageContent?.components?.[0]?.children?.filter(
        (item: ComponentInstance) => item.id.includes('Virtualman')
      ) || [];
    if (contents.length > 0) {
      await handler.selectComponent(contents[0].key);
    }
  },
};
