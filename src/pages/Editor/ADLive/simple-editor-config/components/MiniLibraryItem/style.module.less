.type_item {
    display: flex;
    align-items: center;
    // padding: 0 (8px);
    height: 94px * 0.9;
    width: 120px * 0.9;
    cursor: pointer;

    .type_item_inner {
        width: 100%;
        // padding: (12px);
        height: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        gap: 6px;

        .icon {
            transform: translateX(36px);
            filter: drop-shadow(-36px 0px 0px #00000099);
            width: 36px;
            height: 36px;
        }

        .type_icon {
            width: 36px;
            height: 36px;
            overflow: hidden;
            color: #00000099;
        }

        .type_icon_active {
            .icon {
                filter: drop-shadow(-36px 0px 0px #0047F9)
            }
        }

        &:hover {
            .text {
                color: #0047F9;
            }

            border-radius: (8px);
        }

        .text {
            font-size: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-weight: 400;
            color: #00000099;
        }

        &:hover {
            .icon {
                filter: drop-shadow(-36px 0px 0px #0047F9)
            }

            background: linear-gradient(88.08deg, #C2D6FF -0.01%, #CDE0FF 49.89%, #F0E9FF 99.99%);

        }
    }

    .type_item_inner_active {
        .text {
            color: #0047F9;
        }

        border-radius: 8px;
        background: linear-gradient(88.08deg, #C2D6FF -0.01%, #CDE0FF 49.89%, #F0E9FF 99.99%);

    }
}