/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import type { ILibraryItemProps } from '@tencent/pagedoo-editor';
import React, { useCallback, useContext, useEffect, useRef } from 'react';
import Style from './style.module.less';
import { adComponentLimitationConfig } from '../../custom';
import { Component } from '@tencent/pagedoo-library';
import { LibraryContext } from '../../LibraryContext';

export function MiniLibraryItem(props: ILibraryItemProps) {
  const {
    tyBlockData,
    active: initialActive,
    onClick,
    customActionApi,
  } = props;
  const {
    currentSelectedComponent,
    cuurentSelectedLibraryItem,
    closeLibraryPanel,
    openLibraryPanel,
  } = customActionApi;

  const { eventBus } = useContext(LibraryContext);

  let active = initialActive;
  if (initialActive) {
    if (
      cuurentSelectedLibraryItem &&
      currentSelectedComponent &&
      tyBlockData.id !== cuurentSelectedLibraryItem.id
    ) {
      // 同时存在selectedComponent和selectedLibraryItem说明目前有两个高亮，libraryItem为正确的高亮，selectedComponent为错误的
      // 将item中存在customAction的取消高亮
      active = false;
    }
  }
  // 主要作用是不要首次就执行closeLibraryPanel逻辑，换成boolean都可以
  const preSelectComp = useRef<Component | null>();
  useEffect(() => {
    if (!active) preSelectComp.current = null;
    // 只对高亮的能打开简化表单的libraryItem做处理
    if (active && currentSelectedComponent && cuurentSelectedLibraryItem) {
      // 高亮的id
      const libraryId = cuurentSelectedLibraryItem.id.split('/')[2];
      // 当前选中的id
      const selectedId = currentSelectedComponent.id.split('/')[2];
      let multiple = false;
      const libLimit = adComponentLimitationConfig.filter(
        (item) => item.name === libraryId
      )[0];
      const selectedLimit = adComponentLimitationConfig.filter(
        (item) => item.name === selectedId
      )[0];
      if (libLimit?.maxInstance !== 1 && selectedLimit?.maxInstance !== 1) {
        multiple = true;
      }
      if (!preSelectComp.current) {
        // 首次不执行closeLibraryPanel
        preSelectComp.current = currentSelectedComponent;
      } else if (libraryId !== selectedId && !multiple) {
        // 后续画布或者图层选中时，如果选中其他组件，就关闭简化表单。
        closeLibraryPanel();
        preSelectComp.current = null;
      }
    }
  }, [
    active,
    currentSelectedComponent,
    cuurentSelectedLibraryItem,
    closeLibraryPanel,
  ]);

  // 当选中时，允许通过监听dom点击的方式来收起panel
  useEffect(() => {
    if (active) {
      eventBus.on(tyBlockData.id, (domId: string) => {
        const libraryId = tyBlockData.id.split('/')[2];
        // 当前选中的id
        const selectedId = domId.split('/')[2];
        const libLimit = adComponentLimitationConfig.filter(
          (item) => item.name === libraryId
        )[0];
        const selectedLimit = adComponentLimitationConfig.filter(
          (item) => item.name === selectedId
        )[0];
        let multiple = false;
        if (libLimit?.maxInstance !== 1 && selectedLimit?.maxInstance !== 1) {
          multiple = true;
        }
        if (domId !== tyBlockData.id && !multiple) {
          closeLibraryPanel();
          eventBus.off(tyBlockData.id);
        }
      });
    }
    return () => eventBus.off(tyBlockData.id);
  }, [active, closeLibraryPanel, tyBlockData, eventBus]);

  const ICON = tyBlockData.icon;
  return (
    <div className={Style.type_item}>
      <div
        className={`${Style.type_item_inner}  ${
          active ? Style.type_item_inner_active : ''
        }`}
        onClick={() => {
          onClick(tyBlockData);
        }}
      >
        <div
          className={`${Style.type_icon} ${
            active ? Style.type_icon_active : ''
          }`}
        >
          {React.cloneElement(ICON, { className: Style.icon })}
        </div>
        <span className={Style.text}>
          {tyBlockData.displayName || tyBlockData.name}
        </span>
      </div>
    </div>
  );
}
