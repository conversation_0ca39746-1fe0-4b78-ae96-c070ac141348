import { useEditorPageContent } from '@tencent/pagedoo-editor';
import { useDebounceEffect } from 'ahooks';
import { useCallback } from 'react';
/**
 * 处理ai 主播标识的层级的组件
 */
export function FixLiveAiIconZIndex() {
  const { sortedChildComponentList, changeComponentListSort, ready } =
    useEditorPageContent();

  const makeAiIconTop = useCallback(() => {
    if (
      Array.isArray(sortedChildComponentList) &&
      sortedChildComponentList.length > 0
    ) {
      const len = sortedChildComponentList.length;
      const idx = sortedChildComponentList.findIndex(
        (item) => item.name === 'LiveIdentify'
      );
      if (idx > -1 && idx !== len - 1) {
        console.log('-- AI标识被遮挡，提升层级');
        const keysArr = sortedChildComponentList.map((item) => item.key);
        const [key] = keysArr.splice(idx, 1);
        keysArr.push(key);
        changeComponentListSort(keysArr);
      }
    }
  }, [changeComponentListSort, sortedChildComponentList]);
  useDebounceEffect(() => {
    if (!ready) return;

    makeAiIconTop();
  }, [makeAiIconTop, ready]);
  return null;
}
