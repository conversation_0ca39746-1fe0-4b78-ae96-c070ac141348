import { DeepRequire } from '@/utils/type-util';
import {
  PagedooEditorGlobalData,
  PagedooComponentLibraryItem,
  ITypesDataProps,
} from '@tencent/pagedoo-editor';
import type {
  IPagedooEditorComponentLibraryProps,
  IPagedooEditorComponentListProps,
} from '@tencent/pagedoo-editor';

import { AdCandidateComponents } from '@/pages/Editor/ADLive/simple-editor-config/candidate-components';
import { findComponent } from '@/pages/Editor/SimpleEditor/util';
import { get, reject } from 'lodash-es';
import { baseSize, commonStyle } from '@/utils/play-view';
import { defaultRate } from '@/utils/template/live/utils';

export const adCustomActionHandler: IPagedooEditorComponentLibraryProps['customActionHandler'] =
  async ({
    item,
    openConfigPanel,
    addComponent,
    openLibraryPanel,
    closeLibraryPanel,
    findComponentInstanceByName,
    currentSelectedComponent,
    cuurentSelectedLibraryItem,
  }) => {
    // 广告场景下可能存在左侧选中组件的情况，这里判断没有选中的组件下才调用 closeLibraryPanel
    // if (!currentSelectedComponent) {
    //   closeLibraryPanel();
    // }
    const addOrOpen = async (name: string) => {
      // 背景音乐组件特殊处理
      const components = await findComponentInstanceByName(name);
      let key = -1;
      if (components.length === 0) {
        const result = await addComponent({
          item,
          data: undefined,
          commonStyle: {},
        });
        result && (key = result.key);
      }
      if (components.length > 0) {
        key = components[0].key;
      }
      if (key !== -1) {
        openConfigPanel(key);
      }
    };

    // 主播，背景，音乐特殊逻辑
    const libraryOrOpen = async (item: ITypesDataProps, name: string) => {
      const components = await findComponentInstanceByName(name);
      let key = -1;
      if (components.length === 0) {
        openLibraryPanel({ ...item, customAction: false });
      } else {
        key = components[0].key;
      }
      if (key !== -1) {
        openConfigPanel(key);
      }
    };
    const addOrOpenName = ['LiveSpeechAD', 'LiveQA'].find((n) =>
      item.id.includes(n)
    );
    const libraryOrOpenName = ['Virtualman', 'LiveBkg', 'BackgroundMusic'].find(
      (n) => item.id.includes(n)
    );
    if (item.id.includes('LiveText')) {
      addComponent({
        item,
        data: undefined,
        commonStyle: {},
      });
      // 文本添加完成
      // closeLibraryPanel();
    } else if (addOrOpenName) {
      await addOrOpen(addOrOpenName);
      // 选择完将selectedLibrary设置为undefined
      closeLibraryPanel();
    } else if (libraryOrOpenName) {
      closeLibraryPanel();
      await libraryOrOpen(item, libraryOrOpenName);
    }
  };

export const adComponentLibraryChildren: IPagedooEditorComponentLibraryProps['children'] =
  {
    configRender: (api) => {
      const { item } = api;
      const itemName = item.id.split('/')[2];
      return (
        <PagedooComponentLibraryItem
          tyBlockData={item}
          active={
            api.cuurentSelectedLibraryItem?.id === item.id ||
            api?.currentSelectedComponent?.name === itemName
          }
          key={item.id}
          onClick={async () => {
            const inst = await api.findComponentInstanceByName(
              item.id.split('/')[2]
            );
            let key = -1;
            if (inst.length > 0) {
              key = inst[0].key;
            } else {
              // 没有就添加组件
              console.log(`组件 ${item.id} 不存在，开始添加`);
              const inst = await api.addComponent({
                item,
                commonStyle: {},
                data: undefined,
              });
              if (inst) key = inst.key;
            }
            if (key > -1) {
              api.openConfigPanel(key);
            }
            // only 设置library panel 激活态
            // api.openConfigPanel();
          }}
        />
      );
    },
  };

export const adComponentLimitationConfig: DeepRequire<PagedooEditorGlobalData>['__meta']['template']['limitedComponent'] =
  [
    {
      name: 'Virtualman',
      maxInstance: 1,
      disableRemove: true,
      extendData: {},
    },
    // {
    //   name: 'LiveSound',
    //   maxInstance: 1,
    //   disableRemove: true,
    //   extendData: {},
    // },
    {
      name: 'LiveSpeechAD',
      maxInstance: 1,
      disableRemove: true,
      extendData: {},
    },
    {
      name: 'BackgroundMusic',
      maxInstance: 1,
      disableRemove: false,
      extendData: {},
    },
    {
      name: 'LiveQA',
      maxInstance: 1,
      disableRemove: true,
      extendData: {},
    },
    {
      name: 'LiveBkg',
      maxInstance: 1,
      disableRemove: true,
      extendData: {},
    },
    {
      name: 'LiveIdentify',
      maxInstance: 1,
      disableRemove: true,
      extendData: {},
    },
  ];

export const adComponentListProps: IPagedooEditorComponentListProps = {
  title: '图层',
  floatAbility: true,
};

// 左侧componentLibrary添加完组件逻辑
export const adOnAfterAdd: IPagedooEditorComponentLibraryProps['onAfterAdd'] =
  async (instance, api) => {
    const addName = ['Virtualman', 'BackgroundMusic', 'LiveBkg'].find((n) =>
      instance?.name.includes(n)
    );
    // 如果是数字人，音乐，背景，添加完就收起
    if (addName) {
      api.closeLibraryPanel();
      instance?.key && api.openConfigPanel(instance.key);
    } else {
      // 贴片视频等添加后选中
      instance?.key && api.openConfigPanel(instance.key);
    }
  };

// 左侧componentLibrary添加组件前逻辑
const componentNameMap = {
  LiveImage: 'img',
  LiveVideo: 'video',
  LiveText: '这是文本内容',
};

type rectType = { width: number; height: number };

// 获取图片的长宽信息
const getImageRect = async (url: string): Promise<rectType> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = url;
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
      });
    };
    img.onerror = (error) => {
      reject(error);
    };
  });
};
// 获取视频的长宽信息
const getVideoRect = async (url: string): Promise<rectType> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.src = url;
    video.onloadedmetadata = () => {
      resolve({
        width: video.videoWidth,
        height: video.videoHeight,
      });
    };
    video.onerror = (error) => {
      reject(error);
    };
  });
};

// 获取文本长宽
const getTextRect = (text: string): Promise<rectType> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    const canvasIframe = document.querySelector('.editor-preview>iframe');
    // 获取iframe的基准fontSize
    const iframeWindow = (canvasIframe as HTMLIFrameElement)
      ?.contentDocument as Document;
    const { fontSize } = iframeWindow.documentElement.style;
    // 计算出文字通过rem缩放完成后的fontSize，以缩放完成后的字体大小计算文本宽度
    ctx.font = `${(18 * parseInt(fontSize || '50', 10)) / 50}px PingFang SC`;
    const metrics = ctx.measureText(text);
    try {
      resolve({
        width: metrics.width || 0,
        height:
          metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent,
      });
    } catch (err) {
      reject(err);
    }
  });
};

// 获取居中的commonStyle
const getMiddleCommonStyle = (rect: rectType) => {
  const left = (baseSize.width - rect.width) / 2;
  const top = (baseSize.height - rect.height) / 2;
  return commonStyle(left, top, rect.width);
};
export const adOnBeforeAdd: IPagedooEditorComponentLibraryProps['onBeforeAdd'] =
  async (cfg, api) => {
    const { item, data } = cfg;
    // 获取图片或者video的url
    const compName = item.id.split('/')[2];
    if (!compName || !Object.keys(componentNameMap).includes(compName)) {
      // 不是文字/贴片/视频 就不做处理
      return void 0;
    }
    const name = compName as 'LiveImage' | 'LiveVideo' | 'LiveText';
    const url = get(data, `${componentNameMap[name]}[0].url`) || '';
    // 获取长宽信息
    let rect: rectType;
    if (name === 'LiveImage') {
      const res = await getImageRect(url);
      rect = {
        width: res.width * defaultRate,
        height: res.height * defaultRate,
      };
    } else if (name === 'LiveVideo') {
      const res = await getVideoRect(url);
      rect = {
        width: res.width * defaultRate,
        height: res.height * defaultRate,
      };
    } else {
      rect = await getTextRect(componentNameMap[name]);
    }
    if (name === 'LiveText') {
      cfg.data = {
        ...(cfg.data as undefined | Record<string, any>),
        fontStyle: {
          ...(cfg.data as undefined | Record<string, any>)?.fontSize,
          fontSize: 18,
          fontWeight: 600,
        },
      };
    }
    cfg.commonStyle = getMiddleCommonStyle(rect);
  };
