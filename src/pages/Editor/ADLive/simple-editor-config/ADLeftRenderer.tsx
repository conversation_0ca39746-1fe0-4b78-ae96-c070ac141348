import { MiniLibraryItem } from '@/pages/Editor/ADLive/simple-editor-config/components/MiniLibraryItem/MiniLibraryItem';
import {
  PagedooEditorComponentLibrary,
  handler,
} from '@tencent/pagedoo-editor';
import { AdCandidateComponents } from './candidate-components';
import {
  adComponentLibraryChildren,
  adCustomActionHandler,
  adOnAfterAdd,
  adOnBeforeAdd,
} from './custom';
import { LibraryContext } from './LibraryContext';
import EventBus from '@tencent/eventbus';
import { useEffect, useMemo } from 'react';
import { FixLiveAiIconZIndex } from '@/pages/Editor/ADLive/simple-editor-config/components/FixLiveAiIconZIndex/FixLiveAiIconZIndex';

export function ADLeftRenderer() {
  const libraryContextValue = useMemo(
    () => ({ eventBus: new EventBus<Record<string, any>>() }),
    []
  );
  const canvasIframe = document.querySelector('.editor-preview>iframe');
  const iframeWindow = (canvasIframe as HTMLIFrameElement)
    ?.contentDocument as Document;
  useEffect(() => {
    const iframeDocument = iframeWindow?.documentElement;
    function findDomKey(dom: HTMLElement) {
      if (dom.classList.contains('editor-preview-component')) {
        // 这是终止
        if (dom.getAttribute('data-key'))
          return parseInt(dom.getAttribute('data-key')!);
        return null;
      }
      // 寻找父节点
      if (dom.parentElement) return findDomKey(dom.parentElement);
      return null;
    }
    const handleClick = async (evnet: MouseEvent) => {
      const target = event?.target as HTMLElement;
      if (!target) return;
      const domKey = findDomKey(target);
      if (!domKey) return;
      const id = (await handler.getComponentViews()).views.find(
        (view) => view.key === domKey
      )?.id;

      if (id) {
        for (const busId of libraryContextValue.eventBus.keys()) {
          libraryContextValue.eventBus.emit(busId, id);
        }
      }
    };
    iframeDocument?.addEventListener('click', handleClick);
    return () => {
      iframeDocument?.removeEventListener('click', handleClick);
    };
  }, [libraryContextValue, iframeWindow]);

  return (
    <LibraryContext.Provider value={libraryContextValue}>
      <PagedooEditorComponentLibrary
        typesData={AdCandidateComponents}
        itemRender={(props) => <MiniLibraryItem {...props} />}
        customActionHandler={adCustomActionHandler}
        onBeforeAdd={adOnBeforeAdd}
        onAfterAdd={adOnAfterAdd}
        listContainerStyle={{
          width: '140px',
        }}
        libraryListStyle={{
          alignItems: 'center',
          display: 'flex',
          gap: '8px',
        }}
      >
        {adComponentLibraryChildren}
      </PagedooEditorComponentLibrary>
      <FixLiveAiIconZIndex />
    </LibraryContext.Provider>
  );
}

export const adLeftRender = () => <ADLeftRenderer />;
