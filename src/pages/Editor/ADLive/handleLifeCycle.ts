/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ComponentGroupType, IContentData } from '@/pages/Editor/common/type';
import { editorHandler, InitPageType } from '@tencent/pagedoo-editor';
import { RespType } from '@/pb/config';
import { generateHtml } from '@/pages/Editor/common/editor';
import { getParams } from '@/utils/url';
import { ContentSvr, Development } from '@/pb/pb';
import { queryEditorControl } from '@/pages/Editor/ADLive/index';
import { generateHtmlPage } from '@/pages/Editor/ADLive/defaultOptions';
import { ADEditorComponents } from '@/pages/Editor/ADLive/components';
import { IADSaveExtendData } from '@/pages/Editor/ADLive/typings';

// 编辑器初始化协议【勿改】
interface IInitReturn {
  // 页面协议
  contentData: IContentData | null;
  // 组件库分组信息
  componentGroupList: ComponentGroupType;
  // 组件库列表
  componentLibList: InitPageType['component_lib_list'];
  // 是否是来自脚本
  isFromScript: boolean;
}

// 编辑器保存协议
interface ISaveReturn {
  pageList: RespType<typeof ContentSvr.ContentDetailQuery>['page_list'];
  contentId: string;
}

/**
 * 获取编辑器初始化需要的参数
 * 1. DB获取当前编辑器下的组件信息
 * 2. 根据路由参数获取页面协议
 * 3. 返回组件信息、页面协议、素材库信息
 */
export const handleInit = async (): Promise<IInitReturn> => {
  return {
    contentData: null,
    // componentGroupList: [...ADEditorComponents],
    componentGroupList: [],
    componentLibList: [],
    isFromScript: true,
  };
};

/**
 * @description: 编辑器保存
 * @param componentLibList 组件库列表
 * @param contentProtocol 内容协议
 * @param extendData 扩展数据
 * @param contentType 内容类型
 */
export const handleSave = async ({
  componentLibList,
  contentProtocol,
  extendData,
  contentType,
}: {
  componentLibList: InitPageType['component_lib_list'];
  contentProtocol: Awaited<ReturnType<typeof queryEditorControl>>;
  extendData: IADSaveExtendData;
  contentType: string;
}): Promise<ISaveReturn> => {
  let urlParams = getParams();
  // 修改已有的页面
  if (urlParams.contentId) {
    contentProtocol.page_list[contentProtocol.page_list.length - 1].html =
      await generateHtml({
        insertData: {
          'pagedoo-play-script': JSON.parse(
            (extendData?.playScript as string) || '{}'
          ),
          'pagedoo-live': {
            id: urlParams.contentId,
          },
          pages: generateHtmlPage,
        },
        pageList: [],
        componentLibList,
        uin: urlParams.contentId,
      });
    // 修改直播内容
    await ContentSvr.ContentModify({
      ...contentProtocol,
      content_id: urlParams.contentId,
      content_type: contentType,
      template_id: urlParams.template_id,
      extend: extendData,
    } as any);
  } else {
    // 新增直播内容
    const createRes = await ContentSvr.ContentCreate({
      ...contentProtocol,
      content_type: contentType,
      template_id: urlParams.template_id,
      activity_id: urlParams.presetId,
      template_type: urlParams.template_type,
      extend: extendData,
    } as any);

    console.debug('--创建成功：', createRes);

    contentProtocol.page_list[contentProtocol.page_list.length - 1].html =
      await generateHtml({
        insertData: {
          'pagedoo-play-script': JSON.parse(
            (extendData?.playScript as string) || '{}'
          ),
          'pagedoo-live': {
            id: createRes.content_id,
          },
          pages: generateHtmlPage,
        },
        pageList: [],
        componentLibList,
        uin: createRes.content_id,
      });

    editorHandler.setGlobalData('pagedoo-live', { id: createRes.content_id });
    await ContentSvr.ContentModify({
      ...contentProtocol,
      content_type: contentType,
      content_id: createRes.content_id,
      template_id: urlParams.template_id,
      extend: extendData,
    } as any);
    // location.hash = `${location.hash}&contentId=${createRes.content_id}`;

    // 将content_id加入hash
    let newHash = `${location.hash}&contentId=${createRes.content_id}`;
    // 如果hash中有pid，删除掉
    if (location.hash.includes('presetId')) {
      const hashArr = newHash.split('&');
      const newHashArr = hashArr.filter((item) => !item.includes('presetId'));
      newHash = newHashArr.join('&');
    }
    history.replaceState(null, '', newHash);
  }

  urlParams = getParams();
  const { page_list: pageList } = await ContentSvr.ContentDetailQuery({
    content_id: urlParams.contentId,
  });
  return {
    pageList,
    contentId: urlParams.contentId,
  };
};

export const saveMetaLiveExtend = async (
  contentId: string,
  config_item_id: string,
  data: Record<string, unknown> | unknown[]
) => {
  await Development.UpdateLiveExtendConfigItem({
    live_id: contentId,
    node_id: 'global',
    config_item_id,
    config_item_value: JSON.stringify(data),
  });
};
