import { PlayConfig } from './../../../type/pagedoo';
import { extractComponentData, extractVirtualManInfo } from './extract';
import { CompLiveSpeechAD } from './components';
import { IADMetaLiveStatisticData } from './typings';
import to from 'await-to-js';
import { GetVoiceResourceList } from '@/pb/api/ResourceSvr';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';

/**
 * @throws
 */
export const getADMetaLiveStatisticData = async (
  playConfig: PlayConfig
): Promise<IADMetaLiveStatisticData> => {
  const virtualman = extractVirtualManInfo(playConfig);
  const liveSpeechAD = extractComponentData<CompLiveSpeechAD>(
    'LiveSpeechAD',
    playConfig
  ).nodeList;
  const [err, resp] = await to(
    GetVoiceResourceList({
      app_code: MatchedGlobalConfigItem.appcode,
      category_level1: 'all',
      category_level2: 'all',
      auth_level: 'all',
      user_id: '',
    })
  );
  if (err) throw err;
  return {
    productList: liveSpeechAD?.[0]?.data?.speechConfig?.goodsInfoList?.map(
      (item) => ({
        productId: item.product_id,
        productName: item.product_name,
      })
    ),
    virtualManInfoList: virtualman.map((item) => {
      const { data } = item;
      const { virtualMan, voiceConfig } = data;
      return {
        virtualManKey: virtualMan.key,
        virtualManName: virtualMan.label,
        voiceId: voiceConfig.currentVoiceItem.voiceId,
        voicePlatform: voiceConfig.currentVoiceItem.platform,
        voiceName:
          resp.voice_info_list.filter(
            (item) =>
              item.voice_id === voiceConfig.currentVoiceItem.voiceId &&
              item.platform === voiceConfig.currentVoiceItem.platform
          )[0]?.voice_name || '',
        driverMode:
          (voiceConfig.currentVoiceItem
            .driverMode as IADMetaLiveStatisticData['virtualManInfoList'][0]['driverMode']) ||
          'voice',
      };
    }),
  };
};
