import { ADComponentsNameEnum } from './components';
import { ADSpeechGenerateStatus } from './constants';
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
/**
 * <AUTHOR>
 * @desc 数字人编辑器接入业务逻辑
 */

import { CompLiveQA, CompLiveSpeechAD } from '@/pages/Editor/ADLive/components';
import {
  ADLiveDefaultContentTitle,
  ADLiveEditorDefaultOptions,
} from '@/pages/Editor/ADLive/defaultOptions';
import {
  extractComponentData,
  removeSpeechADTempField,
} from '@/pages/Editor/ADLive/extract';
import {
  handleInit,
  handleSave,
  saveMetaLiveExtend,
} from '@/pages/Editor/ADLive/handleLifeCycle';
import { getADMetaLiveStatisticData } from '@/pages/Editor/ADLive/statistic';
import { setEditorContent } from '@/pages/Editor/common/editor';
import { handleEditorBack } from '@/pages/Editor/common/handleEditorBack';
import { handleScreenShot } from '@/pages/Editor/common/handleScreenShot';
import { IEditorData } from '@/pages/Editor/common/type';
import { pluginADEditorHooks } from '@/pages/Editor/plugins/plugin-ad-editor-hooks';
import { pluginSafeZone } from '@/pages/Editor/plugins/plugin-safe-zone';
import { MetaEditorExtendData } from '@/pages/Editor/recoil/store';
import { ConfirmSeedScript, GetSeedScript } from '@/pb/api/DigitalManProc';
import { RespError } from '@/pb/config';
import { Development } from '@/pb/pb';
import { PlayConfig } from '@/type/pagedoo';
import { MaterialsBase } from '@/utils/play-component';
import { getParams } from '@/utils/url';
import to from 'await-to-js';
import cloneDeep from 'lodash-es/cloneDeep';
import React from 'react';
import { getRecoil } from 'recoil-nexus';
import { MessagePlugin } from 'tdesign-react';
import { screenPlugin } from '../common/screenshot';
import {
  InitPageType,
  PageEditorRef,
  PagedooEditorPolyfillProps,
} from '@tencent/pagedoo-editor';
import {
  IADSaveExtendData,
  IADSaveExtendsComponentMetaInfo,
} from '@/pages/Editor/ADLive/typings';
import { walkTimelineComponent } from '@/pages/Editor/common/updateComponent';
import { Component } from '@tencent/pagedoo-library';

const editorData: IEditorData = {
  // 素材库信息
  componentLibList: [],
};

type SaveOriginProps = { content_data: InitPageType['content_data'] };

// 保存前的校验
const savePreValidate = async (
  editorRef: PageEditorRef,
  options: {
    contentId: string;
    playScript: PlayConfig;
  }
) => {
  const { playScript: pagedooPlayScript } = options;

  const liveSpeechAD = extractComponentData<CompLiveSpeechAD>(
    'LiveSpeechAD',
    pagedooPlayScript
  ).nodeList;
  const errObj = {
    err: false,
    msg: '',
  };
  if (
    !Array.isArray(
      liveSpeechAD?.[0]?.data.speechConfig.__tempConfirmedScriptList
    ) ||
    liveSpeechAD?.[0]?.data.speechConfig.__tempConfirmedScriptList.length === 0
    // 没有临时数据
  ) {
    errObj.err = true;
    errObj.msg =
      '当前直播间暂未确认话术风格，无法保存，请在直播话术组件中确认要选择的直播话术后再次尝试';
    // 查询是否有上一次确认过的话术
    if (options.contentId) {
      const [err, resp] = await to(
        GetSeedScript({
          live_room_id: options.contentId,
        })
      );
      if (
        !err &&
        resp?.script_generate_status === ADSpeechGenerateStatus.CONFIRMED
      ) {
        // 有上一次确认过的话术
        walkTimelineComponent(
          pagedooPlayScript,
          (comp) => {
            // 添加临时确认的话术数据到组件中
            if (comp.id.includes(ADComponentsNameEnum.Speech)) {
              const { data } = comp as Component<CompLiveSpeechAD>;
              if (data.speechConfig) {
                errObj.err = false;
                data.speechConfig.__tempConfirmedScriptList = [
                  {
                    script: resp.script || '',
                    script_kv: resp.script_kv || [],
                    live_room_id: options.contentId,
                    script_source: resp.script_source,
                  },
                ];
              }
            }
          },
          {
            noClone: true,
          }
        );
      }
    }
    // MessagePlugin.error(
    //   '当前直播间暂未确认话术风格，无法保存，请在直播话术组件中确认要选择的直播话术后再次尝试'
    // );
    // throw new Error('no confirm confirmedScriptList');
  }
  if (errObj.err) {
    MessagePlugin.error(
      // '当前直播间暂未确认话术风格，无法保存，请在直播话术组件中确认要选择的直播话术后再次尝试'
      errObj.msg
    );
    throw new Error(errObj.msg);
  }
};

// 编辑器接入
export const ADLiveClient: (
  editorRef: React.RefObject<PageEditorRef>
) => PagedooEditorPolyfillProps['client'] = (editorRef) => ({
  // --- 初始化
  async InitPage() {
    if (!editorRef.current) return {} as any;
    console.log('进入直播编辑器初始化');
    pluginADEditorHooks({
      editorRef: editorRef.current,
    });
    pluginSafeZone({
      editorRef: editorRef.current,
      width: 40,
    });
    // editorRef.current.handler?.addHook()
    try {
      const { componentGroupList, componentLibList } = await handleInit();
      // 保存当前素材库信息
      editorData.componentLibList = componentLibList;
      return queryEditorInitOptions({
        componentGroupList,
        componentLibList,
      });
    } catch (error) {
      console.error('--error:', error);
      void MessagePlugin.error('页面数据初始化失败');
      return {} as any;
    }
  },

  // --- 保存草稿接口
  async Save(data) {
    if (!data) {
      throw new Error('save data is undefined', data);
    }
    if (!data.content_data?.page_list?.length) {
      throw new Error('请添加页面');
    }
    const editorHandler: PageEditorRef = window.__page_editor.refHandle();
    if (!editorHandler) return {} as any;
    // 将page-editor中的截屏图片url替换
    await screenPlugin(data as SaveOriginProps);
    const saveExtendData = getRecoil(MetaEditorExtendData);
    console.log('saveExtendData:', saveExtendData);
    if (!saveExtendData.contentType) {
      void MessagePlugin.error('保存失败，未选择发布类型');
      throw new Error('保存失败，未选择发布类型');
    }

    const { scriptId, contentId: urlContentId } = getParams();
    if (!scriptId) {
      void MessagePlugin.error('保存失败，未找到脚本');
      return;
    }
    // TODO: 待优化为抽象stage 执行
    try {
      // 获取时间轴数据
      // 复制一份
      const pagedooPlayScript = cloneDeep(
        await editorHandler.handler.getGlobalData('pagedoo-play-script')
      ) as PlayConfig;
      // 前置校验
      await savePreValidate(editorHandler, {
        contentId: urlContentId || '',
        playScript: pagedooPlayScript,
      });
      // 获取处理后的页面协议数据
      const contentProtocol = await queryEditorControl(
        data as SaveOriginProps,
        saveExtendData.contentName
      );
      if (!contentProtocol) {
        console.error('处理后的数据为', contentProtocol);
        return false;
      }
      // QA组件
      const liveQA = extractComponentData<CompLiveQA>(
        'LiveQA',
        pagedooPlayScript
      ).nodeList;
      console.debug(liveQA, 'LiveQA');
      const LiveQAData = liveQA?.[0]?.data;
      const liveSpeechAD = cloneDeep(
        extractComponentData<CompLiveSpeechAD>(
          'LiveSpeechAD',
          pagedooPlayScript
        ).nodeList
      );
      const LiveSpeechADData = liveSpeechAD?.[0]?.data;
      console.debug(liveSpeechAD, 'LiveSpeechAD');
      // 移除话术组件不必要的字段
      removeSpeechADTempField(pagedooPlayScript, {
        noClone: true,
      });
      // 处理setting数据
      const extendData: IADSaveExtendData = {
        scriptId,
        is_check_ok: `1`,
        playScript: JSON.stringify(pagedooPlayScript),
        qaGroupId: LiveQAData.qaConfig?.qaGroupId || '',
      };
      let componentMetaInfo: IADSaveExtendsComponentMetaInfo = {};
      if (LiveSpeechADData.speechConfig.version === 'v2') {
        // 如果话术组件是v2版本，则进行记录
        componentMetaInfo = {
          ...componentMetaInfo,
          LiveSpeechAD: {
            version: 'v2',
          },
        };
      }
      extendData.componentMetaInfo = JSON.stringify(componentMetaInfo);
      // 话术组件

      // 处理回显page_id和截图;
      const { pageList, contentId } = await handleSave({
        componentLibList: editorData.componentLibList,
        contentProtocol,
        extendData,
        contentType: saveExtendData.contentType as string,
      });
      const [getStatisticErr, result] = await to(
        getADMetaLiveStatisticData(pagedooPlayScript)
      );
      if (getStatisticErr) {
        const msg = '获取元数据失败';
        MessagePlugin.error(msg);
        throw new Error(msg);
      }

      const [saveStatisticErr] = await to(
        saveMetaLiveExtend(
          contentId,
          'ADMetaLiveStatisticData',
          result as Record<string, any>
        )
      );
      if (saveStatisticErr) {
        const msg = '保存直播间元数据失败';
        MessagePlugin.error(msg);
        throw new Error(msg);
      }
      await setEditorContent({
        editorHandler,
        pageList,
        contentName: saveExtendData.contentName,
      });

      // 这里前置校验一定会确保这里话术信息有选中的list
      if (LiveSpeechADData) {
        const productList = LiveSpeechADData.speechConfig.goodsInfoList || [];
        // 保存广告列表
        await saveMetaLiveExtend(contentId, 'ad_product_list', productList);
        // 保存直播话术
        if (LiveSpeechADData.speechConfig.version === 'v2') {
          const [err] = await to(
            ConfirmSeedScript({
              ...LiveSpeechADData.speechConfig.__tempConfirmedScriptList[0],
              live_room_id: contentId,
              product_id: productList[0]?.product_id || '',
            })
          );
          if (err) {
            const maybeRespErr: RespError = err as RespError;
            MessagePlugin.error(
              `直播话术确认失败，${
                maybeRespErr.resultInfo || maybeRespErr.message
              }`
            );
            throw err;
          }
        }
      }
      const saveExtendSelectedQALib: string[] = [];
      //  定义主动弹幕互动状态
      let QABulletStatus: CompLiveQA['qaConfig']['bulletStatus'] = {
        activeBullet: false,
        bulletOnWall: false,
      };

      // QA组件保存问答库
      // 有qaGroupId说明qa组件已经创建了问答库草稿，这里进行信息的更新
      if (LiveQAData.qaConfig?.qaGroupId) {
        // TODO: 这里的qaGroupId 也要从页面协议中获取，修改组名称然后调用转化草稿箱
        const {
          // qaList,
          qaGroupId,
          bulletStatus,
        } = LiveQAData.qaConfig;
        // 更新弹幕互动状态
        QABulletStatus = cloneDeep(bulletStatus);
        // 修改草稿箱名称
        const [updateErr] = await to(
          Development.UpdateUserGroupInfor({
            group_id: qaGroupId,
            // 直播间名称
            group_name: saveExtendData.contentName,
            group_type: 2,
          })
        );
        if (updateErr) throw updateErr;
        // 转换草稿箱到正式问答库
        const [tranErr] = await to(
          Development.TransUserGroupType({
            group_id: qaGroupId,
            group_type: 1,
          })
        );
        if (tranErr) throw tranErr;
        saveExtendSelectedQALib.push(qaGroupId);
        // if (saveExtendData.qaGroupId) {
        //   // if (qaList.length && saveExtendData.qaGroupId) {
        //   await addIndependentQALib(saveExtendData.qaGroupId, qaList);
        // }
      }
      await saveMetaLiveExtend(contentId, 'MetaLiveExtendFormValue', {
        selectQALib: saveExtendSelectedQALib,
        answerType: 'qaLib',
        productId:
          LiveSpeechADData?.speechConfig?.goodsInfoList?.[0]?.product_id || '',
      });
      // 主动弹幕互动状态保存到扩展表中
      if (QABulletStatus) {
        await saveMetaLiveExtend(
          contentId,
          'MetaQABulletStatus',
          QABulletStatus
        );
      }
      await MessagePlugin.success('直播间保存成功，您可以进行开播啦');

      window.location.replace(
        `${
          window.location.origin + window.location.pathname
        }#/ad-list?t=${Date.now()}`
      );
      // 路由跳转到/ad-list
      // location.hash = '/ad-list';
      return true;
    } catch (e) {
      console.error('失败:', e);
      if (e instanceof RespError) {
        const msg =
          e.resultCode === '1101015' ? '正在直播中，保存失败' : '保存失败';
        throw new Error(msg);
      } else {
        throw new Error('保存失败');
      }
    }
  },

  // --- 返回按钮
  async OnBack() {
    const { protocol, hostname, pathname } = location;
    // 处理离开编辑器跳转逻辑
    handleEditorBack({
      hasOpenerHref: `${protocol}//${hostname}${pathname}`,
      href: `${protocol}//${hostname}${pathname}`,
      reload: false,
    });
    // 编辑器协议返回，【！】勿修改
    return {};
  },

  // -- 【ignore】
  async OnTemplateChange() {
    return {} as any;
  },

  // --- 预览【ignore】
  async Preview() {
    return {
      url: '',
      description: '',
      effective_times: 0,
    };
  },

  // --- 完成并提审【ignore】
  async Finish() {
    return true;
  },
});

/**
 * 获取编辑器初始化配置
 * @param componentGroupList 组件信息
 * @param componentLibList 使用素材库信息
 */
// TODO: 这里需要传个contentData进来获取content_title
export const queryEditorInitOptions: (arg: {
  componentGroupList: any;
  // componentGroupList: ComponentGroupType;
  componentLibList: InitPageType['component_lib_list'];
}) => InitPageType = ({ componentGroupList, componentLibList }) => {
  const queryOptions:
    | Pick<
        InitPageType,
        'content_data' | 'components' | 'component_lib_list' | 'toolbar'
      >
    | any = {};
  queryOptions.content_data = {
    page_list: [
      {
        data: JSON.stringify({
          components: [
            {
              id: `component/${MaterialsBase}/Background`,
              key: 0,
              name: 'Background',
              style: {},
              commonStyle: {},
              wrapperStyle: {},
              actions: [],
              children: [],
              data: {
                __component_name: '背景设置',
                backgroundConf: {
                  type: 'color',
                  backgroundColor: {
                    color: '',
                    show: true,
                    realColor: 'unset',
                  },
                },
              },
            },
          ],
          plugins: [],
          version: {
            versionId: 'v606055e',
            versionName: 'default',
          },
        }),
        page_id: 'index',
      },
    ],
    content_title: ADLiveDefaultContentTitle,
  };
  queryOptions.components = componentGroupList;
  queryOptions.component_lib_list = componentLibList;
  return {
    // 返回合并后的编辑器配置
    ...ADLiveEditorDefaultOptions,
    ...queryOptions,
  };
};

/**
 * @param editorData 页面协议
 * @param contentName 内容名称
 */
export const queryEditorControl = async (
  editorData: SaveOriginProps,
  contentName?: string
) => {
  const pageListData = [];
  let instancePosterUrl = '';

  for (const page of editorData.content_data.page_list) {
    const { terminal_type, ...resetList } = page;
    // 当前页面的封面图片
    let poster_url = page.poster_img;
    // 上传图片到cos
    if (
      poster_url &&
      /data:image\/(png|jpg|jpeg|gif|bmp);base64/.test(poster_url)
    ) {
      poster_url = await handleScreenShot({ imageBase64: poster_url });
      // !instancePosterUrl && (instancePosterUrl = poster_url);
    }
    instancePosterUrl = poster_url || '';

    pageListData?.push({
      ...resetList,
      html: '',
      poster_url,
      show: resetList?.show ?? 1,
      terminal_id: terminal_type || 'mobile',
    });
  }

  return {
    // 封面图
    poster_url: instancePosterUrl,
    // 页面协议
    page_list: pageListData,
    // 组件列表， 这里目前没有需要检查组件的逻辑
    component_list: [],
    // 内容标题
    content_title: contentName || editorData.content_data.content_title,
  };
};
