import { walkTimelineComponent } from './../common/updateComponent';
import { CompDataVirtualMan } from '@/pages/Editor/ADLive/components';
import { PlayConfig } from '@/type/pagedoo';
export interface ExtractConfig<T> {
  nodeList: {
    nodeId: string;
    data: T;
  }[];
}
export const extractComponentData = <T>(
  name: string,
  playConfig: PlayConfig
): ExtractConfig<T> => {
  const nodeList: ExtractConfig<T>['nodeList'] = [];
  for (const pagedooPlayTimeline of playConfig.timeline) {
    for (const playNode of pagedooPlayTimeline.node) {
      if (playNode.component.id.endsWith(`/${name}`)) {
        const data = playNode.component.data as T;
        nodeList.push({
          nodeId: playNode.id,
          data,
        });
      }
    }
  }
  return { nodeList };
};

export const extractVirtualManInfo = (playConfig: PlayConfig) => {
  const virtualman = extractComponentData<CompDataVirtualMan>(
    'Virtualman',
    playConfig
  ).nodeList;
  return virtualman;
};

export const removeSpeechADTempField = (
  playConfig: PlayConfig,
  options?: Parameters<typeof walkTimelineComponent>[2]
) => {
  walkTimelineComponent(
    playConfig,
    (comp) => {
      if (comp.id.includes('LiveSpeechAD')) {
        const {
          data = {
            speechConfig: {},
          },
        } = comp;
        if ((data as any).speechConfig) {
          ['__tempConfirmedScriptList', '__tempAIScriptList'].forEach(
            (key) =>
              delete (data as Record<'speechConfig', any>).speechConfig[key]
          );
        }
      }
    },
    options
  );
};
