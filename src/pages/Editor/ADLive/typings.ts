/**
 * 直播间统计数据上报
 */
export interface IADMetaLiveStatisticData {
  productList: {
    productId: string;
    productName?: string;
  }[];

  virtualManInfoList: {
    virtualManKey: string;
    virtualManName?: string;
    voiceId: string;
    voiceName?: string;
    voicePlatform?: string;
    driverMode?: 'voice' | 'text';
  }[];
}

export interface IADSaveExtendsComponentMetaInfo {
  LiveSpeechAD?: {
    version?: 'v2';
  };
}
/**
 * 调用ContentCreate/ContentModify 接口保存的扩展信息
 */
export interface IADSaveExtendData {
  /**
   * 对应后台researchId
   */
  scriptId: string;
  /**
   * 固定填1
   */
  is_check_ok: string;
  /**
   * 时间轨协议
   */
  playScript: string;
  /**
   * 问答库groupId
   */
  qaGroupId: string;

  /**
   * 组件元信息，对应 IADSaveExtendsComponentMetaInfo 这个结构
   */
  componentMetaInfo?: string;
}
