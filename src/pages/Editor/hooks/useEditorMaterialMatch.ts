import { useEffect } from 'react';
// import { COS_CONFIG } from '@/configs/upload';
import { MaterialsInfo, editorHandler } from '@tencent/pagedoo-editor';
export const loader = async (
  materialsName: string,
  __no_use: unknown
): Promise<MaterialsInfo | void> => {
  const domain = 'https://dev-pagedoo.pay.qq.com';
  if (materialsName.match(/(.*):component-(.*)@(.*)/)) {
    // const domain = COS_CONFIG.cdnBaseUrl; // 域名 区分环境
    return {
      meta: `${domain}/cdn/materials/${materialsName.replace(
        ':',
        '/'
      )}/manifest.json`,
      name: materialsName,
      alias: materialsName,
    };
  }
  // 超级工厂素材库
  if (materialsName.match(/^.+-.+-.+-.+-.+@\d+$/)) {
    // const domain = COS_CONFIG.superFactoryCdnBaseUrl; // 域名 区分环境
    return {
      meta: `${domain}/cdn/module/${materialsName}/manifest.json`,
      name: materialsName,
      alias: materialsName,
    };
  }
  if (materialsName.match(/(.*)@(.*)/)) {
    // const domain = COS_CONFIG.cdnBaseUrl; // 域名 区分环境
    return {
      meta: `${domain}/cdn/materials/dist/${materialsName}/manifest.json`,
      name: materialsName,
      alias: materialsName,
    };
  }
  if (materialsName === 'gems-materials-pagedoo-base') {
    console.error('请不要使用此素材库 gems-materials-pagedoo-base');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-base/manifest.json',
      name: 'gems-materials-pagedoo-base',
      alias: 'gems-materials-pagedoo-base',
    };
  }
  if (materialsName === 'gems-materials-pagedoo-cms') {
    console.error('请不要使用此素材库 gems-materials-pagedoo-cms');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-cms/manifest.json',
      name: 'gems-materials-pagedoo-cms',
      alias: 'gems-materials-pagedoo-cms',
    };
  }
  if (materialsName === 'gems-materials-pagedoo-shop') {
    console.error('请不要使用此素材库 gems-materials-pagedoo-shop');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-shop/manifest.json',
      name: 'gems-materials-pagedoo-shop',
      alias: 'gems-materials-pagedoo-shop',
    };
  }
  if (materialsName === 'gems-materials-pagedoo-activity') {
    console.error('请不要使用此素材库 gems-materials-pagedoo-activity');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-activity/manifest.json',
      name: 'gems-materials-pagedoo-activity',
      alias: 'gems-materials-pagedoo-activity',
    };
  }
  if (materialsName === 'pagedoo-base') {
    console.error('请不要使用此素材库 pagedoo-base');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-base/manifest.json',
      name: 'gems-materials-pagedoo-base',
      alias: 'pagedoo-base',
    };
  }
  if (materialsName === 'pagedoo-cms') {
    console.error('请不要使用此素材库 pagedoo-cms');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-cms/manifest.json',
      name: 'gems-materials-pagedoo-cms',
      alias: 'pagedoo-cms',
    };
  }
  if (materialsName === 'pagedoo-shop') {
    console.error('请不要使用此素材库 pagedoo-shop');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-shop/manifest.json',
      name: 'gems-materials-pagedoo-shop',
      alias: 'pagedoo-shop',
    };
  }
  if (materialsName === 'pagedoo-activity') {
    console.error('请不要使用此素材库 pagedoo-activity');
    return {
      meta: 'https://dev-pagedoo.pay.qq.com/cdn/materials/pagedoo-activity/manifest.json',
      name: 'gems-materials-pagedoo-activity',
      alias: 'pagedoo-activity',
    };
  }
};
export const useEditorMaterialMatch = () => {
  useEffect(() => {
    let fn: (() => void) | void = void 0;
    console.log(111, loader);
    editorHandler.addLoader(loader).then((f) => (fn = f));
    return fn;
  }, []);
};
