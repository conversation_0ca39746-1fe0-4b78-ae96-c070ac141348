import { useMount } from 'ahooks';
import { useMemo, useState } from 'react';
import { Button, Dialog, DialogPlugin } from 'tdesign-react';

export const useBrowserSupport = (options: { onConfirm: () => void }) => {
  const [support, setSupport] = useState(false);

  useMemo(() => {
    const browserMap = {
      chromeVersion: /Chrome\/([0-9.]+)/
        .exec(navigator.userAgent)?.[1]
        ?.split('.')?.[0],
    };

    let valid = false;
    if (
      browserMap.chromeVersion &&
      parseInt(browserMap.chromeVersion, 10) >= 80
    ) {
      valid = true;
    }
    setSupport(valid);
  }, []);

  useMount(() => {
    if (support) return;

    DialogPlugin.alert({
      body: '暂不支持当前浏览器，请使用chrome/QQ浏览器/edge浏览器编辑内容',
      closeBtn: null,
      confirmBtn: (
        <div className="pagedoo-meta-live-global">
          <Button
            className="gradient-primary"
            size="medium"
            onClick={() => {
              options.onConfirm();
            }}
          >
            确认
          </Button>
        </div>
      ),
    });
  });

  return {
    support,
  };
};
