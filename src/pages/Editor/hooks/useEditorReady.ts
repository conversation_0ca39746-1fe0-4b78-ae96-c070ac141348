import { useEffect, useState } from 'react';
import { handler as editor<PERSON><PERSON><PERSON> } from '@tencent/pagedoo-editor';
import { useRecoilValue } from 'recoil';
import { TimeAxisSharedAtom } from '@/pages/Editor/MetaHumanLive/TimeAxis/store';

// 等待编辑器和时间轨ready hook
export const useEditorReady = () => {
  const [ready, setReady] = useState(false);
  const sharedState = useRecoilValue(TimeAxisSharedAtom);

  useEffect(() => {
    if (!sharedState.componentLoaded) return;
    // 等待 play-script ready
    let timeout: NodeJS.Timeout;
    const wait = async () => {
      const playScript = await editorHandler.getGlobalData(
        'pagedoo-play-script'
      );
      const hasContent =
        (await editorHandler.getPageContent())?.components?.length > 0;
      console.log('hasContent ', hasContent);
      if (!playScript || !hasContent) {
        timeout = setTimeout(wait, 200);
        return;
      }
      setReady(true);
    };
    wait();
    return () => {
      clearTimeout(timeout);
    };
  }, [sharedState.componentLoaded]);
  return {
    ready,
  };
};
