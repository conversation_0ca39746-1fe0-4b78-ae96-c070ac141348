import { useEffect } from 'react';
import { EditorConfigMap, EditorSystemMap } from '@/pages/Editor/editorConfig';
import { sleep } from '@tencent/pagedoo-editor';
import { getParams } from '@/utils/url';

/**
 * <AUTHOR>
 * @date 2022/12/15 11:37
 * @desc gems编辑器会修改title，这里监听title变化，如果不是预期的title，就恢复
 */

export const useObserveTitle = () => {
  useEffect(() => {
    const titleNode = document.querySelector('title');
    const system = getParams().system as EditorSystemMap;

    const obv = new MutationObserver(async () => {
      const title = EditorConfigMap[system]?.documentTitle || '创意编辑器';
      if (!titleNode) return;
      if (titleNode && titleNode.innerHTML !== title) {
        await sleep(20);
        titleNode.innerHTML = title;
      }
    });
    obv.observe(titleNode as Node, {
      subtree: true,
      characterData: true,
      childList: true,
    });
    return () => {
      obv.disconnect();
    };
  }, []);
};
