import { UPLOAD_CODE } from '@/configs/upload';
import { LoginContextType } from '@/hooks/login';
import { ILoginSession } from '@/model/login';
import { uploadRequest } from '@/utils/cos';
import { getMonitor } from '@/utils/monitor';
import to from 'await-to-js';
import type COS from 'cos-js-sdk-v5';
import omit from 'lodash-es/omit';

export const createPagedooApi = (options: {
  loginApi: LoginContextType | undefined;
  loginState: ILoginSession | undefined;
  monitorInstance: ReturnType<typeof getMonitor>;
  /**
   * 扩展api
   */
  extends?: Partial<typeof window.__pagedoo_api>;
}) => {
  const { loginApi, loginState, monitorInstance } = options;
  const api: typeof window.__pagedoo_api = {
    reportLog: async (level, data) => {
      monitorInstance?.log(level, data);
    },
    uploadFile: async (type, file, options) => {
      const result = await uploadRequest(
        type,
        file,
        0,
        (taskId, cos: COS) => {
          options?.onReady?.();
          /** */
          // const onabort = () => {
          //   options?.abortSignal?.removeEventListener('abort', onabort);
          //   cos.cancelTask(taskId);
          // };
          // options?.abortSignal?.addEventListener('abort', onabort);
        },
        omit(options || {}, 'onReady')
      );
      let code = -1;
      if (result.code !== UPLOAD_CODE.SUCCESS) {
        code = 0;
        throw new Error('文件上传失败');
      }
      return {
        key: result.key,
        remoteUri: result.url,
        resultCode: `${code}`,
      };
    },
    getLogin: async (options) => {
      let needLogin = !!options?.force;
      if (!loginState) needLogin = true;
      if (needLogin && loginApi) {
        // 调用方在明确获取到当前登录态失效后，传入force进行强制登录
        if (loginApi) {
          await loginApi.goToLogin();
          const result = await loginApi.fetchLoginInfo();
          return {
            stringifyLoginInfo: result?.loginSession.strinifyLoginInfo || '',
            userId: result?.loginSession.openid || '',
          };
        }
      }
      return {
        stringifyLoginInfo: loginState?.strinifyLoginInfo || '',
        userId: loginState?.openid || '',
      };
    },
    getLoginSync: () => {
      return {
        stringifyLoginInfo: loginState?.strinifyLoginInfo || '',
        userId: loginState?.openid || '',
      };
    },
    openPage: async (page: string) => {
      const routeMap: Record<string, string> = {
        virtualImage: '#/virtual-image',
        voiceList: '#/voice-list',
      } as const;
      const targetRoute = routeMap[page];
      if (targetRoute) {
        window.open(targetRoute);
      }
    },
    ...options.extends,
  };

  window.__pagedoo_api = {
    ...window.__pagedoo_api,
    ...api,
  };
  type Methods = keyof typeof api;
  interface RemoteReq {
    key: string;
    method: Methods;
    args: any[];
  }
  interface RemoteRes {
    key: string;
    method: Methods;
    data: any;
    err?: Error;
  }
  const handleDebugMessage = async (ev: MessageEvent<RemoteReq>) => {
    console.log('接收到消息 ', ev);
    // if (ev.source !== opener) return;
    const { data } = ev;
    if (!api?.[data.method]) return;
    const [err, result] = await to(
      (api[data.method] as (...args: any[]) => Promise<any>)(...data.args)
    );
    const res: RemoteRes = {
      key: data.key,
      method: data.method,
      data: result,
      err: err || void 0,
    };
    opener.postMessage(res, '*');
  };
  let opener: Window;
  if (import.meta.env.VITE_ENV === 'dev') {
    window.__material_debug = {
      openLocalEditor: (url: string) => {
        window.addEventListener('message', handleDebugMessage);
        opener = window.open(url) as Window;
      },
    };
  }
  return () => {
    if (import.meta.env.VITE_ENV === 'dev') {
      window.removeEventListener('message', handleDebugMessage);
      opener?.close();
    }
  };
};
