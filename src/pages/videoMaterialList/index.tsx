import React, { useEffect, useMemo, useState } from 'react';
import { Tabs } from 'tdesign-react';
import './index.less';
import { MainContent, Page } from '@/components/Layout';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { TAB_LIST, videoMaterialListConfigMap } from './config';
import List from './list';

const { TabPanel } = Tabs;

export default function VideoMaterialList() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const tab_type = useMemo(() => searchParams.get('tab-type'), [searchParams]);
  const [value, setValue] = useState(tab_type || undefined);
  useEffect(() => {
    if (tab_type) {
      setValue(tab_type);
    }
  }, [tab_type]);
  const onChange = (key: string) => {
    setValue(key);
    searchParams.set('tab-type', key);
    const newSearch = searchParams.toString();
    navigate(
      { search: newSearch },
      {
        replace: true,
      }
    );
  };
  return (
    <Page title="视频">
      <MainContent className="videoMaterialList-mainContent">
        <Tabs onChange={(v) => onChange(v as string)} value={value}>
          {TAB_LIST.map((item) => {
            return (
              <TabPanel
                value={item}
                label={videoMaterialListConfigMap[item].label}
                key={item}
              >
                <List {...videoMaterialListConfigMap[item]} />
              </TabPanel>
            );
          })}
        </Tabs>
      </MainContent>
    </Page>
  );
}
