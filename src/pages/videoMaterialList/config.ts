import { IMaterialVideoForm } from '@/pages/videoMaterialList/type';
import { FileItemType } from '@/components/CustomUpload/type';
import { createTypeEnum } from '@/pages/videoMaterialList/create/videoHighlightsForm/utils';

export enum VideoMaterialType {
  /**
   * 视频切片
   */
  HIGHLIGHT_CLIP = '202',
  /**
   *  视频集锦
   */
  HIGHLIGHTS = '203',
  /**
   * 用户上传
   */
  USER_UPLOAD = '201',
}

export enum VideoMaterialJobType {
  HIGHLIGHT_CLIP_JOB = '101',
  HIGHLIGHTS_JOB = '102',
  USER_UPLOAD_JOB = '',
}

export enum VideoMaterialJobStatus {
  ALL = 0, // 全部
  SUCCESS = 1, // 成功
  PENDDING = 2, // 进行中
  INVALID = -1, // 无效
  manual_stop = 3, // 手动停止
  ERROR = 4, // 失败
}

export const VideoMaterialTypeToJobTypeMap = {
  [VideoMaterialType.HIGHLIGHT_CLIP]: VideoMaterialJobType.HIGHLIGHT_CLIP_JOB,
  [VideoMaterialType.HIGHLIGHTS]: VideoMaterialJobType.HIGHLIGHTS_JOB,
  [VideoMaterialType.USER_UPLOAD]: VideoMaterialJobType.USER_UPLOAD_JOB,
};

export const VideoType2Label = {
  [VideoMaterialType.HIGHLIGHT_CLIP]: '视频切片',
  [VideoMaterialType.HIGHLIGHTS]: '视频集锦',
  [VideoMaterialType.USER_UPLOAD]: '用户上传',
};

export const TAB_LIST = [
  VideoMaterialType.HIGHLIGHT_CLIP,
  VideoMaterialType.HIGHLIGHTS,
  VideoMaterialType.USER_UPLOAD,
];

export interface IVideoMaterialConfigItem<T> {
  type: T;
  label: string;
  createBtn: {
    jumpTo?: string;
    btnName: string | React.ReactNode;
  };
  pageTitle: string;
}

export const videoMaterialListConfigMap: {
  [key in VideoMaterialType]: IVideoMaterialConfigItem<key>;
} = {
  [VideoMaterialType.HIGHLIGHT_CLIP]: {
    type: VideoMaterialType.HIGHLIGHT_CLIP,
    label: VideoType2Label[VideoMaterialType.HIGHLIGHT_CLIP],
    pageTitle: '视频切片生成',
    createBtn: {
      btnName: '智能生成',
      jumpTo: `/videoMaterial-create?from=${VideoMaterialType.HIGHLIGHT_CLIP}`,
    },
  },
  [VideoMaterialType.HIGHLIGHTS]: {
    type: VideoMaterialType.HIGHLIGHTS,
    label: VideoType2Label[VideoMaterialType.HIGHLIGHTS],
    pageTitle: '视频集锦生成',
    createBtn: {
      btnName: '智能生成',
      jumpTo: `/videoMaterial-create?from=${VideoMaterialType.HIGHLIGHTS}`,
    },
  },
  [VideoMaterialType.USER_UPLOAD]: {
    type: VideoMaterialType.USER_UPLOAD,
    label: VideoType2Label[VideoMaterialType.USER_UPLOAD],
    pageTitle: '新增用户上传',
    createBtn: {
      btnName: '新增上传',
      jumpTo: `/videoMaterial-create?from=${VideoMaterialType.USER_UPLOAD}`,
    },
  },
};

export const MaterialVideoFormDefault: IMaterialVideoForm = {
  video_highlights: {
    job_name: '',
    video_duration: 60,
    video_id_list: [],
    video_slice_job_id: '',
    createType: createTypeEnum.SMART_HIGHLIGHTS,
  },
  video_clip: {
    job_name: '',
    video: [],
  },
  user_upload: {
    list: [],
  },
};
