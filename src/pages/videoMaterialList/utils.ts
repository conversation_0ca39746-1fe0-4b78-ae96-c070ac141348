import dayjs from 'dayjs';
import { videoMaterialListConfigMap, VideoMaterialType } from './config';
import { VideoType } from './type';
import moment, { DurationInputArg2 } from 'moment';

export const getBreadcrumbName = (fromParam: VideoMaterialType) => {
  return videoMaterialListConfigMap[fromParam]?.pageTitle;
};

export const genResultPath = ({
  type,
  id,
  name,
}: {
  type: VideoType;
  id: string;
  name: string;
}) => {
  return `/videoMaterial-result?type=${type}&vid=${id}&name=${name}`;
};

export const dealTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// 处理时间-> 时分秒
export const handleDuration = (
  time: string | number,
  unit: DurationInputArg2 = 'seconds'
) => {
  const times = moment.duration(time, unit);
  const hours = times.hours();
  const minutes = times.minutes();
  const seconds = times.seconds();
  return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss');
};
