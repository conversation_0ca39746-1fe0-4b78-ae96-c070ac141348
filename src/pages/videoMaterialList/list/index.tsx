import React, { useEffect } from 'react';
import './index.less';
import { Button, Input } from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { IVideoMaterialConfigItem, VideoMaterialType } from '../config';
import { ListTable } from './listTable';
import { useVideoMaterialListRequest } from '../hooks/useVideoMaterialListRequest';
import TaskDrawer from '../components/TaskDrawer';
import { useBoolean } from 'ahooks';
import { EnumToUnion } from '@/utils/template/type';
import { TaskButton } from '../components/TaskButton';

const classPrefix = 'videoMaterial-list-page';

export default function VideoMaterialListItem({
  type,
  createBtn,
}: IVideoMaterialConfigItem<EnumToUnion<typeof VideoMaterialType>>) {
  const navigate = useNavigate();
  const { state } = useLocation();
  const [taskDrawerVisible, { setTrue, setFalse }] = useBoolean(state?.isOpen);

  const {
    queryParams,
    setQueryParams,
    records,
    refresh,
    recordCount,
    loading,
  } = useVideoMaterialListRequest({
    queryParamsDefault: {
      searchKey: '',
      type,
      pageNum: 1,
      pageSize: 10,
    },
  });

  return (
    <div className={classPrefix}>
      <div className={`${classPrefix}-content`}>
        <div className={`${classPrefix}-content-inner-box`}>
          <div className="right">
            <div className="operate-box">
              <div className="flex flex-1 gap-3">
                <Button
                  theme="primary"
                  className="gradient-primary"
                  onClick={() => {
                    if (createBtn?.jumpTo) {
                      navigate(createBtn.jumpTo);
                    }
                  }}
                >
                  {createBtn?.btnName}
                </Button>
                {type !== VideoMaterialType.USER_UPLOAD && (
                  <TaskButton actionClick={setTrue} type={type} />
                )}
              </div>

              <div className="search-box" style={{ width: '296px' }}>
                <Input
                  placeholder="请输入你需要搜索的内容"
                  onEnter={(value) => {
                    setQueryParams({
                      ...queryParams,
                      searchKey: value,
                      pageNum: 1,
                    });
                  }}
                  suffixIcon={<SearchIcon />}
                />
              </div>
            </div>
            <div>
              <ListTable
                type={type}
                queryParams={queryParams}
                setQueryParams={setQueryParams}
                records={records}
                refresh={refresh}
                loading={loading}
                recordCount={recordCount}
              />
            </div>
          </div>
          <TaskDrawer
            visible={taskDrawerVisible}
            onClose={setFalse}
            type={type}
          />
        </div>
      </div>
    </div>
  );
}
