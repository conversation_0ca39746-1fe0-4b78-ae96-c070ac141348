import {
  Button,
  Dialog,
  DialogProps,
  Form,
  Input,
  MessagePlugin,
  Textarea,
} from 'tdesign-react';
import { IVideoMaterialListRecord } from '../hooks/useVideoMaterialListRequest';
import { isBoolean } from 'lodash-es';
import { ResourceSvr } from '@/pb/pb';
import { useEffect } from 'react';

export interface IUpdateVideoBaseInfoProps extends DialogProps {
  baseInfo?: IVideoMaterialListRecord;
  onCancel?: () => void;
  onOk?: () => void;
}
export default function UpdateVideoBaseInfo({
  visible,
  header = '视频基本信息',
  baseInfo,
  width = 498,
  onCancel,
  onOk,
  ...rest
}: IUpdateVideoBaseInfoProps) {
  const [form] = Form.useForm();
  const { FormItem } = Form;
  useEffect(() => {
    if (visible && baseInfo) {
      form.setFieldsValue({
        video_name: baseInfo.video_name || undefined,
        video_content: baseInfo.video_content || undefined,
      });
    }
  }, [visible, baseInfo, form]);
  const handleConfirm = async () => {
    const validateSuccess = await form.validate();
    if (isBoolean(validateSuccess) && validateSuccess) {
      const values = form.getFieldsValue(true);
      try {
        await ResourceSvr.UpdateUserVideoInfor({
          video_id: baseInfo?.video_id,
          ...values,
        });
        void MessagePlugin.success('编辑成功');
      } catch (error) {
        console.info(error);
        void MessagePlugin.error('编辑失败');
      } finally {
        onOk?.();
      }
    }
  };
  return (
    <Dialog
      visible={visible}
      header={header}
      width={width}
      onClose={() => onCancel?.()}
      closeOnOverlayClick={false}
      className="updateVideoBaseInfo-dialog"
      footer={
        <div className="pagedoo-meta-live-global">
          <Button
            className="gradient-default"
            onClick={() => onCancel?.()}
            style={{ width: 68 }}
          >
            取消
          </Button>
          <Button
            className="gradient-primary"
            onClick={() => handleConfirm()}
            style={{ width: 68 }}
          >
            保存
          </Button>
        </div>
      }
      {...rest}
    >
      <Form
        labelAlign="left"
        layout="vertical"
        preventSubmitDefault
        resetType="empty"
        showErrorMessage
        form={form}
      >
        <FormItem
          label="视频名称"
          name="video_name"
          requiredMark={false}
          rules={[{ required: true, message: '请输入视频名称' }]}
        >
          <Input placeholder="请输入视频名称" />
        </FormItem>
        <FormItem
          label="视频描述"
          name="video_content"
          requiredMark={false}
          rules={[{ required: true, message: '请输入视频描述' }]}
        >
          <Textarea
            placeholder="请输入视频描述"
            autosize={{ minRows: 5 }}
            maxlength={150}
          />
        </FormItem>
      </Form>
    </Dialog>
  );
}
