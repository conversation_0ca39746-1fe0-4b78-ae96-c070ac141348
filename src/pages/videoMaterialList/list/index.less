.videoMaterial-list-page {

  &-content {
    padding: 20px 16px;
    border-radius: 8px;
    background: #FFFFFF;
    height: calc(100vh - 165px);

    &-inner-box {
      display: flex;
      border: 1px solid #E3E5EB;
      border-radius: 4px;

      .left {
        width: 182px;
        height: 100%;
        border-right: 1px solid #E3E5EB;

        // .search-box {
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
        //   height: 54px;
        //   border-bottom: 1px solid #E3E5EB;

        //   .t-input {
        //     border: none;
        //     box-shadow: none;
        //   }
        // }
      }

      .right {
        flex: 1;

        .operate-box {
          width: 100%;
          height: 54px;
          display: flex;
          align-items: center;
          padding: 0 12px;
          border-bottom: 1px solid #E3E5EB;
        }

        // .search-box {
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
        //   height: 100%;
        //   border-left: 1px solid #E3E5EB;

        //   .t-input__wrap {
        //     height: 100%;
        //   }

        //   .t-input {
        //     height: 100%;
        //     border: none;
        //     box-shadow: none;
        //   }
        // }
      }
    }
  }

  .videoMaterial-record-comp {
    .t-table__header {
      tr>th {
        background-color: #f8f6fb;
      }
    }

    .t-table__content {
      min-height: calc(100vh - 345px);

      .video-url {
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.9);
        margin: 0px !important;
      }
    }

    .t-table th,
    .t-table td {
      vertical-align: middle;
    }
  }
}

.updateVideoBaseInfo-dialog {
  .t-dialog--default {
    padding: 0px;

    .t-dialog__header {
      padding: 20px;
      // border-bottom: 1px solid #e8e8e8;
    }

    .t-dialog__body {
      padding: 0px 20px 20px;
      padding-bottom: 40px;

      // border-bottom: 1px solid #e8e8e8;
      .t-textarea__info_wrapper_align {
        position: absolute;
        right: 0;
      }

      .t-form__label {
        width: 56px !important;
      }

      .t-form__controls {
        margin-left: 72px !important;
      }
    }

    .t-dialog__footer {
      padding: 0px 20px 20px;
    }
  }
}