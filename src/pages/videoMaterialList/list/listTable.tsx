import React, { useState } from 'react';
import {
  Link,
  MessagePlugin,
  Popconfirm,
  PrimaryTableCol,
  Space,
  Table,
} from 'tdesign-react';
import './index.less';
import {
  IVideoMaterialListRequestParams,
  IVideoMaterialListRecord,
} from '../hooks/useVideoMaterialListRequest';
import { VideoMaterialType } from '../config';
import UpdateVideoBaseInfo, {
  IUpdateVideoBaseInfoProps,
} from './updateVideoBaseInfo';
import PreviewVideo, { IPreviewVideoProps } from '../components/previewVideo';
import { downloadVideo } from '@/utils/download_video';
import { ResourceSvr } from '@/pb/pb';
import { dealTime, handleDuration } from '../utils';
import { BasicImageViewer } from '@/pages/List/components/image_viewer';
import { COS_CONFIG } from '@/configs/upload';

interface IListTableProps {
  queryParams: IVideoMaterialListRequestParams;
  setQueryParams: (params: IVideoMaterialListRequestParams) => void;
  records: IVideoMaterialListRecord[];
  recordCount: number;
  refresh: () => void;
  loading: boolean;
  type: VideoMaterialType;
}

export function ListTable({
  records,
  refresh,
  queryParams,
  setQueryParams,
  recordCount,
  loading,
  type,
}: IListTableProps) {
  const [updateVideoBaseInfoParams, setUpdateVideoBaseInfoParams] =
    useState<IUpdateVideoBaseInfoProps>();
  const [previewVideoParams, setPreviewVideoParams] =
    useState<IPreviewVideoProps>();
  const handleDelete = async (row: IVideoMaterialListRecord) => {
    const loading = await MessagePlugin.loading('删除中...');
    try {
      await ResourceSvr.DelUserVideoInfor({
        video_id: row.video_id,
      });
      void MessagePlugin.success('删除成功');
    } catch (error) {
      console.info(error);
      void MessagePlugin.error('删除失败');
    } finally {
      refresh();
      loading.close();
    }
  };

  const columns = (
    [
      {
        colKey: 'pic_url',
        title: '视频封面',
        width: 130,
        cell: ({ row }) => (
          <BasicImageViewer
            width={72}
            height={128}
            className="video-url"
            img={row.pic_url}
            name="视频封面"
          />
        ),
      },
      {
        colKey: 'video_name',
        title: '视频名称',
      },
      {
        colKey: 'video_content',
        title: '视频描述',
        align: 'center',
        width: 230,
        cell: ({ row }) => <>{row.video_content || '-'}</>,
      },
      {
        colKey: 'video_id',
        title: 'ID',
      },
      {
        colKey: 'job_name',
        title: '任务名称',
        width: 130,
        cell: ({ row }) => <>{row.job_name || '-'}</>,
      },
      {
        colKey: 'time',
        title: '时长｜大小',
        width: 130,
        cell: ({ row }) => (
          <>{`${handleDuration(row.video_duration)} ｜ ${row.video_size}`}</>
        ),
      },
      {
        colKey: 'user_id',
        title: '创建人｜创建时间',
        cell: ({ row }) => (
          <>
            <div className="name">{row.user_id}</div>
            <div className="time">
              {row.create_time ? dealTime(row.create_time) : '-'}
            </div>
          </>
        ),
      },
      {
        colKey: 'operate',
        fixed: 'right',
        width: 220,
        title: '操作',
        cell: ({ row }) => (
          <Space>
            <Link
              theme="primary"
              hover="color"
              onClick={() =>
                setPreviewVideoParams({
                  visible: true,
                  videoUrl: row.video_url,
                })
              }
            >
              预览
            </Link>
            <Link
              theme="primary"
              hover="color"
              onClick={() =>
                setUpdateVideoBaseInfoParams({ visible: true, baseInfo: row })
              }
            >
              编辑
            </Link>
            <Link
              theme="primary"
              hover="color"
              onClick={() => {
                const video_url = row.video_url.replace(
                  COS_CONFIG.cdnBaseUrl,
                  COS_CONFIG.cosBaseUrl
                );
                downloadVideo(video_url, row.video_name, '_self');
              }}
            >
              下载
            </Link>
            <Popconfirm
              content="确认删除吗"
              destroyOnClose
              placement="top"
              showArrow
              theme="default"
              onConfirm={() => {
                handleDelete(row).then();
              }}
            >
              <Link theme="danger" hover="color">
                删除
              </Link>
            </Popconfirm>
          </Space>
        ),
      },
    ] as PrimaryTableCol<IVideoMaterialListRecord>[]
  ).filter((item) => {
    if (type === VideoMaterialType.USER_UPLOAD && item.colKey) {
      return !['job_name'].includes(item.colKey);
    }
    return true;
  });
  return (
    <div className="videoMaterial-record-comp">
      <Table
        rowKey="video_id"
        columns={columns}
        data={records}
        loading={loading}
        maxHeight="calc(100vh - 345px)"
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: recordCount,
        }}
        onPageChange={(pageInfo) => {
          setQueryParams({
            ...queryParams,
            // 切换每页条数时，重置页码
            pageNum:
              queryParams.pageSize === pageInfo.pageSize ? pageInfo.current : 1,
            pageSize: pageInfo.pageSize,
          });
        }}
        size="medium"
        verticalAlign="middle"
      />
      {updateVideoBaseInfoParams?.visible && (
        <UpdateVideoBaseInfo
          {...updateVideoBaseInfoParams}
          onCancel={() =>
            setUpdateVideoBaseInfoParams({
              ...updateVideoBaseInfoParams,
              visible: false,
            })
          }
          onOk={() => {
            setUpdateVideoBaseInfoParams({
              ...updateVideoBaseInfoParams,
              visible: false,
            });
            refresh();
          }}
        />
      )}
      {previewVideoParams?.visible && (
        <PreviewVideo
          {...previewVideoParams}
          onCancel={() =>
            setPreviewVideoParams({
              ...previewVideoParams,
              visible: false,
            })
          }
        />
      )}
    </div>
  );
}
