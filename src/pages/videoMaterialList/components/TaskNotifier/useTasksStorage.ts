import { useLocalStorageState, useMemoizedFn } from 'ahooks';
import { includes, reject, uniq } from 'lodash-es';
import { VideoType } from '../../type';
import {
  EXPIRY_TIME,
  useExpiringLocalStorageArrayState,
} from '@/hooks/useExpiringLocalStorageStateArray';

export type LocalTaskType = {
  id: string;
  type: VideoType;
  /**
   * 记录是否提示
   */
  status?: 'finish';
};

export const useTasksStorage = () => {
  const [localTasks, setLocalTasks] =
    useExpiringLocalStorageArrayState<LocalTaskType>('tasks', {
      listenStorageChange: true,
      expiry: EXPIRY_TIME, // 设置过期时间为2小时
    });

  const addLocalTask = useMemoizedFn((task: LocalTaskType) => {
    setLocalTasks((prev = []) => {
      return uniq([...prev, task]);
    });
  });

  const deleteLocalTask = useMemoizedFn((ids: string[]) => {
    setLocalTasks((prev = []) => {
      return reject(prev, (task) => includes(ids, task.id));
    });
  });

  /**
   * 强制更新 tasks
   */
  const updateLocalTasks = useMemoizedFn((tasks: LocalTaskType[]) => {
    setLocalTasks(tasks);
  });

  const updateSingleTask = useMemoizedFn(
    (id: string, updates: Partial<LocalTaskType>) => {
      setLocalTasks((prevTasks = []) => {
        return prevTasks.map((task) =>
          task.id === id ? { ...task, ...updates } : task
        );
      });
    }
  );

  return {
    localTasks,
    addLocalTask,
    deleteLocalTask,
    updateLocalTasks,
    updateSingleTask,
  };
};
