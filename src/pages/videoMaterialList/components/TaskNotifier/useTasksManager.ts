import { useEffect, useMemo } from 'react';
import { useDebounceEffect, useDeepCompareEffect, useMemoizedFn } from 'ahooks';
import { TaskStatus } from '../TaskList/constants';
import { useNotifyStorage } from './useNotifyStorage';
import { useCancelTask } from '../../hooks/useCancelTask';
import { useTaskDetailByIdMap } from '../../hooks/useTaskDetailByIdMap';
import { useTasksStorage } from './useTasksStorage';
import { map } from 'lodash-es';
import { MessagePlugin } from 'tdesign-react';
import { useIntervalUpdate } from '@/hooks/useIntervalUpdate';
import {
  CLEAR_POLL_TIME,
  DEBOUNCE_TIME,
  NOTIFIER_POLL_TIME,
} from './constants';

export const useTasksManager = () => {
  const { run: fetchTasksMap, data: tasks, refresh } = useTaskDetailByIdMap();
  const { showNotification, closeNotification } = useNotifyStorage();
  const { localTasks, updateSingleTask } = useTasksStorage();

  // 只有没有提示过的节点需要判断
  const filterTasks = useMemo(() => {
    return localTasks?.filter((task) => task.status !== 'finish');
  }, [localTasks]);

  // 定时任务轮训
  const { updateInterval } = useIntervalUpdate(() => {
    console.log('!!!!!!!interval 定时器输出，来源于 TaskNotifier');

    if (showNotification && inProgressTasks.length) {
      refresh();
    }
  }, NOTIFIER_POLL_TIME);

  // 过滤出执行中的任务
  const inProgressTasks = useMemo(() => {
    if (!tasks?.length) {
      updateInterval(CLEAR_POLL_TIME);
      return [];
    }

    // 这里的 tasks 就是通过 local id 查出来的，不用二次过滤 local
    const progressTasks = tasks.filter(
      (task) => task.job_status === TaskStatus.MAKING
    );
    updateInterval(NOTIFIER_POLL_TIME);

    if (!progressTasks.length) {
      updateInterval(CLEAR_POLL_TIME);
    }
    return progressTasks;
  }, [tasks, updateInterval]);

  // 请求未完成的，完成的说明已经提示过了，完成了，延迟触发
  useDebounceEffect(
    () => {
      // 最后关闭的时候也需要执行一次
      if (filterTasks) {
        fetchTasksMap(map(filterTasks, (i) => i.id));
      }
    },
    [fetchTasksMap, filterTasks, showNotification],
    {
      wait: DEBOUNCE_TIME,
    }
  );

  // 轮训变化，实时 message 提示
  useDebounceEffect(
    () => {
      if (!filterTasks?.length || !tasks?.length) return;

      filterTasks.forEach((localTask) => {
        // 找到当前 id
        const matchingTask = tasks.find((i) => i.job_id === localTask.id);
        // 如果状态完成
        if (
          matchingTask &&
          (matchingTask.job_status === TaskStatus.SUCCESS ||
            matchingTask.job_status === TaskStatus.FAIL)
        ) {
          if (localTask.status !== 'finish') {
            // 提示任务完成
            console.log('!!!!notifier 提示的', matchingTask, localTask);
            if (matchingTask.job_status === TaskStatus.SUCCESS) {
              MessagePlugin.success(
                `任务 ${matchingTask.job_name} 已生成成功，请前往“任务列表”查看`
              );
            }
            if (matchingTask.job_status === TaskStatus.FAIL) {
              MessagePlugin.error(`任务 ${matchingTask.job_name} 执行失败`);
            }
            updateSingleTask(localTask.id, { status: 'finish' });
          }
        }
      });
    },
    [tasks, updateSingleTask, filterTasks],
    {
      wait: DEBOUNCE_TIME,
    }
  );

  const closeNotifier = useMemoizedFn(() => {
    closeNotification();
  });

  return {
    makingTasks: inProgressTasks,
    showNotification,
    closeNotifier,
    refresh,
  };
};

export const useTaskCancel = () => {
  const { runAsync: cancelTask } = useCancelTask();
  const { deleteLocalTask } = useTasksStorage();

  const cancelTaskReq = useMemoizedFn(async (id: string) => {
    await cancelTask(id);
    deleteLocalTask([id]);
  });

  return {
    cancelTaskReq,
  };
};
