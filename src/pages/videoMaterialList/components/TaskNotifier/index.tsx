import React from 'react';
import { Button, Loading, MessagePlugin } from 'tdesign-react';
import { useTaskCancel, useTasksManager } from './useTasksManager';
import { useMemoizedFn } from 'ahooks';
import CustomNotification from '@/components/CustomNotification';

function TaskNotifier() {
  const { makingTasks, showNotification, closeNotifier, refresh } =
    useTasksManager();
  const { cancelTaskReq } = useTaskCancel();

  const handleTaskClose = useMemoizedFn(() => {
    closeNotifier();
  });

  const cancelTask = useMemoizedFn(async (id: string, name: string) => {
    await cancelTaskReq(id);
    refresh();
    MessagePlugin.success(`取消任务${name}成功`);
  });

  return (
    <>
      <div
        x-if={showNotification && makingTasks.length}
        className="absolute right-[12px] bottom-[15px] z-[999] transition-all"
      >
        <CustomNotification
          icon={<Loading size="20px" />}
          onClose={handleTaskClose}
          title="任务生成中..."
          content={
            <div className="flex flex-col gap-[8px]">
              {makingTasks.map((task) => {
                return (
                  <div
                    key={task.job_id}
                    className="flex flex-row justify-between items-center h-[44px] text-[14px] py-[12px] pl-[10px]  bg-gradient-to-r from-[#F6F7FB] to-[#FBF8FB] rounded-[4px]"
                  >
                    <div className="text-[rgba(0,0,0,0.6)] flex flex-1 justify-between">
                      <span className="text-[rgba(0,0,0,0.9)]">
                        {task.job_name}
                      </span>
                      {/* 暂时不展示进度 */}
                      {/* <span>生成中...（{task.progress_percent || 9}%）</span> */}
                      <span className="mr-12">生成中...</span>
                    </div>

                    <Button
                      variant="text"
                      theme="primary"
                      className="h-[32px] pl-[4px]"
                      onClick={() => cancelTask(task.job_id, task.job_name)}
                    >
                      取消生成
                    </Button>
                  </div>
                );
              })}
            </div>
          }
        />
      </div>
    </>
  );
}

export default TaskNotifier;
