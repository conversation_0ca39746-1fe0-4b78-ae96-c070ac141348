import { useLocalStorageState, useMemoizedFn } from 'ahooks';
import { LocalTaskType, useTasksStorage } from './useTasksStorage';
import {
  ONE_DAY_IN_MS,
  useExpiringLocalStorageState,
} from '@/hooks/useExpiringLocalStorageState';

export const useNotifyStorage = () => {
  const [showNotification, setShowNotification] =
    useExpiringLocalStorageState<boolean>('showNotification', {
      defaultValue: false,
      listenStorageChange: true,
      expiry: ONE_DAY_IN_MS, // 设置过期时间为24小时
    });

  const { addLocalTask } = useTasksStorage();

  const closeNotification = useMemoizedFn(() => {
    setShowNotification(false);
  });

  const openNotification = useMemoizedFn((local: LocalTaskType) => {
    setShowNotification(true);
    addLocalTask(local);
  });

  return {
    showNotification,
    closeNotification,
    openNotification,
  };
};
