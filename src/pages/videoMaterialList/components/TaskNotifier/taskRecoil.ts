import { EnumToUnion } from '@/type';
import { atom, selector } from 'recoil';
import { TaskStatus } from '../TaskList/constants';

export interface Task {
  id: number;
  name: string;
  status: EnumToUnion<typeof TaskStatus>;
}

// 定义一个 atom 用于存储任务列表
export const taskListState = atom<Task[]>({
  key: 'taskListState',
  default: [],
});

// 定义一个 selector 来获取任务状态
export const taskStatusState = selector<EnumToUnion<typeof TaskStatus>>({
  key: 'taskStatusState',
  get: ({ get }) => {
    const tasks = get(taskListState);
    if (tasks.length === 0) return TaskStatus.SUCCESS;
    const hasInProgress = tasks.some(
      (task) => task.status === TaskStatus.MAKING
    );
    const allCompleted = tasks.every(
      (task) => task.status === TaskStatus.SUCCESS
    );

    if (hasInProgress) return TaskStatus.MAKING;
    if (allCompleted) return TaskStatus.SUCCESS;
    return TaskStatus.MAKING;
  },
});
