import React from 'react';
import { Drawer } from 'tdesign-react';
import TaskList from '../TaskList';
import { VideoType } from '../../type';

interface TaskDrawerProps {
  visible: boolean;
  onClose: () => void;
  type: VideoType;
}

function TaskDrawer({ visible, onClose, type }: TaskDrawerProps) {
  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      header="任务列表"
      placement="right"
      size="692px"
      footer={null}
      destroyOnClose
    >
      <TaskList type={type} />
    </Drawer>
  );
}

export default TaskDrawer;
