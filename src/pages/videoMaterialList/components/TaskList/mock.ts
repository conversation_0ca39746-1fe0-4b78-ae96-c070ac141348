import { uuid } from '@tencent/midas-util';
import { TaskStatus } from './constants';
import { RespType } from '@/pb/config';
import { ResourceSvr } from '@/pb/pb';

export type TaskType = (typeof tasksMock)[number];

export const tasksMock: RespType<
  typeof ResourceSvr.GetVideoHistoryJobList
>['history_job_infor_list'] = [
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.MAKING,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.SUCCESS,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 4,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.FAIL,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.MAKING,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.MAKING,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.MAKING,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.MAKING,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
  {
    job_id: uuid(),
    job_name: `任务名称AAA${Math.floor(Math.random() * 100)}`,
    job_status: TaskStatus.MAKING,
    progress_percent: 80,
    create_time: new Date().getTime() / 1000,
    video_segment_num: 0,
  },
];
