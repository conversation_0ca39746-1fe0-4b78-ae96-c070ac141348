import React, { Fragment, useMemo } from 'react';
import { <PERSON>ton, MessagePlugin } from 'tdesign-react';
import { StatusColorMap, TaskStatus, TaskStatusMap } from './constants';
import ThemeTag from '@/components/ThemeTag';
import { VideoType } from '../../type';
import { useMemoizedFn } from 'ahooks';
import { useNavigate } from 'react-router-dom';
import { dealTime, genResultPath } from '../../utils';
import { useTaskListScroll } from '../../hooks/useTaskList';
import { Loading } from '@/components/Loading';
import { EnumToUnion } from '@/type';
import { Empty } from '@/components/Empty';
import { ImplServer } from '@/pb/config';
import { VideoMaterialTypeToJobTypeMap } from '../../config';
import { useTaskCancel } from '../TaskNotifier/useTasksManager';
import { TipsIcon } from './TipsIcon';

const defaultTextCls = 'text-[12px] text-[#00000066]';

interface ITaskList {
  type: VideoType;
}

type TaskStatusType = EnumToUnion<typeof TaskStatus>;

export default function TaskList({ type }: ITaskList) {
  const navigate = useNavigate();

  const goToResult = useMemoizedFn((id, name) => {
    const path = genResultPath({ type, id, name });
    navigate(path);
  });
  const { cancelTaskReq } = useTaskCancel();

  const {
    data: taskList,
    loading,
    containerRef,
    refresh,
  } = useTaskListScroll({ type });

  // const handleTest = useMemoizedFn(async (id) => {
  //   const data = {
  //     job_id: id,
  //     job_status: 1,
  //     fail_message_info: 'success',
  //     app_id: 'test-env',
  //     info: {
  //       type: VideoMaterialTypeToJobTypeMap[type],
  //     },
  //     metadata: {
  //       user_job_video_result_list: [
  //         {
  //           video_name: '视频切片4',
  //           pic_url:
  //             'https://dev-avatarcdn.pay.qq.com/material/88c30742da658eb2c44b3e269ea7aedb.png?imageMogr2/thumbnail/140x270',
  //           video_content: '此为切片4的内容',
  //           score: 50,
  //           video_url:
  //             'https://dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
  //           video_duration: '100',
  //           video_size: '10m',
  //         },
  //       ],
  //     },
  //   };
  //   await ImplServer(
  //     '/api/virtual_man_resource_manager.Manager/VideoMakeJobCallback',
  //     {},
  //     data,
  //     false
  //   );
  //   await refresh();
  // });
  const handleCancel = useMemoizedFn(async (id, name) => {
    await cancelTaskReq(id);
    MessagePlugin.success(`取消任务${name}成功`);
    await refresh();
  });

  return (
    <div className="overflow-scroll max-h-full" ref={containerRef}>
      {/* 内容 */}
      <div className="flex flex-col gap-[12px] transition-all">
        <Fragment x-if={taskList?.length}>
          {taskList!.map((task) => (
            <div
              key={task.job_id}
              className="flex items-center justify-between h-[56px] py-[18px] pl-[12px] leading-[18px] bg-gradient-to-r from-[#F6F7FB] to-[#FBF8FB] rounded-[4px]"
            >
              <div className="flex items-center gap-[10px] flex-1">
                <span className="text-[16px] basis-[10em] shrink-0 font-medium text-[#000] line-clamp-2">
                  {task.job_name}
                </span>
                <ThemeTag
                  text={TaskStatusMap[task.job_status as TaskStatusType]}
                  textColor={
                    StatusColorMap[task.job_status as TaskStatusType]?.text
                  }
                  bgColor={
                    StatusColorMap[task.job_status as TaskStatusType]?.bg
                  }
                />
                {/* <div onClick={() => handleTest(task.job_id)}>点我完成任务</div> */}
                <span
                  // 'YYYY-MM-DD HH:mm:ss'加上空格共19个字符(即“19ch”)
                  className={`${defaultTextCls} w-[19ch]`}
                  x-if={task.job_status !== TaskStatus.MAKING}
                >
                  {dealTime(task.create_time)}
                </span>
                <Fragment x-if={task.job_status === TaskStatus.SUCCESS}>
                  <span className={defaultTextCls}>|</span>
                  <span className={defaultTextCls}>
                    生成 {task.video_segment_num} 个片段
                  </span>
                </Fragment>
                {/* <span
                  className={defaultTextCls}
                  x-if={task.job_status === TaskStatus.MAKING}
                >
                  {task?.progress_percent || '11'}%
                </span> */}
              </div>
              <div
                className="flex items-center"
                x-if={task.job_status === TaskStatus.FAIL}
              >
                <TipsIcon />
                <span className="text-[12px] text-[rgba(0,0,0,0.6)] ml-4 mr-16">
                  {task.fail_message_info || '未知报错'}
                </span>
              </div>
              <div className="flex items-center">
                <Button
                  variant="text"
                  theme="primary"
                  className="text-blue-500"
                  onClick={() => {
                    goToResult(task.job_id, task.job_name);
                  }}
                  x-if={task.job_status === TaskStatus.SUCCESS}
                >
                  查看详情
                </Button>
                <Button
                  variant="text"
                  theme="primary"
                  className="text-blue-500"
                  onClick={() => {
                    handleCancel(task.job_id, task.job_name);
                  }}
                  x-if={task.job_status === TaskStatus.MAKING}
                >
                  取消生成
                </Button>
              </div>
            </div>
          ))}
        </Fragment>
        <div
          className="w-full h-full flex justify-center items-center"
          x-if={loading}
        >
          <Loading />
        </div>
        <Fragment x-if={!taskList?.length && !loading}>
          <div className="flex-center h-full w-full">
            <Empty>暂无数据</Empty>
          </div>
        </Fragment>
      </div>
    </div>
  );
}
