.videoMaterialList-components-selectVideoMaterial {
  .t-radio-group {
    border-radius: 3px;
    background: var(--Gray-Gray5-normal, linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%));
    border: 1px solid rgba(232, 231, 239, 1);

    .t-radio-button {
      padding: 0px 57px;
    }
  }

  .t-radio-group.t-radio-group--filled .t-radio-button::before {
    width: 0;
    height: 0;
  }

  .t-radio-group.t-radio-group--filled .t-radio-button.t-is-checked {
    color: #0047F9;
  }

  .content {
    height: 480px;
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    align-items: flex-start;

    .videoMaterialItem {
      margin: 12px 12px 0px 0px;
      border-radius: 8px;
      background-color: #ffffff;
      cursor: pointer;
      position: relative;

      .videoMaterialItem-img {
        border-radius: 8px 8px 0px 0px;
        width: 100%;
        aspect-ratio: 9 / 16;
      }

      .itemContent {
        padding: 16px;
        background: linear-gradient(97.99deg, #EBF4FF 12.3%, #F8F8FF 99.99%);
        border-radius: 0px 0px 8px 8px;

        .name {
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: 400;
          line-height: 22.4px;
          color: rgba(0, 0, 0, 0.9);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          text-align: center;
        }
      }

      .t-checkbox {
        position: absolute;
        left: 10px;
        top: 10px;
      }
    }

    .active-videoMaterialItem {
      outline: 2px solid #0153FF;

      .itemContent {
        background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%);

        .name {
          color: #ffffff;
        }
      }
    }
    .active-videoMaterialItem-disabled {
      opacity: 0.32;
      outline: 2px solid #0153FF;
      cursor: not-allowed;
      .itemContent {
        background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%);

        .name {
          color: #ffffff;
        }
      }
    }

    .videoMaterialItem:nth-child(5n+5) {
      margin-right: 0px;
    }

    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .icon {
        width: 100px;
      }

      .title {
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-top: 36px;
        color: rgba(0, 0, 0, 0.9);
      }
      .desc {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.6);
        margin-top: 15px;
      }

      .t-link {
        margin-top: 15px;
      }
    }
  }

  .pagination {
    height: 56px;
    display: flex;
    align-items: center;
  }
}

.selectVideoMaterial-dialog {
  .t-dialog--default {
    padding: 0px;

    .t-dialog__header {
      padding: 20px;
      border-bottom: 1px solid #e8e8e8;
    }

    .t-dialog__body {
      padding: 20px;
      padding-bottom: 0px;
      border-bottom: 1px solid #e8e8e8;

      /* 分页 */
      .t-pagination .t-pagination__pager .t-pagination__number.t-is-current {
        background: linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
        border: none;
      }

      .t-pagination .t-pagination__jump {
        background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FCF8FB 99.98%);
      }
    }

    .t-dialog__footer {
      padding: 20px;
    }
  }

  .t-dialog__position {
    padding: 24px 0px;
  }
}