import { VideoMaterialType } from '../../config';
import emptyIcon from '@/assets/images/empty-icon.png';

export interface IselectVideoMaterialConfigItem {
  icon: string;
  title: string;
  desc?: string;
  jumpTo: {
    label: string;
    url: string;
  };
}

export const selectVideoMaterialConfigMap: {
  [key in VideoMaterialType]: IselectVideoMaterialConfigItem;
} = {
  [VideoMaterialType.HIGHLIGHT_CLIP]: {
    icon: emptyIcon,
    title: '暂无视频切片',
    desc: '视频切片是一段长视频内容中最具吸引力、最能代表主题的部分',
    jumpTo: {
      label: '前往智能生成',
      url: `/videoMaterial-create?from=${VideoMaterialType.HIGHLIGHT_CLIP}`,
    },
  },
  [VideoMaterialType.HIGHLIGHTS]: {
    icon: emptyIcon,
    title: '暂无视频集锦',
    desc: '视频集锦是从多个视频内容中精选而成的一系列精彩片段的集合',
    jumpTo: {
      label: '前往智能生成',
      url: `/videoMaterial-create?from=${VideoMaterialType.HIGHLIGHTS}`,
    },
  },
  [VideoMaterialType.USER_UPLOAD]: {
    icon: '',
    title: '暂无视频',
    jumpTo: {
      label: '前往上传',
      url: `/videoMaterial-create?from=${VideoMaterialType.USER_UPLOAD}`,
    },
  },
};

export const jumpToPage = (url: string) => {
  let envParam = '';
  if (import.meta.env.VITE_ENV === 'dev') {
    envParam = `?sandbox=2`;
  }

  window.open(`${envParam}#${url}`);
};
