import {
  Button,
  Checkbox,
  Dialog,
  DialogProps,
  Input,
  Link,
  Loading,
  Pagination,
  Radio,
  Space,
} from 'tdesign-react';
import {
  IVideoMaterialListRecord,
  useVideoMaterialListRequest,
} from '../../hooks/useVideoMaterialListRequest';
import {
  VideoMaterialType,
  TAB_LIST,
  videoMaterialListConfigMap,
} from '../../config';
import { SearchIcon } from 'tdesign-icons-react';
import './index.less';
import { useMemo, useState } from 'react';
import { useDeepCompareEffect } from 'ahooks';
import { jumpToPage, selectVideoMaterialConfigMap } from './utils';

export type SelectMode = 'checkBox' | 'radio'; // checkBox：多选 radio： 单选  默认多选
export interface ISelectVideoMaterialProps extends DialogProps {
  defaultData?: IVideoMaterialListRecord[];
  onCancel?: () => void;
  onOk?: (data: IVideoMaterialListRecord[]) => void;
  rowLens?: number;
  selectMode?: SelectMode;
  reSelectData?: IVideoMaterialListRecord; // 重新选择的数据
}
export default function SelectVideoMaterial({
  visible,
  header = '请选择视频素材',
  defaultData,
  width = 933,
  onCancel,
  onOk,
  rowLens = 5,
  placement = 'center',
  selectMode = 'checkBox',
  reSelectData,
  ...rest
}: ISelectVideoMaterialProps) {
  const [selectVideoMaterialData, setSelectVideoMaterialData] = useState<
    IVideoMaterialListRecord[]
  >([]);
  const [reSelectVideoMaterialData, setReSelectVideoMaterialData] =
    useState<IVideoMaterialListRecord>();
  useDeepCompareEffect(() => {
    setSelectVideoMaterialData(defaultData || []);
  }, [defaultData]);
  useDeepCompareEffect(() => {
    setReSelectVideoMaterialData(reSelectData);
  }, [reSelectData]);
  const { queryParams, setQueryParams, records, recordCount, loading } =
    useVideoMaterialListRequest({
      queryParamsDefault: {
        searchKey: '',
        type: TAB_LIST[0],
        pageNum: 1,
        pageSize: 10,
      },
    });
  const selectVideoMaterialConfig = useMemo(
    () => selectVideoMaterialConfigMap[queryParams.type],
    [queryParams.type]
  );
  const selectVideoMaterialFn = (data: IVideoMaterialListRecord) => {
    if (selectMode === 'checkBox') {
      const newSelectVideoMaterialData = [...selectVideoMaterialData];
      const findIndex = selectVideoMaterialData.findIndex(
        (findItem) => findItem.video_id === data.video_id
      );
      if (findIndex > -1) {
        newSelectVideoMaterialData.splice(findIndex, 1);
      } else {
        newSelectVideoMaterialData.push(data);
      }
      setSelectVideoMaterialData([...newSelectVideoMaterialData]);
    }
    if (selectMode === 'radio') {
      if (reSelectVideoMaterialData) {
        const filterData = selectVideoMaterialData.filter(
          (findItem) => findItem.video_id !== reSelectVideoMaterialData.video_id
        );
        if (
          filterData?.some((someItem) => someItem.video_id === data.video_id)
        ) {
          return;
        }
        const newSelectVideoMaterialData = [...selectVideoMaterialData];
        const findIndex = selectVideoMaterialData.findIndex(
          (findItem) => findItem.video_id === reSelectVideoMaterialData.video_id
        );
        newSelectVideoMaterialData[findIndex] = data;
        setSelectVideoMaterialData([...newSelectVideoMaterialData]);
        setReSelectVideoMaterialData(data);
      } else {
        const newSelectVideoMaterialData = [data];
        setSelectVideoMaterialData([...newSelectVideoMaterialData]);
      }
    }
  };
  const handleVideoMaterialItem = ({
    item,
  }: {
    item: IVideoMaterialListRecord;
  }) => {
    if (selectMode === 'checkBox') {
      return selectVideoMaterialData?.some(
        (someItem) => someItem.video_id === item.video_id
      )
        ? 'active-videoMaterialItem'
        : '';
    }
    if (selectMode === 'radio') {
      if (reSelectVideoMaterialData) {
        const filterData = selectVideoMaterialData.filter(
          (findItem) => findItem.video_id !== reSelectVideoMaterialData.video_id
        );
        if (
          filterData?.some((someItem) => someItem.video_id === item.video_id)
        ) {
          return 'active-videoMaterialItem-disabled';
        }
        if (reSelectVideoMaterialData.video_id === item.video_id) {
          return 'active-videoMaterialItem';
        }
      } else {
        return selectVideoMaterialData?.some(
          (someItem) => someItem.video_id === item.video_id
        )
          ? 'active-videoMaterialItem'
          : '';
      }
    }
  };
  return (
    <Dialog
      visible={visible}
      header={header}
      width={width}
      onClose={() => onCancel?.()}
      closeOnOverlayClick={false}
      placement={placement}
      className="selectVideoMaterial-dialog"
      footer={
        <div className="pagedoo-meta-live-global">
          <Button
            className="gradient-default"
            theme="default"
            onClick={() => onCancel?.()}
            style={{ width: 88 }}
          >
            取消
          </Button>
          <Button
            className="gradient-primary"
            theme="primary"
            onClick={() => onOk?.(selectVideoMaterialData)}
            disabled={!selectVideoMaterialData.length}
            style={{ width: 105 }}
          >
            确定添加
          </Button>
        </div>
      }
      {...rest}
    >
      <div className="videoMaterialList-components-selectVideoMaterial">
        <div className="flex items-center gap-2 justify-between mb-4">
          <Radio.Group
            variant="default-filled"
            value={queryParams.type}
            onChange={(v) => {
              setQueryParams({
                ...queryParams,
                type: v as VideoMaterialType,
                pageNum: 1,
              });
            }}
          >
            {TAB_LIST.map((item) => (
              <Radio.Button key={item} value={item}>
                {videoMaterialListConfigMap[item]?.label}
              </Radio.Button>
            ))}
          </Radio.Group>
          <Input
            onChange={(v) => {
              setQueryParams({
                ...queryParams,
                searchKey: v,
                pageNum: 1,
              });
            }}
            placeholder="请输入你需要搜索的内容"
            className="flex-1 max-w-[356px]"
            suffixIcon={<SearchIcon />}
          />
        </div>
        <div className="content">
          {loading ? (
            <Space className="flex-center h-full flex-1">
              <Loading text="正在获取视频素材..." loading size="small" />
            </Space>
          ) : (
            <>
              {Array.isArray(records) && !!records.length ? (
                records.map((item) => (
                  <div
                    className={`videoMaterialItem ${handleVideoMaterialItem({
                      item,
                    })}`}
                    style={{ width: `calc(${100 / rowLens}% - 9.6px)` }}
                    key={item.video_id}
                    onClick={() => selectVideoMaterialFn(item)}
                  >
                    <div
                      className="videoMaterialItem-img"
                      style={{
                        background: `url(${item.pic_url})`,
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'contain',
                        backgroundPosition: 'center',
                        backgroundColor: '#000000',
                      }}
                    />
                    <div className="itemContent">
                      <div className="name">{item.video_name}</div>
                    </div>
                    {selectMode === 'checkBox' && (
                      <Checkbox
                        value={item.video_id}
                        onClick={(context) => {
                          context?.e.stopPropagation();
                          selectVideoMaterialFn(item);
                        }}
                        checked={selectVideoMaterialData?.some(
                          (someItem) => someItem.video_id === item.video_id
                        )}
                      />
                    )}
                  </div>
                ))
              ) : (
                <div className="empty">
                  {selectVideoMaterialConfig.icon && (
                    <img
                      src={selectVideoMaterialConfig.icon}
                      alt=""
                      className="icon"
                    />
                  )}
                  <div className="title">{selectVideoMaterialConfig.title}</div>
                  {selectVideoMaterialConfig.desc && (
                    <div className="desc">{selectVideoMaterialConfig.desc}</div>
                  )}
                  <Link
                    theme="primary"
                    hover="color"
                    onClick={() =>
                      jumpToPage(selectVideoMaterialConfig.jumpTo?.url)
                    }
                  >
                    {selectVideoMaterialConfig.jumpTo?.label}
                  </Link>
                </div>
              )}
            </>
          )}
        </div>
        <div className="pagination">
          <Pagination
            showPageNumber
            showPageSize={false}
            showPreviousAndNextBtn
            size="small"
            theme="default"
            total={recordCount}
            totalContent
            onCurrentChange={(current: number) => {
              setQueryParams({
                ...queryParams,
                pageNum: current,
              });
            }}
          />
        </div>
      </div>
    </Dialog>
  );
}
