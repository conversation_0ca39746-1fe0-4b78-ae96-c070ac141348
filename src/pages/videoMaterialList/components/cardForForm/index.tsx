import { MainContent } from '@/components/Layout';
import { InfoCircleIcon } from 'tdesign-icons-react';
import classnames from 'classnames';

export interface ICardForFormProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  desc?: string;
}

export default function CardForForm({
  children,
  className,
  title,
  desc,
}: ICardForFormProps) {
  return (
    <MainContent className={classnames('mb-[16px]', className)}>
      {title && (
        <div
          className="font-medium text-[16px] text-lg text-[rgba(0,0,0,0.90)]"
          style={{ marginBottom: desc ? 4 : 12 }}
        >
          {title}
        </div>
      )}
      {desc && (
        <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[12px]">
          <InfoCircleIcon className="mr-[5px]" />
          <span>{desc}</span>
        </div>
      )}
      {children}
    </MainContent>
  );
}
