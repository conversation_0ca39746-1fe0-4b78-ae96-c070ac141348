import { Dialog, DialogProps } from 'tdesign-react';
import BaseVideo from './video';

export interface IPreviewVideoProps extends DialogProps {
  videoUrl: string;
  pic_url?: string;
  onCancel?: () => void;
}
export default function PreviewVideo({
  visible,
  header = '查看视频',
  width = 860,
  pic_url,
  videoUrl,
  onCancel,
  ...rest
}: IPreviewVideoProps) {
  return (
    <Dialog
      visible={visible}
      header={header}
      width={width}
      onClose={() => onCancel?.()}
      closeOnOverlayClick={false}
      // 全局预览样式 global.less
      className="previewVideo-dialog"
      footer={null}
      {...rest}
    >
      <BaseVideo src={videoUrl} poster={pic_url || ''} />
    </Dialog>
  );
}
