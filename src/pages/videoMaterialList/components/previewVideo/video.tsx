import { Interpolation, Theme } from '@emotion/react';
import classnames from 'classnames';

export type IBaseVideoProps = React.ClassAttributes<HTMLVideoElement> &
  React.VideoHTMLAttributes<HTMLVideoElement> & {
    css?: Interpolation<Theme>;
  };
export default function BaseVideo({
  src,
  autoPlay = true,
  controlsList = 'nodownload noremoteplayback noplaybackrate nofullscreen',
  controls = true,
  disablePictureInPicture = true,
  disableRemotePlayback = true,
  poster,
  className,
  ...rest
}: IBaseVideoProps) {
  return (
    <video
      src={src}
      className={classnames('video', className)}
      controls={controls}
      autoPlay={autoPlay}
      controlsList={controlsList}
      poster={poster}
      disablePictureInPicture={disablePictureInPicture}
      disableRemotePlayback={disableRemotePlayback}
      {...rest}
    />
  );
}
