import { useState } from 'react';
import { IVideoMaterialListRecord } from '../../hooks/useVideoMaterialListRequest';
import SelectVideoMaterial, {
  ISelectVideoMaterialProps,
} from '../selectVideoMaterial';
import './index.less';
import { useDeepCompareEffect } from 'ahooks';

export interface SelectVideoProps {
  value?: Array<string>;
  onChange?: (value: Array<string>) => void;
  cdData?: (value: Array<IVideoMaterialListRecord>) => void; // 暂时用于计算视频总时长
}
export default function SelectVideo({ onChange, cdData }: SelectVideoProps) {
  const [selectVideoList, setSelectVideoList] = useState<
    Array<IVideoMaterialListRecord>
  >([]);
  const [selectVideoMaterialParams, setSelectVideoMaterialParams] =
    useState<ISelectVideoMaterialProps>();
  useDeepCompareEffect(() => {
    cdData?.(selectVideoList);
    onChange?.(selectVideoList.map((item) => item.video_id));
  }, [selectVideoList]);
  return (
    <div className="videoMaterialList-components-selectVideo">
      <div className="add-contanier">
        <div
          className="left"
          onClick={() =>
            setSelectVideoMaterialParams({
              visible: true,
              defaultData: selectVideoList,
            })
          }
        >
          <div className="addIcon" />
          <div className="add-title">添加视频</div>
        </div>
        <div className="right">
          <div className="desc">1. 格式支持：MP4、MOV等；</div>
          <div className="desc">2. 基于对于视频的画面和语音识别高光片段；</div>
        </div>
      </div>
      {Array.isArray(selectVideoList) &&
        selectVideoList.map((item) => (
          <div className="videoItem">
            <div className="video_poster-wrap">
              <img src={item.pic_url} alt="" className="video_poster" />
            </div>
            <div
              className="closeImg"
              onClick={() => {
                const newSelectVideoList = [...selectVideoList];
                const findIndex = newSelectVideoList.findIndex(
                  (findItem) => findItem.video_id === item.video_id
                );
                newSelectVideoList.splice(findIndex, 1);
                setSelectVideoList([...newSelectVideoList]);
              }}
            />
          </div>
        ))}
      {selectVideoMaterialParams?.visible && (
        <SelectVideoMaterial
          {...selectVideoMaterialParams}
          onCancel={() =>
            setSelectVideoMaterialParams({
              ...selectVideoMaterialParams,
              visible: false,
            })
          }
          onOk={(data) => {
            setSelectVideoList(data);
            setSelectVideoMaterialParams({
              ...selectVideoMaterialParams,
              visible: false,
            });
          }}
        />
      )}
    </div>
  );
}
