.videoMaterialList-components-selectVideo {
  display: flex;
  flex-wrap: wrap;

  .add-contanier {
    width: 350px;
    height: 88px;
    background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    margin-right: 12px;

    .left {
      width: 64px;
      height: 64px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: rgba(0, 71, 249, 1);
      border: 1px dashed #ece6ed;
      background-color: #ffffff;
      cursor: pointer;

      .addIcon {
        width: 16px;
        height: 16px;
        margin-bottom: 6px;
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.4 7.4H8.6V2.6C8.6 2.2688 8.3312 2 8 2C7.6688 2 7.4 2.2688 7.4 2.6V7.4H2.6C2.2688 7.4 2 7.6688 2 8C2 8.3312 2.2688 8.6 2.6 8.6H7.4V13.4C7.4 13.7318 7.6688 14 8 14C8.3312 14 8.6 13.7318 8.6 13.4V8.6H13.4C13.7318 8.6 14 8.3312 14 8C14 7.6688 13.7318 7.4 13.4 7.4Z" fill="url(%23paint0_linear_1520_19924)" style=""/><defs><linearGradient id="paint0_linear_1520_19924" x1="2" y1="14" x2="13.8284" y2="15.4248" gradientUnits="userSpaceOnUse"><stop stop-color="%230047F9" style="stop-color:%230047F9;stop-color:color(display-p3 0.0000 0.2784 0.9765);stop-opacity:1;"/><stop offset="1" stop-color="%237386FF" style="stop-color:%237386FF;stop-color:color(display-p3 0.4507 0.5258 1.0000);stop-opacity:1;"/></linearGradient></defs></svg>');
      }
    }

    .right {
      line-height: 20px;
      color: rgba(0, 0, 0, 0.4);

    }

  }

  .videoItem {
    width: 88px;
    height: 88px;
    border-radius: 4px;
    position: relative;
    margin-right: 12px;
    margin-bottom: 12px;
    background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
    display: flex;
    align-items: center;
    justify-content: center;
    .video_poster-wrap {
      width: 64px;
      height: 64px;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.9);
    }
    .video_poster {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      object-fit: contain;
    }

    .closeImg {
      width: 16px;
      height: 16px;
      position: absolute;
      top: 0;
      right: 0;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      background-image: url('data:image/svg+xml,<svg width="28" height="27" viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 0H24C26.2091 0 28 1.79086 28 4V27H4C1.79086 27 0 25.2091 0 23V0Z" fill="black" fill-opacity="0.9" style="fill:black;fill-opacity:0.9;"/><path d="M14 14.1504L17.2743 17.4247C17.592 17.7424 18.1071 17.7424 18.4248 17.4247V17.4247C18.7425 17.1071 18.7425 16.592 18.4248 16.2743L15.1505 13L18.4248 9.72569C18.7425 9.40801 18.7425 8.89294 18.4248 8.57525V8.57525C18.1071 8.25757 17.592 8.25757 17.2743 8.57525L14 11.8496L10.7257 8.57522C10.408 8.25753 9.89291 8.25753 9.57522 8.57522V8.57522C9.25754 8.8929 9.25754 9.40797 9.57522 9.72566L12.8496 13L9.57522 16.2743C9.25754 16.592 9.25754 17.1071 9.57522 17.4248V17.4248C9.89291 17.7425 10.408 17.7425 10.7257 17.4248L14 14.1504Z" fill="white" style="fill:white;fill-opacity:1;"/></svg>');
      cursor: pointer;
    }
  }
}