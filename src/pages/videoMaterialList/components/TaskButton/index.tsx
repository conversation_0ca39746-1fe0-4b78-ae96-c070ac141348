import { Badge, Button } from 'tdesign-react';
import chevronRight from '../../assets/chevron-right.png';
import { VideoType } from '../../type';
import useTaskStatus from './useTaskStatus';
import { Status2Text, StatusColorMap } from './constants';
import { TaskStatus } from '../TaskList/constants';

interface IProps {
  actionClick: () => void;
  type: VideoType;
}

export function TaskButton(props: IProps) {
  const { actionClick, type } = props;
  const { status, showBadge, closeButtonBadge } = useTaskStatus({
    type,
  });

  // console.log('!!!!statusTaskButton,组件渲染', status);

  return (
    <Badge
      style={{
        textWrap: 'nowrap',
        border: status
          ? `1px solid ${StatusColorMap[status].borderColor}`
          : undefined,
      }}
      offset={[35, -5]}
      color={status ? StatusColorMap[status].bg : ''}
      count={
        status ? (
          <span style={{ color: StatusColorMap[status].text }} x-if={showBadge}>
            {Status2Text[status]}
          </span>
        ) : null
      }
    >
      <Button
        className="gradient-default"
        theme="default"
        onClick={() => {
          if (showBadge && status === TaskStatus.SUCCESS) {
            closeButtonBadge();
          }
          actionClick();
        }}
      >
        {createTaskLabel('任务列表')}
      </Button>
    </Badge>
  );
}

const createTaskLabel = (label: string) => (
  <div className="flex items-center">
    {label}
    <img src={chevronRight} alt="" style={{ width: 16, marginLeft: 5 }} />
  </div>
);
