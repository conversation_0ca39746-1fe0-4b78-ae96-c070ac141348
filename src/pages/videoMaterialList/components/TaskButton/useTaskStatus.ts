import { useEffect, useMemo, useState } from 'react';
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks';
import { TaskStatus } from '../TaskList/constants';
import { useTasksStorage } from '../TaskNotifier/useTasksStorage';
import { EnumToUnion } from '@/utils/template/type';
import { filter, map, some } from 'lodash-es';
import { VideoType } from '../../type';
import { useTaskDetailByIdMap } from '../../hooks/useTaskDetailByIdMap';
import { MessagePlugin } from 'tdesign-react';
import { useIntervalUpdate } from '@/hooks/useIntervalUpdate';
import { BUTTON_POLL_TIME, CLEAR_POLL_TIME } from './constants';

interface ITaskStatus {
  type: VideoType;
}

const useTaskStatus = ({ type }: ITaskStatus) => {
  const { run: fetchTasksMap, data: tasks, refresh } = useTaskDetailByIdMap();

  const { localTasks, deleteLocalTask, updateSingleTask } = useTasksStorage();

  const currentTypeTasks = useMemo(() => {
    return filter(localTasks, (task) => task.type === type);
  }, [localTasks, type]);

  // 定义用于存储任务状态的变量，这个指的是统一状态
  const [status, setStatus] = useState<Exclude<
    EnumToUnion<typeof TaskStatus>,
    typeof TaskStatus.FAIL | typeof TaskStatus.CANCEL
  > | null>(null);

  const { updateInterval } = useIntervalUpdate(() => {
    console.log(
      '!!!!!!!interval 定时器输出，来源于 TaskButton,status:',
      status
    );

    // 只有任务状态是
    if (status === TaskStatus.MAKING) {
      refresh();
    }
  }, BUTTON_POLL_TIME);

  useEffect(() => {
    // 初次加载时获取任务状态
    console.log('useTaskStatus!!!!currentTypeTasks', currentTypeTasks);
    fetchTasksMap(currentTypeTasks?.map((task) => task.id) ?? []);
  }, [fetchTasksMap, currentTypeTasks]);

  // 更新本地任务状态
  useDeepCompareEffect(() => {
    if (!tasks?.length || !currentTypeTasks?.length) {
      updateInterval(CLEAR_POLL_TIME);
      return;
    }

    console.log('!!!useDeepCompareEffect useTaskStatus', localTasks, tasks);
    // 获取出本次执行的任务
    const filterTasks = tasks;

    // 判断任务状态
    const hasInProgress = filterTasks.some(
      (task) => task.job_status === TaskStatus.MAKING
    );
    /**
     * 完成包括 成功和失败
     */
    const allCompleted = filterTasks.every(
      (task) => task.job_status !== TaskStatus.MAKING
    );

    if (hasInProgress) {
      setStatus(TaskStatus.MAKING);
      updateInterval(BUTTON_POLL_TIME);
    } else if (allCompleted) {
      setStatus(TaskStatus.SUCCESS);
      updateInterval(CLEAR_POLL_TIME);
    }
  }, [tasks, currentTypeTasks]);

  const closeButtonBadge = useMemoizedFn(() => {
    if (status === TaskStatus.SUCCESS) {
      deleteLocalTask(map(currentTypeTasks, (i) => i.id));
      setStatus(null);
      updateInterval(CLEAR_POLL_TIME);
    }
  });

  // 更新 localstorage 提示状态
  useDeepCompareEffect(() => {
    if (!tasks?.length) return;
    currentTypeTasks.forEach((localTask) => {
      const matchingTask = tasks.find((i) => i.job_id === localTask.id);
      if (
        matchingTask &&
        (matchingTask.job_status === TaskStatus.SUCCESS ||
          matchingTask.job_status === TaskStatus.FAIL)
      ) {
        if (localTask.status !== 'finish') {
          // 提示任务完成
          console.log('!!!!notifier 提示的', matchingTask, localTask);
          if (matchingTask.job_status === TaskStatus.SUCCESS) {
            MessagePlugin.success(
              `任务 ${matchingTask.job_name} 已生成成功，请前往“任务列表”查看`
            );
          }
          if (matchingTask.job_status === TaskStatus.FAIL) {
            MessagePlugin.error(`任务 ${matchingTask.job_name} 执行失败`);
          }
          updateSingleTask(localTask.id, { status: 'finish' });
        }
      }
    });
  }, [currentTypeTasks, tasks, updateSingleTask]);

  return {
    showBadge: (currentTypeTasks?.length || 0) > 0,
    status,
    currentTypeTasks,
    refresh,
    closeButtonBadge,
  };
};

export default useTaskStatus;
