import { useMemoizedFn, useRequest } from 'ahooks';
import { VideoType } from '../type';
import {
  VideoMaterialJobStatus,
  VideoMaterialTypeToJobTypeMap,
} from '../config';
import { ResourceSvr } from '@/pb/pb';
import { useRef } from 'react';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { TaskStatus } from '../components/TaskList/constants';

type ITaskListRequest = {
  manual?: boolean;
  /**
   * 视频类型，内部自己转任务类型
   */
  type: VideoType;
  jobStatus?: VideoMaterialJobStatus;
};

export const useTaskList = ({
  manual = false,
  type,
  jobStatus = 0,
}: ITaskListRequest) => {
  return useRequest(
    async () => {
      const data = await ResourceSvr.GetVideoHistoryJobList({
        job_type: Number(VideoMaterialTypeToJobTypeMap[type]),
        job_status: jobStatus,
        page_num: 1,
        page_size: 999,
      });
      return data.history_job_infor_list;
    },
    {
      manual,
      debounceWait: 200,
      onError: (e) => {
        console.error('ERROR:', e);
      },
    }
  );
};

export const useTaskListScroll = ({ type }: ITaskListRequest) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchTaskList = useMemoizedFn(async (pageNum: number) => {
    const data = await ResourceSvr.GetVideoHistoryJobList({
      job_type: Number(VideoMaterialTypeToJobTypeMap[type]),
      page_num: pageNum,
      page_size: 20,
    });
    return data.history_job_infor_list;
  });

  const { data, loading, noMore, loadMore, refresh } = useInfiniteScroll(
    fetchTaskList,
    {
      containerRef,
    }
  );

  return {
    refresh,
    // 过滤不展示取消状态
    data: data.filter((item) => item.job_status !== TaskStatus.CANCEL),
    loading,
    noMore,
    loadMore,
    containerRef,
  };
};
