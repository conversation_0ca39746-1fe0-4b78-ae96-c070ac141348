import { ResourceSvr } from '@/pb/pb';
import { useRequest } from 'ahooks';

export const useTaskDetailByIdMap = () => {
  return useRequest(
    async (ids: string[]) => {
      if (!ids.length) return null;
      const data = await Promise.all(
        ids.map(async (id) => {
          const detail = await ResourceSvr.GetVideoJobDetail({ job_id: id });
          return detail.job_infor;
        })
      );
      return data;
    },
    {
      manual: true,
      onError: (err) => {
        console.error(err);
      },
    }
  );
};
