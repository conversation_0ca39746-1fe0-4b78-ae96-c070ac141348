import { useCallback, useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';
import { RespType } from '@/pb/config';
import { ResourceSvr } from '@/pb/pb';
import { VideoMaterialType } from '../config';

export interface IVideoMaterialListRequestParams {
  searchKey?: string;
  type: VideoMaterialType;
  // 分页参数
  pageSize?: number;
  pageNum?: number;
}

export type IVideoMaterialListRecord = RespType<
  typeof ResourceSvr.GetUserVideoInforList
>['user_video_object_list'][number];

export const useVideoMaterialListRequest = ({
  queryParamsDefault,
}: {
  queryParamsDefault: IVideoMaterialListRequestParams;
}) => {
  const [queryParams, setQueryParams] =
    useState<IVideoMaterialListRequestParams>(queryParamsDefault);
  const [records, setRecords] = useState<IVideoMaterialListRecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);
  const { runAsync, loading, error } = useRequest(
    () => {
      return ResourceSvr.GetUserVideoInforList({
        video_create_type: Number(queryParams.type),
        search_key: queryParams?.searchKey || '',
        page_num: queryParams?.pageNum || 0,
        page_size: queryParams?.pageSize || 10,
      });
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    // 请求
    runAsync()
      .then((res) => {
        setRecords(res.user_video_object_list || []);
        setRecordCount(res.query_count || 0);
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        setRecords([]);
        setRecordCount(0);
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
      });
  }, [runAsync, queryParams]);

  const refresh = useCallback(() => {
    setQueryParams({ ...queryParams });
  }, [queryParams]);

  return {
    records,
    recordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
    run: runAsync,
  };
};
