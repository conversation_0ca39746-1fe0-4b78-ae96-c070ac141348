import videoIcon from './assets/videoIcon.png';
import aiVideoIcon from './assets/aiVideoIcon.png';
export enum createTypeEnum {
  SMART_HIGHLIGHTS = 'smart_highlights', // 智能集锦
  CUSTOM_SELECTION = 'custom_select', // 自定义选择
}
export const createTypeList = [
  {
    selector_id: createTypeEnum.SMART_HIGHLIGHTS,
    selector_name: '智能集锦',
    selector_value: createTypeEnum.SMART_HIGHLIGHTS,
    selector_desc: '基于历史智能生成的视频切片，智能生成视频集锦',
    icon: aiVideoIcon,
  },
  {
    selector_id: createTypeEnum.CUSTOM_SELECTION,
    selector_name: '自定义选择',
    selector_value: createTypeEnum.CUSTOM_SELECTION,
    selector_desc: '基于库里已有的各种类型的视频素材，可以自定义选择',
    icon: videoIcon,
  },
];

export const createTypeConfig = {
  [createTypeEnum.SMART_HIGHLIGHTS]: {
    desc: '基于历史智能生成的视频切片，为您生成视频集锦',
  },
  [createTypeEnum.CUSTOM_SELECTION]: {
    desc: '基于选择的视频，为您生成视频集锦',
  },
};
