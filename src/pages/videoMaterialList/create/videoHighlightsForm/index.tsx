import CardForForm from '../../components/cardForForm';
import CardList from '@/pages/WorkbenchVideo/components/create/cardList';
import SelectVideo from '../../components/selectVideo';
import {
  Form,
  Input,
  InputNumber,
  InternalFormInstance,
  Select,
} from 'tdesign-react';
import { createTypeConfig, createTypeEnum, createTypeList } from './utils';
import { useState } from 'react';
import { useTaskList } from '../../hooks/useTaskList';
import { VideoMaterialJobStatus, VideoMaterialType } from '../../config';
import { IMaterialVideoForm } from '@/pages/videoMaterialList/type';
import { useTaskDetailDuration } from '@/pages/VideoTaskResult/hooks/useTaskDetail';
import { useMemoizedFn } from 'ahooks';
import { isString } from 'lodash-es';

const name = 'video_highlights';

const { FormItem } = Form;
export default function VideoHighlightsForm({
  form,
}: {
  form: InternalFormInstance;
}) {
  const [createType, setCreateType] = useState<createTypeEnum>(
    createTypeList[0].selector_value
  );
  const { data: videoSliceTaskList } = useTaskList({
    type: VideoMaterialType.HIGHLIGHT_CLIP,
    jobStatus: VideoMaterialJobStatus.SUCCESS,
  });

  const { setJobId, durationSum } = useTaskDetailDuration();

  const handleTaskChange = useMemoizedFn(async () => {
    const jobId = form.getFieldValue([name, 'video_slice_job_id']);
    if (!isString(jobId)) return;
    await setJobId(jobId);
  });

  const changeFormValue = (key: string, val: unknown) => {
    const oFormValue = form.getFieldsValue(true) as IMaterialVideoForm;
    form.setFieldsValue({
      [name]: {
        ...oFormValue.video_highlights,
        [key]: val,
      },
    });
  };

  console.log('!!!!durationSum', durationSum);

  return (
    <div className="videoHighlightsForm">
      <CardForForm title="生成方式">
        <FormItem label="" name={[name, 'createType']}>
          <CardList
            cardList={createTypeList}
            cbData={(val) =>
              setCreateType(val.selector_value as createTypeEnum)
            }
            className="w-[100%]"
          />
        </FormItem>
      </CardForForm>
      <CardForForm title="视频选择" desc={createTypeConfig[createType]?.desc}>
        {createType === createTypeEnum.SMART_HIGHLIGHTS && (
          <FormItem
            label="视频切片任务"
            name={[name, 'video_slice_job_id']}
            rules={[
              {
                required: true,
                message: '请选择任务',
                type: 'error',
                trigger: 'submit',
              },
            ]}
          >
            <Select
              clearable
              className="w-[340px] mb-[10px]"
              placeholder="请选择内容"
              onChange={handleTaskChange}
            >
              {videoSliceTaskList?.map((item) => (
                <Select.Option
                  value={item.job_id}
                  label={item.job_name}
                  key={item.job_id}
                />
              ))}
            </Select>
          </FormItem>
        )}
        {createType === createTypeEnum.CUSTOM_SELECTION && (
          <FormItem
            label=""
            name={[name, 'video_id_list']}
            rules={[
              {
                required: true,
                message: '请选择视频',
                type: 'error',
                trigger: 'submit',
              },
            ]}
          >
            <SelectVideo
              cdData={(val) => {
                if (Array.isArray(val) && !!val.length) {
                  const totalDuration = val.reduce(
                    (pre, cur) => pre + Number(cur.video_duration || 0),
                    0
                  );
                  changeFormValue(
                    'video_duration',
                    Number.parseInt(totalDuration.toString(), 10)
                  );
                } else {
                  changeFormValue('video_duration', 60);
                }
              }}
            />
          </FormItem>
        )}
      </CardForForm>
      <CardForForm title="信息收集" desc="将参考您填写的目标时长输出视频集锦">
        <FormItem
          label="生成时长(秒)"
          name={[name, 'video_duration']}
          requiredMark
          rules={[
            {
              validator: (val: number) => {
                if (val > durationSum) {
                  return {
                    result: false,
                    message: `目标时长需小于视频切片总时长`,
                  };
                }
                return { result: true, message: '' };
              },
            },
            {
              required: true,
              message: '请填写生成时长',
              type: 'error',
              trigger: 'submit',
            },
          ]}
        >
          <InputNumber
            size="medium"
            theme="row"
            className="w-[340px] mb-[10px]"
            disabled={createType === createTypeEnum.CUSTOM_SELECTION}
            min={1}
          />
        </FormItem>
      </CardForForm>
      <CardForForm title="基本信息" className="noBottpm">
        <FormItem
          label="任务名称"
          name={[name, 'job_name']}
          rules={[
            {
              required: true,
              message: '请填写任务名称',
              type: 'error',
              trigger: 'submit',
            },
          ]}
        >
          <Input placeholder="请输入任务名称" className="w-[340px] mb-[10px]" />
        </FormItem>
      </CardForForm>
    </div>
  );
}
