/**
 * <AUTHOR>
 * @date 2024/9/9 下午8:30
 * @desc index
 */

import React, { Fragment, useState } from 'react';
import { Form, Input, InputNumber, Loading } from 'tdesign-react';
import { CustomSingleSelector } from '@/components/CustomSingleSelector';
import './index.less';
import CustomUpload from '@/components/CustomUpload';
import { ErrorIcon, PlusIcon } from 'tdesign-icons-react';
import { CustomFileListProps } from '@/components/CustomUpload/type';
import { UploadStatus } from '@/components/CustomUpload/constants';
import FilePreview from '@/components/CustomUpload/FilePreview';

const { FormItem } = Form;

const name = 'video_clip';

const selectOptions = [
  { name: '智能识别', value: 'intelligent' },
  { name: '自定义', value: 'custom' },
];

const className = 'material-video-clip-form-comp';

export function VideoClipForm() {
  const [selectWay, setSelectWay] = useState('intelligent');
  return (
    <div className={className}>
      {/* <div className="p-24 bg-[#fff] rounded-4">*/}
      {/*  <div className="text-[18px] font-medium	mb-24">上传视频素材</div>*/}
      {/*  <div className="flex items-center gap-[12px]">*/}
      {/*    <FormItem*/}
      {/*      label=""*/}
      {/*      name={[name, 'video']}*/}
      {/*      style={{margin: 0}}*/}
      {/*      rules={[*/}
      {/*        {*/}
      {/*          required: true,*/}
      {/*          message: '请上传视频',*/}
      {/*          type: 'error',*/}
      {/*          trigger: 'submit',*/}
      {/*        },*/}
      {/*      ]}*/}
      {/*    >*/}
      {/*      <CustomUpload multiple={false} accept="video/mp4,video/mov"/>*/}
      {/*    </FormItem>*/}
      {/*    <div*/}
      {/*      className="h-[122px] w-[310px] text-[#00000060] p-12 bg-[linear-gradient(83deg,#F6F7FB_0%,#FBF8FB_100%)]">*/}
      {/*      <div>1. 格式支持：MP4、MOV等；</div>*/}
      {/*      <div>2. 基于对于视频的画面和语音识别高光片段</div>*/}
      {/*    </div>*/}
      {/*  </div>*/}
      {/* </div>*/}

      <div className="p-24 bg-[#fff] rounded-4">
        <div className="text-[18px] font-medium	mb-24">上传视频素材</div>
        <div className="flex items-center gap-[12px] w-[360px] h-[80px] p-12 bg-[linear-gradient(85deg,#F6F7FB_0%,#FBF8FB_100%)] rounded-4">
          <FormItem
            label=""
            name={[name, 'video']}
            style={{ margin: 0 }}
            rules={[
              {
                required: true,
                message: '请上传视频',
                type: 'error',
                trigger: 'submit',
              },
            ]}
          >
            <CustomUpload
              multiple={false}
              customUploadButton={
                <div className="w-[64px] h-[64px] border-dashed bg-[#fff] border-[#cccccc] border-[1px] text-[#0047F9] cursor-pointer rounded-4  flex justify-center items-center flex-col gap-[4px] bg-[#F4F6FF] hover:bg-[#f2f2f4] active:bg-[#ededf1] transition-all duration-200 ease-in-out">
                  <PlusIcon size={20} />
                  <div className="text-[12px]">添加视频</div>
                </div>
              }
              customFileList={CustomFileList}
              accept="video/mp4,video/quicktime"
            />
          </FormItem>

          <div className="flex-1 text-[rgba(0,0,0,0.40)] text-[12px]">
            <div className="desc">1. 格式支持：MP4、MOV等；</div>
            <div className="desc">
              2. 基于对于视频的画面和语音识别高光片段；
            </div>
          </div>
        </div>
      </div>

      <div className="p-24 my-16 bg-[#fff] rounded-4">
        <div className="text-[18px] font-medium	mb-24">信息收集</div>
        <div className="information-collection">
          <div>生成片段数(个）</div>
          <CustomSingleSelector
            value={selectWay}
            onChange={setSelectWay}
            className="ml-8"
            options={selectOptions}
          />
          {selectWay === 'custom' && (
            <FormItem label="" name={[name, 'video_segment_num']}>
              <InputNumber size="medium" theme="row" min={1} />
            </FormItem>
          )}
        </div>
      </div>
      <div className="p-24 bg-[#fff] rounded-4">
        <div className="text-[18px] font-medium	mb-24">基本信息</div>
        <div>
          <FormItem
            label="任务名称"
            name={[name, 'job_name']}
            rules={[
              {
                required: true,
                message: '请填写任务名称',
                type: 'error',
                trigger: 'submit',
              },
            ]}
          >
            <Input className="w-[340px]" placeholder="请输入任务名称" />
          </FormItem>
        </div>
      </div>
    </div>
  );
}

function CustomFileList(props: CustomFileListProps) {
  const { files, onReupload } = props;
  const [file] = files;
  return file ? (
    <div className="w-[64px] h-[64px] relative cursor-pointer group rounded-[4px] overflow-hidden">
      <Fragment x-if={file.status === UploadStatus.SUCCESS}>
        <FilePreview file={file} />
      </Fragment>
      <Fragment x-if={file.status === UploadStatus.UPLOADING}>
        <div className="w-full h-full bg-[#606061] flex justify-center items-center text-[white]">
          <Loading inheritColor size="small" />
        </div>
      </Fragment>
      <Fragment x-if={file.status === UploadStatus.ERROR}>
        <div className="w-full h-full bg-[#606061] flex justify-center items-center text-[white]">
          <ErrorIcon />
        </div>
      </Fragment>
      <div
        onClick={() => {
          onReupload(file._uid);
        }}
        className="absolute bottom-0 bg-opacity-85 bg-black text-[#fff] text-[12px] w-full text-center leading-[20px]"
      >
        重新上传
      </div>
    </div>
  ) : (
    <></>
  );
}
