import { useNavigate, useSearchParams } from 'react-router-dom';
import { Breadcrumb, Button, Form, MessagePlugin } from 'tdesign-react';
import {
  MaterialVideoFormDefault,
  VideoMaterialJobType,
  VideoMaterialType,
} from '../config';
import { getBreadcrumbName } from '../utils';
import VideoHighlightsForm from './videoHighlightsForm';
import './index.less';
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks';
import { createTypeList } from './videoHighlightsForm/utils';
import CardForForm from '../components/cardForForm';
import { UserUploadForm } from '@/pages/videoMaterialList/create/UserUploadForm';
import { VideoClipForm } from '@/pages/videoMaterialList/create/VideoClipForm';
import { ResourceSvr } from '@/pb/pb';
import { ReqType } from '@/pb/config';
import { IMaterialVideoForm } from '@/pages/videoMaterialList/type';
import { getVideoFirstFrame } from '@/utils/getVideoFirstFrame';
import { convertFileSize } from '@/utils/file';
import { FileItemType } from '@/components/CustomUpload/type';
import { useState } from 'react';
import { useNotifyStorage } from '@/pages/videoMaterialList/components/TaskNotifier/useNotifyStorage';

const classPrefix = 'videoMaterial-create-page';
const { BreadcrumbItem } = Breadcrumb;

const BTN_TEXT = {
  [VideoMaterialType.HIGHLIGHTS]: '开始生成',
  [VideoMaterialType.USER_UPLOAD]: '确定添加',
  [VideoMaterialType.HIGHLIGHT_CLIP]: '开始生成',
};

export default function VideoMaterialCreate() {
  const navigator = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [disable, setDisable] = useState(false);
  const videoMaterialType = searchParams.get('from') || '';
  const [form] = Form.useForm();
  useDeepCompareEffect(() => {
    if (videoMaterialType === VideoMaterialType.HIGHLIGHTS) {
      form.setFieldsValue({
        video_highlights: {
          createType: createTypeList[0].selector_value,
        },
      });
    }

    if (videoMaterialType === VideoMaterialType.USER_UPLOAD) {
      const formData = form.getFieldsValue(true);
      if (formData.user_upload.list.length === 0) {
        setDisable(true);
      }
    }
  }, [videoMaterialType, form]);

  const { openNotification } = useNotifyStorage();

  const handleSubmit = async () => {
    if (loading) return;
    const validateRes = await form.validate();
    const formValue = form.getFieldsValue(true) as IMaterialVideoForm;
    console.log(formValue);
    if (validateRes !== true) return;

    const loadingMsg = await MessagePlugin.loading(
      videoMaterialType === VideoMaterialType.USER_UPLOAD
        ? '上传中'
        : '任务创建中...'
    );
    try {
      setLoading(true);
      if (videoMaterialType === VideoMaterialType.USER_UPLOAD) {
        const fileList = formValue.user_upload?.list || [];
        const checkRes = checkFileStatus(fileList);
        if (!checkRes) {
          void MessagePlugin.error('视频上传中或上传失败，请等待或重新上传');
          return;
        }
        await handleUploadFile(fileList);
        void MessagePlugin.success('上传成功');
        navigator(`/videoMaterial-list?tab-type=${videoMaterialType}`);
        //   调用上传接口
      } else {
        // 101:高光片段生成
        // 102:高光集锦生成
        const params: ReqType<typeof ResourceSvr.CreateVideoProcJob> = {};
        if (videoMaterialType === VideoMaterialType.HIGHLIGHT_CLIP) {
          const fileParams = [
            {
              video: formValue?.video_clip?.video || [],
              desc: '来自视频切片-智能生成的上传视频素材',
              name: formValue?.video_clip?.video?.[0]?.name || '切片素材',
            },
          ];
          const checkRes = checkFileStatus(fileParams);
          if (!checkRes) {
            void MessagePlugin.error('视频上传中或上传失败，请等待或重新上传');
            return;
          }
          const uploadRes = await handleUploadFile(fileParams);
          if (!uploadRes) {
            void MessagePlugin.error('素材上传失败，请稍后重试');
            return;
          }
          params.job_type = Number.parseInt(
            VideoMaterialJobType.HIGHLIGHT_CLIP_JOB,
            10
          );
          params.video_id_list = uploadRes?.user_video_object_list
            .map((v) => v.video_id)
            .join(';');
          params.job_name = formValue?.video_clip?.job_name || '';
          params.video_segment_num = formValue?.video_clip?.video_segment_num;
        } else {
          params.job_type = Number.parseInt(
            VideoMaterialJobType.HIGHLIGHTS_JOB,
            10
          );
          params.job_name = formValue?.video_highlights?.job_name || '';
          params.video_slice_job_id =
            formValue?.video_highlights?.video_slice_job_id;
          params.video_duration =
            formValue.video_highlights?.video_duration?.toString();
          params.video_id_list =
            (formValue.video_highlights?.video_id_list ?? []).join(';') ||
            void 0;
        }

        console.log(params, 'params');
        const { job_id } = await ResourceSvr.CreateVideoProcJob(params);
        openNotification({
          id: job_id,
          type: videoMaterialType as VideoMaterialType,
        });
        void MessagePlugin.success('任务创建成功，已开始生成，请稍后');
        navigator(`/videoMaterial-list?tab-type=${videoMaterialType}`);
      }
    } catch (error) {
      console.error(error);
      void MessagePlugin.error('操作失败，请稍后重试');
    } finally {
      setLoading(false);
      loadingMsg.close();
    }
  };

  const checkFileStatus = (
    fileList: Array<{ video: FileItemType[]; name: string; desc: string }>
  ) => {
    return fileList.every((item) => item.video?.[0]?.status === 'success');
  };

  const handleUploadFile = async (
    fileList: Array<{ video: FileItemType[]; name: string; desc: string }>
  ) => {
    try {
      const list = await Promise.all(
        fileList.map(async (item) => {
          const videoUrl = item.video[0]?.url || '';
          const videoMsg = await getVideoFirstFrame({
            videoUrl,
            autoUpload: true,
          });
          return {
            video_cos_path: videoUrl,
            video_url: videoUrl,
            video_size: convertFileSize(item.video[0]?.size || 0),
            video_duration: videoMsg.info?.duration?.toString(),
            pic_url: videoMsg.cosUrl,
            video_create_type: 201,
            video_name: item.name,
            video_content: item.desc,
          };
        })
      );
      return await ResourceSvr.CreateUserVideoInfor({
        user_video_object_list: list,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleChange = useMemoizedFn((all) => {
    if (videoMaterialType !== VideoMaterialType.USER_UPLOAD) return;
    if (all.user_upload.list.length === 0) {
      setDisable(true);
    } else {
      setDisable(false);
    }
  });

  return (
    <div className={classPrefix}>
      <div className={`${classPrefix}-breadcrumb`}>
        <Breadcrumb separator=">" maxItemWidth="140px">
          <BreadcrumbItem
            onClick={() =>
              navigator(`/videoMaterial-list?tab-type=${videoMaterialType}`)
            }
          >
            视频
          </BreadcrumbItem>
          <BreadcrumbItem>
            {getBreadcrumbName(videoMaterialType as VideoMaterialType)}
          </BreadcrumbItem>
        </Breadcrumb>
      </div>
      <div className={`${classPrefix}-content`}>
        <Form
          form={form}
          labelWidth={106}
          initialData={MaterialVideoFormDefault}
          labelAlign="left"
          onValuesChange={(_, all) => {
            handleChange(all);
          }}
        >
          {/* 高光集锦表单 */}
          <VideoHighlightsForm
            form={form}
            x-if={videoMaterialType === VideoMaterialType.HIGHLIGHTS}
          />
          {/* 自定义上传表单*/}
          <UserUploadForm
            x-if={videoMaterialType === VideoMaterialType.USER_UPLOAD}
            form={form}
            onChange={handleChange}
          />
          {/* 视频切片表单*/}
          <VideoClipForm
            x-if={videoMaterialType === VideoMaterialType.HIGHLIGHT_CLIP}
          />
        </Form>
      </div>
      <CardForForm className={`${classPrefix}-footer`}>
        <Button
          loading={loading}
          className="gradient-primary"
          onClick={() => {
            handleSubmit().then();
          }}
          style={{ width: 150, height: 40 }}
          disabled={disable}
        >
          {BTN_TEXT[videoMaterialType as VideoMaterialType]}
        </Button>
        <Button
          className="gradient-default"
          theme="default"
          onClick={() => {
            navigator(`/videoMaterial-list?tab-type=${videoMaterialType}`);
          }}
          style={{ width: 110, height: 40, marginLeft: 20 }}
        >
          返回
        </Button>
      </CardForForm>
    </div>
  );
}
