/**
 * <AUTHOR>
 * @date 2024/9/6 下午4:47
 * @desc 用户自定义视频素材上传
 */

import React, { useEffect, useState } from 'react';
import { Form, Input, InternalFormInstance, Textarea } from 'tdesign-react';
import { AddIcon, MinusCircleIcon, PlusIcon } from 'tdesign-icons-react';
import CustomUpload from '@/components/CustomUpload';
import './index.less';
import { FileItemType } from '@/components/CustomUpload/type';
import { cloneDeep } from 'lodash-es';
import { IMaterialVideoForm } from '@/pages/videoMaterialList/type';

interface IProps {
  form: InternalFormInstance;
  onChange: (a: any) => void;
}

const { FormItem, FormList } = Form;

const FORM_KEY = 'user_upload';

const className = 'video-material-upload-form-comp';

export function UserUploadForm(props: IProps) {
  const { form, onChange } = props;
  const [uploadedFiles, setUploadedFiles] = useState<Partial<FileItemType>[]>(
    []
  );

  const handleFilesChange = (files: FileItemType[]) => {
    setUploadedFiles(files);
    const newList =
      (form.getFieldsValue(true) as IMaterialVideoForm)?.user_upload?.list ||
      [];

    files.forEach((file) => {
      const uid = file._uid;
      const findIdx = newList.findIndex(
        (item) => item.video?.[0]?._uid === uid
      );
      if (findIdx !== -1) {
        newList[findIdx].video = [file];
      } else {
        newList.push({
          name: file.name,
          desc: '',
          video: [file],
        });
      }
    });
    const newField = {
      user_upload: {
        list: newList,
      },
    };
    // console.log(newList, 'after');
    form.setFieldsValue(newField);
    onChange?.(newField);
  };

  return (
    <div className={className}>
      <div className="text-[18px] font-medium	mb-24">本地视频上传</div>
      <FormList name={[FORM_KEY, 'list']}>
        {(fields, { remove }) => (
          <>
            {fields.map(({ key, name, ...restField }, index) => {
              return (
                <div
                  className="rounded-[4px] bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)] mt-16"
                  key={key}
                >
                  <header
                    style={{
                      padding: 20,
                      display: 'flex',
                      justifyContent: 'space-between',
                      // borderBottom: '1px solid rgba(225, 225, 225, 1)',
                    }}
                  >
                    <div className="text-[18px] font-medium">
                      视频{index + 1}
                    </div>
                    <div>
                      <div
                        style={{
                          width: 16,
                          height: 16,
                          cursor: 'pointer',
                        }}
                      >
                        <MinusCircleIcon
                          onClick={() => {
                            remove(index);
                            const files = cloneDeep(uploadedFiles);
                            //   删除uploadedFiles中对应的值
                            files.splice(index, 1);
                            setUploadedFiles(files);
                          }}
                          style={{ fontSize: 16, color: '#FBC3C4' }}
                        />
                      </div>
                    </div>
                  </header>
                  <div className="px-20 pb-20 flex items-center h-[154px] overflow-hidden">
                    <div className="w-[144px]">
                      <FormItem
                        {...restField}
                        name={[name, 'video']}
                        rules={[
                          {
                            required: true,
                            message: '请上传视频',
                            type: 'error',
                            trigger: 'submit',
                          },
                        ]}
                      >
                        <CustomUpload
                          include=""
                          multiple={false}
                          customUploadButton={
                            <div className="w-[122px] border-dashed border-[#cccccc] border-[1px] cursor-pointer h-[122px] rounded-4 overflow-hidden flex justify-center items-center flex-col gap-[4px] bg-[#F4F6FF] hover:bg-[#f2f2f4] active:bg-[#ededf1] transition-all duration-200 ease-in-out">
                              <PlusIcon size={20} />
                              <div>添加视频</div>
                            </div>
                          }
                        />
                      </FormItem>
                    </div>

                    <div className="flex-1">
                      <FormItem
                        {...restField}
                        label="视频名称"
                        // requiredMark={false}
                        name={[name, 'name']}
                        rules={[
                          {
                            required: true,
                            message: '请填写视频名称',
                            type: 'error',
                            trigger: 'submit',
                          },
                        ]}
                        labelAlign="right"
                      >
                        <Input placeholder="请输入视频名称" />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="视频描述"
                        name={[name, 'desc']}
                        labelAlign="right"
                      >
                        <Textarea rows={2} placeholder="请输入描述、非必填" />
                      </FormItem>
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        )}
      </FormList>

      <CustomUpload
        wrapperClassName="custom-upload"
        type="video"
        multiple
        accept="video/*"
        customFileList={null}
        value={uploadedFiles}
        onChange={handleFilesChange}
        customUploadButton={
          <div
            className="h-[124px] w-full flex flex-col justify-center items-center cursor-pointer"
            style={{
              background: 'linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%)',
              color: 'rgba(0, 0, 0, 0.30)',
            }}
          >
            <AddIcon
              style={{
                color: 'rgba(0, 0, 0, 0.30)',
              }}
            />
            添加视频
          </div>
        }
      />
    </div>
  );
}
