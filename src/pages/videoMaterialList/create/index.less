.videoMaterial-create-page {
  display: flex;
  flex-direction: column;
  &-breadcrumb{
      flex-shrink: 0;
      color: rgba(0, 0, 0, 0.90);
      font-style: normal;
      font-weight: 600;
      margin-bottom: 16px;
  }
  .t-form {
    flex: 1;
    overflow: auto;
    margin-bottom: 16px;
    .create-cardList {
      .cardListItem  {
        margin-bottom: 0px;
      }
    }
    .noBottpm {
      margin-bottom: 0px;
    }
  }

  &-footer{
    flex-shrink: 0;
    margin-bottom: -10px;
    &.main_content {
      margin-bottom: 16px;
      flex-shrink: 0;
      height: auto;
      flex-direction: row;
    }
  }
}