import { EnumToUnion } from '@/utils/template/type';
import { VideoMaterialJobType, VideoMaterialType } from './config';
import { FileItemType } from '@/components/CustomUpload/type';

export type VideoType = EnumToUnion<typeof VideoMaterialType>;

export type JobType = EnumToUnion<typeof VideoMaterialJobType>;

export interface IMaterialVideoForm {
  video_highlights?: {
    job_name: string;
    video_duration: number;
    video_id_list?: string[];
    video_slice_job_id?: string;
    createType: string;
  };
  video_clip?: {
    video_segment_num?: number;
    job_name: string;
    video: FileItemType[];
  };
  user_upload?: {
    list: Array<{ video: FileItemType[]; name: string; desc: string }>;
  };
}
