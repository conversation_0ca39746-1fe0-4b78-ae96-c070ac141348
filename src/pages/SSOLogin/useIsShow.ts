import { RefObject, useCallback, useEffect, useRef, useState } from 'react';

export function useIsShow(iframeRef: RefObject<HTMLIFrameElement> | null) {
  const [isShow, setIsShow] = useState<boolean>(true);
  const num = useRef<number>(0);
  const queryIframe = useCallback(() => {
    try {
      const href = iframeRef?.current?.contentWindow?.location.href;
      console.log('可以读了');
      // if (href !== 'about:blank') {
      setIsShow(false);
      // }
    } catch (err) {
      console.log('轮询iframe中');
    }
  }, [iframeRef]);
  useEffect(() => {
    let intervalId: ReturnType<typeof setInterval> | null = null;
    if (iframeRef?.current) {
      if (isShow) {
        // 一直尝试读取contentWindow，只要读到说明后台报错，可以隐藏字体
        intervalId = setInterval(() => queryIframe(), 200);
      }
      // 使用计数的方式决定文字显影，第一次load显示，第二次load隐藏
      iframeRef.current?.addEventListener('load', () => {
        // TODO 与sso那边进行postmessage通信后更改isShow值
        num.current = num.current + 1;
        num.current > 1 ? setIsShow(false) : setIsShow(true);
      });
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [iframeRef, queryIframe, isShow]);
  return [isShow];
}
