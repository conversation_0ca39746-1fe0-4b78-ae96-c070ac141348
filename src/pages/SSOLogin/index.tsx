import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import './style.less';

export default function SSOLogin() {
  const location = useLocation();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const iframeUrl = useMemo(() => {
    const regexp = /iframeUrl=(.+)/;
    return decodeURIComponent(location.search.match(regexp)?.[1] || '');
  }, [location]);

  // 监听iframe中页面的message，iframe中登陆完成刷新页面
  const monitor = useCallback(async () => {
    let loginSuccess = false;
    let redirectUrl = `https://${window.location.host}/intelligent/live/page/index.html`;
    await new Promise((resolve) => {
      const onmessage = (ev: MessageEvent) => {
        if (ev.origin !== window.location.origin) return;
        if (ev.data.status === 'success') {
          loginSuccess = true;
          redirectUrl = ev.data.redirectUrl;
          resolve(void 0);
        }
      };
      window.addEventListener('message', onmessage);
    });
    if (loginSuccess) {
      // 通知消费方刷新登录态
      const successUrl = decodeURIComponent(redirectUrl);
      window.location.replace(successUrl);
    }
  }, []);

  useEffect(() => {
    monitor().then();
  }, [monitor]);

  return (
    <div className="h-full relative" style={{ background: '#F5F9FF' }}>
      <iframe
        id="myIframe"
        ref={iframeRef}
        src={iframeUrl}
        title="sso登陆"
        style={{
          width: '1026px',
          height: '800px',
          border: 'none',
          position: 'absolute',
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      />

      {/* 客户端操作栏*/}
      {/* <div className="absolute flex items-center px-24 mt-16 w-full">*/}
      {/*   <div className="config_admuse_sso__logo2 h-full" />*/}
      {/* </div>*/}
    </div>
  );
}
