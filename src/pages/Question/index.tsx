import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Breadcrumb, Button, MessagePlugin } from 'tdesign-react';
import { useNavigate } from 'react-router-dom';
import { cloneDeep } from 'lodash-es';
import { MainContent, Page } from '@/components/Layout';
import { CONTENT_TYPE_MAP } from '@/const/common';
import {
  IScriptFormRef,
  ScriptForm,
  ScriptResult,
} from '@/components/ScriptForm';
import { getFromParams } from '@/components/ScriptForm/utils';
import { IScriptForm } from '@/components/ScriptForm/type';
import { getParams } from '@/utils/url';
import SessionStorageUtil from '@/utils/sessionStorageUtil';
import './index.less';

const { BreadcrumbItem } = Breadcrumb;

export default function Question() {
  const navigate = useNavigate();
  const formRef = useRef<IScriptFormRef>(null);
  const [initFormValue, setInitFormValue] = useState<Partial<IScriptForm>>();

  const { fromParam, application_scenarios, type } = getFromParams();
  const { uid } = getParams();

  useEffect(() => {
    if (uid) {
      const value = SessionStorageUtil.getItem(`script_${uid}`);
      if (value) {
        setInitFormValue(value);
      }
    }
  }, [uid]);

  const onCreateFail = useCallback(() => {
    void MessagePlugin.error('脚本解析失败');
  }, []);

  const onCreateSuccess = useCallback(
    (scriptRes: ScriptResult) => {
      // 视频理解跳转的路由是特别的
      const createRoutePath =
        application_scenarios === CONTENT_TYPE_MAP.Video.children.Vision.value
          ? '/script-list/vision/create'
          : `/script-list/create`;
      // 在 state 里面传递参数
      navigate(`${createRoutePath}?from=${fromParam}`, {
        state: cloneDeep({
          scriptViews: scriptRes.scriptViews,
          elements: scriptRes.scriptElements,
          info: scriptRes.scriptGlobalData.info,
          globalField: scriptRes.scriptGlobalData,
        }),
      });
    },
    [application_scenarios, fromParam, navigate]
  );

  const submit = async () => {
    if (!formRef.current) return;

    await formRef.current.submit();
  };
  return (
    <>
      <Page
        title="新增脚本"
        breadCrumb={
          <Breadcrumb separator=">" maxItemWidth="140px">
            <BreadcrumbItem onClick={() => navigate('/script-list')}>
              脚本
            </BreadcrumbItem>
            <BreadcrumbItem>新增脚本</BreadcrumbItem>
          </Breadcrumb>
        }
      >
        <div className="pb-[15px]">
          <ScriptForm
            initFormData={initFormValue}
            uid={uid}
            ref={formRef}
            type={type}
            scenarios={application_scenarios}
            onCreateSuccess={onCreateSuccess}
            onCreateFail={onCreateFail}
          />
          <MainContent className="p-24 mb-16 operations">
            <Button
              className="gradient-primary"
              onClick={() => {
                submit().then();
              }}
              style={{ width: 150, height: 40 }}
            >
              生成脚本
            </Button>
            <Button
              className="gradient-default"
              theme="default"
              onClick={() => {
                navigate('/script-list');
              }}
              style={{ width: 110, height: 40, marginLeft: 20 }}
            >
              返回
            </Button>
          </MainContent>
        </div>
      </Page>
    </>
  );
}
