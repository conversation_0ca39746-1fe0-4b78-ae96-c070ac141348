import React, { useContext } from 'react';
import { useRecoilState } from 'recoil';
import { LoginContext } from '@/hooks/login';
import IndexFrame from '@/assets/images/index-frame.svg';
import IndexLogo from '@/assets/images/index-logo.svg';
import IndexSlogan from '@/assets/images/index-slogan.svg';
import { LoginStateAtom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { UserInfo } from '@/components/UserInfo';
import { useNavigate } from 'react-router-dom';
import { LEFT_MENU_LIST } from '@/layouts/LeftMenu';
import { ApplicationSelect } from '@/pages/Index/ApplicationSelect';
import { useMount } from 'ahooks';
import to from 'await-to-js';

const firstMenu = LEFT_MENU_LIST[0].children[0].value;

function LoginModule() {
  const [loginState] = useRecoilState(LoginStateAtom);
  const [userInfo] = useRecoilState(UserInfoAtom);
  const loginCtx = useContext(LoginContext);
  const nav = useNavigate();
  if (loginCtx?.isFetchingLogin) return null;
  if (loginState) {
    return <UserInfo userInfo={userInfo} />;
  }

  return (
    <div
      className="w-[90px] h-[45px] flex items-center justify-center text-[16px] text-[#fff] rounded-[8px] border-[2px] border-white border-solid cursor-pointer"
      onClick={() => {
        if (loginState) {
          nav(firstMenu);
          return;
        }
        loginCtx?.goToLogin({ userGesture: true });
      }}
    >
      登录
    </div>
  );
}

export function IndexPage() {
  const loginContext = useContext(LoginContext);
  const [userInfo] = useRecoilState(UserInfoAtom);
  // const navigate = useNavigate();

  // 获取一次个人信息
  useMount(async () => {
    if (!loginContext || loginContext.isFetchingLogin) return;
    if (userInfo) return;
    await to(loginContext?.fetchLoginInfo({ noCache: true }));
    // const [, info] = await to(loginContext?.fetchLoginInfo({ noCache: true }));
    // if (info?.loginUserInfo?.pagedooExtend?.account_id) {
    //   navigate('/workbench');
    //   return;
    // }
  });

  return (
    <div
      className="pagedoo-meta-live-global w-screen h-screen overflow-hidden bg-no-repeat bg-cover"
      style={{
        backgroundImage: `url(${IndexFrame})`,
      }}
    >
      <div
        className="max-w-[1440px] min-w-[1280px] px-20"
        style={{ margin: '0 auto' }}
      >
        <div className="pt-20 w-full flex justify-between">
          <img
            className="w-[182px] h-[24px]"
            src={IndexLogo}
            alt="index-logo"
          />
          <LoginModule />
        </div>
        <div className="pt-[44px] mb-20">
          <img
            className="w-[432px] h-[168px]"
            src={IndexSlogan}
            alt="index-slogan"
          />
        </div>
        <ApplicationSelect />
      </div>
    </div>
  );
}
