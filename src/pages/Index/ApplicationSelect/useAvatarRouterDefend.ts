/**
 * <AUTHOR>
 * @date 2025/3/20 20:43
 * @desc useAvatarRouterDefend
 */
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';

export const useAvatarRouterDefend = () => {
  const [userState] = useRecoilState(UserInfoAtom);
  const navigate = useNavigate();
  useEffect(() => {
    if (MatchedGlobalConfigItem.appcode !== 'pagedoo') return;
    if (!userState) return;

    if (!userState?.pagedooExtend?.account_id) {
      navigate(`/`);
    }
  }, [navigate, userState]);
  return;
};
