/**
 * <AUTHOR>
 * @date 2025/3/20 19:35
 * @desc useUpdateAvatarAccount
 */
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { LoginStateAtom } from '@/model/login';
import { useMemoizedFn } from 'ahooks';
import { AVATAR_ACCOUNT_KEY } from '@/pages/Index/ApplicationSelect/const';

export const useUpdateAvatarAccount = () => {
  const [userState, setUserState] = useRecoilState(UserInfoAtom);
  const [loginState, setLoginState] = useRecoilState(LoginStateAtom);

  const updateAvatarAccountInfo = useMemoizedFn(async (appId: string) => {
    if (userState?.pagedooExtend && loginState) {
      localStorage.setItem(AVATAR_ACCOUNT_KEY, appId);
      setUserState({
        ...userState,
        pagedooExtend: {
          ...userState.adExtend,
          account_id: appId,
        },
      });
      setLoginState({
        ...loginState,
        strinifyLoginInfo: JSON.stringify({
          ...JSON.parse(loginState.strinifyLoginInfo),
          app_id: appId,
        }),
      });
    }
  });

  return {
    updateAvatarAccountInfo,
  };
};
