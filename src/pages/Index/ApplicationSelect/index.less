.avatar-Application-select-comp {
  .application-list {
    padding: 20px 20px 0;
    background: rgba(255, 255, 255, .8);
    border-radius: 8px;
    box-shadow: -6px 6px 20px 0px rgba(146, 146, 146, 0.16);
    overflow-y: auto;
    height: 542px;
    width: 100%;


    .application-list-item {
      position: relative;
      margin-bottom: 20px;
      background: #fff;
      box-sizing: border-box;
      padding: 20px 16px;
      border-radius: 8px;
      cursor: pointer;
      box-shadow: -6px 6px 20px 0px rgba(146, 146, 146, 0.16);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: calc(50% - 10px);
      display: inline-block;
      vertical-align: top;

      &:nth-child(odd) {
        margin-right: 20px;
      }

      .application-img {
        width: 57px;
        height: 57px;
        border-radius: 6px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .application-message {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .app-name {
          font-size: 16px;
          line-height: 26px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .app-id {
          font-weight: 300;
          font-size: 14px;
          line-height: 22px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }
      }

      &--active {
        background: linear-gradient(88.08deg, #0153ff -0.01%, #2e7ffd 49.89%, #c1a3fd 99.99%);

        .application-message {
          color: #fff;
        }
      }
    }
  }
}
