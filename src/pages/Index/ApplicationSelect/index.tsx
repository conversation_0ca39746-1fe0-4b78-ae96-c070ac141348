/**
 * <AUTHOR>
 * @date 2025/3/20 15:30
 * @desc ApplicationSelect
 */

import React, { useContext, useMemo, useState } from 'react';
import { Button, MessagePlugin, Tooltip } from 'tdesign-react';
import { useRecoilState } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { LoginContext } from '@/hooks/login';
import { useNavigate } from 'react-router-dom';
import './index.less';
import Text from '@/components/CopyText';
import placeholderSvg from '@/assets/images/placeHolder.svg';
import { useAsyncEffect, useMemoizedFn } from 'ahooks';
import { useUpdateAvatarAccount } from '@/pages/Index/ApplicationSelect/useUpdateAvatarAccount';
import { AppList } from '@/pb/api/AuthAdminSvr';
import to from 'await-to-js';
import { RespType } from '@/pb/config';

export function ApplicationSelect() {
  const [loginState] = useRecoilState(LoginStateAtom);
  const loginCtx = useContext(LoginContext);
  const nav = useNavigate();
  const [selectApplicationId, setSelectApplicationId] = useState('');
  const [applicationList, setApplicationList] = useState<
    RespType<typeof AppList>
  >([]);
  const { updateAvatarAccountInfo } = useUpdateAvatarAccount();

  const handleClick = useMemoizedFn(() => {
    // 未登录
    if (!loginState) {
      loginCtx?.goToLogin({ userGesture: true });
      return;
    }
    if (!selectApplicationId) {
      void MessagePlugin.error('请选择应用');
      return;
    }
    updateAvatarAccountInfo(selectApplicationId).then(() => {
      nav('/workbench');
    });
  });

  const btnValue = useMemo(() => {
    if (loginState) {
      return {
        disabled: !selectApplicationId,
        text: '进入管理台',
      };
    }
    return {
      disabled: true,
      text: '立即体验',
    };
  }, [loginState, selectApplicationId]);

  useAsyncEffect(async () => {
    if (!loginState) {
      if (applicationList.length) {
        setApplicationList([]);
      }
      return;
    }
    const [, list] = await to(AppList({}));
    setApplicationList(list ?? []);
  }, [loginState]);

  // 默认选第一个
  // useEffect(() => {
  //   setSelectApplicationId(applicationList?.[0]?.appId);
  // }, [applicationList]);

  return (
    <div className="avatar-Application-select-comp">
      <div className="text-18 font-semibold mb-12">请选择应用</div>
      <div className="application-list" style={{}}>
        {applicationList.map((application) => {
          return (
            <div
              className={`application-list-item ${
                selectApplicationId === application.appId
                  ? 'application-list-item--active'
                  : ''
              }`}
              onClick={() => {
                setSelectApplicationId(application.appId);
              }}
              key={application.appId}
            >
              <div className="flex">
                <div className="application-img">
                  <img src={application.picUrl || placeholderSvg} alt="" />
                </div>
                <div className="ml-16 application-message">
                  <Tooltip content={application.appName} showArrow={false}>
                    <div className="app-name">
                      {application.appName || '默认应用'}
                    </div>
                  </Tooltip>
                  <div className="app-id">
                    <div className="mr-[16px] font-normal">应用ID</div>
                    <Text
                      iconStyle={{ opacity: '0.6' }}
                      copyable
                      key={application.appId}
                    >
                      {application.appId}
                    </Text>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="fixed bottom-0 py-20 px-0">
        <Button
          disabled={btnValue.disabled}
          className="w-[256px] h-[52px] gradient-primary"
          onClick={handleClick}
        >
          {btnValue.text}
        </Button>
      </div>
    </div>
  );
}
