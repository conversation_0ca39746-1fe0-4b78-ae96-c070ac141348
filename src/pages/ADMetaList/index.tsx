/**
 * <AUTHOR>
 * @date 2024/5/27 下午8:12
 * @desc index
 */

import React, { useEffect, useState } from 'react';
import './index.less';
import {
  Button,
  Dialog,
  DialogPlugin,
  Input,
  MessagePlugin,
} from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import { useMetaLiveList } from '@/pages/ADMetaList/useMetaLiveList';
import {
  IMetaLiveRecordProps,
  MetaRecord,
} from '@/pages/ADMetaList/MetaRecord';
import { MetaLiveSvr } from '@/pb/pb';
import { ADSelectProductModal } from '@/pages/ADMetaList/ADSelectProductModal';
import { ContentDetailQuery } from '@/pb/api/ContentSvr';
import { UpdateLiveExtendConfigItem } from '@/pb/api/Development';
import to from 'await-to-js';
import { PlayConfig } from '@/type/pagedoo';
import { getADMetaLiveStatisticData } from '@/pages/Editor/ADLive/statistic';
import { useMemoizedFn } from 'ahooks';
import { walkTimelineComponent } from '@/pages/Editor/common/updateComponent';
import { useUpdateLiveRoom } from '@/pages/ADMetaList/hooks/useUpdateLiveRoom';
import { LiveRoomUpdater } from '@/pages/ADMetaList/LiveRoomUpdater';
import { RespError } from '@/pb/config';

const className = 'ad-meta-live-list-page';
const contentType = 'ad_script_live';

const livePushError: Record<string, string> = {
  1120004: '当前直播间不存在，请检查直播间配置',
  1122028: '直播间不是「未开播」状态，暂时无法开播',
  1122032: '问答不足5条，补充后再开播',
  1122033: '话术生成中，请稍后',
};

export default function ADMetaLiveList() {
  const {
    refresh,
    loading,
    recordCount,
    records,
    setQueryParams,
    queryParams,
  } = useMetaLiveList({
    queryParamsDefault: {
      searchKey: '',
      contentType,
      // 分页参数
      pageSize: 10,
      pageNum: 1,
    },
    fetchContentDetail: true,
  });
  // 搜索
  const [searchVal, setSearchVal] = useState('');

  const [showCreateLiveDiag, setShowCreateLiveDiag] = useState(false);

  const { checkShouldUpdate, updateProcessOptions, terminateUpdateProcess } =
    useUpdateLiveRoom();

  // 其他页面返回时刷新一下列表
  useEffect(() => {
    const visibilitychangeReport = () => {
      if (document.visibilityState === 'visible') {
        refresh();
      }
    };

    document.addEventListener('visibilitychange', visibilitychangeReport);

    return () => {
      document.removeEventListener('visibilitychange', visibilitychangeReport);
    };
  }, [refresh]);

  // 创建直播间
  const handleCreateLive = () => {
    setShowCreateLiveDiag(true);
    // const confirmDia = DialogPlugin.confirm({
    //   header: '创建直播间',
    //   body: (
    //     <ADSelectProductModal
    //       onClose={() => {
    //         confirmDia.hide();
    //       }}
    //     />
    //   ),
    //   footer: null,
    //   closeOnOverlayClick: false,
    //   width: 580,
    //   onClose: () => {
    //     confirmDia.hide();
    //   },
    // });
  };

  const beforeStartJudge = async (meta_live_id: string) => {
    // 开始上报直播间元数据
    const [err, resp] = await to(
      ContentDetailQuery({
        content_id: meta_live_id,
      })
    );
    if (err) {
      MessagePlugin.error('查询直播间数据失败');
      return false;
    }
    let playScript: PlayConfig;
    try {
      playScript = JSON.parse(resp.extend.playScript);
    } catch (e) {
      MessagePlugin.error('解析直播间数据失败');
      return false;
    }
    // 开播前上报统计信息给后台
    const [getStatisticErr, result] = await to(
      getADMetaLiveStatisticData(playScript)
    );
    if (getStatisticErr) {
      const msg = '获取元数据失败';
      MessagePlugin.error(msg);
      return false;
    }
    // 开播前上报
    const [saveStatisticErr] = await to(
      UpdateLiveExtendConfigItem({
        live_id: meta_live_id,
        node_id: 'global',
        config_item_id: 'ADMetaLiveStatisticData',
        config_item_value: JSON.stringify(result),
      })
    );
    if (saveStatisticErr) {
      const msg = '请求直播间元数据失败';
      MessagePlugin.error(msg);
      return false;
    }
    return true;
  };

  const handleBeforeOpenEditor = useMemoizedFn<
    Required<IMetaLiveRecordProps>['beforeOpenEdit']
  >(async (options) => {
    const shouldUpdate = checkShouldUpdate({
      data: options.item,
      openEdit: options.openEdit,
    });
    if (!shouldUpdate) {
      return true;
    }
    return false;
  });

  return (
    <>
      <div className="ad-meta-live-list-page">
        <div className={`${className}-header`}>直播间管理</div>

        <div className={`${className}-content`}>
          <div className="flex p-8">
            <div className="flex-1">
              <Button
                className="gradient-primary"
                onClick={() => handleCreateLive()}
              >
                创建直播
              </Button>
            </div>
            <Input
              style={{ width: 280 }}
              value={searchVal}
              placeholder="请输入直播间名称"
              onChange={setSearchVal}
              onEnter={(value) => {
                setQueryParams({
                  ...queryParams,
                  pageNum: 1,
                  searchKey: value,
                });
              }}
              suffixIcon={<SearchIcon />}
            />
          </div>
          <div>
            <MetaRecord
              record={records}
              loading={loading}
              recordCount={recordCount}
              queryParams={queryParams}
              setQueryParams={setQueryParams}
              refresh={refresh}
              contentType={contentType}
              onBeforeStart={beforeStartJudge}
              disableRealTimeQA
              autoRefresh
              showBarrageStatus
              // TODO：广告临时隐藏复制
              disableCopy
              beforeOpenEdit={handleBeforeOpenEditor}
            />
          </div>
        </div>
      </div>
      <Dialog
        visible={showCreateLiveDiag}
        header="创建直播间"
        footer={null}
        width={600}
        closeOnOverlayClick={false}
        destroyOnClose
        onClose={() => setShowCreateLiveDiag(false)}
      >
        <ADSelectProductModal
          onClose={() => {
            setShowCreateLiveDiag(false);
          }}
        />
      </Dialog>
      <LiveRoomUpdater
        x-if={!!updateProcessOptions}
        options={updateProcessOptions}
        onCancel={() => terminateUpdateProcess()}
        onFinish={() => terminateUpdateProcess()}
      />
    </>
  );
}
