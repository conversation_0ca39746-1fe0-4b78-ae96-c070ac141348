import { walkTimelineComponent } from './../../Editor/common/updateComponent';
import { OpenEditType } from '@/pages/ADMetaList/typings';
import { useMemoizedFn } from 'ahooks';
import { useMemo, useState } from 'react';
import { ContentDetailQuery } from '@/pb/api/ContentSvr';
import { PlayConfig } from '@/type/pagedoo';
import {
  ADComponentsNameEnum,
  CompLiveSpeechAD,
} from '@/pages/Editor/ADLive/components';
import { IMetaLiveRecord } from '@/pages/ADMetaList/useMetaLiveList';

export interface IUpdateLiveRoomProcessOptions {
  data: IMetaLiveRecord;
  openEdit: OpenEditType;
}
/**
 * 升级旧直播间判断逻辑
 */
export const useUpdateLiveRoom = () => {
  // 是否进入更新流程
  const [updateProcessOptions, setUpdateProcessOptions] =
    useState<IUpdateLiveRoomProcessOptions>();

  const terminateUpdateProcess = useMemoizedFn(() =>
    setUpdateProcessOptions(undefined)
  );

  const checkShouldUpdate = useMemoizedFn(
    (options: IUpdateLiveRoomProcessOptions) => {
      const { data } = options;
      if (!data.contentDetail) return false;
      let shouldUpdate = false;
      const { componentMetaInfo } = data.contentDetail;
      if (!componentMetaInfo) {
        shouldUpdate = true;
      } else {
        // 旧直播间没有version 字段
        try {
          const adComponentMetaInfo = JSON.parse(componentMetaInfo);
          if (!adComponentMetaInfo.LiveSpeechAD?.version) {
            shouldUpdate = true;
          }
        } catch (e) {
          // 解析报错，统一走升级逻辑
          shouldUpdate = true;
        }
      }
      if (shouldUpdate) {
        setUpdateProcessOptions(options);
      }
      return shouldUpdate;
    }
  );

  return useMemo(
    () => ({
      terminateUpdateProcess,
      setUpdateProcessOptions,
      updateProcessOptions,
      checkShouldUpdate,
    }),
    [checkShouldUpdate, terminateUpdateProcess, updateProcessOptions]
  );
};
