/**
 * <AUTHOR>
 * @date 2025/03/05 下午20:20
 * @desc useLiveGroup
 */
import {
  /** 查询直播组列表 */
  LiveGroupQueryList,
  /** 直播组新增，操作成功后返回新列表 */
  LiveGroupAdd,
  /** 直播组修改信息（名称），操作成功后返回新列表 */
  LiveGroupModify,
  /** 获取某个组下所有直播列表（带分页） */
  LiveGroupLiveInfoList,
  /** 删除某个直播组 */
  LiveGroupDelete,
  /** 绑定直播间到直播组 */
  LiveGroupBind,
} from '@/pb/api/LiveGroupSvr';
import { RespError, RespType } from '@/pb/config';
import { useRequest } from 'ahooks';
import { useCallback, useEffect, useState } from 'react';
import { MessagePlugin } from 'tdesign-react';
import to from 'await-to-js';

export type LiveGroupListItem = RespType<typeof LiveGroupQueryList>[number];
export type IGroupMetaLiveRecord = RespType<
  typeof LiveGroupLiveInfoList
>[number];

export const useLiveGroup = () => {
  const [liveGroupList, setLiveGroupList] = useState<LiveGroupListItem[]>([]);
  const [activeLiveGroupId, setActiveLiveGroupId] = useState('__default__');

  const {
    data: listData,
    error,
    loading,
  } = useRequest(async () => {
    const res = await LiveGroupQueryList({ title: '' });
    return res;
  });
  useEffect(() => {
    setLiveGroupList(listData || []);
  }, [listData]);

  const addLiveGroup = useCallback(
    async ({ title }: { title: string }) => {
      if (!title) {
        return;
      }
      try {
        const res = await LiveGroupAdd({ title });
        setLiveGroupList([res, ...liveGroupList]);
        MessagePlugin.success('新增直播组成功');
      } catch (error) {
        // 请求逻辑统一处理
        // MessagePlugin.error('系统繁忙，请稍后再试');
      }
    },
    [liveGroupList]
  );

  const editLiveGroup = useCallback(
    async ({ id, title }: { id: string; title: string }) => {
      if (!title || !id) {
        return;
      }
      try {
        await LiveGroupModify({ live_group_id: id, title });
        setLiveGroupList(
          liveGroupList.map((item) =>
            item.live_group_id === id ? { ...item, title } : item
          )
        );
        MessagePlugin.success('修改组名成功');
      } catch (error) {
        // 请求逻辑统一处理
        // MessagePlugin.error('系统繁忙，请稍后再试');
      }
    },
    [liveGroupList]
  );
  const getLiveGroupLiveInfo = useCallback(
    async (groupId: string, pageNum: number, pageSize: number) => {
      try {
        if (!groupId) return { meta_live_list: [], count: '0' };
        return await LiveGroupLiveInfoList({
          live_group_id: groupId,
          page_num: pageNum,
          page_size: pageSize,
        });
      } catch (error) {
        // 请求逻辑统一处理
        // MessagePlugin.error('系统繁忙，请稍后再试');
      }
    },
    []
  );

  const queryLiveGroupLiveInfo = useCallback(
    async (groupId: string, pageNum: number, pageSize: number) => {
      if (!groupId) return { list: [], count: 0 };
      const [, list] = await to(
        LiveGroupLiveInfoList({
          live_group_id: groupId,
          page_num: pageNum,
          page_size: pageSize,
        })
      );
      // FIXME: 临时处理，等协议统一
      if (Array.isArray(list)) {
        return {
          list: list.map((item) => ({
            ...item,
            meta_live_status: item.meta_live_status.toString(),
          })),
          count: list.length,
        };
      }
    },
    []
  );

  const deleteLiveGroup = useCallback(
    async (id: string) => {
      if (!id) {
        return;
      }
      try {
        await LiveGroupDelete({ live_group_id: id });
        setLiveGroupList(
          liveGroupList.filter((item) => item.live_group_id !== id)
        );
        MessagePlugin.success('直播组删除成功');
      } catch (error) {
        // 请求逻辑统一处理
        // MessagePlugin.error('系统繁忙，请稍后再试');
      }
    },
    [liveGroupList]
  );

  const bindLiveGroup = useCallback(async (liveId: string, groupId: string) => {
    if (!liveId || !groupId) {
      return;
    }
    try {
      await LiveGroupBind({
        live_id: liveId,
        live_group_id: groupId,
      });
      MessagePlugin.success('绑定直播组成功');
    } catch (error) {
      if (error instanceof RespError) {
        if (error.resultCode === 'ALREADY_EXISTS') {
          MessagePlugin.error('直播组已绑定');
          return;
        }
      }
      // 请求逻辑统一处理
      // MessagePlugin.error('系统繁忙，请稍后再试');
    }
  }, []);

  return {
    liveGroupList,
    addLiveGroup,
    editLiveGroup,
    deleteLiveGroup,
    getLiveGroupLiveInfo,
    queryLiveGroupLiveInfo,
    activeLiveGroupId,
    setActiveLiveGroupId,
    bindLiveGroup,
    error,
    loading,
  };
};
