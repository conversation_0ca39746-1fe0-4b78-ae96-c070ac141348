/**
 * <AUTHOR>
 * @date 2024/5/28 下午9:02
 * @desc index
 */

import ActionSuccess from '@/assets/images/create-condition-success.png';
import idooLoading from '@/assets/images/idooLoading.gif';
import { BaseScript } from '@/components/ScriptForm/type';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { UserInfoAtom } from '@/model/user';
import {
  getPresetId,
  openEditor,
  ORIGIN_TYPE,
} from '@/pages/Editor/common/openEditor';
import { TEMP_ID_MAP } from '@/pages/Editor/components/ADSaveButton/TempScriptMap';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';
import { ListProductsResponse } from '@/pb/api/DigitalManProc';
import { RespError } from '@/pb/config';
import { Development, MetaFeedbackSvr } from '@/pb/pb';
import { DipListItem } from '@/type';
import type { Script } from '@/type/pagedoo';
import { PromiseType } from '@/utils/type-util';
import { useUnmount } from 'ahooks';
import to from 'await-to-js';
import bowser from 'bowser';
import { useMemo, useState } from 'react';
import { getRecoil } from 'recoil-nexus';
import { Button, MessagePlugin } from 'tdesign-react';
import {
  ADSelectProductForm,
  IADSelectProductFormProps,
} from '../ADSelectProductForm';
import '../ADSelectProductForm/TextHighlight';
import './index.less';
import { useGenerateAISpeech } from './useADSpeech';

type SaveScriptRes = PromiseType<
  ReturnType<(typeof MetaFeedbackSvr)['SaveScript']>
>;

interface IProps {
  onClose: () => void;
  // 模版id
  templateId?: string;
  defaultDipItem?: DipListItem;
  defaultVoiceItem?: IVoiceItem;
  updateComponentData?: Required<BaseScript>['adExtendData']['updateComponentData'];
}

export type ProductOption = ListProductsResponse['data']['product_list'][0];

export interface ADLiveExtendFormValue {
  // productList: ProductOption[];
  liveMode: 'noAnchor' | 'hasAnchor';
}

// 打开编辑器的参数
export interface IOpenEditorParams {
  saveRes: SaveScriptRes;
  presetId: string;
}

export function ADSelectProductModal(props: IProps) {
  const { onClose, updateComponentData = {} } = props;

  const [loading, setLoading] = useState(false);
  // const [userInfo, setUserInfo] = useRecoilState(UserInfoAtom);

  // 用于弹框人工点击使用
  const [saveEditorParamsForConfirm, setSaveEditorParamsForConfirm] = useState<{
    saveResp: SaveScriptRes;
    presetId: string;
  } | null>(null);

  const userInfo = useMemo(() => {
    return getRecoil(UserInfoAtom);
  }, []);

  const account_id = useMemo(
    () => userInfo?.adExtend?.account_id || '',
    [userInfo]
  );

  const { generate: generateSpeech, cancel: stopGenerateSpeech } =
    useGenerateAISpeech();

  const onSubmit: IADSelectProductFormProps['onSubmit'] = async ({
    productList,
    liveMode,
  }) => {
    if (productList.length === 0) {
      void MessagePlugin.warning('请先选择商品');
      return;
    }
    let presetId = '';
    try {
      presetId = await getPresetId();
      if (!presetId) {
        void MessagePlugin.error('获取presetId失败');
        return;
      }
    } catch (e) {
      setLoading(false);
      void MessagePlugin.error('生成presetId失败');
      return;
    }

    const scriptInfo: Script = {
      backgroundImage: [],
      npcId: 'null',
      views: [],
      type: 'ad_script',
      size: [455, 812],
      adExtendData: {
        updateComponentData,
        liveId: presetId,
        liveMode,
        productList,
        templateId: props.templateId || '',
        defaultDipItem: props.defaultDipItem,
        defaultVoiceItem: props.defaultVoiceItem,
      },
    };
    console.debug(scriptInfo, 'scriptInfo');
    const research_name = `AD_Script_${new Date().getTime()}`;
    let saveRes: PromiseType<
      ReturnType<(typeof MetaFeedbackSvr)['SaveScript']>
    >;
    // const pollStateToOpenEditor = () => {
    //   if (loading) {
    //     setTimeout(pollStateToOpenEditor, 0);
    //     return;
    //   }
    //   console.log('before open editor');
    //   if (saveRes) {
    //     openEditor({
    //       system: EditorSystemMap.AD_LIVE,
    //       origin: 'ad_script',
    //       scriptId: saveRes.script_list[0].research_id,
    //       presetId,
    //     });
    //   }
    // };

    try {
      setLoading(true);
      const startTime = new Date().getTime();
      if (!props.templateId) {
        const [err, result] = await to(
          Development.GetUserGroupList({
            group_type: 4,
            user_id: account_id,
          })
        );
        if (err) {
          MessagePlugin.error('获取模版失败');
          throw err;
        }

        // 临时逻辑：针对特定商品id，使用与之对应的炫酷的直播间装修
        if (Object.keys(TEMP_ID_MAP).includes(productList[0].product_id)) {
          scriptInfo.adExtendData.templateId =
            TEMP_ID_MAP[productList[0].product_id];
        } else {
          scriptInfo.adExtendData.templateId = 'blank';
        }
        // scriptInfo.adExtendData.templateId =
        //   result.records?.[0]?.group_id ||
        //   'd58626fd-d898-4df7-bfa2-f43f7b29a02d';
      }
      saveRes = await MetaFeedbackSvr.SaveScript({
        research_name,
        script_info: [JSON.stringify(scriptInfo)],
        type: 'ad_script',
      });
      const scriptId = saveRes.script_list[0]?.research_id;
      if (scriptId) {
        //  这里可以开始调用话术生成 [presetId]
        const [err] = await to(
          generateSpeech(
            {
              productId: productList[0].product_id,
              liveID: presetId,
            },
            {
              generateOnly: true,
            }
          )
        );
        if (err) {
          let msg = '生成话术失败，请稍后重试';
          if (RespError.is(err)) {
            msg = `${msg} (${err.resultInfo || err.message})`;
          }
          void MessagePlugin.error(msg);
          return;
        }
        void MessagePlugin.success('创建成功');
        const browserName = bowser.parse(navigator.userAgent).browser.name;
        const needUserConfirm = ['safari'].some(
          (name) => browserName && browserName?.toLowerCase().indexOf(name) > -1
        );
        if (!needUserConfirm && new Date().getTime() - startTime <= 1800) {
          handleOpenEditor({
            saveRes,
            presetId,
          });
          // openEditor({
          //   system: EditorSystemMap.AD_LIVE,
          //   origin: 'ad_script',
          //   scriptId: saveRes.script_list[0].research_id,
          //   presetId,
          // });
          // onClose();
        } else {
          // 暂存起来供弹窗使用
          setSaveEditorParamsForConfirm({
            presetId,
            saveResp: saveRes,
          });
        }
      } else {
        void MessagePlugin.error('创建失败');
      }
    } catch (e) {
      console.error(e, '创建失败');
      void MessagePlugin.error('创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenEditor = (options: IOpenEditorParams) => {
    const { saveRes, presetId } = options;
    openEditor({
      system: EditorSystemMap.AD_LIVE,
      contentType: CONTENT_TYPE_MAP.Live.value,
      origin: ORIGIN_TYPE.AD_SCRIPT,
      scriptId: saveRes.script_list[0].research_id,
      presetId,
    });
    onClose();
  };

  useUnmount(() => {
    stopGenerateSpeech();
  });

  return (
    <div className="ad-select-product-modal-comp pagedoo-meta-live-global">
      <ADSelectProductForm onCancel={onClose} onSubmit={onSubmit} />
      {loading && (
        <div className="loading-wrap">
          <img src={idooLoading} width={83} height={60} alt="" />
          <div className="mt-32">根据商品信息正在生成中...</div>
        </div>
      )}
      {saveEditorParamsForConfirm && (
        <div className="loading-wrap">
          <img src={ActionSuccess} width={83} height={60} alt="" />
          <div className="mt-32">商品信息生成成功</div>
          <Button
            className="gradient-primary"
            onClick={() => {
              const params = saveEditorParamsForConfirm;
              setSaveEditorParamsForConfirm(null);
              handleOpenEditor({
                presetId: params.presetId,
                saveRes: params.saveResp,
              });
            }}
          >
            确定
          </Button>
        </div>
      )}
    </div>
  );
}

ADSelectProductModal.defaultProps = {
  templateId: '',
  defaultDipItem: undefined,
};
