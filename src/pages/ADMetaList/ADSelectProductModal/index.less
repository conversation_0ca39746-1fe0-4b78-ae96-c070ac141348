.ad-select-product-modal-comp {
  position: relative;
  .card {
    &-body {
      position: relative;
      padding: 20px 16px;

      .my-form-item {
        width: 100%;
      }

      .t-form__controls-content {
        flex-wrap: wrap;
        gap: 8px;
      }

      .t-form__item {
        width: 100%;
      }

      > .t-form__item {
        margin-bottom: 0;
      }

      .t-form__label {
        color: rgba(0, 0, 0, 0.40);
      }
    }

    .delete-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      font-size: 10px;
      background: #969397;
      cursor: pointer;
      border-radius: 0 4px 0 4px;
      color: #FFFFFF;
    }

    margin-bottom: 12px;
    border-radius: 4px;
    background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);
  }

  .loading-wrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    position: absolute;
    background: #FFFFFF;
  }
}
