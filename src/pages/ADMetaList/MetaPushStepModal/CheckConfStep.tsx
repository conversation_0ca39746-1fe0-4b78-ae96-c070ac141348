/**
 * <AUTHOR>
 * @date 2024/11/28 11:37
 * @desc CheckConfStep
 */

import React, {
  ForwardRefExoticComponent,
  RefAttributes,
  useEffect,
  useState,
} from 'react';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import {
  CheckCircleFilledIcon,
  ChevronRightIcon,
  CloseCircleFilledIcon,
  IconProps,
  InfoCircleFilledIcon,
  TimeFilledIcon,
} from 'tdesign-icons-react';
import { MessagePlugin } from 'tdesign-react';
import { MetaLivePrePush } from '@/pb/api/MetaLiveSvr';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { useMetaPushContext } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import to from 'await-to-js';
import { useMemoizedFn, useThrottleFn } from 'ahooks';
import {
  detectCPU,
  detectGPU,
  detectNetwork,
  DetectResult,
} from '@/pages/ADMetaList/MetaPushStepModal/detection';
import { runningInClient } from '@/utils/electron';
import {
  IMetaPushCheckItem as IMetaPushCheck,
  PushCheckStatus as CheckStatus,
  LIVE_CHECK_CONF,
} from './check-conf.typing';
import { useRenderType } from './useRenderType';
import { MetaPushError } from './meta-push.errors';

interface IProps {
  liveId: string;
  onClose: () => void;
  onNextStep: () => void;
}

// type CheckStatus = 'success' | 'warning' | 'error' | 'loading';

// type IMetaPushCheck = {
//   key: string;
//   title: string;
//   desc?: string;
//   status: CheckStatus;
//   hiddenAmend: boolean;
// };

// // 1. 数字人可用检测 key：vman_available_detection
// // 2. 话术检测 key: speech_detection
// // 3. 问答库QA数量检测 qa_quantity_detection
// export enum LIVE_CHECK_CONF {
//   VMAN_AVAILABLE_DETECTION = 'vman_available_detection',
//   SPEECH_DETECTION = 'speech_detection',
//   QA_QUANTITY_DETECTION = 'qa_quantity_detection',
//   Client_GPU_DETECTION = 'client_gpu_detection',
//   Client_CPU_DETECTION = 'client_cpu_detection',
//   Client_Network_DETECTION = 'client_network_detection',
// }

const LIVE_CONF: Record<
  LIVE_CHECK_CONF,
  {
    title: string;
    /** 展示去修改入口 */
    hiddenAmend?: boolean;
    detectFn?: () => Promise<DetectResult>;
  }
> = {
  [LIVE_CHECK_CONF.BASE_CHECK]: {
    title: '直播基础配置检测',
    hiddenAmend: true,
  },
  [LIVE_CHECK_CONF.VMAN_AVAILABLE_DETECTION]: {
    title: '主播可用检测',
  },
  [LIVE_CHECK_CONF.SPEECH_DETECTION]: {
    title: '话术检测',
    hiddenAmend: true,
  },
  [LIVE_CHECK_CONF.QA_QUANTITY_DETECTION]: {
    title: '问答库QA数量检测',
  },
  [LIVE_CHECK_CONF.Client_CPU_DETECTION]: {
    title: 'CPU',
    hiddenAmend: true,
    detectFn: detectCPU,
  },
  [LIVE_CHECK_CONF.Client_GPU_DETECTION]: {
    title: 'GPU',
    hiddenAmend: true,
    detectFn: detectGPU,
  },
  [LIVE_CHECK_CONF.Client_Network_DETECTION]: {
    title: '网络',
    hiddenAmend: true,
    detectFn: detectNetwork,
  },
  [LIVE_CHECK_CONF.LIVE_COUNT_DETECTION]: {
    title: '开播数量检测',
    hiddenAmend: true,
  },
  // [LIVE_CHECK_CONF.LIVE_PHONE_NOTICE_DETECTION]: {
  //   // title: ''
  // }
};

const ERROR_MSG: Record<string, string> = {
  1120000: '不正确的请求参数',
  1120001: '系统失败',
  1120004: '直播ID不存在',
  1122028: '直播间已开播',
  1122032: '问答库的回答数量不足5条',
  1122033: '话术正在生成中，请耐心等待',
  1126001: '主播正在其他直播间直播中',
  1122036: '已达到开播数量额度上限，暂时无法开播。',
};

const STATUS_MAP: Record<
  CheckStatus,
  {
    icon: ForwardRefExoticComponent<IconProps & RefAttributes<SVGElement>>;
    color: string;
    text: string;
  }
> = {
  success: {
    icon: CheckCircleFilledIcon,
    color: '#00A870',
    text: '通过',
  },
  error: {
    icon: CloseCircleFilledIcon,
    color: '#C9353F',
    text: '不通过',
  },
  warning: {
    icon: InfoCircleFilledIcon,
    color: '#ED7B2F',
    text: '有风险',
  },
  loading: {
    icon: TimeFilledIcon,
    color: '#92A8D5',
    text: '检测中',
  },
};

export function CheckConfStep(props: IProps) {
  const { liveId, onClose, onNextStep } = props;
  const { liveConf, linkToEditor } = useMetaPushContext();
  const [checkStatus, setCheckStatus] = useState<
    'init' | 'loading' | 'fail' | 'success'
  >('init');

  const [checkList, setCheckList] = useState<IMetaPushCheck[]>(
    MatchedGlobalConfigItem.liveConf.livePushCheckList.map((item) => ({
      key: item,
      title: LIVE_CONF[item].title,
      desc: '',
      status: 'loading',
      hiddenAmend: LIVE_CONF[item].hiddenAmend || false,
    }))
  );
  const { decideFinalRenderType } = useRenderType();
  console.log(checkList, 'checkList');

  const { run: doCheck } = useThrottleFn(
    () => {
      setCheckList(
        checkList.map((item) => ({ ...item, status: 'loading', desc: '' }))
      );
      void checkMetaPushConf();
    },
    { wait: 500 }
  );

  const checkMetaPushConf = useMemoizedFn(async () => {
    if (!liveConf) return;
    setCheckStatus('loading');
    // 服务器支持检测的项目名称列表
    const serverDetectList = [
      LIVE_CHECK_CONF.BASE_CHECK,
      LIVE_CHECK_CONF.VMAN_AVAILABLE_DETECTION,
      LIVE_CHECK_CONF.SPEECH_DETECTION,
      LIVE_CHECK_CONF.QA_QUANTITY_DETECTION,
      LIVE_CHECK_CONF.LIVE_COUNT_DETECTION,
    ];
    // 并发检查非服务端检查的项目
    void (async () => {
      const restDetectList =
        MatchedGlobalConfigItem.liveConf.livePushCheckList.filter(
          (item) => !serverDetectList.includes(item)
        );
      if (!restDetectList.length) return;
      for (const i of restDetectList) {
        try {
          const fn = LIVE_CONF[i].detectFn;
          if (!fn) throw new Error('检测方法不存在！');
          const { tier, score, desc } = await fn();
          console.log('tier', i, tier, desc, score);
          setCheckList((list) =>
            list.map((item) => {
              if (item.key !== i) return item;
              return {
                ...item,
                status: ['error', 'error', 'warning', 'success'][
                  tier
                ] as CheckStatus,
                desc,
              };
            })
          );
        } catch (e) {
          setCheckList((list) =>
            list.map((item) => {
              if (item.key !== i) return item;
              return { ...item, status: 'error', desc: `${e}` };
            })
          );
        }
      }
      console.log(restDetectList, 'restDetectList');
    })();
    const [err, res] = await to(
      MetaLivePrePush({
        check_list: MatchedGlobalConfigItem.liveConf.livePushCheckList.filter(
          (item) => serverDetectList.includes(item)
        ),
        meta_live_id: liveId,
        virtual_man_live_data:
          liveConf.virtualmanMetaLiveData.map((item) => ({
            platform_account_id: item.platformAcctId || '',
            virtualman_key: item.virtualManKey || '',
            platform: item.platform,
          })) || [],
      })
    );
    if (err || !res?.check_result.length) {
      void MessagePlugin.error('检测直播配置失败');
      console.error(err);
      setCheckList((checkList) =>
        checkList.map((item) => {
          if (serverDetectList.includes(item.key as LIVE_CHECK_CONF))
            return {
              ...item,
              status: 'error',
              desc: `${err}`,
            };
          return item;
        })
      );
      // setCheckStatus('fail');
      return;
    }
    setCheckList((checkList) =>
      checkList.map((item) => {
        const { key } = item;
        const findRes = res.check_result.find((resItem) => resItem.key === key);
        console.log(findRes, 'findResfindRes');
        return findRes
          ? {
              ...item,
              desc:
                findRes.code === '0'
                  ? ''
                  : ERROR_MSG[findRes.code] ??
                    findRes.info ??
                    '检测失败，请稍后重试',

              status: findRes.code === '0' ? 'success' : 'error',
            }
          : item;
      })
    );
  });
  useEffect(() => {
    if (checkList.some((i) => i.status === 'loading')) {
      setCheckStatus('loading');
    } else if (checkList.some((i) => i.status === 'error')) {
      setCheckStatus('fail');
    } else if (
      checkList.every((i) => i.status === 'success' || i.status === 'warning')
    ) {
      setCheckStatus('success');
    }
  }, [checkList]);

  useEffect(() => {
    doCheck();
  }, [doCheck]);

  // 检测整个页面重新出现在可视区的时候，重新检测
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        void checkMetaPushConf();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    // 清理事件监听器
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkMetaPushConf]);

  const nextStep = () => {
    const checkNotPass = checkList.some((item) => item.status === 'error');
    if (checkNotPass) {
      void MessagePlugin.error('存在未通过的检测项，请修改后再开播');
      return;
    }
    console.log(checkList, '开播前检测结果--LIVEId: ', liveId);
    decideFinalRenderType(checkList)
      .then(() => {
        onNextStep();
      })
      .catch((e: Error | MetaPushError) => {
        console.error('开播前决策渲染类型失败 ', e.message);
        let msg = '开播检查失败';
        if (e.name === 'MetaPushError') msg = e.message;
        MessagePlugin.error(msg);
      });
  };

  const toAmend = () => {
    linkToEditor(liveId);
  };

  return (
    <div className="relative min-h-[500px]">
      <div className="flex flex-col gap-[12px]">
        {checkList.map((item) => {
          const { icon: Icon, color, text } = STATUS_MAP[item.status];
          return (
            <div
              key={item.key}
              className="bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)] py-[18px] pl-20 pr-[36px] rounded-4"
            >
              <header className="flex items-center h-[22px]">
                <div className="flex-1 text-[16px] font-medium text-black">
                  {item.title}
                </div>
                <div className="flex items-center w-[80px]">
                  <Icon size={18} style={{ color }} />
                  <span className="ml-8" style={{ color }}>
                    {text}
                  </span>
                </div>
              </header>

              {/*  错误描述*/}
              <footer x-if={item.desc} className="mt-8 max-w-[92%]">
                <span>{item.desc}</span>
                <span
                  x-if={!item.hiddenAmend}
                  onClick={() => {
                    toAmend();
                  }}
                  className="inline-flex items-center ml-[10px] gradient-primary-text cursor-pointer"
                >
                  去修改
                  <ChevronRightIcon style={{ color: '#8649ff' }} />
                </span>
              </footer>
            </div>
          );
        })}
      </div>
      <div className="h-[48px]" />
      <StepModalFooter
        primaryBtn="下一步"
        primaryDisabled={checkStatus === 'init' || checkStatus === 'fail'}
        primaryLoading={checkStatus === 'loading'}
        subBtn="重新检测"
        onPrimary={nextStep}
        onSub={doCheck}
        onClose={onClose}
      />
    </div>
  );
}
