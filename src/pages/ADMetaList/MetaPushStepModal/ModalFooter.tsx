/**
 * <AUTHOR>
 * @date 2024/11/26 21:12
 * @desc ModalFooter
 */

import React, { ReactElement } from 'react';
import { Button } from 'tdesign-react';

interface IProps {
  primaryBtn: string | ReactElement;
  onPrimary?: () => void;
  primaryLoading?: boolean;
  primaryDisabled?: boolean;
  subBtn?: string;
  onSub?: () => void;
  onClose?: () => void;
}

export function StepModalFooter(props: IProps) {
  const {
    primaryBtn,
    subBtn,
    primaryDisabled,
    primaryLoading,
    onPrimary,
    onSub,
    onClose,
  } = props;
  return (
    <div
      className="flex justify-end w-full gap-[8px] absolute bottom-0 right-0 pt-8"
      style={{ borderTop: '1px solid #e7e7e7' }}
    >
      <Button
        x-if={onClose}
        theme="default"
        className="gradient-default"
        onClick={onClose}
      >
        取消
      </Button>
      <Button
        disabled={primaryLoading}
        x-if={subBtn && onSub}
        theme="default"
        className="gradient-default"
        onClick={onSub}
      >
        {subBtn}
      </Button>
      {typeof primaryBtn === 'string' ? (
        <Button
          x-if={primaryBtn}
          loading={primaryLoading}
          disabled={primaryLoading || primaryDisabled}
          theme="primary"
          className="gradient-primary"
          onClick={() => {
            onPrimary?.();
          }}
        >
          {primaryBtn}
        </Button>
      ) : (
        primaryBtn
      )}
    </div>
  );
}
