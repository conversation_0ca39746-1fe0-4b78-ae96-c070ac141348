/**
 * <AUTHOR>
 * @date 2024/11/28
 * @desc PreviewStep
 */

import React, { useEffect, useMemo, useState } from 'react';
import { MessagePlugin } from 'tdesign-react';
import { ContentDetailQuery, ContentModify } from '@/pb/api/ContentSvr';
import { RespType } from '@/pb/config';
import { PlayConfig } from '@/type/pagedoo';
import { updateVersion } from '@/pages/Editor/common/updateComponent';
import { generateHtmlPage } from '@/pages/Editor/ADLive/defaultOptions';
import { GemsPreviewer } from '@/components/GemsPreviewer';
import { cloneDeep } from 'lodash-es';
import { generateHtml } from '@/pages/Editor/common/editor';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import { useMetaPushContext } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { runningInClient } from '@/utils/electron';
import to from 'await-to-js';

interface IProps {
  liveId: string;
  onClose: () => void;
  onNextStep: () => void;
}

type ContentData = RespType<typeof ContentDetailQuery>;

export function PreviewStep(props: IProps) {
  const { onClose, liveId, onNextStep } = props;
  const [contentData, setContentData] = useState<ContentData>();
  const { livePush, liveConf, linkToEditor } = useMetaPushContext();

  useEffect(() => {
    const init = async () => {
      if (liveId) {
        const contentData = await ContentDetailQuery({
          content_id: liveId,
        });
        setContentData(contentData);
      }
    };
    void init();
  }, [liveId]);

  const globalData = useMemo(() => {
    if (!contentData) return;

    // 从内容详情中获取脚本
    const script = JSON.parse(contentData.extend.playScript) as PlayConfig;
    // 编辑情况下直接刷到最新的组件版本
    const updateScript = updateVersion(script);

    const pages = cloneDeep(generateHtmlPage);

    return {
      'pagedoo-play-script': updateScript,
      'pagedoo-live': {
        id: contentData.content_id,
      },
      pages,
    };
  }, [contentData]);

  const previewGlobalData = useMemo(() => {
    if (!globalData) return;
    const cloneData = cloneDeep(globalData);
    if (cloneData['pagedoo-live']) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (cloneData['pagedoo-live'] as any).preview = true;
    }
    if (cloneData.pages) {
      const { pages } = cloneData;
      // 不自动播放
      if (pages[0].data.components[0].children?.[0]?.id?.endsWith('Director')) {
        pages[0].data.components[0].children[0].data.play = false;
      }
    }
    return cloneData;
  }, [globalData]);

  const handleSave = async () => {
    if (!contentData || !globalData) return;
    const poster_url = contentData.page_list[0].poster_url || '';
    const { extend } = contentData;
    const html = await generateHtml({
      insertData: {
        'pagedoo-play-script': globalData['pagedoo-play-script'],
        'pagedoo-live': globalData['pagedoo-live'],
        pages: generateHtmlPage,
      },
      pageList: [],
      componentLibList: [],
      uin: liveId,
    });
    const page_list = contentData.page_list.map((item, index) => ({
      data: '{}',
      page_id: item.page_id,
      html: index === contentData.page_list.length - 1 ? html : '',
      poster_url: item.poster_url,
      show: item.show,
      terminal_id: item.terminal_id,
    }));
    await ContentModify({
      component_list: [],
      content_id: liveId,
      poster_url,
      content_title: contentData.content_name || '',
      page_list,
      extend,
    });
  };

  const nextStep = async () => {
    if (!liveConf) {
      return;
    }
    const [err] = await to(handleSave());
    if (err) {
      void MessagePlugin.error('直播间更新组件失败');
      console.error(err, '直播间更新组件失败');
      return;
    }
    // 客户端且不需要互动的话，在这里就调用开播【因为客户端不用走到填写直播配置的step】
    if (runningInClient && !liveConf.needInteractionLogin) {
      await livePush({
        callback: onNextStep,
      });
    } else {
      onNextStep();
    }
  };

  return (
    <div className="relative min-h-[500px]">
      <div className="flex justify-center bg-black h-[463px] w-[834px]">
        {previewGlobalData && (
          <GemsPreviewer
            width={262}
            height={463}
            globalData={previewGlobalData}
          />
        )}
      </div>
      <div className="h-[48px]" />
      <StepModalFooter
        primaryBtn="下一步"
        subBtn="前往编辑"
        onSub={() => {
          linkToEditor(liveId);
        }}
        onPrimary={nextStep}
        onClose={onClose}
      />
    </div>
  );
}
