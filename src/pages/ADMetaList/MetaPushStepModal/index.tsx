/**
 * <AUTHOR>
 * @date 2024/11/26 15:23
 * @desc index
 */

import { Dialog, Loading, Steps } from 'tdesign-react';
import { META_LIVE_PUSH_STEP } from '@/pages/ADMetaList/MetaPushStepModal/useMetaPushStep';
import { CheckConfStep } from '@/pages/ADMetaList/MetaPushStepModal/CheckConfStep';
import { useMetaPushContext } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { PreviewStep } from '@/pages/ADMetaList/MetaPushStepModal/PreviewStep';
import { ClientDriverICheckStep } from '@/pages/ADMetaList/MetaPushStepModal/ClientDriverICheckStep';
import { MetaPushGuideStep } from '@/pages/ADMetaList/MetaPushStepModal/MetaPushGuideStep';
import { MetaPushConfStep } from '@/pages/ADMetaList/MetaPushStepModal/MetaPushConfStep';
import { MetaPushQrCodeStep } from '@/pages/ADMetaList/MetaPushStepModal/MetaPushQRCodeStep';
import { useState } from 'react';

const { StepItem } = Steps;

interface IProps {
  liveId: string;
  onClose: () => void;
}

export function MetaPushStepModal(props: IProps) {
  const { onClose } = props;
  const { steps, currentStep, setCurrentStep } = useMetaPushContext();
  const [dialogCloseBtn, setDialogCloseBtn] = useState(true);

  const nextStep = () => {
    const findIndex = steps.findIndex((item) => item.value === currentStep);
    const nextStepValue = steps[findIndex + 1]?.value;
    if (findIndex !== -1 && nextStepValue) {
      setCurrentStep(nextStepValue as META_LIVE_PUSH_STEP);
    }
  };
  return (
    <Dialog
      closeOnEscKeydown={false}
      closeOnOverlayClick={false}
      closeBtn={dialogCloseBtn}
      destroyOnClose
      header="开播"
      width={900}
      placement="center"
      // top={60}
      visible
      onClose={onClose}
      footer={null}
    >
      <div className="pagedoo-meta-live-global">
        <Steps
          readonly
          style={{ marginBottom: 24 }}
          current={currentStep}
          onChange={(val) => setCurrentStep(val as META_LIVE_PUSH_STEP)}
        >
          {steps.map((item) => {
            return (
              <StepItem
                key={item.value}
                status={item?.status}
                title={item.title}
                value={item.value}
                content={item.content}
              />
            );
          })}
        </Steps>
        <div
          x-if={currentStep === META_LIVE_PUSH_STEP.INIT}
          className="h-[500px] flex justify-center items-center"
        >
          <Loading loading text="开播步骤加载中..." size="small" />
        </div>
        {/* 客户端驱动检查*/}
        <ClientDriverICheckStep
          x-if={currentStep === META_LIVE_PUSH_STEP.DRIVER_INSTALL}
          onClose={onClose}
          onSuccess={() => {
            setDialogCloseBtn(false);
          }}
        />
        {/* 开播前检查*/}
        <CheckConfStep
          x-if={currentStep === META_LIVE_PUSH_STEP.CHECK_CONF}
          onNextStep={() => {
            nextStep();
          }}
          {...props}
        />
        {/* 直播预览*/}
        <PreviewStep
          x-if={currentStep === META_LIVE_PUSH_STEP.LIVE_PREVIEW}
          onNextStep={() => {
            nextStep();
          }}
          {...props}
        />
        {/* 直播配置*/}
        <MetaPushConfStep
          x-if={currentStep === META_LIVE_PUSH_STEP.LIVE_SETTING}
          {...props}
          onNextStep={() => {
            nextStep();
          }}
        />
        {/* 扫码配置*/}
        <MetaPushQrCodeStep
          x-if={currentStep === META_LIVE_PUSH_STEP.INTERACTION_QRCODE}
          {...props}
          onNextStep={() => {
            nextStep();
          }}
        />
        {/* 开播流程指引*/}
        <MetaPushGuideStep
          x-if={currentStep === META_LIVE_PUSH_STEP.SUCCESS_PAGE}
          onClose={onClose}
        />
      </div>
    </Dialog>
  );
}
