/**
 * <AUTHOR>
 * @date 2024/11/26 15:50
 * @desc useMetaPushStep
 */
import { LiveConf } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { runningInClient } from '@/utils/electron';
import to from 'await-to-js';
import { cloneDeep } from 'lodash-es';
import { useEffect, useState } from 'react';
import { MessagePlugin } from 'tdesign-react';
import { LiveConf } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { checkDriver } from '@/components/ClientDialog/utils';

export type MetaPushStepItem = {
  status?: StepStatus;
  content?: string;
  title: string;
  value: string;
};

export enum META_LIVE_PUSH_STEP {
  INIT = 'init',
  DRIVER_INSTALL = 'driverInstall',
  CHECK_CONF = 'checkConf',
  LIVE_PREVIEW = 'livePreview',
  LIVE_SETTING = 'liveSetting',
  INTERACTION_QRCODE = 'interactionQrcode',
  SUCCESS_PAGE = 'SuccessPage',
}

const DEFAULT_STEP: MetaPushStepItem[] = [
  // { title: '开播检测', value: META_LIVE_PUSH_STEP.CHECK_CONF },
  // { title: '开播预览', value: META_LIVE_PUSH_STEP.LIVE_PREVIEW },
];

export const useMetaPushStep = (liveId: string, liveConf?: LiveConf) => {
  const [steps, setSteps] = useState<MetaPushStepItem[]>([]);
  const [currentStep, setCurrentStep] = useState<META_LIVE_PUSH_STEP>(
    META_LIVE_PUSH_STEP.INIT
  );

  useEffect(() => {
    const init = async () => {
      if (!liveId || !liveConf) return;
      const currentSteps = cloneDeep(DEFAULT_STEP);
      // 根据运行终端增减步骤
      if (runningInClient) {
        const [checkErr, checkRes] = await to(checkDriver());
        if (checkErr) {
          void MessagePlugin.error('获取直播间信息失败');
          console.error(checkErr, '获取驱动配置失败');
          setSteps([]);
          return;
        }
        // 客户端场景未安装驱动需要装摄像头驱动
        if (!checkRes?.camera) {
          currentSteps.unshift({
            title: '驱动安装',
            value: META_LIVE_PUSH_STEP.DRIVER_INSTALL,
          });
        }
      } else {
        //   非客户端场景需要一个开播设置
        currentSteps.push({
          title: '开播设置',
          value: META_LIVE_PUSH_STEP.LIVE_SETTING,
        });
      }
      // 需要互动，增加扫码
      if (liveConf?.needInteractionLogin) {
        currentSteps.push({
          title: '互动登录扫码',
          value: META_LIVE_PUSH_STEP.INTERACTION_QRCODE,
        });
      }
      currentSteps.push({
        title: '开播指引',
        value: META_LIVE_PUSH_STEP.SUCCESS_PAGE,
      });
      setSteps(currentSteps);
      setCurrentStep(currentSteps?.[0].value as META_LIVE_PUSH_STEP);
    };

    init().then();
  }, [liveConf, liveId]);

  return { steps, currentStep, setCurrentStep };
};
