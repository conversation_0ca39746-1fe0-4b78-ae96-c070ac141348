/**
 * <AUTHOR>
 * @date 2024/11/29 14:21
 * @desc MetaPushConfStep
 */

import {
  FlattenSelect,
  IIFlattenSelectOption,
} from '@/components/FlattenSelect';
import { BlockSelectItem } from '@/components/LiveQRCode/BlockSelectItem';
import flattenSelectStyle from '@/components/LiveQRCode/flatten-select.module.less';
import {
  HostedLiveConfig,
  YoutubeHostedLiveConfig,
} from '@/components/LiveQRCode/HostedLiveConfig';
import { ThirdPartyLivePushPermission } from '@/components/LiveQRCode/typings';
import {
  IMetaPushParams,
  useMetaPushContext,
} from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import { PUSH_METHOD } from '@/pages/Editor/MetaHumanLive/typings';
import { LiveResourceList, GetPushUrl, GetLiveUrl } from '@/pb/api/MetaLiveSvr';
import { useRequest } from 'ahooks';
import { Fragment, useCallback, useState } from 'react';
import {
  Form,
  FormRules,
  ImageViewer,
  Loading,
  MessagePlugin,
  Select,
  Textarea,
} from 'tdesign-react';
import './metaPushConfStep.less';
import to from 'await-to-js';

const { FormItem } = Form;

interface IProps {
  liveId: string;
  onClose: () => void;
  onNextStep: () => void;
}

interface FormValues {
  dst_push_stream_url: string;
  dst_push_secret?: string;
}

export function MetaPushConfStep(props: IProps) {
  const { onClose, onNextStep } = props;
  const { livePush, liveConf, setRemotePushParams, liveId } =
    useMetaPushContext();

  const [form] = Form.useForm();
  const [pushPermission, setPushPermission] =
    useState<ThirdPartyLivePushPermission>('allow');

  const { data: platforms, loading: platformsLoading } = useRequest(
    async () => {
      const res = await LiveResourceList({}).catch(() => ({
        resources: [{ name: '视频号', id: 'sph' }],
      }));
      return res.resources.map((resource) => ({
        label: resource.name,
        value: resource.id,
      }));
    },
    {
      throttleWait: 500,
    }
  );
  const [platform, setPlatform] = useState<string>('sph');

  const renderPermissionSelectBlock = useCallback<
    Required<IIFlattenSelectOption<ThirdPartyLivePushPermission>>['render']
  >((options) => {
    return (
      <BlockSelectItem
        text={options.label}
        active={options.active}
        onClick={() => options.onSelect(options.value)}
      />
    );
  }, []);

  const rules: FormRules<FormValues> = {
    dst_push_stream_url: [
      { whitespace: true, message: '地址不能为空' },
      { required: true, message: '地址不能为空', type: 'error' },
    ],
    dst_push_secret: [
      { whitespace: true, message: '密钥不能为空' },
      { required: true, message: '密钥不能为空', type: 'error' },
    ],
  };

  const handleSubmit = async () => {
    if (!liveConf || !(await formValidate())) {
      return;
    }
    const remoteParams = form.getFieldsValue(true) as FormValues;
    // 视频号开播推两路视频流（视频号、云开播），拉流地址为云开播的拉流地址
    const [err, result] = await to(
      Promise.all([
        GetPushUrl({
          meta_live_id: liveId,
        }),
        GetLiveUrl({
          meta_live_id: liveId,
        }),
      ])
    );
    if (err) {
      MessagePlugin.error('获取拉流地址失败，请重新扫码');
      return;
    }
    const [{ push_url }, { live_url }] = result;
    const pushUrl = new URL(push_url.replace(/^rtmp:/, 'http:'));
    // 不需要互动的话，在这里就调用开播
    if (!liveConf.needInteractionLogin) {
      await livePush({
        pushMethod: PUSH_METHOD.LIVE_PLATFORM,
        remoteParams: {
          ...remoteParams,
          watch_push_stream_url: `rtmp://${pushUrl.host}${pushUrl.pathname}${pushUrl.search}`,
          dst_pull_stream_url: live_url,
        },
        callback: onNextStep,
      });
    } else {
      const newRemoteParams: IMetaPushParams['remoteParams'] = {
        dst_push_stream_url: remoteParams.dst_push_stream_url,
        dst_pull_stream_url: live_url,
        dst_push_secret: remoteParams.dst_push_secret,
        watch_push_stream_url: `rtmp://${pushUrl.host}${pushUrl.pathname}${pushUrl.search}`,
      };
      setRemotePushParams(newRemoteParams);
      onNextStep();
    }
  };

  const formValidate = async () => {
    const validate = await form.validate();
    return validate === true;
  };

  // 直接开播，不进行下一步了
  const handleDirectPush = ({
    pushUrl,
    pullUrl,
  }: {
    pushUrl: string;
    pullUrl: string;
  }) => {
    const url = new URL(pushUrl.replace(/^rtmp:/, 'http:'));
    const fakeFormValues: FormValues & IMetaPushParams['remoteParams'] = {
      dst_push_stream_url: `rtmp://${url.host}${url.pathname}`,
      dst_pull_stream_url: pullUrl,
      dst_push_secret: `${url.search}`,
    };
    livePush({
      pushMethod: PUSH_METHOD.LIVE_HOSTED,
      remoteParams: fakeFormValues,
      options: {
        is_crawl_barrage: true,
      },
      callback: () => {
        void MessagePlugin.success('开播成功');
        onClose();
      },
    });
    // .then(() => {
    //   直接跳转到最后一步，不管后面有没有扫码环节
    // setCurrentStep(
    //   steps[steps.length - 1].value as META_LIVE_PUSH_STEP
    // );
    // });
  };

  return (
    <div className="relative min-h-[500px] live_qrcode_comp">
      <div
        x-if={platformsLoading}
        className="h-[500px] flex justify-center items-center"
      >
        <Loading loading text="开播平台加载中..." size="small" />
      </div>
      <div x-else className="w-[700px]" style={{ margin: '0 auto' }}>
        <div className="live_qrcode_header">
          <span>开播平台</span>
          <Select
            value={platform}
            onChange={(value) => setPlatform(value as string)}
            style={{ width: '80%' }}
            clearable
            options={platforms || []}
          />
        </div>
        <>
          <div className="live_qrcode_header" x-if={platform === 'sph'}>
            <span>推流权限</span>
            <FlattenSelect<ThirdPartyLivePushPermission>
              className={flattenSelectStyle.flattenSelectContainer}
              options={[
                {
                  label: '有直播推流权限',
                  value: 'allow',
                  key: 'allow',
                  render: renderPermissionSelectBlock,
                },
                {
                  label: '无直播推流权限',
                  value: 'deny',
                  key: 'deny',
                  render: renderPermissionSelectBlock,
                },
              ]}
              value={pushPermission}
              onChange={async (v) => setPushPermission(v)}
            />
          </div>
          <Fragment x-if={platform === 'sph' && pushPermission === 'allow'}>
            <div className="live_qrcode_container">
              <div className="form">
                <Form
                  form={form}
                  layout="vertical"
                  labelWidth={60}
                  labelAlign="top"
                  rules={rules}
                >
                  <FormItem label="推流地址" name="dst_push_stream_url">
                    <Textarea
                      placeholder="请输入推流的地址"
                      autosize={{ minRows: 6, maxRows: 10 }}
                      style={{ width: '266px' }}
                    />
                  </FormItem>
                  <FormItem label="推流密钥" name="dst_push_secret">
                    <Textarea
                      placeholder="请输入推流的密钥"
                      autosize={{ minRows: 6, maxRows: 10 }}
                      style={{ width: '266px' }}
                    />
                  </FormItem>
                </Form>
              </div>
              <div className="divider" />
              <div className="step-guide">
                <h1>开播引导</h1>
                <ul>
                  <li>
                    <div>
                      <p>前往视频号后台并登录</p>
                      <p className="tip">
                        前往“
                        <span
                          className="text-[#0047F9]"
                          style={{
                            cursor: 'pointer',
                          }}
                          onClick={() => {
                            window.open('https://channels.weixin.qq.com/');
                          }}
                        >
                          视频号助手
                        </span>
                        ”
                      </p>
                    </div>
                  </li>
                  <li>
                    <div>
                      <p>进入“直播管理”创建直播，获取密钥</p>
                      <ImageViewer
                        trigger={({ open }) => (
                          <img
                            width={129}
                            height={78}
                            src="https://avatarcdn.pay.qq.com/material/broadcast-source.png"
                            alt=""
                            className="cursor-pointer"
                            onClick={open}
                          />
                        )}
                        images={[
                          'https://avatarcdn.pay.qq.com/material/broadcast-source.png',
                        ]}
                      />
                    </div>
                  </li>
                  <li>
                    <div>
                      <p>将密钥粘贴至左侧输入框，点击“确定”</p>
                      <p className="tip">👈请将密钥粘贴至左侧</p>
                    </div>
                  </li>
                  <li>
                    <div>
                      <p>返回视频号后台，点击“开播”</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </Fragment>
          <YoutubeHostedLiveConfig
            x-if={platform === 'youtube' && liveId}
            metaLiveID={liveId}
            onCancel={onClose}
            onConfirm={handleDirectPush}
          />
          <Fragment x-if={platform === 'sph' && pushPermission === 'allow'}>
            <div className="h-[48px]" />
            <StepModalFooter
              primaryBtn="下一步"
              onPrimary={handleSubmit}
              onClose={onClose}
            />
          </Fragment>
        </>
        <HostedLiveConfig
          x-if={platform === 'sph' && pushPermission === 'deny' && liveId}
          metaLiveID={liveId}
          onCancel={onClose}
          onConfirm={handleDirectPush}
        />
      </div>
    </div>
  );
}
