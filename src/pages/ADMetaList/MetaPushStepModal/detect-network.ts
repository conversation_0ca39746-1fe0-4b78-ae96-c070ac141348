import { getBaseUrl } from '@/pb/config';

// 获取下载速度 MB/s
export function getDownloadSpeed(): Promise<number> {
  const url = `https://pagedoo-release-1258344706.cos.ap-guangzhou.myqcloud.com/material/@platform/b3215c06647bc550406a9c8ccc378756.zip?t=${Math.random()}`; // 你的下载URL
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    let startTime = 0;
    let lastDownloaded = 0;
    let isTimeout = false;

    const timeoutId = setTimeout(() => {
      isTimeout = true;
      xhr.abort();
      const duration = Date.now() - startTime;
      const speed = lastDownloaded / (duration / 1000);
      resolve(speed / 1024 / 1024);
    }, 1000);

    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        clearTimeout(timeoutId);
        if (xhr.status === 200) {
          const duration = Date.now() - startTime;
          const speed = lastDownloaded / (duration / 1000);
          if (!isTimeout) {
            resolve(speed / 1024 / 1024);
          }
        } else {
          if (!isTimeout) {
            return reject(new Error('Download failed'));
          }
        }
      }
    };

    xhr.onerror = function () {
      if (!isTimeout) {
        reject(new Error('Download error'));
      }
    };

    xhr.onprogress = function (event) {
      if (event.lengthComputable) {
        if (!startTime) {
          startTime = Date.now(); // 在第一次进度事件时记录开始时间
        }
        lastDownloaded = event.loaded;
      }
    };

    xhr.open('GET', url, true);
    xhr.send();
  });
}

// 获取延迟的ms数
export const getDelay = function () {
  return new Promise<number>((resolve) => {
    const delayList: number[] = [];

    function makeRangeIterator(start = 0, end = Infinity, step = 1) {
      let nextIndex = start;
      return {
        next: () => {
          return new Promise<{ delay?: number; done: boolean }>((resolve) => {
            if (nextIndex < end) {
              const startTime = Date.now();
              let endTime: number | null = null;
              let delay: number | null = null;
              const img = document.createElement('img');
              img.onerror = () => {
                endTime = Date.now();
                delay = endTime - startTime;
                delayList.push(delay);
                resolve({ delay, done: false });
              };
              img.src = `/api/?network-speed-test-delay&t=${Date.now()}`;
              nextIndex += 1;
            } else {
              resolve({ done: true });
            }
          });
        },
      };
    }

    const it = makeRangeIterator(1, 4, 1);

    async function init() {
      let result = await it.next();
      while (!result.done) {
        result = await it.next();
      }
      return parseFloat(
        (delayList.reduce((a, b) => a + b) / delayList.length).toFixed(2)
      );
    }

    init().then((res) => resolve(res));
  });
};

// 获取上传速度 MB/s
export function getUploadSpeed(): Promise<number> {
  const size = 1024 * 1024 * 2; // 2MB
  const blob = new Blob([new Uint8Array(size)], {
    type: 'application/octet-stream',
  });
  const url = `${getBaseUrl()}/api/?network-speed-test-upload`;
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    let startTime = 0;
    let lastUploaded = 0;
    // let lastUploadedTime = 0;
    let isTimeout = false;

    const timeoutId = setTimeout(() => {
      isTimeout = true;
      xhr.abort();
      const duration = Date.now() - startTime;
      const speed = lastUploaded / (duration / 1000);
      resolve(speed / 1024 / 1024);
    }, 1500);
    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        clearTimeout(timeoutId);
        if (xhr.status === 200) {
        } else {
          if (!isTimeout) {
            return reject(new Error(`Upload speed test fail:${xhr.status}`));
          }
        }
        const duration = Date.now() - startTime;
        const speed = lastUploaded / (duration / 1000);
        if (!isTimeout) {
          resolve(speed / 1024 / 1024);
        }
      }
    };

    xhr.onerror = function () {
      if (!isTimeout) {
        reject(new Error('Upload error'));
      }
    };

    xhr.upload.onprogress = function (event) {
      if (event.lengthComputable) {
        // const percentComplete = event.loaded / event.total * 100;
        if (!startTime) {
          startTime = Date.now(); // 在第一次进度事件时记录开始时间
        }
        // const speed = event.loaded / ((Date.now() - startTime) / 1000); // 计算每秒上传的字节数
        lastUploaded = event.loaded;
        // lastUploadedTime = Date.now();
      }
    };

    xhr.open('POST', url, true);
    xhr.setRequestHeader('Content-Type', 'application/octet-stream');
    xhr.send(blob);
  });
}
