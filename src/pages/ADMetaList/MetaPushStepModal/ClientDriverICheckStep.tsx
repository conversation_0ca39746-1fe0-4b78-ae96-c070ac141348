/**
 * <AUTHOR>
 * @date 2024/11/28 22:45
 * @desc ClientDriverICheckStep
 */

import { checkDriver } from '@/components/ClientDialog/utils';
import { CommonDialog } from '@/components/CommonDialog';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import { useMemo, useState } from 'react';
import { Button, MessagePlugin, Popconfirm } from 'tdesign-react';

interface IProps {
  onClose: () => void;
  onSuccess: () => void;
}

const INSTALL_DRIVER = [
  {
    key: 'virtualCamera',
    icon: 'https://avatarcdn.pay.qq.com/material/98359bbfff098a2a34a7ed203f39d72f.png',
    title: '虚拟摄像头驱动',
    desc: '基于软件技术虚拟摄像头，将数字人画面输出到虚拟摄像头，帮助您在直播平台拉取到数字人画面。',
  },
  // {
  //   key: 'virtualVoice',
  //   icon: 'https://avatarcdn.pay.qq.com/material/21d930b354477f3636206621e309fca2.png',
  //   title: '虚拟声卡',
  //   desc: '基于软件技术虚拟麦克风或者扬声器硬件，帮助您在直播平台拉取到数字人音频。',
  // },
];

enum INSTALL_STATUS {
  INIT = 'init',
  INSTALLING = 'installing',
  SUCCESS = 'success',
  FAIL = 'fail',
}

const buttonText = {
  [INSTALL_STATUS.INIT]: '一键安装',
  [INSTALL_STATUS.INSTALLING]: '安装中...',
  [INSTALL_STATUS.SUCCESS]: '重启电脑',
  [INSTALL_STATUS.FAIL]: '重新安装',
};

export function ClientDriverICheckStep(props: IProps) {
  const { onClose } = props;

  const [installStatus, setInstallStatus] = useState<INSTALL_STATUS>(
    INSTALL_STATUS.INIT
  );

  const handleInstall = async () => {
    if (installStatus === INSTALL_STATUS.INSTALLING) return;
    setInstallStatus(INSTALL_STATUS.INSTALLING);

    const loading = await MessagePlugin.loading('正在安装中', 0);
    try {
      await window.avatarClientBridge?.pushTask.installDrivers({
        camera: true,
      });

      const status = await checkDriver();
      if (status?.camera) {
        setInstallStatus(INSTALL_STATUS.SUCCESS);
        // onSuccess();
      } else {
        setInstallStatus(INSTALL_STATUS.FAIL);
      }
    } catch (e) {
      setInstallStatus(INSTALL_STATUS.FAIL);
      console.error(e);
    } finally {
      loading.close();
    }
  };

  const handleClick = async () => {
    // 安装中直接返回
    if (installStatus === INSTALL_STATUS.INSTALLING) return;
    if (installStatus === INSTALL_STATUS.SUCCESS) {
      await window.avatarClientBridge?.system.reboot();
      return;
    }
    // 未安装或者安装失败
    handleInstall().then();
  };

  const btnText = useMemo(() => {
    return buttonText[installStatus];
  }, [installStatus]);

  return (
    <div className="relative">
      <div className="h-[240px] flex items-center justify-center">
        <div
          x-if={
            installStatus === INSTALL_STATUS.INIT ||
            installStatus === INSTALL_STATUS.INSTALLING
          }
          className="flex flex-col items-center gap-[24px]"
        >
          <header className="mb-8 text-[#000] font-medium">
            请先安装以下工具才能进行开播
          </header>
          {INSTALL_DRIVER.map((item) => {
            return (
              <div
                key={item.key}
                className="w-full p-20 flex gap-[24px] bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)]"
              >
                <img width={84} height={84} src={item.icon} alt="" />
                <div className="flex flex-col justify-around">
                  <div className="font-medium h-[24px] leading-[24px] text-[black]">
                    {item.title}
                  </div>
                  <div>{item.desc}</div>
                </div>
              </div>
            );
          })}
        </div>

        <CommonDialog
          x-if={installStatus === INSTALL_STATUS.SUCCESS}
          theme="success"
          title="安装成功"
          desc="首次安装需要重新启动电脑后才可以进行开播"
        />
        <CommonDialog
          x-if={installStatus === INSTALL_STATUS.FAIL}
          theme="error"
          title="安装失败，请重新安装"
        />
      </div>
      <div className="h-[48px]" />
      <StepModalFooter
        primaryBtn={
          installStatus === INSTALL_STATUS.SUCCESS ? (
            <Popconfirm
              content="请您确认是否要立即对电脑进行重启？"
              theme="danger"
              confirmBtn="确认重启"
              onConfirm={handleClick}
            >
              <Button className="gradient-primary">{btnText}</Button>
            </Popconfirm>
          ) : (
            <Button
              className="gradient-primary"
              onClick={handleClick}
              disabled={installStatus === INSTALL_STATUS.INSTALLING}
            >
              {btnText}
            </Button>
          )
        }
        onClose={installStatus !== INSTALL_STATUS.SUCCESS ? onClose : undefined}
      />
    </div>
  );
}
