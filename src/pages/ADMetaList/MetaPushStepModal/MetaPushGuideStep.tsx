/**
 * <AUTHOR>
 * @date 2024/11/29 11:02
 */

import { checkDriver } from '@/components/ClientDialog/utils';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import { runningInClient } from '@/utils/electron';
import { useAsyncEffect } from 'ahooks';
import cloneDeep from 'lodash-es/cloneDeep';
import { useState } from 'react';
import { Swiper } from 'tdesign-react';

const { SwiperItem } = Swiper;

// 虚拟声卡指引图
const VB_AUDIO_SWIPER_DATA = [
  'https://avatarcdn.pay.qq.com/material/b8bf1769f4971ed1a7cc5b6441ed4e5a.png',
  'https://avatarcdn.pay.qq.com/material/ed8043fd39a5e48c9330a2e6012f5f4b.png',
];

interface IProps {
  onClose: () => void;
}
export function MetaPushGuideStep(props: IProps) {
  const { onClose } = props;
  const [swiperData, setSwiperData] = useState(
    runningInClient
      ? [
          'https://avatarcdn.pay.qq.com/material/7ef3e3fd4ac0d231ca4c268e902218eb.png',
          'https://avatarcdn.pay.qq.com/material/e5a56ed761d6dfc32f59c4b1eccee3ab.png',
          'https://avatarcdn.pay.qq.com/material/73c6619c8ad9a3e42bd407c33de40c12.png',
          'https://avatarcdn.pay.qq.com/material/f6698380009bc1a5e14ba48145e89ea4.png',
          'https://avatarcdn.pay.qq.com/material/1d2de5c6e0fa107e29c9a1a6f61be315.png',
          // 'https://avatarcdn.pay.qq.com/material/df163036878cb9e788753486841054b4.png',
          'https://avatarcdn.pay.qq.com/material/076ac7a2432193205ce3e070e8f10652.png',
        ]
      : [
          'https://avatarcdn.pay.qq.com/material/da582f13cfaf1e6e4b90801d12f9ac1b.png',
          // 'https://avatarcdn.pay.qq.com/material/c593b83f348d28f5ea88bae1bc345242.png',
        ]
  );

  // 客户端走声卡模式增加声卡配置指引
  useAsyncEffect(async () => {
    if (!runningInClient) return;
    checkDriver().then((checkRes) => {
      let newData = cloneDeep(swiperData);

      if (checkRes?.audio) {
        newData = newData.concat(VB_AUDIO_SWIPER_DATA);
      } else {
        newData.push(VB_AUDIO_SWIPER_DATA[1]);
      }
      setSwiperData(newData);
    });
  }, []);

  return (
    <div className="relative min-h-[500px]">
      <Swiper autoplay={false}>
        {swiperData.map((item) => {
          return (
            <SwiperItem key={item}>
              <img
                style={{ width: '100%', height: '460px' }}
                src={item}
                alt=""
              />
            </SwiperItem>
          );
        })}
      </Swiper>
      <div className="h-[48px]" />
      <StepModalFooter primaryBtn="完成" onPrimary={onClose} />
    </div>
  );
}
