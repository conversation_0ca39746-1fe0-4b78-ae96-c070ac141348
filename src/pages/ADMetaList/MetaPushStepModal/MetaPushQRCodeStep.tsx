/**
 * <AUTHOR>
 * @date 2024/11/29 16:08
 * @desc MetaPushQRCodeStep
 */

import React, { Fragment, useCallback, useEffect, useState } from 'react';
import {
  ILoginCustomRenderData,
  InteractionLogin,
} from '@/components/LiveQRCode/InteractionLogin';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { useMetaPushContext } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { PUSH_METHOD } from '@/pages/Editor/MetaHumanLive/typings';
import {
  InteractionNotification,
  useInteractionNotification,
} from '@/components/LiveQRCode/InteractionNotification';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import { useMemoizedFn } from 'ahooks';
import { Button, MessagePlugin } from 'tdesign-react';
import SuccessIcon from '@/assets/images/success-icon.png';
import to from 'await-to-js';

interface IProps {
  liveId: string;
  onClose: () => void;
  onNextStep: () => void;
}

export function MetaPushQrCodeStep(props: IProps) {
  const { liveId, onNextStep, onClose } = props;
  const [count, setCount] = useState(0);
  const [logined, setLogined] = useState(false);
  const { livePush } = useMetaPushContext();
  const {
    available: interactionAvailable,
    onAvailableStateChange: onInteractionAvailableStateChange,
    ref: interactionRef,
    ...rest
  } = useInteractionNotification();

  const doLivePush = useMemoizedFn(async () => {
    if (rest.enabled && interactionRef.current) {
      const [err] = await to(interactionRef.current?.confirm());
      if (err) {
        MessagePlugin.error(`系统错误，请稍后重试（${err?.message}）`);
        return;
      }
    }
    return livePush({
      pushMethod: PUSH_METHOD.LIVE_PLATFORM,
      options: {
        is_crawl_barrage: true,
        is_throw_error: true,
      },
      callback: onNextStep,
    }).catch(() => {
      setCount((v) => v + 1);
    });
  });

  /**
   * 渲染二维码刷新
   */
  const renderRefreshQRCode = useCallback((options: ILoginCustomRenderData) => {
    const { refreshQRCode } = options;
    return (
      <div className="flex flex-col gap-2">
        <div
          className="flex justify-center items-center gap-1"
          style={{
            color: '#fff',
          }}
        >
          <img
            src={SuccessIcon}
            alt=""
            style={{
              width: 22,
              height: 22,
            }}
          />
          <div>已登录</div>
        </div>
        <Button
          onClick={() => {
            refreshQRCode();
          }}
          shape="rectangle"
          theme="default"
          style={{ background: '#fff' }}
        >
          刷新
        </Button>
      </div>
    );
  }, []);

  return (
    <div className="flex flex-col gap-2">
      <InteractionNotification
        ref={interactionRef}
        onAvailableStateChange={onInteractionAvailableStateChange}
        liveID={liveId}
        onIsEnabled={rest.onEnabled}
        style={{
          background: 'linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 100%)',
          paddingBottom: '10px',
        }}
      >
        {({ enabled }) => (
          <>
            <InteractionLogin
              key={count}
              titleRender={
                MatchedGlobalConfigItem.liveConf.liveQRConf.titleRender
              }
              barrageCheckFn={
                MatchedGlobalConfigItem.liveConf.liveQRConf.barrageCheckFn
              }
              contentRender={
                MatchedGlobalConfigItem.liveConf.liveQRConf.contentRender
              }
              meta_live_id={liveId}
              immediate={enabled}
              onBeforeQRCodeUrl={() => setLogined(false)}
              onLogin={() => {
                setLogined(true);
                if (enabled) return;
                doLivePush();
              }}
              renderLoggined={enabled ? renderRefreshQRCode : undefined}
            />
          </>
        )}
      </InteractionNotification>
      <div x-if={rest.enabled} className="relative h-[42px]">
        <StepModalFooter
          primaryBtn="开播"
          primaryDisabled={!(logined && interactionAvailable)}
          onPrimary={() => {
            doLivePush().then(onNextStep);
          }}
          onClose={onClose}
        />
      </div>
    </div>
  );
}
