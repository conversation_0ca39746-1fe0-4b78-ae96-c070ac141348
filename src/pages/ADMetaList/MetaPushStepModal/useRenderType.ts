import { runningInClient } from '@/utils/electron';
import { useLatest, useMemoizedFn } from 'ahooks';
import { IMetaPushParams, useMetaPushContext } from './metaPushContext';
import {
  IMetaPushCheckItem,
  // LIVE_CHECK_CONF
} from './check-conf.typing';
import { to } from 'await-to-js';
import { GetLiveUrl, GetPushUrl } from '@/pb/api/MetaLiveSvr';
import { MetaPushError } from './meta-push.errors';
import { PushTaskConfig } from '@tencent/avatar-client-bridge-type/es';

/**
 * 处理渲染类型
 */
export const useRenderType = () => {
  /**
   * 处理最终渲染类型
   */
  const {
    setPushRenderType,
    setRemotePushParams,
    pushRenderType,
    liveId,
    setClientMachineId,
  } = useMetaPushContext();
  const latestRenderType = useLatest(pushRenderType);
  const initClientRemoteRender = async () => {
    // 能调用这个函数一定是在客户端内
    const [idErr, res] = await to(
      window.avatarClientBridge!.getMachineIdentity!()
    );
    if (idErr || !res.uuid) {
      throw new MetaPushError(
        `获取机器身份失败 ${idErr ? idErr.message : '未获取到uuid'}`
      );
    }
    setClientMachineId(res.uuid);
    const [err, result] = await to(
      Promise.all([
        GetPushUrl({
          meta_live_id: liveId,
        }),
        GetLiveUrl({
          meta_live_id: liveId,
        }),
      ])
    );
    if (err) {
      throw new MetaPushError('云端渲染开播失败');
    }
    const [{ push_url }, { live_url }] = result;
    const pushUrl = new URL(push_url.replace(/^rtmp:/, 'http:'));
    const params: IMetaPushParams['remoteParams'] = {
      dst_push_stream_url: `rtmp://${pushUrl.host}${pushUrl.pathname}`,
      dst_pull_stream_url: live_url,
      dst_push_secret: `${pushUrl.search}`,
    };

    setPushRenderType('remote');
    setRemotePushParams(params);
  };
  const decideFinalRenderType = useMemoizedFn(
    async (
      checkList: IMetaPushCheckItem[]
    ): Promise<Required<PushTaskConfig>['renderType']> => {
      if (!runningInClient) {
        setPushRenderType('remote');
        return 'remote';
      }

      try {
        console.log('判断渲染检测列表：', JSON.stringify(checkList));
      } catch {}

      /**
       * 有客户端环境 且客户端支持拉rtmp流推虚拟摄像头
       */
      if (
        window.avatarClientBridge?.pushTask.supportPullRtmp?.() &&
        typeof window.avatarClientBridge.getMachineIdentity === 'function'
      ) {
        if (
          typeof window.avatarClientBridge.getUserProfileSettings === 'function'
        ) {
          const [, userProfile] = await to(
            window.avatarClientBridge.getUserProfileSettings()
          );
          // 客户端场景下，如果存在强制云端渲染标记，设置为remote
          if (userProfile?.forcePullRtmp) {
            await initClientRemoteRender();
            return 'remote';
          }
        }
        // 客户端场景下，如果存在cpu 网络异常项 且客户端支持本地拉流模式，设置为remote渲染模式
        // Todo: 2025年4月18日因为云端资源有限，暂时关闭云端合流，强制成本地。后期添加接口判断资源情况开来放开注释，并修改这里的逻辑
        // const shouldUseRemote = checkList
        //   .filter(
        //     (item) =>
        //       item.key === LIVE_CHECK_CONF.Client_CPU_DETECTION ||
        //       item.key === LIVE_CHECK_CONF.Client_Network_DETECTION
        //   )
        //   .some((item) => item.status === 'error' || item.status === 'warning');
        // if (shouldUseRemote) {
        //   await initClientRemoteRender();
        //   return 'remote';
        // }
      }
      setPushRenderType('local');
      return 'local';
    }
  );

  return {
    decideFinalRenderType,
    latestRenderType,
  };
};
