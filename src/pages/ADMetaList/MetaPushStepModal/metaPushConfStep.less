.live_qrcode_comp {
  .live_qrcode_header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 20px;
  }

  .live_qrcode_container {
    display: flex;
    padding: 20px;
    border-radius: 4px;
    background: linear-gradient(rgba(244, 246, 255, 1), rgba(250, 245, 252, 1));

    .form {
      width: 282px;

      .t-form__label {
        color: rgba(0, 0, 0, 0.60);
      }
    }

    .divider {
      margin: 0 16px 0 0;
      background-color: #E8E7EF;
      width: 1px;
    }

    .step-guide {
      flex: 1;
    }

    .step-guide {
      h1 {
        color: rgba(0, 0, 0, 0.9);
        margin-bottom: 20px;
      }

      ul {
        list-style-type: none;
        counter-reset: sectioncounter;
      }

      li {
        color: rgba(0, 0, 0, 0.9);
        padding-bottom: 10px;
        margin-bottom: 2px;
        position: relative;
        display: flex;
        justify-content: flex-start;
        font-size: 16px;

        .tip {
          color: rgba(0, 0, 0, 0.4);
          font-size: 13px;
        }
      }

      li::before {
        content: counter(sectioncounter) '  ';
        counter-increment: sectioncounter;
        width: 24px;
        height: 24px;
        display: inline-block;
        border: 1px solid rgba(0, 0, 0, 0.4);
        border-radius: 50%;
        text-align: center;
        margin-right: 10px;
        color: rgba(0, 0, 0, 0.4);
      }

      li:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 12px;
        top: 26px;
        bottom: 0;
        width: 2px;
        background-color: rgba(0, 0, 0, 0.06);
      }
    }
  }
}
