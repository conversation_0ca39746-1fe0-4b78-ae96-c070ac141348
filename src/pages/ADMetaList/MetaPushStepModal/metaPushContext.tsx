/**
 * <AUTHOR>
 * @date 2024/11/28 14:26
 * @desc metaPushContext
 */

import { FAKE_REMOVE_CLIENT_DRIVER } from '@/components/ClientDialog/ClientVBAudioCheck';
import {
  handleClientUrl,
  queryClientSid,
} from '@/components/ClientDialog/useClientLogin';
import {
  checkDriver,
  getClientLiveSessionId,
} from '@/components/ClientDialog/utils';
import { checkLiveConf } from '@/components/LiveQRCode/common';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { ClientLive } from '@/model/clientLive';
import {
  META_LIVE_PUSH_STEP,
  MetaPushStepItem,
  useMetaPushStep,
} from '@/pages/ADMetaList/MetaPushStepModal/useMetaPushStep';
import { LiveEditorOrigin, openEditor } from '@/pages/Editor/common/openEditor';
import {
  IMetaLiveExtendData,
  PUSH_METHOD,
} from '@/pages/Editor/MetaHumanLive/typings';
import { UpdateLiveExtendConfigItem } from '@/pb/api/Development';
import { MetaLiveStop } from '@/pb/api/MetaLiveSvr';
import { hasRunningLive, runningInClient } from '@/utils/electron';
import { useMemoizedFn } from 'ahooks';
import to from 'await-to-js';
import { cloneDeep } from 'lodash-es';
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useRecoilState } from 'recoil';
import { MessagePlugin } from 'tdesign-react';
import { queryOutputAudioDevice } from '@/pages/ADMetaList/MetaPushStepModal/utils';
import { MetaLivePush } from '@/pb/api/DipContentManage_4';
import type { PushTaskConfig } from '@tencent/avatar-client-bridge-type/es';
import { enc, SHA256 } from 'crypto-js';
import {
  ClientPushConfigExtraType,
  MutablePushTaskConfig,
} from '@/type/client';
import { MetaPushError } from './meta-push.errors';
import { useClientLiveRecover } from '@/components/ClientDialog/useClientLiveRecover';

export type LiveConf = Awaited<ReturnType<typeof checkLiveConf>>;

interface MyContextType {
  liveId: string;
  liveConf?: LiveConf;
  livePush: (params: IMetaPushParams) => Promise<void>;
  linkToEditor: (metaLiveId?: string) => void;
  steps: MetaPushStepItem[];
  currentStep: META_LIVE_PUSH_STEP;
  setCurrentStep: (step: META_LIVE_PUSH_STEP) => void;
  setRemotePushParams: React.Dispatch<
    React.SetStateAction<Exclude<IMetaPushParams['remoteParams'], undefined>>
  >;
  /**
   * 推流渲染类型
   */
  pushRenderType: Required<PushTaskConfig>['renderType'];

  /**
   * 设置最终渲染类型
   */
  setPushRenderType: (type: Required<PushTaskConfig>['renderType']) => void;

  /**
   * 客户端模式下，设置唯一标识
   */
  setClientMachineId: (id: string) => void;
}

export interface IMetaPushParams {
  pushMethod?: IMetaLiveExtendData['pushMethod'];
  remoteParams?: {
    dst_push_stream_url: string;
    dst_pull_stream_url?: string;
    dst_push_secret?: string;
    watch_push_stream_url?: string;
  };
  options?: {
    /**
     *  是否要抓取弹幕
     */
    is_crawl_barrage?: boolean;
    /**
     * 是否要抛出异常
     */
    is_throw_error?: boolean;
  };
  callback?: () => void;
}

const MyContext = createContext<MyContextType | undefined>(undefined);

/** 开播上下文 */
export function MetaPushContext({
  children,
  liveId,
}: {
  children: ReactNode;
  liveId: string;
}) {
  const [, setPushTaskConfig] = useRecoilState(ClientLive);
  const [liveConf, setLiveConf] = useState<LiveConf>();
  const [remotePushParams, setRemotePushParams] = useState<
    Exclude<IMetaPushParams['remoteParams'], undefined>
  >({
    dst_push_stream_url: '',
    dst_push_secret: '',
  });

  const [pushRenderType, setPushRenderType] = useState<
    Required<PushTaskConfig>['renderType']
  >(runningInClient ? 'local' : 'remote');

  const [clientMachineId, setClientMachineId] = useState('');

  const { steps, currentStep, setCurrentStep } = useMetaPushStep(
    liveId,
    liveConf
  );

  const queryLiveConf = useMemoizedFn(async () => {
    const [err, liveConf] = await to(checkLiveConf(liveId));
    if (err) {
      void MessagePlugin.error({ content: '获取直播配置失败' });
    }
    setLiveConf(liveConf);
    return liveConf;
  });

  const linkToEditor = useMemoizedFn((metaLiveId?: string) => {
    const contentId = metaLiveId || liveId;
    const editorParams = MatchedGlobalConfigItem.liveConf.liveEditor;
    openEditor({
      system: editorParams.system,
      contentType: CONTENT_TYPE_MAP.Live.value,
      origin: editorParams.contentType as LiveEditorOrigin,
      contentId,
    });
  });
  useClientLiveRecover();

  useEffect(() => {
    queryLiveConf().then();
  }, [queryLiveConf]);

  const livePush = useMemoizedFn(async (params: IMetaPushParams) => {
    if (!liveConf) return;
    const {
      remoteParams = remotePushParams,
      pushMethod,
      options = {},
      callback,
    } = params;
    const { is_crawl_barrage, is_throw_error } = options;
    let clientSid = '';
    let clientVersion = '';
    /**
     * 标记是否为客户端拉远端流渲染模式
     */
    if (runningInClient) {
      clientVersion = (await window.avatarClientBridge?.getVersion()) || '';
      const liveHasRunning = await hasRunningLive();
      if (liveHasRunning) {
        void MessagePlugin.warning({ content: '客户端正在开播中' });
        return;
      }
      clientSid = (await queryClientSid()) || '';
      if (!clientSid) {
        void MessagePlugin.error('未获取到客户端登录态');
        return;
      }
    }
    const message = await MessagePlugin.loading('正在开播中', 0);
    // 开播前上报webrtc地址/rtmp地址/live_rtmp_push_rul地址
    try {
      const realPushMethod = pushMethod ?? PUSH_METHOD.CLIENT;
      const metaLiveExtendData: IMetaLiveExtendData = {
        pushMethod: realPushMethod,
        rtmp_push_url: remoteParams?.dst_push_stream_url || '',
        rtmp_pull_url: remoteParams?.dst_pull_stream_url || '',
        // 存储客户端额外的开播信息
        ...(clientSid
          ? {
              clientMeta: {
                clientSid,
                clientLiveSessionId: await getClientLiveSessionId(clientSid),
                featureFlags: {
                  supportRestart: true,
                },
                // 额外需要记录一下客户端的唯一id, 恢复直播时需要做一下校验
                machineId: clientMachineId,
                clientVersion,
              },
            }
          : null),
        runningInClient,
        renderType: pushRenderType,
      };
      await to(
        UpdateLiveExtendConfigItem({
          live_id: liveId,
          node_id: 'global',
          config_item_id: 'MetaLivePushConfig',
          config_item_value: JSON.stringify(metaLiveExtendData),
        })
      );
    } catch (err) {}

    try {
      const pushRes = await MetaLivePush({
        meta_live_id: liveId,
        dst_push_stream_url: remoteParams.dst_push_stream_url,
        dst_push_secret: remoteParams.dst_push_secret,
        // 视频号开播时，有监播推流地址；非视频号开播，watch_push_stream_url和dst_push_stream_url+dst_push_secret一致
        watch_push_stream_url:
          remoteParams.watch_push_stream_url ||
          `${remoteParams.dst_push_stream_url}${remoteParams.dst_push_secret}`,
        is_crawl_barrage: is_crawl_barrage ? 'true' : 'false',
        virtual_man_live_data:
          liveConf.virtualmanMetaLiveData?.map((item) => ({
            platform_account_id: item.platformAcctId || '',
            virtualman_key: item.virtualManKey || '',
            platform: item.platform,
          })) || [],
        // 判断渲染模式时本地渲染时为true
        is_local_living: pushRenderType === 'local' ? 'true' : 'false',
      });

      // 客户端开播
      if (runningInClient) {
        let customOutputAudioDevice = '';
        const driverCheck = await checkDriver();
        const installFlag = localStorage.getItem(FAKE_REMOVE_CLIENT_DRIVER);
        // 安装了声卡
        if (driverCheck?.audio && !installFlag) {
          customOutputAudioDevice = await queryOutputAudioDevice();
          // customOutputAudioDevice = 'CABLE In 16 Ch (VB-Audio Virtual Cable)';
        }
        const pushConf: MutablePushTaskConfig = {
          // url: handleClientUrl(pushRes.url, clientSid),
          url: '',
          width: 1080,
          fps: 30,
          height: 1920,
          audioDelay: 800,
          renderType: pushRenderType,
          extra: JSON.stringify({
            liveId,
            clientSid,
            // 如果有安装声卡就走声卡
            customOutputAudioDevice,
          } satisfies ClientPushConfigExtraType),
        };
        if (pushRenderType === 'local') {
          pushConf.url = handleClientUrl(pushRes.url, clientSid);
        } else if (pushRenderType === 'remote') {
          if (!remoteParams.dst_pull_stream_url) {
            throw new MetaPushError('内部错误，缺少云端渲染开播参数');
          }
          if (!clientMachineId)
            throw new MetaPushError('内部错误，缺少客户端机器uuid');
          pushConf.url = remoteParams.dst_pull_stream_url;
          pushConf.rtmpInfo = {
            clientId: clientMachineId,
            pullUrl: remoteParams.dst_pull_stream_url,
          };
        }
        console.log('客户端开播配置 =======', pushConf);
        const taskConf =
          await window.avatarClientBridge?.pushTask.startPushTask(pushConf);
        if (taskConf) {
          setPushTaskConfig([cloneDeep(taskConf)]);
          // 调用客户端成功了，记录一下开播状态
        }
      }

      callback?.();
    } catch (error) {
      console.error(
        '[MetaPushContext] livePush Error: ',
        (error as Error)?.message
      );
      await MetaLiveStop({ meta_live_id: liveId });
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      void MessagePlugin.error(`开播失败 ${error?.message || ''}`);
      if (is_throw_error) {
        throw error;
      }
    } finally {
      message.close();
    }
  });
  (window as any).__debug_set_renderType = setPushRenderType;
  (window as any).__debugLivePush = livePush;
  const memoValue = useMemo(
    () => ({
      liveId,
      liveConf,
      livePush,
      linkToEditor,
      steps,
      currentStep,
      setCurrentStep,
      setRemotePushParams,
      pushRenderType,
      setPushRenderType,
      setClientMachineId,
    }),
    [
      currentStep,
      linkToEditor,
      liveConf,
      liveId,
      livePush,
      pushRenderType,
      setCurrentStep,
      steps,
    ]
  );

  return <MyContext.Provider value={memoValue}>{children}</MyContext.Provider>;
}

// 自定义 Hook 用于使用上下文
export const useMetaPushContext = () => {
  const context = useContext(MyContext);
  if (!context) {
    throw new Error('useMyContext must be used within a MyProvider');
  }
  return context;
};
