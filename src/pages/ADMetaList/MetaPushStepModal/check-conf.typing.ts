export type PushCheckStatus = 'success' | 'warning' | 'error' | 'loading';

export type IMetaPushCheckItem = {
  key: string;
  title: string;
  desc?: string;
  status: PushCheckStatus;
  hiddenAmend: boolean;
};

// 1. 数字人可用检测 key：vman_available_detection
// 2. 话术检测 key: speech_detection
// 3. 问答库QA数量检测 qa_quantity_detection
export enum LIVE_CHECK_CONF {
  BASE_CHECK = 'vman_basic_check',
  VMAN_AVAILABLE_DETECTION = 'vman_available_detection',
  SPEECH_DETECTION = 'speech_detection',
  QA_QUANTITY_DETECTION = 'qa_quantity_detection',
  LIVE_COUNT_DETECTION = 'live_count_detection',
  Client_GPU_DETECTION = 'client_gpu_detection',
  Client_CPU_DETECTION = 'client_cpu_detection',
  Client_Network_DETECTION = 'client_network_detection',
}
