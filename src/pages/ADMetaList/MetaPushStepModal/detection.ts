import { getGPUTier } from 'detect-gpu';
import {
  getDelay,
  getDownloadSpeed,
  getUploadSpeed,
} from '@/pages/ADMetaList/MetaPushStepModal/detect-network';

async function measureCpuPerformance() {
  return new Promise<number>((resolve) => {
    const startTime = performance.now() || Date.now();
    let count = 0;

    function cpuIntensiveOperation() {
      // 一个简单的数学计算，避免被优化
      let result = 0;
      for (let i = 0; i < 1000; i++) {
        result += Math.sqrt(i) * Math.sin(i);
      }
      return result;
    }
    while ((performance.now() || Date.now()) - startTime < 100) {
      cpuIntensiveOperation();
      count += 1;
    }
    resolve(count);
  });
}

export const detectCPU = async (): Promise<DetectResult> => {
  const count = await measureCpuPerformance();
  console.log('cpu score', count);
  let tier: 0 | 1 | 2 | 3;
  let desc = '';
  if (count > 5000) {
    tier = 3;
  } else if (count > 2000) {
    tier = 2;
    desc =
      '您的设备当前CPU状况较差，存在一定的卡顿风险，可以尝试关闭一些软件或更换设备后再次检测';
  } else {
    tier = 1;
    desc = '您的设备当前CPU状况差，请尝试关闭一些软件或更换设备后再次检测';
  }
  const cpuUsage = (await window.avatarClientBridge?.system.cpuUsage()) || 0;
  console.log('cpuUsage', cpuUsage);
  if (cpuUsage > 0.6) {
    tier = 2;
    desc =
      '您的设备当前CPU占用率较高，存在一定的卡顿风险，可以尝试关闭一些软件或更换设备后再次检测';
  } else if (cpuUsage > 0.9) {
    tier = 1;
    desc = '您的设备当前CPU占用率高，请尝试关闭一些软件或更换设备后再次检测';
  }
  return {
    // 0失败
    // 1 不通过
    // 2 风险
    // 3 通过
    tier,
    // 最低为0 越高越好
    score: count,
    // 描述
    desc,
  };
};
const ignoreGPUs = {
  nvidia: ['rtx 5060', 'rtx 5070', 'rtx 5080', 'rtx 5090'],
};
export const detectGPU = async (): Promise<DetectResult> => {
  const gpuTier = await getGPUTier();
  console.log('gpuTier', gpuTier);

  let tier: 0 | 1 | 2 | 3;
  let desc = '';
  if (
    gpuTier.gpu?.includes('nvidia') &&
    ignoreGPUs.nvidia.some((ignore) => gpuTier.gpu?.includes(ignore))
  ) {
    return {
      tier: 3,
      score: 0,
      desc: '当前GPU未获取到benchmark数据',
    };
  }
  if (!gpuTier.fps || gpuTier.fps <= 5) {
    tier = 0;
    desc = `您的设备当前GPU状况差，请尝试关闭一些软件或更换设备后再次检测`;
  } else if (gpuTier.fps < 10) {
    tier = 1;
    desc = '您的设备当前GPU状况较差，请尝试关闭一些软件或更换设备后再次检测';
  } else if (gpuTier.fps < 60) {
    tier = 2;
    desc =
      '您的设备当前GPU状况不良，存在一定的卡顿风险，可以尝试关闭一些软件或更换设备后再次检测';
  } else {
    tier = 3;
  }
  if (tier <= 2) {
    desc += `gpu:${gpuTier.gpu} fps:${gpuTier.fps} type:${gpuTier.type}`;
  }
  return {
    // 0失败
    // 1 不通过
    // 2 风险
    // 3 通过
    tier,
    // 最低为0 越高越好
    score: gpuTier.fps || 0,
    // 描述
    desc,
  };
};
export const detectNetwork = async (): Promise<DetectResult> => {
  console.log('start network detect');
  // ms
  const delay = await getDelay();
  // 下载速度MB/s
  const downloadSpeed = await getDownloadSpeed().then((res) =>
    parseFloat(res.toFixed(2))
  );
  // 上传速度MB/s
  const uploadSpeed = await getUploadSpeed().then((res) =>
    parseFloat(res.toFixed(2))
  );
  console.log(downloadSpeed, uploadSpeed, delay, 'network detect');

  let tier: 0 | 1 | 2 | 3 = 3;
  let desc = '';
  if (downloadSpeed < 2 || uploadSpeed < 0.5 || delay > 500) {
    tier = 1;
  } else if (downloadSpeed < 5 || uploadSpeed < 1.5 || delay > 200) {
    tier = 2;
  }
  if (tier <= 1) {
    desc = `您的设备当前的网络状况差，请尝试优化您的网络环境后再次检测`;
  } else if (tier <= 2) {
    desc =
      '您的设备当前的网络状况不良，存在一定的卡顿风险，可以尝试优化您的网络环境后再次检测';
  } else {
    desc = '您的设备当前的网络环境良好';
  }
  desc += `\n（下载速度是${downloadSpeed}MB/s，上传速度是${uploadSpeed}MB/s，延迟是${delay}ms）`;
  console.log(desc, 'network detect desc');
  return {
    // 0失败
    // 1 不通过
    // 2 风险
    // 3 通过
    tier,
    // 最低为0 越高越好
    score: downloadSpeed + uploadSpeed,
    desc,
  };
};
export type DetectResult = {
  // 0 不可用或检测失败 1 检测不通过 2 风险 3 检测通过
  tier: 0 | 1 | 2 | 3;
  // 检测分数 最低为0 越高越好
  score?: number;
  // 检测结果详情
  desc?: string;
};
