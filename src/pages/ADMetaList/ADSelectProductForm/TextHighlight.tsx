import { findAll } from 'highlight-words-core';

export interface ITextHightLightProps {
  matchText: string;
  children: string;
  highlightStyle?: React.CSSProperties;
}

// 文本高亮组件
export function TextHightLight(props: ITextHightLightProps) {
  const { children, matchText, highlightStyle } = props;
  if (typeof children !== 'string') return children;
  const chunks = findAll({
    searchWords: [matchText],
    textToHighlight: children,
  });
  return (
    <>
      {chunks.map((chunk) => {
        const { start, end, highlight } = chunk;
        const str = children.substring(start, end);
        if (!highlight) {
          return str;
        }
        return (
          <span
            style={{
              color: 'rgba(41, 107, 239, 1)',
              ...highlightStyle,
            }}
          >
            {str}
          </span>
        );
      })}
    </>
  );
}
