import {
  useProductList,
  ProductItem,
} from '@/pages/ADPages/ProductLib/hooks/useProductList';
import { useMemoizedFn, useMount } from 'ahooks';
import {
  Fragment,
  startTransition,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Image,
  Link,
  Loading,
  MessagePlugin,
  PopupRef,
  Select,
  SelectProps,
  Tooltip,
} from 'tdesign-react';
import { TextHightLight } from './TextHighlight';
import { createPortal } from 'react-dom';
import Styles from './product-select.module.less';
import { HelpCircleIcon } from 'tdesign-icons-react';
import {
  useCheckBindShop,
  useOperateShopInfo,
} from '@/configs/admuse/product-lib/hooks/useShopInfo';
import { css } from '@emotion/react';
import {
  GetProductDetail,
  ProductFieldsConfig,
  ProductFieldsConfigResponse,
} from '@/pb/api/DigitalManProc';
import { IProductFieldConfig } from '@/pages/ADPages/ProductLib/ProductEditDrawer/typings';
import { ProductInfoDisplay } from '@/pages/ADMetaList/ADSelectProductForm/ProductInfoDisplay';
import {
  IProductEditorDrawerInstance,
  ProductEditDrawer,
  ProxyProductEditorDrawer,
} from '@/pages/ADPages/ProductLib/ProductEditDrawer';
import to from 'await-to-js';

const { Option } = Select;
export interface IProductSelectProps extends Omit<SelectProps, 'onChange'> {
  onChange: (productId: string, productItem: ProductItem) => void;
  value: string;
}
/**
 * 商品选择组件
 */
export function ProductSelect(props: IProductSelectProps) {
  const { value, onChange, ...rest } = props;
  /**
   * 涉及到广告的路由下的组件都会包
   */
  const [searchText, setSearchText] = useState('');
  const {
    loadMore,
    runWithReset,
    list,
    resetList,
    loading: searchLoading,
  } = useProductList({
    manual: true,
    mode: 'infinite-scroll',
  });
  const { syncShopInfo } = useOperateShopInfo();
  const { hasBinded } = useCheckBindShop();
  const [noMore, setNomore] = useState(false);
  const [singleProductDetail, setSingleProductDetail] = useState<ProductItem>();
  const [showBottom, setShowBottom] = useState(false);
  const [toolTipVisible, setToolTipVisible] = useState(false);
  const toolTipRef = useRef<PopupRef>(null);
  const [currentItem, setCurrentItem] = useState<ProductItem>();
  const [ready, setReady] = useState(false);
  const [productFieldConfig, setProductFieldConfig] =
    useState<IProductFieldConfig>();
  const productEditorRef = useRef<IProductEditorDrawerInstance>(null);

  const handleSearch = useMemoizedFn(async (searchText) => {
    if (searchLoading) return;
    setSearchText(searchText);
    // if (!searchText) return;
    setNomore(false);
    if (!hasBinded && searchText) {
      // 未绑定，查询单个productDetail作为列表
      GetProductDetail({
        product_id: searchText,
      }).then((resp) => {
        setSingleProductDetail(resp);
      });
    } else {
      runWithReset({
        search: searchText,
      });
    }
  });

  const handleChange = useMemoizedFn(async (product: (typeof list)[0]) => {
    setCurrentItem(product);
    onChange(product.product_id, product);
  });

  const handleSync = async () => {
    const loading = await MessagePlugin.loading('正在更新商品知识库', 0);
    syncShopInfo()
      .then(() => {
        MessagePlugin.success('商品知识库更新成功');
      })
      .catch(() => {
        MessagePlugin.error('商品知识库更新失败');
      })
      .finally(() => {
        loading.close();
      });
  };

  /**
   * 补充/修改商品信息回调
   */
  const handleExtraProductInfo = useMemoizedFn(() => {
    if (!currentItem) return;
    if (!productEditorRef.current) return;

    productEditorRef.current?.show({
      productId: currentItem?.product_id || '',
      tdDrawerProps: {
        zIndex: 3000,
      },
      onSuccess: async () => {
        // 查询一次物品详情，并更新当前选中的
        const [err, resp] = await to(
          GetProductDetail({
            product_id: currentItem.product_id,
          })
        );
        if (err) {
          MessagePlugin.error('查询物品详情失败，请重新修改');
          handleExtraProductInfo();
          return;
        }
        handleChange({
          ...currentItem,
          ...resp,
          product_id: currentItem.product_id,
        });
      },
    });
  });

  /**
   * 搜索字符串为空重置列表
   */
  useLayoutEffect(() => {
    if (searchText !== '') return;
    resetList();
    startTransition(() => {
      setNomore(false);
    });
  }, [resetList, searchText]);

  /** 触发搜索 */
  // useEffect(() => {
  //   handleSearch(searchText);
  // }, [handleSearch, searchText]);
  // useMount(() => handleSearch(''));

  useLayoutEffect(() => {
    // 查询商品字段配置
    ProductFieldsConfig({})
      .then((resp) => {
        setProductFieldConfig(JSON.parse(resp.config_data));
        setReady(true);
      })
      .catch((e) => {
        /**
         * 查询商品字段失败
         */
      });
  }, []);

  const arrowPos = useMemo(() => {
    if (!toolTipVisible) return null;
    const el = toolTipRef.current?.getPopupContentElement();
    if (!el) return null;
    const listDOM = document.querySelector('.__product_select');
    if (!listDOM) return null;
    const rect = el.getBoundingClientRect();
    const listRect = listDOM.getBoundingClientRect();
    return {
      top: rect.top - listRect.top + rect.height / 2,
      left: rect.left - listRect.left,
    };
  }, [toolTipVisible]);

  const options = useMemo(() => {
    if (!hasBinded) {
      return singleProductDetail ? [singleProductDetail] : [];
    }
    return list;
  }, [hasBinded, list, singleProductDetail]);
  return (
    <>
      <div className="w-full">
        <Select
          {...rest}
          filterable
          clearable
          // loading={searchLoading}
          value={value}
          onPopupVisibleChange={(visible, { trigger }) => {
            if (!hasBinded) return;
            startTransition(() => {
              setShowBottom(visible);
            });
          }}
          reserveKeyword={false}
          onChange={(value) => {
            const target = options.filter((opt) => opt.product_id === value)[0];
            if (target) {
              handleChange(target);
            }
          }}
          onFocus={() => {
            handleSearch(searchText || '');
            // if (currentItem) {
            //   // 聚焦时如果有选中的物品，用选中的物品进行搜索
            //   handleSearch(currentItem.product_id);
            // } else {
            // }
          }}
          onSearch={(text) => {
            handleSearch(text);
          }}
          // onBlur={() => {
          //   handleSearch('');
          // }}
          //   options={options}
          placeholder="请输入商品名称/商品 ID"
          scroll={{
            type: 'virtual',
          }}
          popupProps={{
            onScrollToBottom: async () => {
              if (searchLoading) return;
              if (noMore) return;
              // if (!searchText) return;
              // 未绑定的情况下，无加载更多列表功能
              if (!hasBinded) return;
              const data = await loadMore({
                search: searchText,
              });
              if (data?.list.length === 0) setNomore(true);
            },
            overlayClassName: `__product_select ${Styles.patchProductSelect}`,
            overlayInnerClassName: Styles.patchPopupContent,
            overlayInnerStyle: {
              width: '449px',
              ...(hasBinded
                ? {
                    borderRadius: '0',
                  }
                : null),
            },
          }}
        >
          {options.map((option, index) => (
            <Option
              style={{ height: '78px' }}
              // key={`${option.product_id}-${index}`}
              value={option.product_id}
              label={option.product_name}
            >
              <div className="flex items-center" style={{ width: '100%' }}>
                <Image
                  src={option.header_imgs?.[0] || ''}
                  style={{
                    width: '48px',
                    height: '48px',
                    background: '#000',
                    borderRadius: '6px',
                  }}
                  error={<span>暂无图片</span>}
                  fit="contain"
                  loading={
                    <Loading
                      style={{
                        width: '14px',
                        height: '14px',
                      }}
                    />
                  }
                />
                <div className="ml-12" style={{ width: 'calc(100% - 60px)' }}>
                  <div>
                    <TextHightLight matchText={searchText}>
                      {option.product_name}
                    </TextHightLight>
                  </div>
                  <div className="text-12 text-desc">
                    ID：
                    <TextHightLight matchText={searchText}>
                      {option.product_id}
                    </TextHightLight>
                  </div>
                </div>
              </div>
            </Option>
          ))}
        </Select>
        {currentItem && productFieldConfig && (
          <ProductInfoDisplay
            product={currentItem}
            productFieldsConfig={productFieldConfig}
            className="mt-8"
            onAdditionalProductInfo={handleExtraProductInfo}
          />
        )}
      </div>
      {showBottom &&
        options.length > 0 &&
        createPortal(
          <div
            className="flex items-center"
            style={{
              borderRadius: '0 0 4px 4px',
              height: '36px',
              borderTop: '1px solid rgba(226, 229, 234, 1)',
              background: '#fff',
            }}
          >
            {/* 模拟一个箭头放在body上 */}
            <Fragment x-if={toolTipVisible}>
              {createPortal(
                <div
                  style={{
                    ...(arrowPos
                      ? {
                          position: 'absolute',
                          left: `calc(${arrowPos.left}px - 4px)`,
                          top: `calc(${arrowPos.top}px - 4px)`,
                        }
                      : null),
                  }}
                  css={css`
                    position: absolute;
                    z-index: 5501;
                    content: '';
                    width: 8px;
                    height: 8px;
                    transform: rotate(45deg);
                    background: var(--td-bg-color-container);
                  `}
                  onMouseMove={() => {
                    toolTipRef.current?.setVisible(true);
                  }}
                />,
                document.querySelector('.__product_select')!
              )}
            </Fragment>

            <div className="flex items-center gap-1 cursor-pointer">
              <Tooltip
                // trigger="click"
                attach=".__product_select"
                ref={toolTipRef}
                onVisibleChange={(visible) => {
                  startTransition(() => {
                    setToolTipVisible(visible);
                  });
                }}
                content={
                  <div
                    className={Styles.helpTipsContainer}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <p>1.输入正确的商品名称/ID</p>
                    <p>2.确保您已在视频号小店中添加直播商品</p>
                    <p>
                      3.
                      <Link theme="primary" onClick={handleSync}>
                        更新商品知识库
                      </Link>
                      后，重新搜索
                    </p>
                  </div>
                }
                overlayInnerStyle={{
                  width: '248px',
                  padding: '11px 15px',
                }}
                theme="light"
                placement="right"
              >
                <div className="flex items-center gap-2">
                  <HelpCircleIcon className="ml-16" />
                  <span>找不到商品</span>
                </div>
              </Tooltip>
            </div>
          </div>,
          document.querySelector('.__product_select')!
        )}
      <ProxyProductEditorDrawer ref={productEditorRef} />
    </>
  );
}

ProductSelect.displayName = 'ProductSelect';
