import { SlideSelector } from '@/components/SlideSelector';
import { ProductOption } from '@/pages/ADMetaList/ADSelectProductModal';
import { useMemoizedFn } from 'ahooks';
import { Fragment, ReactNode, useLayoutEffect, useMemo, useState } from 'react';
import { HelpCircleIcon } from 'tdesign-icons-react';
import { Button, Form, Image, ImageViewer, Popup, Space } from 'tdesign-react';
import { IProductSelectProps, ProductSelect } from './ProductSelect';
import './TextHighlight';

const { FormItem } = Form;
export interface ADLiveExtendFormValue {
  // productList: ProductOption[];
  liveMode: 'noAnchor' | 'hasAnchor';
}

export interface IADSelectProductFormChildrenProps {
  /**
   * 物品准备完成
   */
  isProductReady: boolean;
  /**
   * 当前选中物品
   */
  currentProducts: ProductOption[];
  /**
   * 取消
   */
  cancel: () => void;

  /**
   * 触发表单提交
   */
  submit: () => void;
}

export interface IADSelectProductFormProps {
  onCancel?: () => void;
  onSubmit?: (
    data: { productList: ProductOption[] } & ADLiveExtendFormValue
  ) => void;
  onChange?: (
    data: {
      productList: ProductOption[];
    } & ADLiveExtendFormValue
  ) => void;
  children?: (props: IADSelectProductFormChildrenProps) => ReactNode;

  /**
   * 取消按钮文本
   */
  cancelText?: string;

  /** 确认按钮文本 */
  confirmText?: string;

  hideLiveModeField?: boolean;
}

export function ADSelectProductForm(props: IADSelectProductFormProps) {
  const {
    children,
    onCancel,
    onChange,
    onSubmit,
    cancelText = '取消',
    confirmText = '开始创建',
    hideLiveModeField,
  } = props;

  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<ADLiveExtendFormValue>({
    liveMode: 'noAnchor',
  });
  const [value, setValue] = useState('');
  const [currentProducts, setCurrentProducts] = useState<ProductOption[]>([]);
  const isProductReady = useMemo(
    () =>
      Array.isArray(currentProducts) &&
      currentProducts.length > 0 &&
      !currentProducts.some((product) => !product.is_required_fields_filled),
    [currentProducts]
  );
  const handleChange: IProductSelectProps['onChange'] = (
    value: string,
    product
  ) => {
    setValue(value);
    handleSelect(product);
  };

  const handleSelect = (selectProduct: ProductOption) => {
    form.setFieldsValue({
      productList: [selectProduct],
    });
    setCurrentProducts([selectProduct]);
  };
  const handleSubmit = useMemoizedFn(() => {
    const values = form.getFieldsValue(true) as ADLiveExtendFormValue;
    onSubmit?.({
      productList: currentProducts,
      liveMode: values.liveMode,
    });
  });

  const childrenProps = useMemo(() => {
    return {
      isProductReady,
      currentProducts,
      cancel: () => onCancel?.(),
      submit: () => form.submit(),
    };
  }, [currentProducts, form, isProductReady, onCancel]);

  useLayoutEffect(() => {
    onChange?.({
      productList: currentProducts,
      liveMode: formValues.liveMode,
    });
  }, [currentProducts, formValues.liveMode, onChange, value]);

  return (
    <Form
      onSubmit={handleSubmit}
      form={form}
      onValuesChange={(changedValues) => {
        setFormValues((prev) => ({ ...prev, ...changedValues }));
      }}
      labelAlign="left"
      labelWidth={85}
    >
      <FormItem
        label={
          <div className="flex items-center">
            <span className="mr-8">直播商品</span>
            <Popup
              trigger="focus"
              showArrow
              content={
                <div
                  style={{ width: '265px' }}
                  className="flex flex-col  items-center p-8"
                >
                  <div>
                    可以在“视频号助手-直播商品管理”找到商品ID，如下图所示：
                  </div>
                  <ImageViewer
                    zIndex={6000}
                    trigger={({ open }) => (
                      <Image
                        src="https://avatarcdn.pay.qq.com/material/1cc82e3b7d64bdc146c29800682b2780.png"
                        style={{
                          width: 242,
                          height: 127,
                          cursor: 'pointer',
                          borderRadius: '8px',
                          border: '1px solid #DDD',
                          marginTop: '4px',
                        }}
                        fit="fill"
                        shape="round"
                        onClick={open}
                        error="加载失败"
                      />
                    )}
                    images={[
                      'https://avatarcdn.pay.qq.com/material/1cc82e3b7d64bdc146c29800682b2780.png',
                    ]}
                  />
                </div>
              }
              placement="right"
            >
              <span tabIndex={-1} className="cursor-pointer">
                <HelpCircleIcon />
              </span>
            </Popup>
          </div>
        }
        name="_select"
      >
        <ProductSelect value={value} onChange={handleChange} />
      </FormItem>
      <FormItem
        x-if={!hideLiveModeField}
        label="直播模式"
        name="liveMode"
        initialData={formValues.liveMode}
        style={{ marginTop: '24px' }}
      >
        <SlideSelector
          border
          options={[
            {
              name: '无主播，需要AI主播',
              value: 'noAnchor',
            },
            {
              name: '有主播，需要AI助播',
              value: 'hasAnchor',
              disable: true,
              tips: '即将解锁',
            },
          ]}
        />
      </FormItem>
      <FormItem className="flex justify-end mt-20">
        <Space x-if={!children}>
          <Button
            className="gradient-default"
            theme="default"
            onClick={() => onCancel?.()}
          >
            {cancelText}
          </Button>
          <Button
            className="gradient-primary"
            type="submit"
            disabled={!isProductReady}
          >
            {confirmText}
          </Button>
        </Space>
        <Fragment x-if={children}>{children?.(childrenProps)}</Fragment>
      </FormItem>
    </Form>
  );
}
