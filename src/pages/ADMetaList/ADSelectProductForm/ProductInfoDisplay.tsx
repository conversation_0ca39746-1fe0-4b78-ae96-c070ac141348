import type { ProductOption } from '@/pages/ADMetaList/ADSelectProductModal';
import { resolveMergedFields } from '@/pages/ADPages/ProductLib/ProductEditDrawer/product-form-utils';
import { IProductFieldConfig } from '@/pages/ADPages/ProductLib/ProductEditDrawer/typings';
import { FormPath } from '@formily/core';
import React, { Fragment, useMemo, useState } from 'react';
import { InfoCircleFilledIcon } from 'tdesign-icons-react';
import { Divider, Link } from 'tdesign-react';

export interface IProductInfoDisplayProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'className' | 'style'> {
  product: ProductOption;
  productFieldsConfig: IProductFieldConfig;
  /**
   * 补充商品信息回调
   */
  onAdditionalProductInfo: () => void;
}

export function ProductInfoDisplay(props: IProductInfoDisplayProps) {
  const {
    product,
    productFieldsConfig,
    className,
    style,
    onAdditionalProductInfo,
  } = props;
  /**
   * 存放合并后字段
   */
  const requiredFields = useMemo<
    (IProductFieldConfig['fields'][0] & {
      value: string | number;
    })[]
  >(() => {
    return (
      resolveMergedFields(productFieldsConfig, product.product_type)
        ?.filter((field) => field.required)
        .map((field) => {
          const path = new FormPath(field.name);
          const value = path.getIn(product);
          return {
            ...field,
            value,
          };
        }) || []
    );
  }, [product, productFieldsConfig]);

  return (
    <div
      style={{
        ...style,
      }}
      className={`flex flex-col gap-2 ${className || ''}`}
    >
      <div
        style={{
          background: 'linear-gradient(84.64deg, #F4F6FF 0%, #FBF5FD 100%)',
          padding: '16px 20px',
        }}
        className="flex flex-col gap-[16px] rounded-8"
      >
        <div className="flex flex-col gap-2">
          {requiredFields.map((field) => {
            let { value } = field;
            if (field.type === 'option' && Array.isArray(field.options)) {
              const target = field.options.filter(
                (option) => option.value === value
              )?.[0];
              if (target) {
                value = target.label;
              }
            }
            return (
              <div className="flex gap-[32px]">
                <span
                  style={{
                    color: 'rgba(137, 139, 143, 1)',
                    minWidth: '50px',
                  }}
                  className="text-14"
                >
                  {field.label || ''}
                </span>
                <span
                  className="text-14"
                  style={{
                    color: 'rgba(13, 13, 13, 1)',
                  }}
                >
                  {value === '' ? '-' : value}
                </span>
              </div>
            );
          })}
        </div>
        {/* 必填字段都满足了的展示 */}
        <Fragment x-if={product.is_required_fields_filled}>
          <Divider
            layout="horizontal"
            style={{
              background: 'rgba(73, 90, 122, 0.12)',
              margin: 0,
            }}
          />
          <div
            className="flex items-center gap-1 cursor-pointer w-fit"
            style={{
              color: 'rgba(98, 99, 101, 1)',
            }}
            onClick={onAdditionalProductInfo}
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8 0.5C8 0.223858 7.77614 0 7.5 0H2C0.89543 0 0 0.89543 0 2V12C0 13.1046 0.89543 14 2 14H12C13.1046 14 14 13.1046 14 12V6.5C14 6.22386 13.7761 6 13.5 6C13.2239 6 13 6.22386 13 6.5V12L12.9973 12.0746C12.9591 12.5921 12.5272 13 12 13H2L1.92537 12.9973C1.40794 12.9591 1 12.5272 1 12V2L1.00274 1.92537C1.04092 1.40794 1.47282 1 2 1H7.5C7.77614 1 8 0.776142 8 0.5ZM13.3891 1.3184C13.5844 1.12313 13.5844 0.806553 13.3891 0.61129C13.1938 0.416028 12.8773 0.416028 12.682 0.61129L5.61092 7.68236C5.41566 7.87762 5.41566 8.1942 5.61092 8.38947C5.80619 8.58473 6.12277 8.58473 6.31803 8.38947L13.3891 1.3184Z"
                fill="currentColor"
              />
            </svg>
            <span>修改信息</span>
          </div>
        </Fragment>
      </div>
      <div
        x-if={!product.is_required_fields_filled}
        className="flex items-center gap-2"
        style={{
          background: 'rgba(245, 248, 255, 1)',
          border: '1px solid rgba(212, 225, 252, 1)',
          height: '36px',
          borderRadius: '6px',
          padding: '0 0 0 12px',
        }}
      >
        <InfoCircleFilledIcon
          style={{
            color: 'rgba(41, 107, 239, 1)',
          }}
        />
        <div>
          商品信息不完整，请
          <Link theme="primary" hover="color" onClick={onAdditionalProductInfo}>
            补充商品信息
          </Link>
        </div>
      </div>
    </div>
  );
}
