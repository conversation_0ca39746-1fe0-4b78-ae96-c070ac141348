/**
 * <AUTHOR>
 * @date 2024/5/27 下午8:55
 * @desc MetaRecord
 */

import { UpdateClientDialog } from '@/components/ClientDialog/UpdateClientDialog';
import { useCheckClientVersion } from '@/components/ClientDialog/useCheckClientVersion';
import { CommonDialog } from '@/components/CommonDialog';
import { BarrageQRCode } from '@/components/LiveQRCode/BarrageQRCode';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { ClientLive } from '@/model/clientLive';
import {
  BARRAGE_CRAW,
  MetaLiveBarrageCrawStatusMap,
  MetaLiveStatus,
  MetaLiveStatusMap,
} from '@/pages/ADMetaList/const';
import { MetaPushStepModal } from '@/pages/ADMetaList/MetaPushStepModal';
import { MetaPushContext } from '@/pages/ADMetaList/MetaPushStepModal/metaPushContext';
import { OpenEditType } from '@/pages/ADMetaList/typings';
import {
  IMetaLiveListRequestParams,
  IMetaLiveRecord,
} from '@/pages/ADMetaList/useMetaLiveList';
import { LiveEditorOrigin, openEditor } from '@/pages/Editor/common/openEditor';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { BasicImageViewer } from '@/pages/List/components/image_viewer';
import { RealTimeQAEntry } from '@/pages/List/components/realtime_qa_entry/RealTimeQAEntry';
import {
  MetaLiveCopy,
  MetaLiveDelete,
  MetaLiveStop,
} from '@/pb/api/MetaLiveSvr';
import { RespError } from '@/pb/config';
import { runningInClient } from '@/utils/electron';
import { useLatest } from 'ahooks';
import dayjs from 'dayjs';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useRecoilState } from 'recoil';
import {
  DialogPlugin,
  Dropdown,
  FilterValue,
  Link,
  MessagePlugin,
  Popconfirm,
  PrimaryTableCol,
  Table,
} from 'tdesign-react';
import './index.less';
import {
  IGroupMetaLiveRecord,
  LiveGroupListItem,
  useLiveGroup,
} from '@/pages/ADMetaList/hooks/useLiveGroup';
import VideoDialog from '@/pages/ADMetaList/MetaRecord/VideoDialog';
import { VIDEO_TYPE } from '@/pages/ADMetaList/MetaRecord/VideoDialog/useAudo';

interface IProps {
  contentType: string;
  record: IMetaLiveRecord[];
  recordCount: number;
  queryParams: IMetaLiveListRequestParams;
  setQueryParams: (params: IMetaLiveListRequestParams) => void;
  loading: boolean;
  refresh: (groupId?: string) => Promise<void>;
  onBeforeStart?: (contentId: string) => Promise<boolean>;
  maxHeight?: string;
  disableRealTimeQA?: boolean;
  // 是否定时刷新列表
  autoRefresh?: boolean;
  /**
   * 是否展示互动状态
   */
  showBarrageStatus?: boolean;

  disableCopy?: boolean;

  beforeOpenEdit?: (options: {
    openEdit: OpenEditType;
    item: IMetaLiveRecord;
  }) => Promise<boolean>;
  // 直播组ID
  groupId?: string;
  // 直播组列表
  groupList?: LiveGroupListItem[];
  // 直播组setter
  setGroupId?: (id: string) => void;
}

export type { IProps as IMetaLiveRecordProps };
const isRunningLive = (status: string) => {
  return (
    status === MetaLiveStatus.running || status === MetaLiveStatus.localLiving
  );
};

export function MetaRecord(props: IProps) {
  const {
    record,
    contentType,
    refresh,
    recordCount,
    loading,
    setQueryParams,
    queryParams,
    onBeforeStart,
    disableRealTimeQA,
    autoRefresh,
    beforeOpenEdit,
    groupId = '__default__',
    groupList = [],
    setGroupId,
  } = props;
  const [metaLiveId, setMetaLiveId] = useState('');
  // 弹幕通路异常打开的弹窗
  const [barrageDialogVisible, setBarrageDialogVisible] = useState(false);
  const latestLoading = useLatest(loading);
  const latestRefresh = useLatest(refresh);
  const [isAutoRefreshing, setIsAutoRefreshing] = useState(false);
  const loadingTimeRef = useRef(0);
  const [, setPushTaskConfig] = useRecoilState(ClientLive);
  const [metaPushStepModalVisible, setMetaPushStepModalVisible] =
    useState(false);
  const [refreshUpdateDialog, setRefreshUpdateDialog] = useState(0);
  const { fetchRemoteVersion } = useCheckClientVersion(() => void 0, false);
  const [liveVideoUrl, setLiveVideoUrl] = useState('');
  const { bindLiveGroup } = useLiveGroup();
  const openEdit = (metaLiveId: string) => {
    openEditor({
      system:
        contentType === 'ad_script_live'
          ? EditorSystemMap.AD_LIVE
          : EditorSystemMap.META_HUMAN,
      contentType: CONTENT_TYPE_MAP.Live.value,
      origin: contentType as LiveEditorOrigin,
      contentId: metaLiveId,
    });
  };

  async function stopMetaLive(contentId: string, metaLiveStatus: string) {
    // 下播过程中
    if (metaLiveStatus === MetaLiveStatus.ending) {
      void MessagePlugin.warning('直播正在停止，请稍后操作');
      return;
    }
    // 本地开播只能在客户端下播
    if (metaLiveStatus === MetaLiveStatus.localLiving && !runningInClient) {
      const myDialog = DialogPlugin({
        width: 700,
        footer: null,
        header: '下播提示',
        body: (
          <CommonDialog
            theme="error"
            title="下播失败"
            desc="该直播间的开播方式为“本地开播”，请前往客户端进行下播操作"
            onConfirm={() => {
              myDialog.hide();
            }}
            confirmText="我知道了"
          />
        ),
        onClose: () => {
          myDialog.hide();
        },
      });
      return;
    }
    const loading = await MessagePlugin.loading('下播中', 0);
    console.debug('下播: ', contentId);
    try {
      // 如果是本地客户端下播，可以同时下播客户端开播状态和云端开播状态
      if (
        runningInClient &&
        (
          [MetaLiveStatus.localLiving, MetaLiveStatus.running] as string[]
        ).includes(metaLiveStatus)
      ) {
        const list =
          (await window.avatarClientBridge?.pushTask.listPushTask()) || [];
        if (list.length) {
          const extraData = JSON.parse(list[0].config.extra || '{}');
          // 判断终端下的直播id是否和当前下播的id一致
          if (extraData.liveId === contentId) {
            const stopId = list[0].id;
            stopId &&
              (await window.avatarClientBridge?.pushTask.stopPushTask(stopId));
            setPushTaskConfig([]);
          } else {
            console.error('开播的终端和当前下播的终端不一致');
            void MessagePlugin.error('请在对应的开播终端执行下播');
            return;
          }
        } else {
          console.error('没有正在开播的直播');
          void MessagePlugin.error('请在对应的开播终端执行下播');
          return;
        }
      }
      await MetaLiveStop({ meta_live_id: contentId });
      loading.close();
      void MessagePlugin.success({ content: '下播成功' });
    } catch (e) {
      console.error(e);
      void MessagePlugin.error({ content: '下播失败' });
    } finally {
      loading.close();
      refresh();
    }
  }

  async function deleteMetaLive(MetaLiveId: string) {
    try {
      await MetaLiveDelete({
        meta_live_id: MetaLiveId,
      });
      void MessagePlugin.success({ content: '删除成功' });
      refresh();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e) {
      console.error(e);
      if (e instanceof RespError) {
        const msg =
          e.resultCode === '1122027' ? '正在直播中，删除失败' : '删除失败';
        void MessagePlugin.error(msg);
      } else {
        void MessagePlugin.error({ content: '删除失败' });
      }
    }
  }

  async function copyMetaLive(MetaLiveId: string) {
    try {
      await MetaLiveCopy({
        meta_live_id: MetaLiveId,
      });
      void MessagePlugin.success({ content: '复制成功' });
      setGroupId?.('__default__');
      refresh('__default__'); // 明确指定要刷新的groupId
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e) {
      console.error(e);
      if (e instanceof RespError) {
        const msg = '复制失败';
        void MessagePlugin.error(msg);
      } else {
        void MessagePlugin.error({ content: '复制失败' });
      }
    }
  }

  const onFilterChange = (filterValue: FilterValue) => {
    setQueryParams({
      ...queryParams,
      pageNum: 1,
      metaLiveStatus: filterValue.meta_live_status,
    });
  };

  const columns: PrimaryTableCol<IMetaLiveRecord | IGroupMetaLiveRecord>[] = [
    {
      title: '直播预览图',
      colKey: 'meta_live_poster',
      width: 110,
      align: 'center',
      cell: ({ row }) => (
        <BasicImageViewer
          width={70}
          height={135}
          img={row.meta_live_poster}
          name="直播预览图"
        />
      ),
    },
    {
      title: '直播名称',
      width: 220,
      colKey: 'meta_live_name',
    },
    { title: 'ID', width: 180, colKey: 'meta_live_id' },
    // { title: '创建人', colKey: 'create_user', width: 140 },
    {
      title: '创建时间',
      colKey: 'create_time',
      width: 180,
      cell: ({ row }) => (
        <>{dayjs.unix(+row.create_time).format('YYYY-MM-DD HH:mm:ss')}</>
      ),
    },
    {
      title: '开播方式',
      colKey: 'live_type',
      align: 'center',
      width: 180,
      cell: ({ row }) => {
        let text = '-';
        if (row.meta_live_status === MetaLiveStatus.running) {
          text = '云端开播';
        } else if (row.meta_live_status === MetaLiveStatus.localLiving) {
          text = '本地开播';
        }
        return <>{text}</>;
      },
    },
    {
      title: '直播状态',
      colKey: 'meta_live_status',
      width: 180, // 开播后状态栏还会显示问答入口，这里不能太小
      // filter: {
      //   type: 'single',
      //   list: Object.keys(MetaLiveStatusMap).map((item) => ({
      //     value: item,
      //     label:
      //       MetaLiveStatusMap[item as keyof typeof MetaLiveStatusMap]?.text ||
      //       '',
      //   })),
      // },
      cell: ({ row }) => {
        const status = row.meta_live_status as keyof typeof MetaLiveStatusMap;
        // 弹幕通路状态
        const barrageStatus =
          row.barrage_crawl_status as keyof typeof MetaLiveBarrageCrawStatusMap;
        let barrageEl: React.ReactNode = null;
        if (props.showBarrageStatus) {
          if (isRunningLive(status)) {
            let text = MetaLiveBarrageCrawStatusMap[barrageStatus]?.text || '';
            const need = [BARRAGE_CRAW.ERROR_CRAW, BARRAGE_CRAW.INIT].includes(
              barrageStatus
            );
            if (need) {
              text += ',';
            }
            barrageEl = (
              <div>
                <p
                  style={{
                    fontSize: '12px',
                    margin: 0,
                  }}
                >
                  {text}
                </p>
                {need && (
                  <p
                    style={{
                      display: 'inline-block',
                      fontSize: '12px',
                      margin: 0,
                    }}
                  >
                    请&nbsp;
                    <span
                      style={{ color: '#0047F9', cursor: 'pointer' }}
                      onClick={() => {
                        setBarrageDialogVisible(true);
                        setMetaLiveId(row.meta_live_id);
                      }}
                    >
                      重新扫码
                    </span>
                  </p>
                )}
              </div>
            );
          }
        }
        return (
          <div className="flex flex-col items-start" style={{ gap: '8px' }}>
            {/* <div className="flex flex-col gap-y-2"> */}
            <div>
              <span
                className={`record-status-cell meta-live-status--${row.meta_live_status}`}
                style={{
                  color:
                    MetaLiveStatusMap[status]?.color || 'rgba(0, 10, 41, 0.26)',
                  whiteSpace: 'nowrap',
                }}
              >
                {MetaLiveStatusMap[status]?.text || '未知状态'}
              </span>
            </div>
            {!disableRealTimeQA && isRunningLive(status) && (
              <RealTimeQAEntry
                liveID={row.meta_live_id}
                liveName={row.meta_live_name}
              />
            )}
            {barrageEl}
          </div>
        );
      },
    },
    {
      title: '操作',
      colKey: 'operation',
      width: 340,
      cell: ({ row }) => (
        <div className="flex" style={{ gap: '20px' }}>
          {isRunningLive(row.meta_live_status) && (
            <Link
              theme="primary"
              hover="color"
              onClick={() => {
                stopMetaLive(row.meta_live_id, row.meta_live_status).then();
              }}
            >
              下播
            </Link>
          )}

          {!isRunningLive(row.meta_live_status) && (
            <Link
              theme="primary"
              hover="color"
              disabled={row.meta_live_status === MetaLiveStatus.ending}
              onClick={async () => {
                if (runningInClient) {
                  // 校验客户端是否为最新版本
                  const needUpdate = await fetchRemoteVersion();
                  if (needUpdate) {
                    setRefreshUpdateDialog(refreshUpdateDialog + 1);
                    return;
                  }
                }
                if (onBeforeStart) {
                  const res = await onBeforeStart?.(row.meta_live_id);
                  if (!res) return;
                }
                setMetaLiveId(row.meta_live_id);
                setMetaPushStepModalVisible(true);
              }}
            >
              开播
            </Link>
          )}

          <Link
            theme="primary"
            hover="color"
            disabled={isRunningLive(row.meta_live_status)}
            onClick={() => {
              const promise =
                typeof beforeOpenEdit === 'function'
                  ? beforeOpenEdit({
                      openEdit: openEdit.bind(null, row.meta_live_id),
                      item: row,
                    })
                  : Promise.resolve(true);
              promise.then((allow) => {
                if (allow) openEdit(row.meta_live_id);
              });
            }}
          >
            编辑
          </Link>
          <Popconfirm
            x-if={!props.disableCopy}
            content="复制并新建"
            onConfirm={() => copyMetaLive(row.meta_live_id)}
          >
            <Link theme="primary" hover="color">
              复制
            </Link>
          </Popconfirm>

          <Popconfirm
            content="确认删除该直播间吗？"
            theme="danger"
            onConfirm={() => deleteMetaLive(row.meta_live_id)}
          >
            <Link
              theme="danger"
              hover="color"
              disabled={isRunningLive(row.meta_live_status)}
            >
              删除
            </Link>
          </Popconfirm>

          {'live_extend_config_list' in row &&
            isRunningLive(row.meta_live_status) && (
              <Link
                theme="primary"
                hover="color"
                // disabled={isRunningLive(row.meta_live_status)}
                onClick={() => {
                  let videoUrl = '';
                  const extendConfigList = row?.live_extend_config_list;
                  const metaLivePushConfig = extendConfigList?.find(
                    (item) => item.config_item_id === 'MetaLivePushConfig'
                  );
                  if (metaLivePushConfig) {
                    const configValue: {
                      rtmp_pull_url: string;
                    } = JSON.parse(metaLivePushConfig.config_item_value);
                    if (configValue.rtmp_pull_url) {
                      const url = new URL(configValue.rtmp_pull_url);
                      url.pathname += '.flv';
                      videoUrl = url.toString().replace('rtmp://', 'https://');
                      setLiveVideoUrl(videoUrl);
                    }
                  }
                }}
              >
                直播预览
              </Link>
            )}

          {groupId === '__default__' && (
            <Dropdown
              trigger="click"
              options={groupList.map((group) => {
                return {
                  content: group.title,
                  value: group.live_group_id,
                };
              })}
              onClick={({ value }) => {
                if (!value || typeof value !== 'string') return;
                bindLiveGroup(row.meta_live_id, value);
              }}
            >
              <Link theme="primary" hover="color">
                移动至组
              </Link>
              Dorp
            </Dropdown>
          )}
        </div>
      ),
    },
  ];

  useLayoutEffect(() => {
    /** 记录loading 为 true的时间  */
    if (loading) {
      loadingTimeRef.current = Date.now();
    }
  }, [loading]);

  /** 处理自动刷新 */
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (autoRefresh) {
      const startRefresh = async () => {
        let canRefresh = true;
        /** 自动刷新做一下限制，避免其他流程触发了刷新，导致这里刷新太频繁 */
        if (
          latestLoading.current ||
          Date.now() - loadingTimeRef.current < 5000
        ) {
          canRefresh = false;
        }
        if (canRefresh) {
          try {
            setIsAutoRefreshing(true);
            await latestRefresh.current?.();
          } catch {
          } finally {
            setIsAutoRefreshing(false);
          }
        }
        timeout = setTimeout(startRefresh, 5000);
      };
      timeout = setTimeout(startRefresh, 5000);
    }
    return () => clearTimeout(timeout);
  }, [autoRefresh, latestLoading, latestRefresh]);

  return (
    <>
      <Table
        className="meta-record-comp flex-1 height-0"
        rowKey="meta_live_id"
        headerAffixedTop
        data={record}
        columns={columns}
        loading={loading && !isAutoRefreshing}
        // maxHeight={maxHeight}
        onFilterChange={onFilterChange}
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: recordCount,
        }}
        onPageChange={(pageInfo) => {
          setQueryParams({
            ...queryParams,
            // 切换每页条数时，重置页码
            pageNum:
              queryParams.pageSize === pageInfo.pageSize ? pageInfo.current : 1,
            pageSize: pageInfo.pageSize,
          });
        }}
        size="medium"
        verticalAlign="middle"
      />

      {/** 互动失效弹窗 */}
      <BarrageQRCode
        x-if={barrageDialogVisible}
        meta_live_id={metaLiveId}
        onClose={() => {
          setBarrageDialogVisible(false);
          setMetaLiveId('');
        }}
      />

      <MetaPushContext x-if={metaPushStepModalVisible} liveId={metaLiveId}>
        <MetaPushStepModal
          liveId={metaLiveId}
          onClose={() => {
            refresh().then();
            setMetaPushStepModalVisible(false);
          }}
        />
      </MetaPushContext>

      <UpdateClientDialog
        x-if={runningInClient && refreshUpdateDialog}
        key={refreshUpdateDialog}
      />

      <VideoDialog
        x-if={liveVideoUrl}
        autoPlay
        onClose={() => {
          setLiveVideoUrl('');
        }}
        videoType={VIDEO_TYPE.FLV}
        videoUrl={liveVideoUrl}
        width="30%"
      />
    </>
  );
}

MetaRecord.defaultProps = {
  maxHeight: 'calc(100vh - 320px)',
};
