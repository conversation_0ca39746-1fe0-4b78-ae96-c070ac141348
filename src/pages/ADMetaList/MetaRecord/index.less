.tab_content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .meta-live-record {
    height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
.meta-record-comp {
  height: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: none;

  .record-status-cell {
    --c: #f4f6ff;
    position: relative;
    height: 24px;
    padding: 0 10px 0 4px;
    line-height: 24px;
    font-size: 14px;
    color: rgba(0, 10, 41, 0.26);
    background: linear-gradient(to right, var(--c), #faf5fc);
    display: inline-block;
    border-radius: 0 12px 16px 0;

    &::before {
      content: '';
      position: absolute;
      left: -2px;
      top: 0;
      bottom: 0;
      width: 6px;
      border-radius: 4px;
      transform: skewX(-10deg);
      background: var(--c);
      display: block;
    }

    &.meta-live-status--5 {
      --c: #ffd513;
      //color: #b09d00;
      background: linear-gradient(to right, var(--c), #fff181);

      &::before {
        background: var(--c);
      }
    }

    &.meta-live-status--4 {
      --c: #e44527;
      //color: #fff;
      background: linear-gradient(to right, var(--c), #f7a59e);

      &::before {
        background: var(--c);
      }
    }

    &.meta-live-status--3 {
      --c: rgba(235, 248, 240, 1);
      //color: rgba(0, 198, 83, 1);
      background: linear-gradient(to right, var(--c),rgba(222, 248, 232, 1));

      &::before {
        background: var(--c);
      }
    }
  }
  .t-table__pagination-wrap {
    border-top: 1px solid #e8e8e8;
  }
}

