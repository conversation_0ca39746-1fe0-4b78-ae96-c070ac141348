/* eslint-disable no-multi-assign */
import { useMemoizedFn } from 'ahooks';
import { RefObject, useRef } from 'react';
import flvJs from 'flv.js';

export type LiveVideoCtx = {
  abort: AbortController;
  url: string;
  video: HTMLVideoElement;
};
export interface ICommonInitOptions {
  /**
   * 错误事件
   */
  onError: (data: { err: Error; reason: string }) => void;
}

export enum VIDEO_TYPE {
  'FLV' = 'flv',
  'MP4' = 'mp4',
}

// 格式为flv时
const initFlv = (ctx: LiveVideoCtx, options: ICommonInitOptions) => {
  const { url, video, abort } = ctx;
  const { onError } = options;
  const player = flvJs.createPlayer({
    type: 'flv',
    url,
  });
  player.attachMediaElement(video);
  player.on('error', (error) => {
    console.log(error);
    onError({ err: error, reason: '获取视频资源失败' });
  });
  player.load();
  abort.signal.addEventListener('abort', () => {
    player.destroy();
  });
};

// 格式为mp4时
const initMp4 = (ctx: LiveVideoCtx, options: ICommonInitOptions) => {
  const { url, video, abort } = ctx;
  const { onError } = options;
  video.src = url;
  abort.signal.addEventListener('abort', () => {
    video.src = '';
  });
};
export const useAutoAudio = () => {
  const abort = useRef<AbortController | null>();

  const initVideo = useMemoizedFn(
    (
      url: string,
      type: VIDEO_TYPE,
      videoRef: RefObject<HTMLVideoElement | null>,
      onError: ICommonInitOptions['onError']
    ) => {
      const video = videoRef.current;
      if (video === null) return;
      abort.current = new AbortController();
      // 根据音频格式，初始化video
      switch (type) {
        case VIDEO_TYPE.FLV:
          initFlv(
            { abort: abort.current, url, video },
            {
              onError,
            }
          );
          break;
        case VIDEO_TYPE.MP4:
          initMp4({ abort: abort.current, url, video }, { onError });
          break;
      }
    }
  );

  const destroyVideo = useMemoizedFn(() => {
    if (abort.current) {
      abort.current.abort();
    }
  });

  return {
    initVideo,
    destroyVideo,
  };
};
