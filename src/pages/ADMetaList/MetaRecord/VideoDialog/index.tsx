// copy from tolntao in avatar-opeartor
/* eslint-disable jsx-a11y/media-has-caption */
import { css } from '@emotion/css';
import { useMemoizedFn, useUnmount } from 'ahooks';
import React, {
  CSSProperties,
  PropsWithChildren,
  useEffect,
  useRef,
} from 'react';
import { Dialog, MessagePlugin } from 'tdesign-react';
import {
  ICommonInitOptions,
  useAutoAudio,
  VIDEO_TYPE,
} from '@/pages/ADMetaList/MetaRecord/VideoDialog/useAudo';

interface IVideoDialogProps {
  videoUrl: string;
  videoType: VIDEO_TYPE;
  autoPlay?: boolean;
  width?: CSSProperties['width'];
  onClose: () => void;
}
export default function VideoDialog(
  props: PropsWithChildren<IVideoDialogProps>
) {
  const {
    videoUrl,
    videoType,
    autoPlay = false,
    width = '60%',
    onClose,
  } = props;

  const onVideoError = useMemoizedFn(({ reason, err }) => {
    void MessagePlugin.warning(reason);
  });

  return (
    <Dialog
      visible
      closeOnOverlayClick={false}
      onClose={onClose}
      placement="center"
      header={
        <div className="flex items-center gap-4">
          <h3>视频播放</h3>
        </div>
      }
      footer={null}
      width={width}
      destroyOnClose
    >
      <PreviewVideo
        onError={onVideoError}
        autoPlay={autoPlay}
        videoUrl={videoUrl}
        videoType={videoType}
      />
    </Dialog>
  );
}

type PreviewVideo = Omit<IVideoDialogProps, 'width' | 'onClose'> &
  Pick<ICommonInitOptions, 'onError'>;
function PreviewVideo(props: PreviewVideo) {
  const { videoUrl, videoType, autoPlay, onError: onVideoError } = props;
  const videoRef = useRef<HTMLVideoElement>(null);
  const { destroyVideo, initVideo } = useAutoAudio();
  useEffect(() => {
    initVideo(videoUrl, videoType, videoRef, onVideoError);
  }, [initVideo, videoType, videoUrl, onVideoError]);

  useUnmount(() => {
    destroyVideo();
  });

  return (
    <div
      className={css`
        cursor: pointer;
      `}
    >
      <video ref={videoRef} width="100%" controls autoPlay={autoPlay} />
    </div>
  );
}
