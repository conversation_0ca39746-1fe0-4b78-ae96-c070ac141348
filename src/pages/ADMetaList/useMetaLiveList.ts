/**
 * <AUTHOR>
 * @date 2024/5/27 下午8:45
 * @desc useMetaLiveList
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { useMemoizedFn, useRequest } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';
import { RespType } from '@/pb/config';
import { MetaLiveSvr } from '@/pb/pb';
import { ContentDetailQuery, ContentListQuery } from '@/pb/api/ContentSvr';
import { createDelayPromise } from '@/utils/delay-promise';
import to from 'await-to-js';
import { PlayConfig } from '@/type/pagedoo';
import { IADSaveExtendData } from '@/pages/Editor/ADLive/typings';

export interface IMetaLiveListRequestParams {
  searchKey?: string;
  metaLiveStatus?: string;
  contentType: string;
  // 分页参数
  pageSize?: number;
  pageNum?: number;
}

export type IMetaLiveRecord = RespType<
  typeof MetaLiveSvr.MetaLiveList
>['meta_live_list'][number] & {
  /**
   * 直播间对应 ContentDetail 信息
   */
  contentDetail?: {
    // 创建直播间时的脚本信息
    scriptId: string;

    componentMetaInfo?: string;
  };
};

export const useMetaLiveList = ({
  queryParamsDefault,
  fetchContentDetail,
}: {
  queryParamsDefault: IMetaLiveListRequestParams;
  /**
   * 是否查询
   */
  fetchContentDetail?: boolean;
}) => {
  const [queryParams, setQueryParams] =
    useState<IMetaLiveListRequestParams>(queryParamsDefault);
  const [records, setRecords] = useState<IMetaLiveRecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);
  const delayPromiseRef = useRef<ReturnType<typeof createDelayPromise<void>>>();
  /**
   * 批量查询直播间对应的ContentDetail信息
   */
  const batchQueryContentDetail = useMemoizedFn(
    async (liveIdList: string[]) => {
      const detailMap: Record<string, IMetaLiveRecord['contentDetail']> = {};
      const resp = await ContentListQuery({
        page_num: 1,
        page_size: liveIdList.length,
        condition: {
          content_id: liveIdList,
          expandKeys: ['scriptId', 'componentMetaInfo'],
        },
      });
      resp.content_list.forEach((item) => {
        detailMap[item.content_id] = {
          scriptId: item.extend.scriptId || '',
          componentMetaInfo: item.extend.componentMetaInfo,
        };
      });
      // await Promise.all(
      //   liveIdList.map((liveID) =>
      //     ContentDetailQuery({
      //       content_id: liveID,
      //     }).then((resp) => {
      //       let playScript: PlayConfig;
      //       try {
      //         playScript = JSON.parse(resp.extend.playScript);
      //       } catch (e) {
      //         throw e;
      //       }
      //       detailMap[liveID] = {
      //         playConfig: playScript,
      //         scriptId: resp.extend.scriptId || '',
      //       };
      //     })
      //   )
      // );
      return detailMap;
    }
  );
  const { runAsync, loading, error } = useRequest(
    async () => {
      const list = await MetaLiveSvr.MetaLiveList({
        meta_live_name_or_id: queryParams?.searchKey || '',
        content_type: queryParams.contentType,
        page_num: queryParams?.pageNum || 0,
        page_size: queryParams?.pageSize || 10,
        meta_live_status: queryParams?.metaLiveStatus || '',
      });
      if (fetchContentDetail) {
        const detailMap = await batchQueryContentDetail(
          list.meta_live_list.map((item) => item.meta_live_id)
        );
        const newMetaLiveList: IMetaLiveRecord[] = list.meta_live_list.map(
          (item) => ({
            ...item,
            contentDetail: detailMap[item.meta_live_id],
          })
        );
        list.meta_live_list = newMetaLiveList;
      }
      return list;
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    // 请求
    const delayPromise = delayPromiseRef.current;
    runAsync()
      .then((res) => {
        setRecords(res.meta_live_list || []);
        setRecordCount(Number.parseInt(res.count, 10) || 0);
        delayPromise?.resolve();
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        setRecords([]);
        setRecordCount(0);
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
        delayPromise?.reject(e);
      });
  }, [runAsync, queryParams]);

  const refresh = useCallback(async () => {
    delayPromiseRef.current = createDelayPromise();
    setQueryParams({ ...queryParams });
    await to(delayPromiseRef.current.promise);
  }, [queryParams]);

  return {
    records,
    setRecords,
    recordCount,
    setRecordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
  };
};
