/**
 * <AUTHOR>
 * @date 2024/5/28 上午10:35
 * @desc const
 */

//   1: '未开播',
//   2: '未开播',
//   3: '直播中',
//   4: '直播失败',
//   5: '直播结束中',

export const MetaLiveStatus = {
  beforeStart: '1',
  beforeStart2: '2',
  running: '3',
  localLiving: '6',
  fail: '4',
  ending: '5',
} as const;

export const MetaLiveStatusMap = {
  [MetaLiveStatus.beforeStart]: {
    text: '未开播',
    color: 'rgba(0, 10, 41, 0.26)',
  },
  [MetaLiveStatus.beforeStart2]: {
    text: '未开播',
    color: 'rgba(0, 10, 41, 0.26)',
  },
  [MetaLiveStatus.running]: {
    text: '开播中',
    color: 'rgba(0, 198, 83, 1)',
  },
  [MetaLiveStatus.localLiving]: {
    text: '直播流运行中',
    color: 'rgba(0, 198, 83, 1)',
  },
  [MetaLiveStatus.fail]: {
    text: '直播失败',
    color: '#e44527',
  },
  [MetaLiveStatus.ending]: {
    text: '直播结束中',
    color: '#ffd513',
  },
};

export enum BARRAGE_CRAW {
  // 未开播
  INIT = '0',
  // 抓取中
  ON_CRAW = '1',
  // 已下播
  DOWN_LIVE = '2',
  // 异常退出
  ERROR_CRAW = '3',
  // 互动生效中
  STARTING = '4',
}
export const MetaLiveBarrageCrawStatusMap = {
  [BARRAGE_CRAW.INIT]: {
    text: '·互动已失效',
  },
  [BARRAGE_CRAW.ON_CRAW]: {
    text: '·互动生效中',
  },
  [BARRAGE_CRAW.ERROR_CRAW]: {
    text: '·互动已失效',
  },
  [BARRAGE_CRAW.STARTING]: {
    text: '·互动连接中',
  },
  [BARRAGE_CRAW.DOWN_LIVE]: {
    text: '已下播',
  },
};
