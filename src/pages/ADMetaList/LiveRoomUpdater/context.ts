import { createContext } from 'react';
import { IUpdateLiveRoomProcessOptions } from '../hooks/useUpdateLiveRoom';
import type { useUpdateStep } from './useUpdateStep';

export interface ILiveRoomUpdaterContextValue {
  options: IUpdateLiveRoomProcessOptions | undefined;
  stepApi: Omit<ReturnType<typeof useUpdateStep>, 'nextStep'>;
  abort: () => void;
  nextStep: () => void;
}
export const LiveRoomUpdaterContext = createContext<
  ILiveRoomUpdaterContextValue | undefined
>(undefined);
