import { useCheckBindShop } from '@/configs/admuse/product-lib/hooks/useShopInfo';
import { useMemoizedFn } from 'ahooks';
import { useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, Loading } from 'tdesign-react';
import { LiveRoomUpdaterContext } from './context';
import { useStepCommonDialog } from './useUpdateStep';
import { GlobalMessage } from '@/components/GlobalMessage';

export function StepBindShop() {
  const { hasBinded, loading: checkBindShopLoading } = useCheckBindShop();
  const navigate = useNavigate();
  const { abort, nextStep } = useContext(LiveRoomUpdaterContext)!;

  const { closeDialog, dialogShow, onClosed, showDialog } = useStepCommonDialog(
    {
      defaultShow: false,
    }
  );

  const handleCancel = useMemoizedFn(() => {
    closeDialog().then(() => {
      abort();
    });
  });

  const handleSkip = useMemoizedFn(() => {
    /** 跳过绑定小店*/
    nextStep();
  });

  const gotoBindShop = useMemoizedFn(() => {
    /** 导航到商品页*/
    closeDialog().then(() => {
      navigate('/product-lib', {
        replace: true,
      });
    });
  });

  useEffect(() => {
    if (!hasBinded) {
      showDialog();
      return;
    }
    nextStep();
  }, [hasBinded, nextStep, showDialog]);

  return (
    <>
      <GlobalMessage
        x-if={checkBindShopLoading}
        theme="loading"
        content="查询小店绑定状态中..."
      />
      <Dialog
        header="提示"
        destroyOnClose
        visible={dialogShow && !checkBindShopLoading}
        confirmBtn="去绑定"
        cancelBtn="跳过绑定"
        onClose={handleCancel}
        onConfirm={gotoBindShop}
        onClosed={onClosed}
        onCancel={handleSkip}
        className="pagedoo-meta-live-global"
      >
        <div>您还没有绑定小店，请先绑定店铺</div>
      </Dialog>
    </>
  );
}
