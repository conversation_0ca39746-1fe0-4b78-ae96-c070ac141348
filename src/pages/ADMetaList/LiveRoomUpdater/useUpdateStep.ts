import { createDelayPromise } from '@/utils/delay-promise';
import { useMemoizedFn } from 'ahooks';
import { useCallback, useRef, useState } from 'react';
import { i } from 'vite/dist/node/types.d-aGj9QkWt';

export const UpdateSteps = {
  CHECK_BIND_SHOP: 'check_bind_shop',
  SELECT_PRODUCT: 'select_product',
};

export const useUpdateStep = () => {
  const updateStepRef = useRef<string[]>([
    UpdateSteps.CHECK_BIND_SHOP,
    UpdateSteps.SELECT_PRODUCT,
  ]);
  const [stepIndex, setStepIndex] = useState(0);
  const [finished, setFinished] = useState(false);

  const computedStep = updateStepRef.current[stepIndex];
  const isLastStep = stepIndex === updateStepRef.current.length - 1;
  const nextStep = useMemoizedFn(() => {
    if (isLastStep) {
      // 最后一个步骤
      setFinished(true);
      return;
    }
    setStepIndex((prev) => prev + 1);
  });

  const reset = useMemoizedFn(() => {
    setStepIndex(0);
    setFinished(false);
  });

  return {
    step: computedStep,
    nextStep,
    finished,
    reset,
    isLastStep,
  };
};

export const useStepCommonDialog = (options?: { defaultShow?: boolean }) => {
  const { defaultShow } = options ?? {};
  const [show, setShow] = useState(defaultShow);
  const waitClosedPromiseRef = useRef<ReturnType<typeof createDelayPromise>>();

  const showDialog = useCallback(() => {
    setShow(true);
  }, []);

  const closeDialog = useCallback(async () => {
    setShow(false);
    if (!waitClosedPromiseRef.current)
      waitClosedPromiseRef.current = createDelayPromise();
    return waitClosedPromiseRef.current.promise.finally(() => {
      waitClosedPromiseRef.current = void 0;
    });
  }, []);

  const onClosed = useMemoizedFn(() => {
    if (waitClosedPromiseRef.current)
      waitClosedPromiseRef.current.resolve(void 0);
  });
  return {
    dialogShow: show,
    showDialog,
    closeDialog,
    onClosed,
  };
};
