import { useMemoizedFn } from 'ahooks';
import React, { useEffect, useMemo, useRef } from 'react';
import {
  ILiveRoomUpdaterContextValue,
  LiveRoomUpdaterContext,
} from './context';
import { StepBindShop } from './StepBindShop';
import { StepSelectProduct } from './StepSelectProduct';
import { ILiveRoomUpdaterProps } from './typings';
import { UpdateSteps, useUpdateStep } from './useUpdateStep';

const StepComponents = {
  [UpdateSteps.CHECK_BIND_SHOP]: StepBindShop,
  [UpdateSteps.SELECT_PRODUCT]: StepSelectProduct,
};

export function LiveRoomUpdater(props: ILiveRoomUpdaterProps) {
  const {
    options,
    onCancel,
    onFinish = () => {
      return null;
    },
  } = props;
  const memoOnFinish = useMemoizedFn(onFinish);
  const nextStepRef = useRef<() => void>(() => {
    /** */
  });
  const updateStepApi = useUpdateStep();

  const { step, finished } = updateStepApi;

  nextStepRef.current = () => {
    if (finished) return;
    nextStepRef.current = () => {
      /** leave empty */
    };
    updateStepApi.nextStep();
  };

  const abortUpdate = useMemoizedFn(() => {
    onCancel?.();
  });
  const memoContextValues = useMemo<ILiveRoomUpdaterContextValue>(() => {
    return {
      options,
      stepApi: updateStepApi,
      abort: abortUpdate,
      nextStep: nextStepRef.current,
    };
  }, [abortUpdate, options, updateStepApi]);

  const currentComponent = StepComponents[step];

  useEffect(() => {
    if (finished) {
      memoOnFinish?.();
    }
  }, [finished, memoOnFinish]);

  return (
    <LiveRoomUpdaterContext.Provider value={memoContextValues}>
      {!finished && React.createElement(currentComponent)}
    </LiveRoomUpdaterContext.Provider>
  );
  // return (
  //   <Dialog
  //     zIndex={2501}
  //     visible={!!options}
  //     header="创建直播间"
  //     footer={null}
  //     closeOnOverlayClick={false}
  //     width={580}
  //     onClose={() => {
  //       setShowProductModal(false);
  //     }}
  //     destroyOnClose
  //   >
  //     <ADSelectProductModal
  //       onClose={() => {
  //         setShowProductModal(false);
  //       }}
  //       templateId={templateItem?.templateId}
  //       defaultDipItem={dipItem}
  //       defaultVoiceItem={voiceItem}
  //     />
  //   </Dialog>
  // );
}
