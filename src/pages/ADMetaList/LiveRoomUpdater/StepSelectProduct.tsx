import ActionSuccess from '@/assets/images/create-condition-success.png';
import { BaseScript } from '@/components/ScriptForm/type';
import {
  ADSelectProductForm,
  IADSelectProductFormProps,
} from '@/pages/ADMetaList/ADSelectProductForm';
import { ProductOption } from '@/pages/ADMetaList/ADSelectProductModal';
import { useGenerateAISpeech } from '@/pages/ADMetaList/ADSelectProductModal/useADSpeech';
import { LiveRoomUpdaterContext } from '@/pages/ADMetaList/LiveRoomUpdater/context';
import { UpdateError } from '@/pages/ADMetaList/LiveRoomUpdater/update-errors';
import { useStepCommonDialog } from '@/pages/ADMetaList/LiveRoomUpdater/useUpdateStep';
import {
  ADComponentsNameEnum,
  CompLiveSpeechAD,
} from '@/pages/Editor/ADLive/components';
import { ModifyScript, QueryScriptList } from '@/pb/api/MetaFeedbackSvr';
import { RespError } from '@/pb/config';
import { createDelayPromise } from '@/utils/delay-promise';
import { useMemoizedFn, useUnmount } from 'ahooks';
import to from 'await-to-js';
import bowser from 'bowser';
import { useContext, useRef, useState } from 'react';
import { Button, Dialog, MessagePlugin } from 'tdesign-react';
/**
 * 处理选择商品阶段的组件
 */
export function StepSelectProduct() {
  const {
    abort,
    options: updateOptions,
    nextStep,
  } = useContext(LiveRoomUpdaterContext)!;

  const { generate: generateSpeech, cancel: stopGenerateSpeech } =
    useGenerateAISpeech();
  const { closeDialog, dialogShow, onClosed, showDialog } = useStepCommonDialog(
    {
      defaultShow: true,
    }
  );

  const waitClosedPromiseRef = useRef<ReturnType<typeof createDelayPromise>>();

  const [showConfirm, setShowConfirm] = useState(false);

  /**
   * 等待弹窗关闭动画结束
   */

  const handleCancel = useMemoizedFn(() => {
    closeDialog().then(() => {
      abort();
    });
  });

  const handleOpenEdit = useMemoizedFn(() => {
    const { openEdit, data } = updateOptions!;
    openEdit(data.meta_live_id);
    closeDialog().then(() => {
      nextStep();
    });
  });
  const tryOpenEditor = useMemoizedFn((options: { startTime: number }) => {
    const { startTime } = options;
    const browserName = bowser.parse(navigator.userAgent).browser.name;
    const needUserConfirm = ['safari'].some(
      (name) => browserName && browserName?.toLowerCase().indexOf(name) > -1
    );
    if (!needUserConfirm && new Date().getTime() - startTime <= 1500) {
      handleOpenEdit();
    } else {
      // 暂存起来供弹窗使用
      setShowConfirm(true);
    }
  });

  const doUpdate = useMemoizedFn((data: { productList: ProductOption[] }) => {
    return (async () => {
      const { data: updateData } = updateOptions!;
      const { contentDetail } = updateData;
      if (!contentDetail) return;
      const scriptRes = await QueryScriptList({
        research_id: contentDetail.scriptId,
      });
      console.log('-> 需要更新的脚本', scriptRes);
      if (
        !(
          Array.isArray(scriptRes.script_list) &&
          scriptRes.script_list.length > 0
        )
      ) {
        throw new UpdateError('获取脚本内容失败');
      }
      let parsedScriptInfo: BaseScript;
      try {
        parsedScriptInfo = JSON.parse(scriptRes.script_list[0].script_info);
      } catch (e) {
        throw new UpdateError('脚本内容解析失败');
      }
      // 处理广告扩展字段
      if (!parsedScriptInfo.adExtendData)
        throw new UpdateError('扩展字段解析失败');
      parsedScriptInfo.adExtendData = {
        ...parsedScriptInfo.adExtendData,
        productList: data.productList,
        updateComponentData: {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          [ADComponentsNameEnum.Speech]: {
            speechConfig: {
              version: 'v2',
              goodsInfoList: data.productList,
            },
          } as Partial<CompLiveSpeechAD>,
        },
      };
      const [err] = await to(
        ModifyScript({
          ...scriptRes.script_list[0],
          research_id: contentDetail.scriptId,
          script_info: JSON.stringify(parsedScriptInfo),
        })
      );
      if (err)
        throw new UpdateError(
          `更新脚本失败: ${RespError.is(err) ? err.resultInfo : err.message}`
        );
    })();
  });
  const handleSubmit = useMemoizedFn<
    Required<IADSelectProductFormProps>['onSubmit']
  >(async (data) => {
    // 查询出当前的脚本
    const [err] = await to(doUpdate(data));
    if (err) {
      // TODO: 处理错误
    }
    const [generateErr] = await to(
      generateSpeech(
        {
          liveID: updateOptions!.data.meta_live_id,
          productId: data.productList[0].product_id,
        },
        {
          generateOnly: true,
        }
      )
    );
    if (generateErr) {
      MessagePlugin.error('生成话术失败，请重试');
      return;
    }
    const startTime = Date.now();
    tryOpenEditor({
      startTime,
    });
  });

  useUnmount(() => {
    stopGenerateSpeech();
  });

  return (
    <Dialog
      zIndex={2501}
      visible={dialogShow}
      header="选择商品"
      footer={null}
      closeOnOverlayClick={false}
      width={600}
      onClose={handleCancel}
      destroyOnClose
      onClosed={onClosed}
    >
      <div className="relative pagedoo-meta-live-global">
        <div x-if={!showConfirm}>
          <ADSelectProductForm
            confirmText="确定"
            onCancel={handleCancel}
            onSubmit={handleSubmit}
            hideLiveModeField
          />
        </div>
        <div
          x-if={showConfirm}
          className="flex flex-col justify-center items-center"
          style={{
            zIndex: 2,
            background: '#ffffff',
          }}
        >
          <img src={ActionSuccess} width={83} height={60} alt="" />
          <div className="mt-32">直播间修改成功</div>
          <Button
            className="gradient-primary mt-20"
            style={{
              width: '80px',
            }}
            onClick={() => {
              handleOpenEdit();
            }}
          >
            确定
          </Button>
        </div>
      </div>
    </Dialog>
  );
}
