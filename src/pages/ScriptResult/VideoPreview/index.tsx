/**
 * <AUTHOR>
 * @date 2024/4/19 下午4:47
 * @desc index
 */

import { GemsPreviewer } from '@/components/GemsPreviewer';
import { useReport } from '@/hooks/report';
import { MetaFeedbackSvr } from '@/pb/pb';
import { PlayConfig, PlayService, Script } from '@/type/pagedoo';
import { playService } from '@/utils/play';
import { useDebounceFn } from 'ahooks';
import { useEffect, useState } from 'react';
import { Edit2Icon } from 'tdesign-icons-react';
import { Dialog, MessagePlugin, Popconfirm, Textarea } from 'tdesign-react';

interface IProps {
  contentId?: string;
  scriptId: string;
  researchId: string;
  currentScript: Script | undefined;
  currentPlayConfig?: PlayConfig | undefined;
}

// export function VideoPreview(props: IProps) {
//   const { scriptId, researchId, currentScript } = props;
//   const [feedback, setFeedback] = useState('');
//   const [visible, setVisible] = useState(false);
//   const domAction = useReport();
//
//   const [shotSchemaData, setShotSchemaData] = useState<Awaited<
//     ReturnType<PlayService['getPageSchemaShot']>
//   > | null>(null);
//   const [pageSchemaData, setPageSchemaData] = useState<Awaited<
//     ReturnType<PlayService['getPageSchema']>
//   > | null>(null);
//
//   useEffect(() => {
//     if (!currentScript) return;
//     const init = async () => {
//       try {
//         console.log(currentScript, 'scriptscriptscript');
//         // 预览的页面协议
//         const shotSchemaData = await playService.getPageSchemaShot(
//           currentScript
//         );
//         // 完整的页面协议
//         const pageSchemaData = await playService.getPageSchema(currentScript);
//         setShotSchemaData(shotSchemaData);
//         setPageSchemaData(pageSchemaData);
//       } catch (e) {
//         console.error(e, '获取视频预览失败');
//         void MessagePlugin.error('获取视频预览失败');
//       }
//     };
//     init().then();
//   }, [currentScript]);
//
//   const { run: inputReport } = useDebounceFn(
//     () => {
//       domAction.emit('ItemClick', {
//         module_id: 'VideoFeedback',
//         meta_data: {
//           Script_id: scriptId,
//           Action: 'Input',
//         },
//       });
//     },
//     { wait: 500 }
//   );
//
//   // const globalData = playServiceMock.getPageSchemaShot();
//   // console.log(globalData, 'globalData');
//
//   const handleClick = () => {
//     domAction.emit('ItemClick', {
//       module_id: 'VideoFeedback',
//       meta_data: {
//         Script_id: scriptId,
//         Action: 'Play',
//       },
//     });
//     setVisible(true);
//   };
//
//   const handleClickFeedback = () => {
//     domAction.emit('ItemClick', {
//       module_id: 'VideoFeedback',
//       meta_data: {
//         Script_id: scriptId,
//         Action: 'Exposure',
//       },
//     });
//   };
//
//   const handleClose = () => {
//     setVisible(false);
//   };
//
//   const handleSubmit = async () => {
//     domAction.emit('ItemClick', {
//       module_id: 'VideoFeedback',
//       meta_data: {
//         Script_id: scriptId,
//         Action: 'Submit',
//       },
//     });
//
//     try {
//       await MetaFeedbackSvr.VideoFeedbackInfo({
//         // TODO:调整一下入参数
//         research_id: researchId,
//         script_id: scriptId,
//         video_evaluate_info: feedback,
//       });
//     } catch (e) {
//       console.error(e, '视频反馈失败');
//       void MessagePlugin.error('视频反馈失败');
//     }
//   };
//
//   const content = (
//     <div style={{ width: '328px' }}>
//       <div className="mb-8">视频反馈</div>
//       <Textarea
//         placeholder="请告诉我么您对视频的看法...."
//         value={feedback}
//         autosize={{ minRows: 3, maxRows: 5 }}
//         onChange={(value) => {
//           setFeedback(value);
//           inputReport();
//         }}
//       />
//     </div>
//   );
//   return (
//     <div className="video-preview-box">
//       <header className="header px-16">
//         <div className="flex-1">
//           <span>
//             <svg
//               width="20"
//               height="20"
//               viewBox="0 0 20 20"
//               fill="none"
//               xmlns="http://www.w3.org/2000/svg"
//             >
//               <path
//                 d="M10 18.75C14.8325 18.75 18.75 14.8325 18.75 10C18.75 5.16751 14.8325 1.25 10 1.25C5.16751 1.25 1.25 5.16751 1.25 10C1.25 14.8325 5.16751 18.75 10 18.75ZM9.24988 5.74997C9.24988 5.33577 9.58565 5 9.99985 5V5C10.414 5 10.7498 5.33577 10.7498 5.74997V5.74997C10.7498 6.16417 10.414 6.49994 9.99985 6.49994V6.49994C9.58565 6.49994 9.24988 6.16417 9.24988 5.74997V5.74997ZM9.38202 8.75C9.38202 8.40482 9.66184 8.125 10.007 8.125V8.125C10.3522 8.125 10.632 8.40482 10.632 8.75V14.3747C10.632 14.7199 10.3522 14.9997 10.007 14.9997V14.9997C9.66184 14.9997 9.38202 14.7199 9.38202 14.3747V8.75Z"
//                 fill="url(#paint0_linear_159_5099)"
//               />
//               <defs>
//                 <linearGradient
//                   id="paint0_linear_159_5099"
//                   x1="1.24825"
//                   y1="18.75"
//                   x2="19.3133"
//                   y2="18.1459"
//                   gradientUnits="userSpaceOnUse"
//                 >
//                   <stop stopColor="#0153FF" />
//                   <stop offset="0.498979" stopColor="#2E7FFD" />
//                   <stop offset="1" stopColor="#C1A3FD" />
//                 </linearGradient>
//               </defs>
//             </svg>
//           </span>
//           <span className="ml-8">视频由AI加工生成，请注意甄别</span>
//         </div>
//         <Edit2Icon style={{ color: '#0047F9' }} />
//         <Popconfirm
//           icon={<></>}
//           content={content}
//           confirmBtn="提交"
//           onConfirm={handleSubmit}
//           cancelBtn={null}
//         >
//           <div
//             className="ml-4 cursor-pointer"
//             style={{ color: '#0047F9' }}
//             onClick={handleClickFeedback}
//           >
//             我要反馈
//           </div>
//         </Popconfirm>
//       </header>
//
//       <div className="video-preview-box__content">
//         <div className="video-play">
//           {/* <Image*/}
//           {/*  src="https://pagedoo.pay.qq.com/material/202208301145014483768320/c52bc8ea9abba91186e676f6dabbdb56.png"*/}
//           {/*  fit="fill"*/}
//           {/*  style={{ width: 248, height: 394 }}*/}
//           {/*/ >*/}
//           {shotSchemaData?.global && (
//             <div>
//               <GemsPreviewer
//                 width={248}
//                 height={394}
//                 globalData={shotSchemaData.global}
//               />
//             </div>
//           )}
//           {/* <GemsPreviewer globalData={shotSchemaData?.global} />*/}
//           <div className="preview-mask">
//             <span className="cursor-pointer" onClick={() => handleClick()}>
//               <svg
//                 width="40"
//                 height="40"
//                 viewBox="0 0 40 40"
//                 fill="none"
//                 xmlns="http://www.w3.org/2000/svg"
//               >
//                 <path
//                   d="M40 20C40 8.95431 31.0457 0 20 0C8.95431 -6.11959e-06 0 8.9543 0 20C-6.11958e-06 31.0457 8.9543 40 20 40C31.0457 40 40 31.0457 40 20ZM28.5269 20.6512L15.7439 28.0314C15.2426 28.3208 14.616 27.9591 14.616 27.3802L14.616 12.6198C14.616 12.0409 15.2426 11.6791 15.7439 11.9686L28.5269 19.3488C29.0282 19.6382 29.0282 20.3617 28.5269 20.6512Z"
//                   fill="white"
//                 />
//               </svg>
//             </span>
//           </div>
//         </div>
//       </div>
//
//       <Dialog
//         width="auto"
//         closeOnOverlayClick={false}
//         placement="center"
//         destroyOnClose
//         header={
//           <div
//             className="flex items-center"
//             style={{ width: '100%', fontWeight: 400, fontSize: '14px' }}
//           >
//             <div
//               className="flex-1"
//               style={{ fontWeight: 600, fontSize: '16px' }}
//             >
//               视频预览
//             </div>
//             <div>
//               <Popconfirm
//                 icon={<></>}
//                 content={content}
//                 confirmBtn="提交"
//                 onConfirm={handleSubmit}
//                 cancelBtn={null}
//                 placement="bottom-left"
//               >
//                 <Edit2Icon size="12" style={{ color: '#0047F9' }} />
//                 <span
//                   className="cursor-pointer"
//                   style={{ color: '#0047F9' }}
//                   onClick={handleClickFeedback}
//                 >
//                   我要反馈
//                 </span>
//               </Popconfirm>
//             </div>
//           </div>
//         }
//         visible={visible}
//         onClose={handleClose}
//         footer={null}
//       >
//         <div
//           style={{
//             display: 'flex',
//             justifyContent: 'center',
//             alignItems: 'center',
//             width: '928px',
//             height: '933px',
//             borderRadius: '4px',
//             backgroundColor: '#2e2e2e',
//           }}
//         >
//           {pageSchemaData?.global && (
//             <div>
//               <GemsPreviewer
//                 width={455}
//                 height={812}
//                 globalData={pageSchemaData.global}
//               />
//             </div>
//           )}
//         </div>
//       </Dialog>
//     </div>
//   );
// }

export const useVideoPreview = (props: IProps | undefined) => {
  const { scriptId, researchId, currentScript, currentPlayConfig } = props || {
    scriptId: '',
    researchId: '',
  };
  const [feedback, setFeedback] = useState('');
  const [visible, setVisible] = useState(false);
  const domAction = useReport();

  // console.log(currentScript, 'currentScript');

  // const [shotSchemaData, setShotSchemaData] = useState<Awaited<
  //   ReturnType<PlayService['getPageSchemaShot']>
  // > | null>(null);
  const [pageSchemaData, setPageSchemaData] = useState<Awaited<
    ReturnType<PlayService['getPageSchema']>
  > | null>(null);

  useEffect(() => {
    if (!currentPlayConfig && !currentScript) return;
    const init = async () => {
      const playConfig =
        currentPlayConfig ||
        (currentScript
          ? await playService.getPlayScript(currentScript)
          : undefined);
      if (!playConfig) throw new Error('playConfig为空');
      // const globalData = await playService.getPageSchemaShotFromConfig(
      //   playConfig
      // );
      const pageSchemaData = await playService.getPageSchemaFromConfig(
        playConfig
      );
      pageSchemaData.global['pagedoo-live'] = {
        id: props?.contentId || '-',
      };
      // 预览设置为不自动播放 需要点击才能播放
      (
        pageSchemaData as any
      ).global.pages[0].data.components[0].children[0].data.play = false;
      // console.log(globalData, '播放页面协议');
      // setShotSchemaData(globalData);
      setPageSchemaData(pageSchemaData);
    };
    init().catch((e) => {
      console.error(e, '获取视频预览失败');
      void MessagePlugin.error('获取视频预览失败');
    });
  }, [currentScript, currentPlayConfig, props?.contentId]);

  const { run: inputReport } = useDebounceFn(
    () => {
      domAction.emit('ItemClick', {
        module_id: 'VideoFeedback',
        meta_data: {
          Script_id: scriptId,
          Action: 'Input',
        },
      });
    },
    { wait: 500 }
  );

  // const globalData = playServiceMock.getPageSchemaShot();
  // console.log(globalData, 'globalData');

  const handleClick = () => {
    domAction.emit('ItemClick', {
      module_id: 'VideoFeedback',
      meta_data: {
        Script_id: scriptId,
        Action: 'Play',
      },
    });
    setVisible(true);
  };

  const handleClickFeedback = () => {
    domAction.emit('ItemClick', {
      module_id: 'VideoFeedback',
      meta_data: {
        Script_id: scriptId,
        Action: 'Exposure',
      },
    });
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleSubmit = async () => {
    domAction.emit('ItemClick', {
      module_id: 'VideoFeedback',
      meta_data: {
        Script_id: scriptId,
        Action: 'Submit',
      },
    });

    try {
      await MetaFeedbackSvr.VideoFeedbackInfo({
        // TODO:调整一下入参数
        research_id: researchId,
        script_id: scriptId,
        video_evaluate_info: feedback,
      });
    } catch (e) {
      console.error(e, '视频反馈失败');
      void MessagePlugin.error('视频反馈失败');
    }
  };

  const content = (
    <div style={{ width: '328px' }}>
      <div className="mb-8">视频反馈</div>
      <Textarea
        placeholder="请告诉我们您对视频的看法...."
        value={feedback}
        autosize={{ minRows: 3, maxRows: 5 }}
        onChange={(value) => {
          setFeedback(value);
          inputReport();
        }}
      />
    </div>
  );
  const meta = pageSchemaData?.global['pagedoo-play-script'].meta;

  const dialogBody = (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '20px',
        // width: '928px',
        // height: '933px',
        borderRadius: '4px',
        backgroundColor: '#2e2e2e',
      }}
    >
      {pageSchemaData?.global && (
        <div>
          <GemsPreviewer
            width={currentScript?.size?.[0] || 455}
            height={currentScript?.size?.[1] || 812}
            globalData={pageSchemaData.global}
          />
        </div>
      )}
    </div>
  );

  return {
    open() {
      setVisible(true);
    },
    videoViewer: dialogBody,
    dom: (
      <div className="video-preview-box">
        <Dialog
          width="auto"
          closeOnOverlayClick={false}
          placement="center"
          destroyOnClose
          header={
            <div
              className="flex items-center"
              style={{ width: '100%', fontWeight: 400, fontSize: '14px' }}
            >
              <div
                className="flex-1"
                style={{ fontWeight: 600, fontSize: '16px' }}
              >
                视频预览
              </div>
              <div>
                <Popconfirm
                  icon={<></>}
                  content={content}
                  confirmBtn="提交"
                  onConfirm={handleSubmit}
                  cancelBtn={null}
                  placement="bottom-left"
                >
                  <Edit2Icon size="12" style={{ color: '#0047F9' }} />
                  <span
                    className="cursor-pointer"
                    style={{ color: '#0047F9' }}
                    onClick={handleClickFeedback}
                  >
                    我要反馈
                  </span>
                </Popconfirm>
              </div>
            </div>
          }
          visible={visible}
          onClose={handleClose}
          footer={null}
        >
          {dialogBody}
        </Dialog>
      </div>
    ),
  };
};
