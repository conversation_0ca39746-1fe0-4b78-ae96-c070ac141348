/**
 * <AUTHOR>
 * @date 2024/6/12 下午5:24
 * @desc index
 */

import React from 'react';
import { VideoTabContent } from '@/pages/List/video';
import './index.less';
import { useNavigate } from 'react-router-dom';
import { Page, MainContent } from '@/components/Layout';

const classPrefix = 'video-list-page';

// 目前只有课程使用这个路由
export function VideoList() {
  const navigate = useNavigate();
  const handleCreate = () => {
    navigate('/video-template?from=course');
  };
  return (
    <Page title="视频管理" className={classPrefix}>
      <MainContent>
        <VideoTabContent contentType="course" handleCreate={handleCreate} />
      </MainContent>
    </Page>
  );
}
