.video-list-page {
  width: 100%;

  &-title {
    color: rgba(0, 0, 0, 0.90);
    font-style: normal;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .t-tabs__content {
    width: 100%;
    padding: 20px;
  }

  .main_content {
    height: 100%;
  }

  .tab_content {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background: #FFFFFF;

    .action_bar {
      padding: 8px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      justify-content: space-between;

      .btn {
        width: 96px;
        height: 32px;
        color: #fff;
        text-align: center;
        line-height: 32px;
        border-radius: 4px;
        background-image: linear-gradient(to right,
            rgba(1, 83, 255, 1),
            rgba(46, 127, 253, 1),
            rgba(193, 163, 253, 1));
        cursor: pointer;
        user-select: none;
      }

      .search_input {
        width: 280px;
      }
    }

    .loading_wrap {
      width: 100%;
      height: calc(100vh - 250px);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .table {
      width: 100%;
      height: 0;
      flex: 1;
      // height: calc(100vh - 250px);
      overflow: hidden;

      .t-table {
        width: 100%;
        height: 100%;
        overflow: auto;


        .t-table__header {
          tr > th {
            background-color: #f8f6fb;
          }
        }

        &::-webkit-scrollbar {
          width: 4px;
          background-color: rgba(0, 0, 0, 0.06);

          &-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
          }
        }
      }

      .poster {
        width: 72px;
        height: 137px;
        background: #e8e8e8;
        border: 1px solid #e8e8e8;
      }

      .status-table__cell {
        text-align: left;
        display: flex;
      }

      .status_tag {
        --c: #f4f6ff;
        position: relative;
        height: 24px;
        padding: 0 10px 0 4px;
        line-height: 24px;
        font-size: 14px;
        color: rgba(0, 10, 41, 0.26);
        background: linear-gradient(to right, var(--c), #faf5fc);
        display: inline-block;
        border-radius: 0 12px 16px 0;
        white-space: nowrap;

        &::before {
          content: '';
          position: absolute;
          left: -2px;
          top: 0;
          bottom: 0;
          width: 6px;
          border-radius: 4px;
          transform: skewX(-10deg);
          background: var(--c);
          display: block;
        }

        &.video_status_2 {
          --c: rgb(248, 210, 18);
          color: rgb(198, 119, 0);
          background: linear-gradient(to right, var(--c), rgb(247, 235, 93));

          &::before {
            background: var(--c);
          }
        }

        &.meta_live_status_5,
        &.video_status_3 {
          --c: #ffd513;
          color: #b09d00;
          background: linear-gradient(to right, var(--c), #fff181);

          &::before {
            background: var(--c);
          }
        }

        &.meta_live_status_4,
        &.video_status_4 {
          --c: #e44527;
          color: #fff;
          background: linear-gradient(to right, var(--c), #f7a59e);

          &::before {
            background: var(--c);
          }
        }

        &.meta_live_status_3,
        &.video_status_5 {
          --c: rgba(235, 248, 240, 1);
          color: rgba(0, 198, 83, 1);
          background: linear-gradient(to right,
              var(--c),
              rgba(222, 248, 232, 1));

          &::before {
            background: var(--c);
          }
        }
      }

      .operation_groups {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        color: rgba(0, 71, 249, 1);
        gap: 2px 20px;

        .item {
          cursor: pointer;

          &.danger {
            color: rgba(255, 94, 85, 1);
          }

          &.disabled {
            color: #90b7fe;
            cursor: not-allowed;
          }
        }
      }
    }

    .pagination {
      height: 56px;
      padding: 20px;
      border-top: 1px solid #e8e8e8;
    }
  }
}

.make_video_dialog,
.start_meta_live_dialog {
  .t-dialog {
    padding: 20px;

    .t-dialog__body {
      padding-bottom: 0;
    }

    .dialog_body {
      .start_guide {
        margin-top: 16px;
        padding: 16px;
        background: linear-gradient(to right,
            rgba(244, 246, 255, 1),
            rgba(250, 245, 252, 1));
        border-radius: 4px;

        .title {
          margin-bottom: 15px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.9);
        }

        .content {
          display: flex;

          .guide {
            flex: 1;

            .step {
              .t-steps-item__title {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.9);
              }

              .t-steps-item__description {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.4);
              }

              .t-steps-item {
                margin-bottom: 0;
                padding-bottom: 10px;
              }

              .b_text {
                font-weight: normal;
                color: rgba(0, 71, 249, 1);
              }

              .img {
                width: 130px;
                height: 78px;
                background-color: rgba(0, 0, 0, 0.1);
                background-size: 100% 100%;
                display: block;
              }
            }
          }

          .qrcode_box {
            width: 274px;
            padding: 8px 16px;
            font-size: 14px;
            text-align: center;
            background: #fff;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;

            .notice {
              color: rgba(0, 0, 0, 0.3);
            }

            .qrcode {
              position: relative;
              width: 178px;
              height: 178px;
              background-size: 100% 100%;
              background-color: rgba(0, 0, 0, 0.1);
            }

            .refresh {
              color: rgba(0, 71, 249, 1);
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}


