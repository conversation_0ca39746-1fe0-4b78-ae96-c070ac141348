import { useMemo, useRef, useState } from 'react';
import { useMemoizedFn, useRafInterval, useRequest, useUnmount } from 'ahooks';
import { Development, LiveInteractionAnswerSvr } from '@/pb/pb';
import DeleteLiveAnswer = LiveInteractionAnswerSvr.DeleteLiveAnswer;
import UpdateLiveAnswer = LiveInteractionAnswerSvr.UpdateLiveAnswer;
import CreateLiveAnswer = LiveInteractionAnswerSvr.CreateLiveAnswer;
import { DropResult, ResponderProvided } from 'react-beautiful-dnd';
import SortLiveAnswer = LiveInteractionAnswerSvr.SortLiveAnswer;
import { MessagePlugin } from 'tdesign-react';

export type AnswerDataList =
  | (
      | {
          id: string;
          type: '已回答';
          text: string;
        }
      | {
          id: string;
          type: '未回答';
          text: string;
        }
      | {
          id: string;
          type: '回答中';
          text: string;
        }
      | {
          id: string;
          type: '已回答暂停';
        }
      | {
          id: string;
          type: '暂停';
        }
    )[];
export type AnswerList =
  | (
      | {
          id: string;
          type: '已回答';
          text: string;
        }
      | {
          id: string;
          type: '未回答';
          text: string;
          onChange(text: string): Promise<void>;
          onDelete(): Promise<void>;
          onPause(): Promise<void>;
          onAdd(text?: string): Promise<void>;
        }
      | {
          id: string;
          type: '回答中';
          text: string;
          onPause(): Promise<void>;
        }
      | {
          id: string;
          type: '已回答暂停';
        }
      | {
          id: string;
          type: '暂停';
          onDelete(): Promise<void>;
          onAdd(text?: string): Promise<void>;
        }
    )[];
const PLAY_FLAG_KEY = 'switch_of_immediate_interaction';
export const useAnswer = (liveID: string) => {
  const [list, setList] = useState<AnswerDataList>([]);
  const [playStatus, setPlayStatus] = useState<'play' | 'stop'>('stop');
  const { runAsync: queryPlayStatus } = useRequest(
    () => {
      return Development.GetLiveExtendConfigItems({ live_id: liveID });
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );
  // // 拉列表
  const { runAsync } = useRequest(
    (context: string) =>
      LiveInteractionAnswerSvr.GetLiveAnswerList({
        live_id: liveID,
        context,
        count: 1000,
      }),
    {
      manual: true,
      refreshDeps: [liveID],
    }
  );
  useUnmount(() => {
    setList([]);
  });
  const updateList = useMemoizedFn(
    (
      beforeAnswerId: string | undefined,
      records: Awaited<
        ReturnType<typeof LiveInteractionAnswerSvr.GetLiveAnswerList>
      >['records']
    ) => {
      const newList = getAnswerList(records);
      // 在这个节点之后添加
      setList((list) => {
        const id =
          beforeAnswerId ||
          newList.find((i) => list.find((j) => j.id === i.id))?.id;
        const index = list.findIndex((i) => i.id === id);
        if (index === -1) return newList;
        return [
          ...list
            .slice(0, index + 1)
            .filter((i) => !newList.find((j) => j.id === i.id)),
          ...newList,
          ...list
            .slice(index + 1)
            .filter((i) => !newList.find((j) => j.id === i.id)),
        ];
      });
    }
  );
  useRafInterval(
    async () => {
      const data = await queryPlayStatus();
      const find = data.records.find(
        (item) => item.config_item_id === PLAY_FLAG_KEY
      );
      if (find) {
        console.debug(find, '互动结果');
        const value = find.config_item_value;
        setPlayStatus(value === 'true' ? 'play' : 'stop');
      } else {
        //   说明此时没有开启互动
        setPlayStatus('stop');
      }
    },
    1000,
    { immediate: true }
  );
  useRafInterval(
    async () => {
      for (;;) {
        const context =
          list[list.findIndex((i) => i.type === '未回答') - 1]?.id || '';
        const data = await runAsync(context);
        updateList(context, data.records);
        if (!data.has_next) break;
        if (!data.context) break;
      }
    },
    1000,
    { immediate: true }
  );
  // 最后的context
  const last = useRef('');
  useRafInterval(
    async () => {
      for (;;) {
        const context = last.current;
        const data = await runAsync(context);
        updateList(context, data.records);
        if (!data.has_next) break;
        if (!data.context) break;
        last.current = data.context;
      }
    },
    1000,
    { immediate: true }
  );
  // 每60秒全量刷新列表
  useRafInterval(async () => {
    let context = '';
    for (;;) {
      const data = await runAsync(context);
      updateList(context, data.records);
      if (!data.has_next) break;
      if (!data.context) break;
      context = data.context;
    }
  }, 5000);
  const resultList = useMemo<AnswerList>(() => {
    return list
      .map((i) => {
        const onAdd = async (text = '') => {
          const answer = await CreateLiveAnswer({
            answer: text,
            answer_type: 2,
            live_id: liveID,
            target_answer_id: i.id,
          });
          setList((list) => {
            const item = {
              id: answer.answer_id,
              type: '未回答',
              text,
            };
            const index = list.findIndex((item) => item.id === i.id);
            if (index === -1) return list;
            return [
              ...list.slice(0, index + 1),
              item,
              ...list.slice(index + 1),
            ] as AnswerDataList;
          });
        };
        const onPause = async () => {
          const answer = await CreateLiveAnswer({
            answer: '',
            answer_type: 3,
            live_id: liveID,
            target_answer_id: i.id,
          });
          // 在当前节点下方添加一个暂停节点
          setList((list) => {
            const item = {
              id: answer.answer_id,
              type: '暂停',
            };
            const index = list.findIndex((item) => item.id === i.id);
            console.log(index, 'index');
            if (index === -1) return list;
            return [
              ...list.slice(0, index + 1),
              item,
              ...list.slice(index + 1),
            ] as AnswerDataList;
          });
        };
        const onDelete = async () => {
          await DeleteLiveAnswer({
            answer_id: i.id,
            live_id: liveID,
          }).catch(console.warn);
          setList((list) => list.filter((item) => item.id !== i.id));
        };
        const onChange = async (text: string) => {
          // console.log('onChange', text);
          UpdateLiveAnswer({
            answer: text,
            answer_id: i.id,
            live_id: liveID,
          }).catch(console.warn);
          setList((list) =>
            list.map((item) => (item.id === i.id ? { ...item, text } : item))
          );
        };
        if (i.type === '暂停') {
          return {
            ...i,
            onDelete,
            onAdd,
          };
        }
        if (i.type === '已回答') {
          return i;
        }
        if (i.type === '未回答') {
          return {
            ...i,
            onAdd,
            onChange,
            onDelete,
            onPause,
          };
        }
        if (i.type === '回答中') {
          return {
            ...i,
            onPause,
          };
        }
        if (i.type === '已回答暂停') {
          return i;
        }
        return i || {};
      })
      .filter(Boolean) as AnswerList;
  }, [list, liveID]);
  return {
    playStatus,
    list: resultList,
    async add(text = '') {
      const targetId = list[list.length - 1]?.id || '';
      // 添加空节点
      const answer = await CreateLiveAnswer({
        answer: text,
        answer_type: 2,
        live_id: liveID,
        target_answer_id: targetId,
      });
      setList((list) => {
        const item = {
          id: answer.answer_id,
          type: '未回答',
          text,
        };
        if (targetId) {
          const index = list.findIndex((item) => item.id === targetId);
          if (index === -1) return list;
          return [
            ...list.slice(0, index + 1),
            item,
            ...list.slice(index + 1),
          ] as AnswerDataList;
        }
        return [...list, item] as AnswerDataList;
      });
    },
    // 停止该环节互动
    async stop() {
      // 暂停
      // 在当前回答或已回答完的下方添加一个暂停节点
      // const targetId =
      //   list.findLast((i) => i.type === '回答中')?.id ||
      //   list.findLast((i) => i.type === '已回答')?.id ||
      //   '';
      try {
        await Development.UpdateLiveExtendConfigItem({
          live_id: liveID,
          config_item_id: PLAY_FLAG_KEY,
          config_item_value: '',
        });
        setPlayStatus('stop');
        // 添加空节点
        // const answer = await CreateLiveAnswer({
        //   answer: '',
        //   answer_type: 3,
        //   live_id: liveID,
        //   target_answer_id: targetId,
        // });
        // setList((list) => {
        //   const item = {
        //     id: answer.answer_id,
        //     type: '暂停',
        //   };
        //   if (targetId) {
        //     const index = list.findIndex((item) => item.id === targetId);
        //     if (index === -1) return list;
        //     return [
        //       ...list.slice(0, index + 1),
        //       item,
        //       ...list.slice(index + 1),
        //     ] as AnswerDataList;
        //   }
        //   return [...list, item] as AnswerDataList;
        // });
      } catch (e) {
        console.error(e);
        void MessagePlugin.error('操作失败');
      }
    },

    async start() {
      try {
        await Development.UpdateLiveExtendConfigItem({
          live_id: liveID,
          config_item_id: PLAY_FLAG_KEY,
          config_item_value: 'true',
        });
        setPlayStatus('play');
      } catch (e) {
        console.error(e);
        void MessagePlugin.error('操作失败');
      }
    },
    onDragEnd(result: DropResult, _provided: ResponderProvided) {
      console.log(result, _provided, 'args');
      const { destination } = result;
      if (!destination) return;
      debugger;
      const sourceIndex = result.source.index;
      const destinationIndex = destination.index;
      if (sourceIndex === destinationIndex) return;
      if (destinationIndex <= 0) return;
      SortLiveAnswer({
        answer_id: list[sourceIndex].id,
        live_id: liveID,
        target_answer_id:
          list[
            destinationIndex > sourceIndex
              ? destinationIndex
              : destinationIndex - 1
          ]?.id,
      }).then();
      setList((list) => {
        const [removed] = list.splice(result.source.index, 1);
        list.splice(destination.index, 0, removed);
        return [...list] as AnswerDataList;
      });
    },
    info: `${
      list.filter((i) => i.type === '已回答' || i.type === '已回答暂停').length
    }条`,
  };
};

const getAnswerList = (
  records: Awaited<
    ReturnType<typeof LiveInteractionAnswerSvr.GetLiveAnswerList>
  >['records']
) => {
  return records
    .map(function (i): AnswerDataList[0] | undefined {
      // 播报状态, 1:表示已播报; 2:表示未播报; 3：表示正在播报; -1:表示无效
      if (i.answer_type === 3) {
        if (i.status === 2) {
          return {
            id: i.answer_id,
            type: '暂停',
          };
        }
        return {
          id: i.answer_id,
          type: '已回答暂停',
        };
      }
      if (i.status === 1) {
        return {
          id: i.answer_id,
          type: '已回答',
          text: i.answer,
        };
      }
      if (i.status === 2) {
        return {
          id: i.answer_id,
          type: '未回答',
          text: i.answer,
        };
      }
      if (i.status === 3) {
        return {
          id: i.answer_id,
          type: '回答中',
          text: i.answer,
        };
      }
    })
    .filter(Boolean) as AnswerDataList;
};
