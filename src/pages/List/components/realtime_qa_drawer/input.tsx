import { css } from '@emotion/react';
import {
  <PERSON><PERSON>,
  MessagePlugin,
  Popup,
  PopupRef,
  Textarea,
  Tooltip,
} from 'tdesign-react';
import React, { useRef, useState } from 'react';
import { QuickReply } from '@/pages/List/components/realtime_qa_drawer/RealTimeAnswer';
import {
  Translation,
  TranslationOptions,
} from '@/pages/List/components/translation';
import { useRecoilValue } from 'recoil';
import { TranslationAtom } from '@/pages/List/components/realtime_qa_drawer/store';
import { getBaseUrl, getTencentLoginCheck, getUUID } from '@/pb/config';

export function RealtimeInputPanel(props: {
  onSend: (text: string) => Promise<void>;
}) {
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const [text, setText] = useState('');
  const [abortController, setAbortController] = useState(
    () => new AbortController()
  );
  const multilingual = useRecoilValue(TranslationAtom);

  const translateState = useState<TranslationOptions>(['zh', 'en']);
  const [translateText, setTranslateText] = useState('');
  const popupRef = useRef<PopupRef>(null);
  const send = () => {
    if (text) {
      console.log('text', text);
      props.onSend(text).catch((err) => {
        MessagePlugin.error({
          content: `消息发送失败${err}`,
        });
      });
      setText('');
      setTranslateText('');
      setShow(false);
    }
  };
  const translate = async () => {
    setLoading(true);
    abortController.abort();
    const newAbortController = new AbortController();
    setAbortController(newAbortController);

    callTranslateAPI(translateState[0], text, {
      signal: newAbortController.signal,
    })
      .then((res) => {
        setText(res);
        setTranslateText(`原文：${text}`);
      })
      .catch((err) => {
        MessagePlugin.error({ content: `翻译失败：${err}` });
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <div style={{ height: 68, position: 'relative' }}>
      <div
        style={{
          transition: 'all 0.3s ease-in-out',
          height: show ? 318 : 68,
          backgroundColor: '#fff',
          padding: '9px 8px 7px 8px',
          position: 'absolute',
          bottom: 0,
          width: '100%',
        }}
        css={css`
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
          box-shadow: 0px -8px 10px -5px rgba(8, 26, 81, 0.08),
            0px -16px 24px 2px rgba(8, 26, 81, 0.04),
            0px -6px 30px 5px rgba(8, 26, 81, 0.05);
        `}
      >
        {/* 折叠*/}
        {show && (
          <div
            style={{
              position: 'absolute',
              top: -18,
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <svg
              style={{ cursor: 'pointer' }}
              onClick={() => setShow(false)}
              width="58"
              height="20"
              viewBox="0 0 58 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 8C0 3.58172 3.58172 0 8 0H50C54.4183 0 58 3.58172 58 8V20H0V8Z"
                fill="white"
              />
              <path
                d="M24.9997 8.9189C24.7458 8.66506 24.7458 8.2535 24.9997 7.99966V7.99966C25.2535 7.74582 25.6651 7.74582 25.9189 7.99966L28.9997 11.0804L32.0804 7.99966C32.3343 7.74582 32.7458 7.74582 32.9997 7.99966V7.99966C33.2535 8.2535 33.2535 8.66506 32.9997 8.9189L29.7068 12.2118C29.3162 12.6023 28.6831 12.6023 28.2926 12.2118L24.9997 8.9189Z"
                fill="black"
                fillOpacity="0.9"
              />
            </svg>
          </div>
        )}
        <Textarea
          value={text}
          onChange={setText}
          onClick={() => {
            setShow(true);
          }}
          onFocus={() => {
            setShow(true);
          }}
          onKeydown={(_, { e }) => {
            console.log('ee', e.key);
            // ctrl+enter or command+enter
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
              e.preventDefault();
              send();
            }
          }}
          placeholder="请输入..."
          style={{
            height: show
              ? translateText
                ? 'calc(100% - 91px)'
                : 'calc(100% - 62px)'
              : '100%',
          }}
          css={css`
            border-radius: 8px;
            > div {
              height: 100%;
              border-radius: 8px;
            }
            textarea {
              resize: none;
              height: 100% !important;
              font-size: 16px;
              line-height: 24px;
              padding: 12px 20px 16px 20px;
            }
          `}
        />
        {show && translateText && (
          <Tooltip content={translateText} theme="light">
            <span
              style={{
                display: 'inline-block',
                maxWidth: '100%',
                height: 16,
                paddingLeft: 3,
                lineHeight: '16px',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                marginTop: 11,
              }}
            >
              {translateText}
            </span>
          </Tooltip>
        )}
        {show && multilingual && (
          <div
            css={css`
              height: 44px;
              background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
              padding: 8px 12px 8px 12px;
              position: absolute;
              bottom: 9px;
              left: 9px;
              border-radius: 4px;
              display: flex;
              gap: 8px;
            `}
          >
            <Translation state={translateState} />
            <Button
              loading={loading}
              disabled={!text}
              theme="default"
              variant="text"
              style={{
                background:
                  'linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%)',
                opacity: text ? 1 : 0.3,
                transition: 'all 0.3s ease-in-out',
                color: 'rgba(255, 255, 255, 0.9)',
                border: 0,
              }}
              onClick={translate}
            >
              翻译
            </Button>
          </div>
        )}
        <div
          css={css`
            position: absolute;
            bottom: 23px;
            right: 66px;
          `}
        >
          <Popup
            ref={popupRef}
            content={
              <div style={{ height: 424 }}>
                <QuickReply
                  onSelect={(text) => {
                    setText((prev) => prev + text);
                    popupRef.current?.setVisible(false);
                  }}
                />
              </div>
            }
            showArrow
            trigger="click"
          >
            <svg
              style={{
                cursor: 'pointer',
              }}
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              xmlnsXlink="http://www.w3.org/1999/xlink"
            >
              <rect
                width="20"
                height="20"
                fill="url(#pattern0_12951_117349)"
                fillOpacity="0.8"
              />
              <defs>
                <pattern
                  id="pattern0_12951_117349"
                  patternContentUnits="objectBoundingBox"
                  width="1"
                  height="1"
                >
                  <use
                    xlinkHref="#image0_12951_117349"
                    transform="scale(0.005)"
                  />
                </pattern>
                <image
                  id="image0_12951_117349"
                  width="200"
                  height="200"
                  preserveAspectRatio="none"
                  xlinkHref="data:image/png;base64,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"
                />
              </defs>
            </svg>
          </Popup>
        </div>
        <Button
          disabled={!text}
          shape="circle"
          theme="default"
          variant="text"
          style={{
            background: text
              ? 'linear-gradient(89.21deg,#0153ff -0.01%,#8649ff 147.74%)'
              : 'linear-gradient(89.21deg, rgba(1, 83, 255, 0.32) -0.01%, rgba(134, 73, 255, 0.32) 147.74%)',
          }}
          css={css`
            position: absolute;
            bottom: 17px;
            right: 18px;
            border: 0;
          `}
          onClick={send}
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.9738 9.93674C2.31667 9.62246 2.35066 8.67568 3.02864 8.40933L14.9404 3.72971C15.6178 3.46361 16.2869 4.13269 16.0208 4.81005L11.3411 16.7218C11.0748 17.3998 10.128 17.4338 9.81373 16.7767L7.60082 12.1497L2.9738 9.93674ZM8.8589 11.8006L10.5248 15.2839L14.4448 5.30562L4.46658 9.22564L7.94987 10.8916L9.90086 8.94057C10.1519 8.68955 10.5589 8.68955 10.8099 8.94057C11.0609 9.19159 11.0609 9.59858 10.8099 9.84961L8.8589 11.8006Z"
              fill="white"
            />
          </svg>
        </Button>
      </div>
    </div>
  );
}

export const callTranslateAPI = async (
  lang: TranslationOptions,
  text: string,
  options: {
    signal: AbortSignal;
  }
) => {
  // 大语言模型实现
  return await fetch(`https://${getBaseUrl()}/api/v1/multilingual/translate`, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'x-request-id': getUUID(),
      'x-tencent-login-check': await getTencentLoginCheck(
        '/api/v1/multilingual/translate'
      ),
    },
    body: JSON.stringify({
      app_id: 'avatar',
      source_lang: lang[0],
      source_seg: text,
      target_lang: lang[1],
      stream: false,
    }),
    signal: options.signal,
  })
    .then<{ msg: 'success'; target_seg: string }>((res) => res.json())
    .then((res) => {
      if (res.msg === 'success') return res.target_seg;
      throw new Error(`翻译失败:${res.msg}`);
    });
};
