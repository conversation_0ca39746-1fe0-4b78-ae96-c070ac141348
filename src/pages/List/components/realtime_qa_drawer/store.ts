import { atom } from 'recoil';
import { TranslationOptions } from '@/pages/List/components/translation';

// 是否开启翻译
export const TranslationAtom = atom<boolean>({
  key: 'translation-atom',
  default: false,
});

export const TranslationQuestionAtom = atom<TranslationOptions>({
  key: 'translation-question-atom',
  default: ['auto', 'zh'],
});

export const TranslationAnswerAtom = atom<TranslationOptions>({
  key: 'translation-answer-atom',
  default: ['auto', 'en'],
});

export const LiveAddAnswerAtom = atom<
  ((text?: string) => Promise<void>) | undefined
>({
  key: 'live-add-answer-atom',
  default: undefined,
});
