import { css } from '@emotion/react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDraggableInPortal } from '@/pages/List/components/realtime_qa_drawer/useDraggableInPortal';
import { Dialog, Input, Textarea, Tooltip } from 'tdesign-react';
import {
  AnswerList,
  useAnswer,
} from '@/pages/List/components/realtime_qa_drawer/useAnswer';
import { useMemoizedFn, usePagination } from 'ahooks';
import { LiveInteractionAnswerSvr } from '@/pb/pb';
import GetDefaultLiveAnswerList = LiveInteractionAnswerSvr.GetDefaultLiveAnswerList;
import { RealtimeInputPanel } from '@/pages/List/components/realtime_qa_drawer/input';
import { useSetRecoilState } from 'recoil';
import { LiveAddAnswerAtom } from '@/pages/List/components/realtime_qa_drawer/store';

export function RealTimeAnswer(props: { liveID: string }) {
  const renderDraggable = useDraggableInPortal();
  const answer = useAnswer(props.liveID);
  const { list, playStatus, info, stop, start, add } = answer;
  const setter = useSetRecoilState(LiveAddAnswerAtom);
  const memoAdd = useMemoizedFn((text?: string) => {
    return add(text).then(() => {
      setTimeout(() => {
        const scroll = scrollRef.current;
        if (!scroll) return;
        scroll.scrollTo({
          top: scroll.scrollHeight,
          behavior: 'smooth',
        });
      }, 100);
    });
  });
  useEffect(() => {
    setter(() => memoAdd);
  }, [setter, memoAdd]);
  const scrollRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const scroll = scrollRef.current;
    if (!scroll) return;
    let i: ReturnType<typeof setTimeout> | undefined;
    const scrollTo = () => {
      const answered = [
        ...scroll.querySelectorAll<HTMLElement>('.interactive-answered'),
      ];
      const item = answered[answered.length - 1];
      if (!item) return;
      scroll.scrollTo({
        top: item.offsetTop,
        behavior: 'smooth',
      });
    };
    const update = () => {
      clearTimeout(i);
      i = setTimeout(scrollTo, 20000);
    };
    const scrollFn = () => {
      console.log('scroll');
      update();
    };
    update();
    setTimeout(() => {
      scrollTo();
    }, 100);
    scroll.addEventListener('scroll', scrollFn);
    return () => {
      scroll.removeEventListener('scroll', scrollFn);
    };
  }, []);
  const dialogRef = useRef<AddAnswerDialogRef | null>(null);
  return (
    <div
      css={css`
        height: 100%;
        display: flex;
        flex-direction: column;
      `}
    >
      <div
        css={css`
          box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.11);
          padding: 15px;
        `}
      >
        <div
          css={css`
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            align-items: center;
            color: rgba(0, 0, 0, 0.9);
            height: 55px;
            display: flex;
            justify-content: space-between;
          `}
        >
          <div>互动最终话术</div>
          <div
            css={css`
              //styleName: body/medium;
              font-family: PingFang SC;
              font-size: 14px;
              font-weight: 400;
              line-height: 22px;
              text-align: left;
              padding-left: 28px;
              padding-right: 8px;
              color: rgba(0, 0, 0, 0.6);
            `}
          >
            已互动：{info}
          </div>
          {playStatus === 'play' ? (
            <div style={{ cursor: 'pointer' }} onClick={stop}>
              <StopAnswer />
            </div>
          ) : (
            <div style={{ cursor: 'pointer' }} onClick={start}>
              <StartAnswerBtn />
            </div>
          )}
        </div>
        <div
          css={css`
            color: rgba(163, 163, 163, 1);
          `}
        >
          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.00562 10.9998C6.72947 10.9998 6.50562 10.7759 6.50562 10.4998V6C6.50562 5.72386 6.72947 5.5 7.00562 5.5C7.28176 5.5 7.50562 5.72386 7.50562 6V10.4998C7.50562 10.7759 7.28176 10.9998 7.00562 10.9998Z"
              fill="currentColor"
              fillOpacity="0.9"
            />
            <path
              d="M7.59985 3.59998C7.59985 3.26862 7.33124 3 6.99988 3C6.66852 3 6.3999 3.26862 6.3999 3.59998C6.3999 3.93133 6.66852 4.19995 6.99988 4.19995C7.33124 4.19995 7.59985 3.93133 7.59985 3.59998Z"
              fill="currentColor"
              fillOpacity="0.9"
            />
            <path
              d="M0 7C0 10.866 3.13403 14 7 14C10.866 14 14 10.866 14 7C14 3.13403 10.866 0 7 0C3.13403 0 0 3.13403 0 7ZM1 7C1 3.68628 3.68628 1 7 1C10.3137 1 13 3.68628 13 7C13 10.3137 10.3137 13 7 13C3.68628 13 1 10.3137 1 7Z"
              fill="currentColor"
              fillOpacity="0.9"
            />
          </svg>
          <span style={{ lineHeight: '14px', paddingLeft: 4 }}>
            直播间画面与话术推送会有5-8秒延迟
          </span>
        </div>
      </div>
      <div
        ref={scrollRef}
        css={css`
          padding: 9px 16px;
          position: relative;
          flex: 1;
          overflow-y: auto;
        `}
      >
        <AddAnswerDialog ref={dialogRef} />
        <DragDropContext onDragEnd={answer.onDragEnd}>
          <Droppable droppableId="answer-items">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                <span style={{ display: 'none' }}>{provided.placeholder}</span>
                {list.map((i, index) => {
                  if (i.type === '已回答')
                    return (
                      <div key={i.id} className="interactive-answered">
                        <Answer1 i={i} />
                      </div>
                    );
                  if (i.type === '回答中')
                    return (
                      <div key={i.id} className="interactive-answered">
                        <Answer2 i={i} />
                      </div>
                    );
                  if (i.type === '未回答')
                    return (
                      <Draggable key={i.id} draggableId={i.id} index={index}>
                        {renderDraggable((provided) => (
                          <div
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            ref={provided.innerRef}
                          >
                            <Answer3 i={i} />
                            <AddAnswer
                              onClick={() => dialogRef.current?.show(i.onAdd)}
                            />
                          </div>
                        ))}
                      </Draggable>
                    );
                  if (i.type === '暂停')
                    return (
                      <Draggable key={i.id} draggableId={i.id} index={index}>
                        {renderDraggable((provided) => (
                          <div
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            ref={provided.innerRef}
                          >
                            <StopInterActiveLine i={i} />
                            <AddAnswer
                              onClick={() => dialogRef.current?.show(i.onAdd)}
                            />
                          </div>
                        ))}
                      </Draggable>
                    );
                  if (i.type === '已回答暂停') return null;
                  return null;
                })}
                {!list.find(
                  (i) => i.type === '暂停' || i.type === '未回答'
                ) && (
                  <AddAnswer
                    onClick={() => dialogRef.current?.show(answer.add)}
                  />
                )}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
      <RealtimeInputPanel onSend={memoAdd} />
    </div>
  );
}
interface AddAnswerDialogRef {
  show(add: (text: string) => void): void;
}
const AddAnswerDialog = React.forwardRef<AddAnswerDialogRef, {}>(
  (props, ref) => {
    const [fn, setFn] = useState<(text: string) => void>();
    const [text, setText] = useState('');
    const confirm = () => {
      fn?.(text);
      setText('');
      setFn(undefined);
    };
    useImperativeHandle(
      ref,
      () => ({
        show(fn) {
          setFn(() => fn);
        },
      }),
      []
    );
    return (
      <>
        <Dialog
          width={1000}
          header="插入话术"
          visible={!!fn}
          confirmBtn="确认插入"
          // confirmLoading={loading}
          onClose={() => setFn(undefined)}
          onConfirm={confirm}
        >
          <div style={{ display: 'flex', width: '100%', height: '500px' }}>
            <div style={{ flex: 1, height: '100%' }}>
              <Textarea
                value={text}
                onChange={setText}
                placeholder="欢迎来到直播间的宝子们，你们好啊"
                rows={20}
                style={{ height: '100%' }}
                css={css`
                  textarea {
                    height: 100% !important;
                  }
                `}
              />
            </div>
            <div
              style={{ height: '100%', overflowY: 'auto', overflowX: 'hidden' }}
            >
              <QuickReply
                onSelect={(text) => {
                  setText((prev) => prev + text);
                }}
              />
            </div>
          </div>
        </Dialog>
      </>
    );
  }
);
function StopAnswer() {
  return (
    <svg
      style={{ cursor: 'pointer' }}
      width="146"
      height="33"
      viewBox="0 0 146 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="0.5"
        width="146"
        height="32"
        rx="4"
        fill="url(#paint0_linear_7142_54218)"
      />
      <path
        d="M18.611 10.6797C19.7622 9.91054 21.1155 9.5 22.5 9.5C24.3565 9.5 26.137 10.2375 27.4497 11.5503C28.7625 12.863 29.5 14.6435 29.5 16.5C29.5 17.8845 29.0895 19.2378 28.3203 20.389C27.5511 21.5401 26.4579 22.4373 25.1788 22.9672C23.8997 23.497 22.4922 23.6356 21.1344 23.3655C19.7765 23.0954 18.5292 22.4287 17.5503 21.4497C16.5713 20.4708 15.9046 19.2235 15.6345 17.8656C15.3644 16.5078 15.503 15.1003 16.0328 13.8212C16.5627 12.5421 17.4599 11.4489 18.611 10.6797ZM19.9999 13.9999V19.0001H25.0001V13.9999H19.9999Z"
        fill="black"
        fillOpacity="0.9"
      />
      <path
        d="M39.374 9.992L40.312 10.426C39.99 11.462 39.598 12.456 39.136 13.38V22.9H38.114V15.13C37.722 15.718 37.288 16.292 36.826 16.824L36.49 15.76C37.806 14.08 38.772 12.148 39.374 9.992ZM44.582 9.992C44.722 10.342 44.848 10.748 44.988 11.196H48.978V12.092H40.312V11.196H43.98C43.854 10.832 43.714 10.482 43.56 10.16L44.582 9.992ZM41.236 12.904H47.9V15.55H41.236V12.904ZM46.92 14.78V13.674H42.216V14.78H46.92ZM49.006 16.362V18.756H48.25V19.386H45.226V21.654C45.226 22.41 44.806 22.802 43.994 22.802H42.412L42.202 21.822C42.706 21.85 43.182 21.878 43.644 21.878C44.008 21.878 44.19 21.71 44.19 21.402V19.386H41.124V18.756H40.186V16.362H49.006ZM41.166 18.518H48.04V17.244H41.166V18.518ZM57.098 10.132H58.134V14.682H62.446V15.662H58.134V21.318H63.216V22.298H50.784V21.318H53.164V12.582H54.172V21.318H57.098V10.132ZM69.306 11.602H73.058C72.848 11.042 72.61 10.524 72.33 10.048L73.38 9.88C73.604 10.384 73.842 10.958 74.066 11.602H77.44V12.512H73.366C72.484 13.842 71.714 14.794 71.056 15.368C72.064 15.326 73.072 15.27 74.08 15.2C74.444 14.71 74.78 14.178 75.088 13.618L75.942 14.066C74.584 16.614 72.596 18.35 69.992 19.288L69.46 18.448C71 17.93 72.288 17.132 73.324 16.068C72.232 16.138 71.056 16.194 69.81 16.236L69.586 15.368C69.67 15.34 69.726 15.312 69.782 15.284C70.426 14.934 71.252 14.01 72.26 12.512H69.306V11.602ZM74.206 20.086C72.834 21.29 71.238 22.214 69.418 22.83L68.872 21.962C72.246 20.884 74.78 18.798 76.46 15.69L77.314 16.152C76.614 17.412 75.788 18.532 74.85 19.484C75.858 20.352 76.726 21.22 77.454 22.102L76.726 22.83C76.096 21.962 75.256 21.038 74.206 20.086ZM66.296 10.23C67.22 10.93 67.99 11.63 68.634 12.33L67.948 13.016C67.374 12.358 66.59 11.658 65.596 10.916L66.296 10.23ZM64.588 14.346H67.612V20.59C68.102 20.184 68.62 19.708 69.166 19.176L69.446 20.24C68.592 21.052 67.682 21.78 66.73 22.41L66.338 21.514C66.534 21.332 66.646 21.136 66.646 20.912V15.312H64.588V14.346ZM88.766 14.556C89.97 15.998 90.908 17.258 91.552 18.336L90.712 18.924C90.04 17.748 89.144 16.46 87.996 15.046L88.766 14.556ZM78.966 15.158H80.52V12.134H78.756V11.154H83.292V12.134H81.5V15.158H83.096V16.124H81.5V19.442C82.144 19.246 82.76 19.022 83.376 18.784V19.75C81.962 20.296 80.45 20.744 78.826 21.108L78.574 20.142C79.232 20.03 79.876 19.89 80.52 19.722V16.124H78.966V15.158ZM83.852 10.748H90.964V11.728H88.444C88.262 12.344 88.052 12.932 87.814 13.492V22.886H86.834V15.424C86.022 16.782 85.014 18 83.81 19.078L83.194 18.28C85.238 16.474 86.652 14.29 87.464 11.728H83.852V10.748ZM97.502 15.424H93.526V14.458H103.956C103.886 16.796 103.83 18.196 103.788 18.644C103.732 19.4 103.592 19.904 103.34 20.17C103.088 20.45 102.64 20.59 101.996 20.59C101.59 20.59 101.1 20.59 100.512 20.618L100.232 19.61C100.736 19.61 101.24 19.624 101.758 19.624C102.122 19.624 102.388 19.526 102.556 19.344C102.724 19.148 102.836 18.686 102.878 17.958C102.892 17.58 102.92 16.74 102.948 15.424H98.524V22.858H97.502V15.424ZM92.91 11.434H96.144V10.076H97.166V11.434H100.834V10.076H101.856V11.434H105.09V12.414H101.856V13.744H100.834V12.414H97.166V13.73H96.144V12.414H92.91V11.434ZM107.036 10.664H118.936V11.644H111.572L110.998 13.954H117.046L115.926 21.43H119.3V22.41H106.784V21.43H114.932L115.324 18.966H108.744L110.55 11.644H107.036V10.664ZM115.478 18.028L115.968 14.892H110.76L109.976 18.028H115.478ZM121.26 11.042H126.328V12.008H121.26V11.042ZM120.714 14.822H126.86V15.802H123.626C123.066 17.93 122.576 19.442 122.156 20.366C123.262 20.142 124.368 19.82 125.46 19.414C125.096 18.658 124.746 17.972 124.382 17.328L125.236 16.908C125.908 18.112 126.538 19.414 127.126 20.828L126.258 21.276C126.118 20.912 125.978 20.576 125.838 20.254C124.354 20.786 122.73 21.206 120.994 21.514L120.756 20.562C120.854 20.534 120.924 20.506 120.98 20.478C121.4 19.904 121.96 18.35 122.66 15.802H120.714V14.822ZM128.96 10.104H129.968V12.932H133.02C133.02 17.314 132.95 20.016 132.824 21.052C132.684 22.186 131.998 22.76 130.794 22.76C130.556 22.76 130.15 22.746 129.562 22.718L129.338 21.822C129.842 21.85 130.29 21.878 130.668 21.878C131.354 21.878 131.76 21.472 131.872 20.674C131.956 19.932 132.012 17.678 132.04 13.912H129.968V14.08C129.926 18.112 128.946 21.066 127.042 22.928L126.286 22.27C128.036 20.576 128.932 17.846 128.96 14.08V13.912H127.07V12.932H128.96V10.104Z"
        fill="black"
        fillOpacity="0.9"
      />
      <defs>
        <linearGradient
          id="paint0_linear_7142_54218"
          x1="-1.08778e-06"
          y1="32.5"
          x2="134.993"
          y2="-25.2361"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4F6FF" />
          <stop offset="1" stopColor="#FAF5FC" />
        </linearGradient>
      </defs>
    </svg>
  );
}

function StartAnswerBtn() {
  return (
    <svg
      width="146"
      height="32"
      viewBox="0 0 146 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="146"
        height="32"
        rx="4"
        fill="url(#paint0_linear_397_12131)"
      />
      <path
        d="M29.5 16C29.5 12.134 26.366 9 22.5 9C18.634 9 15.5 12.134 15.5 16C15.5 19.866 18.634 23 22.5 23C26.366 23 29.5 19.866 29.5 16ZM25.4844 16.2279L21.0104 18.811C20.8349 18.9123 20.6156 18.7857 20.6156 18.5831L20.6156 13.4169C20.6156 13.2143 20.8349 13.0877 21.0104 13.189L25.4844 15.7721C25.6599 15.8734 25.6599 16.1266 25.4844 16.2279Z"
        fill="black"
        fillOpacity="0.9"
      />
      <path
        d="M36.568 10.402H47.474V11.382H45.136V15.092H48.202V16.058H45.136V22.26H44.114V16.058H40.068C39.942 17.5 39.676 18.69 39.256 19.642C38.724 20.818 37.842 21.728 36.61 22.372L36.064 21.504C37.198 20.86 37.996 20.006 38.444 18.942C38.738 18.13 38.934 17.178 39.046 16.058H35.812V15.092H39.102C39.102 14.91 39.116 14.742 39.116 14.602V11.382H36.568V10.402ZM40.152 11.382V14.602C40.152 14.77 40.138 14.938 40.138 15.092H44.114V11.382H40.152ZM51.296 9.506L52.29 9.59C52.164 10.542 52.038 11.424 51.912 12.236H53.984V12.824C53.872 15.092 53.48 17.038 52.808 18.69C53.466 19.376 54.012 19.992 54.432 20.552L53.76 21.336C53.368 20.818 52.906 20.258 52.36 19.67C51.8 20.734 51.114 21.658 50.274 22.442L49.644 21.644C50.47 20.888 51.142 19.992 51.674 18.928C51.114 18.354 50.512 17.766 49.868 17.15C50.204 15.932 50.498 14.616 50.75 13.202H49.63V12.236H50.918C51.058 11.354 51.184 10.444 51.296 9.506ZM52.094 17.962C52.626 16.562 52.934 14.98 53.032 13.202H51.758C51.492 14.672 51.226 15.89 50.946 16.856C51.352 17.234 51.73 17.598 52.094 17.962ZM61.376 16.45V22.414H60.41V21.574H56.126V22.414H55.16V16.45H61.376ZM56.126 20.622H60.41V17.374H56.126V20.622ZM54.614 15.19L54.46 14.28C55.272 13.86 56.28 12.292 57.484 9.548L58.436 9.87C57.596 11.69 56.77 13.132 55.93 14.21C57.498 14.112 58.996 13.944 60.41 13.72C60.018 12.95 59.626 12.236 59.22 11.592L60.088 11.172C60.858 12.404 61.586 13.748 62.258 15.218L61.32 15.666C61.152 15.288 60.998 14.924 60.83 14.574C58.968 14.84 56.896 15.05 54.614 15.19ZM68.306 11.102H72.058C71.848 10.542 71.61 10.024 71.33 9.548L72.38 9.38C72.604 9.884 72.842 10.458 73.066 11.102H76.44V12.012H72.366C71.484 13.342 70.714 14.294 70.056 14.868C71.064 14.826 72.072 14.77 73.08 14.7C73.444 14.21 73.78 13.678 74.088 13.118L74.942 13.566C73.584 16.114 71.596 17.85 68.992 18.788L68.46 17.948C70 17.43 71.288 16.632 72.324 15.568C71.232 15.638 70.056 15.694 68.81 15.736L68.586 14.868C68.67 14.84 68.726 14.812 68.782 14.784C69.426 14.434 70.252 13.51 71.26 12.012H68.306V11.102ZM73.206 19.586C71.834 20.79 70.238 21.714 68.418 22.33L67.872 21.462C71.246 20.384 73.78 18.298 75.46 15.19L76.314 15.652C75.614 16.912 74.788 18.032 73.85 18.984C74.858 19.852 75.726 20.72 76.454 21.602L75.726 22.33C75.096 21.462 74.256 20.538 73.206 19.586ZM65.296 9.73C66.22 10.43 66.99 11.13 67.634 11.83L66.948 12.516C66.374 11.858 65.59 11.158 64.596 10.416L65.296 9.73ZM63.588 13.846H66.612V20.09C67.102 19.684 67.62 19.208 68.166 18.676L68.446 19.74C67.592 20.552 66.682 21.28 65.73 21.91L65.338 21.014C65.534 20.832 65.646 20.636 65.646 20.412V14.812H63.588V13.846ZM87.766 14.056C88.97 15.498 89.908 16.758 90.552 17.836L89.712 18.424C89.04 17.248 88.144 15.96 86.996 14.546L87.766 14.056ZM77.966 14.658H79.52V11.634H77.756V10.654H82.292V11.634H80.5V14.658H82.096V15.624H80.5V18.942C81.144 18.746 81.76 18.522 82.376 18.284V19.25C80.962 19.796 79.45 20.244 77.826 20.608L77.574 19.642C78.232 19.53 78.876 19.39 79.52 19.222V15.624H77.966V14.658ZM82.852 10.248H89.964V11.228H87.444C87.262 11.844 87.052 12.432 86.814 12.992V22.386H85.834V14.924C85.022 16.282 84.014 17.5 82.81 18.578L82.194 17.78C84.238 15.974 85.652 13.79 86.464 11.228H82.852V10.248ZM96.502 14.924H92.526V13.958H102.956C102.886 16.296 102.83 17.696 102.788 18.144C102.732 18.9 102.592 19.404 102.34 19.67C102.088 19.95 101.64 20.09 100.996 20.09C100.59 20.09 100.1 20.09 99.512 20.118L99.232 19.11C99.736 19.11 100.24 19.124 100.758 19.124C101.122 19.124 101.388 19.026 101.556 18.844C101.724 18.648 101.836 18.186 101.878 17.458C101.892 17.08 101.92 16.24 101.948 14.924H97.524V22.358H96.502V14.924ZM91.91 10.934H95.144V9.576H96.166V10.934H99.834V9.576H100.856V10.934H104.09V11.914H100.856V13.244H99.834V11.914H96.166V13.23H95.144V11.914H91.91V10.934ZM106.036 10.164H117.936V11.144H110.572L109.998 13.454H116.046L114.926 20.93H118.3V21.91H105.784V20.93H113.932L114.324 18.466H107.744L109.55 11.144H106.036V10.164ZM114.478 17.528L114.968 14.392H109.76L108.976 17.528H114.478ZM120.26 10.542H125.328V11.508H120.26V10.542ZM119.714 14.322H125.86V15.302H122.626C122.066 17.43 121.576 18.942 121.156 19.866C122.262 19.642 123.368 19.32 124.46 18.914C124.096 18.158 123.746 17.472 123.382 16.828L124.236 16.408C124.908 17.612 125.538 18.914 126.126 20.328L125.258 20.776C125.118 20.412 124.978 20.076 124.838 19.754C123.354 20.286 121.73 20.706 119.994 21.014L119.756 20.062C119.854 20.034 119.924 20.006 119.98 19.978C120.4 19.404 120.96 17.85 121.66 15.302H119.714V14.322ZM127.96 9.604H128.968V12.432H132.02C132.02 16.814 131.95 19.516 131.824 20.552C131.684 21.686 130.998 22.26 129.794 22.26C129.556 22.26 129.15 22.246 128.562 22.218L128.338 21.322C128.842 21.35 129.29 21.378 129.668 21.378C130.354 21.378 130.76 20.972 130.872 20.174C130.956 19.432 131.012 17.178 131.04 13.412H128.968V13.58C128.926 17.612 127.946 20.566 126.042 22.428L125.286 21.77C127.036 20.076 127.932 17.346 127.96 13.58V13.412H126.07V12.432H127.96V9.604Z"
        fill="black"
        fillOpacity="0.9"
      />
      <defs>
        <linearGradient
          id="paint0_linear_397_12131"
          x1="-1.08778e-06"
          y1="32"
          x2="134.993"
          y2="-25.7361"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F4F6FF" />
          <stop offset="1" stopColor="#FAF5FC" />
        </linearGradient>
      </defs>
    </svg>
  );
}

function Answer1(props: { i: AnswerList[0] & { type: '已回答' } }) {
  return (
    <div style={{ padding: '9px 0' }}>
      <div
        css={css`
          border-radius: 4px;
          padding: 14px;
          color: rgba(0, 0, 0, 0.26);
          //box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.16);
          background: rgba(246, 247, 249, 1);
          position: relative;
          text-indent: 50px;
        `}
      >
        <svg
          style={{ position: 'absolute', left: 6, top: 6 }}
          width="56"
          height="24"
          viewBox="0 0 56 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.210395 22.8113C0.100719 23.4242 0.571797 23.9872 1.19444 23.9874L40.8033 24C47.9137 24 54.6297 18.6256 55.8192 11.9998C57.0086 5.37441 52.2049 0 45.0945 0H6.26947C5.17858 0 4.14685 0.823351 3.96287 1.84106L0.210395 22.8113Z"
            fill="url(#paint0_linear_7142_53517)"
          />
          <path
            d="M18.69 11.904H9.898V16.034C9.898 16.65 10.22 16.972 10.864 16.972H17.892C18.228 16.972 18.508 16.888 18.718 16.72C18.998 16.468 19.18 15.572 19.264 14.032L20.244 14.34C20.104 16.076 19.866 17.112 19.53 17.476C19.222 17.77 18.76 17.924 18.172 17.952H10.584C9.45 17.952 8.89 17.364 8.89 16.216V8.866H9.898V10.938H17.668V7.284H8.33V6.304H18.69V11.904ZM25.312 9.468H30.716V14.452H25.312V9.468ZM29.764 13.598V10.336H26.25V13.598H29.764ZM33.614 6.402V18.176H32.634V17.574H23.366V18.176H22.386V6.402H33.614ZM23.366 16.678H32.634V7.298H23.366V16.678ZM37.758 7.662C37.408 8.32 37.016 8.922 36.568 9.468L35.714 8.922C36.596 7.858 37.24 6.696 37.632 5.422L38.598 5.632C38.472 6.01 38.332 6.374 38.192 6.738H41.958V7.662H39.984C40.25 8.11 40.474 8.53 40.642 8.922L39.732 9.258C39.508 8.698 39.242 8.166 38.962 7.662H37.758ZM43.54 7.662C43.288 8.096 43.008 8.502 42.714 8.894L41.874 8.376C42.602 7.494 43.12 6.514 43.442 5.436L44.408 5.646C44.282 6.038 44.142 6.402 44.002 6.738H48.202V7.662H45.934C46.27 8.194 46.55 8.698 46.76 9.146L45.85 9.468C45.57 8.838 45.248 8.236 44.884 7.662H43.54ZM46.186 13.962V18.386H45.22V17.756H38.78V18.386H37.814V13.962H46.186ZM38.78 16.832H45.22V14.886H38.78V16.832ZM38.78 12.03C37.968 12.464 37.086 12.87 36.148 13.248L35.672 12.366C38.066 11.47 40.054 10.35 41.65 9.02H42.322C44.086 10.448 46.116 11.568 48.412 12.394L47.922 13.262C46.97 12.898 46.074 12.478 45.248 12.03V12.66H38.78V12.03ZM44.702 11.722C43.708 11.148 42.798 10.504 41.986 9.804C41.216 10.49 40.334 11.134 39.326 11.722H44.702Z"
            fill="url(#paint1_linear_7142_53517)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_7142_53517"
              x1="56"
              y1="15"
              x2="0"
              y2="15"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#EBF8F0" />
              <stop offset="1" stopColor="#DEF8E8" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_7142_53517"
              x1="7"
              y1="1"
              x2="49"
              y2="1"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#00C653" />
              <stop offset="1" stopColor="#01D766" />
            </linearGradient>
          </defs>
        </svg>
        {props.i.text}
      </div>
    </div>
  );
}

function Answer2(props: { i: AnswerList[0] & { type: '回答中' } }) {
  return (
    <div style={{ padding: '9px 0' }}>
      <div
        css={css`
          background: linear-gradient(
            89.21deg,
            #0153ff -0.01%,
            #8649ff 147.74%
          );
          border-radius: 4px;
          color: #ffffff;
          padding: 14px;

          font-family: PingFang SC;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          text-align: left;
          position: relative;
          text-indent: 50px;

          .answer-tooltip {
            position: absolute;
            right: 8px;
            top: 6px;
            visibility: hidden;

            > svg {
              cursor: pointer;
            }
          }

          :hover {
            .answer-tooltip {
              visibility: visible;
            }

            color: rgba(255, 255, 255, 0.8);
            background: linear-gradient(
                88.08deg,
                #0153ff -0.01%,
                #2e7ffd 49.89%,
                #c1a3fd 99.99%
              ),
              linear-gradient(0deg, rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.24));

            box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.16);
          }
        `}
      >
        <svg
          style={{ position: 'absolute', left: 5, top: 6 }}
          width="56"
          height="24"
          viewBox="0 0 56 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.210395 22.8113C0.100719 23.4242 0.571797 23.9872 1.19444 23.9874L40.8033 24C47.9137 24 54.6297 18.6256 55.8192 11.9998C57.0086 5.37441 52.2049 0 45.0945 0H6.26947C5.17858 0 4.14685 0.823351 3.96287 1.84106L0.210395 22.8113Z"
            fill="#D4E3FC"
          />
          <path
            d="M11.312 9.468H16.716V14.452H11.312V9.468ZM15.764 13.598V10.336H12.25V13.598H15.764ZM19.614 6.402V18.176H18.634V17.574H9.366V18.176H8.386V6.402H19.614ZM9.366 16.678H18.634V7.298H9.366V16.678ZM23.758 7.662C23.408 8.32 23.016 8.922 22.568 9.468L21.714 8.922C22.596 7.858 23.24 6.696 23.632 5.422L24.598 5.632C24.472 6.01 24.332 6.374 24.192 6.738H27.958V7.662H25.984C26.25 8.11 26.474 8.53 26.642 8.922L25.732 9.258C25.508 8.698 25.242 8.166 24.962 7.662H23.758ZM29.54 7.662C29.288 8.096 29.008 8.502 28.714 8.894L27.874 8.376C28.602 7.494 29.12 6.514 29.442 5.436L30.408 5.646C30.282 6.038 30.142 6.402 30.002 6.738H34.202V7.662H31.934C32.27 8.194 32.55 8.698 32.76 9.146L31.85 9.468C31.57 8.838 31.248 8.236 30.884 7.662H29.54ZM32.186 13.962V18.386H31.22V17.756H24.78V18.386H23.814V13.962H32.186ZM24.78 16.832H31.22V14.886H24.78V16.832ZM24.78 12.03C23.968 12.464 23.086 12.87 22.148 13.248L21.672 12.366C24.066 11.47 26.054 10.35 27.65 9.02H28.322C30.086 10.448 32.116 11.568 34.412 12.394L33.922 13.262C32.97 12.898 32.074 12.478 31.248 12.03V12.66H24.78V12.03ZM30.702 11.722C29.708 11.148 28.798 10.504 27.986 9.804C27.216 10.49 26.334 11.134 25.326 11.722H30.702ZM41.482 5.506H42.518V8.026H47.544V14.606H46.536V13.724H42.518V18.498H41.482V13.724H37.478V14.606H36.47V8.026H41.482V5.506ZM37.478 12.744H41.482V9.006H37.478V12.744ZM42.518 12.744H46.536V9.006H42.518V12.744Z"
            fill="#0047F9"
          />
        </svg>
        <div className="answer-tooltip">
          <svg
            width="30"
            height="33"
            viewBox="0 0 30 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ cursor: 'pointer' }}
            onClick={props.i.onPause}
          >
            <g filter="url(#filter0_d_7142_53548)">
              <rect x="5" width="20" height="20" rx="10" fill="white" />
              <path
                d="M11.6666 5.01118C12.6533 4.35189 13.8133 4 15 4C16.5913 4 18.1174 4.63214 19.2426 5.75736C20.3679 6.88258 21 8.4087 21 10C21 11.1867 20.6481 12.3467 19.9888 13.3334C19.3295 14.3201 18.3925 15.0891 17.2961 15.5433C16.1997 15.9974 14.9933 16.1162 13.8295 15.8847C12.6656 15.6532 11.5965 15.0818 10.7574 14.2426C9.91825 13.4035 9.3468 12.3344 9.11529 11.1705C8.88378 10.0067 9.0026 8.80026 9.45673 7.7039C9.91085 6.60754 10.6799 5.67047 11.6666 5.01118ZM12.8571 7.8571V12.1429H17.1429V7.8571H12.8571Z"
                fill="#316FF8"
              />
            </g>
            <defs>
              <filter
                id="filter0_d_7142_53548"
                x="0"
                y="0"
                width="30"
                height="33"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="5"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect1_dropShadow_7142_53548"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="5" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_7142_53548"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect1_dropShadow_7142_53548"
                  result="shape"
                />
              </filter>
            </defs>
          </svg>
        </div>
        {props.i.text}
      </div>
    </div>
  );
}
const quickReplyItemStyle = css`
  background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
  padding: 9.5px 14px 9.5px 7px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 11px;
  cursor: pointer;
  width: fit-content;
`;
export function QuickReply(props: { onSelect: (text: string) => void }) {
  const { data, pagination } = usePagination(
    async ({ current, pageSize }) => {
      const result = await GetDefaultLiveAnswerList({
        group_id: '0',
        group_type: 3,
        page_num: current,
        page_size: pageSize,
      });
      return {
        total: result.query_count,
        list: result.records,
      };
    },
    { defaultPageSize: 10 }
  );

  const randID = useMemo(() => Math.random().toString(36).substring(2), []);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: 10,
          padding: 10,
          height: 42,
          borderBottom: '1px solid rgba(235, 231, 238, 1)',
        }}
      >
        <svg
          width="15"
          height="15"
          viewBox="0 0 15 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
        >
          <rect
            width="15"
            height="15"
            fill={`url(#pattern0_7065_35998${randID})`}
          />
          <defs>
            <pattern
              id={`pattern0_7065_35998${randID}`}
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref={`#image0_7065_35998${randID}`}
                transform="scale(0.005)"
              />
            </pattern>
            <image
              id={`image0_7065_35998${randID}`}
              width="200"
              height="200"
              xlinkHref="data:image/png;base64,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"
            />
          </defs>
        </svg>
        快捷话术库
        <span
          style={{
            color: 'rgba(0, 0, 0, 0.26)',
          }}
        >
          点击快速填入
        </span>
        <div
          style={{
            color: 'rgba(0, 71, 249, 1)',
            textAlign: 'center',
            cursor: 'pointer',
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 2,
          }}
          onClick={() => {
            if (pagination.current >= pagination.totalPage) {
              pagination.changeCurrent(1);
              return;
            }
            pagination.changeCurrent(pagination.current + 1);
          }}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.00016 2.71134C10.8965 2.71134 13.2534 5.0736 13.2534 7.99868H14.2148C14.2148 4.54763 11.4324 1.75 8.00016 1.75C5.76382 1.75 3.80336 2.93771 2.7087 4.72001L2.7087 2.64252H1.75V5.74894C1.75 6.02508 1.97386 6.24894 2.25 6.24894L5.33676 6.24894V5.28498L3.49045 5.28498C4.40906 3.74211 6.08692 2.71134 8.00016 2.71134Z"
              fill={`url(#paint0_linear_7142_53505${randID})`}
            />
            <path
              d="M1.78554 7.99863H2.74687C2.74687 10.9237 5.10379 13.286 8.00015 13.286C9.91339 13.286 11.5912 12.2553 12.5099 10.7124H10.6632V9.74843H13.75C14.0261 9.74843 14.25 9.97228 14.25 10.2484V13.3548H13.2913V11.2779C12.1966 13.0599 10.2363 14.2474 8.00015 14.2474C4.56792 14.2474 1.78554 11.4497 1.78554 7.99863Z"
              fill={`url(#paint1_linear_7142_53505${randID})`}
            />
            <defs>
              <linearGradient
                id={`paint0_linear_7142_53505${randID}`}
                x1="1.74875"
                y1="14.2473"
                x2="20.468"
                y2="13.9902"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0153FF" />
                <stop offset="1" stopColor="#8649FF" />
              </linearGradient>
              <linearGradient
                id={`paint1_linear_7142_53505${randID}`}
                x1="1.74875"
                y1="14.2473"
                x2="20.468"
                y2="13.9902"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0153FF" />
                <stop offset="1" stopColor="#8649FF" />
              </linearGradient>
            </defs>
          </svg>
          换一批
        </div>
      </div>
      <div
        style={{
          flex: 1,
          padding: 8,
          display: 'flex',
          flexDirection: 'column',
          width: 296,
          height: '100%',
          overflowY: 'auto',
        }}
      >
        {data?.list.map((item) => (
          <div
            css={quickReplyItemStyle}
            key={item.qa_content_id}
            onClick={() => {
              props.onSelect(item.answer);
            }}
          >
            {item.answer}
          </div>
        ))}
        {/* <div css={quickReplyItemStyle}>欢迎来到直播间的宝子们，你们好啊</div>*/}
        {/* <div css={quickReplyItemStyle}>*/}
        {/*  欢迎来到直播间的宝子们，你们好啊,你们好啊*/}
        {/* </div>*/}
      </div>
    </div>
  );
}

// 未回答
function Answer3(props: { i: AnswerList[0] & { type: '未回答' } }) {
  const [modify, setModify] = useState(!props.i.text);
  const [value, setValue] = useState(props.i.text);
  return (
    <div style={{ padding: '9px 0' }}>
      <div
        css={css`
          background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
          border-radius: 4px;
          padding: 14px;
          color: rgba(0, 0, 0, 0.9);
          position: relative;

          .answer-tooltip {
            position: absolute;
            right: 8px;
            top: 6px;
            visibility: hidden;

            > svg {
              cursor: pointer;
            }
          }

          :hover {
            .answer-tooltip {
              visibility: visible;
            }

            box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.16);
          }
        `}
      >
        <div
          className="answer-tooltip"
          style={{
            position: 'absolute',
            top: 6,
            right: 8,
            display: modify ? 'none' : 'block',
          }}
        >
          {/* 修改*/}
          <svg
            width="30"
            height="33"
            viewBox="0 0 30 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            onClick={() => setModify(true)}
          >
            <g filter="url(#filter0_d_7142_53564)">
              <rect x="5" width="20" height="20" rx="10" fill="white" />
              <path
                d="M17.6154 4.60581L20.3942 7.38453L21 6.77873L18.2212 4L17.6154 4.60581Z"
                fill="url(#paint0_linear_7142_53564)"
              />
              <path
                d="M10.3076 14.9949L13.403 14.3758L19.6552 8.12365L16.8764 5.34492L10.6242 11.5971L10.0051 14.6924C9.96916 14.8723 10.1277 15.0308 10.3076 14.9949ZM16.8764 6.55654L18.4436 8.12365L12.9806 13.5866L11.0217 13.9783L11.4135 12.0194L16.8764 6.55654Z"
                fill="url(#paint1_linear_7142_53564)"
              />
            </g>
            <defs>
              <filter
                id="filter0_d_7142_53564"
                x="0"
                y="0"
                width="30"
                height="33"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="5"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect1_dropShadow_7142_53564"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="5" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_7142_53564"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect1_dropShadow_7142_53564"
                  result="shape"
                />
              </filter>
              <linearGradient
                id="paint0_linear_7142_53564"
                x1="10"
                y1="8.2625"
                x2="21"
                y2="8.2625"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0052D9" />
                <stop offset="1" stopColor="#3973FD" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_7142_53564"
                x1="10"
                y1="8.2625"
                x2="21"
                y2="8.2625"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0052D9" />
                <stop offset="1" stopColor="#3973FD" />
              </linearGradient>
            </defs>
          </svg>
          {/* 删除*/}
          <svg
            width="30"
            height="33"
            viewBox="0 0 30 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ cursor: 'pointer' }}
            onClick={props.i.onDelete}
          >
            <g filter="url(#filter0_d_7142_53558)">
              <rect x="5" width="20" height="20" rx="10" fill="white" />
              <path
                d="M13.5 13V8.5H14.25V13H13.5Z"
                fill="url(#paint0_linear_7142_53558)"
              />
              <path
                d="M15.75 8.5V13H16.5V8.5H15.75Z"
                fill="url(#paint1_linear_7142_53558)"
              />
              <path
                d="M16.875 6.25H19.5V7H18.75V14.5C18.75 14.9142 18.4142 15.25 18 15.25H12C11.5858 15.25 11.25 14.9142 11.25 14.5V7H10.5V6.25H13.125L13.125 5.35C13.125 5.01863 13.3936 4.75 13.725 4.75H16.275C16.6064 4.75 16.875 5.01863 16.875 5.35V6.25ZM13.875 6.25H16.125L16.125 5.5L13.875 5.5V6.25ZM12 7V14.5H18V7H12Z"
                fill="url(#paint2_linear_7142_53558)"
              />
            </g>
            <defs>
              <filter
                id="filter0_d_7142_53558"
                x="0"
                y="0"
                width="30"
                height="33"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="5"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect1_dropShadow_7142_53558"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="5" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_7142_53558"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect1_dropShadow_7142_53558"
                  result="shape"
                />
              </filter>
              <linearGradient
                id="paint0_linear_7142_53558"
                x1="10.5"
                y1="8.81875"
                x2="19.5"
                y2="8.81875"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0052D9" />
                <stop offset="1" stopColor="#3973FD" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_7142_53558"
                x1="10.5"
                y1="8.81875"
                x2="19.5"
                y2="8.81875"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0052D9" />
                <stop offset="1" stopColor="#3973FD" />
              </linearGradient>
              <linearGradient
                id="paint2_linear_7142_53558"
                x1="10.5"
                y1="8.81875"
                x2="19.5"
                y2="8.81875"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0052D9" />
                <stop offset="1" stopColor="#3973FD" />
              </linearGradient>
            </defs>
          </svg>
          {/* 暂停*/}
          <svg
            width="30"
            height="33"
            viewBox="0 0 30 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ cursor: 'pointer' }}
            onClick={props.i.onPause}
          >
            <g filter="url(#filter0_d_7142_53548)">
              <rect x="5" width="20" height="20" rx="10" fill="white" />
              <path
                d="M11.6666 5.01118C12.6533 4.35189 13.8133 4 15 4C16.5913 4 18.1174 4.63214 19.2426 5.75736C20.3679 6.88258 21 8.4087 21 10C21 11.1867 20.6481 12.3467 19.9888 13.3334C19.3295 14.3201 18.3925 15.0891 17.2961 15.5433C16.1997 15.9974 14.9933 16.1162 13.8295 15.8847C12.6656 15.6532 11.5965 15.0818 10.7574 14.2426C9.91825 13.4035 9.3468 12.3344 9.11529 11.1705C8.88378 10.0067 9.0026 8.80026 9.45673 7.7039C9.91085 6.60754 10.6799 5.67047 11.6666 5.01118ZM12.8571 7.8571V12.1429H17.1429V7.8571H12.8571Z"
                fill="#316FF8"
              />
            </g>
            <defs>
              <filter
                id="filter0_d_7142_53548"
                x="0"
                y="0"
                width="30"
                height="33"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="5"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect1_dropShadow_7142_53548"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="5" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_7142_53548"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect1_dropShadow_7142_53548"
                  result="shape"
                />
              </filter>
            </defs>
          </svg>
        </div>
        {modify ? (
          <div style={{ position: 'relative' }}>
            <Tooltip
              content={
                <div style={{ height: '480px' }}>
                  <QuickReply
                    onSelect={(text) => {
                      setValue((prev) => prev + text);
                    }}
                  />
                </div>
              }
              theme="light"
              trigger="click"
            >
              <svg
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                xmlnsXlink="http://www.w3.org/1999/xlink"
                style={{
                  position: 'absolute',
                  left: 10,
                  top: 13,
                  zIndex: 2,
                  cursor: 'pointer',
                }}
              >
                <rect width="15" height="15" fill="url(#pattern0_7065_35998)" />
                <defs>
                  <pattern
                    id="pattern0_7065_35998"
                    patternContentUnits="objectBoundingBox"
                    width="1"
                    height="1"
                  >
                    <use
                      xlinkHref="#image0_7065_35998"
                      transform="scale(0.005)"
                    />
                  </pattern>
                  <image
                    id="image0_7065_35998"
                    width="200"
                    height="200"
                    xlinkHref="data:image/png;base64,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"
                  />
                </defs>
              </svg>
            </Tooltip>
            <div
              style={{
                position: 'absolute',
                left: 33,
                top: 14.5,
                borderRight: '1px solid rgba(230, 232, 240, 1)',
                height: 10,
                zIndex: 2,
              }}
            />
            <div />
            <Input
              value={value}
              onChange={setValue}
              onEnter={() => {
                props.i.onChange(value);
                setModify(false);
              }}
              style={{ height: 40 }}
              css={css`
                input {
                  padding-left: 34px;
                }

                .t-input {
                  height: 100%;
                }
              `}
            />
            <div
              style={{ position: 'absolute', right: -4, bottom: -4, zIndex: 2 }}
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  cursor: 'pointer',
                  marginRight: 4,
                  boxShadow: '0px 0px 12px 0px rgba(0, 0, 0, 0.06)',
                }}
                onClick={() => {
                  props.i.onChange(value);
                  setModify(false);
                }}
              >
                <rect
                  width="20"
                  height="20"
                  rx="4"
                  fill="url(#paint0_linear_4081_19568)"
                />
                <path
                  d="M8.43397 12.9219L14.6566 6.69922L15.5759 7.61846L8.43397 14.7604L3.97461 10.301L4.89385 9.38176L8.43397 12.9219Z"
                  fill="white"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_4081_19568"
                    x1="-0.00200387"
                    y1="20"
                    x2="20.6437"
                    y2="19.3096"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#0153FF" />
                    <stop offset="0.498979" stopColor="#2E7FFD" />
                    <stop offset="1" stopColor="#C1A3FD" />
                  </linearGradient>
                </defs>
              </svg>
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  cursor: 'pointer',
                  boxShadow: '0px 0px 12px 0px rgba(0, 0, 0, 0.06)',
                }}
                onClick={() => setModify(false)}
              >
                <rect
                  x="0.5"
                  y="0.5"
                  width="19"
                  height="19"
                  rx="3.5"
                  fill="white"
                  stroke="white"
                />
                <rect
                  width="16"
                  height="16"
                  transform="translate(2 2)"
                  fill="white"
                />
                <path
                  d="M9.99907 10.9183L13.075 13.9942L13.9942 13.075L10.9183 9.99907L13.9942 6.92317L13.075 6.00393L9.99907 9.07983L6.92314 6.00391L6.00391 6.92314L9.07983 9.99907L6.00391 13.075L6.92314 13.9942L9.99907 10.9183Z"
                  fill="black"
                  fillOpacity="0.9"
                />
              </svg>
            </div>
          </div>
        ) : (
          props.i.text || '[请点击右侧开始编辑]'
        )}
      </div>
    </div>
  );
}

function StopInterActiveLine(props: { i: AnswerList[0] & { type: '暂停' } }) {
  return (
    <div style={{ padding: '9px 0' }}>
      <div
        style={{
          display: 'flex',
          height: 22,
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 100%)',
          borderRadius: 2,
          color: 'rgba(0, 0, 0, 0.4)',
          position: 'relative',
        }}
        css={css`
          .answer-tooltip {
            visibility: hidden;
          }
          :hover {
            .answer-tooltip {
              visibility: visible;
            }
          }
        `}
      >
        <div
          style={{
            height: 1,
            backgroundColor: 'rgba(230, 232, 240, 1)',
            width: '30%',
          }}
        />
        到此停止互动
        <div
          style={{
            height: 1,
            backgroundColor: 'rgba(230, 232, 240, 1)',
            width: '30%',
          }}
        />
        <div
          style={{ position: 'absolute', right: 0, top: 0 }}
          className="answer-tooltip"
        >
          <Tooltip
            content={
              <div style={{ color: 'rgba(0,0,0,0.9)' }}>上下拽拖更改停止点</div>
            }
            theme="light"
            placement="top-right"
          >
            <svg
              width="30"
              height="33"
              viewBox="0 0 30 33"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g filter="url(#filter0_d_7192_56716)">
                <rect x="5" width="20" height="20" rx="10" fill="white" />
                <path
                  d="M15.3769 10.3745H20.252V9.62451H15.377L15.377 6.13207L16.6456 7.40068L17.1759 6.87035L15.002 4.69641L12.828 6.87035L13.3583 7.40068L14.627 6.13207L14.627 9.62451H9.75195V10.3745H14.6269L14.627 13.8651L13.3583 12.5965L12.828 13.1268L15.002 15.3008L17.1759 13.1268L16.6456 12.5965L15.377 13.8651L15.3769 10.3745Z"
                  fill="url(#paint0_linear_7192_56716)"
                />
              </g>
              <defs>
                <filter
                  id="filter0_d_7192_56716"
                  x="0"
                  y="0"
                  width="30"
                  height="33"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feMorphology
                    radius="5"
                    operator="erode"
                    in="SourceAlpha"
                    result="effect1_dropShadow_7192_56716"
                  />
                  <feOffset dy="8" />
                  <feGaussianBlur stdDeviation="5" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_7192_56716"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_7192_56716"
                    result="shape"
                  />
                </filter>
                <linearGradient
                  id="paint0_linear_7192_56716"
                  x1="13.8207"
                  y1="15.3008"
                  x2="13.8207"
                  y2="4.69641"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#0052D9" />
                  <stop offset="1" stopColor="#3973FD" />
                </linearGradient>
              </defs>
            </svg>
          </Tooltip>
          <Tooltip
            content={
              <div style={{ color: 'rgba(0,0,0,0.9)' }}>
                删除该线即视为一直互动
              </div>
            }
            theme="light"
            placement="top-right"
          >
            <svg
              width="30"
              height="33"
              viewBox="0 0 30 33"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{ cursor: 'pointer' }}
              onClick={props.i.onDelete}
            >
              <g filter="url(#filter0_d_7192_56712)">
                <rect x="5" width="20" height="20" rx="10" fill="white" />
                <path
                  d="M13.5 13V8.5H14.25V13H13.5Z"
                  fill="url(#paint0_linear_7192_56712)"
                />
                <path
                  d="M15.75 8.5V13H16.5V8.5H15.75Z"
                  fill="url(#paint1_linear_7192_56712)"
                />
                <path
                  d="M16.875 6.25H19.5V7H18.75V14.5C18.75 14.9142 18.4142 15.25 18 15.25H12C11.5858 15.25 11.25 14.9142 11.25 14.5V7H10.5V6.25H13.125L13.125 5.35C13.125 5.01863 13.3936 4.75 13.725 4.75H16.275C16.6064 4.75 16.875 5.01863 16.875 5.35V6.25ZM13.875 6.25H16.125L16.125 5.5L13.875 5.5V6.25ZM12 7V14.5H18V7H12Z"
                  fill="url(#paint2_linear_7192_56712)"
                />
              </g>
              <defs>
                <filter
                  id="filter0_d_7192_56712"
                  x="0"
                  y="0"
                  width="30"
                  height="33"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feMorphology
                    radius="5"
                    operator="erode"
                    in="SourceAlpha"
                    result="effect1_dropShadow_7192_56712"
                  />
                  <feOffset dy="8" />
                  <feGaussianBlur stdDeviation="5" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_7192_56712"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_7192_56712"
                    result="shape"
                  />
                </filter>
                <linearGradient
                  id="paint0_linear_7192_56712"
                  x1="10.5"
                  y1="8.81875"
                  x2="19.5"
                  y2="8.81875"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#0052D9" />
                  <stop offset="1" stopColor="#3973FD" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear_7192_56712"
                  x1="10.5"
                  y1="8.81875"
                  x2="19.5"
                  y2="8.81875"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#0052D9" />
                  <stop offset="1" stopColor="#3973FD" />
                </linearGradient>
                <linearGradient
                  id="paint2_linear_7192_56712"
                  x1="10.5"
                  y1="8.81875"
                  x2="19.5"
                  y2="8.81875"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#0052D9" />
                  <stop offset="1" stopColor="#3973FD" />
                </linearGradient>
              </defs>
            </svg>
          </Tooltip>
        </div>
      </div>
    </div>
  );
}

function AddAnswer(props: {
  onClick?: React.HTMLAttributes<SVGSVGElement>['onClick'];
}) {
  // const click = () => {
  //   if (props.popup) return setVisible(true);
  //   props.i.onAdd();
  // };
  // const [visible, setVisible] = useState(false);
  // const [text, setText] = useState('');
  // const confirm = async () => {
  //   await props.i.onAdd(text);
  //   setVisible(false);
  //   // confirm
  // };
  return (
    <>
      <div
        style={{ padding: '2px 0', borderRadius: 2 }}
        css={css`
          .add-answer {
            visibility: hidden;
          }

          :hover {
            .add-answer {
              visibility: visible;
            }
          }
        `}
      >
        <div
          className="add-answer"
          css={css`
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 20px;
            position: relative;
          `}
        >
          <div
            css={css`
              position: absolute;
              width: 100%;
              height: 2px;
              background: linear-gradient(
                88.08deg,
                #c2d6ff -0.01%,
                #cde0ff 49.89%,
                #f0e9ff 99.99%
              );
            `}
          />
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            css={css`
              cursor: pointer;
              position: relative;
            `}
            onClick={props.onClick}
          >
            <circle
              cx="12"
              cy="12"
              r="12"
              fill="url(#paint0_linear_7065_35995)"
            />
            <path
              d="M11.842 7.802H13.066V11.726H16.99V12.95H13.066V16.892H11.842V12.95H7.9V11.726H11.842V7.802Z"
              fill="#0F53FF"
            />
            <defs>
              <linearGradient
                id="paint0_linear_7065_35995"
                x1="-0.00240465"
                y1="24"
                x2="24.7725"
                y2="23.1715"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#C2D6FF" />
                <stop offset="0.498979" stopColor="#CDE0FF" />
                <stop offset="1" stopColor="#F0E9FF" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>
    </>
  );
}
