// 实时问答抽屉，嵌入QALib 下的RealTimeQATable 组件

import {
  IRealTimeQATableProps,
  RealTimeQATable,
} from '@/pages/QALib/components/RealTimeQATable/RealTimeQATable';
import { css } from '@emotion/react';
import { ChevronRightIcon, CloseIcon } from 'tdesign-icons-react';
import { Drawer, Switch } from 'tdesign-react';
import { RealTimeAnswer } from '@/pages/List/components/realtime_qa_drawer/RealTimeAnswer';
import React from 'react';
import { TranslationAtom } from '@/pages/List/components/realtime_qa_drawer/store';
import { useRecoilState } from 'recoil';

export interface IRealTimeQADrawerProps
  extends Pick<IRealTimeQATableProps, 'liveID'> {
  show: boolean;
  onClose: () => void;
  // 直播间名称
  liveName: string;
}

export function RealTimeQADrawer(props: IRealTimeQADrawerProps) {
  const { show, onClose, ...rest } = props;
  const [multilingual, setMultilingual] = useRecoilState(TranslationAtom);
  const handleClose = () => {
    if (show) {
      onClose();
    }
  };
  const gotoQALib = () => {
    // 跳转问答库
    window.open(`#/qa-lib`);
  };
  const header = (
    <div
      css={css`
        display: flex;
        width: 100%;
        justify-content: space-between;
      `}
    >
      <div>{rest.liveName}直播间-问答干预</div>
      <div
        css={css`
          display: flex;
          justify-content: space-between;
          gap: 24px;
        `}
      >
        <div
          css={css`
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.9);
            display: flex;
            gap: 4px;
            align-items: center;
          `}
        >
          <Switch
            size="small"
            value={multilingual}
            onChange={setMultilingual}
          />
          多语言翻译
        </div>
        <span
          css={css`
            color: rgba(0, 71, 249, 1);
            font-weight: 400;
            cursor: pointer;
          `}
          onClick={gotoQALib}
        >
          前往问答库管理
          <ChevronRightIcon
            css={css`
              height: 24px;
              line-height: 24px;
              vertical-align: bottom;
            `}
          />
        </span>
        <CloseIcon
          css={css`
            height: 24px;
            cursor: pointer;
          `}
          onClick={handleClose}
        />
      </div>
    </div>
  );

  return (
    <Drawer
      size="88%"
      visible={show}
      destroyOnClose
      onClose={handleClose}
      footer={null}
      header={header}
      css={css`
        .t-drawer__body {
          display: flex;
          padding: 0;
          overflow: hidden;
          overflow: unset;
          height: 0;
        }
      `}
    >
      <div style={{ position: 'relative', flex: 1, width: 0 }}>
        <RealTimeQATable
          {...rest}
          filter={{
            questionType: 'ALL',
          }}
        />
      </div>
      <div
        css={css`
          width: 500px;
          min-width: 500px;
          border-left: 1px solid rgba(232, 237, 245, 1);
          height: 100%;
        `}
      >
        <RealTimeAnswer liveID={props.liveID} />
      </div>
    </Drawer>
  );
}
