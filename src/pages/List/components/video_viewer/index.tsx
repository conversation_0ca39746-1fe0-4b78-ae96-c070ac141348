import { Loading } from '@/components/Loading';
import { useRequest } from 'ahooks';
import { ContentSvr, MetaFeedbackSvr } from '@/pb/pb';
import { useVideoPreview } from '@/pages/ScriptResult/VideoPreview';
import './index.less';
import { PlayConfig, Script } from '@/type/pagedoo';
import { queryScriptList } from '@/components/ScriptForm/utils';

export function VideoViewer({ id }: { id: string }) {
  const { loading, data, error } = useRequest(
    async () => {
      const detailRes = await ContentSvr.ContentDetailQuery({
        content_id: id,
      });
      let currentPlayConfig: PlayConfig | undefined = undefined;
      try {
        currentPlayConfig = JSON.parse(detailRes.extend.playScript);
      } catch {}
      const research_id = detailRes.extend.scriptId || '';
      if (!research_id) throw new Error('scriptId is empty');
      let currentScript: Script | undefined = undefined;
      if (research_id === 'EMPTY') {
        return {
          contentId: detailRes.content_id,
          scriptId: research_id,
          researchId: '',
          currentScript,
          currentPlayConfig,
        };
      }
      const scriptListRes = await queryScriptList(research_id);
      try {
        currentScript = JSON.parse(scriptListRes.script_list[0].script_info);
      } catch {}
      return {
        contentId: detailRes.content_id,
        scriptId: research_id,
        researchId: scriptListRes.script_list[0].research_id,
        currentScript,
        currentPlayConfig,
      };
    },
    {
      manual: false,
    }
  );

  const { videoViewer } = useVideoPreview(data);

  if (loading) {
    return (
      <div className="video_view_body">
        <Loading />
      </div>
    );
  }
  if (error) {
    return <div className="video_view_body">{error.message}</div>;
  }

  return <div className="video_view_body">{videoViewer}</div>;
}
