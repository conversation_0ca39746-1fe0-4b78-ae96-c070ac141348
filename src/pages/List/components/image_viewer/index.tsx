import React from 'react';
import { ImageViewer, Image } from 'tdesign-react';
import { BrowseIcon } from 'tdesign-icons-react';
import { getThumbnailUrl } from '@/utils/cos';

export function BasicImageViewer({
  img,
  name = '',
  className,
  width,
  height,
}: {
  img: string;
  name?: string;
  className?: string;
  width: number;
  height: number;
}) {
  const trigger = ({ open }: { open: () => void }) => {
    const imgUrl = getThumbnailUrl(img, width * 2, height * 2);
    const mask = (
      <div
        style={{
          background: 'rgba(0,0,0,.6)',
          color: '#fff',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onClick={open}
      >
        <span>
          <BrowseIcon size="16px" /> 预览
        </span>
      </div>
    );

    return (
      <Image
        className={className}
        alt={name}
        src={imgUrl}
        overlayContent={mask}
        overlayTrigger="hover"
        fit="contain"
        style={{ width, height, margin: '0 auto' }}
      />
    );
  };

  return <ImageViewer trigger={trigger} images={[img]} />;
}
