import { But<PERSON>, Popup, Select } from 'tdesign-react';
import { css } from '@emotion/react';
import {
  Dispatch,
  SetStateAction,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { callTranslateAPI } from '@/pages/List/components/realtime_qa_drawer/input';
export type Lang = 'zh' | 'ja' | 'en' | 'ko';
export type TranslationOptions = [Lang | 'auto' | undefined, Lang | undefined];
export function Translation(props: {
  state?: [TranslationOptions, Dispatch<SetStateAction<TranslationOptions>>];
  // value: TranslationOptions | undefined;
  // onChange: (v: TranslationOptions) => void;
}) {
  const [value, onChange] = props.state || [];
  console.log('ok');
  return (
    <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
      <Select
        size="small"
        value={value?.[0]}
        onChange={(v) => onChange?.([v, value?.[1]] as TranslationOptions)}
        style={{ width: 90 }}
        clearable
        options={[{ label: '自动检测', value: 'auto' }, ...lang]}
      />
      <svg
        width="14"
        height="5"
        viewBox="0 0 14 5"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.7929 4L-8.74228e-08 4L0 5L12.9276 5C13.3998 5 13.6362 4.42908 13.3023 4.09522L9.35354 0.146617L8.64645 0.85374L11.7929 4Z"
          fill="black"
          fillOpacity="0.9"
        />
      </svg>

      <Select
        size="small"
        value={value?.[1]}
        onChange={(v) => onChange?.([value?.[0], v] as TranslationOptions)}
        style={{ width: 90 }}
        clearable
        options={lang}
      />
    </div>
  );
}
const langConst = [
  ['zh', '中文'],
  ['en', '英语', 'English'],
  ['ja', '日语', '日本語'],
  ['ko', '韩语', '한국어'],
  ['ar', '阿拉伯语', 'العربية'],
  ['tr', '土耳其语', 'Türkçe'],
  ['ru', '俄语', 'Русский язык'],
  ['id', '印尼语', 'Bahasa Indonesia'],
  ['ms', '马来语', 'Bahasa Melayu'],
  ['de', '德语', 'Deutsch'],
  ['th', '泰语', 'ภาษาไทย'],
  ['pt', '葡萄牙语', 'Português'],
  ['es', '西班牙语', 'Español'],
  ['fr', '法语', 'Français'],
] as const;
const lang = langConst.map(([id, name, demo]) => ({
  label: name,
  content: demo ? (
    <>
      {name}
      <span
        css={css`
          color: rgba(0, 0, 0, 0.3);
          padding-left: 4px;
        `}
      >
        {demo}
      </span>
    </>
  ) : undefined,
  value: id,
}));

export function TranslationButton(props: {
  state?: [TranslationOptions, Dispatch<SetStateAction<TranslationOptions>>];
}) {
  const randID = useMemo(() => Math.random().toString(36).substring(2), []);
  return (
    <Popup
      trigger="click"
      content={
        <div style={{ padding: '15px 8px' }}>
          <Translation state={props.state} />
        </div>
      }
      showArrow
    >
      <Button
        theme="default"
        shape="circle"
        variant="text"
        size="small"
        style={{
          boxShadow: '0px 8px 10px -5px rgba(0, 0, 0, 0.18)',
        }}
      >
        <svg
          width="12"
          height="13"
          viewBox="0 0 12 13"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
        >
          <rect
            y="0.5"
            width="12"
            height="12"
            fill={`url(#pattern0_12951_113177${randID})`}
          />
          <defs>
            <pattern
              id={`pattern0_12951_113177${randID}`}
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref={`#image0_12951_113177${randID}`}
                transform="scale(0.005)"
              />
            </pattern>
            <image
              id={`image0_12951_113177${randID}`}
              width="200"
              height="200"
              preserveAspectRatio="none"
              xlinkHref="data:image/png;base64,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"
            />
          </defs>
        </svg>
      </Button>
    </Popup>
  );
}

export function AddButton(props: { onClick: () => void }) {
  // const randID = useMemo(() => Math.random().toString(36).substring(2), []);
  return (
    <Button
      theme="default"
      shape="circle"
      variant="text"
      size="small"
      style={{
        boxShadow: '0px 8px 10px -5px rgba(0, 0, 0, 0.18)',
      }}
      onClick={props.onClick}
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.34998 8.64998V11.85C7.34998 12.209 7.64099 12.5 7.99998 12.5C8.35896 12.5 8.64998 12.209 8.64998 11.85V8.64998H11.85C12.209 8.64998 12.5 8.35896 12.5 7.99998C12.5 7.64099 12.209 7.34998 11.85 7.34998H8.64998V4.15C8.64998 3.79101 8.35896 3.5 7.99998 3.5C7.64099 3.5 7.34998 3.79101 7.34998 4.15V7.34998H4.15C3.79101 7.34998 3.5 7.64099 3.5 7.99998C3.5 8.35896 3.79101 8.64998 4.15 8.64998H7.34998Z"
          fill="#1A51FF"
        />
      </svg>
    </Button>
  );
}

export const useTranslationResult = (props: {
  // 是否开启翻译
  switch: boolean;
  text: string;
  globalLang?: TranslationOptions;
  lang?: TranslationOptions;
}): {
  result: undefined | string | Error;
} => {
  const [result, setResult] = useState<undefined | string | Error>(undefined);
  const sourceLang = props.lang?.[0] || props.globalLang?.[0] || 'auto';
  const targetLang = props.lang?.[1] || props.globalLang?.[1] || 'en';
  const ref = useRef<AbortController>();
  useEffect(() => {
    if (ref.current) {
      ref.current.abort();
    }
    if (props.switch) {
      if (!props.text) {
        setResult('');
        return;
      }
      setResult(undefined);
      const abort = new AbortController();
      ref.current = abort;
      callTranslateAPI([sourceLang, targetLang], props.text, {
        signal: abort.signal,
      })
        .then((result) => {
          setResult(result);
        })
        .catch((err) => {
          setResult(err);
        });
    } else {
      setResult(undefined);
    }
  }, [props.switch, props.text, sourceLang, targetLang]);
  return {
    result,
  };
};
export function TranslationResult(props: {
  // undefined loading中
  // string 翻译结果
  // Error 翻译失败
  result: undefined | string | Error;
}) {
  return (
    <div
      style={{
        marginTop: 10,
        borderTop: '1px solid rgba(232, 237, 245, 1)',
        color: 'rgba(0,0,0,0.4)',
        paddingTop: 8,
        fontSize: 12,
        lineHeight: '22px',
      }}
    >
      {props.result === undefined && 'Loading...'}
      {typeof props.result === 'string' && props.result}
      {props.result instanceof Error && (
        <div>翻译出错：{props.result.message}</div>
      )}
    </div>
  );
}
