import { css } from '@emotion/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { RealTimeQADrawer } from '../realtime_qa_drawer/RealTimeQADrawer';
import './realtime_qa_entry.less';
import { RealTimeInteraction } from '@/pb/pb';

export interface IRealTimeQAEntryProps {
  // 直播id
  liveID: string;

  // 直播间名称
  liveName: string;
}

export function RealTimeQAEntry(props: IRealTimeQAEntryProps) {
  const { liveID, ...rest } = props;
  const [questionCount, setQuestionCount] = useState(0);
  const [realTimeQAVisible, setRealTimeQAVisible] = useState(false);
  const openRealTimeQA = async () => setRealTimeQAVisible(true);

  const handleEntryClick = async () => {
    // if (questionCount === 0) return;
    openRealTimeQA();
  };

  const entryCss = useMemo(() => {
    return css`
      opacity: ${questionCount === 0 ? '0.4' : '1'};
      cursor: ${questionCount === 0 ? 'auto' : 'pointer'};
    `;
  }, [questionCount]);

  // 从后台拉取问题，并展示数量
  const loadQuestion = useCallback(async () => {
    const resp = await RealTimeInteraction.QueryLivesQuestionInfo({
      live_id_list: [liveID],
    });
    if (resp.live_question_info_list.length > 0) {
      const item = resp.live_question_info_list[0];
      if (item.live_id === liveID) {
        setQuestionCount(item.question_num);
      }
    }
  }, [liveID]);

  useEffect(() => {
    loadQuestion();
  }, [loadQuestion]);

  return (
    <>
      <div className="qaentry-wrapper">
        已有
        <span
          css={css`
            font-weight: 600;
          `}
        >
          {questionCount}
        </span>
        个提问
        <span className="qaentry-goqa" css={entryCss}>
          {' '}
          <span onClick={handleEntryClick}>
            去干预{' '}
            <svg
              width="5"
              height="10"
              viewBox="0 0 5 10"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1.37891 9.00132C1.12507 9.25516 0.713508 9.25516 0.459667 9.00132C0.205827 8.74748 0.205827 8.33592 0.459668 8.08208L3.54043 5.00132L0.459668 1.92056C0.205827 1.66672 0.205827 1.25516 0.459668 1.00132C0.713509 0.747478 1.12507 0.747478 1.37891 1.00132L4.6718 4.29421C5.06232 4.68474 5.06232 5.3179 4.6718 5.70842L1.37891 9.00132Z"
                fill="#0047F9"
              />
            </svg>
          </span>
        </span>
      </div>
      <RealTimeQADrawer
        show={realTimeQAVisible}
        liveID={liveID}
        onClose={() => setRealTimeQAVisible(false)}
        {...rest}
      />
    </>
  );
}
