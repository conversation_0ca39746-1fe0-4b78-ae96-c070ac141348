import { SearchIcon } from 'tdesign-icons-react';
import * as XLSX from 'xlsx';
import {
  Button,
  Dialog,
  Form,
  Input,
  Popconfirm,
  Radio,
  Space,
  Upload,
  UploadFile,
} from 'tdesign-react';
import './index.less';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMetaLiveList } from '@/pages/ADMetaList/useMetaLiveList';
import { MetaRecord } from '@/pages/ADMetaList/MetaRecord';
import { useLiveGroup } from '../ADMetaList/hooks/useLiveGroup';
import { useMemoizedFn } from 'ahooks';
import { parseImportScript } from './import/ImportScript';

const { FormItem } = Form;

const contentType = 'text_live';

export function MetaLiveTabContent() {
  const {
    refresh,
    loading,
    recordCount,
    records,
    setRecords,
    setRecordCount,
    setQueryParams,
    queryParams,
  } = useMetaLiveList({
    queryParamsDefault: {
      searchKey: '',
      contentType: '',
      // 分页参数
      pageSize: 10,
      pageNum: 1,
    },
  });

  const {
    liveGroupList,
    addLiveGroup,
    queryLiveGroupLiveInfo,
    editLiveGroup,
    deleteLiveGroup,
    activeLiveGroupId,
    setActiveLiveGroupId,
  } = useLiveGroup();
  const navigate = useNavigate();

  // 搜索条件
  const [searchVal, setSearchVal] = useState('');

  const doSearch = () => {
    setQueryParams({
      ...queryParams,
      pageNum: 1,
      searchKey: searchVal,
    });
  };

  const goCreate = () => {
    navigate('/live-template');
  };

  const switchLiveGroup = async (groupId: string) => {
    if (groupId === activeLiveGroupId) return;
    setActiveLiveGroupId(groupId);
    if (groupId === '__default__') {
      // 默认直播间列表
      setQueryParams({
        ...queryParams,
        pageNum: 1,
        searchKey: '',
        contentType: '',
      });
    } else {
      await getLiveListFromGroup(groupId);
    }
  };

  const handleDeleteConfirm = (id: string) => {
    if (id === '__default__' || !id) {
      return;
    }
    deleteLiveGroup(id);
    switchLiveGroup('__default__');
  };

  const handleShowDialog = (type: string) => {
    if (!type) return;
    if (type === 'ADD') {
      form.reset();
    } else if (type === 'EDIT') {
      const activeGroupName = liveGroupList.find((group) => {
        return group.live_group_id === activeLiveGroupId;
      });
      form.reset();
      form.setFieldsValue({ name: activeGroupName?.title });
    }
    setDialogType(type);
  };

  const handleDialogConfirm = () => {
    if (dialogType === 'ADD') {
      addLiveGroup({ title: currentGroupName });
    } else if (dialogType === 'EDIT') {
      editLiveGroup({
        title: currentGroupName,
        id: activeLiveGroupId,
      });
    }
    setDialogType('');
  };

  const handleDialogClose = () => {
    setDialogType('');
  };

  const [dialogType, setDialogType] = useState('');

  const [form] = Form.useForm();
  const [createType, setCreateType] = useState('manual');
  const [importFiles, setImportFiles] = useState<UploadFile[]>([]);
  const currentGroupName = Form.useWatch('name', form);

  const getLiveListFromGroup = async (groupId: string) => {
    const res = await queryLiveGroupLiveInfo(groupId, 1, 100);
    setRecords(res?.list || []);
    setRecordCount(res?.count || 0);
  };

  const memoRefresh = useMemoizedFn(async () => {
    if (activeLiveGroupId === '__default__') {
      await refresh();
    } else {
      await getLiveListFromGroup(activeLiveGroupId);
    }
  });

  // 其他页面返回时刷新一下列表
  useEffect(() => {
    const visibilitychangeReport = () => {
      if (document.visibilityState === 'visible') {
        memoRefresh().then();
      }
    };
    document.addEventListener('visibilitychange', visibilitychangeReport);

    return () => {
      document.removeEventListener('visibilitychange', visibilitychangeReport);
    };
  }, [memoRefresh, refresh]);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    const startRefresh = async () => {
      memoRefresh().then(() => {
        timeout = setTimeout(startRefresh, 5000);
      });
    };
    timeout = setTimeout(startRefresh, 5000);
    return () => clearTimeout(timeout);
  }, [memoRefresh]);

  return (
    <div className="tab_content with-left-menu">
      <div className="live-group">
        <button
          className={`default-live-group title ${
            activeLiveGroupId === '__default__' ? 'active' : ''
          }`}
          onClick={() => switchLiveGroup('__default__')}
        >
          直播间
        </button>
        <hr />
        <div className="live-group-title">
          <span className="title">直播组</span>
          <button className="add-btn" onClick={() => handleShowDialog('ADD')}>
            + 新增
          </button>
        </div>
        <div className="live-group-list">
          {liveGroupList.length === 0 ? (
            <span className="empty-tips">暂无直播组</span>
          ) : (
            <ul>
              {liveGroupList.map((item) => (
                <li
                  key={item.live_group_id}
                  className={`live-group-list-item ${
                    activeLiveGroupId === item.live_group_id ? 'active' : ''
                  }`}
                  onClick={() => switchLiveGroup(item.live_group_id)}
                >
                  <Popconfirm
                    content="如该直播组内已有直播间，删除组后直播间也会一并删除，请确认是否删除？"
                    theme="danger"
                    onConfirm={() => handleDeleteConfirm(item.live_group_id)}
                  >
                    <button className="live-group-delete-btn" />
                  </Popconfirm>
                  <p className="live-group-name">
                    {item.title ? item.title : '未命名直播组'}
                  </p>
                  <p className="live-group-id" title={item.live_group_id}>
                    ID: {item.live_group_id}
                  </p>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
      <div className="live-stream-table">
        <div className="action_bar">
          <div className="btn-group">
            <div className="btn min-w-[6em]" onClick={() => goCreate()}>
              创建直播间
            </div>
            <div className="pagedoo-meta-live-global">
              {activeLiveGroupId !== '__default__' && (
                <div
                  className="btn min-w-[6em] default-btn"
                  onClick={() => handleShowDialog('EDIT')}
                >
                  编辑管理
                </div>
              )}
              <Dialog
                width={670}
                className="live-group-dialog"
                visible={!!dialogType}
                header={dialogType === 'EDIT' ? '编辑直播组信息' : '新增直播组'}
                onClose={() => {
                  handleDialogClose();
                }}
                confirmBtn={false}
                cancelBtn={false}
              >
                <Form
                  form={form}
                  onSubmit={(e) => {
                    if (e.firstError) {
                      return;
                    }
                    handleDialogConfirm();
                  }}
                >
                  <FormItem>
                    <Radio.Group
                      className="live-group-dialog-radio-group"
                      variant="default-filled"
                      defaultValue="manual"
                      value={createType}
                    >
                      {[
                        { label: '手动新增', value: 'manual' },
                        { label: '脚本导入', value: 'script' },
                      ].map((item) => (
                        <Radio.Button
                          key={item.value}
                          value={item.value}
                          checked={createType === item.value}
                          onChange={() => setCreateType(item.value)}
                        >
                          {item.label}
                        </Radio.Button>
                      ))}
                    </Radio.Group>
                  </FormItem>
                  {createType === 'manual' && (
                    <FormItem
                      label="直播组名称"
                      name="name"
                      rules={[
                        { required: true },
                        { whitespace: true },
                        { max: 100 },
                        { min: 1 },
                        {
                          pattern: /^[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/,
                          message: '仅支持中英文、数字、下划线、横线',
                        },
                      ]}
                    >
                      <Input placeholder="请填写直播组名称" />
                    </FormItem>
                  )}
                  {createType === 'script' && (
                    <FormItem label="上传脚本" name="script">
                      <Upload
                        theme="file-flow"
                        draggable
                        accept=".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        files={importFiles}
                        onSelectChange={(files) => {
                          setImportFiles(files);
                          // 使用 XLSX 解析 files 中的第一个文件，生成结构化数据
                          if (files.length > 0) {
                            files[0].arrayBuffer().then((buffer) => {
                              try {
                                const parsedData = parseImportScript(buffer);
                                console.log('解析到的脚本数据:', parsedData);
                                // TODO: 这里可以将解析后的数据保存到状态中，用于后续处理
                                // setParsedScriptData(parsedData);
                              } catch (error) {
                                console.error('脚本解析失败:', error);
                                // TODO: 显示错误提示给用户
                              }
                            });
                          }
                        }}
                        allowUploadDuplicateFile
                        requestMethod={async (_files) => {
                          return {
                            status: 'success',
                            response: {
                              url: '',
                              files: [],
                            },
                          };
                        }}
                        // use fileListDisplay to define any file info
                        fileListDisplay={({ files }) => (
                          <div>{JSON.stringify(files)}</div>
                        )}
                      />
                    </FormItem>
                  )}
                  <FormItem
                    style={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                    }}
                  >
                    <Space>
                      <Button
                        theme="default"
                        variant="outline"
                        onClick={() => handleDialogClose()}
                      >
                        取消
                      </Button>
                      <Button type="submit">确认</Button>
                    </Space>
                  </FormItem>
                </Form>
              </Dialog>
            </div>
          </div>
          <Input
            className="search_input"
            value={searchVal}
            onChange={setSearchVal}
            onEnter={() => doSearch()}
            suffixIcon={<SearchIcon onClick={() => doSearch()} />}
          />
        </div>
        <div className="meta-live-record">
          <MetaRecord
            record={records}
            loading={loading}
            recordCount={recordCount}
            queryParams={queryParams}
            setQueryParams={setQueryParams}
            refresh={async (groupId?: string) => {
              const targetGroupId = groupId || activeLiveGroupId;
              if (targetGroupId === '__default__') {
                return refresh();
              }
              getLiveListFromGroup(targetGroupId).then();
            }}
            contentType={contentType}
            groupId={activeLiveGroupId}
            groupList={liveGroupList}
            setGroupId={setActiveLiveGroupId}
          />
        </div>
      </div>
    </div>
  );
}
