import * as XLSX from 'xlsx';

// 分镜数据结构
export interface ScriptSceneData {
  环节: string;
  台词: string;
  场景: string;
  数字人: string;
  背景: string;
  前景: string;
  视频: string;
  音乐: string;
  转场: string;
  事件: string;
}

// 图片素材数据结构
export interface ImageAssetData {
  名称: string;
  英语?: string;
  土耳其语?: string;
  阿拉伯语?: string;
  重名检测?: string;
  [key: string]: string | undefined;
}

// 音视频素材数据结构
export interface VideoAssetData {
  名称: string;
  土耳其语?: string;
  英语?: string;
  阿拉伯语?: string;
  定位?: string;
  音量?: string;
  重名检测?: string;
  [key: string]: string | undefined;
}

// 事件数据结构
export interface EventData {
  名称: string;
  类型: string;
  广告ID?: string;
  在线时长?: string;
  重名检测?: string;
}

// 数字人数据结构
export interface VirtualHumanData {
  名称: string;
  语种: string;
  语速: string;
  音色: string;
  语气?: string;
  性别: string;
  地域: string;
}

// 场景数据结构
export interface SceneData {
  名称: string;
  参数1?: string;
  参数2?: string;
  参数3?: string;
  参数4?: string;
}

// 完整的解析结果数据结构
export interface ParsedScriptData {
  教程?: string;
  分镜数据: {
    [language: string]: ScriptSceneData[];
  };
  图片素材: ImageAssetData[];
  音视频素材: VideoAssetData[];
  事件配置: EventData[];
  数字人配置: VirtualHumanData[];
  场景配置: SceneData[];
}

// 辅助函数：解析表格数据为对象数组
function parseTableData<T>(jsonData: (string | number)[][], startRow = 1): T[] {
  if (jsonData.length <= startRow) return [];

  const headers = jsonData[0] as string[];
  const result: T[] = [];

  for (let i = startRow; i < jsonData.length; i++) {
    const row = jsonData[i];
    if (!row || row.length === 0) continue;

    const rowData: Record<string, string> = {};
    headers.forEach((header, index) => {
      if (header?.trim()) {
        const cellValue = row[index];
        rowData[header.trim()] = cellValue ? String(cellValue).trim() : '';
      }
    });

    // 只添加有效的行（至少有一个非空字段）
    if (Object.values(rowData).some((value) => value !== '')) {
      result.push(rowData as T);
    }
  }

  return result;
}

// 主解析函数
export function parseImportScript(xlsxBuffer: ArrayBuffer): ParsedScriptData {
  try {
    // 读取 ArrayBuffer 并解析为工作簿
    const workbook = XLSX.read(xlsxBuffer, { type: 'array' });

    const result: ParsedScriptData = {
      分镜数据: {},
      图片素材: [],
      音视频素材: [],
      事件配置: [],
      数字人配置: [],
      场景配置: [],
    };

    // 遍历所有工作表
    workbook.SheetNames.forEach((sheetName) => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
      }) as (string | number)[][];

      if (jsonData.length === 0) return;

      // 根据工作表名称解析不同类型的数据
      if (sheetName === '教程' || sheetName.includes('教程')) {
        // 解析教程内容
        const tutorialContent = jsonData
          .flat()
          .filter((cell) => cell && String(cell).trim())
          .join('\n');
        result.教程 = tutorialContent;
      } else if (sheetName.includes('分镜') || sheetName.includes('语')) {
        // 解析分镜数据（如：英语分镜、阿拉伯语分镜等）
        const sceneData = parseTableData<ScriptSceneData>(jsonData);
        if (sceneData.length > 0) {
          result.分镜数据[sheetName] = sceneData;
        }
      } else if (sheetName === '图片' || sheetName.includes('图片')) {
        // 解析图片素材
        result.图片素材 = parseTableData<ImageAssetData>(jsonData);
      } else if (
        sheetName === '音视频' ||
        sheetName.includes('音视频') ||
        sheetName.includes('视频')
      ) {
        // 解析音视频素材
        result.音视频素材 = parseTableData<VideoAssetData>(jsonData);
      } else if (sheetName === '事件' || sheetName.includes('事件')) {
        // 解析事件配置
        result.事件配置 = parseTableData<EventData>(jsonData);
      } else if (sheetName === '数字人' || sheetName.includes('数字人')) {
        // 解析数字人配置
        result.数字人配置 = parseTableData<VirtualHumanData>(jsonData);
      } else if (sheetName === '场景' || sheetName.includes('场景')) {
        // 解析场景配置
        result.场景配置 = parseTableData<SceneData>(jsonData);
      }
    });

    console.log('Excel解析成功');
    console.log('解析结果:', result);

    return result;
  } catch (error) {
    console.error('Excel解析失败:', error);
    throw new Error(
      `Excel解析失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}
