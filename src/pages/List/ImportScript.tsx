import * as XLSX from 'xlsx';

// 定义解析后的数据结构
export interface ParsedScriptData {
  序号: number;
  环节类型: string;
  画面名称: string;
  画面内容: string;
  画面正文: string;
  台词文案: string;
  [key: string]: string | number; // 允许其他字段
}

export function parseImportScript(xlsxBuffer: ArrayBuffer): ParsedScriptData[] {
  try {
    // 读取 ArrayBuffer 并解析为工作簿
    const workbook = XLSX.read(xlsxBuffer, { type: 'array' });

    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];

    // 将工作表转换为 JSON 数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1, // 使用数组格式，第一行作为表头
      defval: '', // 空单元格的默认值
    }) as (string | number)[][];

    if (jsonData.length === 0) {
      throw new Error('Excel文件为空');
    }

    // 获取表头
    const headers = jsonData[0] as string[];

    // 验证必要的列是否存在
    const requiredColumns = [
      '序号',
      '环节类型',
      '画面名称',
      '画面内容',
      '画面正文',
      '台词文案',
    ];
    const missingColumns = requiredColumns.filter(
      (col) => !headers.includes(col)
    );

    if (missingColumns.length > 0) {
      console.warn(`缺少以下列: ${missingColumns.join(', ')}`);
    }

    // 解析数据行
    const parsedData: ParsedScriptData[] = [];

    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (!row || row.length === 0) continue; // 跳过空行

      const rowData: ParsedScriptData = {
        序号: 0,
        环节类型: '',
        画面名称: '',
        画面内容: '',
        画面正文: '',
        台词文案: '',
      };

      // 根据表头映射数据
      headers.forEach((header, index) => {
        const cellValue = row[index];
        if (header && cellValue !== undefined) {
          // 特殊处理序号字段，确保是数字
          if (header === '序号') {
            rowData[header] =
              typeof cellValue === 'number'
                ? cellValue
                : parseInt(String(cellValue), 10) || i;
          } else {
            rowData[header] = String(cellValue || '').trim();
          }
        }
      });

      // 只添加有效的行（至少有画面名称或台词文案）
      if (rowData.画面名称 || rowData.台词文案) {
        parsedData.push(rowData);
      }
    }

    console.log('Excel解析成功，共解析到', parsedData.length, '条数据');
    console.log('解析结果:', parsedData);

    return parsedData;
  } catch (error) {
    console.error('Excel解析失败:', error);
    throw new Error(
      `Excel解析失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}
