import * as XLSX from 'xlsx';

// 中文语言名称到ISO语言代码的映射表
export const LANGUAGE_MAPPING: Record<string, string> = {
  // 中文相关
  中文: 'zh',
  中国: 'zh',
  中国话: 'zh',
  汉语: 'zh',
  普通话: 'zh',
  简体中文: 'zh',
  繁体中文: 'zh',
  台湾: 'zh',
  香港: 'zh',

  // 英语相关
  英语: 'en',
  英文: 'en',
  英国: 'en',
  美国: 'en',
  澳大利亚: 'en',
  加拿大: 'en',

  // 阿拉伯语相关
  阿拉伯语: 'ar',
  阿拉伯文: 'ar',
  阿拉伯: 'ar',
  沙特阿拉伯: 'ar',
  阿联酋: 'ar',
  埃及: 'ar',

  // 土耳其语
  土耳其语: 'tr',
  土耳其文: 'tr',
  土耳其: 'tr',

  // 印尼语
  印尼语: 'id',
  印度尼西亚语: 'id',
  印尼文: 'id',
  印度尼西亚文: 'id',
  印度尼西亚: 'id',

  // 马来语
  马来语: 'ms',
  马来文: 'ms',
  马来西亚: 'ms',

  // 泰语
  泰语: 'th',
  泰文: 'th',
  泰国: 'th',

  // 越南语
  越南语: 'vi',
  越南文: 'vi',
  越南: 'vi',

  // 日语
  日语: 'ja',
  日文: 'ja',
  日本: 'ja',

  // 韩语
  韩语: 'ko',
  韩文: 'ko',
  朝鲜语: 'ko',
  朝鲜文: 'ko',
  韩国: 'ko',
  朝鲜: 'ko',

  // 法语
  法语: 'fr',
  法文: 'fr',
  法国: 'fr',
  加拿大法语: 'fr',

  // 德语
  德语: 'de',
  德文: 'de',
  德国: 'de',
  奥地利: 'de',
  瑞士德语: 'de',

  // 西班牙语
  西班牙语: 'es',
  西班牙文: 'es',
  西班牙: 'es',
  墨西哥: 'es',
  阿根廷: 'es',

  // 葡萄牙语
  葡萄牙语: 'pt',
  葡萄牙文: 'pt',
  葡萄牙: 'pt',
  巴西: 'pt',

  // 俄语
  俄语: 'ru',
  俄文: 'ru',
  俄国: 'ru',
  俄罗斯: 'ru',

  // 意大利语
  意大利语: 'it',
  意大利文: 'it',
  意大利: 'it',

  // 荷兰语
  荷兰语: 'nl',
  荷兰文: 'nl',
  荷兰: 'nl',
  比利时荷兰语: 'nl',

  // 波兰语
  波兰语: 'pl',
  波兰文: 'pl',
  波兰: 'pl',

  // 印地语
  印地语: 'hi',
  印地文: 'hi',
  印度: 'hi',

  // 孟加拉语
  孟加拉语: 'bn',
  孟加拉文: 'bn',
  孟加拉国: 'bn',

  // 乌尔都语
  乌尔都语: 'ur',
  乌尔都文: 'ur',
  巴基斯坦: 'ur',

  // 波斯语
  波斯语: 'fa',
  波斯文: 'fa',
  伊朗: 'fa',

  // 希伯来语
  希伯来语: 'he',
  希伯来文: 'he',
  以色列: 'he',

  // 希腊语
  希腊语: 'el',
  希腊文: 'el',
  希腊: 'el',

  // 瑞典语
  瑞典语: 'sv',
  瑞典文: 'sv',
  瑞典: 'sv',

  // 挪威语
  挪威语: 'no',
  挪威文: 'no',
  挪威: 'no',

  // 丹麦语
  丹麦语: 'da',
  丹麦文: 'da',
  丹麦: 'da',

  // 芬兰语
  芬兰语: 'fi',
  芬兰文: 'fi',
  芬兰: 'fi',
};

// 辅助函数：从工作表名称中提取语言代码
export function extractLanguageCode(sheetName: string): string | null {
  // 直接匹配映射表中的语言名称
  for (const [chineseName, isoCode] of Object.entries(LANGUAGE_MAPPING)) {
    if (sheetName.includes(chineseName)) {
      return isoCode;
    }
  }

  // 如果没有找到匹配，检查是否已经是ISO代码格式
  const isoPattern = /^[a-z]{2}(-[A-Z]{2})?$/;
  if (isoPattern.test(sheetName)) {
    return sheetName;
  }

  return null;
}

// 分镜数据结构（原始）
export interface RawScriptSceneData {
  环节: string;
  台词: string;
  场景: string;
  数字人: string;
  背景: string;
  前景: string;
  视频: string;
  音乐: string;
  转场: string;
  事件: string;
}

// 分镜数据结构（链接后）
export interface ScriptSceneData {
  环节: string;
  台词: string;
  场景: SceneData | null;
  数字人: VirtualHumanData | null;
  背景: string; // 图片URL或引用
  前景: string; // 图片URL或引用
  视频: VideoAssetData | null;
  音乐: VideoAssetData | null; // 音频也在音视频表中
  转场: string; // 图片URL或引用
  事件: EventData | null;
}

// 图片素材数据结构
export interface ImageAssetData {
  名称: string;
  链接: string; // 合并后的URL
}

// 音视频素材数据结构
export interface VideoAssetData {
  名称: string;
  链接: string; // 合并后的URL
  定位?: string;
  音量?: string;
}

// 事件数据结构
export interface EventData {
  名称: string;
  类型: string;
  广告ID?: string;
  在线时长?: string;
  重名检测?: string;
}

// 数字人数据结构
export interface VirtualHumanData {
  名称: string;
  语种: string;
  语速: string;
  音色: string;
  语气?: string;
  性别: string;
  地域: string;
}

// 场景数据结构
export interface SceneData {
  名称: string;
  参数1?: string;
  参数2?: string;
  参数3?: string;
  参数4?: string;
}

// 完整的解析结果数据结构
export interface ParsedScriptData {
  教程?: string;
  分镜数据: {
    [language: string]: ScriptSceneData[];
  };
  图片素材: ImageAssetData[];
  音视频素材: VideoAssetData[];
  事件配置: EventData[];
  数字人配置: VirtualHumanData[];
  场景配置: SceneData[];
  检测到的语言: string[]; // 新增：记录所有检测到的语言代码
}

// 临时存储原始数据的扩展接口
interface ParsedScriptDataWithRaw extends ParsedScriptData {
  [key: string]: unknown;
}

// 表格解析选项
interface TableParseOptions {
  skipColumns?: string[]; // 要跳过的列名
  mergeLanguageUrls?: boolean; // 是否合并语言URL为单一链接
  detectedLanguages?: Set<string>; // 用于收集检测到的语言
  requiredField?: string; // 必须存在的字段名（如"名称"）
}

// 通用表格解析函数：将Excel表格数据转换为JSON Array of Objects
function parseGenericTable<T>(
  jsonData: (string | number)[][],
  options: TableParseOptions = {},
  startRow = 1
): T[] {
  if (jsonData.length <= startRow) return [];

  const {
    skipColumns = ['重名检测'],
    mergeLanguageUrls = false,
    detectedLanguages,
    requiredField,
  } = options;

  const headers = jsonData[0] as string[];
  const result: T[] = [];

  for (let i = startRow; i < jsonData.length; i++) {
    const row = jsonData[i];
    if (!row || row.length === 0) continue;

    const rowData: Record<string, string> = {};
    const languageUrls: Record<string, string> = {};

    headers.forEach((header, index) => {
      if (!header?.trim()) return;

      const cellValue = row[index];
      const trimmedHeader = header.trim();

      // 跳过指定的列
      if (skipColumns.some((skipCol) => trimmedHeader.includes(skipCol))) {
        return;
      }

      if (mergeLanguageUrls) {
        // 检查是否是语言列
        const languageCode = extractLanguageCode(trimmedHeader);

        if (languageCode && languageCode !== trimmedHeader) {
          // 这是一个语言URL列
          const url = cellValue ? String(cellValue).trim() : '';
          if (url) {
            languageUrls[languageCode] = url;
            if (detectedLanguages) {
              detectedLanguages.add(languageCode);
              console.log(`语言列映射: ${trimmedHeader} -> ${languageCode}`);
            }
          }
        } else {
          // 这是普通字段
          rowData[trimmedHeader] = cellValue ? String(cellValue).trim() : '';
        }
      } else {
        // 不合并语言URL，直接处理所有字段
        rowData[trimmedHeader] = cellValue ? String(cellValue).trim() : '';
      }
    });

    // 如果需要合并语言URL，选择第一个可用的URL作为链接
    if (mergeLanguageUrls) {
      const availableUrls = Object.values(languageUrls).filter((url) => url);
      if (availableUrls.length > 0) {
        const [firstUrl] = availableUrls;
        rowData['链接'] = firstUrl;
      }
    }

    // 检查是否满足必需字段要求
    if (requiredField && !rowData[requiredField]) {
      continue;
    }

    // 只添加有效的行（至少有一个非空字段）
    const hasValidData = Object.values(rowData).some(
      (value) => value && value.trim() !== ''
    );

    if (hasValidData) {
      result.push(rowData as T);
    }
  }

  return result;
}

// 兼容性函数：保持原有的简单表格解析接口
function parseTableData<T>(jsonData: (string | number)[][], startRow = 1): T[] {
  return parseGenericTable<T>(jsonData, {}, startRow);
}

// 辅助函数：处理图片素材表，合并语言URL为单一链接
function parseImageAssetTable(
  jsonData: (string | number)[][],
  detectedLanguages: Set<string>,
  startRow = 1
): ImageAssetData[] {
  return parseGenericTable<ImageAssetData>(
    jsonData,
    {
      mergeLanguageUrls: true,
      detectedLanguages,
      requiredField: '名称',
    },
    startRow
  );
}

// 辅助函数：处理音视频素材表，合并语言URL为单一链接
function parseVideoAssetTable(
  jsonData: (string | number)[][],
  detectedLanguages: Set<string>,
  startRow = 1
): VideoAssetData[] {
  return parseGenericTable<VideoAssetData>(
    jsonData,
    {
      mergeLanguageUrls: true,
      detectedLanguages,
      requiredField: '名称',
    },
    startRow
  );
}

// 辅助函数：创建名称到数据的映射表
function createNameMap<T extends { 名称: string }>(items: T[]): Map<string, T> {
  const map = new Map<string, T>();
  items.forEach((item) => {
    if (item.名称?.trim()) {
      map.set(item.名称.trim(), item);
    }
  });
  return map;
}

// 辅助函数：从图片素材中获取URL
function getImageUrl(
  imageName: string,
  imageAssets: ImageAssetData[],
  _language?: string // 保留参数兼容性，但现在不使用
): string {
  if (!imageName?.trim()) return '';

  const asset = imageAssets.find((item) => item.名称 === imageName.trim());
  if (!asset) return imageName; // 如果找不到，返回原始名称

  // 直接返回链接字段
  return asset.链接 || '';
}

// 辅助函数：链接分镜数据到子表
function linkSceneDataToSubTables(
  rawSceneData: RawScriptSceneData[],
  imageAssets: ImageAssetData[],
  videoAssets: VideoAssetData[],
  events: EventData[],
  virtualHumans: VirtualHumanData[],
  scenes: SceneData[],
  language?: string
): ScriptSceneData[] {
  // 创建查找映射表
  const eventMap = createNameMap(events);
  const virtualHumanMap = createNameMap(virtualHumans);
  const sceneMap = createNameMap(scenes);
  const videoMap = createNameMap(videoAssets);

  return rawSceneData.map((rawData) => {
    // 过滤掉重名检测相关数据
    const cleanData = { ...rawData };

    return {
      环节: cleanData.环节,
      台词: cleanData.台词,
      场景: cleanData.场景 ? sceneMap.get(cleanData.场景) || null : null,
      数字人: cleanData.数字人
        ? virtualHumanMap.get(cleanData.数字人) || null
        : null,
      背景: getImageUrl(cleanData.背景, imageAssets, language),
      前景: getImageUrl(cleanData.前景, imageAssets, language),
      视频: cleanData.视频 ? videoMap.get(cleanData.视频) || null : null,
      音乐: cleanData.音乐 ? videoMap.get(cleanData.音乐) || null : null,
      转场: getImageUrl(cleanData.转场, imageAssets, language),
      事件: cleanData.事件 ? eventMap.get(cleanData.事件) || null : null,
    };
  });
}

// 主解析函数
export function parseImportScript(xlsxBuffer: ArrayBuffer): ParsedScriptData {
  try {
    // 读取 ArrayBuffer 并解析为工作簿
    const workbook = XLSX.read(xlsxBuffer, { type: 'array' });

    const result: ParsedScriptDataWithRaw = {
      分镜数据: {},
      图片素材: [],
      音视频素材: [],
      事件配置: [],
      数字人配置: [],
      场景配置: [],
      检测到的语言: [],
    };

    // 用于收集所有检测到的语言代码
    const detectedLanguages = new Set<string>();

    // 遍历所有工作表
    workbook.SheetNames.forEach((sheetName) => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
      }) as (string | number)[][];

      if (jsonData.length === 0) return;

      // 根据工作表名称解析不同类型的数据
      if (sheetName === '教程' || sheetName.includes('教程')) {
        // 解析教程内容
        const tutorialContent = jsonData
          .flat()
          .filter((cell) => cell && String(cell).trim())
          .join('\n');
        result.教程 = tutorialContent;
      } else if (sheetName.includes('分镜') || sheetName.includes('语')) {
        // 先解析原始分镜数据
        const rawSceneData = parseTableData<RawScriptSceneData>(jsonData);
        if (rawSceneData.length > 0) {
          // 尝试提取语言代码，如果找到则使用ISO代码作为键，否则使用原始名称
          const languageCode = extractLanguageCode(sheetName);
          const key = languageCode || sheetName;

          // 暂时存储原始数据，稍后进行链接处理
          result[`_raw_${key}`] = rawSceneData;

          // 如果成功提取了语言代码，记录映射关系并添加到检测列表
          if (languageCode) {
            console.log(`检测到语言: ${sheetName} -> ${languageCode}`);
            detectedLanguages.add(languageCode);
          }
        }
      } else if (sheetName === '图片' || sheetName.includes('图片')) {
        // 解析图片素材，合并语言URL为单一链接
        result.图片素材 = parseImageAssetTable(jsonData, detectedLanguages);
      } else if (
        sheetName === '音视频' ||
        sheetName.includes('音视频') ||
        sheetName.includes('视频')
      ) {
        // 解析音视频素材，合并语言URL为单一链接
        result.音视频素材 = parseVideoAssetTable(jsonData, detectedLanguages);
      } else if (sheetName === '事件' || sheetName.includes('事件')) {
        // 解析事件配置
        result.事件配置 = parseTableData<EventData>(jsonData);
      } else if (sheetName === '数字人' || sheetName.includes('数字人')) {
        // 解析数字人配置
        result.数字人配置 = parseTableData<VirtualHumanData>(jsonData);
      } else if (sheetName === '场景' || sheetName.includes('场景')) {
        // 解析场景配置
        result.场景配置 = parseTableData<SceneData>(jsonData);
      }
    });

    // 将检测到的语言添加到结果中
    result.检测到的语言 = Array.from(detectedLanguages).sort();

    // 处理分镜数据链接
    const resultWithRaw = result;
    Object.keys(resultWithRaw).forEach((key) => {
      if (key.startsWith('_raw_')) {
        const originalKey = key.replace('_raw_', '');
        const rawSceneData = resultWithRaw[key] as RawScriptSceneData[];

        // 提取语言代码用于图片URL选择
        const languageCode = extractLanguageCode(originalKey);

        // 链接分镜数据到子表
        const linkedSceneData = linkSceneDataToSubTables(
          rawSceneData,
          result.图片素材,
          result.音视频素材,
          result.事件配置,
          result.数字人配置,
          result.场景配置,
          languageCode || undefined
        );

        result.分镜数据[originalKey] = linkedSceneData;

        console.log(
          `已链接 ${originalKey} 分镜数据，共 ${linkedSceneData.length} 条记录`
        );

        // 删除临时的原始数据
        delete resultWithRaw[key];
      }
    });

    console.log('Excel解析成功');
    console.log('检测到的语言:', result.检测到的语言);
    console.log('解析结果:', result);

    return result;
  } catch (error) {
    console.error('Excel解析失败:', error);
    throw new Error(
      `Excel解析失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}
