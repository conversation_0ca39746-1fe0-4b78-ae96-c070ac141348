import { Loading } from '@/components/Loading';
import {
  IVideoMakerFormInstance,
  VideoMakerForm,
} from '@/components/VideoMaker';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { RespType } from '@/pb/config';
import { VideoSvr } from '@/pb/pb';
import { downloadVideo } from '@/utils';
import { sleep } from '@/utils/sleep';
import { useLatest, usePagination } from 'ahooks';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { SearchIcon } from 'tdesign-icons-react';
import {
  Dialog,
  DialogPlugin,
  Input,
  MessagePlugin,
  Pagination,
  Popconfirm,
  Popup,
  Table,
} from 'tdesign-react';
import { ContentType } from '.';
import { openEditor } from '../Editor/common/openEditor';
import { EditorSystemMap } from '../Editor/editorConfig';
import { BasicImageViewer } from './components/image_viewer';
import { VideoViewer } from './components/video_viewer';
import './index.less';

type VideoListItem = RespType<typeof VideoSvr.VideoList>['video_list'][number];

interface IProps {
  contentType: ContentType;
  handleCreate?: () => void;
}

export function VideoTabContent({ handleCreate }: IProps) {
  // 搜索条件
  const [searchVal, setSearchVal] = useState('');
  const [previewVideoId, setPreviewVideoId] = useState('');
  // 制作参数
  const releaseFormRef = useRef<IVideoMakerFormInstance>(null);

  // 控制loading动画的展示
  const [showLoading, setShowLoading] = useState(true);

  const timer = useRef<number | undefined | NodeJS.Timeout>(undefined);

  async function getVideoList({
    searchText,
    current,
    pageSize,
  }: {
    searchText: string;
    current: number;
    pageSize: number;
  }): Promise<{ total: number; list: VideoListItem[] }> {
    try {
      const res = await VideoSvr.VideoList({
        video_name_or_id: searchText,
        // content_type: `${contentType}_video`, TODO: 暂时去掉类型过滤
        content_type: '',
        page_num: current,
        page_size: pageSize,
      });
      const list = res.video_list;
      // 只要有制作中的，就刷新
      if (list.some((item) => item.video_status === '3')) {
        setShowLoading(false);
        clearTimeout(timer.current);
        timer.current = setTimeout(refresh, 3e3);
      } else {
        setShowLoading(true);
      }
      return { total: parseInt(res.count, 10), list };
    } catch (e) {
      return { total: 0, list: [] };
    }
  }
  // 销毁定时器
  useEffect(() => () => clearTimeout(timer.current), []);
  const { loading, pagination, data, run, params } = usePagination(
    getVideoList,
    { manual: false }
  );

  const latestParams = useLatest(params);

  const refresh = () => {
    run({
      searchText: searchVal,
      current: latestParams.current?.[0]?.current || 1,
      pageSize: latestParams.current?.[0]?.pageSize || 10,
    });
  };

  function doSearch() {
    run({
      searchText: searchVal,
      current: 1,
      pageSize: params?.[0]?.pageSize || 10,
    });
  }

  function goCreate() {
    handleCreate?.();
  }

  function makeVideoFile(videoId: string, loadingTs = 1000) {
    const dialog = DialogPlugin.confirm({
      className: 'make_video_dialog',
      header: '制作视频',
      body: (
        <div className="dialog_body">
          <VideoMakerForm ref={releaseFormRef} />
        </div>
      ),
      onCancel: () => dialog.destroy(),
      onClose: () => dialog.destroy(),
      onConfirm: async () => {
        const formVals = releaseFormRef.current?.getFormValues();
        if (!formVals) return;
        const { definition, format } = formVals;
        const loading = MessagePlugin.loading({
          content: '正在提交制作...',
          duration: 0,
        });
        // const definitionMap: Record<string, any> = {
        //   horizontal: {
        //     '720P': '1280x720',
        //     '1080P': '1920x1080',
        //     '2K': '2560x1440',
        //     '4K': '3840x2160',
        //   },
        //   vertical: {
        //     '720P': '720x1280',
        //     '1080P': '1080x1920',
        //     '2K': '1440x2560',
        //     '4K': '2160x3840',
        //   },
        // };
        // const definitionTarget =
        //   definitionMap[direction as keyof typeof definitionMap][definition];
        const ts = +new Date();
        const res = await releaseVideo(videoId, definition, format as string);
        const costTime = +new Date() - ts;
        costTime < loadingTs && (await sleep(loadingTs - costTime));
        MessagePlugin.close(loading);
        if (!res) {
          MessagePlugin.error({ content: '提交制作失败，请稍候重试！' });
          return;
        }
        MessagePlugin.success({ content: '提交制作成功' });
        refresh();
        dialog.destroy();
      },
    });
  }

  async function releaseVideo(
    videoId: string,
    definition: string,
    format: string
  ) {
    try {
      const res = await VideoSvr.VideoRelease({
        video_id: videoId,
        video_definition: definition || '',
        video_format: format || '',
      });
      return res.result_code === '0';
    } catch (e) {
      return false;
    }
  }

  async function stopRelease(videoId: string) {
    try {
      const res = await VideoSvr.VideoStop({
        video_id: videoId,
      });
      if (res.result_code !== '0') {
        throw new Error(res.result_info || '取消制作失败');
      }
      MessagePlugin.success({ content: '取消制作成功' });
      refresh();
    } catch (e: any) {
      MessagePlugin.success({ content: e.message || '取消制作失败' });
    }
  }

  function download(videoStatus: string, videoUrl: string, videoName: string) {
    if (videoStatus === '3') {
      MessagePlugin.warning({ content: '该视频正在制作中，暂不支持下载！' });
      return;
    }

    if (videoUrl === '') {
      MessagePlugin.warning({ content: '该视频还未制作完成，暂不支持下载！' });
      return;
    }

    downloadVideo(videoUrl, videoName, '_self');
  }

  // function videoPreview(videoId: string) {
  //   const dialog = DialogPlugin.alert({
  //     className: 'video_preview_dialog',
  //     width: 'auto',
  //     placement: 'center',
  //     header: '视频预览',
  //     body: <VideoViewer id={videoId} />,
  //     footer: false,
  //     onClose: () => dialog.destroy(),
  //   });
  // }

  function openEdit(videoId: string, contentType: ContentType) {
    openEditor({
      system: EditorSystemMap.META_HUMAN,
      contentType: CONTENT_TYPE_MAP.Video.value,
      origin: contentType,
      contentId: videoId,
    });
  }

  async function deleteVideo(videoId: string) {
    try {
      const res = await VideoSvr.VideoDelete({
        video_id: videoId,
      });
      if (res.result_code !== '0') {
        throw new Error(res.result_info || '删除失败');
      }
      MessagePlugin.success({ content: '删除成功' });
      refresh();
    } catch (e: any) {
      MessagePlugin.success({ content: e.message || '删除失败' });
    }
  }

  async function copyVideo(id: string) {
    try {
      const res = await VideoSvr.VideoCopy({
        video_id: id,
      });
      if (res.result_code !== '0') {
        throw new Error(res.result_info || '复制失败');
      }
      MessagePlugin.success({ content: '复制成功' });
      refresh();
    } catch (e: any) {
      MessagePlugin.success({ content: e.message || '复制失败' });
    }
  }

  return (
    <>
      <div className="tab_content">
        <div className="action_bar">
          <div className="btn" onClick={() => goCreate()}>
            创建视频
          </div>
          <Input
            className="search_input"
            value={searchVal}
            onChange={setSearchVal}
            onEnter={() => doSearch()}
            suffixIcon={<SearchIcon onClick={() => doSearch()} />}
          />
        </div>
        {showLoading && loading ? (
          <div className="loading_wrap">
            <Loading />
          </div>
        ) : (
          <>
            <div className="table">
              <Table
                rowKey="video_id"
                data={data?.list || []}
                verticalAlign="middle"
                columns={[
                  {
                    title: '视频封面',
                    colKey: 'video_poster',
                    width: 100,
                    cell: ({ row }) => (
                      <BasicImageViewer
                        width={70}
                        height={135}
                        className="poster"
                        img={row.video_poster}
                        name="视频封面"
                      />
                    ),
                  },
                  {
                    title: '视频名称',
                    align: 'left',
                    colKey: 'video_name',
                    // width: 140
                  },
                  {
                    title: 'ID',
                    align: 'left',
                    colKey: 'video_id',
                    // width: 140
                  },
                  // { title: '时长', colKey: 'video_play_time', width: 120 },
                  // { title: '大小', colKey: 'video_size', width: 120 },
                  {
                    title: '创建人',
                    align: 'left',
                    colKey: 'create_user',
                    // width: 160
                  },
                  {
                    title: '创建时间',
                    colKey: 'create_time',
                    align: 'left',
                    // width: 140,
                    cell: ({ row }) => (
                      <>
                        {moment(+row.create_time * 1000).format(
                          'YYYY-MM-DD HH:mm:ss'
                        )}
                      </>
                    ),
                  },
                  {
                    title: '状态',
                    colKey: 'video_status',
                    align: 'left',
                    width: 100,
                    cell: ({ row }) => {
                      const statusText =
                        {
                          1: '未发布',
                          2: '未制作',
                          3: `制作中${Math.round(row.progress_percent * 100)}%`,
                          4: '制作失败',
                          5: '制作完成',
                        }[row.video_status] || '未知状态';
                      return (
                        <div
                          className={`status_tag video_status_${row.video_status}`}
                        >
                          {statusText}
                        </div>
                      );
                    },
                  },
                  {
                    title: '操作',
                    colKey: 'operation',
                    // width: 340,
                    cell: ({ row }) => (
                      <div className="operation_groups">
                        {row.video_status === '3' ? (
                          <Popconfirm
                            content="确认取消制作吗？"
                            theme="danger"
                            onConfirm={() => stopRelease(row.video_id)}
                          >
                            <div className="item">取消制作</div>
                          </Popconfirm>
                        ) : (
                          <div
                            className="item"
                            onClick={() => makeVideoFile(row.video_id)}
                          >
                            制作
                          </div>
                        )}
                        <Popup
                          showArrow
                          placement="top"
                          content={
                            row.video_status === '3'
                              ? '视频制作中暂不支持下载'
                              : '视频未制作完成'
                          }
                          disabled={
                            row.video_status !== '3' && row.video_url !== ''
                          }
                        >
                          <div
                            className={`item  ${
                              row.video_url === '' || row.video_status === '3'
                                ? 'disabled'
                                : ''
                            }`}
                            onClick={() =>
                              download(
                                row.video_status,
                                row.video_url,
                                row.video_name
                              )
                            }
                          >
                            下载
                          </div>
                        </Popup>
                        <div
                          className="item"
                          onClick={() => {
                            setPreviewVideoId(row.video_id);
                          }}
                        >
                          预览
                        </div>
                        <div
                          className="item"
                          onClick={() => {
                            const contentType = row.content_type.split('_')[0];
                            openEdit(row.video_id, contentType as ContentType);
                          }}
                        >
                          编辑
                        </div>
                        {/* <PopInput
                          title="复制并新建"
                          defaultValue={`${row.video_name}_复制`}
                          onConfirm={() => {
                            // TODO: 复制接口相关调用
                          }}
                        >
                          <div className="item">复制</div>
                        </PopInput> */}
                        <Popconfirm
                          content="复制并新建"
                          onConfirm={() => copyVideo(row.video_id)}
                        >
                          <div className="item">复制</div>
                        </Popconfirm>
                        <Popconfirm
                          content="确认删除该短视频吗？"
                          theme="danger"
                          onConfirm={() => deleteVideo(row.video_id)}
                        >
                          <div className="item danger">删除</div>
                        </Popconfirm>
                      </div>
                    ),
                  },
                ]}
              />
            </div>
            <Pagination
              className="pagination"
              showPageNumber
              showPageSize
              showPreviousAndNextBtn
              size="medium"
              total={pagination.total}
              totalContent
              current={pagination.current}
              pageSize={pagination.pageSize}
              onChange={(pageInfo) =>
                pagination.onChange(pageInfo.current, pageInfo.pageSize)
              }
            />
          </>
        )}
      </div>
      <Dialog
        destroyOnClose
        width="auto"
        placement="center"
        header="视频预览"
        visible={!!previewVideoId}
        onClose={() => {
          setPreviewVideoId('');
        }}
        footer={null}
      >
        <VideoViewer id={previewVideoId} />
      </Dialog>
    </>
  );
}
