import { useAsyncEffect, useRequest } from 'ahooks';
import { ResourceSvr } from '@/pb/pb';
import { mockUserJobVideoResultList } from '../api-mock';
import { useEffect, useState } from 'react';

export const useTaskDetail = ({
  manual,
  jobId,
}: {
  manual?: boolean;
  jobId?: string;
}) => {
  return useRequest(
    async (currentJobId?: string) => {
      if (!jobId && !currentJobId) return;
      const data = await ResourceSvr.GetJobVideoList({
        job_id: currentJobId || jobId,
      });
      return data.user_job_video_result_list;
    },
    {
      manual,
      debounceWait: 200,
      onError: (e) => {
        console.error('ERROR:', e);
      },
    }
  );
};

export const useTaskDetailDuration = () => {
  const { runAsync } = useTaskDetail({ manual: true });
  const [jobId, setJobId] = useState<string>(undefined);
  const [durationSum, setDurationSum] = useState(Infinity);

  useAsyncEffect(async () => {
    const data = await runAsync(jobId);

    if (data?.length) {
      const sum = data.reduce((prev, current) => {
        return prev + Number(current.video_duration);
      }, 0);
      setDurationSum(sum);
    }
  }, [jobId]);

  return {
    setJobId,
    durationSum,
  };
};
