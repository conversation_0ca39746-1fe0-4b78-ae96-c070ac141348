import { useRequest } from 'ahooks';
import { ResourceSvr } from '@/pb/pb';

export const useSaveVideo = () => {
  return useRequest(
    async (videoIds: string[], job_id) => {
      if (!videoIds.length) return;
      const data = await ResourceSvr.SaveJobVideoToUserVideo({
        user_job_video_result_list: videoIds.map((vid) => {
          return {
            video_id: vid,
            job_id,
          };
        }),
      });
      return data;
    },
    {
      manual: true,
      debounceWait: 200,
      onError: (e) => {
        console.error('ERROR:', e);
      },
    }
  );
};
