import { Loading } from '@/components/Loading';
import { VideoType } from '../../../videoMaterialList/type';
import { VideoRateComp } from '../VideoRateComp';
import { Button, MessagePlugin } from 'tdesign-react';
import { VideoMaterialType } from '@/pages/videoMaterialList/config';
import { DownloadIcon } from 'tdesign-icons-react';
import { useSaveVideo } from '../../hooks/useSaveVideo';
import { useMemoizedFn } from 'ahooks';
import { useTaskDetail } from '../../hooks/useTaskDetail';
import { useMemo } from 'react';

interface IProps {
  type: VideoType;
  taskId: string;
  name: string;
}

export function VideoRateList(props: IProps) {
  const { type, taskId, name } = props;
  // 使用获取任务执行结果的 Hook
  const {
    loading: loadingVideos,
    data: videos,
    refreshAsync,
  } = useTaskDetail({
    manual: false,
    jobId: taskId,
  });

  const { runAsync: saveVideo, loading: saving } = useSaveVideo();

  const canSaveVideoIds = useMemo(() => {
    return videos?.filter((video) => video.is_have_save_user_video !== 1);
  }, [videos]);

  const handleSaveAllVideos = useMemoizedFn(async () => {
    if (!canSaveVideoIds?.length) return;
    await saveVideo(
      canSaveVideoIds.map((item) => item.video_id),
      taskId
    );
    await refreshAsync();
    MessagePlugin.success(`保存视频成功`);
  });

  return (
    <>
      <div
        className="flex justify-between items-center h-[40px] mt-[10px] text-[#000000E5] mb-[8px]"
        x-if={type !== VideoMaterialType.USER_UPLOAD}
      >
        <div>任务名称：{name}</div>
        <Button
          className="gradient-default h-[40px]"
          onClick={handleSaveAllVideos}
          x-if={
            type === VideoMaterialType.HIGHLIGHT_CLIP ||
            type === VideoMaterialType.HIGHLIGHTS
          }
          icon={<DownloadIcon color="black" />}
          loading={saving}
          theme="default"
          disabled={!canSaveVideoIds?.length}
        >
          {!canSaveVideoIds?.length ? '已全部保存' : '保存所有视频至管理页'}
        </Button>
      </div>
      <div className="flex gap-[12px] flex-wrap overflow-auto pb-[80px]">
        <div
          className="w-full h-full flex justify-center items-center"
          x-if={loadingVideos}
        >
          <Loading />
        </div>
        {videos?.map((video) => {
          return <VideoRateComp video={video} />;
        })}
      </div>
    </>
  );
}
