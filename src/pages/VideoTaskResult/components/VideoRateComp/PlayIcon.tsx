export function PlayIcon() {
  return (
    <div className="inline-flex">
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M52.5 28C52.5 14.469 41.531 3.5 28 3.5C14.469 3.49999 3.5 14.469 3.5 28C3.49999 41.531 14.469 52.5 28 52.5C41.531 52.5 52.5 41.531 52.5 28ZM38.4454 28.7977L22.7863 37.8385C22.1722 38.193 21.4046 37.7499 21.4046 37.0408L21.4046 18.9592C21.4046 18.2501 22.1722 17.8069 22.7863 18.1615L38.4454 27.2022C39.0595 27.5568 39.0595 28.4431 38.4454 28.7977Z"
          fill="url(#paint0_linear_11891_71254)"
        />
        <path
          d="M52.5 28C52.5 14.469 41.531 3.5 28 3.5C14.469 3.49999 3.5 14.469 3.5 28C3.49999 41.531 14.469 52.5 28 52.5C41.531 52.5 52.5 41.531 52.5 28ZM38.4454 28.7977L22.7863 37.8385C22.1722 38.193 21.4046 37.7499 21.4046 37.0408L21.4046 18.9592C21.4046 18.2501 22.1722 17.8069 22.7863 18.1615L38.4454 27.2022C39.0595 27.5568 39.0595 28.4431 38.4454 28.7977Z"
          fill="white"
          fillOpacity="0.4"
        />
        <defs>
          <linearGradient
            id="paint0_linear_11891_71254"
            x1="3.49105"
            y1="52.5"
            x2="57.9621"
            y2="45.385"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#F6F7FB" />
            <stop offset="1" stopColor="#FBF8FB" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
}
