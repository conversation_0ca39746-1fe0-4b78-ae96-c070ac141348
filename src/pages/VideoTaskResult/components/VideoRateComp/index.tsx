import { Button, DialogPlugin, MessagePlugin, Rate } from 'tdesign-react';
import { VideoInfoType } from '../../api-mock';
import { PlayIcon } from './PlayIcon';
import { DownloadIcon } from 'tdesign-icons-react';
import { useMemoizedFn } from 'ahooks';
import BaseVideo from '@/pages/videoMaterialList/components/previewVideo/video';
import { useSaveVideo } from '../../hooks/useSaveVideo';
import { formatNumber } from '@/utils/number';
import { useVideoInfo } from '@/hooks/useVideoInfo';
import { Fragment } from 'react';
import { Loading } from '@/components/Loading';
import { handleDuration } from '@/pages/videoMaterialList/utils';

interface IProps {
  video: VideoInfoType;
  // refresh: () => Promise<any>;
}

const getButtonText = (saving: boolean, isSaved: boolean) => {
  if (saving) return '保存中...';
  if (isSaved) return '已保存';
  return '保存到素材库';
};

export function VideoRateComp(props: IProps) {
  const { video } = props;
  const { videoInfo, loading } = useVideoInfo({
    videoUrl: video.video_url,
    options: {
      fetchFirstFrame: true,
    },
  });

  // 使用保存单个视频的 Hook
  const {
    runAsync: saveVideo,
    loading: savingSingleVideo,
    data: saveResult,
  } = useSaveVideo();

  const saveSingleVideo = useMemoizedFn(async () => {
    await saveVideo([video.video_id], video.job_id);
    // await refresh();
    MessagePlugin.success(`保存视频成功`);
  });

  const openDialog = useMemoizedFn(() => {
    const dialog = DialogPlugin.confirm({
      body: <BaseVideo src={video.video_url} poster={videoInfo?.url} />,
      className: 'previewVideo-dialog',
      onClose: () => {
        dialog.destroy();
      },
      footer: null,
      closeOnOverlayClick: false,
      placement: 'center',
      width: 860,
      header: '查看视频',
    });
  });

  return (
    <div
      key={video.video_id}
      className="border overflow-hidden relative w-[280px] rounded-[8px] bg-[linear-gradient(85deg,#EBF4FF_0%,#F8F8FF_100%)]"
    >
      <div
        className="w-full h-[360px] flex items-center justify-center bg-black group"
        onClick={openDialog}
      >
        {loading ? (
          <div className="absolute h-[360px] inset-0 bg-black bg-opacity-50 flex flex-col justify-center items-center">
            <Loading />
            <span className="text-[#fff]">正在获取视频...</span>
          </div>
        ) : (
          <>
            <img
              src={videoInfo?.url}
              alt={video.video_name}
              className={`${
                videoInfo?.info.direction === 'horizontal' ? 'w-full' : 'h-full'
              } object-contain`}
            />
            <div className="absolute inset-0 h-[360px] bg-black bg-opacity-60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
              <PlayIcon />
            </div>
          </>
        )}
      </div>
      <div className="p-[16px] text-[14px] h-[182px] text-[rgba(0,0,0,0.4)] flex flex-col justify-between gap-[8px]">
        <p className="text-[14px] line-clamp-2 text-[rgba(0,0,0,0.6)]">
          {video.video_content}
        </p>
        <div className="flex justify-between">
          <span className="text-[14px] ">评分</span>
          <div className="flex gap-[16px]">
            <Rate disabled allowHalf value={video.score / 20} size="16px" />
            <div className="font-bold text-[rgba(0,0,0,0.9)]">
              {formatNumber(video.score / 20)}
            </div>
          </div>
        </div>
        <div className="flex justify-between">
          <div>
            时长
            <Fragment x-if={video?.video_size}>
              <span className="text-[rgba(0,0,0,0.1)] px-[6px]">|</span>大小
            </Fragment>
          </div>
          <div className="text-[rgba(0,0,0,0.9)]">
            {handleDuration(videoInfo?.info.duration || 0)}
            <Fragment x-if={video?.video_size}>
              <span className="text-[rgba(0,0,0,0.1)] px-[6px]">|</span>
              {video?.video_size}
            </Fragment>{' '}
          </div>
        </div>
        <Button
          theme="default"
          variant="outline"
          onClick={() => saveSingleVideo()}
          disabled={
            !!saveResult ||
            video.is_have_save_user_video === 1 ||
            savingSingleVideo
          }
          className="gradient-default h-[40px] "
          style={{ background: 'white' }}
          icon={<DownloadIcon color="black" />}
        >
          {getButtonText(
            savingSingleVideo,
            !!saveResult || video.is_have_save_user_video === 1
          )}
        </Button>
      </div>
    </div>
  );
}
