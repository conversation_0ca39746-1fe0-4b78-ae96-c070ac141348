import { RespType } from '@/pb/config';
import { ResourceSvr } from '@/pb/pb';
import { MessagePlugin } from 'tdesign-react';

// 定义视频信息的接口类型
export type VideoInfoType = RespType<
  typeof ResourceSvr.GetJobVideoList
>['user_job_video_result_list'][number];

interface UserJobVideoResult {
  job_id: string;
  video_id: string;
  video_index_id: number;
  status: number;
  video_name: string;
  video_content: string;
  score: number;
  video_url: string;
  video_cos_path: string;
  create_time: string;
  update_time: string;
  is_have_save_user_video: number;
}

export const mockUserJobVideoResultList: UserJobVideoResult[] = [
  {
    job_id: 'job_1',
    video_id: 'video_1',
    video_index_id: 1,
    status: 1,
    video_name: 'Sample Video 1',
    video_content: 'This is a sample video description 1.',
    score: 90,
    video_url:
      '//dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
    video_cos_path: '/cos/path/video1.mp4',
    create_time: '2023-01-01T00:00:00Z',
    update_time: '2023-01-02T00:00:00Z',
    is_have_save_user_video: 1,
  },
  {
    job_id: 'job_2',
    video_id: 'video_2',
    video_index_id: 2,
    status: 2,
    video_name: 'Sample Video 2',
    video_content: 'This is a sample video description 2.',
    score: 85,
    video_url:
      '//dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
    video_cos_path: '/cos/path/video2.mp4',
    create_time: '2023-02-01T00:00:00Z',
    update_time: '2023-02-02T00:00:00Z',
    is_have_save_user_video: 2,
  },
  {
    job_id: 'job_3',
    video_id: 'video_3',
    video_index_id: 3,
    status: -1,
    video_name: 'Sample Video 3',
    video_content: 'This is a sample video description 3.',
    score: 95,
    video_url:
      '//dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
    video_cos_path: '/cos/path/video3.mp4',
    create_time: '2023-03-01T00:00:00Z',
    update_time: '2023-03-02T00:00:00Z',
    is_have_save_user_video: 0,
  },
  {
    job_id: 'job_4',
    video_id: 'video_4',
    video_index_id: 4,
    status: 1,
    video_name: 'Sample Video 4',
    video_content: 'This is a sample video description 4.',
    score: 88,
    video_url:
      '//dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
    video_cos_path: '/cos/path/video4.mp4',
    create_time: '2023-04-01T00:00:00Z',
    update_time: '2023-04-02T00:00:00Z',
    is_have_save_user_video: 1,
  },
  {
    job_id: 'job_5',
    video_id: 'video_5',
    video_index_id: 5,
    status: 2,
    video_name: 'Sample Video 5',
    video_content: 'This is a sample video description 5.',
    score: 92,
    video_url:
      '//dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
    video_cos_path: '/cos/path/video5.mp4',
    create_time: '2023-05-01T00:00:00Z',
    update_time: '2023-05-02T00:00:00Z',
    is_have_save_user_video: 2,
  },
];

console.log(mockUserJobVideoResultList);
