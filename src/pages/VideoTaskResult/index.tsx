import React, { useMemo } from 'react';
import { MainContent, Page } from '@/components/Layout';
import { useNavigate } from 'react-router-dom';
import { Button, Breadcrumb } from 'tdesign-react';
import { useMemoizedFn } from 'ahooks';

import { VideoType2Label } from '../videoMaterialList/config';
import { VideoType } from '../videoMaterialList/type';
import { VideoRateList } from './components/VideoRateList';
import { getParams } from '@/utils/url';
const { BreadcrumbItem } = Breadcrumb;

export default function VideoTaskResult() {
  const navigate = useNavigate();
  const { name, type, vid } = getParams();

  // request 请求详情
  const backToList = useMemoizedFn((isOpen = true) => {
    navigate(`/videoMaterial-list?tab-type=${type}`, {
      state: {
        isOpen,
      },
    });
  });

  const type2text = useMemo(() => {
    return VideoType2Label[type as VideoType];
  }, [type]);

  return (
    <>
      <Page
        breadCrumb={
          <div className="text-[14px] flex items-center ">
            <BreadcrumbItem
              className="text-[#00000066]"
              onClick={() => backToList(false)}
            >
              视频
            </BreadcrumbItem>
            <BreadcrumbItem className="text-[black] font-bold">
              {name}
            </BreadcrumbItem>
          </div>
        }
      >
        <MainContent className="relative h-full">
          <div className="flex flex-col min-h-full max-h-full">
            <h2 className="text-[#000] text-[16px] font-medium leading-[22px]">
              {`智能生成${type2text}如下：`}
            </h2>
            <VideoRateList type={type as VideoType} taskId={vid} name={name} />
          </div>
          <div className="fixed bottom-0 left-[233px] w-full h-[80px] bg-[#fff] py-[25px] border-t-[1px] border-[#E7E7E7] border-solid">
            <Button
              className="gradient-default w-[150px] h-[40px] ml-[34px]"
              theme="default"
              onClick={() => backToList(true)}
            >
              返回
            </Button>
          </div>
        </MainContent>
      </Page>
    </>
  );
}
