import { css } from '@emotion/react';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { SearchIcon } from 'tdesign-icons-react';
import { Button, Input } from 'tdesign-react';
import { useVoiceQuota } from '@/pages/VoiceList/hooks/useVoiceQuota';

interface IVoiceAddActionBarProps {
  inputValue: string;
  onInputChange: (value: string) => void;
  onEnter?: (value: string) => void;
}

export default function VoiceAddActionBar(props: IVoiceAddActionBarProps) {
  const { inputValue, onInputChange, onEnter } = props;
  const { effectiveQuota, quota } = useVoiceQuota();
  const navigate = useNavigate();

  return (
    <div
      className="voice-add-action-bar"
      css={css`
        width: 100%;
        padding: 8px;
        display: flex;
        align-items: center;
      `}
    >
      <div className="flex-1">
        <Button
          disabled={!effectiveQuota}
          theme="primary"
          className="gradient-primary"
          onClick={() => {
            navigate('/voice-list/add');
          }}
        >
          新建声音定制
        </Button>

        <Button
          theme="default"
          className="gradient-default cursor-default ml-8"
          style={{ borderRadius: '0 20px 20px 0' }}
        >
          剩余额度：
          <span
            css={css`
              font-weight: 600;
              background: linear-gradient(
                89deg,
                #0153ff -0.01%,
                #8649ff 147.74%
              );
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            `}
          >
            {quota.remain_personal_quota}
          </span>
        </Button>
      </div>

      <div style={{ width: '364px' }}>
        <Input
          value={inputValue}
          placeholder="请输入你需要搜索的内容"
          onChange={(value) => {
            onInputChange(value);
          }}
          onEnter={(value) => {
            onEnter?.(value);
          }}
          suffixIcon={<SearchIcon />}
        />
      </div>
    </div>
  );
}
