import React, { useMemo, useState } from 'react';
import { Button, Image } from 'tdesign-react';
import './voice.less';
import { CommonCategoryWrapper } from '@/components/Category';
import { divide } from 'lodash-es';
import { FlattenSelect } from '@/components/FlattenSelect';
import { CategoryHandlerType } from '@/components/Category/typings';
import { ResourceCategoryButton } from '@/pages/VirtualImage/CategoryButton';
import { useAscription } from '@/pages/VirtualImage/hooks/useAscription';
import { css } from '@emotion/react';
import VoiceLibraryList from './VoiceLibraryList';
import { useVoiceLibraryQuery } from './hooks/useVoiceLibraryRequest';
import { useDeepCompareEffect } from 'ahooks';
import VoiceAddActionBar from '../VoiceAddActionBar';
export default function VoiceLibrary() {
  const { choices, currentValue, setCurrentValue } = useAscription(
    useMemo(
      () => ({
        choices: [
          {
            value: 'private',
            name: '我的',
          },
          {
            value: 'public',
            name: '公共',
          },
        ],
      }),
      []
    )
  );
  const [searchVal, setSearchVal] = useState<string>('');

  return (
    <div className="category">
      <VoiceAddActionBar
        inputValue={searchVal}
        onInputChange={(value) => {
          setSearchVal(value);
        }}
      />
      <div
        className="flex flex-col h-full"
        css={css`
          overflow-x: hidden;
          overflow-y: auto;
        `}
      >
        <CommonCategoryWrapper
          categoryType="voice"
          renderCategoryList={({ categoryList }) => {
            const second = categoryList[1];
            const first = categoryList[0];
            const labelStyle: React.CSSProperties = {
              width: 'auto',
              color: 'rgba(0, 0, 0, 0.6)',
            };
            return (
              <div
                css={css`
                  display: flex;
                  flex-direction: column;
                  gap: 20px;
                `}
              >
                <FlattenSelect<string>
                  title="归属"
                  site="left"
                  labelStyle={labelStyle}
                  value={currentValue}
                  options={choices.map((choice) => ({
                    key: choice.value,
                    label: choice.name,
                    value: choice.value,
                    render: ({ active, value, onSelect, label }) => {
                      return (
                        <ResourceCategoryButton
                          active={active}
                          onClick={() => onSelect(value)}
                        >
                          {label}
                        </ResourceCategoryButton>
                      );
                    },
                  }))}
                  onChange={async (value) => setCurrentValue(value)}
                />
                <FlattenSelect<CategoryHandlerType | undefined>
                  value={first.value}
                  title="性别"
                  site="left"
                  labelStyle={labelStyle}
                  // style={{
                  //   marginTop: '20px',
                  // }}
                  options={first.data.map((item) => ({
                    key: item.id,
                    label: item.name,
                    value: item,
                    render: ({ active, onSelect, label, value }) => {
                      return (
                        <ResourceCategoryButton
                          active={active}
                          onClick={() => onSelect(value)}
                        >
                          {label}
                        </ResourceCategoryButton>
                      );
                    },
                  }))}
                  onChange={async (value) => {
                    value?.onSelect();
                  }}
                />
                {second && (
                  <FlattenSelect<CategoryHandlerType | undefined>
                    value={second.value}
                    title="特征"
                    site="left"
                    labelStyle={labelStyle}
                    // style={{
                    //   marginTop: '20px',
                    // }}
                    options={second.data.map((item) => ({
                      key: item.id,
                      label: item.name,
                      value: item,
                      render: ({ active, onSelect, label, value }) => {
                        return (
                          <ResourceCategoryButton
                            active={active}
                            onClick={() => onSelect(value)}
                          >
                            {label}
                          </ResourceCategoryButton>
                        );
                      },
                    }))}
                    onChange={async (value) => {
                      value?.onSelect();
                    }}
                  />
                )}
                {/* <div
                css={css`
                  display: flex;
                  gap: 8px;
                `}
              >
                {first.data.map((item) => {
                  return (
                    <ResourceCategoryButton active={first.value === item}>
                      {item.name}
                    </ResourceCategoryButton>
                  );
                })}
              </div> */}
              </div>
            );
          }}
          defaultCategory={['all', 'all']}
        >
          {({ categories, categoryType }) => {
            console.log('categories', categories, categoryType);
            return (
              <VoiceLibraryList
                categories={[currentValue, ...categories]}
                filter={{ text: searchVal }}
              />
            );
          }}
        </CommonCategoryWrapper>
      </div>
    </div>
  );
}
