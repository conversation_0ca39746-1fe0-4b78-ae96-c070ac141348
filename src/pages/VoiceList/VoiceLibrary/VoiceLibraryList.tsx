import { AssetsLibConfigContext } from '@/configs/config_context';
import { useCallback, useContext, useEffect, useState } from 'react';
import VoiceItem from './VoiceItem';
import { IVoiceItem } from './type';
import { useDeepCompareEffect } from 'ahooks';
import { useVoiceLibraryQuery } from './hooks/useVoiceLibraryRequest';

export interface IVoiceLibraryListProps {
  categories: string[];
  filter: {
    text: string;
  };
}
export default function VoiceLibraryList(props: IVoiceLibraryListProps) {
  const {
    categories: [auth_level, primary, secondary],
    filter: { text },
  } = props;
  const { voiceList, queryVoiceList, isGettingList, refresh } =
    useVoiceLibraryQuery();
  useDeepCompareEffect(() => {
    queryVoiceList({ primary, secondary, auth_level });
  }, [primary, secondary, auth_level]);
  useEffect(() => {
    refresh(text);
  }, [text, refresh]);
  const [selectVoice, setSelectVoice] = useState<IVoiceItem>();
  const [candidateVoice, setCandidateVoice] = useState<IVoiceItem>();
  const configCtx = useContext(AssetsLibConfigContext);
  const changeSelectVoice = useCallback((item: IVoiceItem) => {
    setSelectVoice(item);
  }, []);
  return (
    <div
      className="category-list"
      onClick={() => {
        setSelectVoice(undefined);
      }}
    >
      {voiceList.map((item) => {
        const key = `${item.appCode}_${item.platform}_${item.driverMode}_${item.userId}_${item.voiceId}_${item.voiceName}`;
        return (
          <VoiceItem
            voice={item}
            key={key}
            isActive={selectVoice?.voiceId === item.voiceId || false}
            onCancel={() => {
              setSelectVoice(undefined);
            }}
            onClick={changeSelectVoice}
            onCandidate={(item: IVoiceItem) => {
              setCandidateVoice(item);
            }}
          />
        );
      })}
      {configCtx.renderUseVoice?.({
        voiceItem: candidateVoice,
        destroy: () => {
          setCandidateVoice(undefined);
        },
      })}
    </div>
  );
}
