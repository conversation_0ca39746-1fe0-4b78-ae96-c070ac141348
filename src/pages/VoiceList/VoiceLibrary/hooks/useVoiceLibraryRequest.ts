import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IVoiceItem } from '../type';
import { ResourceSvr } from '@/pb/pb';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { RespError } from '@/pb/config';
import { useLockFn } from 'ahooks';
import { gainAudioUrl } from '../../voice/gainAudio';
import { MessagePlugin } from 'tdesign-react';
interface ILibraryItem {
  title: string;
}
export const useVoiceLibraryQuery = () => {
  const [voiceList, setVoiceList] = useState<IVoiceItem[]>([]);
  const originVoiceList = useRef<IVoiceItem[]>();
  const [isGettingList, setIsGetList] = useState(false);

  const [errorState, setErrorState] = useState({
    isError: false,
    errMsg: '',
  });

  const queryVoiceList = useCallback(
    async (props: {
      primary: string;
      secondary: string;
      auth_level: string;
    }) => {
      const { primary, secondary, auth_level } = props;
      try {
        setIsGetList(true);
        setErrorState({
          isError: false,
          errMsg: '',
        });
        const resp = await ResourceSvr.GetVoiceResourceList({
          user_id: '',
          app_code: MatchedGlobalConfigItem.appcode,
          category_level1: primary,
          category_level2: secondary,
          auth_level,
        });
        // more语速
        const list: IVoiceItem[] = resp.voice_info_list.map((item) => ({
          platform: item.platform as any,
          speed: 1,
          voiceDesc: item.voice_description,
          voiceExtendConfig: item.voice_extend_config,
          voiceId: item.voice_id,
          voiceImage: item.voice_image,
          voiceName: item.voice_name,
          voiceTestText: item.voice_text,
          driverMode: (item.drive_mode || 'voice') as IVoiceItem['driverMode'],
          voiceAuthority: item.voice_authority,
          appCode: item.app_code,
          userId: item.user_id,
        }));
        setVoiceList(list);
        originVoiceList.current = list;
        return list;
      } catch (e) {
        setErrorState({
          isError: true,
          errMsg: (e as RespError).message,
        });
      } finally {
        setIsGetList(false);
      }
      return [];
    },
    []
  );

  const setVoiceSpeed = useCallback((idx: number, speed: number) => {
    setVoiceList((prev) => [
      ...prev.slice(0, idx),
      {
        ...prev[idx],
        speed,
      },
      ...prev.slice(idx + 1),
    ]);
  }, []);

  const refresh = useCallback(async (searchParams: string) => {
    if (!originVoiceList.current) return;
    const filterList = originVoiceList.current.filter(
      (voice) =>
        voice.voiceDesc.includes(searchParams) ||
        voice.voiceName.includes(searchParams)
    );
    setVoiceList(filterList);
  }, []);
  return {
    isGettingList,
    gettingVoiceListErrorState: errorState,
    voiceList,
    queryVoiceList,
    setVoiceSpeed,
    refresh,
  };
};

export const useVoiceStreamPlay = () => {
  // audio 的 ref
  const player = useRef<HTMLAudioElement | null>(null);
  // 中断 ref
  const abortControllerRef = useRef<AbortController | null>();
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 加载音频error
  const [errMsg, setErrMsg] = useState<{ desc: string; err: unknown }>();
  // 播放状态
  const [playState, setPlayState] = useState<'pause' | 'playing' | 'undo'>(
    'undo'
  );
  // 缓存audio url
  const cacheUrl = useRef<string>('');

  // 销毁
  const destroy = useCallback(async () => {
    player.current?.pause();
    player.current = null;
    setPlayState('undo');
  }, []);

  // 给audio绑定必要事件
  const bindStateChange = useCallback(
    (audio: HTMLAudioElement) => {
      // 播放时修改state
      audio.addEventListener('play', () => {
        setPlayState('playing');
      });
      // 音频播放结束后，销毁audio
      audio.addEventListener('ended', () => {
        destroy();
      });
    },
    [destroy]
  );
  // 播放
  const play = useLockFn(async (data: IVoiceItem): Promise<boolean> => {
    if (player.current) {
      // 继续播放
      player.current.play();
      return true;
    }
    // 用于中断音频获取请求
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;
    let audioUrl: string;
    try {
      setLoading(true);
      if (!cacheUrl.current) {
        // 超过10s未获取到，中断
        const timer = setTimeout(() => {
          abortControllerRef.current?.abort();
        }, 10000);
        audioUrl = (await gainAudioUrl(data, { signal })) || '';
        cacheUrl.current = audioUrl;
        // 成功获取到，清除定时器
        clearTimeout(timer);
        if (signal.aborted) return false;
        player.current = new Audio(audioUrl);
      } else {
        player.current = new Audio(cacheUrl.current);
      }
      return true;
    } catch (err) {
      if (!abortControllerRef.current.signal.aborted) {
        // 中断了，说明是超时引起
        setErrMsg({
          desc: '获取音频失败',
          err,
        });
      } else {
        setErrMsg({
          desc: '获取音频超时',
          err,
        });
      }
      console.log('获取音频失败', err);
      return false;
    } finally {
      if (player.current) {
        // 开始播放
        player.current.play();
        // 播放时修改state
        bindStateChange(player.current);
      }
      setLoading(false);
    }
  });

  // 暂停
  const pause = useCallback(async () => {
    player.current?.pause();
    setPlayState('pause');
  }, []);

  // 获取音频出错后，销毁还原state
  useEffect(() => {
    if (errMsg?.err) {
      MessagePlugin.error(errMsg.desc);
      destroy();
    }
  }, [errMsg, destroy]);

  return {
    play,
    destroy,
    playState,
    pause,
    loading,
    errMsg,
  };
};
