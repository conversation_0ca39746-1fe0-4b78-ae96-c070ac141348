import React, { useCallback, useContext, useEffect, useState } from 'react';
import { IVoiceItem } from './type';
import { Button, Loading, MessagePlugin } from 'tdesign-react';
import { useVoiceStreamPlay } from './hooks/useVoiceLibraryRequest';
import { AssetsLibConfigContext } from '@/configs/config_context';

export interface IVoiceItemProps {
  voice: IVoiceItem;
  isActive: boolean;
  onClick: (item: IVoiceItem) => void;
  onCandidate: (item: IVoiceItem) => void;
  onCancel: (item: IVoiceItem) => void;
}
const TextMap = {
  undo: '试听',
  playing: '试听中',
  pause: '停止试听',
};
export default function VoiceItem(props: IVoiceItemProps) {
  const { voice, isActive } = props;
  // 获取播放器
  const { play, playState, pause, destroy, errMsg, loading } =
    useVoiceStreamPlay();
  const configCtx = useContext(AssetsLibConfigContext);
  // 点击icon切换playState状态，控制暂停与否
  const handleClick = useCallback(async () => {
    if (playState === 'undo' || playState === 'pause') {
      play(voice);
      props.onClick(voice);
    } else {
      pause();
    }
  }, [play, props, pause, playState, voice]);

  // 选择其他音频，销毁逻辑
  useEffect(() => {
    if (!isActive) {
      destroy();
    }
  }, [destroy, isActive]);

  return (
    <div
      className={`category-list-item ${
        isActive ? 'category-list-item-active' : ''
      }`}
      onClick={(event) => {
        event.stopPropagation();
        props.onCancel(voice);
      }}
    >
      <div
        className="category-list-item-mine"
        style={{ display: voice.voiceAuthority ? 'block' : 'none' }}
      >
        我的
      </div>
      <svg
        width="32"
        height="32"
        viewBox="0 0 32 32"
        fill="none"
        className="item-icon"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.6429 12.8586V26.6795C10.6424 29.0657 8.70782 31 6.32147 31C3.93479 31 2 29.0652 2 26.6785C2 24.2919 3.93479 22.3571 6.32147 22.3571C7.10859 22.3571 7.84657 22.5675 8.4822 22.9352V5.92732C8.4822 4.27433 9.72616 2.88618 11.3693 2.70562L26.7105 1.01977C28.6291 0.808933 30.3056 2.31131 30.3056 4.24148V24.518C30.3056 24.5421 30.3048 24.5661 30.3032 24.5899C30.3048 24.6377 30.3056 24.6857 30.3056 24.7339C30.3056 27.1206 28.3708 29.0553 25.9841 29.0553C23.5975 29.0553 21.6627 27.1206 21.6627 24.7339C21.6627 22.3472 23.5975 20.4124 25.9841 20.4124C26.7713 20.4124 27.5092 20.6229 28.1449 20.9905V10.9353L10.6429 12.8586ZM26.9465 3.16757L11.6053 4.85342C11.0576 4.91361 10.6429 5.37632 10.6429 5.92732V10.6848L28.1449 8.76154V4.24148C28.1449 3.59809 27.586 3.09729 26.9465 3.16757ZM8.4822 26.6785C8.4822 25.4852 7.5148 24.5178 6.32147 24.5178C5.12813 24.5178 4.16073 25.4852 4.16073 26.6785C4.16073 27.8719 5.12813 28.8393 6.32147 28.8393C7.5148 28.8393 8.4822 27.8719 8.4822 26.6785ZM25.9841 22.5731C24.7908 22.5731 23.8234 23.5405 23.8234 24.7339C23.8234 25.9272 24.7908 26.8946 25.9841 26.8946C27.1775 26.8946 28.1449 25.9272 28.1449 24.7339C28.1449 23.5405 27.1775 22.5731 25.9841 22.5731Z"
          fill="white"
          fillOpacity="0.9"
        />
      </svg>

      <div className="bottom">
        <span className="bottom-text">{voice.voiceName}</span>
      </div>
      <div className="float-mask">
        {loading ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '80px',
              width: '100%',
            }}
          >
            <Loading style={{ color: '#ffffff' }} />
          </div>
        ) : (
          <div
            className="mask-icon"
            onClick={(event) => {
              event.stopPropagation();
              handleClick();
            }}
          >
            {playState === 'undo' && (
              <svg
                width="33"
                height="32"
                viewBox="0 0 33 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M30.5723 16C30.5723 8.26802 24.3043 2 16.5723 2C8.84028 2 2.57227 8.26801 2.57227 16C2.57226 23.732 8.84028 30 16.5723 30C24.3042 30 30.5723 23.732 30.5723 16ZM22.5411 16.4558L13.593 21.622C13.2421 21.8246 12.8035 21.5713 12.8035 21.1662L12.8035 10.8338C12.8035 10.4286 13.2421 10.1754 13.593 10.378L22.5411 15.5441C22.892 15.7467 22.892 16.2532 22.5411 16.4558Z"
                  fill="url(#paint0_linear_7877_23461)"
                />
                <path
                  d="M30.5723 16C30.5723 8.26802 24.3043 2 16.5723 2C8.84028 2 2.57227 8.26801 2.57227 16C2.57226 23.732 8.84028 30 16.5723 30C24.3042 30 30.5723 23.732 30.5723 16ZM22.5411 16.4558L13.593 21.622C13.2421 21.8246 12.8035 21.5713 12.8035 21.1662L12.8035 10.8338C12.8035 10.4286 13.2421 10.1754 13.593 10.378L22.5411 15.5441C22.892 15.7467 22.892 16.2532 22.5411 16.4558Z"
                  fill="white"
                  fillOpacity="0.4"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_7877_23461"
                    x1="2.56715"
                    y1="30"
                    x2="33.6935"
                    y2="25.9343"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#F6F7FB" />
                    <stop offset="1" stopColor="#FBF8FB" />
                  </linearGradient>
                </defs>
              </svg>
            )}
            {playState === 'pause' && (
              <svg
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M30 16C30 23.732 23.732 30 16 30C8.26801 30 2 23.732 2 16C2 8.26801 8.26801 2 16 2C23.732 2 30 8.26801 30 16ZM12 10V22H14V10H12ZM20 10H18V22H20V10Z"
                  fill="white"
                  fillOpacity="0.9"
                />
              </svg>
            )}
            {playState === 'playing' && (
              <svg
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="16"
                  cy="16"
                  r="14"
                  fill="url(#paint0_linear_7877_23421)"
                />
                <rect x="9" y="9" width="2" height="10" rx="1" fill="white" />
                <rect x="13" y="14" width="2" height="10" rx="1" fill="white" />
                <rect x="17" y="9" width="2" height="10" rx="1" fill="white" />
                <rect x="21" y="14" width="2" height="10" rx="1" fill="white" />
                <defs>
                  <linearGradient
                    id="paint0_linear_7877_23421"
                    x1="2"
                    y1="30"
                    x2="31.0514"
                    y2="28.8017"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#E0EAFF" />
                    <stop offset="0.470486" stopColor="#E2EFFF" />
                    <stop offset="1" stopColor="#F5F3FF" />
                  </linearGradient>
                </defs>
              </svg>
            )}
            <span
              style={{ marginTop: '3px', fontSize: '14px', color: '#ffffff' }}
            >
              {TextMap[playState]}
            </span>
          </div>
        )}
        {!configCtx.readonlyLibList && (
          <Button
            className="float-mask-button"
            onClick={(event) => {
              event.stopPropagation();
              props.onCandidate(voice);
            }}
          >
            使用
          </Button>
        )}
      </div>
    </div>
  );
}
