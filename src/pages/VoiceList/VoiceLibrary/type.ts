export type ConfigVoiceItem = {
  // 平台
  platform: 'tencent' | 'azure' | 'custom_tts' | 'tencent_man' | 'ad_chattts';
  // 音色id
  voiceId: string;
  // 语速
  speed: number;
  // 平台特定扩展配置
  voiceExtendConfig: string;

  // 驱动模式，音频或文本
  driverMode?: 'voice' | 'text';
};

export interface IVoiceItem extends ConfigVoiceItem {
  voiceName: string;

  voiceDesc: string;

  speed: number;

  voiceImage: string;

  voiceTestText: string;

  voiceAuthority: number;

  appCode: string;

  userId: string;
}
