@import '../../../components/Style/mixins/_scrollbar.less';

.category{
    display: flex;
    flex-direction: column;
    height: 100%;

    .voice-add-action-bar {
        padding: 8px 0 20px 0;
    }

    &-level{
        display: flex;
        align-items: center;
        .first-level{
            &-text{
                color:#00000099;
                font-size: 14px;
            }
        }
        .sub-level{
            display: flex;
            align-items: center;
            gap: 8px;
            .sub-level-item{
                width: 68px;
                height: 24px;
                border-radius: 4px;
                background: linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 100%);
                box-sizing: border-box;
                padding:4px 20px 4px 20px;
                &-text{
                    color: #00000099;
                    font-size: 14px;
                }
            }
            .sub-level-item-active{
                &-text{
                    color:#0047F9;
                }
            }
        }
    }
    &-list{
        height: 0;
        flex:1;
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        overflow-y: auto;
        gap: 16px;
        .scrollbar();

        &-item{
            width: 156px;
            height: 118px;
            position: relative;
            background: linear-gradient(88.08deg, rgba(1, 83, 255, 0.5) -0.01%, rgba(46, 127, 253, 0.5) 49.89%, rgba(193, 163, 253, 0.5) 99.99%),
linear-gradient(0deg, rgba(255, 255, 255, 0.24), rgba(255, 255, 255, 0.24));
            border-radius: 8px;
            overflow: hidden;
            &-mine{
                position: absolute;
                width: 46px;
                height: 20px;
                border-radius: 8px 0 8px 0;
                line-height: 20px;
                text-align: center;
                color: #FFFFFF;
                background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%);
            }
            .item-icon{
                position:absolute;
                top:25px;
                left: 64px;
            }
            .bottom{
                position: absolute;
                bottom: 0;
                height: 38px;
                width: 100%;
                text-align: center;
                background: linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
                .bottom-text{
                    line-height: 38px;
                }
            }
            &:hover{
                .float-mask{
                    opacity: 1;;
                }
            }

            .float-mask{
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                background: #3232329E;
                opacity: 0;
                transition: opacity 0.1s linear;
                .mask-icon{
                    position: absolute;
                    top:11px;
                    left:50%;
                    transform: translateX(-50%);
                    cursor: pointer;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    user-select: none;
                }
                &-button{
                    background: linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
                    position: absolute;
                    top: 76px;
                    left:20px;
                    width: 116px;
                    height: 32px;
                    border-radius: 4px;
                    border: none;
                }
            }
        }
        &-item-active {
            .float-mask {
                opacity: 1;
            }
        }
    }
}
