/**
 * <AUTHOR>
 * @date 2024/6/28 15:24
 * @desc useVoiceListRequest
 */

import { RespType } from '@/pb/config';
import { ShuzirenImageMngSvr } from '@/pb/pb';
import { useRequest } from 'ahooks';
import { useCallback, useEffect, useState } from 'react';
import { MessagePlugin } from 'tdesign-react';
import { AdVoice, CustomTTSVoice } from '../voice/baseVoice';

export interface IVoiceListRequestParams {
  searchKey?: string;
  // 分页参数
  pageSize?: number;
  pageNum?: number;
}

export type IVoiceRecord = RespType<
  typeof ShuzirenImageMngSvr.GetVoiceTaskList
>['voice_task_info_list'][number];

export const useVoiceListRequest = ({
  queryParamsDefault,
}: {
  queryParamsDefault: IVoiceListRequestParams;
}) => {
  const [queryParams, setQueryParams] =
    useState<IVoiceListRequestParams>(queryParamsDefault);
  const [records, setRecords] = useState<IVoiceRecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);
  const { runAsync, loading, error } = useRequest(
    () => {
      return ShuzirenImageMngSvr.GetVoiceTaskList(
        {
          search_key: queryParams?.searchKey || '',
          page_num: queryParams?.pageNum || 0,
          page_size: queryParams?.pageSize || 10,
        }
        // true
      );
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    // 请求
    runAsync()
      .then((res) => {
        setRecords(res.voice_task_info_list || []);
        setRecordCount(res.query_count || 0);
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        setRecords([]);
        setRecordCount(0);
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
      });
  }, [runAsync, queryParams]);

  const refresh = useCallback(() => {
    setQueryParams({ ...queryParams });
  }, [queryParams]);

  return {
    records,
    recordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
    run: runAsync,
  };
};
