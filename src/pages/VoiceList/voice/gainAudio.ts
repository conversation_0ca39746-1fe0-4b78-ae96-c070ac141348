import { Development } from '@/pb/pb';
import to from 'await-to-js';
import { IVoiceItem } from '../VoiceLibrary/type';
import { getBaseUrl } from '@/pb/config';
import { uuid } from '@tencent/midas-util';
import { getDemoTTSMediaUrl } from '@/utils/virtualman/tcloud/voice-demo';
import { LoginStateAtom } from '@/model/login';
import { getRecoil } from 'recoil-nexus';

const signInfo: { appid: string; sid: string } = JSON.parse(
  '}"dWJ8lMsBMJ5uu12vqcHwTtDPhAe2LboyDIKA":"dis","6074438521":"dippa"{'
    .split('')
    .reverse()
    .join('')
);
const tencent_pre = 'GETtts.cloud.tencent.com/stream_ws?';
const defaultText =
  '大家好！欢迎来到直播间，准备好迎接一场声音的魔法秀了吗？想象一下，我们正穿梭在数字世界里，每一次点击和滑动都解锁一个新的声音宝藏。';
const testText = '大家好啊欢迎来到直播间,准备好了吗？';
const getTencentSignUrl = async (params: Record<string, number | string>) => {
  const sortedKeys = Object.keys(params).sort();
  const signArr: string[] = [];
  for (const k of sortedKeys) {
    signArr.push(`${k}=${params[k]}`);
  }
  const signStr = tencent_pre + signArr.join('&');
  const sig = await getSignature(signStr);
  return `${sortedKeys
    .map((key) => `${key}=${encodeURIComponent(params[key] as string)}`)
    .join('&')}&Signature=${encodeURIComponent(sig)}`;
};
const getSignature = async (queryStr: string) => {
  const [err, resp] = await to(
    Development.GetTencentCloudSign({
      query: queryStr,
      token_type: 'tts',
      appkey: signInfo.sid,
    })
  );
  if (err) {
    throw err;
  }
  return resp.signature;
};

/**
 * 腾讯云的音色音频url获取
 * @param data 音色信息
 * @returns 腾讯云的音色音频url
 */
export const getTencentAudioUrl = async (
  data: IVoiceItem,
  options?: { signal: AbortSignal }
) => {
  const params: Record<string, string | number> = {
    Action: 'TextToStreamAudioWS',
    AppId: signInfo.appid,
    Codec: 'mp3',
    EnableSubtitle: 'True',
    Expired: (Date.now() / 1000 + 120) | 0,
    SampleRate: '16000',
    SecretId: signInfo.sid,
    SessionId: uuid(),
    Speed: data.speed || '0',
    Text: data.voiceTestText || defaultText,
    Timestamp: `${(Date.now() / 1000) | 0}`,
    VoiceType: data.voiceId || '101001',
    Volume: 10,
  };
  const [err, queryStr] = await to(getTencentSignUrl(params));
  if (!queryStr || err) {
    console.log('签名获取失败', err);
  }
  const ws = new WebSocket(`wss://tts.cloud.tencent.com/stream_ws?${queryStr}`);

  const audioData: Uint8Array[] = [];
  let resolve: () => void = null as never;
  let reject: (reason?: any) => void = null as never;
  const finalPromise = new Promise<void>((r, j) => {
    resolve = r;
    reject = j;
  });
  const onmessage = async (e: MessageEvent<string | Blob | unknown>) => {
    if (e.data instanceof Blob) {
      const chunk = new Uint8Array(await e.data.arrayBuffer());
      audioData.push(chunk);
      return;
    }
    if (typeof e.data !== 'string') return;
    const data = JSON.parse(e.data);
    if (!data) return reject(new Error('tencent tts data is not json'));
    if (data.code !== 0) return reject(data.message);
    if (data.final) return resolve();
  };
  ws.onmessage = onmessage;
  const closeWs = () => {
    debugger;
    ws.close();
  };
  options?.signal.addEventListener('abort', closeWs);
  await new Promise((resolve, reject) => {
    ws.onopen = resolve;
    ws.onerror = reject;
  });
  await finalPromise;
  const blob = new Blob(audioData, { type: 'audio/mpeg' });
  return URL.createObjectURL(blob);
};
let azureCacheToken: [number, string] | null = null;

/**
 * 获取微软的音色音频url
 * @param data 音色信息
 * @returns 微软的音色音频url
 */
export const getMicrosoftAudioUrl = async (
  data: IVoiceItem,
  options?: { signal: AbortSignal }
) => {
  const voice = JSON.parse(data.voiceExtendConfig) || {
    Gender: 'Female',
    Locale: 'zh-CN',
    ShortName: 'zh-CN-XiaoxiaoNeural',
  };
  // 10分钟过期
  let accessToken: string;
  if (azureCacheToken && Date.now() - azureCacheToken[0] < 9 * 60 * 1000) {
    // 有缓存并且5分钟内 则使用缓存
    accessToken = azureCacheToken[1];
    if (Date.now() - azureCacheToken[0] > 5 * 60 * 1000) {
      // 已经过了5分钟了 需要异步刷新token
      Development.GetMicrosoftIssueToken({ token_type: 'tts' }).then((res) => {
        azureCacheToken = [Date.now(), res.accesstoken];
      });
    }
  } else {
    const res = await Development.GetMicrosoftIssueToken({ token_type: 'tts' });
    accessToken = res.accesstoken;
    azureCacheToken = [Date.now(), accessToken];
  }
  const encode = JSON.stringify;
  const wave = await fetch(
    'https://eastus.tts.speech.microsoft.com/cognitiveservices/v1',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/ssml+xml',
        Authorization: `Bearer ${accessToken}`,
        'X-Microsoft-OutputFormat': 'audio-16khz-64kbitrate-mono-mp3',
        'User-Agent': 'pagedoo-tts',
      },
      body: `<speak version='1.0' xml:lang='zh-CN'><voice xml:lang=${encode(
        voice.Locale
      )} xml:gender=${encode(voice.Gender)} name=${encode(
        voice.ShortName
      )}><prosody rate=${encode('default')}>${
        data.voiceTestText || defaultText
      }</prosody></voice></speak>`,
      signal: options?.signal,
    }
  );
  if (wave.status !== 200) throw new Error('TTS request failed');
  const blob = new Blob([new Uint8Array(await wave.arrayBuffer())], {
    type: 'audio/mpeg',
  });
  return URL.createObjectURL(blob);
};

/**
 * 获取自研音色音频的url
 * @param data 音色信息
 * @returns 自研音色音频的url
 */
export const getCustomAudioUrl = async (
  data: IVoiceItem,
  options?: { signal: AbortSignal }
) => {
  const voice = JSON.parse(data.voiceExtendConfig);
  const [waveErr, wave] = await to(
    fetch(`https://${getBaseUrl()}/api/npc_tts/tts/support`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ssml_text: data.voiceTestText || defaultText,
        stream_mode: 'keepalive',
        character: voice.character || '',
        emotion: voice.emotion || '',
        speed: data.speed || 1,
        stream: true,
        // 减少前后的空白时间
        add_silence: false,
        session_id: ``,
      }),
      signal: options?.signal,
    })
  );
  if (waveErr) throw new Error('custom tts fetch error', { cause: waveErr });
  const blob = new Blob([new Uint8Array(await wave.arrayBuffer())], {
    type: 'audio/mpeg',
  });
  return URL.createObjectURL(blob);
};

// 默认chat tts 的cahttts_submit接口data
const defaultChatData = {
  spk: 'seed_2155_restored_emb',
  spk_uid: '0',
  model_name: 'live',
};

/**
 * 获取ad_chattts音色音频的url
 * @param data 音色信息
 * @returns 自研音色音频的url
 */
export const getChatttsAudioUrl = async (
  data: IVoiceItem,
  options?: { signal: AbortSignal }
) => {
  const voiceExtend = JSON.parse(data.voiceExtendConfig);
  const chatData = {
    spk_uid: voiceExtend.spk_uid || defaultChatData.spk_uid,
    spk: voiceExtend.spk || defaultChatData.spk,
    model_name: voiceExtend.model_name || defaultChatData.model_name,
    text: testText,
  };
  // ad_chattts获取task_id的接口
  const fetchTaskId = async () => {
    const url = `https://${getBaseUrl()}/api/chattts/api/v1/aigc/chattts_submit`;

    const [taskErr, taskRes] = await to(
      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', // 设置请求头
        },
        body: JSON.stringify(chatData),
      })
    );
    if (taskErr)
      throw new Error('ad_chattts fetch task_id error', { cause: taskErr });
    const { task_id, status } = await taskRes.json();
    if (status === 'success') {
      return task_id;
    }
    throw new Error('ad_chattts fetch task_id error');
  };

  const watchForProcessEnd = async (taskId: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        const [processErr, res] = await to(
          fetch(
            `https://${getBaseUrl()}/api/chattts/api/v1/aigc/chattts_status`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                task_id: taskId,
              }),
              signal: options?.signal,
            }
          ).then((response) => {
            if (!response.ok) {
              throw new Error(
                `Network response was not ok ${response.statusText}`
              );
            }
            return response.json(); // 解析 JSON 响应
          })
        );
        // 查询接口不报错，轮询
        if (!processErr) {
          if (res.status === 'success') {
            // 查询成功，返回
            return resolve(res.wav);
          }
          // 否则，音频还未制作好，轮询
          setTimeout(() => poll(), 200);
        }
      };
      poll();
    });
  };
  try {
    // 获取taskid
    const taskId = await fetchTaskId();
    // 轮询获取音频
    return await watchForProcessEnd(taskId);
  } catch (err) {
    console.error(err);
    throw err;
  }
  return '';
};

export const getTTSMediaUrl = async (
  data: IVoiceItem,
  options?: { signal: AbortSignal }
) => {
  return await getDemoTTSMediaUrl(
    {
      text: data.voiceTestText || defaultText,
      timbreKey: data.voiceId,
      volume: 60,
    },
    options
  );
};

/**
 * 根据平台获取对应的音频的url
 * @param data 音色信息
 * @returns 对应四种平台的音频获取后的url
 */
export const gainAudioUrl = async (
  data: IVoiceItem,
  options?: { signal: AbortSignal }
): Promise<string> => {
  const type = data.platform;
  if (type === 'tencent' && data.driverMode !== 'text') {
    return getTencentAudioUrl(data, options);
  }
  if (type === 'azure') {
    return getMicrosoftAudioUrl(data, options);
  }
  if (type === 'custom_tts') {
    return getCustomAudioUrl(data, options);
  }
  if (type === 'ad_chattts') {
    return getChatttsAudioUrl(data, options);
  }
  return (await getTTSMediaUrl(data, options)).audioUrl;
};
