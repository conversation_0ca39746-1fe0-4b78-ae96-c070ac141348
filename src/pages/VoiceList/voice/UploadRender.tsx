import { IVoiceUploadSlotApi } from '@/components/VoiceUpload';
import { formatSecond } from '@/utils/cos';
import { css } from '@emotion/react';
import { useCallback, useEffect, useState } from 'react';
import { Button, Loading, UploadFile } from 'tdesign-react';
import { MiniQRCodeDialog } from '@/pages/VirtualImage/MiniQRCodeDialog';

interface IProps {
  files: UploadFile[];
  api: IVoiceUploadSlotApi;
}
export const VoiceMainRender: React.FC<IProps> = function ({ files, api }) {
  let statusIcon: React.ReactElement = <></>;
  const [duration, setDuration] = useState<string>();
  const getAudioDuration = useCallback(async () => {
    if (files[0]) {
      const audioContext = new AudioContext();
      const buffer = await files[0]?.raw?.arrayBuffer();
      audioContext.decodeAudioData(buffer!, function (buffer) {
        const { duration } = buffer;
        setDuration(formatSecond(Math.floor(duration), 2));
      });
    }
  }, [files]);
  useEffect(() => {
    getAudioDuration();
  }, [getAudioDuration]);
  if (files[0]) {
    const { status } = files[0];

    console.log('file', files[0]);
    //   let audioIcon: React.ReactElement = <></>;
    if (status === 'success') {
      statusIcon = (
        <svg
          width="16"
          height="17"
          viewBox="0 0 16 17"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.99935 15.8332C12.0494 15.8332 15.3327 12.5499 15.3327 8.49984C15.3327 4.44975 12.0494 1.1665 7.99935 1.1665C3.94926 1.1665 0.666016 4.44975 0.666016 8.49984C0.666016 12.5499 3.94926 15.8332 7.99935 15.8332ZM4.99925 7.55702L6.99925 9.55702L10.9993 5.55702L11.9421 6.49983L6.99925 11.4426L4.05644 8.49983L4.99925 7.55702Z"
            fill="url(#paint0_linear_7877_22737)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_7877_22737"
              x1="0.666016"
              y1="1.1665"
              x2="15.3327"
              y2="1.1665"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#00C653" />
              <stop offset="1" stopColor="#01D766" />
            </linearGradient>
          </defs>
        </svg>
      );
    } else if (status === 'fail') {
      statusIcon = (
        <svg
          width="16"
          height="17"
          viewBox="0 0 16 17"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.99935 1.1665C12.0494 1.1665 15.3327 4.44975 15.3327 8.49984C15.3327 12.5499 12.0494 15.8332 7.99935 15.8332C3.94926 15.8332 0.666016 12.5499 0.666016 8.49984C0.666016 4.44975 3.94926 1.1665 7.99935 1.1665ZM7.33276 9.83316H8.6661V4.83316H7.33276V9.83316ZM8.6687 10.8332H7.33276V12.1691H8.6687V10.8332Z"
            fill="url(#paint0_linear_7877_22839)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_7877_22839"
              x1="0.666016"
              y1="8.49984"
              x2="15.3327"
              y2="8.49984"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#E25050" />
              <stop offset="1" stopColor="#FF5353" />
            </linearGradient>
          </defs>
        </svg>
      );
    } else if (typeof api.progress === 'number') {
      statusIcon = (
        <span>
          <Loading size="12px" />
          <span style={{ fontSize: '12px' }}>{api.progress}%</span>
        </span>
      );
    }

    return (
      <div>
        <div
          css={css`
            background: linear-gradient(
              97.99deg,
              #ebf4ff 12.3%,
              #f8f8ff 99.99%
            );
            width: 378px;
            height: 89px;
            padding: 20px;
            display: flex;
          `}
        >
          <div
            css={css`
              background: ${status !== 'success'
                ? 'linear-gradient(88.08deg,#c2d6ff -0.01%,#cde0ff 49.89%,#f0e9ff 99.99%)'
                : 'linear-gradient(88.08deg,#0153ff -0.01%,#2e7ffd 49.89%,#c1a3fd 99.99%),linear-gradient(0deg,rgba(255, 255, 255, 0.24),rgba(255, 255, 255, 0.24))'};
              height: 48px;
              width: 48px !important;
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
            `}
          >
            {status === 'success' ? (
              <svg
                width="32"
                height="33"
                viewBox="0 0 32 33"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M26 28.5L26 4.5H28L28 28.5H26Z"
                  fill="white"
                  fillOpacity="0.55"
                />
                <path
                  d="M21 28.5V12.5H19V28.5H21Z"
                  fill="white"
                  fillOpacity="0.55"
                />
                <path
                  d="M7 12.5L7 28.5H5V12.5H7Z"
                  fill="white"
                  fillOpacity="0.55"
                />
                <path
                  d="M12 28.5L12 4.5H14L14 28.5H12Z"
                  fill="white"
                  fillOpacity="0.55"
                />
              </svg>
            ) : undefined}
          </div>
          <div
            css={css`
              margin-left: 16px;
              display: flex;
              flex-direction: column;
            `}
          >
            <span>
              已上传的录音文件
              <span style={{ marginLeft: '4px' }}>{statusIcon}</span>
            </span>
            <span
              style={{
                color: '#0000004D',
                fontSize: '14px',
                marginTop: '8px',
              }}
            >
              {duration}
            </span>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div
      style={{ display: files[0] ? 'none' : 'flex' }}
      css={css`
        display: flex;
        align-items: center;
        flex-direction: column;
      `}
    >
      <svg
        width="49"
        height="48"
        viewBox="0 0 49 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="0.5"
          width="48"
          height="48"
          rx="24"
          fill="url(#paint0_linear_7877_22623)"
        />
        <path
          d="M29.413 12.1415C29.7161 12.656 29.8775 13.2545 29.8779 13.8659V34.1375C29.8778 34.7113 29.736 35.2744 29.4674 35.7679C29.1988 36.2613 28.8132 36.6669 28.3512 36.942C27.8892 37.2171 27.3677 37.3516 26.8416 37.3314C26.3154 37.3113 25.8039 37.1371 25.3608 36.8273L17.9032 31.614C17.7434 31.4993 17.5563 31.4382 17.3649 31.4382H12.7672C12.382 31.4382 12.0006 31.3555 11.6447 31.1949C11.2888 31.0342 10.9655 30.7988 10.6931 30.502C10.4207 30.2052 10.2047 29.8528 10.0573 29.465C9.90985 29.0772 9.83398 28.6615 9.83398 28.2418V19.7313C9.83398 19.3116 9.90985 18.8959 10.0573 18.5081C10.2047 18.1203 10.4207 17.768 10.6931 17.4711C10.9655 17.1743 11.2888 16.9389 11.6447 16.7782C12.0006 16.6176 12.382 16.5349 12.7672 16.5349H17.3899C17.582 16.5349 17.7712 16.4742 17.9296 16.3591L25.3623 11.1714C25.6867 10.945 26.0488 10.7904 26.4281 10.7166C26.8073 10.6427 27.1962 10.651 27.5724 10.741C27.9487 10.8309 28.3051 11.0007 28.6211 11.2408C28.9372 11.4808 29.2068 11.7863 29.4144 12.1399L29.413 12.1415ZM27.5621 13.436C27.4698 13.2785 27.3298 13.1612 27.1663 13.1045C27.0029 13.0479 26.8264 13.0555 26.6675 13.126L26.5487 13.1931L19.1424 18.3601C18.6966 18.6813 18.1862 18.8747 17.6568 18.9227L17.3899 18.9338H12.7672C12.5955 18.9338 12.4293 18.9993 12.2975 19.1191C12.1656 19.2388 12.0765 19.4051 12.0456 19.5891L12.0339 19.7329V28.2402C12.0338 28.4272 12.094 28.6083 12.2038 28.752C12.3137 28.8957 12.4663 28.9928 12.6352 29.0265L12.7672 29.0393H17.3649C17.8973 29.0393 18.4209 29.1863 18.8726 29.4532L19.0926 29.597L26.5487 34.8104C26.6501 34.8813 26.7659 34.9239 26.8859 34.9344C27.006 34.945 27.1266 34.9232 27.237 34.8709C27.3475 34.8186 27.4445 34.7374 27.5193 34.6346C27.5942 34.5318 27.6447 34.4105 27.6663 34.2814L27.678 34.1375V13.8659C27.6778 13.7646 27.6598 13.6643 27.6252 13.5703L27.5621 13.4344V13.436ZM32.5471 18.4128C32.8203 18.3016 33.1229 18.3132 33.3883 18.4451C33.6538 18.5769 33.8602 18.8183 33.9623 19.116C34.4933 20.6663 34.7673 22.3172 34.7673 24.0049C34.7673 25.6543 34.505 27.2653 33.9975 28.7852C33.898 29.084 33.6935 29.3275 33.4292 29.462C33.1649 29.5966 32.8624 29.6113 32.5882 29.5028C32.3139 29.3942 32.0905 29.1715 31.967 28.8834C31.8435 28.5954 31.8301 28.2657 31.9297 27.9669C32.3536 26.6971 32.5683 25.3562 32.5674 24.0049C32.5674 22.6033 32.3418 21.2384 31.9018 19.9551C31.7997 19.6573 31.8103 19.3276 31.9314 19.0383C32.0524 18.7491 32.2739 18.5241 32.5471 18.4128ZM35.7575 15.7854C36.0101 15.6268 36.3102 15.5839 36.5918 15.6664C36.8735 15.7488 37.1135 15.9497 37.2592 16.2249C38.5125 18.59 39.1707 21.2738 39.1673 24.0049C39.171 26.741 38.5107 29.4295 37.2534 31.7978C37.1038 32.0651 36.8641 32.258 36.5857 32.3353C36.3073 32.4126 36.0121 32.3681 35.7634 32.2112C35.5147 32.0544 35.3322 31.7978 35.2549 31.4962C35.1776 31.1947 35.2116 30.8721 35.3497 30.5975C36.4127 28.5938 36.9703 26.3194 36.9659 24.0049C36.9694 21.6946 36.4129 19.4242 35.3527 17.4235C35.2804 17.2871 35.2334 17.1365 35.2146 16.9804C35.1957 16.8242 35.2053 16.6655 35.2428 16.5134C35.2802 16.3612 35.3448 16.2186 35.4329 16.0937C35.5209 15.9688 35.6322 15.864 35.7575 15.7854Z"
          fill="url(#paint1_linear_7877_22623)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_7877_22623"
            x1="0.5"
            y1="48"
            x2="50.3023"
            y2="45.9458"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E0EAFF" />
            <stop offset="0.470486" stopColor="#E2EFFF" />
            <stop offset="1" stopColor="#F5F3FF" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_7877_22623"
            x1="9.83104"
            y1="37.3334"
            x2="53.7571"
            y2="36.6697"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#0153FF" />
            <stop offset="1" stopColor="#8649FF" />
          </linearGradient>
        </defs>
      </svg>
      <span style={{ color: '#000000E5', margin: '16px' }}>
        上传音频制作您的数字人音色
      </span>
      <div
        css={css`
          background: linear-gradient(
            88.08deg,
            #0153ff -0.01%,
            #2e7ffd 49.89%,
            #c1a3fd 99.99%
          );
          border-radius: 4px;
          height: 32px;
          width: 103px;
          cursor: pointer;
          text-align: center;
          color: #ffffff;
          line-height: 32px;
        `}
        onClick={() => {
          api.triggerUpload();
        }}
      >
        上传音频
      </div>

      <div
        css={css`
          color: #00000066;
          font-weight: 400;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          margin: 38px;
        `}
      >
        <span>1、大小不超过20M，10-30秒</span>
        <span>2、格式为wav、mp3、aac、m4a</span>
        <span>3、采样率需大于16k</span>
      </div>
    </div>
  );
};

export const VoiceBottomRender: React.FC<IProps> = function ({ files, api }) {
  const [showQRCode, setShowQRCode] = useState(false);
  return (
    <>
      {/* <span>若没有现成音频，可体验小程序制作哦</span> */}
      <span>小程序音频制作入口正在制作中～&nbsp;&nbsp;&nbsp;敬请期待！</span>
      {/* <Button
        variant="text"
        disabled
        style={{ color: '#0047F9' }}
        onClick={() => setShowQRCode(true)}
      >
        去使用
      </Button> */}
      <MiniQRCodeDialog
        show={showQRCode}
        onClose={() => setShowQRCode(false)}
      />
    </>
  );
};
