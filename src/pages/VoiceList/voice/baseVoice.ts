/* eslint-disable class-methods-use-this */
import { LoginStateAtom } from '@/model/login';
import { getBaseUrl } from '@/pb/config';
import { ShuzirenImageMngSvr } from '@/pb/pb';
import {
  IGetDemoTTSMediaOptions,
  IGetDemoTTSMediaParams,
  getDemoTTSMediaUrl,
} from '@/utils/virtualman/tcloud/voice-demo';
import { uuid } from '@tencent/midas-util';
import { getRecoil } from 'recoil-nexus';
import { gainAudioUrl } from './gainAudio';
import { IVoiceItem } from '../VoiceLibrary/type';

(window as any).__getDemoTTSMediaUrl = getDemoTTSMediaUrl;

export type voiceUploadType = {
  material_voice_url: string;
  voice_task_name: string;
  voice_sexy: number;
  notes: string;
};
export abstract class BaseVoice {
  // 试听逻辑
  public abstract voiceListen(
    params: IGetDemoTTSMediaParams,
    _options?: IGetDemoTTSMediaOptions
  ): Promise<{ audioUrl: string }>;

  // 删除逻辑
  public abstract voiceDelete(voiceId: string): Promise<any>;

  // 提交逻辑
  public abstract voiceSubmit(voice_data: voiceUploadType): Promise<void>;
}

// 聚合版音色试听、音色训练、音色训练任务删除逻辑提取
export class CustomTTSVoice extends BaseVoice {
  public voiceListen(
    params: IGetDemoTTSMediaParams,
    _options?: { signal: AbortSignal }
  ): Promise<{ audioUrl: string }> {
    const voiceData: Partial<IVoiceItem> = {
      platform: 'custom_tts',
      voiceExtendConfig: JSON.stringify({
        character: params.timbreKey,
        emotion: 'default',
      }),
      voiceTestText: params.text,
      speed: 1,
    };
    return new Promise((resolve, reject) => {
      gainAudioUrl(voiceData as IVoiceItem, _options)
        .then((url) => resolve({ audioUrl: url }))
        .catch(reject);
    });
  }
  public voiceDelete(voiceId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ShuzirenImageMngSvr.DeleteVoiceId({
        voice_task_id: voiceId,
      })
        .then(resolve)
        .catch(reject);
    });
  }
  public voiceSubmit(voice_data: voiceUploadType): Promise<void> {
    return new Promise((resolve, reject) => {
      const { material_voice_url, voice_task_name, voice_sexy, notes } =
        voice_data;
      ShuzirenImageMngSvr.CreateVoiceTask({
        material_voice_url,
        voice_task_name,
        voice_sexy: Number(voice_sexy),
        notes,
      })
        .then(() => resolve(void 0))
        .catch(reject);
    });
  }
}

// 广告线试听、音色训练、音色训练删除逻辑提取
export class AdVoice extends BaseVoice {
  public voiceListen(
    params: IGetDemoTTSMediaParams,
    _options?: { signal: AbortSignal }
  ): Promise<{ audioUrl: string }> {
    const voiceData: Partial<IVoiceItem> = {
      platform: 'tencent_man',
      voiceId: params.timbreKey,
      voiceTestText: params.text,
    };
    return new Promise((resolve, reject) => {
      // 获取音频的url
      gainAudioUrl(voiceData as IVoiceItem, _options)
        .then((url) => resolve({ audioUrl: url }))
        .catch(reject);
    });
  }
  public voiceDelete(voiceId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ShuzirenImageMngSvr.DeleteVoiceId({
        voice_task_id: voiceId,
      })
        .then(resolve)
        .catch(reject);
    });
  }
  public voiceSubmit(voice_data: voiceUploadType): Promise<void> {
    return new Promise((resolve, reject) => {
      const { material_voice_url, voice_task_name, voice_sexy, notes } =
        voice_data;
      ShuzirenImageMngSvr.CreateVoiceTask({
        material_voice_url,
        voice_task_name,
        voice_sexy: Number(voice_sexy),
        notes,
      })
        .then(() => resolve(void 0))
        .catch(reject);
    });
  }
}
