import { UPLOAD_CODE } from '@/configs/upload';
import { CosUpload } from '@/pages/VirtualImage/VirtualAdd/tencentCloud';
import { ShuzirenImageMngSvr } from '@/pb/pb';
import {
  UploadFilesParams,
  UploadRequestProps,
  UploadResponseProps,
  fileMD5,
  queryCosParams,
  upload,
} from '@/utils/cos';
import { sessionToken } from '@tencent/midas-util';
import COS from 'cos-js-sdk-v5';

export const uploadAdRequest: UploadRequestProps = async (
  type,
  file,
  index,
  getTask,
  options
) => {
  return new Promise((resolve, reject) => {
    const fileType = file.name.includes('.')
      ? file.name.split('.').pop()?.toLowerCase()
      : '';
    console.time('time-cost-of-md5');
    const namePromise = options?.fileName
      ? Promise.resolve(options.fileName)
      : fileMD5(file, 2097152);
    // 分块大小设置为2M
    namePromise
      .then((fileName) => {
        console.timeEnd('time-cost-of-md5');
        // 把 getTask 透传到 调用 cos 的方法中
        return uploadFileRequest({
          // bucket 和 region 会在queryCosParams方法中获取
          bucket: '',
          region: '',
          name: `${fileName}.${fileType}`,
          file,
          getTask,
          onProgress: options?.onProgress,
        });
      })
      .then((res) => {
        resolve({ url: res, code: 200, key: '' });
      })
      .catch((err) => {
        reject(err);
      });
  });
};

async function uploadFileRequest(params: UploadFilesParams): Promise<string> {
  const credentials = await ShuzirenImageMngSvr.GetUploadCredentials({
    image_id: '',
  });
  // 获取上传路径
  const filePath = new URL(credentials.PathPrefix).pathname;
  // 初始化cos
  const TXCloudCOS = new CosUpload(credentials);
  await TXCloudCOS.uploadFileToCos({
    fileName: `${filePath}zeroshot/${params.name}`,
    file: params.file,
    onProgress: params.onProgress,
  });
  console.debug('上传音频成功');
  return `${credentials.PathPrefix}zeroshot/${params.name}`;
}
