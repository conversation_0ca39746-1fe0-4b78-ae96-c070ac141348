.voice-list-page {
    .main_content {
      overflow: initial;
      padding: 0;
    }

    &-title {
      color: rgba(0, 0, 0, 0.90);
      font-style: normal;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .t-tabs__content {
      padding: 20px;
    }

    &-content {

      &-inner-box {
        border: 1px solid #E3E5EB;
        border-radius: 4px;
        height: 100%;

        .operate-box {
          width: 100%;
          height: 54px;
          display: flex;
          align-items: center;
          padding: 0 12px;
          border-bottom: 1px solid #E3E5EB;
        }

        .VoiceAddList {
          height: calc(100% - 49px);
        }
      }
    }

    .voice-record-comp {
      height: 100%;

      .t-table {
        border-top: 1px solid rgb(232, 232, 232);;
        height: 100%;
      }
      .t-table__header {
        tr > th {
          background-color: #f8f6fb;
        }
      }

      .t-table__content {
        // min-height: calc(100vh - 300px);
        height: calc(100% - 64px);
      }

      .t-table__body {
        td {
          vertical-align: middle;
        }
      }

      .t-table__pagination-wrap {
        border-top: 1px solid #E3E5EB;
      }

      .voice_status_tag {
        --c: #f4f6ff;
        position: relative;
        height: 24px;
        padding: 0 10px 0 4px;
        line-height: 24px;
        font-size: 14px;
        color: rgba(0, 10, 41, 0.26);
        background: linear-gradient(to right, var(--c), #faf5fc);
        display: inline-block;
        border-radius: 0 12px 16px 0;

        &::before {
          content: '';
          position: absolute;
          left: -2px;
          top: 0;
          bottom: 0;
          width: 6px;
          border-radius: 4px;
          transform: skewX(-10deg);
          background: var(--c);
          display: block;
        }

        &.voice_status_2 {
          --c: rgb(248, 210, 18);
          color: rgb(198, 119, 0);
          background: linear-gradient(to right, var(--c), rgb(247, 235, 93));

          &::before {
            background: var(--c);
          }
        }

        &.voice_status_1000 {
          --c: rgba(235, 248, 240, 1);
          color: rgba(0, 198, 83, 1);
          background: linear-gradient(
                  to right,
                  var(--c),
                  rgba(222, 248, 232, 1)
          );

          &::before {
            background: var(--c);
          }
        }
      }
    }
}

.voice-list-page-dialog {
  .t-slider {
    .t-slider__track {
      background: linear-gradient(88deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
    }

    .t-slider__button {
      border: 2px solid #2E7FFD;
    }
  }
}
