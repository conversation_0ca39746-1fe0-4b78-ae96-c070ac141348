import { Page } from '@/components/Layout';
import { AssetsLibConfigContext } from '@/configs/config_context';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import TabPanel from 'tdesign-react/es/tabs/TabPanel';
import Tabs from 'tdesign-react/es/tabs/Tabs';
import VoiceList from '.';
import VoiceLibrary from './VoiceLibrary';
import Style from './wrapper.module.less';
import { useSearchParams } from 'react-router-dom';
const classPrefix = 'voice-list-page';

export default function Wrapper() {
  const [searchParams, setSearchParams] = useSearchParams();
  const tab = searchParams.get('tab');
  return (
    <AssetsLibConfigContext.Provider
      value={MatchedGlobalConfigItem.assetsLibraryConfig}
    >
      <Page
        title="声音定制"
        className={[classPrefix, Style['voice-list__wrapper']].join(' ')}
        style={{
          overflow: 'hidden',
        }}
      >
        <Tabs
          defaultValue={tab ?? 'list'}
          placement="top"
          size="medium"
          theme="normal"
          onChange={(value) => {
            setSearchParams((pre) => ({ ...pre, tab: value }));
          }}
          style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <TabPanel label="资产" value="list">
            <VoiceLibrary />
          </TabPanel>
          <TabPanel label="定制" value="task">
            <VoiceList disable />
          </TabPanel>
        </Tabs>
      </Page>
    </AssetsLibConfigContext.Provider>
  );
}
