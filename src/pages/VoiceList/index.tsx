/**
 * <AUTHOR>
 * @date 2024/6/26 下午4:30
 * @desc index
 */

import React, { useState } from 'react';
import './index.less';
import { Button, Input } from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import { useNavigate } from 'react-router-dom';
import { useVoiceListRequest } from '@/pages/VoiceList/hooks/useVoiceListRequest';
import { VoiceRecord } from '@/pages/VoiceList/VoiceRecord';
import { MainContent, Page } from '@/components/Layout';
import VoiceAddActionBar from './VoiceAddActionBar';

const classPrefix = 'voice-list-page';

export default function VoiceList(props: { disable?: boolean }) {
  const [searchVal, setSearchVal] = useState('');

  const {
    queryParams,
    setQueryParams,
    records,
    refresh,
    recordCount,
    loading,
  } = useVoiceListRequest({
    queryParamsDefault: {
      searchKey: searchVal,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const mainContent = (
    <MainContent className={`${classPrefix}-content`}>
      <div className={`${classPrefix}-content-inner-box`}>
        <VoiceAddActionBar
          inputValue={searchVal}
          onInputChange={(value: string) => {
            setSearchVal(value);
          }}
          onEnter={(value: string) => {
            setQueryParams({
              ...queryParams,
              searchKey: value,
            });
          }}
        />
        {/* Record*/}
        <div className="VoiceAddList">
          <VoiceRecord
            queryParams={queryParams}
            setQueryParams={setQueryParams}
            records={records}
            refresh={refresh}
            loading={loading}
            recordCount={recordCount}
          />
        </div>
      </div>
    </MainContent>
  );
  if (props.disable) {
    // return <div>{mainContent}</div>;
    return mainContent;
  }
  return (
    <Page title="声音定制" className={classPrefix}>
      {mainContent}
    </Page>
    // <div className={classPrefix}>
    //   <div className={`${classPrefix}-title`}>声音定制</div>
    //   <div className={`${classPrefix}-content`}>

    //   </div>
    // </div>
  );
}
