/**
 * <AUTHOR>
 * @date 2024/5/20 下午7:25
 * @desc index
 */

import VoiceDemoImage from '@/assets/images/voice-demo.png';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { CustomSteps } from '@/pages/VirtualImage/CustomSteps';
import {
  IVoiceListRequestParams,
  IVoiceRecord,
} from '@/pages/VoiceList/hooks/useVoiceListRequest';
import { ResourceSvr, ShuzirenImageMngSvr } from '@/pb/pb';
import dayjs from 'dayjs';
import { Fragment, useState } from 'react';
import { EllipsisIcon, PlayCircleIcon } from 'tdesign-icons-react';
import {
  Button,
  Dialog,
  Image,
  Input,
  Link,
  MessagePlugin,
  Popconfirm,
  Popup,
  PrimaryTableCol,
  Slider,
  Space,
  Table,
  Textarea,
} from 'tdesign-react';
import {
  VOICE_STATUS,
  VOICE_STATUS_TEXT_MAP,
} from '@/pages/VoiceList/VoiceRecord/const';
import { gainAudioUrl } from '../voice/gainAudio';
import { IVoiceItem } from '../VoiceLibrary/type';

interface IProps {
  queryParams: IVoiceListRequestParams;
  setQueryParams: (params: IVoiceListRequestParams) => void;
  records: IVoiceRecord[];
  recordCount: number;
  refresh: () => void;
  loading: boolean;
}

const LISTEN_TEXT = {
  init: '合成试听',
  loading: '合成中',
  play: '试听中',
};

export function VoiceRecord(props: IProps) {
  const [dialogVisible, setDialogVisible] = useState<
    'audition' | 'verify' | ''
  >('');
  const [inputValue, setInputValue] = useState('');
  // const [reason, setReason] = useState('');
  const [voiceIncrement, setVoiceIncrement] = useState(30);
  const [listenStatus, setListenStatus] = useState<'init' | 'loading' | 'play'>(
    'init'
  );
  const [processMap, setProcessMap] = useState<
    Record<string, Array<{ label: string; text: string; state: string }>>
  >({});
  const [selectRow, setSelectRow] = useState<IVoiceRecord | null>(null);
  const {
    runtimeConf: {
      voiceConf: {
        voice: { voiceListen, voiceDelete },
      },
    },
  } = MatchedGlobalConfigItem;
  const {
    records,
    refresh,
    queryParams,
    setQueryParams,
    recordCount,
    loading,
  } = props;

  // 定制进度
  const viewProcess = async (voiceId: string) => {
    if (processMap[voiceId]) {
      return processMap[voiceId];
    }
    try {
      const res = await ShuzirenImageMngSvr.GetVoiceLogList({
        voice_task_id: voiceId,
      });
      setProcessMap({
        ...processMap,
        [voiceId]:
          res?.voice_operator_logs_list
            ?.map((i, idx) => {
              return {
                label: new Date(i.update_time).toLocaleString(),
                text: i.notes,
                state: `${idx}`,
              };
            })
            ?.sort(
              (a, b) =>
                new Date(a.label).getTime() - new Date(b.label).getTime()
            ) || [],
      });
    } catch (error) {
      console.error('获取定制进度失败', error);
    }
  };

  const handleDelete = async (voiceId: string) => {
    const loading = await MessagePlugin.loading('删除中...');
    try {
      await voiceDelete(voiceId);
      void MessagePlugin.success('删除成功');
    } catch (error) {
      console.log(error);
      void MessagePlugin.error('删除失败');
    } finally {
      refresh();
      loading.close();
    }
  };
  const handleListen = async () => {
    if (!selectRow?.voice_mod_uri) {
      return;
    }
    let audioUrl = '';
    setListenStatus('loading');
    // 后续音色训练走自研，所以这里要走特殊逻辑判断
    if (selectRow.platform === 'custom_tts') {
      const custom_voice: IVoiceItem = {
        voiceName: '',
        voiceDesc: '',
        speed: 1,
        voiceImage: '',
        voiceTestText: inputValue,
        voiceAuthority: 0,
        appCode: '',
        userId: '',
        platform: 'custom_tts',
        voiceId: '',
        voiceExtendConfig: JSON.stringify({
          character: selectRow.voice_mod_uri,
          emotion: 'default',
        }),
      };
      audioUrl = (await gainAudioUrl(custom_voice)) || '';
    } else {
      const audio = await voiceListen({
        text: inputValue,
        timbreKey: selectRow.voice_mod_uri,
        volume: voiceIncrement,
      });
      audioUrl = audio.audioUrl || '';
    }

    // 监听 ended 事件
    const audio = new Audio(audioUrl);
    // 自研训练音色接口不支持音量调节传参，这里特殊处理一下
    if (selectRow.platform === 'custom_tts') {
      audio.volume = voiceIncrement / 100;
    }
    audio.addEventListener('ended', () => {
      setListenStatus('init');
      console.debug('Audio playback has ended');
    });
    // 播放音频
    audio
      .play()
      .then(() => {
        console.debug('Audio is playing');
        setListenStatus('play');
      })
      .catch((error) => {
        console.error('Error playing audio:', error);
      });
  };

  const addToEditor = async (voiceInfo: IVoiceRecord) => {
    if (
      voiceInfo?.voice_task_id &&
      voiceInfo?.process_status === VOICE_STATUS.finishing &&
      voiceInfo?.store_status !== 1
    ) {
      try {
        await ResourceSvr.StoreDipVoiceResource({
          voice_task_id: voiceInfo.voice_task_id,
        });
        void MessagePlugin.success('入库成功');
        refresh();
      } catch (e) {
        void MessagePlugin.error('入库失败，请稍后重试');
      }
    }
  };

  // 确认效果
  // const handleConfirm = async (type: 'AGREE' | 'REJECT', reason?: string) => {
  //   if (!selectRow?.voice_task_id) return;
  //   const params = {
  //     image_id: selectRow.voice_task_id,
  //     operate: type,
  //     reason: '',
  //   };
  //   if (type === 'REJECT') {
  //     if (!reason) {
  //       void MessagePlugin.error({ content: '请填写不认可原因' });
  //       return;
  //     }
  //     params.reason = reason;
  //   }
  //
  //   try {
  //     // await ShuzirenImageMngSvr.PractiseStatusConfirm(params)
  //     if (type === 'AGREE') {
  //       void MessagePlugin.success({ content: '您已认可该训练结果' });
  //     }
  //     if (type === 'REJECT') {
  //       void MessagePlugin.success({ content: '已提交反馈，将为您返回额度' });
  //     }
  //   } catch (e) {
  //     void MessagePlugin.error({ content: '操作失败，请稍后重试' });
  //   } finally {
  //     refresh();
  //   }
  // };

  const columns: PrimaryTableCol<IVoiceRecord>[] = [
    {
      title: 'demo预览',
      colKey: 'pic_url',
      width: 104,
      cell: () => (
        <Image
          src={VoiceDemoImage}
          style={{
            width: 72,
            height: 72,
            cursor: 'pointer',
          }}
          shape="round"
        />
      ),
    },
    { title: '任务名称', colKey: 'voice_task_name', width: 200 },
    { title: 'ID', colKey: 'voice_task_id', width: 300 },
    {
      title: '定制进度',
      colKey: 'process_status',
      width: 150,
      cell: ({ row }) => {
        const statusText =
          VOICE_STATUS_TEXT_MAP[
            row.process_status as keyof typeof VOICE_STATUS_TEXT_MAP
          ] || '未知状态';
        return (
          <>
            <div
              className={`voice_status_tag voice_status_${row.process_status}`}
            >
              {statusText}
            </div>
            <Popup
              content={
                <div
                  style={{
                    padding: '15px 15px 0',
                    maxHeight: '500px',
                    overflow: 'scroll',
                  }}
                >
                  {!!processMap[row.voice_task_id]?.length ? (
                    <CustomSteps
                      timelineList={processMap[row.voice_task_id]}
                      state={`${
                        processMap[row.voice_task_id][
                          processMap[row.voice_task_id].length - 1
                        ]?.state
                      }`}
                    />
                  ) : (
                    <div>暂无数据</div>
                  )}
                </div>
              }
              trigger="click"
              placement="bottom-right"
              showArrow
            >
              <EllipsisIcon onClick={() => viewProcess(row.voice_task_id)} />
            </Popup>
          </>
        );
      },
    },
    {
      title: '自然度评分（100）',
      colKey: 'nature_score',
      width: 200,
      cell: ({ row }) => {
        return (
          <>
            {row?.nature_score ? (
              <>
                <div>自然度：{row.nature_score}</div>
                <div>亲和度：{row.affinity_score}</div>
                <div>流畅度：{row.fluency_score}</div>
              </>
            ) : (
              <span>-</span>
            )}
          </>
        );
      },
    },
    {
      title: '是否入库',
      colKey: 'add_to_editor',
      width: 120,
      cell: ({ row }) => {
        const msg = row.store_status === 1 ? '已入库' : '未入库';
        return (
          <>
            {row.process_status === VOICE_STATUS.confirmSubmit ? '确认中' : msg}
          </>
        );
      },
    },
    {
      title: '创建时间',
      colKey: 'create_time',
      width: 150,
      cell: ({ row }) => (
        <>
          {/* <div>{row.user_nick}</div>*/}
          {dayjs(row.create_time).format('YYYY-MM-DD HH:mm:ss')}
        </>
      ),
    },
    {
      title: '更多信息',
      colKey: 'video_msg',
      width: 200,
      cell: ({ row }) => {
        return (
          <>
            <div>形象性别： {row.voice_sexy === 1 ? '男' : '女'}</div>
            <div>
              <a
                href={row.material_voice_url}
                target="_blank"
                rel="noopener noreferrer"
                download={row.material_voice_url}
              >
                <span style={{ color: '#0153FF' }}>原始素材</span>
              </a>
            </div>
          </>
        );
      },
    },
    {
      colKey: 'operate',
      fixed: 'right',
      width: 220,
      title: '操作',
      cell: ({ row }) => (
        <Space>
          <Popconfirm
            x-if={
              row.process_status === VOICE_STATUS.finishing &&
              row.store_status === 0
            }
            content="确认添加入库吗？"
            onConfirm={() => addToEditor(row)}
          >
            <Link className="block" theme="primary" hover="color">
              添加入库
            </Link>
          </Popconfirm>
          <Link
            x-if={row.process_status === VOICE_STATUS.finishing}
            theme="primary"
            hover="color"
            onClick={() => {
              setInputValue(
                `你好，我是页匠数字人${row.voice_task_name}。欢迎你与我一起来到页匠的数字世界，期待在这里共同开启一段非凡的数字旅程。`
              );
              setListenStatus('init');
              setSelectRow(row);
              setDialogVisible('audition');
            }}
          >
            试听
          </Link>
          {/* <Link*/}
          {/*  x-if={row.process_status === VOICE_STATUS.confirm}*/}
          {/*  theme="primary"*/}
          {/*  hover="color"*/}
          {/*  onClick={() => {*/}
          {/*    setInputValue(*/}
          {/*      `你好，我是页匠数字人${row.voice_task_name}。欢迎你与我一起来到页匠的数字世界，期待在这里共同开启一段非凡的数字旅程。`*/}
          {/*    );*/}
          {/*    setListenStatus('init');*/}
          {/*    setSelectRow(row);*/}
          {/*    setDialogVisible('verify');*/}
          {/*  }}*/}
          {/* >*/}
          {/*  效果确认*/}
          {/* </Link>*/}
          <Popconfirm
            content="确认删除吗"
            destroyOnClose
            placement="top"
            showArrow
            theme="default"
            onConfirm={() => {
              handleDelete(row.voice_task_id).then();
            }}
          >
            <Link theme="danger" hover="color">
              删除
            </Link>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  return (
    <div className="voice-record-comp">
      <Table
        rowKey="qa_content_id"
        columns={columns}
        data={records}
        loading={loading}
        maxHeight="calc(100vh - 280px)"
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: recordCount,
        }}
        onPageChange={(pageInfo) => {
          setQueryParams({
            ...queryParams,
            // 切换每页条数时，重置页码
            pageNum:
              queryParams.pageSize === pageInfo.pageSize ? pageInfo.current : 1,
            pageSize: pageInfo.pageSize,
          });
        }}
        size="medium"
        verticalAlign="middle"
      />
      <Dialog
        className="voice-list-page-dialog pagedoo-meta-live-global"
        header={dialogVisible === 'audition' ? '试听' : '确认效果'}
        footer={null}
        destroyOnClose
        closeOnOverlayClick={false}
        visible={!!dialogVisible}
        confirmOnEnter
        onClose={() => {
          setDialogVisible('');
        }}
      >
        <Textarea
          disabled={listenStatus !== 'init'}
          placeholder="请输入试听文案"
          value={inputValue}
          autosize={{ minRows: 7, maxRows: 10 }}
          onChange={(value) => {
            setInputValue(value);
          }}
        />
        <div className="flex mt-12 justify-between">
          <span>音量增益</span>
          <Slider
            disabled={listenStatus !== 'init'}
            style={{ width: '80%', paddingRight: '10px' }}
            label={({ value }) => `${value}%`}
            value={voiceIncrement}
            onChange={(value) => {
              setVoiceIncrement(value as number);
            }}
          />
        </div>
        <div className="mt-16 flex justify-start">
          <Button
            disabled={listenStatus === 'play'}
            loading={listenStatus === 'loading'}
            icon={<PlayCircleIcon />}
            theme="default"
            className="gradient-default"
            onClick={handleListen}
          >
            {LISTEN_TEXT[listenStatus]}
          </Button>
        </div>

        {/* <Fragment x-if={dialogVisible === 'verify'}>*/}
        {/*  <div className="mt-16 flex justify-end gap-[8px]">*/}
        {/*    <Popconfirm*/}
        {/*      theme="default"*/}
        {/*      icon={<></>}*/}
        {/*      placement="bottom-left"*/}
        {/*      content={*/}
        {/*        <>*/}
        {/*          <div*/}
        {/*            style={{*/}
        {/*              fontSize: 14,*/}
        {/*              fontWeight: 600,*/}
        {/*              lineHeight: '28px',*/}
        {/*            }}*/}
        {/*          >*/}
        {/*            不认可原因*/}
        {/*          </div>*/}
        {/*          <Input*/}
        {/*            value={reason}*/}
        {/*            onChange={(values) => {*/}
        {/*              setReason(values);*/}
        {/*            }}*/}
        {/*            maxlength={300}*/}
        {/*            showLimitNumber*/}
        {/*          />*/}
        {/*        </>*/}
        {/*      }*/}
        {/*      onConfirm={() => handleConfirm('REJECT', reason)}*/}
        {/*    >*/}
        {/*      <Button*/}
        {/*        theme="danger"*/}
        {/*        style={{*/}
        {/*          background:*/}
        {/*            'linear-gradient(45deg, #FF5E55 -0.01%, #FF8E86 99.99%)',*/}
        {/*          border: 'none',*/}
        {/*        }}*/}
        {/*      >*/}
        {/*        不认可，返回额度*/}
        {/*      </Button>*/}
        {/*    </Popconfirm>*/}
        {/*    <Button*/}
        {/*      theme="primary"*/}
        {/*      className="gradient-primary"*/}
        {/*      onClick={() => handleConfirm('AGREE')}*/}
        {/*    >*/}
        {/*      认可结果*/}
        {/*    </Button>*/}
        {/*  </div>*/}
        {/* </Fragment>*/}
      </Dialog>
    </div>
  );
}
