/**
 * <AUTHOR>
 * @date 2024/6/26 下午4:57
 * @desc index
 */

import { useMemo, useState } from 'react';
import {
  Breadcrumb,
  Button,
  Form,
  FormRules,
  Input,
  Link as LinkButton,
  MessagePlugin,
  Radio,
} from 'tdesign-react';
import { useNavigate } from 'react-router-dom';
import './index.less';
import { TrainResultPage } from '@/components/TrainResultPage';
import VoiceUpload from '../../../components/VoiceUpload/index';
import { VoiceBottomRender, VoiceMainRender } from '../voice/UploadRender';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { RespError } from '@/pb/config';

const { FormItem } = Form;

const { BreadcrumbItem } = Breadcrumb;
const classPrefix = 'voice-list-add-page';

const ERROR_MSG = {
  VOICE_TOTAL_QUOTA_NOT_ENOUGH: '训练配额不足',
  VOICE_PERSONAL_QUOTA_NOT_ENOUGH: '个人训练配额不足',
} as const;

interface FormValues {
  voice_task_name: string;
  voice_sexy: '1' | '0';
  notes: string;
  selectType: 'file' | 'qrcode';
  material_voice_url?: string;
}

const TIPS_LIST = [
  {
    title: '环境安静无回音',
  },
  {
    title: '声音洪亮',
  },
  {
    title: '富有情绪',
  },
  // {
  //   title: '文件命名',
  // },
];

const rules: FormRules<FormValues> = {
  voice_task_name: [
    { required: true, message: '请输入音色名称', type: 'error' },
  ],
};

export default function VoiceAdd() {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [status, setStatus] = useState<'init' | 'submit'>('init');
  const {
    runtimeConf: {
      voiceConf: {
        voice: { voiceSubmit },
        uploadFn,
      },
    },
  } = MatchedGlobalConfigItem;
  const isAd = useMemo(() => import.meta.env.VITE_RUNNING_SYSTEM === 'AD', []);
  const handleSubmit = async () => {
    const res = await form.validate();
    if (res === true) {
      const formValue = form.getFieldsValue(true) as FormValues;
      console.debug(formValue, 'formValue');
      const { material_voice_url, voice_task_name, voice_sexy, notes } =
        formValue;
      try {
        await voiceSubmit({
          material_voice_url: material_voice_url || '',
          voice_task_name,
          voice_sexy: Number(voice_sexy),
          notes,
        });
        setStatus('submit');
      } catch (e) {
        console.error(e, '音频创建报错');
        if (e instanceof RespError) {
          const msg =
            ERROR_MSG[e.resultCode as keyof typeof ERROR_MSG] ?? '音频创建失败';
          void MessagePlugin.error(msg);
        } else {
          void MessagePlugin.error('提交失败，请稍后重试');
        }
      }
    } else {
      void MessagePlugin.error('请补充表单信息');
    }
  };
  return (
    <div className={classPrefix}>
      <div className={`${classPrefix}-breadcrumb`}>
        <Breadcrumb separator=">" maxItemWidth="140px">
          <BreadcrumbItem onClick={() => navigate('/voice-list')}>
            声音定制
          </BreadcrumbItem>
          <BreadcrumbItem>定制新声音</BreadcrumbItem>
        </Breadcrumb>
      </div>
      {status === 'init' ? (
        <>
          <Form form={form} rules={rules} labelWidth={140} labelAlign="left">
            <div className={`${classPrefix}-card`}>
              <div className="header">声音素材</div>
              <div className="content">
                <div className="sub-title mb-20">请上传/录制训练素材音频：</div>
                {/* <FormItem name="selectType" label={null} initialData="file">*/}
                {/*  <Radio.Group variant="default-filled">*/}
                {/*    <Radio.Button value="file" style={{ width: '183px' }}>*/}
                {/*      文件上传*/}
                {/*    </Radio.Button>*/}
                {/*    <Radio.Button*/}
                {/*      disabled*/}
                {/*      value="qrcode"*/}
                {/*      style={{ width: '183px' }}*/}
                {/*    >*/}
                {/*      扫码录制*/}
                {/*    </Radio.Button>*/}
                {/*  </Radio.Group>*/}
                {/* </FormItem>*/}

                {/*  upload*/}
                <div className={`${classPrefix}-upload-box`} key="file">
                  <FormItem name="material_voice_url" label={null}>
                    <VoiceUpload
                      addIcon
                      prefix="https:"
                      onFail={() => {
                        void MessagePlugin.error('上传失败,请重新上传');
                      }}
                      onSuccess={() => {
                        void MessagePlugin.success('上传成功');
                      }}
                      onFileUrlChange={(url) => {
                        console.debug(url);
                      }}
                      accept="audio/*"
                      uploadFn={uploadFn}
                    >
                      {(files, api) => {
                        return {
                          MainRender: (
                            <VoiceMainRender files={files} api={api} />
                          ),
                          BottomRender: (
                            <VoiceBottomRender files={files} api={api} />
                          ),
                        };
                      }}
                    </VoiceUpload>
                  </FormItem>

                  <div className="tips-zone">
                    <div className="mb-20 font-[16px]">
                      <span style={{ color: '#000000E5' }}>正确示例</span>{' '}
                    </div>
                    <div
                      style={{ fontSize: '14px', gap: '12px 0' }}
                      className="flex flex-col"
                    >
                      {TIPS_LIST.map((item, index) => {
                        return (
                          // eslint-disable-next-line react/no-array-index-key
                          <div key={index}>
                            <div
                              className="flex"
                              // style={{ height: '32px', marginBottom: '4px' }}
                            >
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M10 18.75C14.8325 18.75 18.75 14.8325 18.75 10C18.75 5.16751 14.8325 1.25 10 1.25C5.16751 1.25 1.25 5.16751 1.25 10C1.25 14.8325 5.16751 18.75 10 18.75ZM6.06653 10.6997C5.82267 10.4558 5.82269 10.0604 6.06657 9.81655V9.81655C6.31044 9.57268 6.70583 9.57265 6.94974 9.81649L8.75 11.6162L13.0485 7.31722C13.2927 7.07299 13.6886 7.07298 13.9328 7.31719V7.31719C14.177 7.5614 14.177 7.95734 13.9328 8.20154L9.59392 12.5399C9.12784 13.006 8.37222 13.0059 7.90621 12.5398L6.06653 10.6997Z"
                                  fill="url(#paint0_linear_7877_22649)"
                                />
                                <defs>
                                  <linearGradient
                                    id="paint0_linear_7877_22649"
                                    x1="1.25"
                                    y1="1.25"
                                    x2="18.75"
                                    y2="1.25"
                                    gradientUnits="userSpaceOnUse"
                                  >
                                    <stop stopColor="#00C653" />
                                    <stop offset="1" stopColor="#01D766" />
                                  </linearGradient>
                                </defs>
                              </svg>

                              <span
                                style={{ color: '#000000', fontWeight: '400' }}
                                className="ml-4"
                              >
                                {item.title}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                      <div className="mt-20">
                        参阅详细的&nbsp;
                        <LinkButton
                          theme="primary"
                          target="_blank"
                          href="https://doc.weixin.qq.com/doc/w3_AVAAYgbaAJ0A3nqHsrlRMeHjh1yqv?scode=AJEAIQdfAAotft1BRVAT0AsQZXANA"
                        >
                          指引文档
                        </LinkButton>
                        &nbsp;以获取更多信息。
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className={`${classPrefix}-card mt-20`}>
              <div className="header">声音基本信息</div>
              <div className="content">
                <div className="sub-title mb-20">请完善数字人音色信息：</div>
                <FormItem name="voice_task_name" label="数字人音色名称">
                  <Input
                    style={{ width: '375px' }}
                    clearable
                    placeholder="请命名，如：小明"
                  />
                </FormItem>
                <FormItem name="voice_sexy" label="音色性别" initialData="1">
                  <Radio.Group variant="default-filled">
                    <Radio.Button value="1" style={{ width: '185px' }}>
                      男性
                    </Radio.Button>
                    <Radio.Button value="0" style={{ width: '185px' }}>
                      女性
                    </Radio.Button>
                  </Radio.Group>
                </FormItem>
                <FormItem name="notes" label="描述">
                  <Input
                    style={{ width: '375px' }}
                    clearable
                    placeholder="请输入描述"
                  />
                </FormItem>
              </div>
            </div>
            <div style={{ height: '60px' }} />
          </Form>
          <div className={`${classPrefix}-footer`}>
            <Button
              size="large"
              theme="default"
              className="gradient-primary"
              style={{ width: 120 }}
              onClick={() => {
                handleSubmit().then();
              }}
            >
              确认提交
            </Button>
            <Button
              size="large"
              theme="default"
              className="gradient-default"
              style={{ width: 120 }}
              onClick={() => navigate('/voice-list')}
            >
              返回
            </Button>
          </div>
        </>
      ) : (
        <div className={`${classPrefix}-submit-card`}>
          <TrainResultPage
            tips="接下会经过以下流程，您可以前往声音定制管理页查看进度"
            onLink={() => navigate(`/voice-list${isAd ? '?tab=task' : ''}`)}
          />
        </div>
      )}
    </div>
  );
}
