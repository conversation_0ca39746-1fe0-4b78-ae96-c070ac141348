.voice-list-add-page {
  position: relative;

  &-breadcrumb{
    color: rgba(0, 0, 0, 0.90);
    font-weight: 600;
    margin-bottom: 16px;
  }

  &-card {
    background: #ffffff;
    border-radius: 8px;

    .header {
      height: 60px;
      line-height: 60px;
      padding: 0 22px;
      font-size: 18px;
      font-weight: 500;
      color: #000000;
      border-bottom: 1px solid #E2E8F3;
    }

    .content {
      padding: 24px 22px;

      .sub-title {
        color: rgba(0, 0, 0, 0.90);
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
      }
    }

    .t-form {
      .t-form__label {
        color: rgba(0, 0, 0, 0.6);
      }
    }

    .t-radio-group {
      border-radius: 3px;
      background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);

      .t-radio-button__label {
        width: 100%;
        text-align: center;
      }
    }

    .t-radio-group.t-radio-group--filled .t-radio-button.t-is-checked {
      color: #0047F9;
    }
  }

  &-upload-box {
    display: flex;
    border-radius: 4px;
    padding: 20px;
    background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);

    .tips-zone {
      display: flex;
      flex-direction: column;
      justify-content: start;
      margin-left: 36px;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }

    .draggable-upload-comp {
      .t-upload__dragger {
        width: 380px;
        height: 280px;
        background: #ffffff;
      }
    }
  }

  &-footer {
    display: flex;
    gap: 12px;
    //padding: 20px 20px 20px 40px;
    padding: 16px 36px;
    position: fixed;
    background: #fff;
    bottom: 0;
    width: 100%;
    margin-left: -40px;
  }

  &-submit-card {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: #FFFFFF;
    height: calc(100vh - 140px);
  }
}
