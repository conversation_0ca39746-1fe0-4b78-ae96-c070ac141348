/**
 * <AUTHOR>
 * @date 2025/2/21 14:38
 * @desc index
 */

import React from 'react';
import { AppNavBar } from '@tencent/creative-mb';
import { LoginStateAtom } from '@/model/login';
import { useRecoilState, useRecoilValue } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { MessageCenter } from '@/pages/MuseLogin/MuseNavBar/MessageCenter';
import { UserCenter } from '@/pages/MuseLogin/MuseNavBar/UserCenter';
import { InviteCodeIcon } from '@/pages/MuseLogin/MuseNavBar/InviteCodeIcon';
import { HelpCenter } from '@/pages/MuseLogin/MuseNavBar/HelpCenter';
import { MEMBER_ROLE } from '@/pages/MuseLogin/config';
import { UserInfo } from '@/components/UserInfo';

interface IProps {
  onRegister: () => void;
  onLoginOut: () => void;
  logoUrl?: string;
  useTransitionStyle: boolean;
}

export function MuseNavBar(props: IProps) {
  const { onLoginOut, onRegister, useTransitionStyle, logoUrl } = props;
  const loginState = useRecoilValue(LoginStateAtom);
  const [userState] = useRecoilState(UserInfoAtom);

  return (
    <AppNavBar
      logoUrl={logoUrl}
      useTransitionStyle={useTransitionStyle}
      data={[]}
      rightChildren={
        <>
          {/* 帮助中心*/}
          <HelpCenter
            x-if={loginState}
            useTransitionStyle={useTransitionStyle}
          />

          {/* 通知下拉菜单 */}
          <MessageCenter
            x-if={
              loginState &&
              userState?.adExtend?.account_id &&
              userState?.adExtend?.roles.includes(MEMBER_ROLE.ADMIN)
            }
            useTransitionStyle={useTransitionStyle}
          />
          {/* 复制邀请链接,管理员可操作 */}
          <InviteCodeIcon
            useTransitionStyle={useTransitionStyle}
            x-if={loginState}
          />
          {/* 个人中心*/}
          {/*  FIXME: 过渡显示*/}
          <UserInfo
            x-if={useTransitionStyle}
            userInfo={{
              avatar: userState?.avatar || '',
              nickname: userState?.nickname || '',
            }}
            nickNameStyle={{
              lineHeight: '22px',
              color: 'rgba(0, 0, 0, 0.9)',
              fontWeight: 400,
            }}
          />
          <UserCenter
            x-if={!useTransitionStyle && userState && loginState}
            useTransitionStyle={useTransitionStyle}
            onRegister={onRegister}
            onLoginOut={onLoginOut}
          />
        </>
      }
    />
  );
}
