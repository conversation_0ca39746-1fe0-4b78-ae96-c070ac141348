/**
 * <AUTHOR>
 * @date 2025/3/2 16:21
 * @desc HelpCenter
 */

import React, { useEffect, useMemo } from 'react';
import { AppNavRightActionHelp } from '@tencent/creative-mb';
import { MUSE_HELPER_QRCODE } from '@/pages/MuseLogin/const';
import { useAIServerCode } from '@/components/AIServerCode/hooks/useAIServerCode';
import { useRecoilValue } from 'recoil';
import { LoginStateAtom } from '@/model/login';

interface IProps {
  useTransitionStyle: boolean;
}

export function HelpCenter(props: IProps) {
  const { useTransitionStyle } = props;
  const { qrcodeUrl, initCode } = useAIServerCode();
  const loginState = useRecoilValue(LoginStateAtom);

  useEffect(() => {
    if (!loginState) return;
    initCode().then();
  }, [initCode, loginState]);

  const conf = useMemo(() => {
    return [
      {
        imgUrl: MUSE_HELPER_QRCODE,
        text: '联系小助手',
      },
      {
        imgUrl: qrcodeUrl,
        text: '了解新动态',
      },
    ];
  }, [qrcodeUrl]);

  return (
    <AppNavRightActionHelp
      useTransitionStyle={useTransitionStyle}
      data={conf}
    />
  );
}
