/**
 * <AUTHOR>
 * @date 2025/2/28 15:06
 * @desc InviteCodeIcon
 */

import React from 'react';
import { AppNavRightActionCopyLink } from '@tencent/creative-mb';
import { MEMBER_ROLE } from '@/pages/MuseLogin/config';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { message } from '@tencent/spaui';
import to from 'await-to-js';
import { GetInvitationCode } from '@/pb/api/AuthAdminSvr_1';
import copy from 'copy-to-clipboard';

interface IProps {
  useTransitionStyle: boolean;
}

export function InviteCodeIcon(props: IProps) {
  const { useTransitionStyle } = props;
  const [userState] = useRecoilState(UserInfoAtom);

  // 获取邀请链接
  const queryInvitationCode = async () => {
    if (!userState?.adExtend?.roles.includes(MEMBER_ROLE.ADMIN)) {
      message.warn('非管理员无法创建邀请码');
      return;
    }
    const [, res] = await to(GetInvitationCode({}));
    if (res?.invitation_code) {
      // copy(
      //   `${location.origin}${location.pathname}#/?invite_code=${res.invitation_code}`
      // );
      copy(
        `${location.origin}${location.pathname}?invite_code=${res.invitation_code}`
      );
      message.success('邀请链接已复制到剪切板');
    } else {
      message.error('获取邀请链接失败');
    }
  };

  return (
    <AppNavRightActionCopyLink
      useTransitionStyle={useTransitionStyle}
      x-if={userState?.adExtend?.roles.includes(MEMBER_ROLE.ADMIN)}
      popOverText="复制邀请链接"
      onCopy={() => queryInvitationCode()}
    />
  );
}
