/**
 * <AUTHOR>
 * @date 2025/2/27 20:41
 * @desc NoticeAction
 */

import React, { useEffect } from 'react';
import {
  AppNavRightActionNotice,
  AppnavRightNoticeDetail,
} from '@tencent/creative-mb';
import { ACCOUNT_STATUS, APPROVE_STATUS_MAP } from '@/pages/MuseLogin/config';
import { useApproveSubmit } from '@/pages/MuseLogin/hooks/useApprove';
import to from 'await-to-js';
import { message } from '@tencent/spaui';
import { useResetState } from 'ahooks';
import { useMuseApproveListRequest } from '@/pages/MuseLogin/hooks/useApproveList';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';

interface IProps {
  useTransitionStyle: boolean;
}

interface ReviewMessage {
  time: string;
  title: string;
  content: string;
}

export function MessageCenter(props: IProps) {
  const { useTransitionStyle } = props;
  const [userState] = useRecoilState(UserInfoAtom);

  const {
    isEnd,
    records,
    loading,
    resetData,
    queryParams,
    recordCount,
    setQueryParams,
  } = useMuseApproveListRequest({
    queryParamsDefault: {
      pageNum: 1,
      pageSize: 10,
    },
  });
  // 协作者申请审批
  const { runAsync: submitApprove } = useApproveSubmit();
  const [reviewMessage, , resetReviewMessage] =
    useResetState<ReviewMessage | null>(null);

  // 处理协作者审批
  const handleApproval = async (approveId: string, status: ACCOUNT_STATUS) => {
    const [err] = await to(
      submitApprove({
        approveId,
        status,
        comments: '',
      })
    );
    if (!err) {
      message.success('操作成功');
      resetData();
    } else {
      console.error(err);
      message.error('操作失败, 请稍后重试');
    }
  };

  useEffect(() => {
    // if (!userState?.adExtend?.account_id) return;
    resetData();
  }, [resetData, userState?.adExtend?.account_id]);

  return (
    <>
      <AppNavRightActionNotice
        useTransitionStyle={useTransitionStyle}
        data={records.map((item) => ({
          id: item.approve_id,
          text: `${item.user_nick_name}申请成为账户协作者`,
          showBadge: item.status === ACCOUNT_STATUS.PENDING,
          time: item.createTime,
          status: APPROVE_STATUS_MAP[item.status as ACCOUNT_STATUS],
        }))}
        isEnd={isEnd || records.length >= recordCount}
        onNext={() => {
          if (isEnd) return;
          if (records.length >= recordCount) return;
          if (loading) return;
          setQueryParams({
            ...queryParams,
            pageNum: (queryParams.pageNum ?? 1) + 1,
          });
        }}
        onAgree={async (val) => {
          handleApproval(val.id, ACCOUNT_STATUS.APPROVED).then();
        }}
        onReject={async (val) => {
          handleApproval(val.id, ACCOUNT_STATUS.REJECTED).then();
        }}
        // onPreview={(val) => {
        //   setReviewMessage({
        //     time: '02-15 15:37:20',
        //     title: '巴黎欧莱雅（中国）直播官号申请成为账户协作者',
        //     content: '申请成为账户协作者',
        //   });
        //   console.log('预览', val);
        // }}
        showBadge={records.some(
          (item) => item.status === ACCOUNT_STATUS.PENDING
        )}
      />

      {/* 通知消息预览详情 */}
      <AppnavRightNoticeDetail
        useTransitionStyle={useTransitionStyle}
        show={!!reviewMessage}
        onClose={() => resetReviewMessage()}
        time={reviewMessage?.time}
        title={reviewMessage?.title}
        content={reviewMessage?.content}
      />
    </>
  );
}
