/**
 * <AUTHOR>
 * @date 2025/2/28 13:20
 * @desc UserCenter
 */

import React, { useMemo } from 'react';
import { AppNavRightActionUserInfo } from '@tencent/creative-mb';
import { useRecoilState, useRecoilValue } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { useMuseAccountListRequest } from '@/pages/MuseLogin/hooks/useAccountList';
import { useUpdateAccount } from '@/pages/MuseLogin/hooks/useAccountInfo';

interface IProps {
  useTransitionStyle: boolean;
  onLoginOut: () => void;
  onRegister: () => void;
}

export function UserCenter(props: IProps) {
  const { useTransitionStyle, onRegister, onLoginOut } = props;
  const loginState = useRecoilValue(LoginStateAtom);
  const [userState] = useRecoilState(UserInfoAtom);
  const { updateAccountInfo } = useUpdateAccount();

  const { records, recordCount, setQueryParams, queryParams } =
    useMuseAccountListRequest({
      queryParamsDefault: {
        status: 'APPROVED',
        searchKey: '',
        pageSize: 999,
      },
      mode: 'all',
    });

  // 用户信息
  const userInfo = useMemo(() => {
    let userId = loginState?.openkey || '';
    if (userState?.adExtend?.login_type === 'wx') {
      userId = `微信号：${userId}`;
    } else {
      userId = `QQ号：${userId}`;
    }
    return {
      avatar: userState?.avatar ?? '',
      nickname: userState?.nickname ?? '',
      id: userId,
      currentAccount: userState?.adExtend?.account_id ?? '',
    };
  }, [
    loginState?.openkey,
    userState?.adExtend?.account_id,
    userState?.adExtend?.login_type,
    userState?.avatar,
    userState?.nickname,
  ]);

  // 协议转换
  const accountList = useMemo(() => {
    return records.map((item) => ({
      imgUrl: item.avatar_url,
      name: item.nick_name,
      id: item.app_id,
      item,
    }));
  }, [records]);

  return (
    <AppNavRightActionUserInfo
      useTransitionStyle={useTransitionStyle}
      data={accountList}
      isEmpty={!recordCount}
      emptyContent="暂无账户信息"
      isEnd
      accountName={userInfo.nickname}
      imgUrl={userInfo.avatar}
      contactText={userInfo.id}
      onRegister={onRegister}
      onLogOut={onLoginOut}
      inputValue={queryParams.searchKey}
      placeholder="请输入账户名称"
      onInputChange={(val) => {
        setQueryParams({ ...queryParams, searchKey: val, pageNum: 1 });
      }}
      currentId={userInfo.currentAccount}
      onSelect={(val) => {
        updateAccountInfo(val.id).then();
      }}
    />
  );
}
