/**
 * <AUTHOR>
 * @date 2025/3/7 15:31
 * @desc Login
 */

import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
  AppContainer,
  LoginTabs,
  LoginType,
  MbTabsPanel,
  WelcomeLoginTabWrap,
  WelcomePage,
  WelcomeQQLogin,
} from '@tencent/creative-mb';
import { MuseQQSSO } from '@/pages/MuseLogin/Login/MuseQQSSO';
import { MuseWXSSO } from '@/pages/MuseLogin/Login/MuseWXSSO';
import { useRecoilState } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useMount } from 'ahooks';
import to from 'await-to-js';
import { LoginContext } from '@/hooks/login';
import { UserInfoAtom } from '@/model/user';
import { LoginApiAtom } from '@/model/api';

export function MuseSSOLogin() {
  const loginContext = useContext(LoginContext);
  const [loginType, setLoginType] = useState('wx');
  const [loginState] = useRecoilState(LoginStateAtom);
  const navigate = useNavigate();
  const [userState] = useRecoilState(UserInfoAtom);
  const [searchParams] = useSearchParams();
  const [loginApi] = useRecoilState(LoginApiAtom);

  // 有登陆态，直接跳转首页
  useEffect(() => {
    const loginType = searchParams.get('login_type');
    if (loginType === 'qq_error') {
      setLoginType('qq');
      return;
    }
    if (loginState) {
      navigate('/');
    }
  }, [loginState, navigate, searchParams]);

  const qqLoginRes = useMemo(() => {
    if (userState?.adExtend?.login_type === 'qq_error') {
      return 'qq_error';
    }
    return 'qq';
  }, [userState?.adExtend?.login_type]);

  useEffect(() => {
    const sendAnalytics = () => {
      if (qqLoginRes !== 'qq_error') return;
      const apiUrl = `/intelligent/muse/user/logout`;
      fetch(apiUrl, {
        method: 'GET',
      }).then(); // 静默处理错误
    };
    // 注册页面卸载事件
    window.addEventListener('beforeunload', sendAnalytics);

    return () => {
      // 组件卸载时：
      // 1. 移除事件监听
      window.removeEventListener('beforeunload', sendAnalytics);
      // 2. 立即执行发送
      sendAnalytics();
    };
  }, [qqLoginRes]);

  // 获取一次个人信息
  useMount(async () => {
    if (!loginContext || loginContext.isFetchingLogin) return;
    if (loginState) return;
    await to(loginContext?.fetchLoginInfo({ noCache: true }));
  });

  return (
    <AppContainer>
      <div className="flex items-center justify-center height-100 w-100">
        <WelcomePage
          value={loginType}
          onChange={(_: unknown, val: string) => {
            setLoginType(val);
          }}
        >
          <MbTabsPanel tab={<LoginTabs type={LoginType.WECHAT} />} name="wx">
            <WelcomeLoginTabWrap>
              <MuseWXSSO x-if={loginType === 'wx'} />
            </WelcomeLoginTabWrap>
          </MbTabsPanel>

          <MbTabsPanel tab={<LoginTabs type={LoginType.QQ} />} name="qq">
            <WelcomeLoginTabWrap>
              <MuseQQSSO x-if={loginType === 'qq' && qqLoginRes === 'qq'} />
              <WelcomeQQLogin
                x-else
                imgUrl={userState?.avatar}
                onClick={() => {
                  loginApi
                    ?.goToLoginOut({
                      disablelogoutIndex: true,
                      disableRedirect: true,
                    })
                    .then(() => {
                      window.location.replace('https://e.qq.com/reg-new/');
                      return void 0;
                    });
                }}
              />
            </WelcomeLoginTabWrap>
          </MbTabsPanel>
        </WelcomePage>
      </div>
    </AppContainer>
  );
}
