/* eslint-disable @typescript-eslint/ban-ts-comment */
/**
 * <AUTHOR>
 * @date 2025/2/19 11:58
 * @desc AccountSelect
 */

import React, { useEffect, useMemo, useState } from 'react';
import {
  MbAccountReviewDialog,
  MbRejectDialog,
  SelectAccount,
} from '@tencent/creative-mb';
import {
  IMuseAccountRecord,
  useMuseAccountListRequest,
} from '@/pages/MuseLogin/hooks/useAccountList';
import {
  ACCOUNT_STATUS,
  ACCOUNT_STATUS_MAP,
  MEMBER_ROLE,
  MEMBER_ROLE_MAP,
} from '@/pages/MuseLogin/config';
import { MemberRoleType } from '@/pages/MuseLogin/types';
import { MUSE_REGISTER_GUIDE } from '@/pages/MuseLogin/const';
import { useNavigate } from 'react-router-dom';
import { useUpdateAccount } from '@/pages/MuseLogin/hooks/useAccountInfo';
import { useResetState } from 'ahooks';
import { message } from '@tencent/spaui';

interface IProps {
  onRegister: (appId?: string) => void;
  onLoginOut: () => void;
  // onEmpty: () => void;
}

/**
 * 账户选择，当前组件是全部加载
 * @param props
 * @constructor
 */
export function AccountSelect(props: IProps) {
  const { onRegister, onLoginOut } = props;
  const navigate = useNavigate();
  const { updateAccountInfo } = useUpdateAccount();
  const [accountReviewDialogVisible, setAccountReviewDialogVisible] =
    useState(false);
  const [reviewRejectReason, setReviewRejectReason, resetReviewRejectReason] =
    useResetState('');
  const [currentSelectAppId, setCurrentSelectAppId] = useState('');
  const handleSelectAccount = async (account: IMuseAccountRecord) => {
    setCurrentSelectAppId(account.app_id);
    if (account.status === ACCOUNT_STATUS.PENDING) {
      setAccountReviewDialogVisible(true);
    } else if (account.status === ACCOUNT_STATUS.REJECTED) {
      if (account.role_ids.includes(MEMBER_ROLE.COLLABORATOR)) {
        message.error('协作者申请未通过');
        return;
      }
      setReviewRejectReason(account.comments || '资料未通过审核，请重新提交');
    } else {
      await updateAccountInfo(account.app_id);
      navigate('/ad-list');
    }
  };

  const { records, setQueryParams, queryParams, refresh } =
    useMuseAccountListRequest({
      queryParamsDefault: {
        searchKey: '',
        pageSize: 999,
      },
      mode: 'all',
    });

  // 定时器监听，每过3s刷新一次
  useEffect(() => {
    const timer = setInterval(() => {
      refresh();
    }, 3000);
    return () => {
      clearInterval(timer);
    };
  }, [refresh]);

  // 协议转换
  const accountList = useMemo(() => {
    return records.map((item) => ({
      imgUrl: item.avatar_url,
      name: item.nick_name || '未命名',
      id: item.app_id,
      position: item.role_ids
        .map((item) => MEMBER_ROLE_MAP[item as MemberRoleType])
        .join('-'),
      status: ACCOUNT_STATUS_MAP[item.status as ACCOUNT_STATUS],
      item,
    }));
  }, [records]);

  return (
    <>
      <SelectAccount
        value={queryParams.searchKey}
        onChange={(_, val) => {
          setQueryParams({ ...queryParams, searchKey: val });
        }}
        onClear={() => setQueryParams({ ...queryParams, searchKey: '' })}
        onLogOut={onLoginOut}
        problemImgUrl={MUSE_REGISTER_GUIDE}
        onSelect={(i) => {
          handleSelectAccount(i.item).then();
        }}
        // @ts-ignore
        data={accountList}
        onRegister={() => {
          onRegister();
        }}
      />
      {/* 账户审批弹窗*/}
      <MbAccountReviewDialog
        imgUrl={MUSE_REGISTER_GUIDE}
        show={accountReviewDialogVisible}
        cancelButton={null}
        closeButton={false}
        onSubmit={() => {
          setAccountReviewDialogVisible(false);
        }}
      />
      <MbRejectDialog
        onClose={resetReviewRejectReason}
        onCancel={resetReviewRejectReason}
        submitLabel="重新提交"
        onSubmit={() => {
          resetReviewRejectReason();
          if (currentSelectAppId) {
            onRegister(currentSelectAppId);
          }
        }}
        content={reviewRejectReason}
        show={!!reviewRejectReason}
      />
    </>
  );
}
