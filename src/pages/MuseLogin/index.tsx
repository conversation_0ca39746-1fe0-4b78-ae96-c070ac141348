/**
 * <AUTHOR>
 * @date 2025/2/17 17:46
 * @desc index
 */

import React, { useContext, useState } from 'react';
import { AppContainer, MbLogOutDialog } from '@tencent/creative-mb';
import { AccountSelect } from '@/pages/MuseLogin/AccountSelect';
import { AgreementSign } from '@/pages/MuseLogin/AgreementSign';
import { AccountRegister } from '@/pages/MuseLogin/AccountRegister';
import { TeamworkerApply } from '@/pages/MuseLogin/TeamworkerApply';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { MuseNavBar } from '@/pages/MuseLogin/MuseNavBar';
import { LoginContext } from '@/hooks/login';
import { useRecoilState } from 'recoil';
import { LoginApiAtom } from '@/model/api';
import { LoginStateAtom } from '@/model/login';
import { message } from '@tencent/spaui';
import { useMount, useResetState } from 'ahooks';
import to from 'await-to-js';
import { CheckAgreementStatus } from '@/pb/api/AuthAdminSvr_3';
import { MUSE_AGREEMENT_ID } from '@/pages/MuseLogin/const';
import { UserInfoAtom } from '@/model/user';
import { useMuseAccountListRequest } from '@/pages/MuseLogin/hooks/useAccountList';
import './style/spaui.css';
import './style/odc.css';
import './style/index.css';
import { getParam } from '@tencent/midas-util';

export enum LOGIN_STEP {
  INIT = 'INIT',
  AGREEMENT = 'AGREEMENT',
  SELECT_ACCOUNT = 'SELECT_ACCOUNT',
  REGISTER = 'REGISTER',
  TEAMWORKER_APPLY = 'TEAMWORKER_APPLY',
}

export function MuseLogin() {
  const loginContext = useContext(LoginContext);
  const [loginState] = useRecoilState(LoginStateAtom);
  const [userState] = useRecoilState(UserInfoAtom);
  const [loginApi] = useRecoilState(LoginApiAtom);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<LOGIN_STEP>(LOGIN_STEP.INIT);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  const { run: queryAccountList } = useMuseAccountListRequest({
    queryParamsDefault: {
      searchKey: '',
      pageSize: 1,
    },
    mode: 'try',
  });

  const [businessState, setBusinessState, resetBusinessState] = useResetState({
    inviteCode: '',
    selectAppId: '',
  });

  // 逻辑处理
  const handleInit = async () => {
    // 判断是否签署协议，如果没有，跳转到协议签署
    const [, res] = await to(
      CheckAgreementStatus({
        agreement_id: MUSE_AGREEMENT_ID,
      })
    );
    if (!res?.is_signed) {
      setCurrentStep(LOGIN_STEP.AGREEMENT);
      return;
    }
    // 协作者模式判断
    const inviteCode = getParam('invite_code');
    // const inviteCode = searchParams.get('invite_code');
    if (inviteCode) {
      console.debug('邀请码为', inviteCode);
      setBusinessState((prev) => ({ ...prev, inviteCode }));
      setCurrentStep(LOGIN_STEP.TEAMWORKER_APPLY);
      return;
    }
    // 指定步骤判断
    const step = searchParams.get('step');
    if (step) {
      setCurrentStep(step as LOGIN_STEP);
      location.hash = '/';
      return;
    }
    //   判断当前是否有选择账号，有直接前往直播列表 如果没有，跳转到选择账号
    if (userState?.adExtend?.account_id) {
      navigate('/ad-list');
      return;
    }
    // 请求账号列表，判断是否有账号，没有跳转到注册
    handleStepLink().then();
  };

  const handleStepLink = async () => {
    const [, accountList] = await to(queryAccountList());
    if (!!accountList?.count) {
      setCurrentStep(LOGIN_STEP.SELECT_ACCOUNT);
    } else {
      setCurrentStep(LOGIN_STEP.REGISTER);
    }
  };

  // 退出登录
  const handleLoginOut = () => {
    setLogoutDialogVisible(false);
    console.log(loginState);
    message.success('退出成功');

    loginApi
      ?.goToLoginOut({
        disableRedirect: true,
      })
      .then(() => {
        location.hash = '#/login';
      });
  };

  // 获取一次个人信息
  useMount(async () => {
    if (!loginContext || loginContext.isFetchingLogin) return;
    const [, info] = await to(loginContext?.fetchLoginInfo({ noCache: true }));
    // 没登录，跳转登录
    if (!info) {
      navigate('/login');
      return;
    }
    if (info.loginUserInfo.adExtend?.login_type === 'qq_error') {
      navigate('/login?login_type=qq_error');
      return;
    }
    handleInit().then();
  });

  return (
    <AppContainer className="ad-muse-insert-style">
      <MuseNavBar
        useTransitionStyle={false}
        onRegister={() => {
          setCurrentStep(LOGIN_STEP.REGISTER);
        }}
        onLoginOut={() => setLogoutDialogVisible(true)}
      />
      <div className="flex items-center justify-center height-100 ">
        {/* 协议签署*/}
        <AgreementSign
          x-if={currentStep === LOGIN_STEP.AGREEMENT}
          onLoginOut={() => setLogoutDialogVisible(true)}
          onSuccess={() => {
            // 请求账号列表，判断是否有账号，没有跳转到注册
            handleStepLink().then();
          }}
        />
        {/* 账号选择*/}
        <AccountSelect
          x-if={currentStep === LOGIN_STEP.SELECT_ACCOUNT}
          onLoginOut={() => setLogoutDialogVisible(true)}
          onRegister={(appId) => {
            setBusinessState((prev) => ({ ...prev, selectAppId: appId || '' }));
            setCurrentStep(LOGIN_STEP.REGISTER);
          }}
        />
        {/* 商户注册*/}
        <AccountRegister
          x-if={currentStep === LOGIN_STEP.REGISTER}
          appId={businessState.selectAppId}
          onLoginOut={() => setLogoutDialogVisible(true)}
          onSuccess={() => {
            setCurrentStep(LOGIN_STEP.SELECT_ACCOUNT);
            resetBusinessState();
          }}
          onReset={resetBusinessState}
        />
        {/* 协作者申请*/}
        <TeamworkerApply
          inviteCode={businessState.inviteCode}
          x-if={
            currentStep === LOGIN_STEP.TEAMWORKER_APPLY &&
            businessState.inviteCode
          }
          onLoginOut={() => setLogoutDialogVisible(true)}
          onSuccess={() => {
            setCurrentStep(LOGIN_STEP.SELECT_ACCOUNT);
            resetBusinessState();
            location.hash = '/';
          }}
        />

        {/* 退出登录弹窗 */}
        <MbLogOutDialog
          show={logoutDialogVisible}
          onCancel={() => {
            setLogoutDialogVisible(false);
          }}
          onSubmit={() => handleLoginOut()}
          closeButton={false}
        />
      </div>
    </AppContainer>
  );
}
