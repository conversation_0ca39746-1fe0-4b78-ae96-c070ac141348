/**
 * <AUTHOR>
 * @date 2025/3/7 16:31
 * @desc MuseSSOIFrame
 */

const MUSE_SSO_WX_CONF = {
  // self_redirect: true,
  // 申请微信公众号,生成唯一的appid
  appid: 'wx708c87b4f90c2b12',
  scope: 'snsapi_login',
  // 回调地址, 验证code, 然后返回在url地址中, 需要
  redirect_uri: encodeURIComponent(
    `https://sso.e.qq.com/login/callback?login_type=2&mini_query_switch=true&service_tag=97&sso_redirect_uri=${encodeURIComponent(
      `${
        window.location.origin
      }/intelligent/muse/user/login?url=${encodeURIComponent(
        `${location.origin}${location.pathname}${location.search}#/`
      )}`
    )}`
  ),
  // 随机数, 验证
  state: Math.random(),
  // 生成微信文字的颜色
  style: 'black',
  // 链接地址加载自定义css样式,覆盖微信默认样式, 这个css必须是网上地址,而且是https开头的 本地测试无效
  // 如果不是https，可以采用编码方式控制样式
  href: 'data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDEzNHB4O30gLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lO30gLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAyMDBweDt9IC5zdGF0dXNfaWNvbiB7ZGlzcGxheTogbm9uZX0gLmltcG93ZXJCb3ggLnN0YXR1cyB7dGV4dC1hbGlnbjogY2VudGVyO30gLmltcG93ZXJCb3ggLnN0YXR1cyBwOmxhc3QtY2hpbGQgeyBkaXNwbGF5Om5vbmU7IH0=',
};

export function MuseWXSSO() {
  const ssoWX = `https://open.weixin.qq.com/connect/qrconnect?appid=${MUSE_SSO_WX_CONF.appid}&redirect_uri=${MUSE_SSO_WX_CONF.redirect_uri}&scope=${MUSE_SSO_WX_CONF.scope}&state=${MUSE_SSO_WX_CONF.state}&login_type=jssdk&self_redirect=default&style=${MUSE_SSO_WX_CONF.style}&href=${MUSE_SSO_WX_CONF.href}`;

  return (
    <iframe
      title="sso登录"
      src={ssoWX}
      style={{
        width: 400,
        height: 356,
        transform: 'translate(0px, 64px)',
      }}
    />
  );
}
