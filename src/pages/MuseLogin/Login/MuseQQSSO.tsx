/**
 * <AUTHOR>
 * @date 2025/3/7 16:31
 * @desc MuseSSOIFrame
 */

import React, { useEffect, useMemo } from 'react';

export function MuseQQSSO() {
  const ssoQQ = useMemo(() => {
    const ssoRedirectUriQQ = `${
      window.location.origin
    }/intelligent/muse/user/login?url=${encodeURIComponent(
      `${location.protocol}//${location.host}${location.pathname}${location.search}#/login_callback`
    )}`;
    const queryQQ = `sso_redirect_uri=${encodeURIComponent(
      ssoRedirectUriQQ
    )}&service_tag=97&redirect_target=self`;
    return `https://sso.e.qq.com/login/qq?${queryQQ}`;
  }, []);

  useEffect(() => {
    const handler = (e: MessageEvent) => {
      if (e.data.status === 'success' && e.origin === window.location.origin) {
        // window.location.reload();
        window.location.replace(e.data.url);
      }
    };
    window.addEventListener('message', handler);
    return () => {
      window.removeEventListener('message', handler);
    };
  }, []);

  return (
    <iframe
      title="sso登录"
      src={ssoQQ}
      scrolling="no"
      style={{
        overflow: 'hidden',
        width: 3000,
        height: 1000,
        transform: 'translate(-15px, 277px)',
      }}
    />
  );
}
