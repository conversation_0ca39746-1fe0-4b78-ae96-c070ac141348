/* eslint-disable @typescript-eslint/ban-ts-comment */
/**
 * <AUTHOR>
 * @date 2025/2/19 14:52
 * @desc AccountRegister
 */

import React, { Fragment, useMemo, useRef, useState } from 'react';
import {
  IconMerchantAccount,
  IconProAccount,
  LoginInput,
  LoginInputAccountPopOver,
  LoginInputShopPopOver,
  LoginPreviewImg,
  LoginRegister,
  LoginUpload,
  MbAccountReviewDialog,
} from '@tencent/creative-mb';
import Schema from 'async-validator';
import { useAsyncEffect, useResetState } from 'ahooks';
import { Rules } from 'async-validator/dist-types/interface';
import to from 'await-to-js';
import { uploadRequest } from '@/utils/cos';
import { MATERIAL_TYPE } from '@/configs/upload';
import { REGISTER_TYPE, useRegisterQRCode } from './hooks/useAccount';
import { ACCOUNT_ROLE } from './config';
import { message } from '@tencent/spaui';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import {
  MUSE_REGISTER_GUIDE,
  PATTERNS,
  REGISTER_ERROR_MESSAGE,
  REGISTER_TALENT_GUIDE,
} from '@/pages/MuseLogin/const';
import { useAccountRegister } from '@/pages/MuseLogin/hooks/useAccountRegister';
import { GetAccountInfo } from '@/pb/api/AuthAdminSvr';
import { ValidatorRes } from '@/pages/MuseLogin/types';
import { RespError } from '@/pb/config';

enum LoginUploadStatus {
  INIT = '',
  LOADING = 'loading',
  SUCCESS = 'success',
}

interface IProps {
  appId?: string;
  onSuccess: () => void;
  onLoginOut: () => void;
  onReset: () => void;
}

interface ICommercialOwner {
  telPhone: string;
  wechatOrQQ: string;
  shopId: string;
  shopSecret: string;
  license: string;
}

interface ITalent {
  telPhone: string;
  wechatOrQQ: string;
  accountId: string;
  accountSecret: string;
}

const commercialOwnerFormRules: Rules = {
  telPhone: [
    { required: true, message: '请输入手机号' },
    { pattern: PATTERNS.mobile, message: '请输入正确的手机号' },
  ],
  wechatOrQQ: [{ required: true, message: '请补全账号信息' }],
  shopId: [{ required: true, message: '请输入小店 id' }],
  shopSecret: [{ required: true, message: '请输入小店密钥' }],
  // license: [{ required: true, message: '请上传营业执照' }],
};

const talentFormRules: Rules = {
  accountId: [{ required: true, message: '请输入账号id' }],
  accountSecret: [{ required: true, message: '请输入账号密钥' }],
  telPhone: [
    { required: true, message: '请输入手机号' },
    { pattern: PATTERNS.mobile, message: '请输入正确的手机号' },
  ],
  wechatOrQQ: [{ required: true, message: '请补全账号信息' }],
};

const commercialOwnerValidator = new Schema(commercialOwnerFormRules);
const talentValidator = new Schema(talentFormRules);

export function AccountRegister(props: IProps) {
  const { onSuccess, onLoginOut, appId, onReset } = props;

  const [userState] = useRecoilState(UserInfoAtom);
  const [previewImgUrl, setPreviewImgUrl] = useState<string[]>([]);
  const [registerType, setRegisterType] = useState<REGISTER_TYPE>(
    REGISTER_TYPE.CommercialOwner
  );
  const [errorText, setErrorText, resetErrorText] = useResetState<Record<
    string,
    string
  > | null>(null);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [accountReviewDialogVisible, setAccountReviewDialogVisible] =
    useState(false);

  const loginType = useMemo(() => {
    return userState?.adExtend?.login_type || 'qq';
  }, [userState?.adExtend?.login_type]);

  const { submitRegister } = useAccountRegister();
  const {
    qrcode,
    getQRCode,
    renderValue,
    errMessage,
    reset: resetQrCode,
  } = useRegisterQRCode(registerType);

  // 是否提交过，校验不过，这个标识用于判断输入失焦点时，是否需要重新校验
  const submittedRef = useRef(false);

  const [
    commercialOwnerForm,
    setCommercialOwnerForm,
    resetCommercialOwnerForm,
  ] = useResetState<ICommercialOwner>({
    telPhone: '',
    wechatOrQQ: '',
    shopId: '',
    shopSecret: '',
    license: '',
  });

  const [talentForm, setTalentForm, resetTalentForm] = useResetState<ITalent>({
    telPhone: '',
    wechatOrQQ: '',
    accountId: '',
    accountSecret: '',
  });

  const [uploadState, setUploadState, resetUploadState] = useResetState<{
    status: LoginUploadStatus;
    fileName: string;
  }>({
    status: LoginUploadStatus.INIT,
    fileName: '',
  });

  useAsyncEffect(async () => {
    if (!appId) return;
    const [, res] = await to(
      GetAccountInfo({
        app_id: appId,
      })
    );
    const registerType = res?.app_type as REGISTER_TYPE;
    if (res && registerType) {
      setRegisterType(registerType);
      // 商家账户
      if (registerType === REGISTER_TYPE.CommercialOwner) {
        setCommercialOwnerForm({
          telPhone: res.phone_number,
          wechatOrQQ: res.qq_number || res.wx_id || '',
          shopId: res.api_app_id,
          shopSecret: res.api_secret,
          license: res.business_license_cos,
        });
        setUploadState({
          status: LoginUploadStatus.SUCCESS,
          fileName: res.business_license_cos,
        });
      } else {
        // 达人账户
        setTalentForm({
          telPhone: res.phone_number,
          wechatOrQQ: res.qq_number || res.wx_id || '',
          accountId: res.api_app_id,
          accountSecret: res.api_secret,
        });
        void getQRCode();
      }
    } else {
      message.error('获取账号信息失败');
    }
  }, []);

  const handleFileUpload = async (file: File) => {
    if (file.size > 1024 * 1024 * 10) {
      message.error('上传图片大小不能超过10M');
      return;
    }
    setUploadState({ status: LoginUploadStatus.LOADING, fileName: file.name });
    const result = await uploadRequest(
      MATERIAL_TYPE.FILE,
      file,
      0,
      () => void 0
    );
    if (result.code === 200) {
      setUploadState({
        status: LoginUploadStatus.SUCCESS,
        fileName: file.name,
      });
      handleCommercialOwnerChange('license', result.url);
    } else {
      setUploadState({
        status: LoginUploadStatus.INIT,
        fileName: '',
      });
    }
  };

  const handleCommercialOwnerChange = (key: string, value: string) => {
    setCommercialOwnerForm({ ...commercialOwnerForm, [key]: value });
  };

  const handleTalentChange = (key: string, value: string) => {
    setTalentForm({ ...talentForm, [key]: value });
  };

  const checkCommercialOwnerValue = async () => {
    const [reject] = await to(
      commercialOwnerValidator.validate(commercialOwnerForm)
    );

    // @ts-ignore
    const errors = (reject?.errors ?? []) as ValidatorRes[];

    if (errors.length) {
      console.warn(errors);
      handleErrorText(errors);
      submittedRef.current = true;
      return false;
    }
    resetErrorText();
    return true;
  };

  const checkTalentValue = async () => {
    const [reject] = await to(talentValidator.validate(talentForm));
    // @ts-ignore
    const errors = (reject?.errors ?? []) as ValidatorRes[];

    if (errors.length) {
      console.warn(errors);
      handleErrorText(errors);
      submittedRef.current = true;
      return false;
    }
    resetErrorText();
    return true;
  };

  // 注册商户
  const registerCommercialOwner = async () => {
    // 特殊判断，判断是否有上传营业执照
    if (!commercialOwnerForm.license) {
      message.error('请上传营业执照');
      return;
    }
    const checkRes = await checkCommercialOwnerValue();
    if (!checkRes) return;
    const [err] = await to(
      submitRegister({
        appId,
        accountRole: ACCOUNT_ROLE.WX_SHOP,
        phoneNumber: commercialOwnerForm.telPhone.trim(),
        weChat: loginType === 'wx' ? commercialOwnerForm.wechatOrQQ.trim() : '',
        apiAppId: commercialOwnerForm.shopId.trim(),
        apiSecret: commercialOwnerForm.shopSecret.trim(),
        token: '',
        businessLicense: commercialOwnerForm.license,
        qq: loginType === 'qq' ? commercialOwnerForm.wechatOrQQ.trim() : '',
      })
    );
    if (err && err instanceof RespError) {
      console.log(err);
      message.error(
        REGISTER_ERROR_MESSAGE[err.resultCode] ?? '提交失败，请稍后重试'
      );
      return;
    }
    message.success('提交成功');
    setAccountReviewDialogVisible(true);
  };

  // 注册达人
  const registerTalent = async () => {
    const checkRes = await checkTalentValue();
    if (!checkRes) return;
    const [err] = await to(
      submitRegister({
        appId,
        accountRole: ACCOUNT_ROLE.WX_CHANNEL,
        phoneNumber: talentForm.telPhone.trim(),
        weChat: loginType === 'wx' ? talentForm.wechatOrQQ.trim() : '',
        apiAppId: talentForm.accountId.trim(),
        apiSecret: talentForm.accountSecret.trim(),
        token: qrcode,
        businessLicense: '',
        qq: loginType === 'qq' ? talentForm.wechatOrQQ.trim() : '',
      })
    );
    if (err && err instanceof RespError) {
      console.log(err);
      message.error(
        REGISTER_ERROR_MESSAGE[err.resultCode] ?? '提交失败，请稍后重试'
      );
      return;
    }
    message.success('提交成功');
    setAccountReviewDialogVisible(true);
  };

  const handleErrorText = (errors: ValidatorRes[]) => {
    const errorText: Record<string, string> = {};
    errors.forEach((item) => {
      errorText[item.field] = item.message;
    });
    setErrorText(errorText);
  };

  const handleBlur = (fn: () => Promise<false | true>) => {
    if (!submittedRef.current) return;
    void fn();
  };

  const handleChangeRegisterType = (registerType: REGISTER_TYPE) => {
    resetErrorText();
    resetCommercialOwnerForm();
    resetTalentForm();
    resetUploadState();
    onReset();
    submittedRef.current = false;
    setRegisterType(registerType);
    if (registerType === REGISTER_TYPE.Talent && !qrcode) {
      void getQRCode();
    } else {
      resetQrCode();
    }
  };

  return (
    <>
      {/* 气派图片浏览 */}
      <LoginPreviewImg
        onClose={() => setPreviewImgUrl([])}
        imgUrls={previewImgUrl}
        currentIndex={currentIndex}
        onChange={(val) => setCurrentIndex(val)}
      />
      {/* 账户审批弹窗*/}
      <MbAccountReviewDialog
        imgUrl={MUSE_REGISTER_GUIDE}
        show={accountReviewDialogVisible}
        cancelButton={null}
        closeButton={false}
        onSubmit={() => {
          setAccountReviewDialogVisible(false);
          onSuccess();
        }}
      />
      <LoginRegister
        imgUrl={renderValue.qrcode}
        // @ts-ignore
        status={renderValue.status}
        errorText={errMessage}
        onRefresh={() => {
          void getQRCode();
        }}
        problemImgUrl={MUSE_REGISTER_GUIDE}
        currentSelect={registerType}
        onOutLog={onLoginOut}
        onSubmit={() => {
          registerType === REGISTER_TYPE.CommercialOwner
            ? registerCommercialOwner()
            : registerTalent();
        }}
        onChange={(val) => {
          if (registerType === val) return;
          handleChangeRegisterType(val);
        }}
        // errorText="上传的营业执照清晰度不足，请重新上传"
        tabData={[
          {
            id: REGISTER_TYPE.CommercialOwner,
            name: '商家账户',
            desc: '关联微信小店',
            icon: (
              <IconMerchantAccount
                color={
                  registerType === REGISTER_TYPE.CommercialOwner
                    ? 'var(--ms-theme-color)'
                    : 'white'
                }
              />
            ),
          },
          {
            id: REGISTER_TYPE.Talent,
            name: '达人账户',
            desc: '关联微信视频号',
            icon: (
              <IconProAccount
                color={
                  registerType === REGISTER_TYPE.Talent
                    ? 'var(--ms-theme-color)'
                    : 'white'
                }
              />
            ),
          },
        ]}
        formChildren={
          <>
            <Fragment x-if={registerType === REGISTER_TYPE.CommercialOwner}>
              <LoginInput
                title="手机号"
                placeholder="请输入手机号"
                value={commercialOwnerForm.telPhone}
                errText={errorText?.telPhone}
                onChange={(_e, val) =>
                  handleCommercialOwnerChange('telPhone', val)
                }
                onBlur={() => {
                  handleBlur(checkCommercialOwnerValue);
                }}
              />
              <LoginInput
                title={loginType === 'wx' ? '微信号' : 'QQ号'}
                placeholder={`请输入与扫码授权一致的${
                  loginType === 'wx' ? '微信号' : 'QQ号'
                }`}
                value={commercialOwnerForm.wechatOrQQ}
                errText={errorText?.wechatOrQQ}
                onChange={(_e, val) =>
                  handleCommercialOwnerChange('wechatOrQQ', val)
                }
                onBlur={() => {
                  handleBlur(checkCommercialOwnerValue);
                }}
              />
              <LoginInput
                showIcon
                title="小店 id"
                placeholder="请输入小店 id"
                value={commercialOwnerForm.shopId}
                errText={errorText?.shopId}
                onChange={(_e, val) =>
                  handleCommercialOwnerChange('shopId', val)
                }
                onBlur={() => {
                  handleBlur(checkCommercialOwnerValue);
                }}
                popOverChildren={
                  <LoginInputShopPopOver
                    imgUrl="https://avatarcdn.pay.qq.com/material/1ae5686fe6b5fc40cfbbda972af102e6.png"
                    onClick={() => window.open('https://store.weixin.qq.com/')}
                    onPreview={(val) => {
                      setCurrentIndex(0);
                      setPreviewImgUrl(val ? [val] : []);
                    }}
                  />
                }
              />
              <LoginInput
                showIcon
                title="小店密钥"
                placeholder="请输入小店密钥"
                onBlur={() => {
                  handleBlur(checkCommercialOwnerValue);
                }}
                value={commercialOwnerForm.shopSecret}
                errText={errorText?.shopSecret}
                onChange={(_e, val) =>
                  handleCommercialOwnerChange('shopSecret', val)
                }
              />
              <LoginUpload
                title="营业执照"
                desc="支持格式：.png .jpg，文件小于 10 MB"
                fileName={uploadState?.fileName || ''}
                fileType=".jpg,.jpeg,.png"
                onDelete={() => {
                  resetUploadState();
                  handleCommercialOwnerChange('license', '');
                }}
                // @ts-ignore
                uploadStatus={uploadState?.status}
                onUploadFromLocal={(e) =>
                  void handleFileUpload(e.target.files[0])
                }
              />
            </Fragment>
            <Fragment x-if={registerType === REGISTER_TYPE.Talent}>
              <LoginInput
                title="账号id"
                showIcon
                value={talentForm.accountId}
                placeholder="请输入账号id"
                errText={errorText?.accountId}
                onChange={(_e, val) => handleTalentChange('accountId', val)}
                onBlur={() => {
                  handleBlur(checkTalentValue);
                }}
                popOverChildren={
                  <LoginInputAccountPopOver
                    imgUrls={REGISTER_TALENT_GUIDE}
                    onClick={() =>
                      window.open('https://channels.weixin.qq.com/login.html')
                    }
                    onPreview={() => {
                      setCurrentIndex(0);
                      setPreviewImgUrl(REGISTER_TALENT_GUIDE);
                    }}
                  />
                }
              />
              <LoginInput
                title="账号密钥"
                value={talentForm.accountSecret}
                errText={errorText?.accountSecret}
                placeholder="请输入账号密钥"
                onChange={(_e, val) => handleTalentChange('accountSecret', val)}
                onBlur={() => {
                  handleBlur(checkTalentValue);
                }}
              />
              <LoginInput
                title="手机号"
                value={talentForm.telPhone}
                errText={errorText?.telPhone}
                placeholder="请输入手机号"
                onChange={(_e, val) => handleTalentChange('telPhone', val)}
                onBlur={() => {
                  handleBlur(checkTalentValue);
                }}
              />
              <LoginInput
                title={loginType === 'wx' ? '微信号' : 'QQ号'}
                value={talentForm.wechatOrQQ}
                errText={errorText?.wechatOrQQ}
                placeholder={`请输入与扫码授权一致的${
                  loginType === 'wx' ? '微信号' : 'QQ号'
                }`}
                onChange={(_e, val) => handleTalentChange('wechatOrQQ', val)}
                onBlur={() => {
                  handleBlur(checkTalentValue);
                }}
              />
            </Fragment>
          </>
        }
      />
    </>
  );
}
