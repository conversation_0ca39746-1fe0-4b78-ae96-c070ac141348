/**
 * <AUTHOR>
 * @date 2025/2/18 16:08
 * @desc const
 */

export const MUSE_AGREEMENT_ID = 'ams_account_agreement';

export const MUSE_ACCOUNT_KEY = 'muse_account_id';
// 妙播小助手二维码
export const MUSE_HELPER_QRCODE =
  'https://avatarcdn.pay.qq.com/material/bddb894fa3b1ca0bc5e448330f1a7b30.png';

// 注册指引二维码
export const MUSE_REGISTER_GUIDE =
  'https://avatarcdn.pay.qq.com/material/11c926437190869a6742a51c8f214a0c.png';

export const PATTERNS = {
  mobile: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/, // 中国大陆手机号
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
};

export const REGISTER_ERROR_MESSAGE: Record<string, string> = {
  20009: '当前申请的商户/达人已存在',
  20010: '微信/qq号与扫码登录账号不一致',
  20011: '微信/qq号与扫码登录账号不一致',
  20012: 'id和密钥不匹配，请检查',
  20028: '微信/qq号与扫码登录账号不一致',
  20031: '无法获取账户信息',
  20032: '扫码视频号与所填写的视频号ID不一致',
  20033: 'ID或者密钥错误，帐户创建失败',
};

export const REGISTER_TALENT_GUIDE = [
  'https://avatarcdn.pay.qq.com/material/2b374900386b9bfcf807200f396fdfcf.png',
  'https://avatarcdn.pay.qq.com/material/e9c499d71def036ce1c6583eaa6e9b47.png',
  'https://avatarcdn.pay.qq.com/material/4b1de913ce35e338951ef52c35ea906e.png',
];
