/* eslint-disable @typescript-eslint/ban-ts-comment */
/**
 * <AUTHOR>
 * @date 2025/2/19 15:35
 * @desc TeamworkerApply
 */

import React, { useEffect, useState } from 'react';
import {
  ApplyCollaborator,
  LoginInput,
  RegisterBasicInfo,
} from '@tencent/creative-mb';
import { GetInvitationInfo } from '@/pb/api/AuthAdminSvr_1';
import to from 'await-to-js';
import { RespError, RespType } from '@/pb/config';
import { message } from '@tencent/spaui';
import {
  CheckIsRegister,
  CheckLoginOpenId,
  SubmitApply,
} from '@/pb/api/AuthAdminSvr';
import { Rules } from 'async-validator/dist-types/interface';
import {
  MUSE_REGISTER_GUIDE,
  PATTERNS,
  REGISTER_ERROR_MESSAGE,
} from '@/pages/MuseLogin/const';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { useMemoizedFn, useResetState } from 'ahooks';
import Schema from 'async-validator';
import { ValidatorRes } from '@/pages/MuseLogin/types';
import { delParam } from '@tencent/midas-util';

interface IProps {
  inviteCode: string;
  onSuccess: () => void;
  onLoginOut: () => void;
}

const rules: Rules = {
  telPhone: [
    { required: true, message: '请输入手机号' },
    { pattern: PATTERNS.mobile, message: '请输入正确的手机号' },
  ],
  wechatOrQQ: [{ required: true, message: '请补全账号信息' }],
};

const Validator = new Schema(rules);

export function TeamworkerApply(props: IProps) {
  const { onSuccess, onLoginOut, inviteCode } = props;
  const [isValidUser, setIsValidUser] = useState(false);
  const [userState] = useRecoilState(UserInfoAtom);
  const [inviteMessage, setInviteMessage] =
    useState<RespType<typeof GetInvitationInfo>['invitation_info']>();
  const [errorText, setErrorText, resetErrorText] = useResetState<Record<
    string,
    string
  > | null>(null);

  const [formValue, setFormValue] = useResetState({
    telPhone: '',
    wechatOrQQ: '',
  });

  // 获取邀请信息
  const queryInviteMessage = async () => {
    const [err, res] = await to(
      GetInvitationInfo({
        invitation_code: inviteCode,
      })
    );
    if (!err && res?.invitation_info) {
      setInviteMessage(res.invitation_info);
    } else {
      message.error('获取邀请信息失败');
    }
  };

  // 检查用户是否在妙播注册过，如果没有则需要注册
  const checkValidUser = useMemoizedFn(async () => {
    //   检查用户是否在妙播注册过，如果没有则需要注册
    const [, res] = await to(CheckIsRegister({}));
    const registered = !!res?.is_register;
    setIsValidUser(registered);
    await queryInviteMessage();
  });

  useEffect(() => {
    void checkValidUser();
  }, [checkValidUser]);

  // 提交申请
  const handleSubmit = async () => {
    resetErrorText();

    const [reject] = await to(Validator.validate(formValue));
    // @ts-ignore
    const errors = (reject?.errors ?? []) as ValidatorRes[];

    if (errors.length) {
      handleErrorText(errors);
      return false;
    }

    const [err] = await to(
      CheckLoginOpenId({
        wx_id:
          userState?.adExtend?.login_type === 'wx'
            ? formValue.wechatOrQQ.trim()
            : '',
        qq_number:
          userState?.adExtend?.login_type === 'qq'
            ? formValue.wechatOrQQ.trim()
            : '',
      })
    );

    if (err && err instanceof RespError) {
      console.error(err);
      message.error(
        REGISTER_ERROR_MESSAGE[err.resultCode] ?? '提交失败，请稍后重试'
      );
    } else {
      setIsValidUser(true);
    }
  };

  // 处理错误信息
  const handleErrorText = (errors: ValidatorRes[]) => {
    const errorText: Record<string, string> = {};
    errors.forEach((item) => {
      errorText[item.field] = item.message;
    });
    setErrorText(errorText);
  };

  // 协作者提交申请
  const handleApply = async () => {
    const [err] = await to(
      SubmitApply({
        invitation_code: inviteCode,
        phone_number: formValue.telPhone.trim(),
        wx_id:
          userState?.adExtend?.login_type === 'wx'
            ? formValue.wechatOrQQ.trim()
            : '',
        qq_number:
          userState?.adExtend?.login_type === 'qq'
            ? formValue.wechatOrQQ.trim()
            : '',
      })
    );
    if (err && err instanceof RespError) {
      console.log(err);
      message.error(
        REGISTER_ERROR_MESSAGE[err.resultCode] ?? '提交失败，请稍后重试'
      );
      return;
    }
    message.success('提交成功');
    history.replaceState({}, '', delParam('invite_code', location.href));
    onSuccess();
  };

  return (
    <>
      <ApplyCollaborator
        x-if={isValidUser && inviteMessage}
        imgUrl={inviteMessage?.avatar_url}
        name={inviteMessage?.nick_name}
        id={inviteMessage?.app_id}
        onSubmit={handleApply}
      />
      <RegisterBasicInfo
        x-else
        title="完善基础信息"
        problemImgUrl={MUSE_REGISTER_GUIDE}
        onRefresh={onLoginOut}
        onSubmit={handleSubmit}
        // onChange={(val) => setAgreementCheck(val)}
        formChildren={
          <>
            <LoginInput
              title="手机号"
              errText={errorText?.telPhone}
              value={formValue.telPhone}
              onChange={(_e, val) => {
                setFormValue({
                  ...formValue,
                  telPhone: val,
                });
              }}
            />
            <LoginInput
              title={
                userState?.adExtend?.login_type === 'wx' ? '微信号' : 'QQ号'
              }
              value={formValue.wechatOrQQ}
              errText={errorText?.wechatOrQQ}
              onChange={(_e, val) => {
                setFormValue({
                  ...formValue,
                  wechatOrQQ: val,
                });
              }}
            />
          </>
        }
      />
    </>
  );
}
