import { ACCOUNT_ROLE, ACCOUNT_STATUS, MEMBER_ROLE } from './config';

// 账号状态 success | fail | loading
export type AccountStatusType = 'success' | 'fail' | 'loading';
// 审批状态
export type ApproveStatusType = 'agree' | 'reject' | 'preview' | null;

// 成员角色类型 admin | collaborator
export type MemberRoleType = MEMBER_ROLE;

// 账户角色类型
export type AccountRoleType = ACCOUNT_ROLE;

export type ValidatorRes = {
  message: string;
  field: string;
  fieldValue: string;
};

// 账户列表查询参数
export interface IAccountListParams {
  nickName: string;
  current: number;
  pageSize: number;
}

// 账户列表数据
export interface IAccountListData {
  id: string;
  // 名称
  name: string;
  // 头像
  imgUrl: string;
  // 成员角色 admin | collaborator
  position: string;
  // 账户状态
  status: AccountStatusType;
}

// 账户列表分页数据
export type AccountPaginationData = {
  total: number;
  list: IAccountListData[];
};

// 账户信息类型
export interface IAccountInfoData {
  // 账号名称
  accountName: string;
  // 头像
  imgUrl: string;
  // 微信号
  weChat: string;
  // 成员角色 admin | collaborator
  memberRole: MemberRoleType;
  // 账户角色 小店 | 达人
  accountRole: AccountRoleType;
  // 营业执照
  businessLicense: string;
}

export interface IRegisterParams {
  // 账户id，新增时为空
  appId?: string;
  // 手机号
  phoneNumber: string;
  // 微信号
  weChat: string;
  // qq号
  qq: string;
  // api_app_id
  apiAppId: string;
  // api_secret
  apiSecret: string;
  // 达人扫码注册的token
  token: string;
  // 账户角色 小店 | 达人
  accountRole: AccountRoleType;
  // 营业执照
  businessLicense: string;
}

export interface IApproveListParams {
  status: ACCOUNT_STATUS[];
  approveType: MEMBER_ROLE;
  pageSize: number;
  current: number;
}

export interface IApproveListData {
  approveId: string;
  // 用户名称
  userNickName: string;
  // 用户id
  userId: string;
  // 头像
  userAvatar: string;
  // 账号id
  appId: string;
}
