import { AccountRoleType, AccountStatusType, ApproveStatusType } from './types';
// 账户角色
export enum ACCOUNT_ROLE {
  WX_SHOP = 'WX_SHOP', // 商户
  WX_CHANNEL = 'WX_CHANNEL', // 达人
}

// 账户角色映射
export const ACCOUNT_ROLE_MAP: {
  [key in ACCOUNT_ROLE]: string;
} = {
  [ACCOUNT_ROLE.WX_SHOP]: '商户',
  [ACCOUNT_ROLE.WX_CHANNEL]: '达人',
};

// 账户状态
export enum ACCOUNT_STATUS {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
}

// 账户状态映射
export const ACCOUNT_STATUS_MAP: {
  [key in ACCOUNT_STATUS]: AccountStatusType;
} = {
  [ACCOUNT_STATUS.REJECTED]: 'fail',
  [ACCOUNT_STATUS.APPROVED]: 'success',
  [ACCOUNT_STATUS.PENDING]: 'loading',
};

// 审批状态
export enum APPROVE_STATUS {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
}

// 审批状态映射
export const APPROVE_STATUS_MAP: {
  [key in APPROVE_STATUS]: ApproveStatusType;
} = {
  [APPROVE_STATUS.APPROVED]: 'agree',
  [APPROVE_STATUS.REJECTED]: 'reject',
  [APPROVE_STATUS.PENDING]: null,
};

// 成员角色
export enum MEMBER_ROLE {
  COLLABORATOR = 'collaborator',
  ADMIN = 'admin',
}

// 成员角色映射
export const MEMBER_ROLE_MAP = {
  [MEMBER_ROLE.ADMIN]: '管理人员',
  [MEMBER_ROLE.COLLABORATOR]: '运营人员',
};

// 达人注册的二维码状态
export enum QR_CODE_STATUS {
  // 0:未知
  UNKNOWN,
  // 1:未扫码
  UNSCANNED,
  // 2:已扫码待确认
  SCANNED,
  // 3:扫码登陆完成
  SUCCESS,
  // 4:二维码已失效
  INVALID,
}
