/**
 * <AUTHOR>
 * @date 2025/2/28 16:40
 * @desc useAccountInfo
 */
import { GetAccountInfo } from '@/pb/api/AuthAdminSvr';
import to from 'await-to-js';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { message } from '@tencent/spaui';
import { MUSE_ACCOUNT_KEY } from '@/pages/MuseLogin/const';
import { LoginStateAtom } from '@/model/login';
import { useMemoizedFn } from 'ahooks';

export const useUpdateAccount = () => {
  const [userState, setUserState] = useRecoilState(UserInfoAtom);
  const [loginState, setLoginState] = useRecoilState(LoginStateAtom);

  const updateAccountInfo = useMemoizedFn(async (appId: string) => {
    const [, res] = await to(
      GetAccountInfo({
        app_id: appId,
      })
    );
    if (res && userState?.adExtend && loginState) {
      localStorage.setItem(MUSE_ACCOUNT_KEY, appId);
      setUserState({
        ...userState,
        adExtend: {
          ...userState.adExtend,
          account_id: appId,
          roles: res.role_ids || [],
          appType: res.app_type,
        },
      });
      setLoginState({
        ...loginState,
        strinifyLoginInfo: JSON.stringify({
          app_id: appId,
        }),
      });
    } else {
      message.error('获取账号信息失败');
    }
  });

  return {
    updateAccountInfo,
  };
};
