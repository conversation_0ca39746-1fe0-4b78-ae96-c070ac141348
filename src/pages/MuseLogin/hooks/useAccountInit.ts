/**
 * <AUTHOR>
 * @date 2025/3/11 19:55
 * @desc useAccountInit
 */
import { useEffect, useRef } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { useUpdateAccount } from '@/pages/MuseLogin/hooks/useAccountInfo';

export const useAccountInit = () => {
  const loginState = useRecoilValue(LoginStateAtom);
  const [userState] = useRecoilState(UserInfoAtom);
  const { updateAccountInfo } = useUpdateAccount();
  const initRef = useRef(false);

  useEffect(() => {
    // 判断账号信息
    if (!loginState || !userState?.adExtend) return;
    // 已经初始化了
    if (userState.adExtend.roles.length || initRef.current) return;
    if (userState.adExtend.account_id) {
      updateAccountInfo(userState.adExtend.account_id).then();
      initRef.current = true;
    }
  }, [loginState, updateAccountInfo, userState?.adExtend]);
};
