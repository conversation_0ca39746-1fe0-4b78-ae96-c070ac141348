import { CheckQRCodeStatus, GenerateQRCode } from '@/pb/api/AuthAdminSvr';
import { useRequest } from 'ahooks';
import to from 'await-to-js';
import { QR_CODE_STATUS } from '../config';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IBaseQRCodeProps } from '@/components/BaseQRCode';
import QRCode from 'qrcode';

export enum REGISTER_TYPE {
  CommercialOwner = 'WX_SHOP',
  Talent = 'WX_CHANNEL',
}

// 达人二维码逻辑，获取二维码、轮询状态、重置
export const useRegisterQRCode = (registerType: REGISTER_TYPE) => {
  const [qrUrl, setQRUrl] = useState('');
  const [qrcode, setQRCode] = useState<string>('');
  const [qrcodeState, setQRCodeState] =
    useState<IBaseQRCodeProps['qrcodeState']>('normal');
  const [errMessage, setErrMessage] = useState<string>('');
  const pollingTimeout = useRef<NodeJS.Timeout>();
  // 获取微信二维码
  const {
    runAsync: getQRCode,
    loading,
    error,
  } = useRequest(
    async () => {
      if (loading) {
        return Promise.reject('loading');
      }
      setQRCode('');
      setQRUrl('');
      setErrMessage('');
      setQRCodeState('normal');
      const resp = await GenerateQRCode({});
      const { token: code } = resp;
      const url = await QRCode.toDataURL(
        `https://channels.weixin.qq.com/mobile/confirm_login.html?token=${code}`,
        {
          margin: 2,
        }
      );
      setQRUrl(url);
      setQRCode(code);
      return code;
    },
    {
      manual: true,
      onError: (error) => {
        console.log(error);
        setErrMessage('获取二维码失败,请稍后重试');
      },
    }
  );

  // 重置
  const reset = useCallback(() => {
    clearTimeout(pollingTimeout.current);
    setQRCodeState('normal');
    setQRCode('');
    setQRUrl('');
    setErrMessage('');
  }, []);

  useEffect(() => {
    if (!qrcode) return;
    const polling = async () => {
      // 轮询后台接口
      const [, resp] = await to(
        CheckQRCodeStatus({
          token: qrcode,
        })
      );
      // 未知状态报错也继续轮询
      const qr_status = resp?.status || 0;
      // if (resp?.status === undefined) return;
      let continued = false;
      if (QR_CODE_STATUS[qr_status] === 'SCANNED') {
        setQRCodeState('scan-success');
        // 扫码确认之后也需要继续轮询
        continued = true;
      } else if (QR_CODE_STATUS[qr_status] === 'INVALID') {
        setQRCodeState('scan-timeout');
      } else if (QR_CODE_STATUS[qr_status] === 'SUCCESS') {
        setQRCodeState('success');
      } else {
        continued = true;
      }
      if (continued) {
        pollingTimeout.current = setTimeout(polling, 2000);
      }
    };
    polling();
    return () => {
      clearTimeout(pollingTimeout.current);
    };
  }, [qrcode]);

  // 配合重构设计逻辑
  const renderValue = useMemo(() => {
    if (registerType === REGISTER_TYPE.CommercialOwner) {
      return {
        status: 'default',
        qrcode: '',
      };
    }
    if (!qrUrl) {
      return {
        status: 'loading',
        qrcode: 'loading',
      };
    }
    if (qrcodeState === 'normal') {
      return {
        status: 'default',
        qrcode: qrUrl,
      };
    }
    if (qrcodeState === 'scan-success') {
      return {
        status: 'success',
        qrcode: qrUrl,
      };
    }
    if (qrcodeState === 'scan-timeout') {
      return {
        status: 'fail',
        qrcode: qrUrl,
      };
    }
    return {
      status: 'success',
      qrcode: '',
    };
  }, [qrUrl, qrcodeState, registerType]);
  return {
    error,
    qrcode,
    errMessage,
    qrUrl,
    getQRCode,
    loading,
    qrcodeState,
    reset,
    renderValue,
  };
};
