/**
 * <AUTHOR>
 * @date 2025/2/28 12:41
 * @desc useAccountList
 */

import { RespType } from '@/pb/config';
import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import { AccountList } from '@/pb/api/AuthAdminSvr';
import { message } from '@tencent/spaui';

export interface IMuseAccountParams {
  searchKey?: string;
  status?: string;
  // 分页参数
  pageSize?: number;
  pageNum?: number;
}

export type IMuseAccountRecord = RespType<
  typeof AccountList
>['account_info_list'][number];

export const useMuseAccountListRequest = ({
  queryParamsDefault,
  mode = 'all',
}: {
  queryParamsDefault: IMuseAccountParams;
  mode: 'all' | 'try';
}) => {
  const [queryParams, setQueryParams] =
    useState<IMuseAccountParams>(queryParamsDefault);
  const [records, setRecords] = useState<IMuseAccountRecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);

  const { runAsync, loading, error } = useRequest(
    () => {
      return AccountList({
        status: queryParams.status || '',
        nick_name: queryParams.searchKey || '',
        page_num: queryParams.pageNum || 1,
        page_size: queryParams.pageSize || 10,
      });
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  // 处理全量加载逻辑
  useEffect(() => {
    if (mode === 'try') return;
    // 请求全量数据
    runAsync()
      .then((res) => {
        setRecords(res.account_info_list || []);
        setRecordCount(res.count || 0);
      })
      .catch((e) => {
        console.warn(e, '获取数据失败');
        message.error('获取账号列表失败');
        setRecords([]);
        setRecordCount(0);
      });
  }, [runAsync, queryParams, mode]);

  const refresh = () => {
    setQueryParams({ ...queryParams });
  };

  return {
    records,
    recordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
    run: runAsync,
  };
};
