// 获取直播间列表 hooks

import { useLatest, useMemoizedFn, usePagination, useUnmount } from 'ahooks';
import { Service } from 'ahooks/lib/usePagination/types';
import { useLayoutEffect, useMemo, useRef, useState } from 'react';

export interface ICommonListOptions {
  defaultPageSize?: number;
  defaultCurrentPage?: number;
  manual?: boolean;
  /**
   * 分页模式，
   */
  mode?: 'infinite-scroll' | 'table';
}

export interface ICommonListParams {
  current: number;
  pageSize: number;
}

export function useCommonList<Params extends ICommonListParams, R = unknown>(
  options: {
    dataLoader: Service<
      {
        total: number;
        list: R[];
      },
      [Params]
    >;
  } & ICommonListOptions
) {
  type OptionsType = typeof options;
  const {
    defaultPageSize = 10,
    defaultCurrentPage = 1,
    manual = false,
    mode = 'table',
    dataLoader,
  } = options || {};

  const loadData = useMemoizedFn<OptionsType['dataLoader']>(dataLoader);
  const lastLoadingTimeRef = useRef(0);
  const autoRefreshTimeoutRef = useRef<NodeJS.Timeout>();
  /**
   * localList
   */
  const [localList, setLocalList] = useState<R[]>([]);

  const { data, loading, pagination, refreshAsync, cancel, runAsync, params } =
    usePagination(loadData, {
      defaultCurrent: defaultCurrentPage,
      defaultPageSize,
      manual,
    });
  const latestLoading = useLatest(loading);
  const latestRefresh = useLatest(refreshAsync);

  /**
   * 记录上一次loading 的事件
   */
  useMemo(() => {
    if (loading) lastLoadingTimeRef.current = Date.now();
  }, [loading]);
  /**
   * 加载更多
   */
  const loadMore = useMemoizedFn(
    async (params: Omit<Params, 'current' | 'pageSize'>) => {
      if (mode !== 'infinite-scroll') return;
      const nextPage = pagination.current + 1;
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const data = await runAsync({
        current: nextPage,
        pageSize: pagination.pageSize,
        ...params,
        // ...params,
      } as Params);
      if (data.list.length > 0) {
        setLocalList((prev) => prev.concat(data.list));
      }
      return data;
    }
  );
  /**
   * 加载新数据，覆盖当前localData
   */
  const runWithReset = useMemoizedFn(
    async (params: Omit<Params, 'current' | 'pageSize'>) => {
      setLocalList([]);
      /** 取消之前正在加载的 */
      cancel();
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const results = await runAsync({
        current: 1,
        pageSize: pagination.pageSize,
        ...params,
      } as Params);
      if (mode === 'infinite-scroll') {
        setLocalList(results.list);
      }
      return results;
    }
  );
  const resetList = useMemoizedFn(() => setLocalList([]));

  const startAutoRefresh = useMemoizedFn(
    (
      options: { interval: number } = {
        interval: 5000,
      }
    ) => {
      const { interval } = options;
      stopAutoRefresh();
      const startRefresh = async (): Promise<void> => {
        let canRefresh = true;
        /** 自动刷新做一下限制，避免其他流程触发了刷新，导致这里刷新太频繁 */
        if (
          latestLoading.current ||
          Date.now() - lastLoadingTimeRef.current < interval
        ) {
          canRefresh = false;
        }
        if (canRefresh) {
          try {
            await latestRefresh.current?.();
          } catch {
          } finally {
          }
        }
        if (autoRefreshTimeoutRef.current === null) return;
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        autoRefreshTimeoutRef.current = setTimeout(startRefresh, interval);
        return;
      };
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      autoRefreshTimeoutRef.current = setTimeout(startRefresh, interval);
    }
  );
  const stopAutoRefresh = useMemoizedFn(async () => {
    if (autoRefreshTimeoutRef.current) {
      clearTimeout(autoRefreshTimeoutRef.current);
      autoRefreshTimeoutRef.current = undefined;
    }
  });

  useUnmount(() => {
    stopAutoRefresh();
  });

  return {
    cancel,
    pagination,
    refresh: refreshAsync,
    runAsync,
    data,
    loading,
    loadMore,
    list: localList,
    runWithReset,
    resetList,
    requestParams: params,
    startAutoRefresh,
    stopAutoRefresh,
  };
}
