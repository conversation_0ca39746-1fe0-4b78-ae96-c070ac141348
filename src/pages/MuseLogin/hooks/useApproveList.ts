/**
 * <AUTHOR>
 * @date 2025/3/1 22:41
 * @desc useMuseApproveListRequest
 */

import { RespType } from '@/pb/config';
import { useMemoizedFn, useRequest, useUpdateEffect } from 'ahooks';
import { useRef, useState } from 'react';
import { uniqBy } from 'lodash-es';
import { GetApproveList } from '@/pb/api/AuthAdminSvr_2';
import { MEMBER_ROLE } from '@/pages/MuseLogin/config';
import { message } from '@tencent/spaui';

interface IMuseApproveListParams {
  status?: string[];
  // 分页参数
  pageSize?: number;
  pageNum?: number;
}

export type IMuseApproveRecord = RespType<
  typeof GetApproveList
>['collaborator_approve_info_list'][number];

export const useMuseApproveListRequest = ({
  queryParamsDefault,
}: {
  queryParamsDefault: IMuseApproveListParams;
}) => {
  const [queryParams, setQueryParams] =
    useState<IMuseApproveListParams>(queryParamsDefault);
  const [records, setRecords] = useState<IMuseApproveRecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);
  const scrollEndRef = useRef(true);

  const { runAsync, loading, error } = useRequest(
    () => {
      return GetApproveList({
        status: queryParams.status ?? ['APPROVED', 'PENDING', 'REJECTED'],
        approve_type: MEMBER_ROLE.COLLABORATOR.toLocaleUpperCase(),
        page_num: queryParams.pageNum || 1,
        page_size: queryParams.pageSize || 10,
      });
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useUpdateEffect(() => {
    // 请求全量数据
    runAsync()
      .then((res) => {
        scrollEndRef.current = false;
        if (queryParams.pageNum && queryParams.pageNum > 1) {
          // 合并数据
          setRecords((prev) => {
            return uniqBy(
              [...prev, ...res.collaborator_approve_info_list],
              'approve_id'
            );
          });
        } else {
          setRecords(res.collaborator_approve_info_list || []);
        }
        setRecordCount(res.count || 0);
        if (res.collaborator_approve_info_list.length === 0) {
          scrollEndRef.current = true;
        }
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        message.error('获取审批列表失败');
        setRecords([]);
        setRecordCount(0);
      });
  }, [runAsync, queryParams]);

  const loadData = useMemoizedFn((params?: IMuseApproveListParams) => {
    setQueryParams({
      ...queryParams,
      ...(params ?? {}),
    });
  });

  const resetData = useMemoizedFn(() => {
    // scrollEndRef.current = true;
    setQueryParams({
      ...queryParams,
      pageNum: 1,
    });
  });

  return {
    records,
    recordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    run: runAsync,
    isEnd: scrollEndRef.current,
    loadData,
    resetData,
  };
};
