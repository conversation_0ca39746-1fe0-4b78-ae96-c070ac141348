/**
 * <AUTHOR>
 * @date 2025/2/28 15:25
 * @desc useAccountRegister
 */
import { useRequest } from 'ahooks';
import { EditAccountInfo, RegisterApp } from '@/pb/api/AuthAdminSvr';
import { IRegisterParams } from '@/pages/MuseLogin/types';

export const useAccountRegister = () => {
  const { runAsync: addRegister } = useRequest(
    (params: IRegisterParams) => {
      return RegisterApp({
        api_app_id: params.apiAppId,
        api_secret: params.apiSecret,
        token: params.token,
        app_type: params.accountRole,
        business_license_cos: params.businessLicense,
        phone_number: params.phoneNumber,
        wx_id: params.weChat,
        qq_number: params.qq,
      });
    },
    {
      manual: true,
    }
  );

  const { runAsync: updateRegister } = useRequest(
    (params: IRegisterParams) => {
      return EditAccountInfo({
        app_id: params.appId,
        api_app_id: params.apiAppId,
        api_secret: params.apiSecret,
        token: params.token,
        app_type: params.accountRole,
        business_license_cos: params.businessLicense,
        phone_number: params.phoneNumber,
        wx_id: params.weChat,
        qq_number: params.qq,
      });
    },
    {
      manual: true,
    }
  );

  // 注册接口
  const submitRegister = async (params: IRegisterParams) => {
    return params.appId ? updateRegister(params) : addRegister(params);
  };

  return {
    submitRegister,
  };
};
