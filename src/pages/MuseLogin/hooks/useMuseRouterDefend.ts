/**
 * <AUTHOR>
 * @date 2025/3/3 15:26
 * @desc useRouterDefend
 */
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { useNavigate } from 'react-router-dom';
import { LOGIN_STEP } from '@/pages/MuseLogin';

// 广告路由守卫
export const useMuseRouterDefend = () => {
  const [userState] = useRecoilState(UserInfoAtom);
  const navigate = useNavigate();
  useEffect(() => {
    if (MatchedGlobalConfigItem.appcode !== 'advertisement') return;
    if (!userState) return;

    if (!userState?.adExtend?.account_id) {
      navigate(`/?step=${LOGIN_STEP.SELECT_ACCOUNT}`);
    }
  }, [navigate, userState, userState?.adExtend?.account_id]);
  return;
};
