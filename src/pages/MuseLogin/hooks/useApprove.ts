import { IApproveListData, IApproveListParams } from '../types';
import to from 'await-to-js';
import { useRequest } from 'ahooks';
import { GetApproveList, SubmitApprove } from '@/pb/api/AuthAdminSvr_2';
import { ACCOUNT_STATUS } from '../config';
import { useCommonList } from './useCommonList';

export const useApproveList = () => {
  const loadData = async (
    params: IApproveListParams
  ): Promise<{
    total: number;
    list: IApproveListData[];
  }> => {
    const [err, resp] = await to(
      GetApproveList({
        status: params.status,
        approve_type: params.approveType,
      })
    );
    if (err || !resp) {
      console.error(err);
      return {
        total: 0,
        list: [],
      };
    }
    return {
      total: parseInt(resp.count, 10) || 0,
      list: resp.collaborator_approve_info_list.map((item) => {
        return {
          approveId: item.approve_id,
          userNickName: item.user_nick_name,
          userId: item.user_id,
          userAvatar: item.user_avatar_url,
          appId: item.app_id,
        };
      }),
    };
  };
  const { list, loading, runWithReset, loadMore, data } = useCommonList<
    IApproveListParams,
    IApproveListData
  >({
    dataLoader: loadData,
    defaultPageSize: 999,
    defaultCurrentPage: 1,
    manual: true,
    mode: 'infinite-scroll',
  });

  return {
    list,
    total: data?.total || 0,
    loadMore,
    loading,
    runWithReset,
  };
};

// 协作者申请审批
export const useApproveSubmit = () => {
  const loadData = async (params: {
    approveId: string;
    status: ACCOUNT_STATUS;
    comments: string;
  }) => {
    const [err] = await to(
      SubmitApprove({
        approve_id: params.approveId,
        status: params.status,
        comments: params.comments,
      })
    );
    if (err) {
      throw err;
    }
  };
  const { runAsync, data, error, loading } = useRequest(loadData, {
    manual: true,
  });
  return {
    runAsync,
    loading,
    error,
  };
};
