import { SelectScriptModel } from '@/pages/ScriptList/components/SelectScriptModel';
import { getOriginByType, openEditor } from '@/pages/Editor/common/openEditor';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { DialogPlugin } from 'tdesign-react';
import { EMPTY_SCRIPT_RESEARCH_ID } from '../ScriptList/const';
import { EMPTY_TEMPLATE_ID, CONTENT_TYPE_MAP } from '@/const/common';
import { showSelectDeviceSizeModel } from '@/components/TemplateDialog';
import './index.less';

export function selectScript(
  templateId: string,
  templateType: string,
  onCreateScript: () => void
) {
  const confirmDia = DialogPlugin.confirm({
    header: (
      <div
        style={{ padding: '20px 20px 0', fontSize: '16px', color: '#000000E5' }}
      >
        请从下方选择一个脚本（可跳过）
      </div>
    ),
    body: (
      <SelectScriptModel
        scriptType={CONTENT_TYPE_MAP.Live.value}
        application_scenarios={templateType}
        onCreateWithScript={(script) => {
          if (!script && templateId === EMPTY_TEMPLATE_ID) {
            showSelectDeviceSizeModel({
              onChoose: (id) => {
                openEditor({
                  system: EditorSystemMap.META_HUMAN,
                  contentType: CONTENT_TYPE_MAP.Live.value,
                  origin: getOriginByType({
                    contentType: CONTENT_TYPE_MAP.Live.value,
                    application_scenarios: templateType,
                  }),
                  scriptId: EMPTY_SCRIPT_RESEARCH_ID,
                  deviceSize: id,
                  templateId,
                });
              },
            });
          } else {
            openEditor({
              system: EditorSystemMap.META_HUMAN,
              contentType: CONTENT_TYPE_MAP.Live.value,
              origin: getOriginByType({
                contentType: CONTENT_TYPE_MAP.Live.value,
                application_scenarios: templateType,
              }),
              scriptId: script?.research_id || EMPTY_SCRIPT_RESEARCH_ID,
              templateId,
              // [未知用途]
              // tempType: 'course_video',
              // tempId: templateId,
            });
          }
          confirmDia.hide();
        }}
        // onCreateScript={() => {
        //   confirmDia.hide();
        //   onCreateScript();
        // }}
        templateId={templateId}
        close={() => {
          confirmDia.hide();
        }}
      />
    ),
    footer: null,
    closeOnOverlayClick: false,
    width: 860,
    placement: 'center',
    style: {
      padding: 0,
    },
    className: 'dialog-live-temp',
    onClose: () => {
      confirmDia.hide();
    },
  });
}
