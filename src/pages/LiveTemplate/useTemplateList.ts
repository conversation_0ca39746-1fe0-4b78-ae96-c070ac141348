import { CONTENT_TYPE_MAP } from '@/const/common';
import { useGetTemplateList } from '@/hooks/template';
import { useState, useCallback } from 'react';
import { PageInfo as TPageInfo } from 'tdesign-react';

interface IUseTemplateList {
  pageSize?: number;
  searchKey?: string;
}

export default function useTemplateList(p?: IUseTemplateList) {
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState<number>(p?.pageSize || 12);
  const [selected, select] = useState<Record<string, string>>({});

  const { list, count, loading } = useGetTemplateList(
    selected,
    pageNum,
    pageSize,
    CONTENT_TYPE_MAP.Live.value,
    p?.searchKey
  );

  const onChange = useCallback(
    (pi: TPageInfo) => {
      if (pi.pageSize !== pageSize) {
        setPageNum(1);
      } else {
        setPageNum(pi.current);
      }

      setPageSize(pi.pageSize);
    },
    [pageSize]
  );

  const pickOption = (
    selectedValue?: Record<string, string>,
    merged = false
  ) => {
    if (!selectedValue) return;

    select((prev) => {
      return merged ? Object.assign({}, prev, selectedValue) : selectedValue;
    });

    setPageNum(1);
  };

  return {
    data: list,
    loading,
    selected,
    pagination: {
      current: pageNum,
      total: count,
      pageSize,
      onChange,
    },
    select: pickOption,
  };
}
