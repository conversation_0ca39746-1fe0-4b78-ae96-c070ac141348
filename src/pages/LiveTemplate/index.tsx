import { useCallback, useEffect, useMemo, useState } from 'react';
import { TemplateList } from '@/components/TemplateList';
import { Pagination, Button, Input } from 'tdesign-react';
import { useTemplateSelectorOptions } from '@/hooks/template';
import { TemplateSelector } from '@/components/TemplateSelector';

import useTemplateList from './useTemplateList';
import { selectScript } from './ScriptModal';
import { EMPTY_TEMPLATE_ID, CONTENT_TYPE_MAP } from '@/const/common';
import { useNavigate } from 'react-router-dom';
import { Page, MainContent } from '@/components/Layout';
import { SearchIcon } from 'tdesign-icons-react';
import { useDebounce } from 'ahooks';
import { FlattenSelect } from '../../components/FlattenSelect';

export default function LiveTemplate() {
  const [searchKey, setSearchKey] = useState('');
  const debouncedSearchKey = useDebounce(searchKey, { wait: 500 });
  const { data, loading, pagination, select, selected } = useTemplateList({
    searchKey: debouncedSearchKey,
  });
  const navigate = useNavigate();

  // 参数 2 为直播接口
  const { selectorOption } = useTemplateSelectorOptions(
    CONTENT_TYPE_MAP.Live.value
  );

  // 当前选中值 Lv1
  const currentSelectLv1 = useMemo<string>(() => {
    return selectorOption?.selectorId
      ? selected[selectorOption?.selectorId]
      : '';
  }, [selected, selectorOption]);

  // 二级选项
  const optionsLv2 = useMemo(() => {
    if (!selectorOption) return [];

    return (
      selectorOption.options.find((item) => item.value === currentSelectLv1)
        ?.childenSelector ?? []
    );
  }, [selectorOption, currentSelectLv1]);

  const getPickLv1Option = useCallback(
    (value?: string) => {
      if (!selectorOption) return;

      const first = selectorOption?.options?.[0];

      const selectLv1 = value
        ? selectorOption.options.find((x) => x.value === value)
        : first;

      if (!selectLv1) return;

      const values = {
        [selectorOption.selectorId]: selectLv1.value,
      };

      selectLv1.childenSelector.forEach((option) => {
        values[option.selectorId] = option.options[0].value;
      });

      return values;
    },
    [selectorOption]
  );

  useEffect(() => {
    const values = getPickLv1Option();

    values && select(values);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectorOption, getPickLv1Option]);

  return (
    <Page title="直播创建">
      <MainContent>
        <div className="flex justify-between border-solid border-b-[1px] border-[#E4EAF4]">
          <div style={{ marginBottom: '20px' }}>
            <Button
              theme="default"
              className="gradient-primary"
              onClick={() =>
                selectScript(EMPTY_TEMPLATE_ID, '', () => {
                  navigate('/script-list');
                })
              }
            >
              从空白页创建
            </Button>
          </div>
          <div className="w-[280px] flex">
            <Input
              value={searchKey}
              onChange={(v) => setSearchKey(v)}
              suffix={<SearchIcon />}
              style={{ border: 'none' }}
              placeholder="请输入你需要搜索的内容"
            />
          </div>
        </div>
        <div className="bg-[#fff] h-0 flex-1 flex flex-col pt-[20px] rounded-[8px]">
          {selectorOption && (
            <FlattenSelect<string>
              site="left"
              title={selectorOption.label}
              className="mb-[16px]"
              labelStyle={{ color: '#00000099', width: '70px' }}
              options={selectorOption.options.map((option) => ({
                value: option.value,
                label: option.label,
                key: `${option.label}-${option.value}`,
                style: { padding: '10px' },
              }))}
              value={currentSelectLv1}
              onChange={async (value) => {
                const values = getPickLv1Option(value);

                select(values);
              }}
            />
          )}
          {optionsLv2?.length > 0 && (
            <>
              {optionsLv2.map((itemLv2) => {
                return (
                  <FlattenSelect<string>
                    site="left"
                    labelStyle={{ color: '#00000099', width: '70px' }}
                    title={itemLv2.label}
                    key={itemLv2.selectorId}
                    className="mb-[16px]"
                    options={itemLv2.options.map((option) => ({
                      value: option.value,
                      label: option.label,
                      key: `${option.label}-${option.value}`,
                    }))}
                    value={selected[itemLv2.selectorId]}
                    onChange={async (value) => {
                      select(
                        {
                          [itemLv2.selectorId]: value,
                        },
                        true
                      );
                    }}
                  />
                );
              })}
            </>
          )}
          <TemplateList
            className="h-0 flex-1 overflow-y-auto overflow-x-hidden content-start scrollbar"
            data={data}
            loading={loading}
            onCreate={(tplId) => {
              const find = data.find((item) => item.id === tplId);
              if (find?.original_data?.application_scenarios) {
                selectScript(
                  tplId,
                  find.original_data.application_scenarios.toString(),
                  () => {
                    navigate('/script-list');
                  }
                );
              }
            }}
          />
          <Pagination
            {...pagination}
            className="pt-[20px]"
            pageSizeOptions={[12, 24, 48, 96]}
            showJumper
          />
        </div>
      </MainContent>
    </Page>
  );
}
