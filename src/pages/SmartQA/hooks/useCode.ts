import { LoginStateAtom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { GenerateMiaowenCode } from '@/pb/api/DigitalManProc';
import { useCreation } from 'ahooks';
import { useState, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useLocation } from 'react-router-dom';
import { useRecoilState } from 'recoil';
export const useCode = () => {
  const comefrom = useCreation(() => 'miaobo', []);
  const [loginState] = useRecoilState(LoginStateAtom);
  const [userInfo] = useRecoilState(UserInfoAtom);
  const [code, setCode] = useState<string>('');
  //   const location = useLocation();
  //   const uriPath = useMemo(() => location.pathname, [location]);
  const uriPath = useMemo(() => window.location.pathname, []);
  useEffect(() => {
    GenerateMiaowenCode({
      uri_path: uriPath,
    }).then((res) => {
      setCode(res.code);
    });
  }, [loginState, userInfo, uriPath]);

  const memoRes = useMemo(() => ({ code, comefrom }), [code, comefrom]);

  return memoRes;
};
