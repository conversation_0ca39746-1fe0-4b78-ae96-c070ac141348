import { useCreation, useMemoizedFn } from 'ahooks';
import React, { CSSProperties, PropsWithChildren, useState } from 'react';
import classnames from 'classnames';
import { Loading } from 'tdesign-react';
import { css } from '@emotion/css';
interface ISmartQA {
  code: string;
  comefrom: string;
  style?: CSSProperties;
  className?: string;
  visibility: CSSProperties['visibility'];
  onClose: () => void;
  title: string;
}
export default function SmartQA(props: PropsWithChildren<ISmartQA>) {
  const { code, comefrom, title, style, className, visibility, onClose } =
    props;
  const [loading, setLoading] = useState(true);
  const onLoadEnd = () => setLoading(false);
  const memoUrl = useCreation(
    () => `https://ad.qq.com/ai/doc/ai-customer/assets/welcome?comefrom=${comefrom}&code=${code}
`,
    [code, comefrom]
  );
  return (
    <div
      className={css`
        z-index: 4002;
        position: fixed;
        top: 0;
        bottom: 0;
        right: 0;
        min-width: 480px;
        cursor: ew-resize;
        height: 100%;
        border-radius: 0;
        overflow: hidden;
        box-shadow: 0 10px 36px #00000029, 0 6px 15px #00000012;
        background-color: #e2e5ea;
      `}
      style={{ visibility }}
    >
      <iframe
        title={title}
        width="100%"
        height="100%"
        src={memoUrl}
        className={classnames(className)}
        onLoad={onLoadEnd}
        onError={onLoadEnd}
        style={{ visibility }}
      />
      <button
        className={css`
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 6px;
          top: 6px;
          width: 26px;
          height: 26px;
          background: 0;
          border: 0;
          cursor: pointer;
          border-radius: 4px;
          -webkit-appearance: none;
          appearance: none;
        `}
        onClick={() => {
          onClose();
        }}
      >
        <svg
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
        >
          <path
            fill="#626365"
            d="M6.155 5.006a.625.625 0 0 0-.884 0l-.265.265a.625.625 0 0 0 0 .884L8.85 10l-3.845 3.845a.625.625 0 0 0 0 .884l.265.265c.244.244.64.244.883 0L10 11.149l3.845 3.845c.244.244.64.244.883 0l.266-.265a.625.625 0 0 0 0-.884L11.149 10l3.845-3.845a.625.625 0 0 0 0-.884l-.265-.265a.625.625 0 0 0-.884 0L10 8.851 6.155 5.006Z"
          />
        </svg>
      </button>
    </div>
  );
}
