.entry-pop-wrapper {
    position: fixed;
    right: 0px;
    top: 313px;
    transition: top 0.1s ease-out;
    z-index: 1000;

    .entry-button-wrapper {
        z-index: 1020;
        position: fixed;
        right: -36px;
        cursor: pointer;
        transition: transform 0.2s ease-in-out;
        transform: rotate(-15deg) translateX(-28px);
        transform-origin: left top;

        .entry-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64px;
            height: 64px;
            color: #fff;
            border-radius: 32px;
            cursor: pointer;
            position: relative;

            &::before {
                content: '';
                display: block;
                width: 60px;
                /* 根据实际需求调整 */
                height: 60px;
                /* 根据实际需求调整 */
                background-image: url('https://qzonestyle.gdtimg.com/gdt_ui_proj/imghub/dist/1714376919048.png?max_age=31536000');
                /* 替换为你的图像路径 */
                background-size: cover;
                /* 或者使用 contain，根据需求调整 */
                background-position: center;
                background-repeat: no-repeat;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }

    .open {
        transform: rotate(0) translateX(-48px);
        cursor: move;
    }
}