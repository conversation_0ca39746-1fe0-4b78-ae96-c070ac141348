import { createPortal } from 'react-dom';
import SmartQA from '.';
import { useCode } from './hooks/useCode';
import { useMemoizedFn } from 'ahooks';
import { CSSProperties, PropsWithChildren, useState } from 'react';
import { LoadIcon } from 'tdesign-icons-react';
import { css } from '@emotion/css';
import MiaoWen from './MiaoWen';

export default function MiaoWenWrapper(props: PropsWithChildren) {
  const { code, comefrom } = useCode();
  const [visibility, setVisibility] =
    useState<CSSProperties['visibility']>('hidden');
  const handleClose = useMemoizedFn(() => {
    setVisibility('hidden');
  });
  return (
    <>
      {props.children}
      {createPortal(
        <div>
          <MiaoWen
            changeVisibility={(value: CSSProperties['visibility']) => {
              setVisibility(value);
            }}
          />
          <SmartQA
            code={code}
            comefrom={comefrom}
            visibility={visibility}
            onClose={() => {
              handleClose();
            }}
            title=""
          />
        </div>,
        document.body
      )}
    </>
  );
}
