import { css } from '@emotion/css';
import React, { CSSProperties, useRef, useState } from 'react';
import Style from './MiaoWen.module.less';
import classnames from 'classnames';
import { useMove, DIRECTION } from '@/hooks/useMove';
interface IMiaoWenProps {
  changeVisibility: (value: CSSProperties['visibility']) => void;
}
export default function MiaoWen({ changeVisibility }: IMiaoWenProps) {
  const [isHovered, setIsHovered] = useState(false);
  const smartRef = useRef<HTMLDivElement>(null);
  const { onMouseDown, style, isDragging } = useMove(smartRef, {
    direction: [DIRECTION.VERTICAL],
  });
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };
  return (
    <div
      ref={smartRef}
      className={Style['entry-pop-wrapper']}
      onMouseDown={onMouseDown}
      style={{ ...style }}
    >
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={classnames(Style['entry-button-wrapper'], [
          isHovered || isDragging ? Style.open : '',
        ])}
      >
        <div
          className={Style['entry-button']}
          onClick={() => {
            !isDragging && changeVisibility('visible');
          }}
        />
      </div>
    </div>
  );
}
