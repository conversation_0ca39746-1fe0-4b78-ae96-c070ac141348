import idooLoading from '@/assets/images/idooLoading.gif';
import { useLoginCallback } from '@/pages/Login/useLoginCallback';
import './style.less';

// 登录回调地址
export function LoginCallback() {
  const { loginCallbackState } = useLoginCallback();

  return (
    <div className="login-callback">
      {/* <div>*/}
      <div className="login-callback-image-wrap">
        <img src={idooLoading} width={75} height={75} alt="" />
        <div className="mt-32" x-if={loginCallbackState.loading}>
          登录成功，返回中...
        </div>
      </div>
      {/* {loginCallbackState.loading && (*/}
      {/* <div className="mt-32">登录成功，返回中...</div>*/}
      {/* )}*/}
      {/* </div>*/}
    </div>
  );
}
