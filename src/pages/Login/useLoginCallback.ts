import { createLoginHandle } from '@/utils/login/sdk';
import { useMount } from 'ahooks';
import to from 'await-to-js';
import { useCallback, useEffect, useRef, useState } from 'react';

export const useLoginCallback = () => {
  const loginInstRef = useRef(createLoginHandle());
  const [loginCallbackState, setLoginCallbackState] = useState({
    isError: false,
    errMsg: '',
    loading: false,
    finished: false,
  });

  const handleCallbackResult = useCallback(async () => {
    setLoginCallbackState((prev) => ({ ...prev, loading: true }));
    const [err] = await to(loginInstRef.current.processLoginCallback());
    setLoginCallbackState((prev) => ({ ...prev, loading: false }));
    if (err) {
      setLoginCallbackState((prev) => ({ ...prev, isError: true }));
    } else {
      setLoginCallbackState((prev) => ({ ...prev, finished: true }));
    }
  }, []);

  useMount(() => {
    handleCallbackResult();
  });
  return {
    loginCallbackState,
  };
};
