import React, { useEffect, useState } from 'react';
import { TemplateList } from '@/components/TemplateList';
import { TemplateSelector } from '@/components/TemplateSelector';
import { Button, DialogPlugin, Input, Pagination } from 'tdesign-react';
import {
  useGetTemplateList,
  useTemplateSelectorOptions,
} from '@/hooks/template';
import './index.less';
import { SelectScriptModel } from '@/pages/ScriptList/components/SelectScriptModel';
import { getOriginByType, openEditor } from '@/pages/Editor/common/openEditor';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { EMPTY_SCRIPT_RESEARCH_ID } from '../ScriptList/const';
import { EMPTY_TEMPLATE_ID, CONTENT_TYPE_MAP } from '@/const/common';
import { useNavigate } from 'react-router-dom';
import { Page, MainContent } from '@/components/Layout';
import { useDebounce } from 'ahooks';
import { SearchIcon } from 'tdesign-icons-react';
import { showSelectDeviceSizeModel } from '@/components/TemplateDialog';
import { FlattenSelect } from '../../components/FlattenSelect';

const classPrefix = 'video-template-page';

export function VideoTemplate() {
  const [selectorValue, setSelectorValue] = useState<Record<string, string>>(
    {}
  );
  const navigate = useNavigate();
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [searchKey, setSearchKey] = useState('');
  const debouncedSearchKey = useDebounce(searchKey, { wait: 500 });
  const { selectorOption } = useTemplateSelectorOptions(
    CONTENT_TYPE_MAP.Video.value
  );
  const {
    loading,
    list: templateList,
    count,
  } = useGetTemplateList(
    selectorValue,
    pageNum,
    pageSize,
    CONTENT_TYPE_MAP.Video.value,
    debouncedSearchKey
  );
  // 初始化筛选器默认值
  useEffect(() => {
    if (!selectorOption) return;
    const firstOption = selectorOption.options[0];
    const values: typeof selectorValue = {
      [selectorOption.selectorId]: firstOption.value,
    };
    firstOption.childenSelector?.map((select) => {
      values[select.selectorId] = select.options[0].value;
    });
    setSelectorValue(values);
  }, [selectorOption]);

  const showSelectScriptModel = (
    templateId: string,
    application_scenarios: string
  ) => {
    const confirmDia = DialogPlugin.confirm({
      header: (
        <div
          style={{
            padding: '20px 20px 0',
            fontSize: '16px',
            color: '#000000E5',
          }}
        >
          请从下方选择一个脚本（可跳过）
        </div>
      ),
      body: (
        <SelectScriptModel
          scriptType={CONTENT_TYPE_MAP.Video.value}
          application_scenarios={application_scenarios}
          onCreateWithScript={(script) => {
            if (!script && templateId === EMPTY_TEMPLATE_ID) {
              showSelectDeviceSizeModel({
                onChoose: (id) => {
                  openEditor({
                    system: EditorSystemMap.META_HUMAN,
                    contentType: CONTENT_TYPE_MAP.Video.value,
                    origin: getOriginByType({
                      contentType: CONTENT_TYPE_MAP.Video.value,
                      application_scenarios,
                    }),
                    scriptId: EMPTY_SCRIPT_RESEARCH_ID,
                    // templateType: 'course_video',
                    templateId,
                    deviceSize: id,
                  });
                },
              });
            } else {
              openEditor({
                system: EditorSystemMap.META_HUMAN,
                contentType: CONTENT_TYPE_MAP.Video.value,
                origin: getOriginByType({
                  contentType: CONTENT_TYPE_MAP.Video.value,
                  application_scenarios,
                }),
                scriptId: script?.research_id || EMPTY_SCRIPT_RESEARCH_ID,
                // templateType: 'course_video',
                templateId,
              });
            }
            confirmDia.hide();
          }}
          templateId={templateId}
          close={() => {
            confirmDia.hide();
          }}
          onCreateScript={() => {
            confirmDia.hide();
            navigate('/script-list');
          }}
        />
      ),
      footer: null,
      closeOnOverlayClick: false,
      width: 860,
      style: {
        padding: 0,
      },
      placement: 'center',
      className: 'dialog-video-temp',
      onClose: () => {
        confirmDia.hide();
      },
    });
  };

  return (
    <Page title="视频创建" className={classPrefix}>
      <MainContent>
        <div className="flex justify-between border-solid border-b-[1px] border-[#E4EAF4]">
          <div className="px-[3px]" style={{ marginBottom: '20px' }}>
            <Button
              theme="default"
              className="gradient-primary"
              onClick={() => {
                showSelectScriptModel(EMPTY_TEMPLATE_ID, '');
              }}
            >
              从空白页创建
            </Button>
          </div>
          <div className="w-[280px] flex">
            <Input
              value={searchKey}
              onChange={(v) => setSearchKey(v)}
              suffix={<SearchIcon />}
              style={{ border: 'none' }}
              placeholder="请输入你需要搜索的内容"
            />
          </div>
        </div>
        <div className={`${classPrefix}-content`}>
          {!!selectorOption && (
            <>
              <FlattenSelect<string>
                title={selectorOption.label}
                style={{ marginBottom: 16 }}
                site="left"
                labelStyle={{ color: '#00000099', width: '70px' }}
                options={selectorOption.options.map((option) => ({
                  value: option.value,
                  label: option.label,
                  key: `${option.label}-${option.value}`,
                }))}
                value={selectorValue[selectorOption.selectorId]}
                onChange={async (v) => {
                  setSelectorValue(() => {
                    const values = {
                      [selectorOption.selectorId]: v,
                    };
                    selectorOption?.options
                      .find((item) => item.value === v)
                      ?.childenSelector?.map((select) => {
                        values[select.selectorId] = select.options[0].value;
                      });
                    return values;
                    // return {
                    //   ...pre,
                    //   [selectorOption.selectorId]: v,
                    // };
                  });
                  setPageNum(1);
                }}
              />
              {selectorOption.options
                .find(
                  (item) =>
                    item.value === selectorValue[selectorOption.selectorId]
                )
                ?.childenSelector?.map((select) => (
                  <FlattenSelect
                    site="left"
                    title={select.label}
                    style={{ marginBottom: 16 }}
                    options={select.options.map((option) => ({
                      value: option.value,
                      label: option.label,
                      key: `${option.label}-${option.value}`,
                    }))}
                    labelStyle={{ color: '#00000099', width: '70px' }}
                    value={selectorValue[select.selectorId]}
                    onChange={async (v) => {
                      setSelectorValue((pre) => {
                        return {
                          ...pre,
                          [select.selectorId]: v,
                        };
                      });
                      setPageNum(1);
                    }}
                  />
                ))}
            </>
          )}
          <TemplateList
            className="h-0 flex-1 overflow-y-auto overflow-x-hidden scrollbar"
            data={templateList}
            loading={loading}
            onCreate={(id) => {
              const find = templateList.find((item) => item.id === id);
              showSelectScriptModel(
                id,
                find?.original_data.application_scenarios.toString() || ''
              );
            }}
          />
          <Pagination
            style={{ paddingTop: '20px' }}
            showJumper
            current={pageNum}
            pageSize={pageSize}
            pageSizeOptions={[12, 24, 48, 96]}
            total={count}
            onChange={(pageInfo) => {
              setPageNum(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
        </div>
      </MainContent>
    </Page>
  );
}
