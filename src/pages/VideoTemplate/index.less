@import "../../components/Style/mixins/_scrollbar.less";

.video-template-page {

  .main_content {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

  &-title {
    color: rgba(0, 0, 0, 0.9);
    font-style: normal;
    font-weight: 600;
    margin-top: 8px;
    margin-bottom: 16px;
  }

  &-content {
    height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: -20px;
    padding: 20px 0;
    background: #ffffff;
    border-radius: 8px;
  }

}

body .t-portal-wrapper .dialog-video-temp {
  .t-dialog__close {
    margin: 20px;
  }
  .t-dialog__body {
    padding-bottom: 0px;
  }
}

.scrollbar {
  .scrollbar()
}