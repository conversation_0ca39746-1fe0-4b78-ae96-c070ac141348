import { useAIServerCode } from '@/components/AIServerCode/hooks/useAIServerCode';
import { css } from '@emotion/css';
import { useMount } from 'ahooks';
import { useEffect, useState } from 'react';
import { LoadIcon } from 'tdesign-icons-react';
import { Popup, Image, Button } from 'tdesign-react';

// 规范 XNode的props
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface IXNodeProps {}

function MobilePopup(props: IXNodeProps) {
  const { qrcodeUrl, error, initCode, loading } = useAIServerCode();
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    if (visible) {
      initCode();
    }
  }, [initCode, visible]);
  return (
    <>
      <Popup
        onVisibleChange={setVisible}
        content={
          <div
            className={css`
              display: flex;
              flex-direction: column;
              align-items: center;
              font-size: 12px;
              color: #626365;
            `}
          >
            <Image
              x-if={!loading}
              src={qrcodeUrl}
              error={
                <div
                  className={css`
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                    font-size: 12px;
                    color: #626365;
                  `}
                >
                  加载失败,
                  <span
                    style={{ color: 'blue', cursor: 'pointer' }}
                    onClick={() => {
                      initCode();
                    }}
                  >
                    重新加载
                  </span>
                </div>
              }
              loading={<LoadIcon />}
              style={{ width: 100, minHeight: '100px' }}
            />
            <div
              x-if={loading}
              className="flex flex-col items-center justify-center"
              style={{ minHeight: '100px', width: 100 }}
            >
              <LoadIcon />
            </div>
            <span>微信扫码关注</span>
            <span>腾讯广告AI资讯</span>
          </div>
        }
        trigger="hover"
        placement="bottom"
      >
        <div
          className={css`
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            margin-right: 24px;
            :hover {
              background: #edeef2;
            }
          `}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <rect
              x="2.5"
              y="1.5"
              width="11"
              height="13"
              rx="1.5"
              stroke="#0D0D0D"
              style={{ stroke: '#0D0D0D', strokeOpacity: 1 }}
            />
            <path
              d="M3 10L13 10"
              stroke="#0D0D0D"
              style={{ stroke: '#0D0D0D', strokeOpacity: 1 }}
            />
            <path
              d="M7 12H9"
              stroke="#0D0D0D"
              style={{
                stroke: '#0D0D0D',
                strokeOpacity: 1,
                strokeLinecap: 'round',
              }}
            />
          </svg>
        </div>
      </Popup>
    </>
  );
}

// xnode，可以拓展信息，用于渲染时
type XNodeType = {
  [key in 'mobile']: React.FC<IXNodeProps>;
};

const XNodes: XNodeType = {
  mobile: MobilePopup,
};

export default XNodes;
