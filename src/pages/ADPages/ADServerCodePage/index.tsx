import logoBefore from '@/assets/images/admuse/logo-before.png';
import { useAIServerCode } from '@/components/AIServerCode/hooks/useAIServerCode';
import { BaseQRCode } from '@/components/BaseQRCode';
import { css } from '@emotion/css';
import cookies from '@tencent/midas-util/lib/cookies';
import { useMemoizedFn } from 'ahooks';
import { useEffect, useState } from 'react';
import { ArrowRightIcon, CloseCircleIcon } from 'tdesign-icons-react';
import { Image } from 'tdesign-react';
import bg from '@/assets/images/admuse/1440-900_compressed.png';
import AITextImg from '@/assets/images/admuse/ai.png?inline';
import logoADFull from '@/assets/images/admuse/logo-ad-full.png?inline';
import qrcodeContainerBg from '@/assets/images/admuse/ai-server-qrcode-container.png';

const cookieKey = '__avatar_ai_code';

export interface IADServerCodePageProps {
  onServerCodeHidden: () => void;
}

export default function ADServerCodePage(props: IADServerCodePageProps) {
  const { onServerCodeHidden } = props;
  const { qrcodeUrl, loading, error, cancel, initCode } = useAIServerCode({
    autoRefresh: true,
  });
  const [visible, setVisible] = useState(false);

  const handleClose = useMemoizedFn(() => {
    setVisible(false);
    cookies.set(cookieKey, '1', {
      domain: location.host,
      path: '/',
    });
    onServerCodeHidden();
  });

  useEffect(() => {
    const isShow = cookies.get(cookieKey);
    if (!isShow) {
      setVisible(true);
    } else {
      onServerCodeHidden();
    }
  }, [onServerCodeHidden]);
  useEffect(() => {
    if (!visible) return;
    initCode();
    return () => {
      cancel();
    };
  }, [visible, cancel, initCode]);

  if (!visible) {
    return null;
  }
  return (
    <div
      className={css`
        background: url(${bg}) no-repeat;
        height: 100vh;
        width: 100vw;
        position: fixed;
        top: 0;
        z-index: 999;
        background-size: 100vw 100vh;
      `}
    >
      <div
        className={css`
          display: flex;
          height: 100%;
          flex-direction: column;
          align-items: center;
        `}
      >
        {/* logo */}
        <div
          className={[
            css`
              padding: 40px 40px 0 40px;
              display: flex;
              align-items: center;
            `,
            'w-full justify-between',
          ].join(' ')}
        >
          <div className="flex">
            <Image
              src={logoADFull}
              style={{ height: 30, background: 'none' }}
            />
          </div>
          <div>
            <CloseCircleIcon
              style={{ height: '30px', color: '#fff', width: '30px' }}
              height={30}
              width={30}
              className="cursor-pointer"
              onClick={handleClose}
            />
          </div>
        </div>
        {/* title */}
        <div
          className={css`
            font-size: 5vh;
            font-weight: bold;
            margin: 50px 0 30px 0;
            color: white;
            text-align: center;
          `}
        >
          <div className="flex gap-2 items-center justify-center">
            <span>腾讯广告</span>
            <Image
              src={AITextImg}
              style={{
                background: 'none',
              }}
              className={css`
                img {
                  width: auto;
                  height: 4.5vh;
                  top: -0.4vh;
                }
              `}
            />
          </div>
          <span>官方服务号正式上线啦</span>
        </div>
        {/* 二维码部分 */}
        <div
          className={css`
            width: 70vh;
            padding: 4vh;
            // background-color: rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            border-radius: 3vh;
            gap: 16px;
            height: 45vh;
            position: relative;
            background: url(${qrcodeContainerBg}) no-repeat;
            background-size: cover;
            background-position: center;
          `}
        >
          <BaseQRCode
            // loadingStyle={{
            //   width: '24vh',
            //   height: '24vh',
            // }}
            style={{
              height: '20vh',
              marginTop: '3.5vh',
            }}
            imgStyle={{
              height: '20vh',
              width: '20vh',
              borderRadius: '1vh',
            }}
            qrCode={qrcodeUrl || ''}
            loading={loading}
          />
          <div
            onClick={handleClose}
            className={`${css`
              background-color: #e7f7fa;
              border-radius: 8px;
              font-size: 2vh;
              cursor: pointer;
              width: 260px;
              height: 40px;
            `} flex items-center justify-center`}
          >
            AI，就关注我&nbsp;
            <ArrowRightIcon />
          </div>
          {/* <Image
            src={qrcodeContainerBg}
            className="absolute w-full h-full object-cover"
            style={{
              left: 0,
              top: 0,
              background: 'none',
            }}
          /> */}
        </div>
        {/* bottom */}
        <div
          className={css`
            margin-top: 6vh;
            font-size: 3vh;
            text-align: center;
            color: white;
          `}
        >
          腾讯广告最新、最全的AI产品应用矩阵尽在&nbsp;
          <span
            className={css`
              color: blue;
            `}
          >
            「腾讯广告AI」
          </span>
          <br />
          妙问·妙思·妙播
        </div>
      </div>
    </div>
  );
}
