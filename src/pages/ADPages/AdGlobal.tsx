import {
  IProductEditorDrawerInstance,
  ProxyProductEditorDrawer,
} from '@/pages/ADPages/ProductLib/ProductEditDrawer';
import { SimpleEditorConfigContext } from '@/pages/Editor/SimpleEditor/context';
import { ExtractReactContextType } from '@/utils/type-util';
import {
  Fragment,
  PropsWithChildren,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import ADServerCodePage from './ADServerCodePage';
import MiaoWenWrapper from '@/pages/SmartQA/MiaoWenWrapper';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IADGlobalProps {}
export function ADGlobal(props: PropsWithChildren<IADGlobalProps>) {
  const { children } = props;
  const [ready, setReady] = useState(false);
  const [hiddenServerCode, setHiddenServerCode] = useState(false);
  const drawerRef = useRef<IProductEditorDrawerInstance>(null);
  // window.__pagedoo_api = {
  //   ...window.__pagedoo_api,
  //   adProductEditorApi: drawerRef.current,
  // };
  const memoEditorApiExtend = useMemo<
    ExtractReactContextType<typeof SimpleEditorConfigContext>
  >(() => {
    if (!ready) return {};
    return {
      pagedooApiExtend: {
        adProductEditorApi: drawerRef.current,
      },
    };
  }, [ready]);

  const showMiaoWen = useMemo(() => hiddenServerCode, [hiddenServerCode]);
  const showChildren = useMemo(() => hiddenServerCode, [hiddenServerCode]);

  useLayoutEffect(() => {
    setReady(true);
  }, []);

  // 首次展示 ai 服务代码提示，判断是否要展示提示

  return (
    <>
      <SimpleEditorConfigContext.Provider value={memoEditorApiExtend}>
        <Fragment x-if={ready && showChildren}>{children}</Fragment>
        <ADServerCodePage
          onServerCodeHidden={() => setHiddenServerCode(true)}
        />
        <ProxyProductEditorDrawer ref={drawerRef} />
        <MiaoWenWrapper x-if={showMiaoWen} />
      </SimpleEditorConfigContext.Provider>
    </>
  );
}
