import { ISchema } from '@formily/react';

interface IField {
  /**
   * 字段名称
   */
  name: string;
  /**
   * 字段中文名
   */
  label?: string;
  /**
   * text 单行文本
   * number 数字
   * multi_text 多行文本
   * option 枚举值
   */
  type: 'text' | 'number' | 'multi_text' | 'option';
  /**
   * 最小值
   */
  min?: number;

  /**
   * 最大值
   */
  max?: number;

  /** type=option时存在 */
  options?: {
    label: string;
    value: string | number;
  }[];

  required?: boolean;
  /**
   * 表单placeholder
   */
  placeholder?: string;

  /**
   * 额外给formily schema 的属性
   */
  extraSchemaProps?: ISchema;
}

export interface IProductFieldConfig {
  fields: IField[];

  scenarios: {
    /**
     * 商品类型
     */
    product_type: string;
    fields: Omit<IField, 'type'>[];
  }[];
}
