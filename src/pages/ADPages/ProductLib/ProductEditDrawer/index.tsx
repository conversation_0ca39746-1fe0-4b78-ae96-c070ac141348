import React, {
  forwardRef,
  ReactNode,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  <PERSON><PERSON>,
  Drawer,
  DrawerProps,
  MessagePlugin,
  Space,
} from 'tdesign-react';
import {
  IProductEditorFormWrapperProps,
  IProductFormInstance,
  ProductEditorFormWrapper,
} from './ProductEditForm';
import '@/global.less';
import to from 'await-to-js';
import { UpdateProduct } from '@/pb/api/DigitalManProc';
import { useMemoizedFn } from 'ahooks';
import BigJS from 'big.js';
export interface IProductEditDrawerProps
  extends Pick<IProductEditorFormWrapperProps, 'productId'>,
    Pick<React.HTMLAttributes<HTMLElement>, 'style' | 'className'> {
  show: boolean;
  onClose: () => void;
  /**
   * 修改成功回调
   */
  onSuccess?: () => void;
  /**
   * 取消回调
   */
  onCancel?: () => void;
  title?: ReactNode;
  width?: string;

  /**
   * 透传tdesign drawer props
   */
  tdDrawerProps?: DrawerProps;
}
// 商品信息编辑侧边抽屉
export function ProductEditDrawer(props: IProductEditDrawerProps) {
  const {
    show,
    onClose,
    title,
    width = '776px',
    onCancel,
    onSuccess,
    style,
    className,
    tdDrawerProps,
    ...rest
  } = props;
  const formRef = useRef<IProductFormInstance>(null);
  const [ready, setReady] = useState(false);
  const [isModifying, setIsModifying] = useState(false);

  const handleModify = async (values: any) => {
    // 触发商品修改
    setIsModifying(true);
    const [err] = await to(
      UpdateProduct({
        ...values,
        product_id: rest.productId,
        price: BigJS(values.price).times(100).toFixed(0),
      })
    );
    setIsModifying(false);
    if (err) throw err;
  };
  const reset = useMemoizedFn(() => {
    setReady(false);
    setIsModifying(false);
  });
  const handleClose = useMemoizedFn(() => {
    reset();
    onClose();
  });
  return (
    <Drawer
      {...tdDrawerProps}
      visible={show}
      destroyOnClose
      onClose={handleClose}
      style={{ ...style }}
      className={`${className || ''}`}
      footer={
        <>
          <Space className="pagedoo-meta-live-global">
            <Button
              theme="primary"
              className="gradient-primary"
              disabled={!ready || isModifying}
              loading={isModifying}
              onClick={async () => {
                try {
                  const values = await formRef.current?.submit();
                  const [err] = await to(handleModify(values));
                  // 发起修改
                  if (err) {
                    void MessagePlugin.error(
                      `修改商品详情失败（${err.message}）`
                    );
                    return;
                  }
                  void MessagePlugin.success(
                    `商品${values?.product_name || ''}知识编辑成功`
                  );
                  onSuccess?.();
                  handleClose();
                } catch (e) {}
              }}
            >
              确定
            </Button>
            <Button
              theme="default"
              className="gradient-default"
              onClick={() => {
                onCancel?.();
                handleClose();
              }}
            >
              取消
            </Button>
          </Space>
        </>
      }
      header={title}
      size={width}
    >
      <ProductEditorFormWrapper
        ref={formRef}
        {...rest}
        onReady={() => setReady(true)}
      />
    </Drawer>
  );
}

interface IShowProductEditorDrawerOptions
  extends Pick<
    IProductEditDrawerProps,
    | 'productId'
    | 'title'
    | 'onCancel'
    | 'onSuccess'
    | 'width'
    | 'style'
    | 'className'
    | 'tdDrawerProps'
  > {
  onClose?: () => void;
}
export interface IProductEditorDrawerInstance {
  show: (options: IShowProductEditorDrawerOptions) => void;
  close: () => void;
}
export const ProxyProductEditorDrawer =
  forwardRef<IProductEditorDrawerInstance>((_, ref) => {
    const [showOptions, setShowOptions] =
      useState<IShowProductEditorDrawerOptions>();

    useImperativeHandle(ref, () => ({
      show: (options) => setShowOptions(options),
      close: () => {
        setShowOptions(undefined);
      },
    }));

    return (
      <ProductEditDrawer
        show={!!showOptions}
        {...showOptions}
        onClose={() => {
          setShowOptions(undefined);
          showOptions?.onClose?.();
        }}
        productId={showOptions?.productId || ''}
        title={showOptions?.title}
      />
    );
  });
