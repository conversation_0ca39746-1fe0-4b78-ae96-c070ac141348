import { FlattenSelect } from '@/components/FlattenSelect';
import { Field, FormPath } from '@formily/core';
import { ISche<PERSON>, observer, useField, useFieldSchema } from '@formily/react';
import { useLayoutEffect, useMemo, useRef, useState } from 'react';
import {
  FormItemProps,
  Input,
  InputNumber,
  Textarea,
  TextareaProps,
} from 'tdesign-react';
import FormItem from '@formily/tdesign-react/esm/form-item';
import '@formily/tdesign-react/esm/form-item/style';
import { TextareaRefInterface } from 'tdesign-react/es/textarea/Textarea';
import { IProductFieldConfig } from './typings';
import Styles from './style/form-util.module.less';

type ProductFieldDef = IProductFieldConfig['fields'][0];

const AutoSizingTextArea = observer(
  (props: TextareaProps) => {
    const ref = useRef<TextareaRefInterface>();
    const { rows = 8, ...rest } = props;
    const [realRows, setRows] = useState(rows);
    const field = useFieldSchema();
    useLayoutEffect(() => {
      if (!ref.current) return;
      const fontSize = parseInt(
        window
          .getComputedStyle(ref.current.textareaElement)
          .fontSize.replace('px', ''),
        10
      );
      const { width } = ref.current.textareaElement.getBoundingClientRect();
      if (!isNaN(fontSize)) {
        if (typeof field.maxLength === 'number') {
          const maxRows = Math.min(
            16,
            Math.floor(field.maxLength / (width / fontSize))
          );
          setRows(maxRows);
        }
      }
    }, [field.maxLength, ref]);
    const newProps = useMemo(() => {
      const p = { ...rest };
      if (p.maxlength === 0) delete p.maxlength;
      return p;
    }, [rest]);
    return (
      <Textarea ref={ref} {...newProps} rows={realRows || 4} autosize={false} />
    );
  },
  {
    displayName: 'AutoSizingTextArea',
  }
);

/**
 * 作用于有枚举的情况
 */
const EnumField = observer(
  (props: { value: unknown; onChange: (value: unknown) => void }) => {
    const field = useField<Field>();
    const { value, onChange } = props;
    const { dataSource } = field;

    return (
      <FlattenSelect
        style={{
          height: '40px',
        }}
        // className={flattenSelectStyle.flattenSelectContainer}
        options={dataSource.map((item, idx) => ({
          label: item.label || '',
          key: item.value || idx,
          value: item.value || '',
        }))}
        value={value}
        onChange={async (value) => {
          onChange(value);
        }}
        theme="block"
      />
    );
  },
  {
    displayName: 'ProductEnumField',
  }
);

const convertNumberField = (field: ProductFieldDef): ISchema => {
  const schema: ISchema = {
    type: 'number',
    'x-component': InputNumber,
    'x-component-props': {},
  };
  if (typeof field.min === 'number') {
    schema.minimum = field.min;
    schema['x-component-props'] = {
      ...schema['x-component-props'],
      min: field.min,
    };
  }
  if (typeof field.max === 'number') {
    schema.maximum = field.max;
    schema['x-component-props'] = {
      ...schema['x-component-props'],
      max: field.max,
    };
  }
  return schema;
};
const convertTextField = (_field: ProductFieldDef): ISchema => {
  const schema: ISchema = {
    type: 'string',
    'x-component': Input,
    'x-component-props': {},
  };
  return schema;
};

const convertMultiTextField = (field: ProductFieldDef): ISchema => {
  const schema: ISchema = {
    type: 'string',
    'x-component': AutoSizingTextArea,
    'x-component-props': {
      rows: 6,
    } satisfies TextareaProps,
  };
  if (typeof field.max === 'number' && field.max > 0) {
    schema.maxLength = field.max;
    schema['x-component-props'].maxlength = field.max;
  }
  return schema;
};

const convertOptionField = (field: ProductFieldDef): ISchema => {
  const schema: ISchema = {
    type: typeof field.options?.[0]?.value === 'number' ? 'number' : 'string',
    'x-component': EnumField,
    enum:
      field.options?.map((opt) => ({
        key: opt.value,
        label: opt.label,
        value: opt.value,
      })) || [],
  };
  return schema;
};

const fieldTypeMapping: Record<
  IProductFieldConfig['fields'][0]['type'],
  ((field: ProductFieldDef) => ISchema) | null
> = {
  text: convertTextField,
  multi_text: convertMultiTextField,
  number: convertNumberField,
  option: convertOptionField,
};

export const convertToJSONSchema = (
  data: Record<string, any>,
  fields: IProductFieldConfig['fields']
): ISchema => {
  //   const
  const schema: ISchema = {
    type: 'object',
    properties: {},
  };
  const { properties } = schema;
  for (const field of fields) {
    const type: ISchema['type'] = 'string';
    const newProp: ISchema = {
      type,
      title: field.label,
      'x-decorator': FormItem,
      'x-decorator-props': {
        labelAlign: 'left',
        labelWidth: '80px',
        className: Styles.formItem,
        colon: false,
      },
    };
    let extraSchema: ISchema | null = null;
    const converter = fieldTypeMapping[field.type];
    if (converter) extraSchema = converter(field);
    Object.assign(newProp, extraSchema);
    const path = new FormPath(field.name);
    const defaultValue = path.getIn(data);
    if (typeof defaultValue !== 'undefined') {
      newProp.default = defaultValue;
    }
    if (field.required) newProp.required = true;
    if (field.placeholder)
      newProp['x-component-props'] = {
        ...newProp['x-component-props'],
        placeholder: field.placeholder,
      };

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    properties[field.name] = newProp;
  }
  return schema;
};

/**
 * 转换后台返回的字段后进行合并
 */
export const resolveMergedFields = (
  productFieldConfig: IProductFieldConfig,
  productType: string
): IProductFieldConfig['fields'] | null => {
  const target = productFieldConfig.scenarios.filter(
    (f) => f.product_type === productType
  )[0];
  if (!target) return null;
  const mergedFields: IProductFieldConfig['fields'] = target.fields
    .map((field) => {
      const finded = productFieldConfig.fields.filter(
        (commonField) => commonField.name === field.name
      )[0];
      if (finded) {
        // 对field 的label type 进行判断，如果为空字符串则不要进行覆盖
        const newField: Record<string, any> = {
          ...field,
        };
        ['label', 'type'].forEach((key) => {
          if (newField[key] === '') delete newField[key];
        });
        return {
          ...finded,
          ...newField,
        };
      }
      return undefined;
    })
    .filter(Boolean) as IProductFieldConfig['fields'];
  return mergedFields;
};
