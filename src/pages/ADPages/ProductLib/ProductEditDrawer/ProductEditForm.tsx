import { User<PERSON>nfo<PERSON>tom } from '@/model/user';
import { GetProductDetail, ProductFieldsConfig } from '@/pb/api/DigitalManProc';
import { sleep } from '@/utils/sleep';
import { css } from '@emotion/react';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider, ISchema } from '@formily/react';
import '@formily/tdesign-react/esm/form-item/style';
import { useMemoizedFn } from 'ahooks';
import to from 'await-to-js';
import {
  forwardRef,
  useImperativeHandle,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react';
import { useRecoilValue } from 'recoil';
import { Loading, Image, Link } from 'tdesign-react';
import { convertToJSONSchema, resolveMergedFields } from './product-form-utils';
import { IProductFieldConfig } from './typings';
import failImage from '@/assets/images/fail.png';
import BigJS from 'big.js';

export interface IProductEditorFormWrapperProps {
  /**
   * 商品id
   */
  productId: string;

  onReady?: () => void;
}

export interface IProductFormInstance {
  /**
   * 触发表单提交
   * @throws 表单校验不通过异常
   */
  submit: () => Promise<Record<string, unknown>>;
}

export const ProductEditorFormWrapper = forwardRef<
  IProductFormInstance,
  IProductEditorFormWrapperProps
>((props: IProductEditorFormWrapperProps, ref) => {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const { productId, onReady = () => {} } = props;
  const userInfoState = useRecoilValue(UserInfoAtom);
  const [productDetail, setProductDetail] = useState<Record<string, any>>();
  const [fieldConfig, setFieldConfig] = useState<IProductFieldConfig>();
  const [loading, setLoading] = useState(false);
  const [productError, setProductError] = useState<{
    errMsg: string;
  }>();
  const [ready, setReady] = useState(false);
  const memoOnReady = useMemoizedFn(onReady);

  const loadProduct = useMemoizedFn(async () => {
    setLoading(true);
    setProductError(undefined);
    const [err, results] = await to(
      Promise.all([
        GetProductDetail({
          product_id: productId,
        }),
        ProductFieldsConfig({}),
      ])
    );
    setLoading(false);
    if (err) {
      setProductError({
        errMsg: err.message,
      });
      return;
    }
    const [productDetailResp, fieldConfigResp] = results;
    // 处理价格
    productDetailResp.price = BigJS(productDetailResp.price.replace(/\D/g, ''))
      .div(100)
      .toFixed(2);
    setProductDetail(productDetailResp);
    let fieldConfig: IProductFieldConfig;
    try {
      fieldConfig = JSON.parse(fieldConfigResp.config_data);
      setFieldConfig(fieldConfig);
    } catch (e) {
      setProductError({
        errMsg: '解析商品字段失败',
      });
      return;
    }
    setReady(true);
  });

  /**
   * 初始化触发拉取商品、和商品字段配置
   */
  useLayoutEffect(() => {
    if (!userInfoState?.adExtend?.account_id) return;
    // 拉取商品详情，商品字段配置
    loadProduct();
  }, [loadProduct, userInfoState?.adExtend?.account_id]);

  /**
   * 变为ready时触发onReady回调
   */
  useLayoutEffect(() => {
    if (!ready) return;
    memoOnReady();
  }, [memoOnReady, ready]);

  const jsonSchema = useMemo(() => {
    if (!fieldConfig || !productDetail) return null;
    // 先去 scenarios 找字段
    const target = fieldConfig.scenarios.filter(
      (f) => f.product_type === productDetail.product_type
    )[0];
    if (!target) return null;
    const mergedFields = resolveMergedFields(
      fieldConfig,
      productDetail.product_type
    );
    if (!mergedFields) return null;
    return convertToJSONSchema(productDetail, mergedFields);
  }, [fieldConfig, productDetail]);
  if (loading)
    return (
      <Loading
        css={css`
          top: 50%;
          position: absolute;
          transform: translateX(-50%) translateY(-50%);
          left: 50%;
        `}
      />
    );
  if (productError) {
    return (
      <div className="flex-1 h-full flex items-center justify-center flex-col gap-2">
        <Image
          src={failImage}
          style={{
            width: '100px',
            background: 'transparent',
          }}
          fit="contain"
        />
        <span className="text-14">
          获取商品详情失败，请
          <Link theme="primary" onClick={loadProduct}>
            刷新
          </Link>
          重试
        </span>
      </div>
    );
  }
  if (!jsonSchema) return null;
  return <InternalForm ref={ref} schema={jsonSchema} />;
});

const InternalForm = forwardRef<
  IProductFormInstance,
  {
    schema: ISchema;
    onSubmit?: (values: Record<string, unknown>) => void;
  }
>((props, ref) => {
  const { schema, onSubmit } = props;
  const form = useMemo(() => {
    return createForm({});
  }, []);
  const handleSubmit = async () => {
    try {
      return (await form.submit(onSubmit)) as Record<string, unknown>;
    } catch (e) {
      console.error('表单校验失败', e);
      throw e;
    }
  };
  useImperativeHandle(ref, () => ({
    submit: handleSubmit,
  }));
  const SchemaField = useMemo(() => createSchemaField({}), []);
  return (
    <FormProvider form={form}>
      <SchemaField schema={schema} />
    </FormProvider>
  );
});
