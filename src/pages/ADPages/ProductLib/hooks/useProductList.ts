import { useMemoizedFn, usePagination } from 'ahooks';
import { Service } from 'ahooks/lib/usePagination/types';
import { useCallback, useState } from 'react';
import { ListProducts, ListProductsResponse } from '@/pb/api/DigitalManProc';
import to from 'await-to-js';
import { PromiseType } from '@/utils/type-util';
import { useOperateShopInfo } from '@/configs/admuse/product-lib/hooks/useShopInfo';
import BigJS from 'big.js';

type ListItemType = ListProductsResponse['data']['product_list'][0];
export type { ListItemType as ProductItem };
export const useProductList = (options?: {
  defaultPageSize?: number;
  defaultCurrentPage?: number;
  manual?: boolean;
  /**
   * 分页模式，
   */
  mode?: 'infinite-scroll' | 'table';
}) => {
  const {
    defaultPageSize = 10,
    defaultCurrentPage = 1,
    manual = false,
    mode = 'table',
  } = options || {};

  // 当前选中的小店
  const { selectedShopInfo } = useOperateShopInfo();
  const { shopId: selectedShopId } = selectedShopInfo || {};
  /**
   * localList
   */
  const [localList, setLocalList] = useState<ListItemType[]>([]);

  const loadData: Service<
    {
      total: number;
      list: ListItemType[];
    },
    [
      {
        current: number;
        pageSize: number;
        /**
         * 小店id，后台非必填，用于未绑定小店情况下的搜索
         */
        shopId?: string;
        search?: string;
      }
    ]
  > = useCallback(
    async ({ current, pageSize, shopId, search }) => {
      const [err, resp] = await to(
        ListProducts({
          keyword: search || '',
          shop_id: shopId || selectedShopId || '',
          page: current,
          page_size: pageSize,
        })
      );
      if (err) throw err;
      try {
        const newList = resp.product_list.slice().map((item) => {
          return {
            ...item,
            price: BigJS(item.price.replace(/\D/g, '')).div(100).toFixed(2),
          };
        });
        return {
          total: resp.page.total,
          list: newList,
        };
      } catch (e) {
        console.error('loadData error', e);
        throw e;
      }
    },
    [selectedShopId]
  );
  const { data, loading, pagination, refreshAsync, cancel, runAsync, params } =
    usePagination(loadData, {
      defaultCurrent: defaultCurrentPage,
      defaultPageSize,
      manual,
    });
  /**
   * 加载更多
   */
  const loadMore = useMemoizedFn(
    async (
      params: Pick<Parameters<typeof runAsync>[0], 'search' | 'shopId'>
    ) => {
      if (mode !== 'infinite-scroll') return;
      const nextPage = pagination.current + 1;
      const data = await runAsync({
        current: nextPage,
        pageSize: pagination.pageSize,
        ...params,
      });
      if (data.list.length > 0) {
        setLocalList((prev) => prev.concat(data.list));
      }
      return data;
    }
  );
  /**
   * 加载新数据，覆盖当前localData
   */
  const runWithReset = useMemoizedFn(
    async (
      params: Pick<Parameters<typeof runAsync>[0], 'search' | 'shopId'>
    ) => {
      setLocalList([]);
      /** 取消之前正在加载的 */
      cancel();
      const results = await runAsync({
        current: 1,
        pageSize: pagination.pageSize,
        ...params,
      });
      setLocalList(results.list);
      return results;
    }
  );
  const resetList = useMemoizedFn(() => setLocalList([]));
  return {
    cancel,
    pagination,
    refresh: refreshAsync,
    runAsync,
    data,
    loading,
    loadMore,
    list: localList,
    runWithReset,
    resetList,
    requestParams: params,
  };
};
