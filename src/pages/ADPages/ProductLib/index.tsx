// 商品知识库

import { GlobalMessage } from '@/components/GlobalMessage';
import { MainContent, Page } from '@/components/Layout';
import { ShopInfoApiContext } from '@/configs/admuse/product-lib/hooks/useShopInfo';
import { ShopInfo } from '@/configs/admuse/product-lib/ShopInfo';
import {
  IProductActionBarInstance,
  ProductActionBar,
} from '@/pages/ADPages/ProductLib/ProductActionBar';
import { useContext, useLayoutEffect, useRef, useState } from 'react';
import { IProductLibTableInstance, ProductLibTable } from './ProductLibTable';
import Styles from './style.module.less';
import { useForceReRender } from '@/hooks/useForceReRender';
import { BindShopEntry } from '@/configs/admuse/product-lib/BindShop/BindShopEntry';
import { useMemoizedFn } from 'ahooks';
import to from 'await-to-js';
import { MessagePlugin } from 'tdesign-react';
import { RespError } from '@/pb/config';

function Content() {
  const [refreshing, setRefreshing] = useState(false);
  const [isInit, setIsInit] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const tableRef = useRef<IProductLibTableInstance>(null);
  const actionBarRef = useRef<IProductActionBarInstance>(null);
  const { syncShopInfo, shopInfo, modifiedPromise } =
    useContext(ShopInfoApiContext)!;
  const { forceKey: forceTableKey, forceRender: forceRenderTable } =
    useForceReRender();
  const memoSyncShopInfo = useMemoizedFn(syncShopInfo);

  const [latestTimeBeforeUpdate, setLatestTimeBeforeUpdate] = useState('');

  const handleRefresh = useMemoizedFn(async () => {
    return (async () => {
      actionBarRef.current?.resetSearchString();
      /** 点击刷新时，重置表格  */
      const list = await tableRef.current?.refresh({
        reset: true,
      });
      if (Array.isArray(list) && list.length) {
        setLatestTimeBeforeUpdate(list[0].create_time);
      }
      setRefreshing(true);
      const [err] = await to(memoSyncShopInfo());
      if (err) {
        setRefreshing(false);
        let errMsg = '商品刷新失败';
        if (err.message.includes('timeout')) {
          errMsg = '商品刷新超时，请稍后重试';
        } else if (RespError.is(err)) {
          errMsg = err.resultInfo || err.message;
        } else if (err.message.includes('aborted')) {
          // 取消的情况不要弹错误提示
          errMsg = '';
        }
        if (errMsg) {
          // MessagePlugin.error(errMsg);
        }
        return;
      }
      // if (force) {
      //   forceRenderTable();
      // } else {
      // }
      if (tableRef.current) {
        const tableRefreshErr = await tableRef.current.refresh({
          reset: true,
        });
        if (tableRefreshErr instanceof Error) {
          let errMsg = '商品刷新失败';
          if (RespError.is(tableRefreshErr)) {
            errMsg = tableRefreshErr.resultInfo || tableRefreshErr.message;
          }
          MessagePlugin.error(errMsg);
        } else {
          MessagePlugin.success('商品刷新成功');
        }
      }
    })().finally(() => {
      setRefreshing(false);
    });
  });

  const refreshTable = useMemoizedFn(async () => {
    if (tableRef.current) {
      return tableRef.current.refresh({
        reset: true,
        searchString: actionBarRef.current?.getSearchString(),
      });
    }
  });
  /**
   * 监听修改小店信息后触发刷新
   */
  useLayoutEffect(() => {
    if (!modifiedPromise) return;
    modifiedPromise.promise.then(() => {
      handleRefresh();
    });
  }, [forceRenderTable, handleRefresh, modifiedPromise]);

  /**
   * 初始化加载 -> 如果没有上次刷新时间则触发同步小店 -> 触发表格刷新
   */
  useLayoutEffect(() => {
    if (!shopInfo) return;
    if (isInit) return;
    if (shopInfo.lastSyncTime === '') {
      handleRefresh();
    } else {
      refreshTable();
    }
    setIsInit(true);
  }, [handleRefresh, isInit, refreshTable, shopInfo]);

  return (
    <>
      <MainContent
        style={{
          padding: '18px',
        }}
      >
        <div className={`${Styles.innerContent} rounded-4 flex flex-col`}>
          <ProductActionBar
            ref={actionBarRef}
            onRefresh={handleRefresh}
            onSearch={async () => {
              // const force = search === searchString;
              // setSearchString(search || '');
              // if (force) {
              //   // 强制刷新
              //   tableRef.current?.refresh();
              // }
              refreshTable();
            }}
            syncTime={shopInfo?.lastSyncTime || ''}
            refreshing={refreshing}
            disabled={tableLoading}
          />
          <ProductLibTable
            className="h-full overflow-hidden flex-1"
            ref={tableRef}
            key={forceTableKey}
            latestTimeBeforeUpdate={latestTimeBeforeUpdate}
            // onFinishRefresh={(status) => {
            //   if (refreshing) {
            //     status === 'fail'
            //       ? MessagePlugin.error('商品刷新失败')
            //       : MessagePlugin.success('商品刷新成功');
            //   }
            //   setRefreshing(false);
            // }}
            onLoadingStateChange={(loading) => setTableLoading(loading)}
          />
        </div>
      </MainContent>
      {/* <GlobalMessage
        x-if={refreshing}
        duration={0}
        theme="loading"
        content="刷新商品中..."
      /> */}
    </>
  );
}

export function ProductLibPage() {
  // const [refreshPromise,setRefreshPromise] =
  //   useState<ReturnType<typeof createDelayPromise<void>>>();
  return (
    <BindShopEntry ignoreSkip nested hideSkipBtn>
      <Page title="商品知识库">
        <div className="flex flex-col h-full gap-[16px]">
          <ShopInfo>{() => <Content />}</ShopInfo>
        </div>
      </Page>
    </BindShopEntry>
  );
}
