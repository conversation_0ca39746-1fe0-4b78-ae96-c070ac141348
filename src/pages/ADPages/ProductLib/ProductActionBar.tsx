// 刷新商品-搜索商品输入框

import '@/global.less';
import { css } from '@emotion/react';
import to from 'await-to-js';
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import { SearchIcon } from 'tdesign-icons-react';
import { Button, Input } from 'tdesign-react';
export interface IProductActionBarProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'style' | 'className'> {
  /**
   * 触发商品刷新
   */
  onRefresh: () => Promise<void>;

  onSearch: (data: { search: string }) => Promise<void>;

  syncTime: string;
  refreshing: boolean;

  disabled?: boolean;
}

export interface IProductActionBarInstance {
  refresh: () => void;

  resetSearchString: () => void;

  getSearchString: () => string;
}

export const ProductActionBar = forwardRef<
  IProductActionBarInstance,
  IProductActionBarProps
>((props: IProductActionBarProps, ref) => {
  const {
    className,
    style,
    onRefresh,
    onSearch,
    syncTime,
    refreshing,
    disabled,
  } = props;
  const [searchVal, setSearchVal] = useState('');
  const computedDisabled = refreshing || disabled;
  const doSearch = useCallback(async () => {
    await to(
      onSearch({
        search: searchVal,
      })
    );
  }, [onSearch, searchVal]);

  const handleRefresh = async () => {
    onRefresh();
  };

  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    resetSearchString: () => setSearchVal(''),
    getSearchString: () => searchVal,
  }));
  return (
    <div
      className={`flex pagedoo-meta-live-global  justify-between items-center ${
        className || ''
      }`}
      style={{
        minHeight: '47px',
        ...style,
      }}
    >
      <div className="ml-8 flex gap-2 items-center">
        <Button
          className="gradient-default"
          theme="default"
          onClick={handleRefresh}
          disabled={computedDisabled}
        >
          刷新商品
        </Button>
        <span
          x-if={syncTime}
          style={{
            color: 'rgba(0, 0, 0, 0.4)',
          }}
        >
          上次刷新：{syncTime || ''}
        </span>
      </div>
      <Input
        style={{ width: '389px' }}
        className="h-full"
        disabled={computedDisabled}
        css={css`
          .t-input {
            height: 100%;
            border-radius: 0 4px 0 0;
            &:hover {
              border-color: var(--td-border-level-2-color);
            }
            border-top: none;
            border-right: none;
            border-bottom: none;
          }
          .t-input--focused {
            border-color: var(--td-border-level-2-color);
            box-shadow: none;
          }
          .t-input__inner {
            height: 100%;
          }
        `}
        placeholder="请输入你需要搜索的内容"
        value={searchVal}
        onChange={setSearchVal}
        onEnter={() => doSearch()}
        suffixIcon={
          <SearchIcon className="cursor-pointer" onClick={() => doSearch()} />
        }
      />
    </div>
  );
});

ProductActionBar.displayName = 'ProductActionBar';
