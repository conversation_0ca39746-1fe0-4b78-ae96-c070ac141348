import { ShopInfoApiContext } from '@/configs/admuse/product-lib/hooks/useShopInfo';
import { css } from '@emotion/react';
import { useLatest, useMemoizedFn, useUnmount } from 'ahooks';
import React, {
  forwardRef,
  useContext,
  useImperativeHandle,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react';
import { Image, Link, Loading, PrimaryTableCol, Table } from 'tdesign-react';
import { ProductItem, useProductList } from './hooks/useProductList';
import to from 'await-to-js';
import { ProductEditDrawer } from './ProductEditDrawer';
import { ListProductsResponse } from '@/pb/api/DigitalManProc';
import dayjs from 'dayjs';

export interface IProductLibTableProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'style' | 'className'> {
  /**
   * 搜索关键字
   */
  onFinishRefresh?: (status: 'success' | 'fail') => void;
  onLoadingStateChange?: (loading: boolean) => void;
  latestTimeBeforeUpdate?: string;
}

export interface IProductTableRefreshOptions {
  reset?: boolean;
  searchString?: string;
}

export interface IProductLibTableInstance {
  refresh: (
    options?: IProductTableRefreshOptions
  ) => Promise<Error | void | ProductItem[]>;
}
export const ProductLibTable = forwardRef<
  IProductLibTableInstance,
  IProductLibTableProps
>((props: IProductLibTableProps, ref) => {
  const {
    onFinishRefresh,
    onLoadingStateChange,
    latestTimeBeforeUpdate,
    className,
    style,
  } = props;

  const {
    data,
    loading,
    pagination,
    refresh,
    cancel,
    runAsync,
    requestParams,
  } = useProductList({
    defaultCurrentPage: 10,
    manual: true,
  });

  const latestPagination = useLatest(pagination);
  const latestOnFinishRefresh = useLatest(onFinishRefresh);
  const latestOnLoadingStateChange = useLatest(onLoadingStateChange);
  /** 小店信息上下文 */
  const { shopInfo } = useContext(ShopInfoApiContext) || {};
  const [editProduct, setEditProduct] = useState<{
    productId: string;
    title: string;
  }>();

  /**
   * 上一次第一页数据
   */
  const [lastFirstPageInfo, setLastFirstPageInfo] = useState<{
    list: ProductItem[];
    total: number;
  }>({
    list: [],
    total: 0,
  });

  const getRowClassName = ({ row }: { row: ProductItem }) => {
    if (!latestTimeBeforeUpdate) return;
    if (dayjs(latestTimeBeforeUpdate).isBefore(dayjs(row.create_time))) {
      return 'ad-product-lib-update-row';
    }
    return '';
  };

  const columns: PrimaryTableCol<
    ListProductsResponse['data']['product_list'][0]
  >[] = [
    {
      title: '商品封面',
      colKey: 'product_img',
      width: '100px',
      resizable: false,
      cell: ({ row }) => (
        <Image
          src={row.header_imgs[0]}
          className="rounded-4"
          style={{
            width: '70px',
            height: '70px',
            background: '#000',
          }}
          loading={
            <Loading
              style={{
                width: '14px',
                height: '14px',
              }}
            />
          }
          fit="contain"
        />
        // <BasicImageViewer
        //   width={70}
        //   height={135}
        //   img={row.meta_live_poster}
        //   name="直播预览图"
        // />
      ),
    },
    {
      title: '商品名称',
      colKey: 'product_name',
      width: '243px',
      cell: ({ row }) => <span>{row.product_name}</span>,
    },
    {
      title: '商品价格',
      colKey: 'price',
      width: '150px',
      resizable: false,
      cell: ({ row }) => <span>¥{row.price}</span>,
    },
    {
      title: '操作',
      colKey: 'operation',
      width: '150px',
      resizable: false,
      cell: ({ row }) => {
        // 如果必填字段都填了，文案为修改，否则为补充
        const text = row.is_required_fields_filled ? '修改' : '补充';
        return (
          <div className="flex" style={{ gap: '20px' }}>
            <Link
              theme="primary"
              hover="color"
              onClick={() => {
                setEditProduct({
                  productId: row.product_id,
                  title: `${text}知识`,
                });
              }}
            >
              {text}
            </Link>
          </div>
        );
      },
    },
  ];

  const computedData = useMemo<
    (ProductItem & { markAsNew?: boolean })[]
  >(() => {
    // if (pagination.current > 1) return data;
    if (!data?.list) return [];
    const reqParams = requestParams[0];
    if (typeof reqParams?.search === 'string' && reqParams.search.length > 0)
      return data?.list || [];
    if (
      Array.isArray(lastFirstPageInfo.list) &&
      lastFirstPageInfo.list.length > 0
    ) {
      const diffPages =
        Math.ceil(data.total - lastFirstPageInfo.total) / pagination.pageSize;
    }
    return data?.list || [];
  }, [
    data?.list,
    data?.total,
    lastFirstPageInfo.list,
    lastFirstPageInfo.total,
    pagination.pageSize,
    requestParams,
  ]);

  const doRefresh = useMemoizedFn(
    async (options?: IProductTableRefreshOptions) => {
      if (!shopInfo) return;
      const { reset, searchString = '' } = options || {};
      console.log('刷新表格');
      cancel();
      // 记录上一次请求第一页的数据
      if (latestPagination.current.current === 1 && !requestParams[0]?.search) {
        setLastFirstPageInfo({
          list: data?.list || [],
          total: latestPagination.current.total,
        });
      }
      const [err, res] = await to(
        runAsync({
          current: reset ? 1 : latestPagination.current.current,
          pageSize: latestPagination.current.pageSize,
          shopId: shopInfo.shopId,
          search: searchString,
        })
      );
      latestOnFinishRefresh.current?.(err ? 'fail' : 'success');
      if (res) return res.list;
      if (err) return err;
    }
  );
  useImperativeHandle(ref, () => ({
    refresh: async (options) => {
      return await doRefresh(options);
    },
  }));

  /**
   * 表格加载状态变更
   */
  useLayoutEffect(() => {
    latestOnLoadingStateChange.current?.(loading);
  }, [latestOnLoadingStateChange, loading]);

  // useEffect(() => {
  //   // searchString 变化强制刷新
  //   doRefresh({
  //     reset: true,
  //   });
  // }, [doRefresh, searchString]);

  /**
   * 销毁时取消请求
   */
  useUnmount(() => cancel());

  return (
    <>
      <div
        css={css`
          .t-table__header {
            tr {
              background-color: #f8f6fb;
            }
            tr > th {
              background-color: #f8f6fb;
              border-top: 1px solid var(--td-component-border);
            }
            tr > th:not(:last-child):after {
              content: '';
              position: absolute;
              background: rgba(223, 229, 238, 1);
              width: 1px;
              height: 22px;
              top: 50%;
              margin-top: -11px;
              right: 0;
            }
          }
          .ad-product-lib-update-row {
            background-color: rgba(208, 228, 255, 0.2);
          }
        `}
        className={`${className || ''} flex flex-col`}
        style={{
          ...style,
        }}
      >
        <Table
          rowKey="product_id"
          columns={columns}
          data={computedData || []}
          loading={loading}
          rowClassName={getRowClassName}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
          }}
          className="flex flex-1 flex-col h-0"
          css={css`
            .t-table__content {
              height: 100%;
            }
          `}
          // resizable
          headerAffixedTop
          onPageChange={(pageInfo) => {
            pagination.onChange(pageInfo.current, pageInfo.pageSize);
          }}
          // tableLayout="auto"
          size="medium"
          verticalAlign="middle"
        />
      </div>
      <ProductEditDrawer
        show={!!editProduct}
        onClose={() => setEditProduct(undefined)}
        productId={editProduct?.productId || ''}
        title={editProduct?.title}
        onSuccess={() => {
          cancel();
          refresh();
        }}
      />
    </>
  );
});
ProductLibTable.displayName = 'ProductLibTable';
