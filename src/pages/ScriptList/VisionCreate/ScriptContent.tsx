import { TitleManage } from '../components/TitleManage';
import {
  Button,
  DialogPlugin,
  Divider,
  Loading,
  MessagePlugin,
  Textarea,
} from 'tdesign-react';
import LinkTable from '../components/LinkTable';
import * as Selections from '../components/Selections';
import { useRequest } from 'ahooks';
import { MetaFeedbackSvr } from '@/pb/pb';
import useVisionScriptInfo from '../hooks/useVisionScriptInfo';
import { get } from 'lodash-es';
import { Fragment, useCallback, useEffect } from 'react';
import { MainContent } from '@/components/Layout';
import PopInput from '@/components/PopInput';
import { DownloadIcon, PlayCircleIcon } from 'tdesign-icons-react';
import { FormPath } from '@formily/core';
import { exportExcelByArray } from '@/utils';
import genScriptInfo from './genScriptInfo';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { useNavigate, useSearchParams } from 'react-router-dom';
import ActionSuccess from '@/assets/images/create-condition-success.png';
import VisionGenTips from '@/assets/images/vision-gen-tips.svg';
import { queryScriptList } from '@/components/ScriptForm/utils';
import { VIDEO_SCRIPT_INFO_FEATURE_GATING } from '@/fg/script';
import { BaseScript, IGlobalResourceList } from '@/components/ScriptForm/type';
import { TooltipColumn } from '../components/TooltipColumn';

interface IProps {
  receiveScript?: BaseScript;
  researchId?: string;
  from?: string;
  _blank?: string;
  saveButtonText?: string;
  backText?: string;
  actionValidate?: string;
  cancelCallback?: () => void;
  saveCallback?: (v: Record<string, any>) => void;
}

export function ScriptContent(props: IProps) {
  const navigator = useNavigate();
  const {
    researchId,
    from,
    _blank,
    saveButtonText,
    actionValidate = 'save;back;export;create;',
    backText,
    cancelCallback,
    saveCallback,
    receiveScript,
  } = props;
  const [searchParams] = useSearchParams();

  const [type, application_scenarios] = (from || '').split('__');

  const {
    runAsync: queryScript,
    data: scriptFetched,
    loading: quering,
  } = useRequest(
    (id: string) =>
      queryScriptList(id).then((r) => {
        const [first] = r.script_list || [];
        return JSON.parse(first.script_info) as BaseScript;
      }),
    { manual: true }
  );

  const { views, globalField, setGlobalField, speech, setSpeech } =
    useVisionScriptInfo(scriptFetched || receiveScript);

  const { runAsync: saveScriptReq, loading: saving } = useRequest(
    MetaFeedbackSvr.SaveScript,
    { manual: true }
  );

  const { runAsync: editScriptReq, loading: editing } = useRequest(
    MetaFeedbackSvr.ModifyOneResearch,
    { manual: true }
  );

  const exportScripts = useCallback(() => {
    const header = [
      '序号',
      '画面名称',
      '台词文案',
      '时长',
      '画面大纲',
      '画面贴纸',
      '画面音效',
      '文本贴纸',
    ];
    const data = (views || []).map((item, index) => [
      index,
      FormPath.getIn(item, 'viewName'),
      FormPath.getIn(item, 'speech'),
      FormPath.getIn(item, 'duration'),
      FormPath.getIn(item, '画面大纲'),
      FormPath.getIn(item, 'resources.{patch,special-effects-gif}'),
      FormPath.getIn(item, 'resources.featured-sound-effects'),
      FormPath.getIn(item, '文本贴纸'),
    ]);

    exportExcelByArray([header, ...data], '脚本.xlsx');
  }, [views]);

  const showSuccessDialog = useCallback(
    (researchId?: string) => {
      const typeDesc = Object.entries(CONTENT_TYPE_MAP).find(
        ([, value]) => value.value === type
      )?.[1].label;

      const ref = DialogPlugin({
        width: 680,
        footer: null,
        closeBtn: true,
        closeOnOverlayClick: false,
        closeOnEscKeydown: false,
        body: (
          <div className="flex flex-col items-center pt-12">
            <img className="h-[74px] w-[88px]" src={ActionSuccess} alt="" />
            <div className="my-16">保存脚本成功！</div>
            <div className="my-16">
              您可以继续基于该脚本创建{typeDesc}，也可以进入管理列表查看与管理
            </div>
            <div className="space-x-[8px]">
              <Button
                theme="default"
                className="btn-background-default"
                onClick={() => {
                  ref.hide();
                  navigator('/script-list?sub_type=3');
                }}
              >
                返回管理列表
              </Button>
              <Button
                className="btn-background-primary"
                onClick={() => {
                  ref.hide();
                  navigator(
                    `/script-list?selectResearchId=${
                      researchId || researchId
                    }&from=${from}`
                  );
                }}
              >
                去创建{typeDesc}
              </Button>
            </div>
          </div>
        ),
        onClose: () => {
          ref.hide();
        },
      });
    },
    [from, navigator, type]
  );

  const saveScript = useCallback(
    (scriptName?: string) => {
      if (researchId) {
        const scriptInfo = JSON.stringify(
          genScriptInfo(
            {
              views,
              globalField,
              globalSpeech: speech,
            },
            scriptFetched
          )
        );
        return editScriptReq({
          research_id: researchId,
          nodes_count: views.length,
          script_info: scriptInfo,
        })
          .then(() =>
            saveCallback
              ? saveCallback?.({ scriptInfo, researchId })
              : showSuccessDialog()
          )
          .catch((e) => {
            MessagePlugin.error(`保存失败: ${e.message}`);
          });
      }
      const scriptInfo = JSON.stringify(
        genScriptInfo(
          {
            views,
            globalField,
            globalSpeech: speech,
          },
          receiveScript
        )
      );
      console.log('!!!scriptInfo', scriptInfo);
      return saveScriptReq({
        research_name: scriptName,
        type,
        application_scenarios,
        source: ORIGIN_TYPE.GAME_PROMOTION,
        script_info: [scriptInfo],
        nodes_count: [views.length],
      })
        .then((r) => get(r, 'script_list[0].research_id'))
        .then((r) =>
          saveCallback
            ? saveCallback?.({ scriptInfo, researchId: r })
            : showSuccessDialog(r)
        )
        .catch((e) => {
          MessagePlugin.error(`保存失败: ${e.message}`);
        });
    },
    [
      researchId,
      views,
      globalField,
      speech,
      receiveScript,
      saveScriptReq,
      type,
      application_scenarios,
      scriptFetched,
      editScriptReq,
      saveCallback,
      showSuccessDialog,
    ]
  );

  useEffect(() => {
    if (!researchId) return;

    queryScript(researchId);
  }, [researchId, queryScript]);

  return (
    <Loading style={{ paddingBottom: '100px' }} loading={quering}>
      <MainContent>
        <div className="flex flex-row items-center" x-if={_blank === null}>
          <div className="flex-1 font-medium text-black text-[22px]">
            智能生成的脚本如下
          </div>
        </div>
        <TitleManage
          titleData={{
            title: globalField?.title || '',
            subTitle: globalField?.subTitle || '',
          }}
          onChange={(v) => setGlobalField((x) => ({ ...x, ...v }))}
        />
        <div
          x-if={VIDEO_SCRIPT_INFO_FEATURE_GATING}
          className="flex flex-col gap-[24px]"
        >
          <div className="flex flex-col gap-[12px]">
            <div className="text-[16px] text-[#000] font-bold">台词文案</div>
            <Textarea
              value={speech}
              autosize={{ minRows: 7, maxRows: 7 }}
              onChange={(value) => {
                console.log(value);
                setSpeech(value);
              }}
              allowInputOverMax
            />
          </div>
          <div
            className="flex flex-col gap-[12px]"
            x-if={globalField?.globalResourceList?.length}
          >
            <div className="text-[16px] text-[#000] font-bold">视频元素</div>
            <LinkTable<IGlobalResourceList>
              initialDataSource={globalField?.globalResourceList ?? []}
              onChange={(v) => {
                setGlobalField((x) => ({
                  ...x,
                  globalResourceList: v as IGlobalResourceList[],
                }));
              }}
              fields={[
                {
                  key: 'resourceType',
                  title: '元素类型',
                  width: 200,
                  component: Selections.Text,
                },
                {
                  title: <TooltipColumn type="elementContent" />,
                  key: 'resource',
                  component: Selections.ResourceContent,
                  width: '450px',
                },
                {
                  key: 'showTime',
                  title: '元素出现时间',
                  component: Selections.ShowTimeSet,
                },
              ]}
            />
          </div>
        </div>
      </MainContent>
      <div
        // x-if={!quering}
        className="fixed bottom-0 ml-[-20px] w-full px-[20px] py-[25px] bg-[#fff] bg-opacity-[95%] space-x-[12px] border-t-[1px] border-solid border-[#eee]"
      >
        <Button
          x-if={actionValidate.includes('script')}
          size="large"
          className="btn-background-primary border-none"
          onClick={() => {
            const scriptInfo = JSON.stringify(
              genScriptInfo(
                { views, globalField, globalSpeech: speech },
                scriptFetched || receiveScript
              )
            );
            saveCallback?.({ scriptInfo });
          }}
        >
          重新生成视频
        </Button>
        <Fragment x-if={!researchId && actionValidate.includes('save')}>
          <PopInput title="脚本保存" onConfirm={(v) => saveScript(v)}>
            <Button
              className="btn-background-primary border-none"
              loading={saving || editing}
              size="large"
            >
              {saveButtonText ? saveButtonText : '保存脚本'}
            </Button>
          </PopInput>
        </Fragment>
        <Fragment x-if={researchId && actionValidate.includes('save')}>
          <Button
            className="btn-background-primary border-none"
            loading={saving || editing}
            onClick={() => saveScript()}
            size="large"
          >
            {saveButtonText ? saveButtonText : '保存脚本'}
          </Button>
        </Fragment>
        <Button
          x-if={actionValidate.includes('back')}
          theme="default"
          className="btn-background-light border-none"
          size="large"
          onClick={() => {
            if (cancelCallback) {
              cancelCallback();
              return;
            }
            const research_id = searchParams.get('research_id');
            // 脚本编辑
            if (research_id) {
              navigator('/script-list?sub_type=3');
            } else {
              // 新建脚本返回上一页
              navigator(-1);
            }
          }}
        >
          {backText ? backText : '返回'}
        </Button>
        <Divider
          layout="vertical"
          x-if={
            actionValidate.includes('export') ||
            actionValidate.includes('create')
          }
        />
        <Button
          x-if={actionValidate.includes('export')}
          theme="default"
          className="btn-background-brand2 border-none"
          size="large"
          onClick={() => exportScripts()}
          icon={<DownloadIcon />}
        >
          下载此脚本
        </Button>
        <div
          className="relative inline-block"
          x-if={researchId && actionValidate.includes('create')}
        >
          <img
            src={VisionGenTips}
            alt="vision-gen-tips"
            className="w-[128px] h-[23px] absolute top-[-15px] left-0 z-[100]"
          />
          <Button
            theme="default"
            className="btn-background-brand2 border-none"
            size="large"
            icon={<PlayCircleIcon />}
            onClick={() =>
              navigator(
                `/script-list?selectResearchId=${
                  researchId || researchId
                }&from=${from}`
              )
            }
            style={{
              color: 'rgba(0, 71, 249, 1)',
            }}
          >
            生成效果视频
          </Button>
        </div>
      </div>
    </Loading>
  );
}
