import React from 'react';
import { Page } from '@/components/Layout';
import { Breadcrumb } from 'tdesign-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ScriptContent } from './ScriptContent';

export default function VisionCreate() {
  const navigator = useNavigate();

  const [queryParams] = useSearchParams();

  const researchId = queryParams.get('research_id');

  return (
    <Page
      className="relative"
      breadCrumb={
        <Breadcrumb separator=">" maxItemWidth="140px">
          <Breadcrumb.BreadcrumbItem onClick={() => navigator('/script-list')}>
            脚本
          </Breadcrumb.BreadcrumbItem>
          <Breadcrumb.BreadcrumbItem>基于视频创建</Breadcrumb.BreadcrumbItem>
        </Breadcrumb>
      }
    >
      <ScriptContent
        researchId={researchId!}
        from={queryParams.get('from') || ''}
        _blank={queryParams.get('_blank') || ''}
        actionValidate="save;back;create;"
      />
    </Page>
  );
}
