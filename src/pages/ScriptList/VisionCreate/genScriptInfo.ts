import { pick, set, isUndefined, isNaN, isNull } from 'lodash-es';
import type { LineItem } from '@/type/pagedoo';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';

import {
  BaseScript,
  ScriptGlobalData,
  ScriptView,
} from '@/components/ScriptForm/type';

function xSet<T>(object: any, path: string, value: T) {
  if (isUndefined(value) || isNaN(value) || isNull(value)) return;

  set(object, path, value);
}

export default function genScriptInfo(
  {
    views,
    globalField,
    globalSpeech,
  }: {
    views: ScriptView[];
    globalField?: ScriptGlobalData;
    globalSpeech?: string;
  },
  preValue?: BaseScript
): BaseScript {
  const outputValue: BaseScript = preValue ?? {
    backgroundImage: [],
    type: ORIGIN_TYPE.GAME_PROMOTION,
    size: [455, 812],
    views: [],
  };

  xSet<ScriptView[]>(outputValue, 'views', views);
  xSet<ScriptGlobalData | undefined>(outputValue, 'globalField', globalField);
  xSet<string>(outputValue, 'globalSpeech', globalSpeech || '');

  console.log('!!!outputValue', outputValue);
  return outputValue;
}
