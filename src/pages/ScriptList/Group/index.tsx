/**
 * <AUTHOR>
 * @date 2024/5/19 下午5:23
 * @desc index
 */

import React, { useEffect } from 'react';
import './index.less';
import { AddIcon, EllipsisIcon } from 'tdesign-icons-react';
import { Button, DialogPlugin, MessagePlugin, Popup } from 'tdesign-react';
import { FormDialog } from '@/pages/ScriptList/Group/FormDialog';
import {
  QA_GROUP_TYPE,
  useGroupRequest,
} from '@/pages/QALib/hooks/useGroupRequest';
import { Development } from '@/pb/pb';

const classPrefix = 'qa-group-comp';

interface IProps {
  searchName?: string;
  selectGroup?: string;
  onSelectGroup?: (groupId: string) => void;
}

export function QAGroup(props: IProps) {
  const { onSelectGroup, selectGroup, searchName } = props;

  const {
    records: groupList,
    refresh,
    queryParams,
    setQueryParams,
  } = useGroupRequest({
    queryParamsDefault: {
      groupType: QA_GROUP_TYPE,
      groupName: searchName,
    },
  });

  useEffect(() => {
    if (!selectGroup && groupList[0]?.group_id) {
      onSelectGroup?.(groupList[0].group_id);
    }
  }, [groupList, onSelectGroup, selectGroup]);

  useEffect(() => {
    // onSelectGroup?.('');
    setQueryParams({
      ...queryParams,
      groupName: searchName,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchName, setQueryParams]);

  const deleteGroupConfirm = (e: React.MouseEvent, groupId: string) => {
    e.stopPropagation();
    const confirmDia = DialogPlugin.confirm({
      header: '确定删除该问题库？',
      body: '删除后将不可恢复，是否确定删除？',
      theme: 'warning',
      confirmBtn: '确定删除',
      cancelBtn: '取消',
      onConfirm: () => {
        Development.DeleteUserGroup({
          group_id: groupId,
          group_type: QA_GROUP_TYPE,
        })
          .then(() => {
            void MessagePlugin.success({ content: '删除成功' });
            confirmDia.hide();
            refresh();
          })
          .catch((e) => {
            console.error(e, '删除失败');
            void MessagePlugin.error({ content: '删除失败, 请稍后重试' });
          })
          .finally(() => {
            refresh();
          });
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  };

  const showEditGroupDialog = (
    e: React.MouseEvent,
    groupId: string,
    groupName: string
  ) => {
    e.stopPropagation();
    const confirmDia = DialogPlugin.confirm({
      header: '编辑问答库',
      body: (
        <FormDialog
          type="edit"
          onClose={() => {
            confirmDia.hide();
          }}
          groupId={groupId}
          initGroupName={groupName}
          refresh={refresh}
        />
      ),
      footer: null,
      onClose: () => {
        confirmDia.hide();
      },
    });
  };

  const showAddGroupDialog = () => {
    const confirmDia = DialogPlugin.confirm({
      header: '新增分组',
      body: (
        <FormDialog
          type="add"
          onClose={() => {
            confirmDia.hide();
          }}
          refresh={refresh}
        />
      ),
      footer: null,
      onClose: () => {
        confirmDia.hide();
      },
    });
  };
  return (
    <div className={classPrefix}>
      <div className={`${classPrefix}-content`}>
        {groupList.length <= 0 ? (
          <div
            className="flex items-center justify-center"
            style={{ height: '100%' }}
          >
            暂无分组信息
          </div>
        ) : (
          <>
            {groupList.map((group) => (
              <div
                key={group.group_id}
                className={`group-item ${
                  selectGroup === group.group_id ? 'group-item--active' : ''
                }`}
                onClick={() => {
                  onSelectGroup?.(group.group_id);
                  // setCurrentGroup(group.group_id);
                }}
              >
                <span className="group-item-title">{group.group_name}</span>
                {selectGroup !== group.group_id && (
                  <Popup
                    trigger="hover"
                    placement="bottom"
                    // destroyOnClose
                    content={
                      <div style={{ width: '124px' }}>
                        <div>
                          <Button
                            style={{
                              width: '124px',
                              justifyContent: 'flex-start',
                            }}
                            theme="default"
                            variant="text"
                            onClick={(e) => {
                              showEditGroupDialog(
                                e,
                                group.group_id,
                                group.group_name
                              );
                            }}
                          >
                            <div style={{ textAlign: 'left' }}>编辑</div>
                          </Button>
                        </div>
                        <div>
                          <Button
                            style={{
                              width: '124px',
                              justifyContent: 'flex-start',
                            }}
                            theme="default"
                            variant="text"
                            onClick={(e) => {
                              deleteGroupConfirm(e, group.group_id);
                            }}
                          >
                            <div style={{ textAlign: 'left' }}>删除</div>
                          </Button>
                        </div>
                      </div>
                    }
                  >
                    <span className="group-item-operate ml-4 flex items-center">
                      <EllipsisIcon />
                    </span>
                  </Popup>
                )}
              </div>
            ))}
            <div style={{ height: '28px' }} />
          </>
        )}
      </div>
      <div
        className={`${classPrefix}-action-zone`}
        onClick={showAddGroupDialog}
      >
        <AddIcon />
        <span className="ml-4">新建分组</span>
      </div>
    </div>
  );
}
