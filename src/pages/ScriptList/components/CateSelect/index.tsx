import React, { useMemo } from 'react';
import classNames from 'classnames';
import { CheckIcon } from 'tdesign-icons-react';
import { Loading } from 'tdesign-react';

export interface CateSelectProps {
  value?: string;
  onChange?: (value: string) => void;
  dataSource: Array<{ title: string; value: string }>;
  loading?: boolean;
  hasAll?: boolean;
}

export default function CateSelect(props: CateSelectProps) {
  const { value = 'all', hasAll = true, dataSource, onChange, loading } = props;

  const realDataSource = useMemo(() => {
    if (!hasAll) return dataSource;

    return [{ title: '全部', value: 'all' }].concat(dataSource);
  }, [dataSource, hasAll]);

  return (
    <Loading loading={loading}>
      <div className="flex flex-row flex-wrap items-center">
        {realDataSource.map((x) => (
          <div
            key={x.value}
            onClick={() => onChange?.(x.value)}
            className={classNames(
              'h-[38px] px-[15px] rounded-[50px] flex flex-row items-center cursor-pointer m-[4px]',
              {
                'btn-background-brand1 font-bold text-[#0047F9]':
                  value === x.value,
                'text-sm btn-background-grey': value !== x.value,
              }
            )}
          >
            <CheckIcon x-if={value === x.value} />
            <span className="pl-[5px]">{x.title}</span>
          </div>
        ))}
      </div>
    </Loading>
  );
}
