import React, { forwardRef, useImperativeHandle } from 'react';
import { Dialog, DialogProps, Button, DialogCloseContext } from 'tdesign-react';
import { useBoolean } from 'ahooks';

export default forwardRef((props: DialogProps, ref) => {
  const [visible, { setTrue, setFalse }] = useBoolean(false);

  useImperativeHandle(ref, () => {
    return {
      open: () => setTrue(),
      close: () => setFalse,
    };
  });

  return (
    <>
      <div onClick={() => setTrue()}>
        <slot name="trigger" />
      </div>
      <Dialog
        {...props}
        onClose={(ctx) => {
          setFalse();
          props.onClose?.(ctx);
        }}
        onCancel={(ctx) => {
          setFalse();
          props.onCancel?.(ctx);
        }}
        onConfirm={(ctx) => {
          setFalse();
          props.onConfirm?.(ctx);
        }}
        onCloseBtnClick={setFalse}
        onEscKeydown={setFalse}
        visible={visible}
        footer={
          <div className="space-x-[10px] mt-[-12px]">
            <Button
              theme="default"
              className="btn-background-light border-none"
              onClick={(e) => {
                setFalse();
                props.onCancel?.({
                  e: e as React.MouseEvent<HTMLButtonElement>,
                });
                props.onClose?.({} as DialogCloseContext);
              }}
            >
              取消
            </Button>
            <Button
              className="btn-background-primary border-none"
              onClick={(e) => {
                setFalse();
                props.onConfirm?.({
                  e: e as React.MouseEvent<HTMLButtonElement>,
                });
              }}
            >
              确认
            </Button>
          </div>
        }
      >
        <div className="absolute left-0 right-0 h-[1px] btn-background-grey" />
        <div className="pt-[16px] min-h-[50px] pb-[16px]">{props.children}</div>
        <div className="absolute left-0 right-0 h-[1px] btn-background-grey" />
      </Dialog>
    </>
  );
});
