import { HelpCircleIcon } from 'tdesign-icons-react';
import { Input, Tooltip } from 'tdesign-react';

import './index.less';
import { ScriptGlobalData } from '@/components/ScriptForm/type';

export function TitleManage(props: {
  titleData: Pick<ScriptGlobalData, 'title' | 'subTitle'>;
  onChange: (data: Pick<ScriptGlobalData, 'title' | 'subTitle'>) => void;
}) {
  const { titleData = { title: '', subTitle: '' }, onChange } = props;
  return (
    <div className="title-group-manage">
      <div className="title-subtip">
        <HelpCircleIcon />
        <span>以下标题为根据内容智能生成，可修改</span>
      </div>
      <div className="title-wrapper-m">
        {/* 大标题的 label + 输入框 */}
        <div className="title-wrapper-m__b">
          <div className="title-wrapper-m__title">大标题</div>
          <Tooltip
            content="应用在智能视频中的标题"
            theme="light"
            placement="right-top"
            overlayInnerStyle={{ width: 200, padding: 20 }}
            overlayStyle={{ top: -4, left: -6 }}
          >
            <HelpCircleIcon style={{ cursor: 'pointer' }} />
          </Tooltip>

          <Input
            value={titleData.title}
            type="text"
            className="title-wrapper-m__input"
            onChange={(v) => {
              onChange({ ...titleData, title: v });
            }}
          />
        </div>
        <div className="title-wrapper-m__b">
          <div className="title-wrapper-m__title">小标题</div>
          <Tooltip
            content="应用在智能视频中的副标题"
            theme="light"
            placement="right-top"
            overlayInnerStyle={{ width: 200, padding: 20 }}
            overlayStyle={{ top: -4, left: -6 }}
          >
            <HelpCircleIcon style={{ cursor: 'pointer' }} />
          </Tooltip>
          <Input
            value={titleData.subTitle}
            type="text"
            className="title-wrapper-m__input"
            onChange={(v) => {
              onChange({ ...titleData, subTitle: v });
            }}
          />
        </div>
      </div>
    </div>
  );
}
