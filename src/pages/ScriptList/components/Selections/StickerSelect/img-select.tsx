import React, { useCallback, useMemo, useState } from 'react';
import { useRequest } from 'ahooks';
import { ResourceSvr } from '@/pb/pb';
import { Divider, Loading, Checkbox } from 'tdesign-react';
import classNames from 'classnames';
import { set, flatten } from 'lodash-es';

import CateSelect from '../../CateSelect';
import { SelectionResourceType } from '../typing';

export type ImgItemProps = {
  resource_id: string;
  image_address: string;
  resource_name: string;
  resource_type?: SelectionResourceType;
  cos_key?: string;
};

export type ImgSelectReturnType = {
  [key: string]: ImgItemProps[];
};

type ImgSelectProps = {
  value?: ImgSelectReturnType;
  onChange: (v: ImgSelectReturnType) => void;
  maxLength?: number;
};

function ImgItem(props: ImgItemProps & { checked?: boolean }) {
  return (
    <div
      className={classNames(
        'rounded-[8px] btn-background-grey overflow-hidden cursor-pointer',
        {
          'border-[1px] border-solid border-[#0153FF]': props.checked,
        }
      )}
    >
      <img
        src={props.image_address}
        className="h-[180px] object-contain"
        alt={props.resource_name}
      />
      <div
        className={classNames('flex flex-row items-center h-[38px] px-[8px]', {
          'bg-[#0153FF] text-[#fff]': props.checked,
          'btn-background-brand1': !props.checked,
        })}
      >
        <Checkbox key={props.resource_id} value={props.resource_id} />
        <div className="flex-1 text-center text-sm">{props.resource_name}</div>
      </div>
    </div>
  );
}

export default function ImgSelect(props: ImgSelectProps) {
  const { value, onChange, maxLength = 1 } = props;

  const [imgType, setImgType] = useState(SelectionResourceType.STATIC_STICKER);

  const currentValue = useMemo(() => {
    return (value?.[imgType] ?? []).map((x) => x.resource_id);
  }, [value, imgType]);

  const calcMaxLength = useMemo(() => {
    // 最大值 maxLength
    // 总消耗掉的值 countLength
    // 当前消耗值 currentValue?.length ?? 0
    // 非当前消耗值 countLength - (currentValue?.length ?? 0)
    // 当前最大值 = 最大值 - (非当前消耗值)
    const countLength = flatten(Object.values(value ?? {})).length;

    return maxLength - (countLength - (currentValue?.length ?? 0));
  }, [maxLength, currentValue, value]);

  const {
    data: cateList,
    runAsync: getCateList,
    loading: cateLoading,
  } = useRequest(ResourceSvr.GetCategoryInfoList, {
    manual: false,
    defaultParams: [
      {
        category_type: SelectionResourceType.STATIC_STICKER,
      },
    ],
  });

  const {
    data: images,
    loading: imagesLoading,
    runAsync: getImages,
    params,
  } = useRequest(ResourceSvr.QueryResourceList, {
    manual: false,
    defaultParams: [
      {
        category_level1: 'all',
        category_level2: '',
        category_type: SelectionResourceType.STATIC_STICKER,
        app_code: 'pagedoo',
        page_num: 1,
        page_size: 10000,
      },
    ],
  });

  const getResources = useCallback(
    (p: {
      category_type?: SelectionResourceType;
      category_level1?: string;
    }) => {
      return getImages(Object.assign({}, params?.[0], p));
    },
    [params, getImages]
  );

  // upload 的图片值会和 select 的值相关影响
  const handleChange = useCallback(
    (v: string[]) => {
      const manualImgList =
        imgType === SelectionResourceType.STATIC_STICKER
          ? (value?.[imgType] ?? []).filter((x) => x.cos_key)
          : [];

      set(
        value as ImgSelectReturnType,
        imgType,
        ((images?.resource_info_list ?? []) as ImgItemProps[])
          .filter((x) => v.includes(x.resource_id))
          .concat(manualImgList)
      );

      onChange?.({ ...value });
    },
    [imgType, images?.resource_info_list, onChange, value]
  );

  return (
    <div>
      <CateSelect
        hasAll={false}
        value={imgType}
        loading={false}
        onChange={(v) => {
          setImgType(v as SelectionResourceType);
          getCateList({ category_type: v });
          getResources({
            category_level1: 'all',
            category_type: v as SelectionResourceType,
          });
        }}
        dataSource={[
          { title: '静态', value: SelectionResourceType.STATIC_STICKER },
          { title: '动态', value: SelectionResourceType.DYNAMIC_STICKER },
        ]}
      />
      <Divider />
      <CateSelect
        loading={cateLoading}
        value={params?.[0]?.category_level1}
        onChange={(p) => getResources({ category_level1: p })}
        dataSource={(cateList?.category_info_list ?? []).map((x) => ({
          title: x.category_level1_name,
          value: x.category_level1,
        }))}
      />
      <Loading loading={imagesLoading}>
        <Checkbox.Group
          className="w-full mt-[24px]"
          value={currentValue}
          onChange={(v) => handleChange(v)}
          max={calcMaxLength}
        >
          <div className="grid grid-cols-5 gap-[16px] w-full">
            {(images?.resource_info_list ?? []).map((i) => {
              return (
                <ImgItem
                  key={i.resource_id}
                  {...i}
                  checked={
                    currentValue.findIndex((x) => x === i.resource_id) > -1
                  }
                />
              );
            })}
          </div>
        </Checkbox.Group>
      </Loading>
    </div>
  );
}
