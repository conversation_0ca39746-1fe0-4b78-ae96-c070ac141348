import React, { useRef, useState, useCallback, useMemo } from 'react';
import { AddIcon, MusicRectangleAddIcon } from 'tdesign-icons-react';
import RadioCircleGroup from '@/components/RadioCircleGroup';
import { Button } from 'tdesign-react';
import { flatten, cloneDeep } from 'lodash-es';
import { useControllableValue } from 'ahooks';

import { CusFieldComponentProps } from '../../LinkTable';
import MaterialsSelect from '../../MaterialsSelect';
import ImgSelect, { ImgSelectReturnType, ImgItemProps } from './img-select';
import ImgUpload from './img-upload';
import { Method } from '../typing';

function StickerDisplay(props: {
  dataSource: ImgItemProps;
  isPreview: boolean;
  onDelete?: (i: ImgItemProps) => void;
  onAdd?: () => void;
  canAdd?: boolean;
}) {
  const { dataSource, isPreview, onAdd, canAdd = true } = props;

  return (
    <div className="flex flex-row items-center flex-wrap">
      <div
        key={dataSource.resource_id}
        className="group relative w-[130px] h-[130px] m-[4px] rounded-[4px] overflow-hidden btn-background-grey"
      >
        <div
          x-if={!isPreview}
          onClick={onAdd}
          className="absolute overflow-hidden flex inset-0 bg-[rgba(0,0,0,.6)] items-center justify-center cursor-pointer"
        >
          <div className="flex items-center gap-[10px]">
            <MusicRectangleAddIcon
              className="cursor-pointer"
              color="#fff"
              size={16}
            />
            <span className="text-[#fff] text-[14px]">更换</span>
          </div>
        </div>
        <img
          className="w-full h-full"
          src={dataSource.image_address}
          alt={dataSource.resource_name}
          title={dataSource.resource_name}
        />
      </div>
      <Button
        x-if={!isPreview && canAdd}
        theme="default"
        className="w-[30px] h-[30px] m-[4px] rounded-[4px] btn-background-brand1 flex flex-row items-center justify-center cursor-pointer"
        onClick={() => onAdd?.()}
      >
        <AddIcon size={16} color="#000" />
      </Button>
    </div>
  );
}

function EditStickerSelect(
  props: CusFieldComponentProps<
    ImgItemProps,
    {
      maxLength?: number;
    }
  >
) {
  const { defaultValue, onChange, componentProps } = props;

  const { maxLength = 1 } = componentProps ?? {};

  const mRef = useRef<any>();

  const [method, setMethod] = useState<Method>(Method.MATERTIAL);

  // 由于层级较深，需要 clone
  const getCloneDefaultValue = useCallback(
    () => cloneDeep([defaultValue]),
    [defaultValue]
  );

  const [selected, setSelected] = useControllableValue<ImgItemProps[]>({
    defaultValue: getCloneDefaultValue(),
    onChange: (v: ImgItemProps[]) => {
      console.log('!!!', v);
      onChange(v[0]);
    },
  });

  const [draftSelected, setDraftSelected] =
    useControllableValue<ImgSelectReturnType>({
      defaultValue: [],
    });

  const confirmSelect = useCallback(() => {
    setSelected(Object.values(draftSelected)[0]);
  }, [draftSelected, setSelected]);

  const handleDelete = useCallback(
    (i: ImgItemProps) => {
      const { resource_type, resource_id } = i;

      if (!resource_type) return;

      const index = selected[resource_type].findIndex(
        (x) => x.resource_id === resource_id
      );

      if (index === -1) return;

      selected[resource_type].splice(index, 1);

      // TODO:需要 draft 和 selected 同步变更，可以优化

      setSelected({ ...selected });
      setDraftSelected(cloneDeep(selected));
    },
    [selected, setSelected, setDraftSelected]
  );

  const countSelectedLength = useMemo(() => {
    return flatten(Object.values(selected ?? {})).length;
  }, [selected]);

  const calcMaxLength = useMemo(() => {
    const countLength = flatten(Object.values(draftSelected ?? {})).length;
    const currentLength = flatten(Object.values(draftSelected ?? {})).filter(
      (x) => (method === Method.UPLOAD ? x.cos_key : !x.cos_key)
    ).length;

    return maxLength - (countLength - currentLength);
  }, [maxLength, method, draftSelected]);

  return (
    <>
      <StickerDisplay
        canAdd={countSelectedLength < maxLength}
        dataSource={selected[0]}
        isPreview={false}
        onAdd={() => {
          mRef.current?.open();
        }}
        onDelete={(i) => handleDelete(i)}
      />
      <MaterialsSelect
        ref={mRef}
        header="添加画图贴纸"
        width={835}
        onConfirm={() => confirmSelect()}
        onClose={() => {
          setDraftSelected(cloneDeep(selected));
        }}
      >
        <RadioCircleGroup
          value={method}
          onChange={(v) => setMethod(v as Method)}
          className="w-full h-[36px] mb-[24px]"
          dataSource={[
            { label: '素材库', value: 'material' },
            { label: '本地上传', value: 'upload' },
          ]}
        />
        <ImgSelect
          x-if={method === Method.MATERTIAL}
          value={draftSelected}
          maxLength={calcMaxLength}
          onChange={setDraftSelected}
        />
        <ImgUpload
          x-if={method === Method.UPLOAD}
          value={draftSelected}
          maxLength={calcMaxLength}
          onChange={setDraftSelected}
        />
      </MaterialsSelect>
    </>
  );
}

export function StickerSelect(props: CusFieldComponentProps<ImgItemProps>) {
  const { defaultValue, isPreview } = props;

  if (isPreview) {
    return <StickerDisplay dataSource={defaultValue} isPreview={isPreview} />;
  }

  return <EditStickerSelect {...props} />;
}
