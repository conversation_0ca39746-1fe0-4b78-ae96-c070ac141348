import React, { useCallback, useState } from 'react';
import { InfoCircleIcon } from 'tdesign-icons-react';
import { UploadFile } from 'tdesign-react';
import { UploadCos } from '@/components/Upload';
import { uploadRequest } from '@/utils/cos';
import { uuid } from '@tencent/midas-util';

import { ImgSelectReturnType, ImgItemProps } from './img-select';
import { SelectionResourceType } from '../typing';

type ImgUploadProps = {
  value: ImgSelectReturnType;
  onChange: (v: ImgSelectReturnType) => void;
  maxLength?: number;
};

export default function ImgUpload(props: ImgUploadProps) {
  const { value, onChange, maxLength = 1 } = props;

  const [fileList, setFileList] = useState<UploadFile[]>(
    (value?.patch ?? [])
      .filter((x) => x.cos_key)
      .map((x) => ({
        name: x.resource_name,
        url: x.image_address,
        status: 'success',
      }))
  );

  const handleChange = useCallback(
    (files: UploadFile[]) => {
      setFileList(files);

      const formatList = files
        .filter((x) => x.status === 'success')
        .map((x) => {
          return {
            cos_key: x.response?.cosKey,
            resource_name: x.name,
            resource_id: uuid(),
            image_address: x.url,
            type: SelectionResourceType.STATIC_STICKER,
          };
        }) as ImgItemProps[];

      value.patch = [...(value?.patch ?? []), ...formatList];

      onChange({ ...value });
    },
    [value, onChange]
  );

  return (
    <div>
      <div className="text-sm text-[rgba(0,0,0,.4)]">
        <InfoCircleIcon />
        <span className="px-[5px]">请上传 PNG 格式的图片，效果会更佳</span>
      </div>
      <div className="mt-[15px]">
        <UploadCos
          disabled={maxLength === 0}
          max={maxLength}
          type="image"
          files={fileList}
          uploadRequest={uploadRequest}
          showUploadProgress
          theme="image-flow"
          accept=".jpg,.jpeg,.png"
          onChange={handleChange}
          multiple
        />
      </div>
    </div>
  );
}
