import React, { useCallback } from 'react';
import { PlayCircleIcon, PauseCircleIcon } from 'tdesign-icons-react';
import { Checkbox, Loading } from 'tdesign-react';
import useAudioDuration from '@/hooks/useAudioDuration';
import { ResourceSvr } from '@/pb/pb';
import { useRequest } from 'ahooks';

import AudioPlayer from './audio-player';
import { SelectionResourceType } from '../typing';
import CateSelect from '../../CateSelect';

export type AudioItemProps = {
  resource_id: string;
  image_address: string;
  resource_name: string;
  audio_address: string;
  duration?: number;
};

function AudioItem(props: AudioItemProps) {
  const { resource_id, resource_name, duration, audio_address } = props;

  return (
    <div className="p-[20px] btn-background-light rounded-[4px]">
      <div className="flex flex-row">
        <div className="w-[48px] h-[48px] rounded-[4px] btn-background-primary text-center place-content-center">
          <AudioPlayer src={audio_address} title={resource_name}>
            <PlayCircleIcon
              x-slot:play
              className="cursor-pointer"
              color="#fff"
              size={24}
            />
            <PauseCircleIcon
              x-slot:pause
              className="cursor-pointer"
              color="#fff"
              size={24}
            />
          </AudioPlayer>
        </div>
        <div className="flex-1 pl-[16px]">
          <div className="flex flex-row items-center justify-between">
            <div className="font-bold text-sm">{resource_name}</div>
            <Checkbox key={resource_id} value={resource_id} />
          </div>
          <div className="text-xs text-[#ccc]">{duration}</div>
        </div>
      </div>
    </div>
  );
}

type AudioSelectProps = {
  value: AudioItemProps[];
  onChange: (value: AudioItemProps[]) => void;
  maxLength?: number;
};

export function AudioSelect(props: AudioSelectProps) {
  const { value, onChange, maxLength = 1 } = props;

  const getAudioDuration = useAudioDuration();

  const {
    data: effects,
    loading: effectsLoading,
    runAsync: getEffects,
    params,
  } = useRequest(
    (p) => {
      return ResourceSvr.QueryResourceList(p).then((r) => {
        const list = r.resource_info_list;

        return getAudioDuration(list.map((x) => x.audio_address)).then(
          (durationList) => {
            return list.map((x, i) =>
              Object.assign(x, { duration: durationList[i] })
            );
          }
        );
      });
    },
    {
      manual: false,
      cacheKey: 'Script-SoundEffectSelect-getEffects',
      cacheTime: 1000 * 60,
      defaultParams: [
        {
          category_level1: 'all',
          category_level2: '',
          category_type: SelectionResourceType.SOUND_EFFECT,
          app_code: 'pagedoo',
          page_num: 1,
          page_size: 10000,
        },
      ],
    }
  );

  const { data: cateList, loading: cateLoading } = useRequest(
    () =>
      ResourceSvr.GetCategoryInfoList({
        category_type: SelectionResourceType.SOUND_EFFECT,
      }),
    {
      manual: false,
      cacheKey: 'Script-SoundEffectSelect-getCateList',
      cacheTime: 1000 * 60,
    }
  );

  const switchCate = useCallback(
    (value: string) => {
      getEffects(
        Object.assign({}, params?.[0], {
          category_level1: value,
        }) as Parameters<typeof getEffects>[0]
      );
    },
    [params, getEffects]
  );

  return (
    <>
      <CateSelect
        loading={cateLoading}
        value={params?.[0]?.category_level1}
        onChange={switchCate}
        dataSource={(cateList?.category_info_list ?? []).map((x) => ({
          title: x.category_level1_name,
          value: x.category_level1,
        }))}
      />
      <Loading loading={effectsLoading}>
        <Checkbox.Group
          max={maxLength}
          className="w-full mt-[24px]"
          value={value.map((x) => x.resource_id)}
          onChange={(v) => {
            const selectItems = v
              .map((x) => (effects ?? []).find((y) => y.resource_id === x))
              .filter((x) => !!x);

            onChange(selectItems);
          }}
        >
          <div className="grid grid-cols-3 gap-[16px] w-full">
            {(effects ?? []).map((i) => {
              return <AudioItem key={i.resource_id} {...i} />;
            })}
          </div>
        </Checkbox.Group>
      </Loading>
    </>
  );
}
