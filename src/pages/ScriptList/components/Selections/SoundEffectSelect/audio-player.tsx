import React, { useCallback, useRef } from 'react';
import { useBoolean, useHover } from 'ahooks';
import { StyledProps } from 'tdesign-react/es/common';

export default function AudioPlayer(
  props: {
    src: string;
    children?: React.ReactNode;
    title?: string;
    isPreview?: boolean;
  } & StyledProps
) {
  const ref = useRef<HTMLAudioElement>(null);

  const [playing, { toggle, setTrue, setFalse }] = useBoolean(false);

  const toggleAudio = useCallback(() => {
    if (!props.isPreview) return;
    if (playing) {
      ref.current?.pause();
    } else {
      ref.current?.play();
    }

    toggle();
  }, [playing, props.isPreview, toggle]);

  return (
    <>
      <audio
        ref={ref}
        src={props.src}
        controls
        className="hidden"
        onPlay={() => setTrue()}
        onPause={() => setFalse()}
        onEnded={() => setFalse()}
      />
      <div
        className={`${props.className}`}
        title={props.title}
        onClick={() => toggleAudio()}
      >
        <slot x-if={!playing} name="play" />
        <slot x-else name="pause" />
      </div>
    </>
  );
}
