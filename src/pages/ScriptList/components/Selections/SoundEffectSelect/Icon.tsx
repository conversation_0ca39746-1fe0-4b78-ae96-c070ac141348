export function PauseIcon(props: any) {
  return (
    <>
      <div {...props}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="49"
          height="49"
          viewBox="0 0 49 49"
          fill="none"
        >
          <path
            d="M49 24.5C49 10.969 38.031 0 24.5 0C10.969 -7.49649e-06 0 10.969 0 24.5C-7.49649e-06 38.031 10.969 49 24.5 49C38.031 49 49 38.031 49 24.5ZM34.9454 25.2977L19.2863 34.3385C18.6722 34.693 17.9046 34.2499 17.9046 33.5408L17.9046 15.4592C17.9046 14.7501 18.6722 14.3069 19.2863 14.6615L34.9454 23.7022C35.5595 24.0568 35.5595 24.9431 34.9454 25.2977Z"
            fill="url(#paint0_linear_11266_170303)"
          />
          <path
            d="M49 24.5C49 10.969 38.031 0 24.5 0C10.969 -7.49649e-06 0 10.969 0 24.5C-7.49649e-06 38.031 10.969 49 24.5 49C38.031 49 49 38.031 49 24.5ZM34.9454 25.2977L19.2863 34.3385C18.6722 34.693 17.9046 34.2499 17.9046 33.5408L17.9046 15.4592C17.9046 14.7501 18.6722 14.3069 19.2863 14.6615L34.9454 23.7022C35.5595 24.0568 35.5595 24.9431 34.9454 25.2977Z"
            fill="white"
            fillOpacity="1"
          />
          <defs>
            <linearGradient
              id="paint0_linear_11266_170303"
              x1="-0.00894527"
              y1="49"
              x2="54.4621"
              y2="41.885"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#F6F7FB" />
              <stop offset="1" stopColor="#FBF8FB" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </>
  );
}

export function PlayIcon({ children, ...rest }: any) {
  return (
    <div {...rest}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="54"
        height="54"
        viewBox="0 0 54 54"
        fill="none"
      >
        <path
          d="M18 23.0227V44.6106C17.9992 48.3378 14.9774 51.3591 11.25 51.3591C7.52208 51.3591 4.5 48.337 4.5 44.6091C4.5 40.8812 7.52208 37.8591 11.25 37.8591C12.4795 37.8591 13.6322 38.1878 14.625 38.7621V12.1963C14.625 9.6144 16.568 7.44615 19.1345 7.16412L43.097 4.53088C46.0938 4.20156 48.7125 6.54823 48.7125 9.56309V41.2344C48.7125 41.2721 48.7113 41.3096 48.7088 41.3467C48.7113 41.4214 48.7125 41.4963 48.7125 41.5716C48.7125 45.2995 45.6904 48.3216 41.9625 48.3216C38.2346 48.3216 35.2125 45.2995 35.2125 41.5716C35.2125 37.8437 38.2346 34.8216 41.9625 34.8216C43.192 34.8216 44.3447 35.1503 45.3375 35.7246V20.0186L18 23.0227ZM43.4657 7.88569L19.5032 10.5189C18.6477 10.6129 18 11.3357 18 12.1963V19.6274L45.3375 16.6233V9.56309C45.3375 8.55813 44.4646 7.77591 43.4657 7.88569ZM14.625 44.6091C14.625 42.7451 13.114 41.2341 11.25 41.2341C9.38604 41.2341 7.875 42.7451 7.875 44.6091C7.875 46.4731 9.38604 47.9841 11.25 47.9841C13.114 47.9841 14.625 46.4731 14.625 44.6091ZM41.9625 38.1966C40.0985 38.1966 38.5875 39.7076 38.5875 41.5716C38.5875 43.4356 40.0985 44.9466 41.9625 44.9466C43.8265 44.9466 45.3375 43.4356 45.3375 41.5716C45.3375 39.7076 43.8265 38.1966 41.9625 38.1966Z"
          fill="white"
        />
      </svg>
      {children}
    </div>
  );
}
