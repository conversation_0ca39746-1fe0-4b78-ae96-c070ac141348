import React, {
  useCallback,
  useState,
  useRef,
  Fragment,
  useEffect,
  useLayoutEffect,
} from 'react';
import { MusicRectangleAddIcon } from 'tdesign-icons-react';
import { Popup } from 'tdesign-react';
import RadioCircleGroup from '@/components/RadioCircleGroup';
import classNames from 'classnames';
import { useControllableValue, useHover, useUpdate } from 'ahooks';
import useAudioDuration from '@/hooks/useAudioDuration';

import { CusFieldComponentProps } from '../../LinkTable';
import MaterialsSelect from '../../MaterialsSelect';
import { AudioItemProps, AudioSelect } from './audio-select';
import AudioPlayer from './audio-player';
import { Method } from '../typing';
import { PauseIcon, PlayIcon } from './Icon';

function AudioDisplay(props: {
  dataSource: AudioItemProps;
  onDelete?: (i: AudioItemProps) => void;
  onAdd?: () => void;
  isPreview?: boolean;
  canAdd?: boolean;
}) {
  const { dataSource, isPreview, onAdd } = props;
  if (!dataSource && isPreview) return <></>;

  return (
    <div className="flex flex-row flex-wrap items-center">
      <div
        key={dataSource.resource_id}
        className={classNames(
          'group relative rounded-[8px] m-[4px] w-[130px] h-[130px] overflow-hidden items-center justify-center flex',
          {
            'btn-background-primary': isPreview,
            'bg-[rgba(0,0,0,.8)]': !isPreview,
          }
        )}
      >
        <Popup
          showArrow
          placement="right"
          trigger="hover"
          content={
            <div className="text-[12px] p-[5px]">
              <div>
                <span className="text-slate-500	">名称:</span>{' '}
                <b>{dataSource.resource_name}</b>
              </div>
              <div>
                <span className="text-slate-500	">时长:</span>{' '}
                <b>{dataSource.duration || '-'}</b>
              </div>
            </div>
          }
        >
          <Fragment x-if={isPreview}>
            <AudioPlayer
              src={dataSource.audio_address}
              title={dataSource.resource_name}
              isPreview
            >
              <PauseIcon
                x-slot:pause
                className="cursor-pointer text-[#fff] w-full h-full bg-black absolute inset-0 flex items-center justify-center"
              />
              <PlayIcon x-slot:play className="cursor-pointer text-[#fff]">
                <PauseIcon className="cursor-pointer text-[#fff] w-full h-full absolute items-center justify-center hidden group-hover:flex inset-0 bg-[rgba(0,0,0,.8)]" />
              </PlayIcon>
            </AudioPlayer>
          </Fragment>
          <Fragment x-else>
            <div
              className="flex flex-row items-center justify-around px-[4px] h-full cursor-pointer"
              onClick={(e) => {
                onAdd?.();
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <AudioPlayer
                src={dataSource.audio_address}
                title={dataSource.resource_name}
                isPreview={false}
              >
                <div className="flex items-center gap-[10px]" x-slot:play>
                  <MusicRectangleAddIcon
                    className="cursor-pointer"
                    color="#fff"
                    size={16}
                  />
                  <span className="text-[#fff] text-[14px]">更换</span>
                </div>
              </AudioPlayer>
            </div>
          </Fragment>
        </Popup>
      </div>
    </div>
  );
}

function EditSoundEffectSelect(
  props: CusFieldComponentProps<
    AudioItemProps,
    {
      maxLength?: number;
    }
  >
) {
  const { defaultValue, onChange, componentProps } = props;

  const { maxLength = 1 } = componentProps ?? {};

  const MaterialsSelectRef = useRef<any>(null);

  const [method, setMethod] = useState<Method>(Method.MATERTIAL);

  const [selectedAudio, setSelectedAudio] = useControllableValue<
    AudioItemProps[]
  >({
    defaultValue: [defaultValue],
    onChange: (v: AudioItemProps[]) => {
      onChange(v[0]);
    },
  });

  const [draftSelected, setDraftSelected] = useControllableValue<
    AudioItemProps[]
  >({ defaultValue: [] });

  const confirmSelect = useCallback(() => {
    setSelectedAudio(draftSelected);
  }, [draftSelected, setSelectedAudio]);

  return (
    <>
      <AudioDisplay
        dataSource={selectedAudio[0]}
        onAdd={() => MaterialsSelectRef.current?.open()}
      />
      <MaterialsSelect
        width={755}
        header="添加画面音效"
        ref={MaterialsSelectRef}
        onConfirm={() => confirmSelect()}
        onClose={() => setDraftSelected([...selectedAudio])}
      >
        <RadioCircleGroup
          value={method}
          onChange={(v) => setMethod(v as Method)}
          className="w-full h-[36px] mb-[24px]"
          dataSource={[
            { label: '素材库', value: 'material' },
            { label: '本地上传', value: 'upload', disabled: true },
          ]}
        />
        <AudioSelect
          maxLength={maxLength}
          value={draftSelected ?? []}
          onChange={(v) => {
            setDraftSelected(v);
          }}
        />
      </MaterialsSelect>
    </>
  );
}

export function SoundEffectSelect(
  props: CusFieldComponentProps<AudioItemProps>
) {
  const { isPreview, defaultValue, onChange } = props;
  // 强制更新
  const forceUpdate = useUpdate();
  const getAudioDuration = useAudioDuration();

  // 强制更新 duration
  useEffect(() => {
    if (defaultValue?.duration || !onChange) return;

    getAudioDuration(defaultValue.audio_address).then((durationList) => {
      const resourceList = Object.assign(defaultValue, {
        duration: durationList,
      });

      onChange(resourceList, 1);
      forceUpdate();
    });
  }, [defaultValue, onChange, getAudioDuration, forceUpdate]);

  if (isPreview) {
    return <AudioDisplay dataSource={defaultValue} isPreview />;
  }

  return <EditSoundEffectSelect {...props} />;
}
