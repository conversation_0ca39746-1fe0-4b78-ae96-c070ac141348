import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import {
  InputNumber,
  MessagePlugin,
  Textarea,
  TimeRangePicker,
} from 'tdesign-react';
import dayjs from 'dayjs';
import dayjsDuration from 'dayjs/plugin/duration';
import { range } from 'lodash-es';

import { CusFieldComponentProps } from '../LinkTable';
import { SelectionResourceType } from './typing';
import { StickerSelect } from './StickerSelect';
import { FlowerText } from './FlowerText';
import { SoundEffectSelect } from './SoundEffectSelect';

dayjs.extend(dayjsDuration);

export function TextArea(props: CusFieldComponentProps<string>) {
  const { defaultValue, onChange, isPreview } = props;

  if (isPreview) return defaultValue ?? '-';

  return (
    <Textarea
      autosize
      allowInputOverMax
      placeholder="请输入内容"
      defaultValue={defaultValue}
      onChange={(v) => onChange(v)}
    />
  );
}

export const MinuteSelectCtx = createContext<string[]>([]);

/**
 * 获取限制区间
 * @param idx
 * @returns
 */
export function useMinuteSelectLimit(idx: number) {
  const minuteList = useContext(MinuteSelectCtx);

  const preInterval = minuteList[idx - 1];

  const nextInterval = minuteList[idx + 1];

  const [, preEnd = null] = preInterval?.split('-') ?? [];
  const [nextStart = null] = nextInterval?.split('-') ?? [];

  return [preEnd, nextStart];
}

function toDuration(v: string) {
  if (!v) return null;

  const [minutes, seconds] = v.split(':').map((v) => parseInt(v, 10));

  return dayjs.duration({
    minutes,
    seconds,
  });
}

export function MinuteSelect(props: CusFieldComponentProps<string>) {
  const { defaultValue, onChange, isPreview, cell } = props;

  const [min = '00:00', max = '00:00'] = useMinuteSelectLimit(cell.rowIndex);

  const minDayjs = min ? toDuration(min) : null;
  const maxDayjs = max ? toDuration(max) : null;

  const getInitValue = useCallback(() => {
    const [start = min || '00:00', end = max || '00:00'] =
      defaultValue?.split('-') ?? [];

    return [start, end];
  }, [min, max, defaultValue]);

  const [controledValue, setControlledValue] = useState(getInitValue());

  useEffect(() => {
    setControlledValue(getInitValue());
  }, [isPreview, getInitValue]);

  if (isPreview) return defaultValue;

  return (
    <TimeRangePicker
      className="w-[180px]"
      value={controledValue}
      allowInput
      format="mm:ss"
      onChange={(v) => {
        const [s, e] = v.map((x) => toDuration(x));

        if (
          s?.asMilliseconds &&
          e?.asMilliseconds &&
          s?.asMilliseconds() > e?.asMilliseconds()
        ) {
          return MessagePlugin.warning('结束时间不能早于开始时间');
        }

        setControlledValue(v);
        onChange(v.join('-'));
      }}
      disableTime={(h, m, s, ms) => {
        const minD = minDayjs as any;
        const maxD = maxDayjs as any;

        const disableMinutes = range(0, minD?.minutes() ?? 0).concat(
          maxD?.minutes() ? maxD?.minutes() + 1 : 60,
          60
        );

        if (!disableMinutes.includes(m)) {
          if (m === (minD?.minutes() ?? 0)) {
            return {
              minute: disableMinutes,
              second: range(0, minD?.seconds() ?? 0),
            };
          }

          if (m === (maxD?.minutes() ?? 0)) {
            return {
              minute: disableMinutes,
              second: range(maxD?.seconds() ?? 60, 60),
            };
          }

          return {
            minute: disableMinutes,
          };
        }

        return {
          minute: range(0, 60),
          second: range(0, 60),
        };
      }}
    />
  );
}

/**
 * 普通文本
 * @param props
 * @returns
 */
export function Text(props: CusFieldComponentProps<string>) {
  const { defaultValue } = props;

  return <span>{defaultValue}</span>;
}

export function ResourceContent(props: CusFieldComponentProps<any>) {
  const { cell } = props;
  const currentResourceType = cell.row.resourceKey;

  if (currentResourceType === SelectionResourceType.SOUND_EFFECT) {
    return <SoundEffectSelect {...props} />;
  }
  if (
    currentResourceType === SelectionResourceType.DYNAMIC_STICKER ||
    currentResourceType === SelectionResourceType.STATIC_STICKER
  ) {
    return <StickerSelect {...props} />;
  }
  if (currentResourceType === SelectionResourceType.FLOWER_TEXT) {
    return <FlowerText {...props} />;
  }
  return <div />;
}

export function ShowTimeSet(props: CusFieldComponentProps<string>) {
  const { defaultValue, isPreview, onChange } = props;
  const caculateValue = defaultValue;
  if (isPreview) {
    return <div>第{caculateValue}秒</div>;
  }

  return (
    <InputNumber
      size="medium"
      theme="row"
      value={caculateValue}
      onChange={(v) => {
        console.log(v);
        onChange(v);
      }}
    />
  );
}
