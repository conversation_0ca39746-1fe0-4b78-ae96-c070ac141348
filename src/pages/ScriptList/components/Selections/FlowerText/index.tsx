import { MaterialView } from '@/components/ScriptForm/type';
import { CusFieldComponentProps } from '../../LinkTable';
import { Textarea } from 'tdesign-react';

function FlowerTypeShow() {
  return (
    <div className="relative w-[130px] h-[130px] shrink-0 bg-black flex justify-center items-center rounded-8 overflow-hidden">
      <img
        src="https://pagedoo.pay.qq.com/material/@platform/bed4e988b2431a52f941cc4b9af22926.png"
        alt=""
      />
      <div className="absolute bottom-0 bg-[#444] text-[#fff] text-[12px] w-full text-center font-[400]">
        仅供参考，非实际效果
      </div>
    </div>
  );
}

export function FlowerText(props: CusFieldComponentProps<MaterialView>) {
  const { defaultValue, onChange, isPreview } = props;

  if (isPreview) {
    return (
      <div className="flex flex-row gap-[20px] items-center">
        <FlowerTypeShow />
        <div>{defaultValue?.flowerText}</div>
      </div>
    );
  }

  return (
    <div className="flex flex-row gap-[20px]">
      <FlowerTypeShow />
      <Textarea
        defaultValue={defaultValue?.flowerText}
        autosize={{ minRows: 5 }}
        onChange={(value) => {
          onChange({ ...defaultValue, flowerText: value });
        }}
        allowInputOverMax
      />
    </div>
  );
}
