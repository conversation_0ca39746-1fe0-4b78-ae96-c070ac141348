.select-script-model {
    .operate-box{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
        padding: 20px 20px 0;
        .text{
            color: rgba(0, 0, 0, 0.9);
            font-weight: 600;
        }
    }

    .footer{
        display: flex;
        justify-content: flex-end;
        margin-bottom: -16px;
        padding: 0 20px;
    }

    .t-table__header {
      tr > th {
        background-color: #f8f6fb;
        &::after{
          content: '|';
          position: absolute;
          color: #0000000F;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      tr > th:last-child,tr > th:first-child {
        &::after{
          display: none;
        }
      }
    }
    .t-table__content {
      border-radius: 4px;
      border: 1px solid #0000000F;
    }
    .t-table__cell-check{
      height: 100% !important;
    }
  }
