import { createContext, useState } from 'react';

export const LinkTableContext = createContext<{
  enableEditRow: number;
  dispatch?: {
    setEdit: (r: number) => void;
    closeEdit: () => void;
    deleteItem: (r: number) => void;
    addItem: (r?: number) => void;
    changeItem: (r: number, v: any) => void;
  };
}>({
  enableEditRow: -1,
});

export function useLinkTable(): [
  { enableEditRow: number },
  { setEdit: (r: number) => void; closeEdit: () => void }
] {
  const [enableEditRow, setEnableEditRow] = useState(-1);

  return [
    { enableEditRow },
    {
      setEdit: (rowIndex: number) => setEnableEditRow(rowIndex),
      closeEdit: () => setEnableEditRow(-1),
    },
  ];
}
