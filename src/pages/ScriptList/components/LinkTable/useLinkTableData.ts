import { useCallback } from 'react';
import { useControllableValue } from 'ahooks';

export default function useLinkTableData(
  receivedData: Array<any>,
  setter?: (data: any) => void
) {
  const [dataSource, setDataSource] = useControllableValue({
    value: receivedData,
    onChange: setter,
  });

  const addItem = useCallback(
    (rowIndex?: number) => {
      if (typeof rowIndex === 'undefined') {
        dataSource.push({});
        return setDataSource([...dataSource]);
      }

      if (rowIndex < 0 || rowIndex > dataSource.length) return;

      dataSource.splice(rowIndex, 0, {});

      return setDataSource([...dataSource]);
    },
    [dataSource, setDataSource]
  );

  const deleteItem = useCallback(
    (rowIndex: number) => {
      if (rowIndex < 0 || rowIndex > dataSource.length) return;

      dataSource.splice(rowIndex, 1);

      return setDataSource([...dataSource]);
    },
    [dataSource, setDataSource]
  );

  const changeItem = useCallback(
    (rowIndex: number, value: any) => {
      if (rowIndex < 0 || rowIndex > dataSource.length) return;

      dataSource[rowIndex] =
        typeof value === 'function' ? value(dataSource[rowIndex]) : value;

      return setDataSource([...dataSource]);
    },
    [dataSource, setDataSource]
  );

  return {
    dataSource,
    dispatch: {
      addItem,
      deleteItem,
      changeItem,
    },
  };
}
