import React, { useRef } from 'react';
import { Button, BaseTableCellParams, Popconfirm } from 'tdesign-react';
import { CheckIcon, CloseIcon } from 'tdesign-icons-react';
import { useKeyPress } from 'ahooks';

import useLinkTableData from './useLinkTableData';
import { useLinkTable, LinkTableContext } from './context';

export type LinkTableDispatch = ReturnType<typeof useLinkTable>[1] &
  ReturnType<typeof useLinkTableData>['dispatch'];

export interface LinkTableActionProps<T> {
  cell: BaseTableCellParams<T>;
  dispatch: LinkTableDispatch;
}

export function LinkTableEditAction<T>(props: LinkTableActionProps<T>) {
  return (
    <Button
      variant="text"
      className="text-[#0052d9]"
      onClick={() => props.dispatch?.setEdit(props.cell.rowIndex)}
    >
      编辑
    </Button>
  );
}

export function LinkTableDelAction<T>(props: LinkTableActionProps<T>) {
  return (
    <Popconfirm
      content="确认删除么"
      showArrow
      onConfirm={() => props.dispatch?.deleteItem(props.cell.rowIndex)}
    >
      <Button className="text-[red]" variant="text">
        删除
      </Button>
    </Popconfirm>
  );
}

export function LinkTableInsertAction<T>(props: LinkTableActionProps<T>) {
  return (
    <Button
      variant="text"
      className="text-[#0052d9]"
      onClick={() => props.dispatch?.addItem(props.cell.rowIndex)}
    >
      插入
    </Button>
  );
}

export default function LinkTableActions(props: BaseTableCellParams<any>) {
  const okRef = useRef<HTMLElement>(null);
  const noRef = useRef<HTMLElement>(null);

  useKeyPress(['enter'], () => {
    okRef?.current?.click();
  });

  useKeyPress(['esc'], () => {
    noRef?.current?.click();
  });

  return (
    <LinkTableContext.Consumer>
      {(ctx) => {
        return (
          <>
            <div
              x-if={props.rowIndex !== ctx.enableEditRow}
              className="grid grid-cols-2"
            >
              <LinkTableEditAction
                dispatch={ctx.dispatch as LinkTableDispatch}
                cell={props}
              />
              {/* <LinkTableInsertAction
                dispatch={ctx.dispatch as LinkTableDispatch}
                cell={props}
              /> */}
              <LinkTableDelAction
                dispatch={ctx.dispatch as LinkTableDispatch}
                cell={props}
              />
            </div>
            <div x-else className="space-x-2 ml-[10px]">
              <Button
                ref={okRef}
                className="btn-background-primary"
                size="small"
                icon={<CheckIcon />}
                onClick={() => {
                  // 草稿内容合并 [!!! resources 合并处理]
                  ctx.dispatch?.changeItem(props.rowIndex, (p: any) => {
                    const resourcesMerge = Object.assign(
                      p.resources ?? {},
                      props.row._draft?.resources
                    );

                    const merge = Object.assign(p, props.row._draft, {
                      resources: resourcesMerge,
                    });

                    merge._draft = undefined;

                    return merge;
                  });
                  ctx.dispatch?.closeEdit();
                }}
              />
              <Button
                ref={noRef}
                theme="default"
                className="btn-background-light"
                size="small"
                icon={<CloseIcon />}
                onClick={() => {
                  // 清除草稿
                  props.row._draft = undefined;
                  ctx.dispatch?.closeEdit();
                }}
              />
            </div>
          </>
        );
      }}
    </LinkTableContext.Consumer>
  );
}
