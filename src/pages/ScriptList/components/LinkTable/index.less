.script-link-table {
  border: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
  border-bottom: none;
  overflow: hidden;

  .t-table__header {
    tr > th {
      background-color: #f8f6fb;
    }
  }
  .t-table__body {
    tr > td {
      vertical-align: middle;
    }
  }
}

.script-link-table-footer {
  border-radius: 0 0 8px 8px;
  border: 1px solid #e8e8e8;
  border-top: none;
  padding: 20px 15px 15px;
  margin-top: -5px;
}
