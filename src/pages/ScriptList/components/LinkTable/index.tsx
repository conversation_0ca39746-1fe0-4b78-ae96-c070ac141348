import React, { useMemo } from 'react';
import {
  PrimaryTable,
  PrimaryTableProps,
  Button,
  BaseTableCellParams,
  PrimaryTableCol,
} from 'tdesign-react';
import { get, cloneDeep } from 'lodash-es';
import { FormPath } from '@formily/core';

import { LinkTableContext, useLinkTable } from './context';
import useLinkTableData from './useLinkTableData';
import LinkTableActions from './actions';

import './index.less';

export type CusFieldComponentProps<T, S = any> = {
  isPreview: boolean;
  defaultValue: T;
  onChange: (v: T, force?: number) => void;
  cell: BaseTableCellParams<any>;
  componentProps?: S;
};

export type CusFieldComponent<T> = (
  props: CusFieldComponentProps<T>
) => React.ReactNode;

type CusField = Omit<PrimaryTableCol, 'colKey'> & {
  key: string;
  component: CusFieldComponent<any>;
};

export interface LinkTableProps<T> {
  // 需要选取的字段
  fields: Array<CusField | string>;

  // 初始数据
  initialDataSource: Array<T>;

  onChange?: (dataSource: Array<T>) => void;
}

export default function LinkTable<T>(
  props: LinkTableProps<T> &
    Omit<PrimaryTableProps, 'data' | 'columns' | 'rowKey'>
) {
  const { fields, initialDataSource, onChange, ...reset } = props;

  const [tableCtx, tableDispatch] = useLinkTable();

  const { dataSource, dispatch: dataDispatch } = useLinkTableData(
    initialDataSource,
    onChange
  );

  const columns = useMemo<PrimaryTableCol[]>(() => {
    const prefix = [
      {
        title: '序号',
        colKey: 'index',
        width: 80,
        align: 'center',
        cell: (r: BaseTableCellParams<T>) => {
          return <span className="text-center">{r.rowIndex + 1}</span>;
        },
      },
    ] as PrimaryTableCol[];

    const fieldsColumns = fields.map((f) => {
      if (typeof f === 'string') {
        return {
          title: f,
          colKey: f,
          cell: (r: BaseTableCellParams<T>) => get(r.row, f),
        };
      }

      const { key, component, title, ...reset } = f;

      return {
        ...reset,
        title: title || key,
        colKey: key,
        cell: (params: BaseTableCellParams<T>) => {
          return (
            <LinkTableContext.Consumer>
              {(ctx) => {
                return React.createElement(component, {
                  cell: params,
                  isPreview: ctx.enableEditRow !== params.rowIndex,
                  defaultValue: cloneDeep(FormPath.getIn(params.row, key)),
                  onChange: (v: any, force = 0) => {
                    if (force === 1) {
                      FormPath.setIn(params.row as any, key, v);
                      return;
                    }

                    (params.row as any)._draft ??= {};
                    FormPath.setIn((params.row as any)._draft, key, v);
                  },
                });
              }}
            </LinkTableContext.Consumer>
          );
        },
      };
    }) as PrimaryTableCol[];

    const suffix = [
      {
        title: '操作',
        width: 120,
        colKey: 'operate',
        align: 'center',
        cell: (params: BaseTableCellParams<T>) => {
          return <LinkTableActions {...params} />;
        },
      },
    ] as PrimaryTableCol[];

    return ([] as PrimaryTableCol[]).concat(fieldsColumns, suffix);
  }, [fields]);

  return (
    <LinkTableContext.Provider
      value={Object.assign(tableCtx, {
        dispatch: Object.assign(tableDispatch, dataDispatch),
      })}
    >
      <div>
        <PrimaryTable
          {...reset}
          className="script-link-table"
          rowKey="index"
          data={dataSource}
          columns={columns}
        />
        <div className="script-link-table-footer" x-if={false}>
          <Button
            className="w-[100%] meta-live-gradient-default-button"
            theme="default"
            onClick={() => dataDispatch.addItem()}
          >
            + 环节
          </Button>
        </div>
      </div>
    </LinkTableContext.Provider>
  );
}
