import { HelpCircleIcon } from 'tdesign-icons-react';
import { Tooltip, TooltipProps } from 'tdesign-react';
import { tooltipColumn, TooltipColumnItem } from './utils';
import './index.less';

export interface ITooltipColumn extends TooltipProps {
  type: keyof typeof tooltipColumn;
  coverTooltipColumn?: TooltipColumnItem; // 覆盖默认的tooltipColumn
}

export function TooltipColumn({
  type,
  coverTooltipColumn,
  theme = 'light',
  placement = 'right-top',
  overlayInnerStyle = { padding: 20 },
  overlayStyle = { top: -4, left: -6 },
  ...rest
}: ITooltipColumn) {
  const defaultTooltipColumn = {
    ...tooltipColumn[type],
    ...(coverTooltipColumn || {}),
  };
  return (
    <div className="scriptList-components-tooltipColumn">
      <div className="title">{defaultTooltipColumn.title}</div>
      <Tooltip
        content={
          defaultTooltipColumn.type === 'text' ? (
            defaultTooltipColumn.tooltipContent
          ) : (
            <img
              src={defaultTooltipColumn.tooltipContent}
              alt=""
              style={{ width: 319 }}
            />
          )
        }
        theme={theme}
        placement={placement}
        overlayInnerStyle={{
          width: defaultTooltipColumn.type === 'text' ? 200 : 319,
          ...overlayInnerStyle,
        }}
        overlayStyle={{ ...overlayStyle }}
        {...rest}
      >
        <HelpCircleIcon style={{ cursor: 'pointer' }} />
      </Tooltip>
    </div>
  );
}
