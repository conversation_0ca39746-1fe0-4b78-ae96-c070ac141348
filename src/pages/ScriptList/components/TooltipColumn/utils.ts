import viewContentImg from './assets/viewContent.png';
import showyTextImg from './assets/showyText.png';
import liveImage from './assets/liveImage.png';

export interface TooltipColumnItem {
  type?: string;
  title?: string;
  tooltipContent?: string;
}

export const tooltipColumn = {
  linkType: {
    type: 'text',
    title: '环节类型',
    tooltipContent: '对应智能视频的不同排版设计',
  },
  linkTheme: {
    type: 'text',
    title: '环节主题',
    tooltipContent: '应用于智能视频的每个画面标题',
  },
  speech: {
    type: 'text',
    title: '台词文案',
    tooltipContent: '应用于智能视频的每段口播话术',
  },
  // 画面内容（文字）
  viewContentForText: {
    type: 'text',
    title: '画面内容',
    tooltipContent: '应用于智能视频的每段画面展示',
  },
  // 画面内容（图片）
  viewContentForImg: {
    type: 'img',
    title: '画面内容',
    tooltipContent: viewContentImg,
  },
  featuredSound: {
    type: 'text',
    title: '画面音效',
    tooltipContent: '应用在智能视频中插入的音效效果',
  },
  showyText: {
    type: 'img',
    title: '文本贴纸',
    tooltipContent: showyTextImg,
  },
  liveImage: {
    type: 'img',
    title: '画面贴纸',
    tooltipContent: liveImage,
  },
  elementContent: {
    type: 'text',
    title: '元素内容',
    tooltipContent: '应用在智能视频中插入的元素',
  },
};
