import React, { useState } from 'react';
import {
  Button,
  Divider,
  Input,
  MessagePlugin,
  PrimaryTableCol,
  Space,
  Table,
} from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import './index.less';
import {
  IScriptRecord,
  useScriptListRequest,
} from '@/pages/ScriptList/hooks/useScriptListRequest';
import { CONTENT_TYPE_MAP } from '@/const/common';
import dayjs from 'dayjs';

interface IProps {
  templateId: string;
  scriptType: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'];
  application_scenarios: string;
  close: () => void;
  onCreateWithScript: (script: IScriptRecord | null) => void;
  onCreateScript?: () => void;
}

// TODO: 需要确定 templateId 和 SciptType 的对应关系
export function SelectScriptModel(props: IProps) {
  const {
    close,
    onCreateWithScript,
    application_scenarios,
    scriptType,
    onCreateScript,
  } = props;
  const [searchVal, setSearchVal] = useState<string>('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<IScriptRecord>();

  const { queryParams, setQueryParams, records, recordCount, loading } =
    useScriptListRequest({
      queryParamsDefault: {
        searchKey: searchVal,
        scriptType,
        pageNum: 1,
        pageSize: 10,
        application_scenarios,
      },
    });

  const columns: PrimaryTableCol<IScriptRecord>[] = [
    {
      colKey: 'row-select',
      type: 'single',
      // 允许单选(Radio)取消行选中
      checkProps: { allowUncheck: true },
      width: 50,
    },
    {
      colKey: 'research_id',
      title: 'ID',
    },
    {
      colKey: 'source',
      title: '来源',
      width: 130,
      cell: () => <>智能创建</>,
    },
    {
      colKey: 'research_name',
      title: '脚本名称',
    },
    {
      colKey: 'all_nodes_count',
      title: '脚本环节数',
    },
    // {
    //   colKey: 'user_id',
    //   title: '脚本创建人',
    // },
    {
      colKey: 'create_time',
      title: '创建时间',
      cell: ({ row }) => (
        <>{dayjs.unix(+row.create_time).format('YYYY-MM-DD hh:mm:ss')}</>
      ),
    },
  ];

  return (
    <div className="select-script-model">
      <Divider style={{ margin: 0 }} />
      <div className="operate-box">
        <span className="text">脚本</span>
        <div className="search-box" style={{ width: '364px' }}>
          <Input
            value={searchVal}
            placeholder="请输入你需要搜索的内容"
            onChange={(value) => {
              setSearchVal(value);
            }}
            onEnter={(value) => {
              setQueryParams({
                ...queryParams,
                searchKey: value,
              });
            }}
            suffixIcon={<SearchIcon />}
          />
        </div>
      </div>
      <Table
        style={{ padding: '0 20px' }}
        rowKey="research_id"
        // bordered
        columns={columns}
        data={records}
        loading={loading}
        maxHeight={542}
        selectedRowKeys={selectedRowKeys}
        onSelectChange={(value, { selectedRowData }) => {
          console.log(value, selectedRowData);
          setSelectedRowKeys(value as string[]);
          setSelectedRowData(selectedRowData[0]);
        }}
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: recordCount,
        }}
        onPageChange={(pageInfo) => {
          setQueryParams({
            ...queryParams,
            // 切换每页条数时，重置页码
            pageNum:
              queryParams.pageSize === pageInfo.pageSize ? pageInfo.current : 1,
            pageSize: pageInfo.pageSize,
          });
        }}
      />
      <Divider style={{ margin: 0 }} />
      <div
        className="footer pagedoo-meta-live-global"
        style={{ justifyContent: 'space-between', margin: '20px' }}
      >
        <div className="flex items-center">
          {/* {!!onCreateScript && (
            <>
              <span>没有合适的脚本，</span>
              <Button
                variant="text"
                theme="primary"
                style={{ padding: 0 }}
                onClick={onCreateScript}
              >
                去创建
              </Button>
            </>
          )} */}
        </div>
        <Space>
          <Button className="gradient-default" theme="default" onClick={close}>
            取消
          </Button>
          <Button
            className="gradient-default"
            theme="default"
            onClick={() => onCreateWithScript(null)}
          >
            跳过，直接创建
          </Button>
          <Button
            disabled={!selectedRowData}
            onClick={() => {
              if (selectedRowData) {
                onCreateWithScript(selectedRowData);
              } else {
                console.error('请选择脚本');
                void MessagePlugin.error('请选择脚本');
              }
            }}
            theme="primary"
            className="gradient-primary"
          >
            开始创建
          </Button>
        </Space>
      </div>
    </div>
  );
}
