/* eslint-disable @typescript-eslint/no-non-null-assertion */
/**
 * <AUTHOR>
 * @date 2024/6/11 下午4:35
 * @desc index
 */

import React, { useCallback, useState } from 'react';
import { Button, Form, MessagePlugin, Space, Textarea } from 'tdesign-react';
import { SlideSelector } from '@/components/SlideSelector';
import { DraggableUpload } from '@/components/DraggableUpload';
import { NpcJob } from '@/pb/pb';
import { NPC_STEP, useIntervalNpcStatus } from '@/hooks/useIntervalNpcStatus';
import { Script } from '@/type/pagedoo';
import { COURSE_NPC_ID } from '@/utils/npc';
import { CommonScriptLoading } from '@/components/CommonScriptLoading';
import { GLOBAL_FIELDS } from '@/pages/Question/constant';

const { FormItem } = Form;

interface IProps {
  onSuccess: (args: {
    scriptViews: Script['views'];
    globalField: unknown;
  }) => void;
  onClose: () => void;
}

interface IFormValue {
  uploadWay: 'link' | 'file';
  linkUrl?: string;
  fileUrl?: string;
}

export function CourseCreateModal(props: IProps) {
  const { onClose, onSuccess } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [jobId, setJobId] = useState(''); // 任务id
  const { percent, stepName, startPolling, stopPolling } = useIntervalNpcStatus(
    {
      onJobError: () => {
        //   失败处理
        void MessagePlugin.error('文件解析失败');
        setLoading(false);
      },
      onJobFinish: () => {
        //   调用data接口获取同步数据， 然后跳转
        parseScriptContent().then();
      },
    }
  );
  const onSubmit = async () => {
    const values = form.getFieldsValue(true) as IFormValue;
    console.log(values);
    let uploadUrl: string;
    if (values.uploadWay === 'link') {
      uploadUrl = values.linkUrl!;
    } else {
      uploadUrl = values.fileUrl!;
    }
    if (!uploadUrl) {
      void MessagePlugin.warning('请先上传文件或者输入链接');
      return;
    }
    await startParseFile(uploadUrl, values);
  };

  const startParseFile = async (uploadUrl: string, values: IFormValue) => {
    if (!uploadUrl) {
      void MessagePlugin.warning('请先上传文件');
      return;
    }
    try {
      let fileType = 'html';
      setLoading(true);
      if (values.uploadWay === 'file') {
        // 获取文件类型，切割链接
        fileType = uploadUrl.split('.').pop()?.toLowerCase() || 'html';
      }
      // 获取JobId
      const createJobRes = await NpcJob.CreateNpcJob({
        npc_id: COURSE_NPC_ID,
        model_input_content: uploadUrl,
        model_input_type: fileType,
        auto_create_script: true,
        subject_parameters: Object.values(GLOBAL_FIELDS),
      });
      if (!createJobRes.job_id) {
        void MessagePlugin.error('文件解析失败');
        console.error('未获取到JobId');
        return;
      }
      setJobId(createJobRes.job_id);
      startPolling(createJobRes.job_id);
    } catch (e) {
      console.error(e, '文件解析失败');
      setLoading(false);
      void MessagePlugin.error('文件解析失败');
    }
  };

  const parseScriptContent = useCallback(async () => {
    try {
      const res = await NpcJob.QueryNpcJobInfo({
        job_id: jobId,
      });
      // 步骤确认
      if (res.job_stage !== NPC_STEP.GENERATE_SCRIPT) {
        void MessagePlugin.error('PPT解析数据获取失败');
        console.error(
          `步骤校验有误，应该为${NPC_STEP.GENERATE_SCRIPT} 实际为${res.job_stage}`
        );
        setLoading(false);
        return;
      }
      const scriptViews = JSON.parse(res.script_content) as Script['views'];
      /**
       * 全局配置，3 类都要加
       *
       * subject_content 映射
       */
      const globalField = JSON.parse(res.subject_content || '{}') as unknown;

      onSuccess({ scriptViews, globalField });
      setLoading(false);
      onClose();

      console.log(scriptViews, 'scriptViews');
    } catch (e) {
      console.error(e, '解析数据获取失败');
      setLoading(false);
      void MessagePlugin.error('解析数据获取失败');
    }
  }, [jobId, onClose, onSuccess]);

  return (
    <div className="relative pagedoo-meta-live-global">
      <Form
        form={form}
        onSubmit={onSubmit}
        colon
        labelWidth="106px"
        labelAlign="top"
      >
        <FormItem
          label="请提供图文链接/文件"
          name="uploadWay"
          initialData="file"
        >
          <SlideSelector
            border
            options={[
              {
                name: '图文文章链接',
                value: 'link',
              },
              {
                name: '图文文章文件',
                value: 'file',
              },
            ]}
          />
        </FormItem>

        <FormItem
          shouldUpdate={(prev, next) => {
            return prev.uploadWay !== next.uploadWay;
          }}
        >
          {/* extend*/}
          {({ getFieldValue }) => {
            const uploadWay = getFieldValue(
              'uploadWay'
            ) as IFormValue['uploadWay'];
            return uploadWay === 'link' ? (
              <FormItem
                label=""
                name="linkUrl"
                initialData=""
                key="linkUrl"
                rules={[
                  {
                    required: true,
                    message: '图文文章链接必填',
                    type: 'warning',
                  },
                  {
                    url: {
                      protocols: ['https'],
                      require_protocol: true,
                    },
                    message: '请输入正确的https链接',
                  },
                ]}
              >
                <Textarea placeholder="请输入链接" rows={5} />
              </FormItem>
            ) : (
              <FormItem
                label=""
                name="fileUrl"
                initialData=""
                key="fileUrl"
                rules={[
                  {
                    required: true,
                    message: '请上传文件',
                    type: 'warning',
                  },
                ]}
              >
                <DraggableUpload
                  prefix="https:"
                  onFail={() => {
                    void MessagePlugin.error('上传失败,请重新上传');
                  }}
                  onSuccess={() => {
                    void MessagePlugin.success('上传成功');
                  }}
                  onFileUrlChange={(url) => {
                    console.debug(url);
                  }}
                  desc="格式要求：DOC、DOCX、PPT、PPTX"
                  accept="application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation"
                />
              </FormItem>
            );
          }}
        </FormItem>

        <FormItem className="flex justify-end mt-20">
          <Space>
            <Button
              className="gradient-default"
              theme="default"
              onClick={onClose}
            >
              取消
            </Button>
            <Button className="gradient-primary" type="submit">
              开始生成脚本
            </Button>
          </Space>
        </FormItem>
      </Form>
      {loading && (
        <CommonScriptLoading
          stepName={stepName}
          percent={percent}
          onCancel={() => {
            stopPolling();
            setLoading(false);
          }}
        />
      )}
    </div>
  );
}
