import { useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { uuid } from '@tencent/midas-util';
import { useCreation, useUpdateEffect } from 'ahooks';
import {
  BaseScript,
  MaterialView,
  ScriptGlobalData,
  ScriptView,
} from '@/components/ScriptForm/type';
import { getAllResource } from '@/components/ScriptForm/utils';

export type ViewsItem = BaseScript['views'][number];

export type ReceivedScriptItem = Awaited<BaseScript>;

export type VideoInfo = {
  video_duration: string; // 服务端返回的为 string，需转换为 number
  video_address: string;
};

// 解析 video 取数据
export default function useVisionScriptInfo(
  receivedScript?: ReceivedScriptItem
): {
  globalField?: ScriptGlobalData;
  formValue?: any;
  views: BaseScript['views'];
  setGlobalField: React.Dispatch<React.SetStateAction<ScriptGlobalData>>;
  speech: BaseScript['globalSpeech'];
  setSpeech: (speech: BaseScript['globalSpeech']) => void;
} {
  const { state, pathname } = useLocation();
  // 避免路由参数变化丢失的情况
  const receivedState = useCreation(() => state, [pathname]);

  const computed = useMemo(() => {
    if (receivedScript) {
      const { views, globalField, globalSpeech } = receivedScript;

      let resource = globalField?.globalResourceList;

      if (globalField && !('globalResourceList' in globalField)) {
        resource = getAllResource(views as unknown as MaterialView[]);
      }
      return {
        globalField: {
          ...globalField,
          globalResourceList: resource,
        },
        views: views as unknown as ScriptView[],
        speech: globalSpeech,
      };
    }

    // 通过路由传参，协议
    const { scriptViews, elements, globalField } = receivedState || {};

    const globalResourceList = getAllResource(elements);

    // "中文名" 字段改名为 “文本贴纸 ”；“画面时长” 字段改名为 “时长”
    return {
      views: (scriptViews || []).map((x: ViewsItem) =>
        Object.assign(x, { scriptViewId: uuid() })
      ) as BaseScript['views'],
      globalField: { ...globalField, globalResourceList },
      speech: (scriptViews || [])
        .map((view: ScriptView) => view.speech)
        .join('\n'),
    };
  }, [receivedScript, receivedState]);

  const [views, setViews] = useState<BaseScript['views']>(computed.views);

  const [globalField, setGlobalField] = useState<ScriptGlobalData>(
    computed.globalField
  );

  // 8.26 维护全局话术
  const [speech, setSpeech] = useState<BaseScript['globalSpeech']>(
    computed.speech
  );

  useUpdateEffect(() => {
    setViews(computed.views);
    setGlobalField(computed.globalField);
    setSpeech(computed.speech);
  }, [computed]);

  return {
    views,
    globalField,
    setGlobalField,
    formValue: state?.formValue,
    speech,
    setSpeech,
  };
}
