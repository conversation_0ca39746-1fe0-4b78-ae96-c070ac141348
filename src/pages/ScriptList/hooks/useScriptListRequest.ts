import { useCallback, useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';
import { RespType } from '@/pb/config';
import { MetaFeedbackSvr } from '@/pb/pb';
import { CONTENT_TYPE_MAP } from '@/const/common';

export interface IScriptListRequestParams {
  searchKey?: string;
  scriptType: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'];
  // 分页参数
  pageSize?: number;
  pageNum?: number;
  application_scenarios: string;
}

export type IScriptRecord = RespType<
  typeof MetaFeedbackSvr.QueryOnlyResearchList
>['research_list'][number];

export const useScriptListRequest = ({
  queryParamsDefault,
}: {
  queryParamsDefault: IScriptListRequestParams;
}) => {
  const [queryParams, setQueryParams] =
    useState<IScriptListRequestParams>(queryParamsDefault);
  const [records, setRecords] = useState<IScriptRecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);
  const { runAsync, loading, error } = useRequest(
    () => {
      return MetaFeedbackSvr.QueryOnlyResearchList({
        type: queryParams.scriptType,
        search_key: queryParams?.searchKey || '',
        page_no: queryParams?.pageNum || 0,
        page_size: queryParams?.pageSize || 10,
        application_scenarios: queryParams?.application_scenarios,
      });
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    // 请求
    runAsync()
      .then((res) => {
        setRecords(res.research_list || []);
        setRecordCount(res.all_count || 0);
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        setRecords([]);
        setRecordCount(0);
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
      });
  }, [runAsync, queryParams]);

  const refresh = useCallback(() => {
    setQueryParams({ ...queryParams });
  }, [queryParams]);

  return {
    records,
    recordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
    run: runAsync,
  };
};
