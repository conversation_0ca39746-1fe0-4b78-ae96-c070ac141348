import { BaseScript } from '@/components/ScriptForm/type';
import {
  IVideoCreateMode,
  transformMap,
} from '@/pages/WorkbenchVideo/components/create/utils';
import { IEditData } from './index';
import { ITooltipColumn } from '../components/TooltipColumn';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';

interface IScriptLink {
  limitInput: {
    noImage: number;
    hasImage: number;
  };
}

export const getBreadcrumbName = (fromParam: string) => {
  const [type, application_scenarios] = (fromParam || '').split('__');
  return (
    transformMap[type as CONTETNT_TYPE]?.[application_scenarios]
      ?.selector_name || '基于文本创建'
  );
};

export type scriptSourceType = {
  [IVideoCreateMode.PRODUCT]: typeof ORIGIN_TYPE.E_COMMERCE;
  [IVideoCreateMode.PPT]: typeof ORIGIN_TYPE.PPT;
  [IVideoCreateMode.ARTICLE]: typeof ORIGIN_TYPE.DOC;
  [IVideoCreateMode.CONTENT]: typeof ORIGIN_TYPE.CONTENT;
};

export type IConfigProps = {
  [key in keyof scriptSourceType]: {
    getScriptInfo: (
      scriptViews: BaseScript['views'],
      globalField: BaseScript['globalField']
    ) => BaseScript;
    exclude: Array<string>;
    type: scriptSourceType[key];
    pictureTypeOptions: Array<{ label: string; value: string }>;
  };
};

export const scriptLinks: Record<string, IScriptLink> = {
  概述: {
    limitInput: {
      noImage: 300,
      hasImage: 100,
    },
  },
  分点概述: {
    limitInput: {
      noImage: 270,
      hasImage: 100,
    },
  },
  QA: {
    limitInput: {
      noImage: 150,
      hasImage: 100,
    },
  },
  结束语: {
    limitInput: {
      noImage: 50,
      hasImage: 50,
    },
  },
};

export const defaultInput = (fromParam: string): IEditData => {
  const application_scenarios = (fromParam || '').split('__')[1];
  const viewTypeMap = {
    [IVideoCreateMode.PRODUCT]: '近景',
    [IVideoCreateMode.PPT]: '概述',
    [IVideoCreateMode.ARTICLE]: '概述',
    [IVideoCreateMode.CONTENT]: '近景',
  };
  return {
    viewType: viewTypeMap[application_scenarios as keyof typeof viewTypeMap],
    viewName: '',
    viewContent: '',
    viewMainText: '',
    speech: '',
    flowerText: '',
    imageList: [],
  };
};

export const getImageListTooltipColumn = (
  fromParam: string
): Omit<ITooltipColumn, 'type'> => {
  const application_scenarios = (fromParam || '').split('__')[1];
  switch (application_scenarios) {
    case IVideoCreateMode.ARTICLE:
      return {
        coverTooltipColumn: {
          title: '画面内容-图片',
        },
        placement: 'left-top',
        overlayStyle: {},
      };
    case IVideoCreateMode.PPT:
    case IVideoCreateMode.CONTENT:
      return {
        coverTooltipColumn: {
          title: '画面内容',
        },
        placement: 'left-top',
        overlayStyle: {},
      };
    default:
      return {
        coverTooltipColumn: {
          title: '画面内容',
        },
      };
  }
};
