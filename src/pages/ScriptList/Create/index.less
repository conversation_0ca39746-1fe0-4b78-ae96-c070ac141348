.script-create-page {
    padding: 8px 16px;
    &-breadcrumb{
        color: rgba(0, 0, 0, 0.90);
        font-style: normal;
        font-weight: 600;
        margin-bottom: 16px;
    }

    &-content {
        padding: 20px 16px;
        border-radius: 8px;
        background: #FFFFFF;
        height: calc(100vh - 200px);

        &-title{
            font-size: 22px;
            font-weight: 500;
            color: #000;
        }

        &-theme{
            font-size: 16px;
            font-weight: 500;
            color: #000;
            margin: 16px 0;
        }
    }

    &-table {
        border: 1px solid #e8e8e8;
        border-radius: 0 0 8px 8px;

        &-btn{
            width: 100%;
            padding: 10px;
           
        }
        .gradient-add {
            border: none;
            border-radius: 4px;
            background: linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
            color: rgba(0, 71, 249, 1);

            &:active {
                .t-ripple {
                    opacity: 0.2;
                }
            }
        }
        .t-table__header {
          tr > th {
            background-color: #f8f6fb;
          }
        }

        .row-script-writing-btns{
            .t-button {
                border-radius: 4px;
                height: 25px;
                width: 25px;
                padding: 4px !important;
                border: none;
              }
            .sure-btn {
                background: linear-gradient(
                  88.08deg,
                  #0153ff -0.01%,
                  #2e7ffd 49.89%,
                  #c1a3fd 99.99%
                );
                margin-right: 4px;
              }
            .cancel-btn {
                color: #000;
                background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
            }
        }

        .frame-pic{
            position: relative;
            .layer{
                display: none;
                width: 57px;
                height: 57px;
                position: absolute;
                top: 0;
                left: 0;
                color: rgba(255, 255, 255, 0.9);
                text-align: center;
                line-height: 57px;
                cursor: pointer;
                border-radius: 4px;
                z-index: 2;
            }
        }

        .frame-pic--hover{
            &:hover {

                .layer--hover {
                    display: block;
                    background: rgba(45, 45, 45, 0.59);
                }
            }
        }
    }

    &-footer{
        display: flex;
        gap: 12px;
        padding: 20px 20px 20px 40px;
        position: fixed;
        background: #fff;
        bottom: 0;
        width: 100%;
        margin-top: 20px;
        margin-left: -40px;
    }
    .t-table__body {
        td {
            img {
                vertical-align: top;
            }
        }
    }
}