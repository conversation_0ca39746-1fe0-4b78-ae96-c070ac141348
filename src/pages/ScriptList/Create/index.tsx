/* eslint-disable no-param-reassign */
// noinspection NonAsciiCharacters

import React, { Fragment, useEffect, useMemo, useState } from 'react';
import {
  Breadcrumb,
  Button,
  DialogPlugin,
  Input,
  Link,
  MessagePlugin,
  Popconfirm,
  Popup,
  PrimaryTableCol,
  Select,
  Table,
  Textarea,
} from 'tdesign-react';
import {
  CheckIcon,
  CloseIcon,
  DownloadIcon,
  MoveIcon,
} from 'tdesign-icons-react';
import './index.less';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { MetaFeedbackSvr } from '@/pb/pb';
import { DEFAULT_SCRIPT_VIEWS } from '@/type/pagedoo';
import { useMemoizedFn, useResetState } from 'ahooks';
import { exportExcelByArray } from '@/utils';
import ActionSuccess from '@/assets/images/create-condition-success.png';
import { cloneDeep } from 'lodash-es';
import { uuid } from '@tencent/midas-util';
import { getTimeIntervals } from '@/utils/broadcastDuration';
import {
  scriptLinks,
  IConfigProps,
  scriptSourceType,
  getBreadcrumbName,
  defaultInput,
  getImageListTooltipColumn,
} from '@/pages/ScriptList/Create/const';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { TitleManage } from '../components/TitleManage';
import { queryScriptList } from '@/components/ScriptForm/utils';
import {
  BaseScript,
  ScriptGlobalData,
  ScriptView,
} from '@/components/ScriptForm/type';
import { IVideoCreateMode } from '@/pages/WorkbenchVideo/components/create/utils';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { TooltipColumn } from '../components/TooltipColumn';
import { PicCell } from './components/PicCell';
import { ShowyTextCell } from './components/ShowyTextCell';

const { BreadcrumbItem } = Breadcrumb;

const classPrefix = 'script-create-page';
export type ITableRecord = ScriptView & { scriptViewId: string };
export type IEditData = Pick<
  ITableRecord,
  | 'viewType'
  | 'viewName'
  | 'viewContent'
  | 'viewMainText'
  | 'speech'
  | 'imageList'
  | 'flowerText'
>;

export const CONFIG: IConfigProps = {
  // PPT
  [IVideoCreateMode.PPT]: {
    getScriptInfo: (scriptViews, globalField) => {
      const script_info: BaseScript = {
        backgroundImage: [],
        views: scriptViews,
        type: ORIGIN_TYPE.PPT,
        size: [1280, 720],
        globalField,
      };
      return script_info;
    },
    exclude: ['环节类型', '环节主题', '画面正文', '文本贴纸'],
    type: ORIGIN_TYPE.PPT,
    pictureTypeOptions: [
      { label: '概述', value: '概述' },
      { label: '分点概述', value: '分点概述' },
      { label: 'QA', value: 'QA' },
      { label: '结束语', value: '结束语' },
    ],
  },
  [IVideoCreateMode.ARTICLE]: {
    getScriptInfo: (scriptViews, globalField) => {
      const script_info: BaseScript = {
        backgroundImage: [],
        views: scriptViews,
        type: ORIGIN_TYPE.DOC,
        size: [1280, 720],
        globalField,
      };
      return script_info;
    },
    exclude: ['文本贴纸'],
    type: ORIGIN_TYPE.DOC,
    pictureTypeOptions: [
      { label: '概述', value: '概述' },
      { label: '分点概述', value: '分点概述' },
      { label: 'QA', value: 'QA' },
      { label: '结束语', value: '结束语' },
    ],
  },
  // 电商带货
  [IVideoCreateMode.PRODUCT]: {
    getScriptInfo: (scriptViews, globalField) => {
      const script_info: BaseScript = {
        backgroundImage: [],
        views: scriptViews,
        type: ORIGIN_TYPE.E_COMMERCE,
        size: [455, 812],
        globalField,
      };
      return script_info;
    },
    exclude: ['画面正文', '环节主题'],
    type: ORIGIN_TYPE.E_COMMERCE,
    pictureTypeOptions: [
      { label: '近景', value: '近景' },
      { label: '远景', value: '远景' },
      { label: '特写', value: '特写' },
    ],
  },
  // 直播内容带货
  [IVideoCreateMode.CONTENT]: {
    getScriptInfo: (scriptViews, globalField) => {
      const script_info: BaseScript = {
        backgroundImage: [],
        views: scriptViews,
        type: ORIGIN_TYPE.CONTENT,
        size: [455, 812],
        globalField,
      };
      return script_info;
    },
    exclude: ['环节类型', '画面正文', '环节主题', '文本贴纸'],
    type: ORIGIN_TYPE.CONTENT,
    pictureTypeOptions: [
      { label: '近景', value: '近景' },
      { label: '远景', value: '远景' },
      { label: '特写', value: '特写' },
    ],
  },
};

export interface ICreateScriptProps {
  from?: string;
  researchId?: string;
  saveButtonText?: string;
  backText?: string;
  saveCallback?: (v: Record<string, any>) => void;
  cancelCallback?: () => void;
  receiveScript?: BaseScript;
}

export default function CreateScript({
  from,
  cancelCallback,
  researchId: fromProps_researchId,
  saveButtonText,
  backText,
  saveCallback,
  receiveScript,
}: ICreateScriptProps) {
  const navigator = useNavigate();
  const { state } = useLocation();
  const [searchParams] = useSearchParams();
  const [records, setRecords] = useState<ITableRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [editRowIndex, setEditRowIndex] = useState(-1);
  const [scriptName, setScriptName] = useState(''); // 脚本名称
  const [isEditStatus, setIsEditStatus] = useState(false);
  const [textAreaObject, setTextAreaObject, resetTextAreaObject] =
    useResetState<IEditData>(
      cloneDeep(defaultInput(from || searchParams.get('from') || ''))
    );
  const [titleData, setTitleData] = useState<
    Pick<ScriptGlobalData, 'title' | 'subTitle'>
  >({
    title: '',
    subTitle: '',
  });
  const captionMaxLength = useMemo(() => {
    const currentImage = textAreaObject.imageList?.length
      ? 'hasImage'
      : 'noImage';
    return scriptLinks[textAreaObject.viewType]?.limitInput[currentImage] || 0;
  }, [textAreaObject]);

  const isEditRow = (rowIndex: number) => editRowIndex === rowIndex;

  const config = useMemo(() => {
    const params = from || searchParams.get('from');
    if (params) {
      const [, origin] = params.split('__');
      return CONFIG[origin as keyof scriptSourceType];
    }
    void MessagePlugin.error('缺少脚本参数');
  }, [searchParams, from]);

  useEffect(() => {
    if (editRowIndex === -1) {
      resetTextAreaObject();
    }
  }, [editRowIndex, resetTextAreaObject]);

  //   获取列表数据
  const fetchRecords = useMemoizedFn(async () => {
    if (state?.scriptViews?.length) {
      // 新建时会通过 state 传递值
      const scriptViews = (state.scriptViews as ScriptView[]).map((item) => ({
        ...item,
        scriptViewId: uuid(),
      }));
      setRecords(scriptViews);

      if (state.globalField as ScriptGlobalData) {
        setTitleData({
          title: state.globalField.title,
          subTitle: state.globalField.subTitle,
        });
      }
      return;
    }
    setLoading(true);
    const research_id = fromProps_researchId || searchParams.get('research_id');
    if (!research_id && !receiveScript) {
      setLoading(false);
      handleAdd();
      // void MessagePlugin.error('缺少脚本参数');
      return;
    }
    try {
      let scriptRes: any = {};
      if (research_id) scriptRes = await queryScriptList(research_id);
      if (!research_id && receiveScript)
        scriptRes = {
          script_list: [{ script_info: JSON.stringify(receiveScript) }],
        };
      const scriptInfoRes = JSON.parse(
        scriptRes.script_list[0].script_info || '{}'
      ) as BaseScript;
      const scriptViews = scriptInfoRes.views.map((item: ScriptView) => ({
        ...item,
        scriptViewId: uuid(),
      }));
      console.log('scriptInfoRes', scriptRes, scriptInfoRes);
      setScriptName(scriptRes.script_list[0].research_name || '');
      setIsEditStatus(!!research_id);
      if (scriptInfoRes.globalField) {
        setTitleData({
          title: scriptInfoRes.globalField.title,
          subTitle: scriptInfoRes.globalField.subTitle,
        });
      }
      setRecords(scriptViews);
    } catch (error) {
      console.error('请求列表失败', error);
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    fetchRecords().then();
  }, [fetchRecords]);

  const confirmRowData = () => {
    if (editRowIndex === -1) return;
    return records.map((item, index) => {
      if (index === editRowIndex) {
        return {
          ...item,
          ...textAreaObject,
        };
      }
      return item;
    });
  };

  //   添加数据
  const handleAdd = (insertIndex = records.length) => {
    const handleRecords = confirmRowData();
    const newRecords = handleRecords
      ? cloneDeep(handleRecords)
      : cloneDeep(records);
    const addItem: ITableRecord = {
      ...DEFAULT_SCRIPT_VIEWS,
      scriptViewId: uuid(),
    };
    newRecords.splice(insertIndex, 0, addItem);

    setRecords(newRecords);
    setEditRowIndex(insertIndex);
  };

  const columns = (
    [
      {
        colKey: 'drag', // 列拖拽排序必要参数
        title: '排序',
        cell: () => (
          <span>
            <MoveIcon />
          </span>
        ),
        width: 46,
      },
      {
        colKey: 'index',
        title: '序号',
        width: 60,
        cell: ({ rowIndex }) => <div>{rowIndex + 1}</div>,
      },
      {
        colKey: '环节类型',
        width: 150,
        title: <TooltipColumn type="linkType" />,
        cell: ({ rowIndex, row }) => {
          return isEditRow(rowIndex) ? (
            <Select
              value={textAreaObject.viewType}
              onChange={(val) => {
                setTextAreaObject((prev) => ({
                  ...prev,
                  viewType: val as string,
                }));
              }}
              options={config?.pictureTypeOptions || []}
            />
          ) : (
            <div>{row.viewType}</div>
          );
        },
      },
      {
        colKey: '环节主题',
        title: <TooltipColumn type="linkTheme" />,
        cell: ({ row, rowIndex }) => {
          return isEditRow(rowIndex) ? (
            <Textarea
              placeholder="请输入内容"
              autosize
              value={textAreaObject.viewName}
              onChange={(val) => {
                setTextAreaObject((prev) => ({
                  ...prev,
                  viewName: val,
                }));
              }}
            />
          ) : (
            <div>{row.viewName}</div>
          );
        },
      },
      {
        colKey: '台词文案',
        title: <TooltipColumn type="speech" />,
        cell: ({ row, rowIndex }) => {
          return isEditRow(rowIndex) ? (
            <Textarea
              placeholder="请输入内容"
              value={textAreaObject.speech}
              autosize
              onChange={(val) => {
                setTextAreaObject((prev) => ({
                  ...prev,
                  speech: val,
                }));
              }}
            />
          ) : (
            <div>{row.speech}</div>
          );
        },
      },
      // {
      //   colKey: '画面内容',
      //   title: '画面大纲',
      //   cell: ({ row, rowIndex }) => {
      //     return isEditRow(rowIndex) ? (
      //       <Textarea
      //         placeholder="请输入内容"
      //         autosize
      //         value={textAreaObject.viewContent}
      //         onChange={(val) => {
      //           setTextAreaObject((prev) => ({
      //             ...prev,
      //             viewContent: val,
      //           }));
      //         }}
      //       />
      //     ) : (
      //       <div>{row.viewContent}</div>
      //     );
      //   },
      // },
      {
        colKey: '画面正文',
        title: (
          <TooltipColumn
            type="viewContentForText"
            coverTooltipColumn={{ title: '画面内容-文本' }}
          />
        ),
        cell: ({ row, rowIndex }) => {
          return isEditRow(rowIndex) ? (
            <Textarea
              placeholder="请输入内容"
              autosize
              value={textAreaObject.viewMainText}
              onChange={(val) => {
                setTextAreaObject((prev) => ({
                  ...prev,
                  viewMainText: val,
                }));
              }}
              maxlength={captionMaxLength}
              status={
                (textAreaObject?.viewMainText?.length ?? 0) > captionMaxLength
                  ? 'warning'
                  : 'default'
              }
              allowInputOverMax
            />
          ) : (
            <div>{row.viewMainText}</div>
          );
        },
      },
      {
        colKey: 'imageList',
        width: 200,
        title: (
          <TooltipColumn
            type="viewContentForImg"
            {...getImageListTooltipColumn(
              from || searchParams.get('from') || ''
            )}
          />
        ),
        cell: ({ row, rowIndex }) => (
          <PicCell
            row={row}
            rowIndex={rowIndex}
            editRowIndex={editRowIndex}
            textAreaObject={textAreaObject}
            setTextAreaObject={setTextAreaObject}
          />
        ),
      },
      {
        colKey: '文本贴纸',
        title: (
          <TooltipColumn
            type="showyText"
            placement="left-top"
            overlayStyle={{}}
          />
        ),
        cell: ({ row, rowIndex }) => (
          <ShowyTextCell
            row={row}
            rowIndex={rowIndex}
            editRowIndex={editRowIndex}
            textAreaObject={textAreaObject}
            setTextAreaObject={setTextAreaObject}
          />
        ),
      },
      {
        colKey: 'operate',
        fixed: 'right',
        width: 120,
        title: '操作',
        cell: ({ row, rowIndex }) => {
          return isEditRow(rowIndex) ? (
            <div className="row-script-writing-btns">
              <Button
                className="sure-btn"
                icon={<CheckIcon />}
                onClick={() => {
                  const newRecords = confirmRowData();
                  newRecords && setRecords(newRecords);
                  setEditRowIndex(-1);
                }}
              />
              <Button
                className="cancel-btn"
                theme="default"
                icon={<CloseIcon />}
                onClick={() => {
                  setEditRowIndex(-1);
                }}
              />
            </div>
          ) : (
            <div className="flex flex-wrap" style={{ gap: '8px' }}>
              <Link
                theme="primary"
                hover="color"
                onClick={() => {
                  setEditRowIndex(rowIndex);
                  setTextAreaObject({
                    viewType: row.viewType,
                    viewName: row.viewName,
                    viewContent: row.viewContent,
                    viewMainText: row.viewMainText,
                    speech: row.speech,
                    flowerText: row.flowerText,
                    imageList: cloneDeep(row.imageList),
                  });
                }}
              >
                编辑
              </Link>
              <Popup content="向下插入一行" trigger="hover">
                <Link
                  theme="primary"
                  hover="color"
                  onClick={() => {
                    handleAdd(rowIndex + 1);
                  }}
                >
                  插入
                </Link>
              </Popup>
              <br />
              <Popconfirm
                content="确认删除吗"
                destroyOnClose
                placement="top"
                showArrow
                theme="default"
                onConfirm={() => {
                  records.splice(rowIndex, 1);
                  setRecords([...records]);
                }}
              >
                <Link theme="danger" hover="color">
                  删除
                </Link>
              </Popconfirm>
            </div>
          );
        },
      },
    ] as Array<PrimaryTableCol<ITableRecord>>
  ).filter((obj) => !config?.exclude.includes(obj.colKey!));

  function onDragSort({ newData }: { newData: ITableRecord[] }) {
    editRowIndex !== -1 && setEditRowIndex(-1);
    // 数据受控实现
    setRecords(newData);
  }

  const saveScript = async ({
    script,
    scriptName,
    titleField,
  }: {
    script: BaseScript['views'];
    scriptName: string;
    titleField: Pick<ScriptGlobalData, 'title' | 'subTitle'>;
  }) => {
    if (!config) {
      void MessagePlugin.warning('脚本类型有误');
      return;
    }
    // 文案时长重置
    const timeList = getTimeIntervals(script.map((item) => item.speech));
    script.forEach((item, index) => {
      item.duration = timeList[index];
    });
    const scriptInfo = config.getScriptInfo(script, titleField);
    let researchId = fromProps_researchId || searchParams.get('research_id');
    const fromParam = from || searchParams.get('from');
    const [type, application_scenarios] = (fromParam || '').split('__');
    const typeText = Object.entries(CONTENT_TYPE_MAP).find(
      ([, value]) => value.value === type
    )?.[1].label;
    if (saveCallback) {
      saveCallback?.({ scriptInfo: JSON.stringify(scriptInfo) });
      return;
    }
    const loading = await MessagePlugin.loading('保存中...');
    try {
      if (isEditStatus && researchId) {
        // TODO: 这里的接口目前不支持修改名称
        await MetaFeedbackSvr.ModifyOneResearch({
          research_id: researchId,
          script_info: JSON.stringify(scriptInfo),
          nodes_count: script.length,
        });
      } else {
        const createRes = await MetaFeedbackSvr.SaveScript({
          research_name: scriptName,
          script_info: [JSON.stringify(scriptInfo)],
          type,
          application_scenarios,
          source: config.type,
          nodes_count: [script.length],
        });
        researchId = createRes.script_list[0].research_id;
      }
      const myDialog = DialogPlugin({
        width: 680,
        footer: null,
        closeBtn: false,
        closeOnOverlayClick: false,
        closeOnEscKeydown: false,
        body: (
          <div className="pagedoo-meta-live-global flex flex-col items-center pt-12">
            <img
              style={{ height: '74px', width: '88px' }}
              src={ActionSuccess}
              alt=""
            />
            <div className="my-16">保存脚本成功！</div>
            <div className="my-16">
              您可以继续基于该脚本创建{typeText}，也可以进入管理列表查看与管理
            </div>
            <div className="flex" style={{ gap: '8px' }}>
              <Button
                theme="default"
                className="gradient-default"
                onClick={() => {
                  myDialog.hide();
                  navigator('/script-list');
                }}
              >
                返回管理列表
              </Button>
              <Button
                className="gradient-primary"
                onClick={() => {
                  myDialog.hide();
                  navigator(
                    `/script-list?selectResearchId=${researchId}&from=${fromParam}`
                  );
                }}
              >
                去创建{typeText}
              </Button>
            </div>
          </div>
        ),
        onClose: () => {
          myDialog.hide();
        },
      });
    } catch (error) {
      void MessagePlugin.error('保存脚本失败');
      console.log('保存脚本失败', error);
    } finally {
      loading.close();
    }
  };

  const onDownloadScript = () => {
    const header = [
      '序号',
      '环节类型',
      '画面名称',
      '画面内容',
      '画面正文',
      '台词文案',
    ];
    const data = records.map((item, index) => [
      `${index}`,
      item.viewType,
      item.viewName,
      item.viewContent,
      item.viewMainText,
      item.speech,
    ]);
    const fileName = `脚本.xlsx`;
    exportExcelByArray([header, ...data], fileName);
  };

  return (
    <div className={classPrefix}>
      {!from && (
        <div className={`${classPrefix}-breadcrumb`}>
          <Breadcrumb separator=">" maxItemWidth="140px">
            <BreadcrumbItem onClick={() => navigator('/script-list')}>
              脚本
            </BreadcrumbItem>
            <BreadcrumbItem>
              {getBreadcrumbName(from || searchParams.get('from') || '')}
            </BreadcrumbItem>
          </Breadcrumb>
        </div>
      )}
      <div className={`${classPrefix}-content`}>
        {!from && (
          <div className={`${classPrefix}-content-title mb-20 mt-4`}>
            智能生成脚本如下：
          </div>
        )}
        <TitleManage titleData={titleData} onChange={(v) => setTitleData(v)} />
        <div className={`${classPrefix}-table`}>
          <Table
            verticalAlign="middle"
            rowKey="scriptViewId"
            dragSort="row-handler"
            columns={columns}
            data={records}
            loading={loading}
            maxHeight="calc(100vh - 426px)"
            onDragSort={onDragSort}
          />
          <div className={`${classPrefix}-table-btn`}>
            <Button
              theme="default"
              className="gradient-add"
              style={{ width: '100%' }}
              onClick={() => handleAdd()}
            >
              + 环节
            </Button>
          </div>
        </div>
      </div>
      <div className={`${classPrefix}-footer`}>
        <Button
          x-if={saveButtonText}
          size="large"
          className="btn-background-primary border-none"
          onClick={() => {
            const handleRecords = confirmRowData();
            const newRecords = handleRecords ? handleRecords : records;
            setRecords(newRecords);
            setEditRowIndex(-1);
            saveScript({
              script: newRecords,
              scriptName,
              titleField: titleData,
            }).then();
          }}
        >
          {saveButtonText}
        </Button>
        <Fragment x-if={!saveButtonText}>
          <Popconfirm
            showArrow={false}
            icon={<></>}
            content={
              <div style={{ width: '100%' }}>
                <div
                  style={{ height: '22px', lineHeight: '22px' }}
                  className="font-semibold mb-4"
                >
                  脚本名称
                </div>
                <Input
                  disabled={isEditStatus}
                  placeholder="请输入脚本名称"
                  value={scriptName}
                  style={{ width: '320px' }}
                  onChange={(value) => {
                    setScriptName(value);
                  }}
                />
              </div>
            }
            confirmBtn={
              <span className="pagedoo-meta-live-global">
                <Button
                  className="gradient-primary"
                  size="small"
                  loading={loading}
                  onClick={() => {
                    if (!scriptName) {
                      void MessagePlugin.error('请输入脚本名称');
                      return;
                    }
                    const handleRecords = confirmRowData();
                    const newRecords = handleRecords ? handleRecords : records;
                    setRecords(newRecords);
                    setEditRowIndex(-1);
                    saveScript({
                      script: newRecords,
                      scriptName,
                      titleField: titleData,
                    }).then();
                  }}
                >
                  确认
                </Button>
              </span>
            }
            cancelBtn={
              <span className="pagedoo-meta-live-global">
                <Button
                  className="gradient-default"
                  theme="default"
                  size="small"
                >
                  取消
                </Button>
              </span>
            }
          >
            <Button
              theme="primary"
              size="large"
              className="btn-background-primary border-none w-[150px]"
            >
              保存脚本
            </Button>
          </Popconfirm>
        </Fragment>

        <Button
          size="large"
          theme="default"
          className="gradient-default"
          style={{ width: 110 }}
          onClick={() => {
            if (cancelCallback) {
              cancelCallback?.();
            } else {
              const application_scenarios = (
                searchParams.get('from') || ''
              ).split('__')[1];
              const research_id = searchParams.get('research_id');
              // 脚本编辑
              if (research_id) {
                navigator(`/script-list?sub_type=${application_scenarios}`);
              } else {
                // 新建脚本返回上一页
                navigator(-1);
              }
            }
          }}
        >
          {backText || '返回'}
        </Button>
        {!from && (
          <Button
            size="large"
            theme="default"
            icon={<DownloadIcon />}
            onClick={() => {
              onDownloadScript();
            }}
            style={{
              background:
                'linear-gradient(88deg, #C2D6FF -0.01%, #CDE0FF 49.89%, #F0E9FF 99.99%)',
              border: 'none',
              color: 'rgba(0, 71, 249, 1)',
            }}
          >
            下载此版本
          </Button>
        )}
      </div>
    </div>
  );
}
