import { Textarea, Image } from 'tdesign-react';
import { IEditData, ITableRecord } from '../../index';
import schemaImg from './assets/schemaImg.png';
import './index.less';

export interface IShowyTextCellProps {
  editRowIndex: number;
  row: ITableRecord;
  rowIndex: number;
  textAreaObject: IEditData;
  setTextAreaObject: React.Dispatch<React.SetStateAction<IEditData>>;
}

export function ShowyTextCell({
  row,
  editRowIndex,
  rowIndex,
  textAreaObject,
  setTextAreaObject,
}: IShowyTextCellProps) {
  const isEditRow = (rowIndex: number) => editRowIndex === rowIndex;

  return (
    <div className="create-components-showyTextCell">
      {(isEditRow(rowIndex) || !!row.flowerText) && (
        <Image
          src={schemaImg}
          style={{
            width: 56,
            height: 56,
            cursor: 'pointer',
            marginRight: 8,
            flexShrink: 0,
          }}
          fit="contain"
          shape="round"
          error="加载失败"
        />
      )}
      {isEditRow(rowIndex) ? (
        <Textarea
          placeholder="请输入内容"
          autosize
          value={textAreaObject.flowerText}
          onChange={(val) => {
            setTextAreaObject((prev) => ({
              ...prev,
              flowerText: val,
            }));
          }}
        />
      ) : (
        <div>{row.flowerText}</div>
      )}
    </div>
  );
}
