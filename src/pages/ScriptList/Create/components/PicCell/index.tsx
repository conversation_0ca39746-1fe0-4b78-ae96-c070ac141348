import {
  ImageViewer,
  Image,
  Upload,
  Button,
  UploadFile,
  MessagePlugin,
} from 'tdesign-react';
import { IEditData, ITableRecord } from '../../index';
import { DeleteIcon } from 'tdesign-icons-react';
import { uploadRequest } from '@/utils/cos';
import { MATERIAL_TYPE } from '@/configs/upload';
import AddIcon from './assets/addIcon.png';
import './index.less';

export interface IPicCellProps {
  editRowIndex: number;
  row: ITableRecord;
  rowIndex: number;
  textAreaObject: IEditData;
  setTextAreaObject: React.Dispatch<React.SetStateAction<IEditData>>;
}

export function PicCell({
  row,
  editRowIndex,
  rowIndex,
  textAreaObject,
  setTextAreaObject,
}: IPicCellProps) {
  const isEditRow = (rowIndex: number) => editRowIndex === rowIndex;
  const imageList = isEditRow(rowIndex)
    ? textAreaObject.imageList
    : row.imageList;
  const handleRequest = async (file: UploadFile) => {
    const loading = await MessagePlugin.loading('上传中...');
    const result = await uploadRequest(
      MATERIAL_TYPE.FILE,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      file.raw!,
      0,
      () => void 0
    );
    loading.close();
    return {
      status: result.code === 200 ? 'success' : 'fail',
      response: { url: result.url, cosKey: result.key },
    } as const;
  };
  return (
    <div className="flex flex-wrap create-components-picCell">
      {imageList
        ?.filter((item) => !!item.url)
        ?.map((item, index) => (
          <div
            key={item.url}
            className={`frame-pic ${
              isEditRow(rowIndex) ? 'frame-pic--hover' : ''
            }`}
          >
            <ImageViewer
              trigger={({ open }) => (
                <Image
                  src={item.url || ''}
                  style={{
                    width: 56,
                    height: 56,
                    cursor: 'pointer',
                    marginRight: 10,
                    marginBottom: 10,
                    background: 'rgba(0, 0, 0, 0.9)',
                  }}
                  fit="contain"
                  shape="round"
                  onClick={open}
                  error="加载失败"
                />
              )}
              images={[item.url || '']}
            />
            <div
              className={`layer ${isEditRow(rowIndex) ? 'layer--hover' : ''}`}
              onClick={() => {
                textAreaObject?.imageList?.splice(index, 1);
                setTextAreaObject((prev) => ({
                  ...prev,
                  imageList: textAreaObject.imageList,
                }));
              }}
            >
              <DeleteIcon />
            </div>
          </div>
        ))}
      {editRowIndex === rowIndex && (
        <div
          className="row-script-writing-btns flex items-center"
          style={{ marginTop: -5 }}
        >
          <Upload
            showUploadProgress
            theme="custom"
            accept=".jpg,.jpeg,.png"
            requestMethod={(file) => {
              return handleRequest(file);
            }}
            onSuccess={(file) => {
              const newData = textAreaObject.imageList || [];
              newData?.push({ url: file?.response?.url });
              setTextAreaObject((prev) => ({
                ...prev,
                imageList: newData,
              }));
            }}
          >
            <Button
              theme="default"
              className="cancel-btn"
              icon={<img src={AddIcon} alt="" className="cancel-btn-addIcon" />}
            />
          </Upload>
        </div>
      )}
    </div>
  );
}
