import React, { useState, useMemo } from 'react';
import './index.less';
import { Button, Input } from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import { List } from '@/pages/ScriptList/CourseList/list';
import { useScriptListRequest } from '@/pages/ScriptList/hooks/useScriptListRequest';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { useNavigate } from 'react-router-dom';
import { uuid } from '@tencent/midas-util';

const classPrefix = 'script-list-page';

// 短视频脚本列表
export default function CourseList(props: {
  type: string;
  from: (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'];
}) {
  const navigate = useNavigate();
  const { type, from } = props;
  const [searchQuestionVal, setSearchQuestionVal] = useState('');

  const {
    queryParams,
    setQueryParams,
    records,
    refresh,
    recordCount,
    loading,
  } = useScriptListRequest({
    queryParamsDefault: {
      searchKey: searchQuestionVal,
      scriptType: from,
      pageNum: 1,
      pageSize: 10,
      application_scenarios: type,
    },
  });

  const createPath = useMemo(() => {
    if (type === CONTENT_TYPE_MAP.Video.children.Vision.value) {
      return '/script-list/vision/create';
    }

    return '/script-list/create';
  }, [type]);

  return (
    <div className={classPrefix}>
      <div className={`${classPrefix}-content`}>
        <div className={`${classPrefix}-content-inner-box`}>
          <div className="right">
            <div className="operate-box">
              <div className="flex flex-1 gap-3">
                <Button
                  theme="primary"
                  className="gradient-primary"
                  onClick={() => {
                    const uid = uuid(12);
                    navigate(`/question?from=${from}__${type}&uid=${uid}`);
                  }}
                >
                  智能创建
                </Button>
                <Button
                  className="gradient-default"
                  theme="default"
                  onClick={() => {
                    navigate(`${createPath}?from=${from}__${type}&_blank`);
                  }}
                  x-if={type !== '3'}
                >
                  从空白创建
                </Button>
              </div>

              <div className="search-box" style={{ width: '296px' }}>
                <Input
                  value={searchQuestionVal}
                  placeholder="请输入你需要搜索的内容"
                  onChange={(value) => {
                    setSearchQuestionVal(value);
                  }}
                  onEnter={(value) => {
                    setQueryParams({
                      ...queryParams,
                      searchKey: value,
                    });
                  }}
                  suffixIcon={<SearchIcon />}
                />
              </div>
            </div>
            <div>
              <List
                type={type}
                queryParams={queryParams}
                setQueryParams={setQueryParams}
                records={records}
                refresh={refresh}
                loading={loading}
                recordCount={recordCount}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
