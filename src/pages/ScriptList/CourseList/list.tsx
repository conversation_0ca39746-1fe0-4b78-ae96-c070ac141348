import React, { useEffect, useState, useMemo } from 'react';
import {
  Link,
  MessagePlugin,
  Popconfirm,
  PrimaryTableCol,
  Space,
  Table,
} from 'tdesign-react';
import './index.less';
import {
  IScriptListRequestParams,
  IScriptRecord,
} from '@/pages/ScriptList/hooks/useScriptListRequest';
import { getOriginByType, openEditor } from '@/pages/Editor/common/openEditor';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TemplateDialog } from '@/components/TemplateDialog';
import { MetaFeedbackSvr } from '@/pb/pb';
import moment from 'moment/moment';
import { EMPTY_TEMPLATE_ID, CONTENT_TYPE_MAP } from '@/const/common';

interface IProps {
  queryParams: IScriptListRequestParams;
  setQueryParams: (params: IScriptListRequestParams) => void;
  records: IScriptRecord[];
  recordCount: number;
  refresh: () => void;
  loading: boolean;
  type: string;
}

export function List(props: IProps) {
  const [visible, setVisible] = useState(false);
  const [selectRowKey, setSelectRowKey] = useState('');
  const {
    records,
    refresh,
    queryParams,
    setQueryParams,
    recordCount,
    loading,
    type,
  } = props;
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const createPath = useMemo(() => {
    if (type === CONTENT_TYPE_MAP.Video.children.Vision.value) {
      return '/script-list/vision/create';
    }

    return '/script-list/create';
  }, [type]);

  const [ScriptType, setScriptType] = useState('');
  const [application_scenarios, setApplication_scenarios] = useState('');

  useEffect(() => {
    const selectResearchId = searchParams.get('selectResearchId');
    const fromParam = searchParams.get('from');
    const [type, application_scenarios] = (fromParam || '').split('__');
    if (selectResearchId) {
      // 清掉参数
      navigate(`/script-list`);
      setVisible(true);
      setSelectRowKey(selectResearchId);
      setScriptType(type);
      setApplication_scenarios(application_scenarios);
    }
  }, [navigate, searchParams]);

  const handleDelete = async (row: IScriptRecord) => {
    const loading = await MessagePlugin.loading('删除中...');
    try {
      await MetaFeedbackSvr.DeleteResearch({
        research_id: row.research_id,
      });
      void MessagePlugin.success('脚本删除成功');
    } catch (error) {
      console.log(error);
      void MessagePlugin.error('删除失败');
    } finally {
      refresh();
      loading.close();
    }
  };

  const navigator = useNavigate();
  const columns: PrimaryTableCol<IScriptRecord>[] = [
    {
      colKey: 'research_id',
      title: 'ID',
    },
    {
      colKey: 'source',
      title: '来源',
      width: 130,
      cell: () => <>智能创建</>,
    },
    {
      colKey: 'research_name',
      title: '脚本名称',
    },
    {
      colKey: 'all_nodes_count',
      title: '脚本环节数',
      width: 130,
    },
    // {
    //   colKey: 'user_id',
    //   title: '脚本创建人',
    // },
    {
      colKey: 'create_time',
      title: '创建时间',
      cell: ({ row }) => (
        <>{moment(+row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</>
      ),
    },
    {
      colKey: 'operate',
      fixed: 'right',
      width: 180,
      title: '操作',
      cell: ({ row }) => (
        <Space>
          <Link
            theme="primary"
            hover="color"
            onClick={() => {
              setVisible(true);
              setSelectRowKey(row.research_id);
              setScriptType(row.type);
              setApplication_scenarios(row.application_scenarios);
            }}
          >
            创建视频
          </Link>
          <Link
            theme="primary"
            hover="color"
            onClick={() => {
              navigator(
                `${createPath}?_blank&research_id=${row.research_id}&from=${row.type}__${row.application_scenarios}`
              );
            }}
          >
            编辑
          </Link>
          <Popconfirm
            content="确认删除吗"
            destroyOnClose
            placement="top"
            showArrow
            theme="default"
            onConfirm={() => {
              handleDelete(row).then();
            }}
          >
            <Link theme="danger" hover="color">
              删除
            </Link>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  const contentType = useMemo(
    () =>
      ScriptType === 'video'
        ? CONTENT_TYPE_MAP.Video.value
        : CONTENT_TYPE_MAP.Live.value,
    [ScriptType]
  );
  return (
    <div className="script-record-comp">
      <Table
        rowKey="research_id"
        columns={columns}
        data={records}
        loading={loading}
        maxHeight="calc(100vh - 345px)"
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: recordCount,
        }}
        onPageChange={(pageInfo) => {
          setQueryParams({
            ...queryParams,
            // 切换每页条数时，重置页码
            pageNum:
              queryParams.pageSize === pageInfo.pageSize ? pageInfo.current : 1,
            pageSize: pageInfo.pageSize,
          });
        }}
        size="medium"
        verticalAlign="middle"
      />
      {visible && (
        <TemplateDialog
          type={ScriptType}
          visible={visible}
          application_scenarios={application_scenarios}
          onClose={() => setVisible(false)}
          onCreateWithTemplate={(template) => {
            if (!selectRowKey) {
              void MessagePlugin.error('未选择脚本');
              return;
            }
            openEditor({
              system: EditorSystemMap.META_HUMAN,
              contentType,
              origin: getOriginByType({
                contentType,
                application_scenarios,
              }),
              scriptId: selectRowKey,
              // templateType: 'course_video',
              templateId: template.id,
            });
            setVisible(false);
          }}
          onCreateWithoutTemplate={() => {
            openEditor({
              system: EditorSystemMap.META_HUMAN,
              contentType,
              origin: getOriginByType({
                contentType,
                application_scenarios,
              }),
              scriptId: selectRowKey,
              templateId: EMPTY_TEMPLATE_ID,
            });
            setVisible(false);
          }}
        />
      )}
    </div>
  );
}
