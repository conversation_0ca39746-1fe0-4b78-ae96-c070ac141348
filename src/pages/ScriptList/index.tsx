import React, { useEffect, useMemo, useState } from 'react';
import { Tabs } from 'tdesign-react';
import CourseList from '@/pages/ScriptList/CourseList';
import './index.less';
import { MainContent, Page } from '@/components/Layout';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { CONTENT_TYPE_MAP } from '@/const/common';
import { transformMap } from '../WorkbenchVideo/components/create/utils';

const { TabPanel } = Tabs;

const TAB_LIST = [
  {
    from: CONTENT_TYPE_MAP.Video.value,
    value: CONTENT_TYPE_MAP.Video.children.ECommerce.value,
    label:
      transformMap[CONTENT_TYPE_MAP.Video.value]?.[
        CONTENT_TYPE_MAP.Video.children.ECommerce.value
      ]?.selector_name,
  },
  {
    from: CONTENT_TYPE_MAP.Video.value,
    value: CONTENT_TYPE_MAP.Video.children.Vision.value,
    label:
      transformMap[CONTENT_TYPE_MAP.Video.value]?.[
        CONTENT_TYPE_MAP.Video.children.Vision.value
      ]?.selector_name,
  },
  {
    from: CONTENT_TYPE_MAP.Video.value,
    value: CONTENT_TYPE_MAP.Video.children.Course.value,
    label:
      transformMap[CONTENT_TYPE_MAP.Video.value]?.[
        CONTENT_TYPE_MAP.Video.children.Course.value
      ]?.selector_name,
  },
  {
    from: CONTENT_TYPE_MAP.Video.value,
    value: CONTENT_TYPE_MAP.Video.children.Article.value,
    label:
      transformMap[CONTENT_TYPE_MAP.Video.value]?.[
        CONTENT_TYPE_MAP.Video.children.Article.value
      ]?.selector_name,
  },
  // {
  //   from: CONTENT_TYPE_MAP.Live.value,
  //   value: CONTENT_TYPE_MAP.Live.children.Live.value,
  //   label:
  //     transformMap[CONTENT_TYPE_MAP.Live.value]?.[
  //       CONTENT_TYPE_MAP.Live.children.Live.value
  //     ]?.selector_name,
  // },
];

export default function ScriptList() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const sub_type = useMemo(() => searchParams.get('sub_type'), [searchParams]);
  const [value, setValue] = useState(sub_type || undefined);
  useEffect(() => {
    if (sub_type) {
      setValue(sub_type);
    }
  }, [sub_type]);
  const onChange = (key: string) => {
    setValue(key);
    searchParams.set('sub_type', key);
    const newSearch = searchParams.toString();
    navigate(
      { search: newSearch },
      {
        replace: true,
      }
    );
  };
  return (
    <Page title="脚本">
      <MainContent className="scriptList-mainContent">
        <Tabs onChange={(v) => onChange(v as string)} value={value}>
          {TAB_LIST.map((item) => {
            return (
              <TabPanel value={item.value} label={item.label} key={item.value}>
                <CourseList type={item.value} from={item.from} />
              </TabPanel>
            );
          })}
        </Tabs>
      </MainContent>
    </Page>
  );
}
