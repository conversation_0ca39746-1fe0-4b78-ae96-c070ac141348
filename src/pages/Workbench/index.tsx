import React from 'react';
import './index.less';
import create from './assets/create.png';
import entry from './assets/entry.png';
import titleBg from './assets/titleBg.png';
import textBg from './assets/textBg.png';
import liying from './assets/liying.png';
import { createConfigList, createPageTitle, entryConfigList } from './utils';
import { useNavigate } from 'react-router-dom';
import { Page } from '@/components/Layout';

const classPrefix = 'workbench-page';

function Workbench() {
  const navigate = useNavigate();
  return (
    <Page title="工作台" className="workbench-index">
      <div className={`${classPrefix}-contanier`}>
        <div className={`${classPrefix}-header`}>
          <div className="flex-1">
            {/* 标题背景 */}
            <img src={titleBg} alt="" className="titleBg" />
            {/* 标题 */}
            <img
              src={textBg}
              alt="智能化营销内容生成工具 助您轻松营销"
              className="textBg"
            />
          </div>
          <div className="flex-1 flex items-start justify-center ">
            <img src={liying} alt="" className="model" />
          </div>
        </div>
        <div className={`${classPrefix}-content`}>
          <div className={`${classPrefix}-content-box`}>
            {createPageTitle({ img: create, title: '开始创建' })}
            <div className="createConfig-warp">
              {createConfigList.map((item) => (
                <div
                  className="createConfigItem"
                  style={{ background: item.background }}
                  key={item.title}
                >
                  <div className="title-wrap">
                    <div className="title">{item.title}</div>
                    <div className="subTitle">{item.subTitle}</div>
                    <div
                      className={`btnWrap btnWrap-${item.type}`}
                      onClick={() => {
                        if (item.jumpUrl) {
                          navigate(item.jumpUrl);
                        }
                      }}
                    >
                      创建
                      <div className="arrowRight" />
                    </div>
                  </div>
                  <img
                    src={item.icon}
                    alt=""
                    className={`icon icon-${item.type}`}
                  />
                </div>
              ))}
            </div>
          </div>
          <div className={`${classPrefix}-content-box`}>
            {createPageTitle({ img: entry, title: '快捷入口' })}
            <div className="entryConfig-contanier">
              {entryConfigList.map((item) => (
                <div className="entryConfig-warp">
                  <div className="entryConfigTitle">{item.title}</div>
                  <div className="entryConfigList">
                    {Array.isArray(item.children) &&
                      item.children.map((child) => (
                        <div className="entryConfigItem">
                          <img src={child.icon} alt="" className="icon" />
                          <div
                            className={`title title-${child.type}`}
                            style={{ background: child.background }}
                            onClick={() => {
                              if (child.jumpUrl) {
                                navigate(child.jumpUrl);
                              }
                            }}
                          >
                            {child.title}
                            <div className="arrowRight" />
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Page>
  );
}

export default Workbench;
