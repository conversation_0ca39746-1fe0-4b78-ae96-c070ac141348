@headerHeight: 56px;
@macbook15WidthBreakPoint: 1440px;
@macbook13HeightBreakPoint: 800px - @headerHeight;
@macbook15HeightBreakPoint: 900px - @headerHeight;

.slot-wrap {
  &:has(.workbench-index) {
    padding: 0 !important;
  }
  .workbench-index.page {
    // 隐藏左上角“工作台”标题
    > div:first-child {
      display: none;
    }
  }
}

.workbench-page-contanier {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url(./assets/bg.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  background-attachment: fixed;
  overflow: auto;
  min-width: 1120px;


  // 图片不支持选中
  img {
    user-select: none;
  }

  @media screen and (max-width: @macbook15WidthBreakPoint) {
    min-width: 960px;
  }

  
  .workbench-page-header,
  .workbench-page-content {
    width: 1064px;
    @media screen and (max-width: @macbook15WidthBreakPoint) {
        width: 900px;
      }
  }


  .workbench-page-header {
    padding: 20px 0;
    height: 300px;
    flex-shrink: 0;
    display: flex;
    position: relative;
    justify-content: space-around;
    align-content: center;

    @media screen and (max-height: @macbook15HeightBreakPoint) {
      height: 220px;
    }

    .titleBg {
      position: absolute;
      left: 0;
      top: 50%;
      width: 417px;
      height: 146px;
      transform: translateY(-50%);
    }

    .textBg {
      position: absolute;
      width: 489px;
      height: 137px;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
    }

    .model {
      width: 294px;
    }
  }

  .workbench-page-content {
    position: relative;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    &-box {
      margin-bottom: 48px;
  
      @media screen and (max-height: @macbook15HeightBreakPoint) {
        margin-bottom: 40px;
      }

      @media screen and (max-width: @macbook15WidthBreakPoint) {
        margin-bottom: 32px;
      }
    }

    .pageTitle {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      line-height: 34px;

      img {
        width: 22px;
        height: 22px;
        margin-right: 6px;
      }
    }

    .createConfig-warp {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;

      .createConfigItem {
        width: calc(50% - 10px);
        height: 140px;
        border-radius: 8px;
        display: flex;
        position: relative;
        transition: all 0.2s cubic-bezier(0.01, 0.32, 0.58, 1);

        .icon {
          //width: 208px;
          height: 134px;
          position: absolute;
          right: 6px;
          top: 6px;
        }

        .icon-live {
          // width: 226px;
          height: 154px;
          top: -13px;
          right: 0px;
        }

        .title-wrap {
          margin-left: 24px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          color: #ffffff;

          .title {
            font-family: PingFang SC;
            font-size: 24px;
            font-weight: 500;
            line-height: 33.6px;

          }

          .subTitle {
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 400;
            line-height: 22px;
            color: rgba(255, 255, 255, 0.8);
          }

          .btnWrap {
            width: 80px;
            height: 32px;
            background: linear-gradient(97.99deg, #EBF4FF 12.3%, #F8F8FF 99.99%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 400;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.9);
            cursor: pointer;
            margin-top: 16px;

            .arrowRight {
              width: 16px;
              height: 16px;
              margin-left: 8px;
              background-size: 16px 16px;
              background-repeat: no-repeat;
              background-position: center;
              background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.4603 12.4595L5.54106 11.5402L9.08144 7.99985L5.54106 4.45947L6.4603 3.54023L10.9199 7.99985L6.4603 12.4595Z" fill="black" fill-opacity="0.4" style="fill:black;fill-opacity:0.4;"/></svg>');
              transition: all 0.2s cubic-bezier(0.01, 0.32, 0.58, 1);
            }
          }

          // .btnWrap-video {
          //   &:hover {
          //     color: rgba(0, 71, 249, 1);

          //     .arrowRight {
          //       background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity="0.6"><path d="M6.4603 12.4595L5.54106 11.5402L9.08144 7.99985L5.54106 4.45947L6.4603 3.54023L10.9199 7.99985L6.4603 12.4595Z" fill="%230047F9" style="fill:%230047F9;fill:color(display-p3 0.0000 0.2784 0.9765);fill-opacity:1;"/></g></svg>');
          //     }
          //   }
          // }

          // .btnWrap-live {
          //   &:hover {
          //     color: rgba(89, 39, 214, 1);

          //     .arrowRight {
          //       background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity="0.6"><path d="M6.4603 12.4556L5.54106 11.5363L9.08144 7.99595L5.54106 4.45557L6.4603 3.53633L10.9199 7.99595L6.4603 12.4556Z" fill="%235927D6" style="fill:%235927D6;fill:color(display-p3 0.3505 0.1526 0.8389);fill-opacity:1;"/></g></svg>');
          //     }
          //   }
          // }
        }
        &:hover {
            transform: translateY(-4px);
            box-shadow: 0px 8px 16px 0px rgba(227, 227, 227, 1);

            .arrowRight {
              transform: scale(1.2);
            }
        }
        
      }
    }

    .entryConfig-contanier {
      display: flex;

      .entryConfig-warp {
        margin-top: 22px;
        margin-right: 24px;

        @media screen and (max-width: @macbook15WidthBreakPoint) {
        margin-top: 16px;
          }

        .entryConfigTitle {
          margin-bottom: 10px;
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: 400;
          line-height: 22.4px;
          color: rgba(0, 0, 0, 0.6);

        }

        .entryConfigList {
          display: flex;
          flex-wrap: wrap;

          .entryConfigItem {
            width: 160px;
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
            margin-right: 16px;
            border-radius: 8px;
            position: relative;
            transition: all 0.2s cubic-bezier(0.01, 0.32, 0.58, 1);

            @media screen and (max-width: @macbook15WidthBreakPoint) {
                width: 150px;
              }


            &:hover {
                transform: translateY(-4px);
                box-shadow: 0px 4px 8px 0px rgba(227, 227, 227, 0.56);

                .arrowRight {
                  transform: scale(1.4);
                }
            }
            .icon {
              width: 100%;
            }

            .title {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              height: 52px;
              font-size: 16px;
              font-weight: 400;
              line-height: 24px;
              box-sizing: border-box;
              padding: 0px 16px;
              border-radius: 0px 0px 8px 8px;
              cursor: pointer;

              @media screen and (max-width: @macbook15WidthBreakPoint),
              screen and (max-height: @macbook13HeightBreakPoint) {
                  height: 42px;
                  font-size: 15px;
                }

              .arrowRight {
                width: 16px;
                height: 16px;
                margin-left: 8px;
                background-size: 16px 16px;
                background-repeat: no-repeat;
                background-position: center;
                background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.4603 12.4595L5.54106 11.5402L9.08144 7.99985L5.54106 4.45947L6.4603 3.54023L10.9199 7.99985L6.4603 12.4595Z" fill="black" fill-opacity="0.4" style="fill:black;fill-opacity:0.4;"/></svg>');
                transition: all 0.2s cubic-bezier(0.01, 0.32, 0.58, 1);
              }
            }
            // .title-script,
            // .title-img {
            //   &:hover {
            //     color: rgba(0, 71, 249, 1);

            //     .arrowRight {
            //       background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.4603 12.4595L5.54106 11.5402L9.08144 7.99985L5.54106 4.45947L6.4603 3.54023L10.9199 7.99985L6.4603 12.4595Z" fill="%230047F9" fill-opacity="0.6" style="fill:%230047F9;fill:color(display-p3 0.0000 0.2784 0.9765);fill-opacity:0.6;"/></svg>');
            //     }
            //   }
            // }
            // .title-video,
            // .title-live,
            // .title-human,
            // .title-audio {
            //   &:hover {
            //     color: rgba(89, 39, 214, 1);

            //     .arrowRight {
            //       background-image: url('data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity="0.6"><path d="M6.4603 12.4556L5.54106 11.5363L9.08144 7.99595L5.54106 4.45557L6.4603 3.53633L10.9199 7.99595L6.4603 12.4556Z" fill="%235927D6" style="fill:%235927D6;fill:color(display-p3 0.3505 0.1526 0.8389);fill-opacity:1;"/></g></svg>');
            //     }
            //   }
            // }
          }
        }
      }
    }
  }

}