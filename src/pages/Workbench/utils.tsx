import video from './assets/video.png';
import live from './assets/live.png';
import union1 from './assets/union1.png';
import union2 from './assets/union2.png';
import union3 from './assets/union3.png';
import union4 from './assets/union4.png';
import union5 from './assets/union5.png';
import union6 from './assets/union6.png';
import { CONTETNT_TYPE } from './constants';

export const createPageTitle = ({
  img,
  title,
  className,
}: {
  img: string;
  title: string;
  className?: string;
}) => (
  <div className={`pageTitle ${className ?? ''}`}>
    <img src={img} alt="" />
    {title}
  </div>
);

export const createConfigList = [
  {
    icon: video,
    type: CONTETNT_TYPE.VIDEO,
    title: '视频',
    subTitle: '短视频、教学视频、资讯视频等',
    background:
      ' linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #725CFF 99.99%)',
    jumpUrl: `/workbench-create?contentType=${CONTETNT_TYPE.VIDEO}`,
  },
  {
    icon: live,
    type: CONTETNT_TYPE.LIVE,
    title: '直播间',
    subTitle: '游戏直播、电商直播等',
    background:
      'linear-gradient(88.08deg, #8842FE -0.01%, #8C5BFF 49.89%, #4952FF 99.99%)',
    jumpUrl: `/workbench-create?contentType=${CONTETNT_TYPE.LIVE}`,
  },
];

export const entryConfigList = [
  {
    title: '素材生成工具',
    children: [
      {
        type: 'script',
        icon: union1,
        title: '智能生成脚本',
        background: 'linear-gradient(97.99deg, #EBF4FF 12.3%, #F8F8FF 99.99%)',
        jumpUrl: '/script-list?sub_type=2',
      },
      // {
      //   type: 'img',
      //   icon: union2,
      //   title: '智能生成图片',
      //   background: 'linear-gradient(97.99deg, #EBF4FF 12.3%, #F8F8FF 99.99%)',
      //   jumpUrl: '',
      // },
    ],
  },
  {
    title: '内容资产管理',
    children: [
      {
        type: 'video',
        icon: union3,
        title: '视频管理',
        background:
          'linear-gradient(102.69deg, #E3DCFF -24.99%, #F8F8FF 99.99%)',
        jumpUrl: '/video-list',
      },
      {
        type: 'live',
        icon: union4,
        title: '直播间管理',
        background:
          'linear-gradient(102.69deg, #E3DCFF -24.99%, #F8F8FF 99.99%)',

        jumpUrl: '/live-list',
      },
      {
        type: 'human',
        icon: union5,
        title: '数字人管理',
        background:
          'linear-gradient(102.69deg, #E3DCFF -24.99%, #F8F8FF 99.99%)',
        jumpUrl: '/virtual-image',
      },
      {
        type: 'audio',
        icon: union6,
        title: '音色管理',
        background:
          'linear-gradient(102.69deg, #E3DCFF -24.99%, #F8F8FF 99.99%)',
        jumpUrl: '/voice-list',
      },
    ],
  },
];
