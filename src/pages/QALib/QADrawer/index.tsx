/**
 * <AUTHOR>
 * @date 2024/5/19 上午10:18
 * @desc index
 */

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Form, MessagePlugin, Textarea } from 'tdesign-react';
import { CloseCircleFilledIcon, AddIcon } from 'tdesign-icons-react';
import './index.less';
import { IntelligentInput } from '@/pages/QALib/IntelligentInput';
import { Development } from '@/pb/pb';
import { QA_GROUP_TYPE } from '@/pages/QALib/hooks/useGroupRequest';

const { FormItem, FormList } = Form;

interface IProps {
  groupId: string;
  onClose: () => void;
  initValue?: QAItem;
}

export interface QAItem {
  question: string;
  answer: string;
  id: string;
}

export function QADrawer(props: IProps) {
  const { initValue, groupId, onClose } = props;
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  useEffect(() => {
    if (initValue) {
      //   编辑
      form.setFieldsValue({
        qaList: [initValue],
      });
    } else {
      // 新建
      form.setFieldsValue({
        qaList: [
          {
            question: '',
            answer: '',
          },
        ],
      });
    }
  }, [form, initValue]);

  const handleSubmit = async () => {
    const values = form.getFieldValue('qaList') as QAItem[];
    console.debug('values', values);
    try {
      setLoading(true);
      if (initValue) {
        const { question, answer } = values[0];
        await Development.UpdateQaContent({
          question,
          answer,
          qa_content_id: initValue.id,
          group_type: QA_GROUP_TYPE,
          group_id: groupId,
        });
        void MessagePlugin.success({ content: '修改成功' });
        onClose();
      } else {
        //   使用Promise.allSettled 保证所有请求都会执行, 调用Development.CreateQaContentId接口
        // await Promise.all(
        //   values.map((item) =>
        //     Development.CreateQaContentId({
        //       group_type: QA_GROUP_TYPE,
        //       group_id: props.groupId,
        //       question: item.question,
        //       answer: item.answer,
        //     })
        //   )
        // );
        const params = values.map((item) => {
          return {
            question: item.question,
            answer: item.answer,
            group_type: QA_GROUP_TYPE,
            group_id: props.groupId,
          };
        });
        await Development.CreateMultiQaContentId({
          group_id: props.groupId,
          group_type: QA_GROUP_TYPE,
          qa_content_object_list: params,
        });
        void MessagePlugin.success({ content: '新增成功' });
        onClose();
      }
    } catch (error) {
      console.error(error, '保存失败');
      void MessagePlugin.error('创建失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <Drawer
        size="500px"
        header={initValue ? '修改问答' : '新增问答'}
        visible
        onClose={onClose}
        confirmBtn={
          <span className="pagedoo-meta-live-global">
            <Button
              className="gradient-primary"
              loading={loading}
              onClick={() => {
                void handleSubmit();
              }}
            >
              确认{initValue ? '修改' : '新增'}
            </Button>
          </span>
        }
        cancelBtn={
          <span className="pagedoo-meta-live-global ml-8">
            <Button
              loading={loading}
              className="gradient-default"
              theme="default"
              onClick={onClose}
            >
              取消
            </Button>
          </span>
        }
      >
        <Form
          form={form}
          labelAlign="left"
          labelWidth={48}
          className="qa-lib-drawer-comp"
        >
          <FormList name="qaList">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name }) => (
                  <div className="card" key={key}>
                    <div className="card-header">
                      <div style={{ flex: 1 }}>问答{name + 1}</div>
                      {fields.length > 1 && (
                        <CloseCircleFilledIcon
                          style={{
                            color: 'rgba(255, 94, 85, 1)',
                            cursor: 'pointer',
                          }}
                          onClick={() => remove(name)}
                        />
                      )}
                    </div>
                    <div className="card-body">
                      <FormItem key={key}>
                        <div className="my-form-item">
                          <FormItem name={[name, 'question']} label="问题">
                            <Textarea
                              placeholder="请输入问题..."
                              autosize={{ minRows: 2, maxRows: 4 }}
                              style={{ width: '100%' }}
                            />
                          </FormItem>
                        </div>

                        <div className="my-form-item">
                          {/* shouldUpdate拿到的prev和next有点问题*/}
                          <FormItem shouldUpdate>
                            {({ getFieldValue }) => {
                              const questionInput =
                                (getFieldValue('qaList') as QAItem[])?.[name]
                                  ?.question || '';
                              return (
                                <FormItem name={[name, 'answer']} label="回答">
                                  <IntelligentInput
                                    questionInput={questionInput}
                                  />
                                </FormItem>
                              );
                            }}
                          </FormItem>
                        </div>
                      </FormItem>
                    </div>
                  </div>
                ))}
                {!initValue && (
                  <FormItem>
                    <div
                      className="flex items-center py-12"
                      style={{ width: '100%', borderTop: '1px solid #E7E7E7' }}
                    >
                      <Button
                        className="add-qa-btn"
                        theme="default"
                        icon={<AddIcon />}
                        onClick={() => add({ question: '', answer: '' })}
                      >
                        添加回答
                      </Button>
                    </div>
                  </FormItem>
                )}
              </>
            )}
          </FormList>
        </Form>
      </Drawer>
    </>
  );
}
