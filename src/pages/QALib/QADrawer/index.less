.qa-lib-drawer-comp {
  .card {
    &-header {
      display: flex;
      align-items: center;
      padding: 0 16px;
      border-bottom: 1px solid #E9E7EF;
      height: 36px;
      color: rgba(0, 0, 0, 0.90);
      font-weight: 500;
    }

    &-body {
      padding: 16px;

      .my-form-item {
        width: 100%;
      }

      .t-form__controls-content {
        flex-wrap: wrap;
        gap: 8px;
      }

      .t-form__item {
        width: 100%;
      }

      .t-form__label {
        color: rgba(0, 0, 0, 0.40);
      }

      .custom-input-area {
        height: 225px;
        padding: 6px 8px;
        border-radius: 4px;
        border: 1px solid #F4F6FF;
      }
    }

    margin-bottom: 12px;
    width: 466px;
    //height: 367px;
    border-radius: 4px;
    background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);
  }

  .add-qa-btn {
    //width: 466px;
    width: 100%;
    border: none;
    height: 32px;
    border-radius: 4px;
    background: linear-gradient(88deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);

    &:active {
      .t-ripple { 
        opacity: 0.2;
      }
    }
  }
}
