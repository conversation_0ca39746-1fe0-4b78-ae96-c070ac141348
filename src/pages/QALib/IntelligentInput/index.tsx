/**
 * <AUTHOR>
 * @date 2024/5/19 下午2:34
 * @desc index
 */

import LoadingGif from '@/assets/images/idooLoading.gif';
import { AudioRecognize } from '@/pages/QALib/components/AudioRecognize/AudioRecognize';
import { useRef, useState } from 'react';
import { UserTalk1Icon } from 'tdesign-icons-react';
import { Textarea } from 'tdesign-react';
import { TextareaRefInterface } from 'tdesign-react/es/textarea/Textarea';
import './index.less';
import { IntelligentRecoveryBtn } from '@/pages/QALib/components/IntelligentRecoveryBtn';

interface IProps {
  value?: string;
  onChange?: (value: string) => void;
  questionInput?: string;
}

export function IntelligentInput(props: IProps) {
  const { value, onChange, questionInput } = props;

  const [intelligentLoading, setIntelligentLoading] = useState(false);

  const [voiceInputActive, setVoiceInputActive] = useState(false);

  // 语音识别状态

  const textareaRef = useRef<TextareaRefInterface>();

  const handleVoiceInput = async () => {
    //   这里处理语音输入
    setVoiceInputActive(true);
  };

  const handleVoiceStop = async () => {
    setVoiceInputActive(false);
    textareaRef?.current?.textareaElement?.blur();
  };

  return (
    <div className="qa-intelligent-input-comp">
      {!voiceInputActive ? (
        <div className="qa-intelligent-input-comp-header">
          <IntelligentRecoveryBtn
            question={questionInput || ''}
            onSuccess={(value) => {
              onChange?.(value);
            }}
            setLoading={setIntelligentLoading}
          />
          <span onClick={handleVoiceInput}>
            <UserTalk1Icon />
            语音输入
          </span>
        </div>
      ) : null}
      {/* 语音输入展示 */}
      {voiceInputActive ? (
        <AudioRecognize
          onStart={() => textareaRef?.current?.textareaElement?.focus()}
          onStop={handleVoiceStop}
          onTextChange={(text) => {
            textareaRef?.current?.textareaElement?.focus();
            console.log('当前value ', value);
            onChange?.(value + text);
          }}
        />
      ) : null}

      <div className="qa-intelligent-input-comp-content">
        <Textarea
          placeholder="请输入回答..."
          autosize={{ minRows: 8, maxRows: 8 }}
          style={{ width: '100%', height: '100%' }}
          value={value}
          onChange={(value) => {
            onChange?.(value);
          }}
          ref={textareaRef}
          onBlur={() => {
            if (voiceInputActive) textareaRef?.current?.textareaElement.focus();
          }}
        />
      </div>

      {intelligentLoading && (
        <div className="qa-intelligent-input-comp-loading">
          <img width={60} height={44} src={LoadingGif} alt="" />
          <span>正在为您生成答案...</span>
        </div>
      )}
    </div>
  );
}
