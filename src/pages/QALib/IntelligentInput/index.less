.qa-intelligent-input-comp {
  position: relative;
  width: 390px;
  height: 225px;
  border-radius: 4px;
  border: 1px solid #E9E7EF;
  background-color: #FFFFFF;

  &-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 12px;
    height: 28px;
    border-bottom: 1px solid #E9E7EF;
    gap: 0 16px;

    span {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      color: #0047F9;
    }
  }

  &-voice-processing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    height: 28px;
    border-bottom: 1px solid #E9E7EF;
    gap: 0 16px;

    span {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
    }
    .stop-voice {
      color: #0047F9;
    }
  }


  &-content {
    .t-textarea__inner {
      border: none;
    }

    .t-textarea__inner:focus {
      border: none;
      box-shadow: none;
    }
  }

  &-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-content: center;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    cursor: not-allowed;
    z-index: 3;
  }
}