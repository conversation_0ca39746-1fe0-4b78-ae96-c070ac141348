/**
 * <AUTHOR>
 * @date 2024/5/19 上午10:10
 * @desc index
 */

import React, { useCallback, useState } from 'react';
import { QADrawer, QAItem } from '@/pages/QALib/QADrawer';
import './index.less';
import { QAGroup } from '@/pages/QALib/Group';
import { Button, Input, MessagePlugin } from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import { QARecord } from '@/pages/QALib/QARecord';
import { useQAListRequest } from '@/pages/QALib/hooks/useQAListRequest';
import { Page, MainContent } from '@/components/Layout';

const classPrefix = 'qa-lib-page';

export default function QALib() {
  const [visible, setVisible] = useState(false);
  const [searchLibVal, setSearchLibVal] = useState('');
  const [searchQuestionVal, setSearchQuestionVal] = useState('');
  const [selectGroup, setSelectGroup] = useState('');

  const [initEditValue, setInitEditValue] = useState<QAItem>();

  const {
    queryParams,
    setQueryParams,
    records,
    refresh,
    recordCount,
    loading,
  } = useQAListRequest({
    queryParamsDefault: {
      searchKey: searchQuestionVal,
      groupId: selectGroup,
      pageNum: 1,
      pageSize: 10,
    },
  });

  const handleSelectGroup = useCallback(
    (groupId: string) => {
      setSelectGroup(groupId);
      setQueryParams({
        ...queryParams,
        pageNum: 1,
        groupId,
      });
    },
    [queryParams, setQueryParams]
  );

  const handleCreateQAItem = () => {
    if (!selectGroup) {
      void MessagePlugin.warning('请先选择/创建问题库');
      return;
    }
    setInitEditValue(undefined);
    setVisible(true);
  };

  return (
    <Page title="问答库管理" className={classPrefix}>
      <MainContent className={`${classPrefix}-content`}>
        <div className={`${classPrefix}-content-inner-box`}>
          <div className="left">
            <div className="search-box">
              <Input
                value={searchLibVal}
                placeholder="输入问题库名称"
                onChange={(value) => {
                  setSearchLibVal(value);
                }}
                suffixIcon={<SearchIcon />}
              />
            </div>
            <QAGroup
              searchName={searchLibVal}
              selectGroup={selectGroup}
              onSelectGroup={(groupId) => {
                handleSelectGroup(groupId);
              }}
            />
          </div>
          <div className="right flex flex-col">
            <div className="operate-box">
              <div className="flex-1">
                <Button
                  theme="primary"
                  className="gradient-primary"
                  onClick={() => {
                    handleCreateQAItem();
                  }}
                >
                  新建问答
                </Button>
              </div>

              <div style={{ width: '364px' }}>
                <Input
                  value={searchQuestionVal}
                  placeholder="请输入你需要搜索的内容"
                  onChange={(value) => {
                    setSearchQuestionVal(value);
                  }}
                  onEnter={(value) => {
                    setQueryParams({
                      ...queryParams,
                      searchKey: value,
                    });
                  }}
                  suffixIcon={<SearchIcon />}
                />
              </div>
            </div>
            <div className="h-0 flex-1">
              <QARecord
                className="h-full"
                groupId={selectGroup}
                queryParams={queryParams}
                setQueryParams={setQueryParams}
                records={records}
                refresh={refresh}
                loading={loading}
                recordCount={recordCount}
                onEdit={(record) => {
                  setInitEditValue({
                    question: record.question,
                    answer: record.answer,
                    id: record.qa_content_id,
                  });
                  setVisible(true);
                }}
              />
            </div>
          </div>
        </div>
      </MainContent>
      {visible && (
        <QADrawer
          initValue={initEditValue}
          onClose={() => {
            refresh();
            setVisible(false);
          }}
          groupId={selectGroup}
        />
      )}
    </Page>
    // <div className={classPrefix}>
    //   <div className={`${classPrefix}-title`}>问答库管理</div>
    //   <div className={`${classPrefix}-content`}>

    //   </div>
    // </div>
  );
}
