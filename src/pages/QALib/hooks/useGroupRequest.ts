/**
 * <AUTHOR>
 * @date 2024/5/21 16:24
 * @desc useGroupRequest
 */

import { useCallback, useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';
import { RespType } from '@/pb/config';
import { Development } from '@/pb/pb';

export const QA_GROUP_TYPE = 1;

export interface IQAListRequestParams {
  groupName?: string;
  groupType: number;
}

export type IGroupData = RespType<
  typeof Development.GetUserGroupList
>['records'][number];

export const useGroupRequest = ({
  queryParamsDefault,
}: {
  queryParamsDefault: IQAListRequestParams;
}) => {
  const [queryParams, setQueryParams] =
    useState<IQAListRequestParams>(queryParamsDefault);
  const [records, setRecords] = useState<IGroupData[]>([]);
  const { runAsync, loading, error } = useRequest(
    () => {
      return Development.GetUserGroupList(
        {
          group_name: queryParams?.groupName || '',
          group_type: queryParams.groupType,
        }
        // true
      );
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    // 请求
    runAsync()
      .then((res) => {
        setRecords(res.records || []);
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        setRecords([]);
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
      });
  }, [runAsync, queryParams]);

  const refresh = useCallback(() => {
    setQueryParams({ ...queryParams });
  }, [queryParams]);

  return {
    records,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
  };
};
