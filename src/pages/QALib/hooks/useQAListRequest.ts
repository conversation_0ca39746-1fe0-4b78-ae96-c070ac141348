/**
 * <AUTHOR>
 * @date 2024/5/21 15:24
 * @desc useQAListRequest
 */

import { useCallback, useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';
import { RespType } from '@/pb/config';
import { Development } from '@/pb/pb';
import { QA_GROUP_TYPE } from '@/pages/QALib/hooks/useGroupRequest';

export interface IQAListRequestParams {
  searchKey?: string;
  groupId: string;
  groupType?: string;
  // 分页参数
  pageSize?: number;
  pageNum?: number;
}

export type IQARecord = RespType<
  typeof Development.SearchQaContentList
>['records'][number];

export const useQAListRequest = ({
  queryParamsDefault,
}: {
  queryParamsDefault: IQAListRequestParams;
}) => {
  const [queryParams, setQueryParams] =
    useState<IQAListRequestParams>(queryParamsDefault);
  const [records, setRecords] = useState<IQARecord[]>([]);
  const [recordCount, setRecordCount] = useState(0);
  const { runAsync, loading, error } = useRequest(
    () => {
      return Development.SearchQaContentList({
        group_type: QA_GROUP_TYPE,
        search_key: queryParams?.searchKey || '',
        group_id: queryParams.groupId || '',
        page_num: queryParams?.pageNum || 0,
        page_size: queryParams?.pageSize || 10,
      });
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    if (!queryParams.groupId) return;
    // 请求
    runAsync()
      .then((res) => {
        setRecords(res.records || []);
        setRecordCount(res.query_count || 0);
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        setRecords([]);
        setRecordCount(0);
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
      });
  }, [runAsync, queryParams]);

  const refresh = useCallback(() => {
    setQueryParams({ ...queryParams });
  }, [queryParams]);

  return {
    records,
    recordCount,
    loading,
    error,
    queryParams,
    setQueryParams,
    refresh,
    run: runAsync,
  };
};
