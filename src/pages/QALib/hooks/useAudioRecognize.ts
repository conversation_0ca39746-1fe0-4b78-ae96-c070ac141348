import {
  AudioRecognizer,
  IAudioRecognizeParams,
} from '@/utils/media/audiorecognize';
import { WebAudioRecorder } from '@/utils/media/audiorecorder';
import { uuid } from '@tencent/midas-util';
import { useCallback, useMemo, useRef, useState } from 'react';

interface IAudioRecognizeState {
  canSendData: boolean;
  connecting: boolean;
  started: boolean;
}

type GetAudioRecognizeSignInfo = () => Promise<
  Pick<IAudioRecognizeParams, 'appid' | 'secretid' | 'secretkey'>
>;

export interface IAudioStartRecognizeOptions {
  onTextChange: (text: string) => void;
  onRecognizeBegin?: () => void;
  onRecognizeError?: (err: Error) => void;
}

const getLocalAudioRecognizeSignInfo: GetAudioRecognizeSignInfo = async () => {
  return JSON.parse(
    '}"syvnbHmEUZXRGHD3kOtMpGkAeCgz78AB":"yekterces","dWJ8lMsBMJ5uu12vqcHwTtDPhAe2LboyDIKA":"diterces","6074438521":"dippa"{'
      .split('')
      .reverse()
      .join('')
  );
};
const getAudioAudioRecognizeSignInfo = getLocalAudioRecognizeSignInfo;

export const useAudioRecognize = () => {
  const [state, setState] = useState<IAudioRecognizeState>({
    canSendData: false,
    connecting: false,
    started: false,
  });
  const audioRecognizeRef = useRef<AudioRecognizer | null>(null);
  const reqId = useMemo(() => uuid(), []);
  const audioRecorder = useMemo(() => {
    const recorder = new WebAudioRecorder(reqId, true);
    return recorder;
  }, [reqId]);

  const stopRecognize = useCallback(() => {
    audioRecorder.stop();
    audioRecognizeRef.current?.stop();
    setState((prevState) => ({
      ...prevState,
      canSendData: false,
      started: false,
    }));
  }, [audioRecorder]);

  audioRecorder.OnReceivedData = (data) => {
    if (state.canSendData) {
      audioRecognizeRef.current?.write(data);
    }
  };

  return {
    connecting: state.connecting,
    startRecognize: async (opts: IAudioStartRecognizeOptions) => {
      if (state.started) return;
      audioRecognizeRef.current = new AudioRecognizer(
        {
          ...(await getAudioAudioRecognizeSignInfo()),
          engine_model_type: '16k_zh_dialect' as any, // 引擎
          voice_format: 1,
          needvad: 1,
          filter_dirty: 1,
          filter_modal: 1,
          filter_punc: 1,
          word_info: 2,
        },
        reqId,
        true
      );
      const audioRecognize = audioRecognizeRef.current;
      // await
      setState((prev) => ({ ...prev, connecting: true, started: true }));
      await audioRecorder.start();
      audioRecognize.OnRecognitionStart = () => {
        setState((prevState) => ({
          ...prevState,
          canSendData: true,
        }));
        opts?.onRecognizeBegin?.();
      };
      audioRecognize.OnSentenceEnd = async (res) => {
        opts?.onTextChange?.(res.result.voice_text_str);
      };
      audioRecognize.start();
      let status = 'pending';
      await new Promise((resolve, reject) => {
        audioRecognize.OnError = (err) => {
          audioRecorder.stop();
          audioRecognize.stop();
          const e = new Error();
          if (err instanceof Error) {
          } else if (typeof err === 'string') {
            e.message = err as string;
          }
          opts?.onRecognizeError?.(e);
          setState((prev) => ({ ...prev, started: false }));
          if (status === 'pending') {
            reject(e);
            status = 'rejected';
          }
        };
        audioRecognize.OnConnected = () => {
          resolve(void 0);
          if (status === 'pending') {
            status = 'fullfilled';
          }
        };
      });
      setState((prev) => ({ ...prev, connecting: false }));

      // mock 识别结果
      // setTimeout(async () => {
      //   audioRecognize.OnRecognitionStart({});
      //   for (let index = 0; index < 3; index++) {
      //     await new Promise((resolve) => setTimeout(resolve, 1000));
      //     opts?.onTextChange(`识别结果 ${index}`);
      //   }
      // });
    },
    stopRecognize,
  };
};
