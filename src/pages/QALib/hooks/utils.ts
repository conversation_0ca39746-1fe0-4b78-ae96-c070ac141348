/**
 * <AUTHOR>
 * @date 2024/5/29 下午3:26
 * @desc utils
 */
import { Development } from '@/pb/pb';
import { QA_GROUP_TYPE } from '@/pages/QALib/hooks/useGroupRequest';

export const addIndependentQALib = async (
  groupId: string,
  qaList: { question: string; answer: string }[]
) => {
  // const res = await Development.CreateUserGroup({
  //   group_name: groupName,
  //   group_type: QA_GROUP_TYPE,
  // });
  // if (!res.group_id) {
  //   void MessagePlugin.error('创建问答库失败');
  // }
  const params = qaList.map((item) => {
    return {
      question: item.question,
      answer: item.answer,
      group_type: QA_GROUP_TYPE,
      group_id: groupId,
    };
  });
  await Development.CreateMultiQaContentId({
    group_type: QA_GROUP_TYPE,
    group_id: groupId,
    is_force_coverage: true,
    qa_content_object_list: params,
  });
};
