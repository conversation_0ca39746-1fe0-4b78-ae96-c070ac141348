@import "../../components/Style/mixins/_scrollbar.less";
.qa-lib-page {

  &-title {
    color: rgba(0, 0, 0, 0.90);
    font-style: normal;
    font-weight: 600;
    margin-bottom: 16px;
  }

  &-content {
    padding: 20px 16px;
    border-radius: 8px;
    background: #FFFFFF;
    height: calc(100vh - 120px);

    &-inner-box {
      display: flex;
      border: 1px solid #E3E5EB;
      border-radius: 4px;
      height: 100%;
      overflow-x: auto;
      .scrollbar();

      .left {
        flex-basis: 180px;
        flex-shrink: 0;
        height: 100%;
        border-right: 1px solid #E3E5EB;

        .search-box {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 54px;
          border-bottom: 1px solid #E3E5EB;

          .t-input {
            border: none;
            box-shadow: none;
          }
        }
      }

      .right {
        flex: 1;
        // width: 0;
        flex-basis: 600px;
        flex-shrink: 0;

        // basis-[600px] shrink-0
        .operate-box {
          width: 100%;
          height: 54px;
          display: flex;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #E3E5EB;
        }
      }
    }
  }
}
