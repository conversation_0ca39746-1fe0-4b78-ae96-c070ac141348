/**
 * <AUTHOR>
 * @date 2024/5/20 下午7:25
 * @desc index
 */

import React from 'react';
import {
  Link,
  MessagePlugin,
  Popconfirm,
  PrimaryTableCol,
  Space,
  Table,
} from 'tdesign-react';
import './index.less';
import {
  IQAListRequestParams,
  IQARecord,
} from '@/pages/QALib/hooks/useQAListRequest';
import { Development } from '@/pb/pb';
import { QA_GROUP_TYPE } from '@/pages/QALib/hooks/useGroupRequest';

interface IProps {
  groupId: string;
  queryParams: IQAListRequestParams;
  setQueryParams: (params: IQAListRequestParams) => void;
  records: IQARecord[];
  recordCount: number;
  refresh: () => void;
  loading: boolean;
  onEdit: (record: IQARecord) => void;
  className?: string;
}

export function QARecord(props: IProps) {
  const {
    records,
    onEdit,
    refresh,
    queryParams,
    setQueryParams,
    recordCount,
    loading,
    groupId,
    className,
  } = props;

  const deleteRecord = async (record: IQARecord) => {
    try {
      await Development.DeleteQaByContentId({
        qa_content_id: record.qa_content_id,
        group_type: QA_GROUP_TYPE,
        group_id: groupId,
      });
      void MessagePlugin.success('删除成功');
      refresh();
    } catch (error) {
      console.error(error, '删除失败');
      void MessagePlugin.error('删除失败');
    } finally {
      refresh();
    }
  };

  const columns: PrimaryTableCol<IQARecord>[] = [
    {
      colKey: 'index',
      title: '序号',
      width: 80,
      cell: ({ rowIndex }) => <div>{rowIndex + 1}</div>,
    },
    {
      colKey: 'question',
      title: '问题',
      width: 240,
    },
    {
      colKey: 'answer',
      title: '答案',
    },
    {
      colKey: 'operate',
      fixed: 'right',
      width: 180,
      title: '操作',
      cell: ({ row }) => (
        <Space>
          <Link
            theme="primary"
            hover="color"
            onClick={() => {
              onEdit(row);
            }}
          >
            修改
          </Link>
          <Popconfirm
            content="确认删除吗"
            destroyOnClose
            placement="top"
            showArrow
            theme="default"
            onConfirm={() => {
              void deleteRecord(row);
            }}
          >
            <Link theme="danger" hover="color">
              删除
            </Link>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  return (
    <div className={`qa-record-comp ${className}`}>
      <Table
        rowKey="qa_content_id"
        columns={columns}
        data={records}
        loading={loading}
        maxHeight="calc(100vh - 280px)"
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: recordCount,
        }}
        onPageChange={(pageInfo) => {
          setQueryParams({
            ...queryParams,
            // 切换每页条数时，重置页码
            pageNum:
              queryParams.pageSize === pageInfo.pageSize ? pageInfo.current : 1,
            pageSize: pageInfo.pageSize,
          });
        }}
        size="medium"
        verticalAlign="middle"
      />
    </div>
  );
}
