/**
 * <AUTHOR>
 * @date 2024/4/4 14:19
 * @desc 分类操作弹窗
 */

import React, { useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Space,
  FormRules,
  MessagePlugin,
} from 'tdesign-react';
import { Development } from '@/pb/pb';
import { QA_GROUP_TYPE } from '@/pages/QALib/hooks/useGroupRequest';

const { FormItem } = Form;

interface IProps {
  // 操作类型
  type: 'add' | 'edit';
  groupId?: string;
  onClose: () => void;
  refresh?: () => void;
  initGroupName?: string;
}

interface IFormValues {
  groupName: string;
}

export function FormDialog(props: IProps) {
  const { onClose, type, initGroupName, refresh, groupId } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (type === 'edit' && initGroupName) {
      form.setFieldsValue({
        groupName: initGroupName,
      });
    }
  }, [form, initGroupName, type]);

  const rules: FormRules<IFormValues> = {
    groupName: [{ required: true, message: '请选择分类名称', type: 'error' }],
  };
  const onSubmit = async () => {
    const values = form.getFieldsValue(true) as IFormValues;
    console.debug(values, 'values');
    if (type === 'add') {
      Development.CreateUserGroup({
        group_name: values.groupName,
        group_type: QA_GROUP_TYPE,
      })
        .then(() => {
          void MessagePlugin.success({ content: '创建成功' });
          onClose();
        })
        .catch((e) => {
          console.error(e, '创建失败');
          void MessagePlugin.error({ content: '创建失败, 请稍后重试' });
        })
        .finally(() => {
          refresh?.();
        });
    } else if (type === 'edit' && groupId) {
      Development.UpdateUserGroupInfor({
        group_id: groupId,
        group_name: values.groupName,
        group_type: QA_GROUP_TYPE,
      })
        .then(() => {
          void MessagePlugin.success({ content: '修改成功' });
          onClose();
        })
        .catch((e) => {
          console.error(e, '创建失败');
          void MessagePlugin.error({ content: '修改失败, 请稍后重试' });
        })
        .finally(() => {
          refresh?.();
        });
    } else {
      void MessagePlugin.error({ content: '参数错误' });
    }
  };

  return (
    <div>
      <Form
        form={form}
        rules={rules}
        onSubmit={onSubmit}
        labelWidth={100}
        labelAlign="left"
      >
        <FormItem name="groupName" label="问答库名称">
          <Input clearable placeholder="请输入问答库名称" />
        </FormItem>

        <FormItem className="flex justify-center mt-20">
          <Space>
            <Button theme="default" onClick={onClose}>
              取消
            </Button>
            <Button type="submit">确定</Button>
          </Space>
        </FormItem>
      </Form>
    </div>
  );
}
