.qa-group-comp {
  position: relative;
  height: calc(100% - 54px);
  padding: 12px 16px;
  color: rgba(0, 0, 0, 0.60);
  //overflow: hidden;

  &-content {
    height: 100%;
    overflow: auto;

    .group-item {
      display: flex;
      align-items: center;
      padding: 4px 12px;
      height: 32px;
      width: 100%;
      border-radius: 4px;
      background: transparent;
      cursor: pointer;
      margin-bottom: 4px;

      &-title {
        display: inline-block;
        //align-items: center;
        width: calc(100% - 24px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-operate {
        visibility: hidden;
      }

      &:hover {
        background: linear-gradient(88deg, rgba(224, 234, 255, 0.5) 0%, rgba(226, 239, 255, 0.5) 46.96%, rgba(245, 243, 255, 0.5) 99.81%);

        .group-item-operate {
          visibility: visible;
        }
      }

      &:active {
        color: #0047F9;
        background: linear-gradient(88deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
      }

      &--active {
        color: #0047F9;
        background: linear-gradient(88deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
      }
    }
  }

  &-action-zone {
    width: 100%;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
    cursor: pointer;
    z-index: 3;
    padding: 12px 0;
    background-color: #FFFFFF;
    //background-color: red;
  }
}
