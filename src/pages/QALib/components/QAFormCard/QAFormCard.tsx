import { WebAudioRecorder } from '@/utils/media/audiorecorder';
import { css } from '@emotion/react';
import { FormItem } from '@formily/antd';
import { createForm } from '@formily/core';
import { Field, FormProvider } from '@formily/react';
import { useEffect, useMemo, useRef } from 'react';
import { Button, Input, Tag } from 'tdesign-react';

export interface IQAFormCardProps {
  initialValues: Record<string, any>;
}

const commonLabelStyle: React.CSSProperties = {
  fontWeight: 400,
  fontSize: '1rem',
  color: 'rgba(0, 0, 0, 0.4)',
};

export function QAFormCard(props: IQAFormCardProps) {
  const form = useMemo(() => {
    const form = createForm({
      initialValues: props.initialValues,
    });
    return form;
  }, [props.initialValues]);
  return (
    <div
      css={css`
        background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
      `}
    >
      <div
        css={css`
          margin-left: 17px;
          margin-top: 7px;
        `}
      >
        <span className="text-base font-medium">问答</span>
      </div>
      <FormProvider form={form}>
        <Field
          name="question"
          decorator={[
            FormItem,
            {
              labelWidth: 100,
              label: '问题',
              labelStyle: commonLabelStyle,
              colon: false,
            },
          ]}
          component={[
            Input,
            {
              readonly: true,
            },
          ]}
        />
      </FormProvider>
      <Button onClick={}>语音转文字</Button>
    </div>
  );
}
