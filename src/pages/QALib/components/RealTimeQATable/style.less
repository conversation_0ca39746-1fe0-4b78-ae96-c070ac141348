.realtime_qa {
    @paginationHeight: 73px;

    &__container {
        height: 100%;
        position: relative;
    }


    &_table {
        max-height: calc(100% - @paginationHeight);
        overflow-y: auto;

        .answer_col {
            background: rgba(251, 251, 251, 1);
        }

        .answer_status {
            width: 56px;
            height: 24px;


            &__noanswered {

                // background: linear-gradient(90deg, #FFF0E6 0%, #FFF5EE 100%);
                color: #EA7017;
            }

            &__answered {
                // background: linear-gradient(270deg, #EBF8F0 0%, #DEF8E8 100%);
                color: #00C653;
                // color: linear-gradient(90deg, #00C653 0%, #01D766 100%);
            }
        }

        .answer_switch {
            &__wrapper {
                user-select: none;
                /* radioButton-Group 单选按钮组 */

                /* Auto layout */
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 2px;
                gap: 4px;

                // width: 86px;
                height: 24px;
                left: 20px;
                top: 88px;

                /* <PERSON>中性/Gray5-normal */
                background: linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 100%);
                border-radius: 3px;

                /* Inside auto layout */
                flex: none;
                flex-grow: 0;
                z-index: 3;

                cursor: pointer;
            }

            &__readonly {
                cursor: auto !important;
            }

            &__disabled {
                cursor: auto !important;
            }

            &__item {
                text-align: center;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.6);
                height: 20px;
            }

            &_need {
                width: 38px;
            }

            &_noneed {
                width: 42px;
            }

            &__active {
                color: rgba(0, 71, 249, 1) !important;
                background: rgba(255, 255, 255, 1);
                border-radius: 2px 0px 0px 0px;
            }
        }

        .answer_editor {
            &__readonly {
                .answer_btn {
                    opacity: 0.4;
                    cursor: auto !important;
                }
            }

            &__preview {

                // 问题回答方式样式
                .answer_method {
                    padding: 0 4px;
                    background: rgba(197, 217, 255, 1);
                    color: rgba(0, 0, 0, 0.4);
                    gap: 0px;
                    border-radius: 2px;
                    font-size: 10px;
                }

                // 答案来源样式
                .answer_source {
                    color: rgba(0, 0, 0, 0.3);
                    font-weight: 400;
                    font-size: 12px;
                    margin-left: 5px;
                }

                .answer_text {
                    color: rgba(0, 0, 0, 0.9);
                    font-weight: 400;
                }

                // 编辑按钮
                .answer_btn {
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                }
            }

            &__edit {
                position: relative;

                &_footer {
                    position: absolute;
                    display: flex;
                    width: 35px;
                    height: 20px;
                    bottom: -6px;
                    right: -3px;
                    justify-content: space-between;
                }

                &_save {
                    border-radius: 4px;
                    background: linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
                    color: #fff;
                    cursor: pointer;
                }

                &_cancel {
                    border: 1px solid rgba(255, 255, 255, 1);
                    background: rgba(255, 255, 255, 1);
                    border-radius: 4px;
                    cursor: pointer;
                }
            }
        }
    }

    &__pagination {
        width: 100%;
        height: @paginationHeight;
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 0 24px;
        box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);

        .t-is-current {
            background: linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
        }

        .t-pagination__jump {
            background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
        }
    }
}
