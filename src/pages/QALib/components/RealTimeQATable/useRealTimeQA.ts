import { Service } from 'ahooks/lib/usePagination/types';
import { QAStatusEnum, RealTimeQAListItem } from './typing';
import { RealTimeInteraction } from '@/pb/pb';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useMemoizedFn, usePagination, usePrevious } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';

type ModifyReqType = Parameters<
  (typeof RealTimeInteraction)['ModifyLiveDmAnswerInfo']
>[0];

type QueryDanmuAnswerReqType = Parameters<
  (typeof RealTimeInteraction)['QueryLiveDmAnswerStatus']
>[0];

const defaultPage = 1;
const defaultPageSize = 10;

export const useRealTimeQA = (options: { liveID: string }) => {
  const { liveID } = options;
  // 存放当前数据的数量
  const [totalNum, setTotalNum] = useState(0);

  const initedRef = useRef(false);

  const previesTotalNum = usePrevious(totalNum);

  // 用于表格展示的数据
  const [data, setData] = useState<RealTimeQAListItem[]>([]);

  const updateTotalNumRef = useRef<() => Promise<void>>(() =>
    Promise.resolve()
  );

  const updateTotalNumOnlyRef = useRef(false);

  // 表示是否为刷新页面操作
  const isRefreshing = useRef(false);

  const isSettingLocalData = useRef(false);

  const loadData: Service<
    {
      total: number;
      list: RealTimeQAListItem[];
    },
    [
      {
        current: number;
        pageSize: number;
      }
    ]
  > = useCallback(
    async (params) => {
      const resp = await RealTimeInteraction.QueryLiveDmAnswerList({
        live_id: liveID,
        query_page_num: params.current,
        query_page_size: params.pageSize,
      });
      console.log('resp', resp);
      setTotalNum(resp.answer_total_num);
      return {
        total: resp.answer_total_num,
        list: resp.dm_answer_list,
      };
    },
    [liveID]
  );

  // 分页加载数据
  const {
    pagination,
    runAsync: runPaginationAsync,
    params,
    loading,
    data: qaData,
  } = usePagination(loadData, {
    manual: false,
    defaultCurrent: defaultPage,
    defaultPageSize,
    // pollingInterval:
    pollingInterval: 1000,
  });

  const setLocalQADataList = useCallback(
    async (list?: RealTimeQAListItem[]) => {
      if (isSettingLocalData.current) return;
      isSettingLocalData.current = true;
      const localQAData = list || qaData?.list || [];
      // 刷新时，仅更新数据，不要更新一些本地注入的状态
      if (!isRefreshing.current) {
        const list =
          localQAData?.map((item) => ({
            ...item,
            __isEdit: false,
          })) || [];
        setData(list);
      } else {
        // 根据当前数据中的弹幕id进行数据合并
        setData((prev) => {
          // 以新数据为准，进行数据合并
          return localQAData.map((newItem) => {
            const targetItem = prev.find(
              (oldItem) => oldItem.danmu_id === newItem.danmu_id
            );
            const newData = {
              ...targetItem,
              ...newItem,
            };
            if (newData.status === QAStatusEnum.ANSWERED) {
              // 已回答的，不能编辑
              newData.__isEdit = false;
            }
            return newData;
          });
        });
      }
      isSettingLocalData.current = false;
    },
    [qaData]
  );

  const updateDataAt = (idx: number, data: Partial<RealTimeQAListItem>) => {
    setData((prevData) => {
      return [
        ...prevData.slice(0, idx),
        {
          ...prevData[idx],
          ...data,
        },
        ...prevData.slice(idx + 1),
      ];
    });
  };

  const modifyAnswer = useMemoizedFn(
    async (req: Omit<ModifyReqType, 'live_id'>) => {
      try {
        await RealTimeInteraction.ModifyLiveDmAnswerInfo({
          live_id: liveID,
          ...req,
        });
      } catch (e) {
        MessagePlugin.error({
          content: `修改弹幕状态失败: ${(e as Error).message}`,
        });
        throw e;
      } finally {
        refresh();
      }
    }
  );
  // 用于告诉后台锁住弹幕播报
  const lockAnswer = async (req: Pick<ModifyReqType, 'danmu_id'>) => {
    // return;
    let err;
    try {
      await RealTimeInteraction.ModifyLiveDmAnswerInfo({
        live_id: liveID,
        ...req,
        answer_status: 3,
      });
    } catch (e) {
      err = e;
    }
    if (req.danmu_id) {
      // 锁住后单独拉取一遍状态
      const danmuList = await queryAnswerStatus({
        danmu_id_list: [req.danmu_id],
      });
      let newItem: RealTimeQAListItem = null as any;
      let newItemIdx = -1;
      if (danmuList.length > 0) {
        data?.forEach((item, idx) => {
          if (item.danmu_id === danmuList[0].danmu_id) {
            newItemIdx = idx;
            newItem = { ...item, status: danmuList[0].status };
          }
        });
      }
      if (newItem && newItemIdx > -1) {
        updateDataAt(newItemIdx, newItem);
        if (newItem.status === QAStatusEnum.ANSWERED) {
          if (!err) err = new Error(`弹幕 ${newItem.danmu_id} 已被回答`);
          throw err;
        }
      }
    }
  };

  // 查询后台回答状态
  const queryAnswerStatus = async (
    req: Pick<QueryDanmuAnswerReqType, 'danmu_id_list'>
  ) => {
    const resp = await RealTimeInteraction.QueryLiveDmAnswerStatus({
      live_id: liveID,
      danmu_id_list: req.danmu_id_list,
    });
    return resp.dm_answer_status_list;
  };

  // 刷新当前页面
  const refresh = useCallback(async () => {
    isRefreshing.current = true;
    try {
      await runPaginationAsync({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    } catch {
      isRefreshing.current = false;
    } finally {
    }
  }, [runPaginationAsync, pagination]);

  updateTotalNumRef.current = async () => {
    console.log(
      `
    当前刷新的页：

    `,
      pagination
    );
    refresh();
    // await runPaginationAsync({
    //   current: pagination.current,
    //   pageSize: pagination.pageSize,
    // });
    // 仅用于刷新页码
    // const currentTotalNum = totalNum;
    // const { total } = await loadData({
    //   current: pagination.current,
    //   pageSize: pagination.pageSize,
    // });
    // if (total !== currentTotalNum) {
    //   // 新的页数小于之前总页数，计算一个新页数下的最大页码
    //   if (total < currentTotalNum) {
    //     pagination.current = Math.ceil(total / pagination.pageSize);
    //   }
    //   // 强制触发刷新
    //   refresh();
    // }
  };
  // 用于更新页面总数使用
  const updateTotalNum = async () => {
    try {
      updateTotalNumOnlyRef.current = true;
      await updateTotalNumRef.current();
    } catch (e) {
      updateTotalNumOnlyRef.current = false;
      throw e;
    }
  };

  const handleChangePage = (current: number, pageSize: number) => {
    pagination.onChange(current, pageSize);
  };

  // qaData 变化后通过改变setLocalQADataList，更新本地数据
  useEffect(() => {
    setLocalQADataList();
    if (isRefreshing.current) {
      isRefreshing.current = false;
    }
  }, [setLocalQADataList]);

  return {
    listData: data,
    totalNum,
    loadData,
    // updateRowData: setData,
    updateDataAt,
    modifyAnswer,
    lockAnswer,
    queryAnswerStatus,
    refreshList: refresh,
    updateTotalNum,
    paginationInfo: {
      totalNum,
      pagination,
      handleChangePage,
      runPaginationAsync,
      qaData,
    },
  };
};
