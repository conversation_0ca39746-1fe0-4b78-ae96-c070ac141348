// 是否问答开关
import React, { useEffect, useMemo, useState } from 'react';
import './style.less';

export interface IAnswerSwitchOption<V = any> {
  // 标签
  label: string;
  // 值
  value: V;
  // 自定义类名
  classNames?: string[];
  style?: React.CSSProperties;
  disabled?: boolean;
}
export interface IAnswerSwitchProps<V = any> {
  value: V;
  onChange: (value: V) => Promise<void>;
  options: IAnswerSwitchOption<V>[];
  onInit?: () => void;
  className?: string;
  style?: React.CSSProperties;
  readonly?: boolean;
}

export function AnswerSwitch<V>(props: IAnswerSwitchProps<V>) {
  const { onChange, value, options, className, style, onInit, readonly } =
    props;
  const [isInited, setInit] = useState(false);

  useEffect(() => {
    if (!isInited) {
      try {
        onInit?.();
      } catch {}
      setInit(true);
    }
  }, [isInited, onInit]);
  const cls = ['answer_switch__wrapper', className];
  if (readonly) {
    cls.push('answer_switch__readonly');
  }
  return (
    <div className={cls.join(' ')} style={{ ...style }}>
      {options.map((option) => {
        const cls = ['answer_switch__item', ...(option.classNames || [])];
        if (value === option.value) {
          cls.push('answer_switch__active');
        }
        if (option.disabled) {
          cls.push('answer_switch__disabled');
        }
        return (
          <span
            className={cls.join(' ')}
            style={{ ...option.style }}
            onClick={() => {
              if (readonly || option.disabled) return;
              onChange(option.value);
            }}
          >
            {option.label}
          </span>
        );
      })}
    </div>
  );
}
