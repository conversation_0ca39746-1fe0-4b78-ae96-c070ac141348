// 问题回答编辑器
import { css } from '@emotion/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { CheckIcon, CloseIcon, UserTalk1Icon } from 'tdesign-icons-react';
import { Button, Divider, MessagePlugin, Space, Textarea } from 'tdesign-react';
import { TextareaRefInterface } from 'tdesign-react/es/textarea/Textarea';
import { AnswerSwitch } from './AnswerSwitch';
import './style.less';
import { AnswerMethodType, AnswerSourceType, QuestionTypeEnum } from './typing';
import { IntelligentRecoveryBtn } from '@/pages/QALib/components/IntelligentRecoveryBtn';
import { AudioRecognize } from '@/pages/QALib/components/AudioRecognize/AudioRecognize';
import LoadingGif from '@/assets/images/idooLoading.gif';
import {
  AddButton,
  TranslationButton,
  TranslationOptions,
  TranslationResult,
  useTranslationResult,
} from '@/pages/List/components/translation';
import { useRecoilValue } from 'recoil';
import {
  LiveAddAnswerAtom,
  TranslationAnswerAtom,
  TranslationAtom,
} from '@/pages/List/components/realtime_qa_drawer/store';

export interface IAnswerEditorProps {
  // 是否需要回答的数据需传入
  needAnswer: boolean;

  // 问题内容
  question: string;

  // 问题类型
  questionType: QuestionTypeEnum;

  // 回答方式
  answerMethod: AnswerMethodType;

  // 问答来源
  answerSource: AnswerSourceType;

  // 问答库名称
  answerGroupName: string;

  // 是否为编辑中状态
  isEdit: boolean;

  readonly: boolean;
  // 使用受控方式处理编辑态
  onEdit?: (shouldEdit: boolean) => Promise<void>;

  // 回答的值
  value: string;

  // 触发写入数据
  onChange: (data: {
    answerMethod: AnswerMethodType;
    value: string;
  }) => Promise<void>;

  className?: string;
  style?: React.CSSProperties;
  previewClassName?: string;
  editClassName?: string;
}

// 编辑的信息
export interface IAnswerEditState
  extends Pick<IAnswerEditorProps, 'answerMethod'> {
  // 回答内容
  answerValue: string;
}

const AnswerMethodTextMapping: Record<AnswerMethodType, string> = {
  dman: '数字人回复',
  barrage: '弹幕回复',
};

const AnswerSourceTextMapping: Record<
  AnswerSourceType,
  (...args: any[]) => string
> = {
  answer_question_library: (_libName: string) => `答案由问答库提供`,
  large_model: () => '答案由大模型提供',
  manual_edit: () => '答案由人工编辑',
};

export function AnswerEditor(props: IAnswerEditorProps) {
  const {
    questionType,
    value,
    onChange,
    className,
    style,
    previewClassName,
    editClassName,
    answerMethod,
    answerSource,
    needAnswer,
    isEdit,
    onEdit,
    readonly,
    question,
  } = props;

  const textareaRef = useRef<TextareaRefInterface | null>(null);
  const [editState, setEditState] = useState<IAnswerEditState>({
    answerMethod,
    answerValue: value,
  });
  const [intelligentLoading, setIntelligentLoading] = useState(false);
  const multilingual = useRecoilValue(TranslationAtom);
  const answerTranslation = useRecoilValue(TranslationAnswerAtom);
  const transitionOptionsState = useState<TranslationOptions>([
    undefined,
    undefined,
  ]);
  const addAnswer = useRecoilValue(LiveAddAnswerAtom);

  const { result } = useTranslationResult({
    switch: multilingual,
    text: value,
    globalLang: answerTranslation,
    lang: transitionOptionsState[0],
  });

  const add = () => {
    const text = result && typeof result === 'string' ? result : value;
    addAnswer?.(text)
      .then(() => {
        MessagePlugin.success('添加成功！');
      })
      .catch(() => {
        MessagePlugin.error('添加失败！');
      });
  };

  const [voiceInputActive, setVoiceInputActive] = useState(false);

  const handleTriggerEdit = async () => {
    // setManualIsEdit(true);
    if (readonly) return;
    await onEdit?.(true);
  };

  const resetEditState = useCallback(() => {
    setEditState((prev) => ({ ...prev, answerMethod, answerValue: value }));
  }, [answerMethod, value]);

  // 保存编辑结果
  const onSave = useCallback(async () => {
    try {
      handleVoiceStop();
      await onChange?.({
        answerMethod: editState.answerMethod,
        value: editState.answerValue,
      });
      onEdit?.(false);
    } catch (e) {
      console.error('保存编辑结果失败： ', e);
    }
  }, [editState, onChange, onEdit]);

  // 取消编辑
  const onCancel = useCallback(() => {
    if (isEdit) {
      onEdit?.(false);
      // 恢复编辑器的值
      resetEditState();
    }
  }, [resetEditState, isEdit, onEdit]);

  const setAnswerValue = (value: string) => {
    setEditState((prev) => ({ ...prev, answerValue: value }));
  };
  // 语音输入开始
  const handleVoiceInput = async () => {
    //   这里处理语音输入
    setVoiceInputActive(true);
  };
  // 语音输入停止
  const handleVoiceStop = async () => {
    setVoiceInputActive(false);
    textareaRef?.current?.textareaElement?.blur();
  };

  const focus = (resetTextRange?: boolean) => {
    const textAreaEl = textareaRef.current?.textareaElement;
    if (textAreaEl) {
      if (resetTextRange) {
        textAreaEl.setSelectionRange(
          textAreaEl.value.length,
          textAreaEl.value.length,
          'backward'
        );
      }
      textAreaEl.focus();
    }
  };

  // 可编辑时，进行自动聚焦
  useEffect(() => {
    if (isEdit) {
      const textAreaEl = textareaRef.current?.textareaElement;
      // 同步props 属性到editState上
      resetEditState();
      if (textAreaEl) {
        focus(true);
      }
    } else {
      resetEditState();
    }
  }, [isEdit, resetEditState]);

  useEffect(() => {
    // 出现readonly
    if (readonly) {
      // 出现只读状态，调用onCancel
      console.log('设置为了只读态，取消编辑态');
      onCancel();
    }
  }, [readonly, onCancel]);

  let el: React.JSX.Element = null as any;
  if (!isEdit || readonly) {
    let answerSourceText = '';
    if (answerSource === 'answer_question_library') {
      // 问答库提供
      answerSourceText = AnswerSourceTextMapping.answer_question_library(
        props.answerGroupName
      );
    } else if (typeof AnswerSourceTextMapping[answerSource] === 'function') {
      answerSourceText = AnswerSourceTextMapping[answerSource]();
    }
    const cls = [previewClassName, 'answer_editor__preview'];
    if (readonly) {
      cls.push('answer_editor__readonly');
    }
    // 预览状态
    el = (
      <div className={cls.join(' ')}>
        <div
          css={css`
            display: flex;
            justify-content: space-between;
          `}
        >
          <div
            css={css`
              display: flex;
            `}
          >
            <span className="answer_method">
              {AnswerMethodTextMapping[answerMethod] || ''}
            </span>
            <span className="answer_source">{answerSourceText}</span>
          </div>
          <div
            css={css`
              display: flex;
              gap: 8px;
            `}
          >
            {/* 翻译按钮*/}
            {multilingual && (
              <TranslationButton state={transitionOptionsState} />
            )}
            {/* 修改按钮*/}
            <Button
              theme="default"
              shape="circle"
              variant="text"
              size="small"
              style={{
                boxShadow: '0px 8px 10px -5px rgba(0, 0, 0, 0.18)',
              }}
            >
              <svg
                className="answer_btn"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                onClick={handleTriggerEdit}
              >
                <path
                  d="M10.6154 2.60581L13.3942 5.38453L14 4.77873L11.2212 2L10.6154 2.60581Z"
                  fill="url(#paint0_linear_5948_25573)"
                />
                <path
                  d="M3.30757 12.9949L6.40297 12.3758L12.6552 6.12365L9.87642 3.34492L3.62421 9.59706L3.00513 12.6924C2.96916 12.8723 3.12773 13.0308 3.30757 12.9949ZM9.87642 4.55654L11.4436 6.12365L5.98058 11.5866L4.02167 11.9783L4.41345 10.0194L9.87642 4.55654Z"
                  fill="url(#paint1_linear_5948_25573)"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_5948_25573"
                    x1="3"
                    y1="6.2625"
                    x2="14"
                    y2="6.2625"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#0052D9" />
                    <stop offset="1" stopColor="#3973FD" />
                  </linearGradient>
                  <linearGradient
                    id="paint1_linear_5948_25573"
                    x1="3"
                    y1="6.2625"
                    x2="14"
                    y2="6.2625"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#0052D9" />
                    <stop offset="1" stopColor="#3973FD" />
                  </linearGradient>
                </defs>
              </svg>
            </Button>
            {/* 添加按钮*/}
            <AddButton onClick={add} />
          </div>
        </div>
        <p>{value}</p>
        {multilingual && <TranslationResult result={result} />}
      </div>
    );
  } else {
    // 编辑状态
    el = (
      <div className={[editClassName, 'answer_editor__edit'].join(' ')}>
        <div
          css={css`
            display: flex;
            justify-content: space-between;
          `}
        >
          <AnswerSwitch<AnswerMethodType>
            value={editState.answerMethod}
            style={{
              marginBottom: '4px',
            }}
            options={[
              {
                label: AnswerMethodTextMapping.dman,
                value: 'dman',
                style: {
                  width: '101px',
                },
              },
              // {
              //   label: AnswerMethodTextMapping.barrage,
              //   value: 'barrage',
              //   style: {
              //     width: '101px',
              //   },
              //   disabled: true,
              // },
            ]}
            onChange={async (value) => {
              setEditState((prev) => ({ ...prev, answerMethod: value }));
            }}
          />
          <div
            css={css`
              background: rgba(234, 231, 238, 1);
              border-radius: 5px 5px 0 0;
              margin-bottom: -2px;
              padding: 2px 11px;
              color: rgba(0, 71, 249, 1);
              cursor: pointer;
            `}
          >
            {!voiceInputActive ? (
              <Space
                align="center"
                size="small"
                separator={
                  <Divider
                    layout="vertical"
                    style={{
                      borderColor: 'rgba(190, 186, 196, 1)',
                    }}
                  />
                }
              >
                <IntelligentRecoveryBtn
                  question={question}
                  onSuccess={(value) => {
                    // onChange?.(value);
                    setAnswerValue(value);
                  }}
                  setLoading={setIntelligentLoading}
                />
                <span onClick={handleVoiceInput}>
                  <UserTalk1Icon />
                  语音输入
                </span>
              </Space>
            ) : null}
            {voiceInputActive ? (
              <AudioRecognize
                onStart={() => focus()}
                onStop={handleVoiceStop}
                onTextChange={(text) => {
                  focus(true);
                  // onChange?.(value + text);
                  setAnswerValue(editState.answerValue + text);
                }}
              />
            ) : null}
          </div>
        </div>
        <Textarea
          placeholder="请输入回答..."
          autosize={{ minRows: 3, maxRows: 3 }}
          style={{ width: '100%', height: '100%' }}
          value={editState.answerValue}
          onChange={(value) => {
            setAnswerValue(value);
          }}
          autoFocus
          ref={textareaRef}
          onBlur={() => {
            if (voiceInputActive) focus(true);
            // if (voiceInputActive) textareaRef?.current?.textareaElement.focus();
          }}
        />
        <div className="answer_editor__edit_footer">
          <CheckIcon className="answer_editor__edit_save" onClick={onSave} />
          <CloseIcon
            className="answer_editor__edit_cancel"
            onClick={onCancel}
          />
        </div>
        {intelligentLoading && (
          <div className="qa-intelligent-input-comp-loading">
            <img width={60} height={44} src={LoadingGif} alt="" />
            <span>正在为您生成答案...</span>
          </div>
        )}
      </div>
    );
  }

  return el;
}
