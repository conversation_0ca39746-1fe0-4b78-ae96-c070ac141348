import { RealTimeInteraction } from '@/pb/pb';
import { PromiseType } from '@/utils/type-util';
// 问题回答模式，控制是否回答的开关值
export enum AnswerModeType {
  // 需要回答
  ALL_NEED_ANSWER = 'needAnswer',

  // 全部不需要回答
  ALL_NO_NEED_ANSWER = 'noNeedAnswer',

  // 仅库内模式需要回答
  INSIDE_QALIB_NEED_ANSWER = 'insideQalibNeedAnswer',
}

export enum QuestionTypeEnum {
  // 库内问题
  IN_LIB = 'inlib',

  // 库外问题
  EXTERNAL_LIB = 'external_lib',
}
/**
 * @description 问题回答方式
 * dman: 数字人回复
 * barrage: 弹幕评论回复
 */
export type AnswerMethodType = 'dman' | 'barrage';

/*
 * 答案来源
 * @description: manual_edit:人工编辑, large_model:大模型生成，answer_question_library: 问答库
 */
export type AnswerSourceType =
  | 'manual_edit'
  | 'large_model'
  | 'answer_question_library';

// 实时QA列表响应
export type RealTimeQAListResponse = PromiseType<
  ReturnType<(typeof RealTimeInteraction)['QueryLiveDmAnswerList']>
>;

// 实时QA列表响应项
export type RealTimeQAListItem = RealTimeQAListResponse['dm_answer_list'][0] & {
  // 是否编辑态
  __isEdit?: boolean;
};

export const QAStatusEnum = {
  ANSWERED: 1,
  NO_ANSWERED: 2,
  MODIFYING: 3,
  INVALID: -1,
};
