// 实时问题列表

import { css } from '@emotion/react';
import { useEventEmitter, useMemoizedFn } from 'ahooks';
import { useEffect, useMemo, useState } from 'react';
import {
  MessagePlugin,
  Pagination,
  PrimaryTableCol,
  Table,
} from 'tdesign-react';
import { AnswerEditor } from './AnswerEditor';
import { AnswerSwitch } from './AnswerSwitch';
import { IRealTimeQAContextValue, RealTimeQAContext } from './shared-context';
import './style.less';
import { QAStatusEnum, QuestionTypeEnum, RealTimeQAListItem } from './typing';
import { useRealTimeQA } from './useRealTimeQA';
import { RespError } from '@/pb/config';
import dayjs from 'dayjs';
import {
  Translation,
  TranslationButton,
  TranslationOptions,
  TranslationResult,
  useTranslationResult,
} from '@/pages/List/components/translation';
import { useRecoilState, useRecoilValue } from 'recoil';
import {
  TranslationAnswer<PERSON>tom,
  TranslationAtom,
  TranslationQuestionAtom,
} from '@/pages/List/components/realtime_qa_drawer/store';

export interface IRealTimeQATableProps {
  filter: {
    questionType: 'ALL' | 'NEED_ANSWER' | 'NO_NEED_ANSWER';
  };

  // defaultAnwserMode: AnswerModeType;

  // 直播id
  liveID: string;
}

export function RealTimeQATable(props: IRealTimeQATableProps) {
  const { filter, liveID } = props;
  // TODO: 获取数据
  // 处理 context 初始化
  const cancelEditEvent$: IRealTimeQAContextValue['events']['cancelEdit$'] =
    useEventEmitter();
  const enterEditEvent$: IRealTimeQAContextValue['events']['enterEdit$'] =
    useEventEmitter();
  const contextValue = useMemo<IRealTimeQAContextValue>(() => {
    return {
      events: {
        cancelEdit$: cancelEditEvent$,
        enterEdit$: enterEditEvent$,
      },
    };
  }, [cancelEditEvent$, enterEditEvent$]);
  // 初始化 hook
  const {
    lockAnswer,
    modifyAnswer,
    paginationInfo,
    listData,
    updateDataAt,
    updateTotalNum,
  } = useRealTimeQA({
    liveID,
  });
  // console.log(listData, 'listDatalistData');
  const { pagination, qaData, runPaginationAsync, totalNum, handleChangePage } =
    paginationInfo;

  // 设置单个行的数据
  const setRowData = useMemoizedFn(
    (rowIndex: number, row: Partial<RealTimeQAListItem>) => {
      updateDataAt(rowIndex, row);
    }
  );

  // 进入编辑态需要和后台交互
  const handleToggleEdit = useMemoizedFn(
    async (rowIndex: number, row: RealTimeQAListItem, shouldEdit: boolean) => {
      if (
        shouldEdit
        // && row.status === QAStatusEnum.NO_ANSWERED
      ) {
        /** 先调用后台锁住状态成功后再进入编辑态 */
        try {
          await lockAnswer({
            danmu_id: row.danmu_id,
          });
        } catch (e) {
          // 刷新列表状态
          console.error('锁住状态失败', e);
          MessagePlugin.error({
            content: `修改弹幕状态失败: ${(e as Error).message}`,
          });
          return;
        }
      }
      setRowData(rowIndex, {
        __isEdit: shouldEdit,
      });
    }
  );

  const colums = useMemo<PrimaryTableCol<RealTimeQAListItem>[]>(() => {
    return [
      {
        colKey: 'in_answer_question_library',
        title: '问题类型',
        cell: ({ row, col, rowIndex }) => {
          return (
            <span>
              {row.in_answer_question_library === 1 ? '库内' : '库外'}
            </span>
          );
        },
      },
      {
        colKey: 'question',
        title: function Title() {
          const questionTranslation = useRecoilState(TranslationQuestionAtom);
          const multilingual = useRecoilValue(TranslationAtom);
          return (
            <div
              css={css`
                display: flex;
                gap: 32px;
                align-items: center;
              `}
            >
              问题
              {multilingual && <Translation state={questionTranslation} />}
            </div>
          );
        },
        width: 320,
        cell: function Title({ row, col, rowIndex }) {
          const multilingual = useRecoilValue(TranslationAtom);
          const questionTranslation = useRecoilValue(TranslationQuestionAtom);
          const transitionOptionsState = useState<TranslationOptions>([
            undefined,
            undefined,
          ]);
          const { result } = useTranslationResult({
            switch: multilingual,
            text: row.question,
            globalLang: questionTranslation,
            lang: transitionOptionsState[0],
          });
          return (
            <div>
              <div>{row.question}</div>
              {multilingual && (
                <div style={{ position: 'absolute', right: 10, top: 20 }}>
                  <TranslationButton state={transitionOptionsState} />
                </div>
              )}
              {multilingual && <TranslationResult result={result} />}
            </div>
          );
        },
      },
      {
        colKey: 'need_answer',
        title: '是否需要回答',
        width: 120,
        cell: ({ row, col, rowIndex }) => {
          const processSpecialLogic = async (needAnswer: boolean) => {
            let shouldEdit = !!row.__isEdit;
            // TODO: 增加库内外判断特殊逻辑
            if (
              row.in_answer_question_library !== 1 &&
              row.answer === '' &&
              needAnswer
            ) {
              // 库外问题，并且没有答案，选择了需要回答，需要强制设置为可编辑态
              shouldEdit = true;
            }
            if (!needAnswer) {
              shouldEdit = false;
            }
            await handleToggleEdit(rowIndex, row, shouldEdit);
          };
          return (
            <AnswerSwitch<boolean>
              key={row.danmu_id}
              value={!!row.need_answer}
              onInit={() => {
                processSpecialLogic(!!row.need_answer);
              }}
              readonly={row.status === QAStatusEnum.ANSWERED}
              onChange={async (value) => {
                //  请求后台修改是否需要修改为未播报
                try {
                  await modifyAnswer({
                    danmu_id: row.danmu_id,
                    need_answer: `${value}`,
                  });
                  await processSpecialLogic(value);
                  setRowData(rowIndex, {
                    need_answer: ~~value,
                  });
                } catch (e) {
                  // TODO: 做提示处理
                  console.error('调用后台是否需要修改失败', e);
                }
              }}
              options={[
                {
                  label: '需要',
                  value: true,
                  classNames: ['answer_switch_need'],
                },
                {
                  label: '不需要',
                  value: false,
                  classNames: ['answer_switch_noneed'],
                },
              ]}
              style={{
                width: '86px',
              }}
            />
          );
        },
      },
      {
        colKey: 'answer',
        title: function Title() {
          const answerTranslation = useRecoilState(TranslationAnswerAtom);
          const multilingual = useRecoilValue(TranslationAtom);

          return (
            <div
              css={css`
                display: flex;
                gap: 32px;
                align-items: center;
              `}
            >
              回答
              {multilingual && <Translation state={answerTranslation} />}
            </div>
          );
        },
        width: 483,
        className: 'answer_col',
        cell: ({ row, col, rowIndex }) => {
          const readonly = row.status === QAStatusEnum.ANSWERED;
          return (
            <div css={css``}>
              <AnswerEditor
                value={row.answer || ''}
                question={row.question || ''}
                needAnswer={!!row.need_answer}
                questionType={
                  row.in_answer_question_library === 1
                    ? QuestionTypeEnum.IN_LIB
                    : QuestionTypeEnum.EXTERNAL_LIB
                }
                answerGroupName={row.answer_group_name || ''}
                answerSource={(row.answer_source || '') as any}
                answerMethod={(row.answer_method || '') as any}
                // 编辑态受控
                isEdit={!!row.__isEdit}
                readonly={readonly}
                onEdit={
                  readonly
                    ? void 0
                    : async (shouldEdit: boolean) => {
                        await handleToggleEdit(rowIndex, row, shouldEdit);
                      }
                }
                onChange={async ({ answerMethod, value }) => {
                  /** 调用后台接口进行更新答案后再 set行数据 */
                  await modifyAnswer({
                    answer: value,
                    answer_method: answerMethod,
                    danmu_id: row.danmu_id,
                    answer_status: QAStatusEnum.NO_ANSWERED,
                  });
                  setRowData(rowIndex, {
                    answer_method: answerMethod,
                    answer: value,
                  });
                }}
              />
            </div>
          );
        },
      },
      {
        colKey: 'insert_time',
        title: '提问时间',
        width: 120,
        cell: ({ row }) => {
          return (
            <span>
              {dayjs.unix(+row.insert_time).format('YYYY-MM-DD HH:mm:ss')}
            </span>
          );
        },
      },
      {
        colKey: 'status',
        title: '回答状态',
        cell: ({ row }) => {
          const answerStatusClass =
            row.status === QAStatusEnum.ANSWERED
              ? 'answer_status__answered'
              : 'answer_status__noanswered';
          const answerSpan =
            row.status === QAStatusEnum.ANSWERED ? (
              <span className={`answer_status ${answerStatusClass}`}>
                已回答
              </span>
            ) : (
              <span className={`answer_status ${answerStatusClass}`}>
                未回答
              </span>
            );
          return <div>{answerSpan}</div>;
        },
      },
    ];
  }, [setRowData, handleToggleEdit, modifyAnswer]);

  // 轮询后台接口用于更新总页数
  useEffect(() => {
    let timeout: any;
    let destroyed = false;
    const polling = async () => {
      if (destroyed) return;
      try {
        await updateTotalNum();
      } catch (e) {
        console.error('轮询列表报错 e, ', e);
      }
      if (destroyed) return;
      timeout = setTimeout(polling, 5000);
    };
    timeout = setTimeout(polling, 5000);
    return () => {
      console.log('清理轮询计时器');
      destroyed = true;
      clearTimeout(timeout);
    };
  }, [updateTotalNum]);

  return (
    <RealTimeQAContext.Provider value={contextValue}>
      <div
        className="realtime_qa__container"
        css={css`
          .t-table,
          .t-table__content {
            height: 100%;
          }
        `}
      >
        <Table
          className="realtime_qa_table"
          columns={colums}
          data={listData}
          rowKey="danmu_id"
          verticalAlign="middle"
        />
        <Pagination
          className="realtime_qa__pagination"
          showPageNumber
          showPageSize
          showJumper
          showPreviousAndNextBtn
          size="medium"
          total={totalNum}
          totalContent
          current={pagination.current}
          pageSize={pagination.pageSize}
          onChange={(pageInfo) =>
            handleChangePage(pageInfo.current, pageInfo.pageSize)
          }
        />
      </div>
    </RealTimeQAContext.Provider>
  );
}
