/**
 * <AUTHOR>
 * @date 2024/5/21 下午2:23
 * @desc index
 */

import React from 'react';
import { MessagePlugin } from 'tdesign-react';
import { QAGenerateRequest } from '@/type/pagedoo';
import { AIService } from '@/type/pagedoo-ai';

interface IProps {
  question: string;
  onSuccess: (value: string) => void;
  setLoading?: (loading: boolean) => void;
}

export function IntelligentRecoveryBtn(props: IProps) {
  const { question, onSuccess, setLoading } = props;

  const handleIntelligentGeneration = async (question: string) => {
    if (!question) {
      void MessagePlugin.warning('未识别到问题');
      return;
    }
    setLoading?.(true);
    try {
      const requestData: QAGenerateRequest = {
        input_string: question,
        question_list: [],
        // question,
      };
      const res = await AIService.pagedoo_0039(requestData);
      // if (!res.answer) {
      //   void MessagePlugin.warning('未识别到答案');
      // }
      onSuccess(res?.generated_replies?.[0] || '');
    } catch (e) {
      console.error(e);
      void MessagePlugin.error('智能生成失败');
    } finally {
      setLoading?.(false);
    }
  };
  return (
    <span
      onClick={() => {
        handleIntelligentGeneration(question).then();
      }}
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9 5.5C9 7.15686 7.65674 8.5 6 8.5C4.34326 8.5 3 7.15686 3 5.5C3 3.84314 4.34326 2.5 6 2.5C7.65674 2.5 9 3.84314 9 5.5ZM8 5.5C8 4.39543 7.10449 3.5 6 3.5C4.89551 3.5 4 4.39543 4 5.5C4 6.60457 4.89551 7.5 6 7.5C7.10449 7.5 8 6.60457 8 5.5Z"
          fill="#0047F9"
        />
        <path
          d="M14 10C14 11.3807 12.8806 12.5 11.5 12.5C10.1194 12.5 9 11.3807 9 10C9 8.61929 10.1194 7.5 11.5 7.5C12.8806 7.5 14 8.61929 14 10ZM13 10C13 9.17157 12.3284 8.5 11.5 8.5C10.6716 8.5 10 9.17157 10 10C10 10.8284 10.6716 11.5 11.5 11.5C12.3284 11.5 13 10.8284 13 10Z"
          fill="#0047F9"
        />
        <path
          d="M5 12C5 12.8284 4.32837 13.5 3.5 13.5C2.67163 13.5 2 12.8284 2 12C2 11.1716 2.67163 10.5 3.5 10.5C4.32837 10.5 5 11.1716 5 12Z"
          fill="#0047F9"
        />
        <path
          d="M13 4.5C13.5522 4.5 14 4.05229 14 3.5C14 2.94771 13.5522 2.5 13 2.5C12.4478 2.5 12 2.94771 12 3.5C12 4.05229 12.4478 4.5 13 4.5Z"
          fill="#0047F9"
        />
      </svg>
      智能生成
    </span>
  );
}
