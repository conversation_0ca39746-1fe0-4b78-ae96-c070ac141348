import {
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
  forwardRef,
  useRef,
} from 'react';
import { useAudioRecognize } from '../../hooks/useAudioRecognize';
import { css } from '@emotion/react';

export interface IAudioRecognizeProps {
  onStart?: () => void;
  onTextChange: (text: string) => void;
  onStop: () => void;
}

export interface IAudioRecognizeRefInterface {
  stop: () => void;
}

const AudioRecognize = forwardRef<
  IAudioRecognizeRefInterface,
  IAudioRecognizeProps
>((props, ref) => {
  const { startRecognize, stopRecognize, connecting } = useAudioRecognize();
  const { onStart, onStop, onTextChange } = props;

  const eventsRef = useRef<
    Pick<IAudioRecognizeProps, 'onStart' | 'onStop' | 'onTextChange'>
  >({
    onStop,
    onTextChange,
    onStart,
  });

  // 语音识别状态
  const [voiceRecognizeProcessing, setVoiceRecognizeProcessing] =
    useState(false);

  const handleVoiceStop = useCallback(async () => {
    setVoiceRecognizeProcessing(() => false);
    try {
      eventsRef.current?.onStop();
    } catch {}
    await stopRecognize();
  }, [stopRecognize]);

  const start = useCallback(async () => {
    await startRecognize({
      onTextChange: (text) => {
        eventsRef.current.onTextChange(text);
      },
      onRecognizeBegin: () => {
        setVoiceRecognizeProcessing(() => true);
        eventsRef.current?.onStart?.();
      },
    });
  }, [startRecognize]);

  useEffect(() => {
    start();
  }, [start]);

  useEffect(() => {
    return () => {
      handleVoiceStop();
    };
  }, [handleVoiceStop]);

  useEffect(() => {
    eventsRef.current.onStop = onStop;
    eventsRef.current.onTextChange = onTextChange;
    eventsRef.current.onStart = onStart;
  });

  useImperativeHandle(ref, () => ({
    stop: () => handleVoiceStop(),
  }));

  const stopBtn = (
    <span className="stop-voice" onClick={handleVoiceStop}>
      <svg
        width="15"
        height="12"
        viewBox="0 0 15 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8 1V3H9V0.5C9 0.223858 8.77614 0 8.5 0H0.5C0.223858 0 0 0.223858 0 0.5V11.5C0 11.7761 0.223858 12 0.5 12H8.5C8.77614 12 9 11.7761 9 11.5V9H8V11H1V1H8Z"
          fill="#0047F9"
        />
        <path
          d="M10.2316 3.25423L12.4774 5.50001L4.99994 5.49999L4.99994 6.49999L12.4774 6.50001L10.2316 8.74579L10.9387 9.4529L14.038 6.35356C14.2333 6.1583 14.2333 5.84172 14.038 5.64646L10.9387 2.54712L10.2316 3.25423Z"
          fill="#0047F9"
        />
      </svg>
      退出
    </span>
  );
  let text = '';
  if (connecting) {
    text = '连接中...';
  } else if (voiceRecognizeProcessing) {
    text = '麦克风语音输入中...';
  }
  return (
    <div
      className="qa-intelligent-input-comp-voice-processing-header"
      css={css`
        font-size: 12px;
      `}
    >
      <span>
        <svg
          width="15"
          height="14"
          viewBox="0 0 15 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.3284 6.36396C13.8905 4.80186 13.8905 2.2692 12.3284 0.707107L13.0355 0C14.9881 1.95262 14.9881 5.11845 13.0355 7.07107L12.3284 6.36396Z"
            fill="black"
          />
          <path
            d="M10 3.53552C10 5.46852 8.433 7.03552 6.5 7.03552C4.567 7.03552 3 5.46852 3 3.53552C3 1.60253 4.567 0.035522 6.5 0.0355224C8.433 0.0355222 10 1.60253 10 3.53552ZM9 3.53552C9 2.15481 7.88071 1.03552 6.5 1.03552C5.11929 1.03552 4 2.15481 4 3.53552C4 4.91623 5.11929 6.03552 6.5 6.03552C7.88071 6.03552 9 4.91623 9 3.53552Z"
            fill="black"
          />
          <path
            d="M12.4631 9.3883C12.797 9.54775 13 9.89018 13 10.2601L13 12.5355C13 12.8117 12.7761 13.0355 12.5 13.0355L0.500002 13.0355C0.223859 13.0355 6.7435e-07 12.8117 0 12.5355V10.2601C1.68587e-07 9.89018 0.203016 9.54775 0.536865 9.3883C2.3494 8.5226 4.36651 8.03552 6.5 8.03552C8.63349 8.03552 10.6506 8.5226 12.4631 9.3883ZM6.5 9.03552C4.5334 9.03552 2.67435 9.48125 1 10.2754L1 12.0355H12V10.2754C10.3257 9.48125 8.4666 9.03552 6.5 9.03552Z"
            fill="black"
          />
          <path
            d="M10.9142 2.12129C11.6953 2.90234 11.6953 4.16867 10.9142 4.94972L11.6213 5.65683C12.7929 4.48525 12.7929 2.58576 11.6213 1.41418L10.9142 2.12129Z"
            fill="black"
          />
        </svg>
        <span
          css={css`
            color: rgba(0, 0, 0, 1);
          `}
        >
          {text}
        </span>
      </span>
      {stopBtn}
    </div>
  );
});
export { AudioRecognize };
