.wrapper {
    width: auto;
    height: 38px;
    border-radius: 50px;
    background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%);
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    text-align: center;
    cursor: pointer;
    padding: 4px 20px;
    display: flex;
    align-items: center;

    .selectedArrow {
        width: 0;
        margin-right: 0;
        transition: all 0.2s;

        path {
            stroke: currentColor;
            stroke-dasharray: 14;
            stroke-dashoffset: 0;
        }

        @keyframes dash {
            from {
                stroke-dashoffset: 14;
            }

            to {
                stroke-dashoffset: 0;
            }
        }
    }
}

.active {
    background: linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
    color: #0047F9 !important;
    
    .selectedArrow {
        width: 14px;
        margin-right: 6px;

        path {
            animation: dash .3s linear;
            animation-fill-mode: both;
        }
    }
}