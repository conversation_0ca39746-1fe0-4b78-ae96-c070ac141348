import React from 'react';
import Styles from './style.module.less';

export interface IResourceCategoryButtonProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'onClick'> {
  active: boolean;
  text?: string;
  children?: string | React.ReactNode;
}

export function ResourceCategoryButton(props: IResourceCategoryButtonProps) {
  const { active, text, children, ...rest } = props;
  const cls = [Styles.wrapper];
  if (active) {
    cls.push(Styles.active);
  }
  return (
    <div {...rest} className={cls.join(' ')}>
      <svg
        className={Styles.selectedArrow}
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M2 7L5 10.5L11.5 4" strokeLinejoin="round" />
      </svg>
      {text || children}
    </div>
  );
}
