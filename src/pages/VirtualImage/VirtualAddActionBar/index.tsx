import { LoginStateAtom } from '@/model/login';
import { MiniQRCodeDialog } from '@/pages/VirtualImage/MiniQRCodeDialog';
import React, { useCallback, useEffect, useState, useTransition } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRecoilState } from 'recoil';
import { ChevronDownIcon, SearchIcon } from 'tdesign-icons-react';
import { Button, Dropdown, Input, MessagePlugin } from 'tdesign-react';
import { css } from '@emotion/react';
import { useImageQuota } from '@/pages/VirtualImage/hooks/useImageQuota';

const { DropdownMenu, DropdownItem } = Dropdown;

export interface IVirtualAddActionBarProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'style'> {
  onSearch: (searchText: string) => void;
  enableAutoSearch?: boolean;
  searchPlaceholder?: string;
}
// 定制新形象按钮逻辑
export function VirtualAddActionBar(props: IVirtualAddActionBarProps) {
  const { onSearch, style, enableAutoSearch, searchPlaceholder } = props;
  const [searchVal, setSearchVal] = useState('');
  const navigate = useNavigate();
  const { effectiveQuota, quota } = useImageQuota();

  // const [qrCode, setQRCode] = useState<string>('');
  const [loginState] = useRecoilState(LoginStateAtom);
  const [searching, startDeferSearch] = useTransition();
  const [showMiniQRCode, setShowMiniQRCode] = useState(false);

  // const [testQRCodeState, setTestQRCodeState] =
  //   useState<IBaseQRCodeProps['qrcodeState']>('normal');

  // (window as any).__setTestQRCodeState = setTestQRCodeState;

  // 搜索
  const doSearch = useCallback(() => {
    onSearch(searchVal);
  }, [onSearch, searchVal]);

  // 新增
  const doAdd = async (type: string) => {
    if (type === 'PC') {
      navigate('/virtual-image/add');
    }
    if (type === 'miniProgram') {
      // const ins = MessagePlugin.loading('加载中，请稍后~', 0);
      try {
        if (loginState?.openid) {
          setShowMiniQRCode(true);
        }
        // MessagePlugin.close(ins);
        // showDialogPlugin(img);
      } catch (error) {
        // MessagePlugin.close(ins);
        MessagePlugin.error('获取二维码失败', 2000);
      }
    }
  };

  useEffect(() => {
    if (enableAutoSearch) {
      startDeferSearch(() => {
        doSearch();
      });
    }
  }, [doSearch, enableAutoSearch]);

  return (
    <div className="flex pt-8" style={{ ...style }}>
      <div className="flex-1">
        <Dropdown
          disabled={!effectiveQuota}
          minColumnWidth={130}
          trigger="click"
        >
          <Button
            icon={<ChevronDownIcon />}
            theme="primary"
            className="gradient-primary"
          >
            新增形象定制
          </Button>

          <DropdownMenu>
            <DropdownItem value={1} onClick={() => doAdd('PC')}>
              通过PC端定制
            </DropdownItem>
            <DropdownItem value={2} onClick={() => doAdd('miniProgram')}>
              通过小程序端定制
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Button
          theme="default"
          className="gradient-default cursor-default ml-8"
          style={{ borderRadius: '0 20px 20px 0' }}
        >
          剩余额度：
          <span
            css={css`
              font-weight: 600;
              background: linear-gradient(
                89deg,
                #0153ff -0.01%,
                #8649ff 147.74%
              );
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            `}
          >
            {quota.remain_personal_quota}
          </span>
        </Button>
      </div>
      <Input
        style={{ width: '364px' }}
        placeholder={searchPlaceholder}
        value={searchVal}
        onChange={setSearchVal}
        suffix={searching && '...'}
        suffixIcon={<SearchIcon />}
        {...(!enableAutoSearch
          ? {
              onEnter: () => doSearch(),
              suffixIcon: <SearchIcon onClick={() => doSearch()} />,
            }
          : undefined)}
      />
      <MiniQRCodeDialog
        show={showMiniQRCode}
        onClose={() => setShowMiniQRCode(false)}
      />
    </div>
  );
}
