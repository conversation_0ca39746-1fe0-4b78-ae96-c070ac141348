import { useLatest, usePrevious } from 'ahooks';
import { useQRCode } from '../hooks/useQRCode';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { Dialog } from 'tdesign-react';
import { LoginStateAtom } from '@/model/login';
import { useRecoilState } from 'recoil';
import { BaseQRCode } from '@/components/BaseQRCode';

export interface IMiniQRCodeDialogProps {
  show: boolean;
  onClose: () => void;
}
export function MiniQRCodeDialog(props: IMiniQRCodeDialogProps) {
  const { show, onClose } = props;
  const prevShow = usePrevious(show);
  const {
    getQRCode,
    loading: isQRCodeLoading,
    qrcode: qrCode,
    reset: resetQRCode,
    qrcodeState,
  } = useQRCode();

  const latestGetQRCode = useLatest(getQRCode);
  const [loginState] = useRecoilState(LoginStateAtom);
  useLayoutEffect(() => {
    if (!loginState) return;
    if (qrcodeState === 'scan-success') {
      setTimeout(async () => {
        await latestGetQRCode.current({ openid: loginState.openid });
      }, 2000);
    }
  }, [latestGetQRCode, loginState, qrcodeState]);

  useEffect(() => {
    if (!loginState) return;
    if (!prevShow && show) {
      latestGetQRCode.current({
        openid: loginState.openid,
      });
    }
  }, [latestGetQRCode, loginState, prevShow, show]);

  return (
    <Dialog
      visible={show}
      destroyOnClose
      footer={null}
      closeOnOverlayClick={false}
      width={330}
      onClose={() => {
        onClose();
        resetQRCode();
      }}
      header="通过小程序定制"
    >
      <BaseQRCode
        title="请扫葵花码进入小程序开始定制"
        loading={isQRCodeLoading}
        qrCode={qrCode}
        imgStyle={{
          height: '260px',
          width: '260px',
          borderRadius: '8px',
        }}
        loadingStyle={{
          height: '260px',
        }}
        style={{
          gap: '20px',
        }}
        onStateChange={(state, prev) => {
          console.log('二维码状态变更，', 'prev', prev, 'current', state);
          // setTestQRCodeState(state);
        }}
        onRefresh={() => {
          getQRCode({
            openid: loginState?.openid || '',
          });
        }}
        qrcodeState={qrcodeState}
      />
    </Dialog>
  );
}
