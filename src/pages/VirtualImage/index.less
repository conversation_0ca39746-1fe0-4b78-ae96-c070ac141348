.virtual-image-page {
  width: 100%;

  .main_content {
    padding: 0;
    overflow: initial;
    border: 1px solid #e3e5eb;
    border-radius: 4px;
  }

  .t-tabs__content {
    width: 100%;
    padding: 20px;
  }

  .t-tab-panel {
    height: 100%;
  }


  &-title {
    color: rgba(0, 0, 0, 0.9);
    font-style: normal;
    font-weight: 600;
    margin-bottom: 16px;
  }
  &-content {
    padding: 20px 16px;
    border-radius: 8px;
    background: #ffffff;
    height: calc(100vh - 120px);

    &-inner-box {
      height: 0;
      flex: 1;
      border-radius: 4px;

      .action_bar {
        padding: 8px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;

        .btn {
          width: 136px;
          height: 32px;
          color: rgba(255, 255, 255, 0.9);
          text-align: center;
          line-height: 32px;
          border-radius: 4px;
          background-image: linear-gradient(
            to right,
            rgba(1, 83, 255, 1),
            rgba(46, 127, 253, 1),
            rgba(193, 163, 253, 1)
          );
          cursor: pointer;
          user-select: none;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          .add_select {
            text-align: left;
            position: absolute;
            top: 120%;
            right: 0;
            left: 0;
            background: #ffffff;
            color: #000;
            border: 1px solid #e3e5eb;
            border-radius: 7px;
            z-index: 999;
            div {
              padding: 0 15px;
            }
            div:hover {
              background: #f2f3ff;
            }
          }
        }

        .search_input {
          width: 280px;
        }
      }

      .loading_wrap {
        width: 100%;
        height: calc(100vh - 250px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .table {
        width: 100%;
        height: calc(100vh - 310px);
        overflow: hidden;
        display: block;

        .t-table {
          width: 100%;
          height: 100%;

          &::-webkit-scrollbar {
            width: 4px;
            background-color: rgba(0, 0, 0, 0.06);

            &-thumb {
              background-color: rgba(0, 0, 0, 0.2);
              border-radius: 2px;
            }
          }

          .t-table__header > tr > th,
          .t-table__body > tr > td {
            vertical-align: middle;
            text-align: center;
          }
        }

        .poster {
          width: 72px;
          height: 137px;
          background: #e8e8e8;
          border: 1px solid #e8e8e8;
        }

        .status-table__cell {
          text-align: left;
          display: flex;
        }

        .status_tag {
          --c: #f4f6ff;
          position: relative;
          height: 24px;
          padding: 0 10px 0 4px;
          line-height: 24px;
          font-size: 14px;
          color: rgba(0, 10, 41, 0.26);
          background: linear-gradient(to right, var(--c), #faf5fc);
          display: inline-block;
          border-radius: 0 12px 16px 0;

          &::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            bottom: 0;
            width: 6px;
            border-radius: 4px;
            transform: skewX(-10deg);
            background: var(--c);
            display: block;
          }

          &.video_status_2 {
            --c: rgb(248, 210, 18);
            color: rgb(198, 119, 0);
            background: linear-gradient(to right, var(--c), rgb(247, 235, 93));

            &::before {
              background: var(--c);
            }
          }

          &.video_status_3,
          &.video_status_4 {
            --c: #ffd513;
            color: #b09d00;
            background: linear-gradient(to right, var(--c), #fff181);

            &::before {
              background: var(--c);
            }
          }

          &.video_status_1001 {
            --c: #e44527;
            color: #fff;
            background: linear-gradient(to right, var(--c), #f7a59e);

            &::before {
              background: var(--c);
            }
          }

          &.video_status_1000 {
            --c: rgba(235, 248, 240, 1);
            color: rgba(0, 198, 83, 1);
            background: linear-gradient(
              to right,
              var(--c),
              rgba(222, 248, 232, 1)
            );

            &::before {
              background: var(--c);
            }
          }
        }
        .score {
          span {
            color: rgba(0, 71, 249, 1);
            cursor: pointer;
            margin-left: 8px;
          }
          &_not {
            color: rgba(0, 0, 0, 0.3);
          }
        }
        .more {
          text-align: left;
          a {
            color: rgba(0, 71, 249, 1);
          }
        }
      }
      .t-table__header {
        tr > th {
          background-color: #f8f6fb;
        }
      }

      .t-table__content {
        min-height: 320px;
        height: calc(100% - 64px);
      }

      .t-table__pagination-wrap {
        height: 56px;
        border-top: 1px solid #E3E5EB;
      }
    }
  }
}
