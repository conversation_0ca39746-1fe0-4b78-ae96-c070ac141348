import { RespType } from '@/pb/config';
import { ShuzirenImageMngSvr } from '@/pb/pb';
import { usePagination, useRequest } from 'ahooks';
import { MessagePlugin } from 'tdesign-react';

export type ImageTaskInfo = RespType<
  typeof ShuzirenImageMngSvr.GetImageInforList
>['image_task_info_list'][number];

export const useImageInfo = () => {
  const STATUS_TEXT = {
    1: '素材提交',
    2: '开始训练',
    3: '开始效果确认',
    4: '效果确认提交',
    1000: '训练完成',
    1001: '训练失败',
  } as const;

  // 获取形象定制列表
  const queryImageInfoList = async ({
    searchText,
    current,
    pageSize,
  }: {
    searchText: string;
    current: number;
    pageSize: number;
  }): Promise<{ total: number; list: ImageTaskInfo[] }> => {
    try {
      const res = await Shu<PERSON>renImageMngSvr.GetImageInforList({
        search_key: searchText,
        page_num: current,
        page_size: pageSize,
      });

      return { total: res.query_count, list: res.image_task_info_list };
    } catch (e) {
      return { total: 0, list: [] };
    }
  };
  const {
    loading,
    pagination,
    data: imageInfo,
    run: getImageInfoList,
    params,
  } = usePagination(queryImageInfoList, { manual: true });

  // 删除形象定制
  const { runAsync: deleteImageInfo, loading: deleteLoading } = useRequest(
    (image_id: string) => {
      if (deleteLoading) {
        return Promise.reject('loading');
      }
      if (!image_id) {
        return Promise.reject('image_id 不能为空');
      }
      return ShuzirenImageMngSvr.DeleteImageId({
        image_id,
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        void MessagePlugin.success({ content: '删除成功' });
      },
      onError: (error) => {
        console.log(error);
      },
    }
  );

  // ImagePractiseStatusConfirm 效果确认
  const { runAsync: imagePractiseStatusConfirm, loading: confirmLoading } =
    useRequest(
      ({
        operate,
        image_id,
        reason = '',
      }: {
        operate: string;
        image_id: string;
        reason?: string;
      }) => {
        if (confirmLoading) {
          return Promise.reject('loading');
        }
        if (!image_id) {
          return Promise.reject('image_id 不能为空');
        }
        return ShuzirenImageMngSvr.ImagePractiseStatusConfirm({
          operate,
          image_id,
          reason,
        });
      },
      {
        manual: true,
        onError: (error) => {
          console.log(error);
          void MessagePlugin.error({ content: '系统繁忙，请稍后再试~~' });
        },
      }
    );

  // 获取形象定制详情
  const { runAsync: getImageDetail, loading: detailLoading } = useRequest(
    (image_id) => {
      if (detailLoading) {
        return Promise.reject('loading');
      }
      if (!image_id) {
        return Promise.reject('image_id 不能为空');
      }
      return ShuzirenImageMngSvr.GetImageDetail({
        image_id,
      });
    },
    {
      manual: true,
      onError: (error) => {
        void MessagePlugin.error({ content: '系统繁忙，请稍后再试~~' });
        console.log(error);
      },
    }
  );

  // 获取训练进度
  const { runAsync: getImagePractiseStatus, loading: practiseLoading } =
    useRequest(
      (image_id) => {
        if (practiseLoading) {
          return Promise.reject('loading');
        }
        if (!image_id) {
          return Promise.reject('image_id 不能为空');
        }
        return ShuzirenImageMngSvr.QueryImagePractiseStatus({ image_id });
      },
      {
        manual: true,
      }
    );

  // 更新形象定制分数
  const { run: updateImageScore, loading: updateLoading } = useRequest(
    ({ nature_score, affinity_score, fluency_score, image_id }) => {
      if (updateLoading) {
        return Promise.reject('loading');
      }
      if (!image_id || !nature_score || !affinity_score || !fluency_score) {
        return Promise.reject('缺少参数');
      }
      return ShuzirenImageMngSvr.UpdateImageScore({
        nature_score,
        affinity_score,
        fluency_score,
        image_id,
      });
    },
    {
      manual: true,
    }
  );

  // 操作日志
  const { runAsync: getImageLogList, loading: logLoading } = useRequest(
    ({ image_id, log_level }) => {
      if (logLoading) {
        return Promise.reject('loading');
      }
      if (!image_id) {
        return Promise.reject('image_id 不能为空');
      }
      return ShuzirenImageMngSvr.GetImageLogList({
        image_id,
        log_level,
      });
    },
    {
      manual: true,
    }
  );

  return {
    loading,
    pagination,
    imageInfo,
    getImageInfoList,
    params,
    deleteImageInfo,
    imagePractiseStatusConfirm,
    confirmLoading,
    getImageDetail,
    detailLoading,
    getImageLogList,
    getImagePractiseStatus,
    updateImageScore,
    STATUS_TEXT,
  };
};
