/**
 * <AUTHOR>
 * @date 2024/10/16 下20:36
 * @desc useImageQuota
 */

import { useRequest, useResetState } from 'ahooks';
import { useEffect, useMemo } from 'react';
import { MessagePlugin } from 'tdesign-react';
import {
  QueryImageQuota,
  QueryImageQuotaResponse,
} from '@/pb/api/MetaFeedbackSvr';

export const useImageQuota = () => {
  const [quota, setQuota, resetQuota] = useResetState<
    QueryImageQuotaResponse['data']
  >({
    remain_total_quota: 0,
    remain_personal_quota: 0,
  });
  const { runAsync, loading, error } = useRequest(
    () => {
      return QueryImageQuota({});
    },
    {
      manual: true,
      throttleWait: 500,
    }
  );

  useEffect(() => {
    // 请求
    runAsync()
      .then((res) => {
        setQuota(res);
      })
      .catch((e) => {
        console.error(e, '获取数据失败');
        resetQuota();
        MessagePlugin.error({ content: '获取数据失败,请稍后再试' }).then();
      });
  }, [resetQuota, runAsync, setQuota]);

  const effectiveQuota = useMemo(() => {
    return quota.remain_total_quota > 0 && quota.remain_personal_quota > 0;
  }, [quota]);

  return { quota, effectiveQuota, loading, error };
};
