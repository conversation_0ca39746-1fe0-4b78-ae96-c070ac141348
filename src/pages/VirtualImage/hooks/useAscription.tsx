import { useMemo, useState } from 'react';

export interface IUseAscriptionOptions {
  choices: {
    value: string;
    name: string;
  }[];
  defaultValue?: string;
}

export function useAscription(options: IUseAscriptionOptions) {
  const [currentValue, setCurrentValue] = useState(
    options.defaultValue || 'all'
  );
  const memoChoices = useMemo(() => {
    return [
      {
        value: 'all',
        name: '全部',
      },
      ...options.choices,
    ];
  }, [options.choices]);

  return {
    currentValue,
    setCurrentValue,
    choices: memoChoices,
  };
}
