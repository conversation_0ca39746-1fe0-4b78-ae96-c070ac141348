import { IBaseQRCodeProps } from '@/components/BaseQRCode';
import { AssetsLibConfigContext } from '@/configs/config_context';
import { Development_1 } from '@/pb/pb';
import { useRequest, useUnmount } from 'ahooks';
import to from 'await-to-js';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';

export const useQRCode = () => {
  const [qrcode, setQRCode] = useState('');
  const [qrcodeState, setQRCodeState] =
    useState<IBaseQRCodeProps['qrcodeState']>('normal');
  const [tempToken, setTempToken] = useState('');
  const configCtx = useContext(AssetsLibConfigContext);
  const pollingTimeout = useRef<NodeJS.Timeout>();
  // 获取微信二维码
  const {
    runAsync: getQRCode,
    loading,
    error,
  } = useRequest(
    async ({ openid }: { openid: string }) => {
      if (loading) {
        return Promise.reject('loading');
      }
      if (!openid) {
        return Promise.reject('openid 不能为空');
      }
      setQRCode('');
      setTempToken('');
      setQRCodeState('normal');
      const resp = await Development_1.GetUnlimitedQRCode({
        scene: openid,
      });
      const { qr_code: code, custom_token } = resp;
      setQRCode(code);
      if (custom_token) {
        setTempToken(custom_token);
      }
      return code;
    },
    {
      manual: true,
      onError: (error) => {
        console.log(error);
      },
    }
  );

  const reset = useCallback(() => {
    setQRCodeState('normal');
    clearTimeout(pollingTimeout.current);
  }, []);

  useEffect(() => {
    if (!configCtx.virtualImageCustom?.OTQRCode) return;
    if (!qrcode) return;
    if (!tempToken) return;
    const polling = async () => {
      // 轮询后台接口
      const [_, resp] = await to(
        Development_1.GetCustomTokenStatus({
          custom_token: tempToken,
        })
      );

      let continued = false;
      if (resp?.status === 'USED') {
        setQRCodeState('scan-success');
      } else if (resp?.status === 'INVALID') {
        setQRCodeState('scan-timeout');
      } else {
        continued = true;
      }
      if (continued) {
        pollingTimeout.current = setTimeout(polling, 2000);
      }
    };
    polling();
    return () => {
      clearTimeout(pollingTimeout.current);
    };
  }, [configCtx.virtualImageCustom?.OTQRCode, qrcode, tempToken]);

  return {
    error,
    qrcode,
    getQRCode,
    loading,
    qrcodeState,
    reset,
  };
};
