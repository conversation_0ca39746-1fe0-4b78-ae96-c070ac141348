// 视频示例组件：

import { css } from '@emotion/react';
import { Image } from 'tdesign-react';
import mainImg from '@/assets/images/virtual-image/main.png';
import noMouthImg from '@/assets/images/virtual-image/no-mouth.png';
import massHair from '@/assets/images/virtual-image/mass-hair.png';
import headImg from '@/assets/images/virtual-image/head.png';
import largeAction from '@/assets/images/virtual-image/large-action.png';

export type IVirtualImageExampleProps = Pick<
  React.HTMLAttributes<HTMLElement>,
  'style' | 'className'
>;

const TipsList = [
  '发型规整无碎发',
  '服装与背景有色彩区别',
  '眼睛正视镜头',
  '嘴部无遮挡',
  '有嘴部闭合静止画面',
];

const ErrorTips: { img: string; text: string }[] = [
  {
    text: '挡住嘴巴',
    img: noMouthImg,
  },
  {
    text: '发型太碎',
    img: massHair,
  },
  {
    text: '大幅歪头',
    img: headImg,
  },
  {
    text: '指向大动作',
    img: largeAction,
  },
];

export function VirtualImageExample(props: IVirtualImageExampleProps) {
  const { style, className } = props;
  const cls = ['flex', 'gap-5', 'flex-col'];
  if (className) {
    cls.push(className);
  }
  return (
    <div
      // css={css`
      //   display: flex;
      //   gap: 20px;
      // `}
      className={cls.join(' ')}
      style={{ ...style }}
    >
      <div className="flex flex-col gap-2">
        <h3>正确示例</h3>
        <section
          className="flex items-center"
          style={{
            gap: '17px',
          }}
        >
          <Image style={{ width: '140px', height: 'auto' }} src={mainImg} />
          <div className="flex flex-col gap-[16px]">
            {TipsList.map((item) => (
              <div className="flex items-center gap-1">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 17.75C13.8325 17.75 17.75 13.8325 17.75 9C17.75 4.16751 13.8325 0.25 9 0.25C4.16751 0.25 0.25 4.16751 0.25 9C0.25 13.8325 4.16751 17.75 9 17.75ZM5.06653 9.69974C4.82267 9.45583 4.82269 9.06043 5.06657 8.81655V8.81655C5.31044 8.57268 5.70583 8.57265 5.94974 8.81649L7.75 10.6162L12.0485 6.31722C12.2927 6.07299 12.6886 6.07298 12.9328 6.31719V6.31719C13.177 6.5614 13.177 6.95734 12.9328 7.20154L8.59392 11.5399C8.12784 12.006 7.37222 12.0059 6.90621 11.5398L5.06653 9.69974Z"
                    fill="url(#paint0_linear_19711_53967)"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_19711_53967"
                      x1="0.25"
                      y1="0.25"
                      x2="17.75"
                      y2="0.25"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#00C653" />
                      <stop offset="1" stopColor="#01D766" />
                    </linearGradient>
                  </defs>
                </svg>
                <span className="font-normal">{item}</span>
              </div>
            ))}{' '}
          </div>
        </section>
      </div>
      <div className="flex flex-col gap-2">
        <h3>错误示例</h3>
        <div className="flex gap-2">
          {ErrorTips.map((tip) => {
            return (
              <div className="flex flex-col gap-2 items-center">
                <Image src={tip.img} className="w-20" />
                <span className="font-normal">{tip.text}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
