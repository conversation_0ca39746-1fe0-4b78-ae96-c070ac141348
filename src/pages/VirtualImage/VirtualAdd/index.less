.virtual-image-add-page {
  position: relative;
  display: flex;
  flex-direction: column;

  &-breadcrumb {
    color: rgba(0, 0, 0, 0.90);
    font-weight: 600;
    margin-bottom: 16px;
  }

  &-card {
    background: #ffffff;
    border-radius: 8px;

    .header {
      height: 60px;
      line-height: 60px;
      padding: 0 22px;
      font-size: 18px;
      font-weight: 500;
      color: #000000;
      border-bottom: 1px solid #E2E8F3;
    }

    .content {
      padding: 24px 22px;

      .sub-title {
        color: rgba(0, 0, 0, 0.90);
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
      }
    }

    .t-form {
      .t-form__label {
        color: rgba(0, 0, 0, 0.6);
      }
    }

    .t-radio-group {
      border-radius: 3px;
      background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);

      .t-radio-button__label {
        width: 100%;
        text-align: center;
      }
    }

    .t-radio-group.t-radio-group--filled .t-radio-button.t-is-checked {
      color: #0047F9;
    }
  }

  &-upload-box {
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 8px;
    background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);

    .tips-zone {
      flex-basis: 400px;
      flex-shrink: 0;
      display: flex;
      // flex: 1;
      flex-direction: column;
      justify-content: center;
      margin-left: 20px;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }

    .video-demo-zone {
      display: flex;
      flex-direction: column;
      height: 100%;
      // width: 212px;

      .font-medium {
        margin-bottom: 8px;
        font-size: 16px;
      }

      .video-demo {
        // width: 142px;
        min-height: 252px;
        border-radius: 8px;
        display: flex;
        height: 356px;
        flex-direction: column;
        justify-content: center;
        background: rgba(76, 76, 76, 1);
      }
    }

    .draggable-upload-comp {
      .t-upload__dragger {
        width: 380px;
        height: 280px;
        background: #ffffff;
      }
    }

    .t-form__item {
      margin-bottom: 0;
    }
  }

  &-supplement-upload-box {
    display: inline-flex;
    align-items: center;
    border-radius: 8px;
    padding: 8px;
    background: linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%);

    .draggable-upload-comp {
      .t-upload__dragger {
        width: 380px;
        height: 138px;
        background: #ffffff;
      }
    }

    .tips {
      width: 100px;
      margin-left: 8px;
      color: rgba(0, 0, 0, 0.40);
    }

    .t-form__item {
      margin-bottom: 0;
    }
  }

  &-footer {
    display: flex;
    gap: 12px;
    padding: 16px 36px;
    position: fixed;
    background: #fff;
    bottom: 0;
    width: 100%;
    margin-left: -40px;
  }

  &-course-video {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    right: -8px;
    bottom: 200px;
    padding: 8px 12px;
    border-radius: 8px;
    background: linear-gradient(88deg, #C2D6FF -0.01%, #CDE0FF 49.89%, #F0E9FF 99.99%);
    line-height: 22px;
    transition: 0.2s all;

    .play-circle-icon {
      display: block;
    }

    .fullscreen-exit-icon {
      position: absolute;
      right: 12px;
      top: 12px;
      display: none;
    }

    .video-demo {
      position: relative;
      display: none;

      .mask {
        display: flex;
        align-items: center;
        justify-content: center;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        position: absolute;
        border-radius: 4px;
        background: rgba(82, 82, 82, 0.48);
        cursor: pointer;
        z-index: 2;
      }
    }

    &:hover {
      right: 4px;

      .play-circle-icon {
        display: none;
      }

      .fullscreen-exit-icon {
        display: block;
      }

      .video-demo {
        display: block;
      }
    }
  }

  &-submit-card {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: #FFFFFF;
    height: calc(100vh - 140px);
  }
}