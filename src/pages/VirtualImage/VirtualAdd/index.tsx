/* eslint-disable @typescript-eslint/no-non-null-assertion,no-param-reassign */
/**
 * <AUTHOR>
 * @date 2024/6/26 下午4:57
 * @desc index
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Breadcrumb,
  Button,
  Checkbox,
  DialogPlugin,
  Form,
  FormRules,
  Input,
  Link,
  MessagePlugin,
  Textarea,
  UploadFile,
} from 'tdesign-react';
import { useNavigate } from 'react-router-dom';
import './index.less';
import {
  DraggableUpload,
  IDraggableUploadRef,
} from '@/components/DraggableUpload';
import { TrainResultPage } from '@/components/TrainResultPage';
import {
  DownloadIcon,
  FullscreenExitIcon,
  PlayCircleIcon,
} from 'tdesign-icons-react';
import { downloadVideo } from '@/utils';
import { ShuzirenImageMngSvr } from '@/pb/pb';
import { CosUpload } from '@/pages/VirtualImage/VirtualAdd/tencentCloud';
import { CommonScriptLoading } from '@/components/CommonScriptLoading';
import { RespError } from '@/pb/config';
import { css } from '@emotion/react';
import overrideStyles from './override.module.less';
import {
  FlattenSelect,
  IIFlattenSelectProps,
} from '@/components/FlattenSelect';
import { CategoryButton } from '@/components/Category/CategoryButton';
import { VirtualImageUploader } from '@/pages/VirtualImage/VirtualAdd/VirtualImageUploader';
import { AssetsLibConfigContext } from '@/configs/config_context';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { VirtualImageExample } from '@/pages/VirtualImage/VirtualAdd/VirtualImageExample';
import { useSystem } from '@/hooks/useSystem';
import { IVideo2PosterRef, Video2Poster } from '@/components/Video2Poster';

const { FormItem } = Form;

const { BreadcrumbItem } = Breadcrumb;

interface FormValues {
  need_matting: 'matting' | 'noMatting';
  video_url: string;
  driver_way: ('text_driver' | 'voice_driver')[];
  text_driver?: string;
  voice_driver_url?: string;
  anchor_name: string;
  image_sexy: '1' | '0';
  notes?: string;
  accredit_video_url: string;
  should_tran_voice?: boolean;
}
const classPrefix = 'virtual-image-add-page';

const ERROR_MSG = {
  ALREADY_EXISTS: '已存在该数字人',
  IMAGE_TOTAL_QUOTA_NOT_ENOUGH: '训练配额不足',
  IMAGE_PERSONAL_QUOTA_NOT_ENOUGH: '个人训练配额不足',
} as const;
// 指引
const GUIDE_VIDEO =
  'https://avatarcdn.pay.qq.com/material/86898e486a808acf295d8214b43360f4.mp4';
// 授权文件
const ZIP_EXAMPLE = {
  win: 'https://pagedoo.pay.qq.com/material/@platform/e398459ed1f5f3d32fd9322f2a35a35a.zip',
  others:
    'https://avatarcdn.pay.qq.com/material/fd8dabd9e1c4eac21425173d002df587.zip',
};
// localstorage key
const STORAGE_KEY = 'virtual-image-guide-video-showed';

const rules: FormRules<FormValues> = {
  // video_url: [{ required: true, message: '请上传训练视频', type: 'error' }],
  // accredit_video_url: [
  //   { required: true, message: '请上传授权视频', type: 'error' },
  // ],
  anchor_name: [{ required: true, message: '请输入音色名称', type: 'error' }],
  image_sexy: [{ required: true, message: '请选择数字人性别', type: 'error' }],
};

export default function VoiceAdd() {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [status, setStatus] = useState<'init' | 'submit'>('init');
  const [floatVideoVisible, setFloatVideoVisible] = useState(false);
  const [imageInfo, setImageInfo] = useState({
    imageId: '',
    anchorName: '',
  });
  const [stepName, setStepName] = useState('');
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const imageVideoUploadRef = useRef<IDraggableUploadRef>(null);
  const imageVoiceUploadRef = useRef<IDraggableUploadRef>(null);
  const accreditFileUploadRef = useRef<IDraggableUploadRef>(null);
  const video2fileRef = useRef<IVideo2PosterRef>(null);
  const { isWindows } = useSystem();

  useEffect(() => {
    const isShow = localStorage.getItem(STORAGE_KEY);
    if (!isShow) {
      showDialog();
      localStorage.setItem(STORAGE_KEY, 'true');
    }
  }, []);

  const handleSubmit = async () => {
    const res = await form.validate();
    if (res === true) {
      const uploadValue: Record<string, string> = {};
      const formValue = form.getFieldsValue(true) as FormValues;
      console.debug(formValue, 'formValue');
      let imageID = '';
      let resolution = '';
      if (formValue.anchor_name === imageInfo.anchorName) {
        imageID = imageInfo.imageId;
      }

      // 表单校验
      const imageVideoUploadFiles = imageVideoUploadRef.current?.getFiles();
      if (!imageVideoUploadFiles?.length) {
        void MessagePlugin.error('请上传训练视频');
        return;
      }
      const imageAccreditFiles = accreditFileUploadRef.current?.getFiles();
      if (!imageAccreditFiles?.length) {
        void MessagePlugin.error('请上传授权文件');
        return;
      }

      try {
        if (!imageID) {
          setStepName('初始化数字人信息中');
          // 初始化一个数字人
          const imageRes = await ShuzirenImageMngSvr.CreateImageID({
            anchor_name: formValue.anchor_name,
            task_name: `PC_${formValue.anchor_name}_${Date.now()}`,
          });
          imageID = imageRes.image_id;
          // 保存一份
          setImageInfo({ imageId: imageID, anchorName: formValue.anchor_name });
          if (!imageID) {
            void MessagePlugin.error('创建数字人失败');
            return;
          }
        }
        setStepName('获取腾讯云密钥中');
        // 获取上传授权
        const credentials = await ShuzirenImageMngSvr.GetUploadCredentials({
          image_id: imageID,
        });

        // 获取上传路径
        const filePath = new URL(credentials.PathPrefix).pathname;
        // 初始化cos
        const TXCloudCOS = new CosUpload(credentials);

        setStepName('上传数字人训练视频中');
        // 上传数字人训练视频
        const imageVideoUploadFile = imageVideoUploadFiles[0];
        const { destroy: destroyImageVideoUrl, url: imageVideoUrl } =
          getVideoUrl(imageVideoUploadFile);
        resolution = await getVideoResolution(imageVideoUrl);
        try {
          await TXCloudCOS.uploadFileToCos({
            fileName: `${filePath}video/${imageVideoUploadFile.name}`,
            file: imageVideoUploadFile.raw!,
            onStart: () => {
              setUploadProgress(0);
            },
            onProgress: (params) => {
              console.log('上传数字人进度 ', params.percent);
              setUploadProgress(params.percent);
            },
            onFinish: () => {
              setUploadProgress(null);
            },
          });
        } finally {
          setUploadProgress(null);
        }
        console.debug('上传数字人训练文件成功');
        uploadValue.material_video_url = `${credentials.PathPrefix}video/${imageVideoUploadFile.name}`;
        // uploadValue.material_video_url = formValue.video_url;

        if (video2fileRef.current && imageVideoUploadFiles[0].raw) {
          // 上传数字人训练视频封面
          setStepName('上传数字人训练视频封面中');
          const uploadName = `${
            imageVideoUploadFile.name!.split('.')[0]
          }_thumb.png`;
          try {
            const thumbCosRes = await video2fileRef.current.getPoster(
              imageVideoUrl
            );
            if (thumbCosRes?.blob) {
              await TXCloudCOS.uploadFileToCos({
                fileName: `${filePath}photo/${uploadName}`,
                file: thumbCosRes.blob,
                onStart: () => {
                  setUploadProgress(0);
                },
                onProgress: (params) => {
                  console.log('上传数字人封面进度 ', params.percent);
                  setUploadProgress(params.percent);
                },
                onFinish: () => {
                  setUploadProgress(null);
                },
              });
            }
          } finally {
            setUploadProgress(null);
          }
          console.debug('上传数字人训练文件成功');
          uploadValue.material_background_pic_url = `${credentials.PathPrefix}photo/${uploadName}`;
        }
        destroyImageVideoUrl();

        // 上传数字人制作授权书
        // TODO: 后端还没有支持pdf
        setStepName('上传数字人制作授权书');
        const imageAccreditFile = imageAccreditFiles[0];
        try {
          await TXCloudCOS.uploadFileToCos({
            fileName: `${filePath}idcard/${imageAccreditFile.name}`,
            file: imageAccreditFile.raw!,
            onStart: () => {
              setUploadProgress(0);
            },
            onProgress: ({ percent }) => {
              setUploadProgress(percent);
            },
            onFinish: () => {
              setUploadProgress(null);
            },
          });
        } finally {
          setUploadProgress(null);
        }
        console.debug('上传数字人制作授权书成功');
        uploadValue.identity_video_cos_url = `${credentials.PathPrefix}idcard/${imageAccreditFile.name}`;
        // 上传数字人语音驱动
        if (
          imageVoiceUploadRef.current &&
          formValue.driver_way.includes('voice_driver')
        ) {
          setStepName('上传数字人语音驱动');
          const files = imageVoiceUploadRef.current.getFiles();
          if (files.length) {
            await TXCloudCOS.uploadFileToCos({
              fileName: `${filePath}audio/${files[0].name}`,
              file: files[0].raw!,
            });
            console.debug('上传数字人语音驱动成功');
            uploadValue.voice_driver_url = `${credentials.PathPrefix}audio/${files[0].name}`;
          }
        }

        setStepName('开启数字人训练中');
        // 视频
        await ShuzirenImageMngSvr.SaveVideoInfor({
          resolution,
          image_id: imageID,
          // 1:有-不抠图-noMatting  0:没有
          is_image_have_background:
            formValue.need_matting === 'noMatting' ? 1 : 0,
          material_video_url: uploadValue.material_video_url || '',
          material_background_pic_url:
            uploadValue.material_background_pic_url || '',
          pratise_type: formValue.should_tran_voice ? 3 : 1,
        });
        //   demo驱动
        await ShuzirenImageMngSvr.SaveDemoInfor({
          image_id: imageID,
          text_driver: formValue.text_driver,
          voice_driver_url: uploadValue.voice_driver_url || '',
        });
        //   提交录制
        await ShuzirenImageMngSvr.SaveImageInfor({
          image_id: imageID,
          image_sexy: Number(formValue.image_sexy),
          identity_video_cos_url: uploadValue.identity_video_cos_url || '',
          notes: formValue.notes,
          anchor_name: formValue.anchor_name,
        });

        setStatus('submit');
        setStepName('');
        void MessagePlugin.success('数字人创建成功');
      } catch (e) {
        console.error(e);
        if (e instanceof RespError) {
          const msg =
            ERROR_MSG[e.resultCode as keyof typeof ERROR_MSG] ??
            '数字人创建失败';
          void MessagePlugin.error(msg);
        } else {
          void MessagePlugin.error('数字人创建失败');
        }
        setStepName('');
      }
    } else {
      void MessagePlugin.error('请补充表单信息');
    }
  };

  const showDialog = () => {
    const myDialog = DialogPlugin({
      header: (
        <div className="flex" style={{ width: '100%' }}>
          <div className="flex-1">
            🎉欢迎使用形象定制，建议您观看一下制作教程
          </div>
          <div className="flex items-center" style={{ gap: '8px' }}>
            <Link
              hover="color"
              theme="primary"
              size="medium"
              prefixIcon={<DownloadIcon />}
              onClick={() => {
                downloadVideo(GUIDE_VIDEO, '');
              }}
            >
              下载教程
            </Link>
            <Link hover="color" theme="primary" size="medium">
              教程文档
            </Link>
          </div>
        </div>
      ),
      width: 928,
      footer: null,
      body: (
        <div className="pagedoo-meta-live-global">
          <video
            autoPlay
            width="928"
            className="rounded-4"
            src={GUIDE_VIDEO}
            controls
          />
          <div className="flex justify-center mt-28">
            <Button
              size="large"
              theme="default"
              className="gradient-primary"
              style={{ width: 120 }}
              onClick={() => {
                myDialog.hide();
              }}
            >
              开始定制
            </Button>
          </div>
        </div>
      ),
      onClose: () => {
        myDialog.hide();
      },
    });
  };

  const getVideoUrl = (
    uploadFile: UploadFile
  ): { url: string; destroy: () => void } => {
    if (uploadFile.url) {
      // 优先使用带url 的，进行文件下载并转换为blob
      return {
        url: uploadFile.url,
        destroy: () => {
          /** placeholder */
        },
      };
    }
    if (uploadFile.raw) {
      const url = URL.createObjectURL(uploadFile.raw);
      return {
        url,
        destroy: () => {
          URL.revokeObjectURL(url);
        },
      };
    }

    throw new Error('文件为空');
  };

  const getVideoResolution = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');

      video.preload = 'metadata';

      video.onloadedmetadata = function () {
        const width = video.videoWidth;
        const height = video.videoHeight;
        resolve(`${width}x${height}`);
      };

      video.onerror = function () {
        reject(new Error('视频元数据加载失败'));
      };

      video.src = url;
    });
  };
  (window as any).__debug_getVideoUrl = getVideoUrl;
  (window as any).__debug_video2PosterRef = video2fileRef;
  (window as any).__debug_getVideoResolution = getVideoResolution;
  (window as any).__debug_imageVideoUploadRef = imageVideoUploadRef;

  return (
    <AssetsLibConfigContext.Provider
      value={MatchedGlobalConfigItem.assetsLibraryConfig}
    >
      <div className={classPrefix}>
        <div className={`${classPrefix}-breadcrumb`}>
          <Breadcrumb separator=">" maxItemWidth="140px">
            <BreadcrumbItem onClick={() => navigate('/virtual-image')}>
              形象定制
            </BreadcrumbItem>
            <BreadcrumbItem>新增形象定制</BreadcrumbItem>
          </Breadcrumb>
        </div>
        <Video2Poster ref={video2fileRef} />
        {status === 'init' ? (
          <>
            <Form form={form} rules={rules} labelWidth={140} labelAlign="left">
              <div className={`${classPrefix}-card`}>
                <div className="header flex">
                  <div className="flex-1">形象训练素材</div>
                  <Link
                    hover="color"
                    theme="primary"
                    size="medium"
                    onClick={showDialog}
                  >
                    如何制作精品数字人素材？
                  </Link>
                </div>
                <div className="content">
                  <div className="sub-title mb-20">请选择是否需要抠图：</div>
                  <FormItem
                    name="need_matting"
                    label={null}
                    initialData="matting"
                  >
                    <FlattenSelect
                      itemGap={8}
                      options={[
                        {
                          key: 'matting',
                          value: 'matting',
                          label: '需要抠图',
                          render: ({ active, onSelect, value, label }) => {
                            return (
                              <CategoryButton
                                selected={active}
                                onClick={() => onSelect(value)}
                              >
                                {label}
                              </CategoryButton>
                            );
                          },
                        },
                        {
                          key: 'noMatting',
                          value: 'noMatting',
                          label: '不需要抠图',
                          render: ({ active, onSelect, value, label }) => {
                            return (
                              <CategoryButton
                                selected={active}
                                onClick={() => onSelect(value)}
                              >
                                {label}
                              </CategoryButton>
                            );
                          },
                        },
                      ]}
                    />
                    {/* <Radio.Group variant="default-filled"> */}
                    {/* <Radio.Button value="matting" style={{ width: '183px' }}>
                      需要抠图
                    </Radio.Button>
                    <Radio.Button value="noMatting" style={{ width: '183px' }}>
                      不需要抠图
                    </Radio.Button> */}
                    {/* </Radio.Group> */}
                  </FormItem>
                  <div className="sub-title mb-20">请上传训练素材视频：</div>

                  {/*  upload*/}
                  <div
                    className={`${classPrefix}-upload-box`}
                    style={{
                      padding: '20px',
                      height: '424px',
                    }}
                  >
                    <FormItem name="video_url" label={null}>
                      <VirtualImageUploader
                        ref={imageVideoUploadRef}
                        accept=".mp4,.mov"
                      />
                      {/* <DraggableUpload
                      ref={imageVideoUploadRef}
                      autoUpload={false}
                      addIcon
                      desc={
                        <div
                          className="mt-12"
                          style={{
                            fontSize: '12px',
                            width: '260px',
                            textAlign: 'left',
                          }}
                        >
                          <div>
                            1、视频大小不超过5GB，时长不短于1分钟、不长于10分钟
                          </div>
                          <div>2、视频格式为mp4、mov</div>
                          <div>
                            3、视频分辨率1080P、4K（3840*2160），宽高比符合16:9（9:16）
                          </div>
                          <div>4、视频帧率不低于25fps、不高于60fps</div>
                        </div>
                      }
                      accept=".mp4,.mov"
                    /> */}
                    </FormItem>

                    <div className="tips-zone">
                      <VirtualImageExample
                        style={{
                          height: '384px',
                        }}
                      />
                      {/* <div className="mb-12">
                        <span style={{ color: '#0047F9' }}>【重要】</span>{' '}
                        素材视频自查小tips
                      </div>
                      <div
                        style={{ fontSize: '14px', gap: '8px 0' }}
                        className="flex flex-col"
                      >
                        {TIPS_LIST.map((item, index) => {
                          return (
                            // eslint-disable-next-line react/no-array-index-key
                            <div key={index}>
                              <div className="flex">
                                <img src={TipsSvg} alt="" />
                                <span className="ml-4">{item.title}</span>
                              </div>
                              <div
                                style={{ color: 'rgba(0, 0, 0, 0.70)' }}
                                className="mt-4 font-normal pl-28"
                              >
                                {item.desc}
                              </div>
                            </div>
                          );
                        })}
                      </div> */}
                    </div>

                    <div
                      className="video-demo-zone"
                      style={{
                        height: '100%',
                      }}
                    >
                      <div className="font-medium">视频教程</div>
                      <div className="video-demo">
                        <video width="375" src={GUIDE_VIDEO} controls />
                      </div>
                      {/* <Link
                        hover="color"
                        theme="primary"
                        size="medium"
                        prefixIcon={<DownloadIcon />}
                        onClick={() => {
                          downloadVideo(DEMO_VIDEO, '');
                        }}
                      >
                        下载
                      </Link> */}
                    </div>
                  </div>
                  <div
                    css={css`
                      display: flex;
                      margin-top: 12px;
                    `}
                  >
                    {/* 是否允许形象和音色一起训练 */}
                    <FormItem
                      name="should_tran_voice"
                      className={overrideStyles.tran_voice_checkbox}
                      // css={css`
                      //   display: flex;
                      //   gap: 4px;
                      //   align-items: center;
                      // `}
                    >
                      <Checkbox>同步训练音色</Checkbox>
                    </FormItem>
                  </div>
                </div>
              </div>

              <div className={`${classPrefix}-card mt-20`}>
                <div className="header">Demo脚本</div>
                <div className="content">
                  <div className="sub-title mb-20">
                    请选择Demo驱动方式（可多选）：
                  </div>
                  <FormItem
                    name="driver_way"
                    label={null}
                    initialData={['text_driver']}
                  >
                    <FlattenSelect<string[]>
                      multiple
                      options={(
                        [
                          {
                            key: 'text_driver',
                            value: 'text_driver',
                            label: '文本驱动',
                          },
                          {
                            key: 'voice_driver',
                            value: 'voice_driver',
                            label: '语音驱动',
                          },
                        ] as IIFlattenSelectProps<string[]>['options']
                      ).map((option) => ({
                        ...option,
                        render: ({ active, label, value, onSelect }) => (
                          <CategoryButton
                            selected={active}
                            checkedStyle="filled"
                            onClick={() => onSelect(value)}
                          >
                            {label}
                          </CategoryButton>
                        ),
                      }))}
                    />
                  </FormItem>
                  <FormItem
                    shouldUpdate={(prev, next) =>
                      prev.driver_way !== next.driver_way
                    }
                  >
                    {({ getFieldValue }) => {
                      const driverWay = (getFieldValue('driver_way') ||
                        []) as FormValues['driver_way'];

                      return (
                        <>
                          {driverWay.includes('text_driver') && (
                            <div key="text_driver">
                              <div className="sub-title mb-20">
                                请输入文本驱动内容：
                              </div>

                              <FormItem name="text_driver" label={null}>
                                <Textarea
                                  style={{ width: '1000px' }}
                                  placeholder="请输入文本驱动内容"
                                  autosize={{ minRows: 4, maxRows: 4 }}
                                  maxlength={150}
                                />
                              </FormItem>
                            </div>
                          )}
                          {driverWay.includes('voice_driver') && (
                            <div key="voice_driver">
                              <div className="sub-title mb-20">
                                请上传语音驱动的音频文件：
                              </div>

                              <div
                                className={`${classPrefix}-supplement-upload-box`}
                              >
                                <FormItem name="voice_driver_url" label={null}>
                                  <DraggableUpload
                                    ref={imageVoiceUploadRef}
                                    autoUpload={false}
                                    desc={
                                      <div
                                        className="mt-12"
                                        style={{ fontSize: '12px' }}
                                      >
                                        <div>1.文件大小10Mb内</div>
                                        <div>2.格式wav、mp3、wma、m4a、aac</div>
                                      </div>
                                    }
                                    accept="audio/*"
                                  />
                                </FormItem>
                              </div>
                            </div>
                          )}
                        </>
                      );
                    }}
                  </FormItem>
                </div>
              </div>

              <div className={`${classPrefix}-card mt-20`}>
                <div className="header">形象基本信息</div>
                <div className="content">
                  <div className="sub-title mb-20">请完善数字人形象信息：</div>
                  <FormItem name="anchor_name" label="数字人形象名称">
                    <Input
                      style={{ width: '375px' }}
                      clearable
                      placeholder="请命名，如：小明"
                    />
                  </FormItem>
                  <FormItem
                    name="image_sexy"
                    label="数字人性别"
                    initialData="0"
                  >
                    <FlattenSelect<string>
                      itemGap={8}
                      options={(
                        [
                          {
                            key: '1',
                            value: '1',
                            label: '男性',
                          },
                          {
                            key: '0',
                            value: '0',
                            label: '女性',
                          },
                        ] as IIFlattenSelectProps<string[]>['options']
                      ).map((option) => ({
                        ...option,
                        render: ({ active, label, value, onSelect }) => (
                          <CategoryButton
                            selected={active}
                            onClick={() => onSelect(value)}
                          >
                            {label}
                          </CategoryButton>
                        ),
                      }))}
                    />
                    {/* <Radio.Group variant="default-filled">
                      <Radio.Button value="1" style={{ width: '185px' }}>
                        男性
                      </Radio.Button>
                      <Radio.Button value="0" style={{ width: '185px' }}>
                        女性
                      </Radio.Button>
                    </Radio.Group> */}
                  </FormItem>
                  <FormItem name="notes" label="描述">
                    <Input
                      style={{ width: '375px' }}
                      clearable
                      placeholder="请输入描述"
                    />
                  </FormItem>
                </div>
              </div>
              <div className={`${classPrefix}-card mt-20`}>
                <div className="header">数字人制作授权信息</div>
                <div className="content">
                  <div className="sub-title mb-20">
                    请上传素材本人的身份授权文件：
                  </div>
                  <div className={`${classPrefix}-supplement-upload-box`}>
                    <FormItem name="accredit_video_url" label={null}>
                      <DraggableUpload
                        ref={accreditFileUploadRef}
                        autoUpload={false}
                        desc={
                          <div className="mt-12" style={{ fontSize: '12px' }}>
                            <div>支持pdf/mp4/mov格式</div>
                            <div>视频小于5G</div>
                            <div>pdf小于10M</div>
                          </div>
                        }
                        accept=".mp4,.mov,.pdf"
                        // , application/pdf
                      />
                    </FormItem>
                    <div className="tips">
                      <div className="mb-8">
                        若上传pdf格式请参考示例提交完整清晰的扫描件
                      </div>
                      <Link
                        hover="color"
                        theme="primary"
                        size="medium"
                        onClick={() => {
                          downloadVideo(
                            isWindows ? ZIP_EXAMPLE.win : ZIP_EXAMPLE.others,
                            ''
                          );
                        }}
                        prefixIcon={<DownloadIcon />}
                      >
                        下载示例
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
              <div style={{ height: '60px' }} />
            </Form>
            {floatVideoVisible && (
              <div
                className={`${classPrefix}-course-video`}
                style={{
                  zIndex: 99999,
                }}
              >
                <div className="flex items-center">
                  <PlayCircleIcon className="play-circle-icon" />
                  <span className="ml-4">形象定制教程</span>
                  <FullscreenExitIcon
                    className="fullscreen-exit-icon cursor-pointer"
                    onClick={() => {
                      setFloatVideoVisible(false);
                    }}
                  />
                </div>

                <div
                  className="video-demo mt-4"
                  onClick={() => {
                    showDialog();
                  }}
                >
                  <video width="268" className="rounded-4" src={GUIDE_VIDEO} />
                  <div className="mask">
                    <svg
                      width="33"
                      height="33"
                      viewBox="0 0 33 33"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="16.5" cy="16.5" r="16.5" fill="white" />
                      <path
                        d="M30.9375 16.5C30.9375 8.52639 24.4736 2.0625 16.5 2.0625C8.52639 2.0625 2.0625 8.52638 2.0625 16.5C2.0625 24.4736 8.52638 30.9375 16.5 30.9375C24.4736 30.9375 30.9375 24.4736 30.9375 16.5ZM22.6553 16.9701L13.4277 22.2977C13.0658 22.5066 12.6134 22.2454 12.6134 21.8276L12.6134 11.1724C12.6134 10.7545 13.0658 10.4934 13.4277 10.7023L22.6553 16.0299C23.0172 16.2388 23.0172 16.7611 22.6553 16.9701Z"
                        fill="url(#paint0_linear_450_1064)"
                      />
                      <defs>
                        <linearGradient
                          id="paint0_linear_450_1064"
                          x1="2.05961"
                          y1="30.9375"
                          x2="45.3011"
                          y2="30.3435"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stopColor="#0153FF" />
                          <stop offset="1" stopColor="#8649FF" />
                        </linearGradient>
                      </defs>
                    </svg>
                  </div>
                </div>
              </div>
            )}

            <div className={`${classPrefix}-footer`}>
              <Button
                size="large"
                theme="default"
                className="gradient-primary"
                style={{ width: 120 }}
                onClick={() => {
                  handleSubmit().then();
                }}
              >
                确认提交
              </Button>
              <Button
                size="large"
                theme="default"
                className="gradient-default"
                style={{ width: 120 }}
                onClick={() => navigate('/virtual-image')}
              >
                返回
              </Button>
            </div>

            {!!stepName && (
              <CommonScriptLoading
                attachBody
                stepName={stepName}
                percent={
                  typeof uploadProgress === 'number'
                    ? Math.round(uploadProgress * 100)
                    : null
                }
              />
            )}
          </>
        ) : (
          <div className={`${classPrefix}-submit-card`}>
            <TrainResultPage
              tips="接下会经过以下流程，您可以前往形象定制管理页查看进度"
              onLink={() => navigate('/virtual-image')}
            />
          </div>
        )}
      </div>
    </AssetsLibConfigContext.Provider>
  );
}
