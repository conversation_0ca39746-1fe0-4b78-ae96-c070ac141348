import VoiceUpload, {
  IVoiceUploadInstance,
  IVoiceUploadProps,
  IVoiceUploadSlotApi,
} from '@/components/VoiceUpload';
import { MiniQRCodeDialog } from '@/pages/VirtualImage/MiniQRCodeDialog';
import { checkVideoCanplay } from '@/utils/video/check-video';
import { css } from '@emotion/react';
import classNames from 'classnames';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { Button, MessagePlugin, Progress, UploadFile } from 'tdesign-react';

interface IRendererProps {
  files: UploadFile[];
  api: IVoiceUploadSlotApi;
}

export interface IVirtualImageUploaderProps
  extends Pick<IVoiceUploadProps, 'accept' | 'addIcon' | 'desc'> {
  onChange?: (url: string) => void;
}

type UploadStatusType =
  | 'init'
  | 'waitupload'
  | 'success'
  | 'progress'
  | 'error';

const MainRender: React.FC<IRendererProps> = function ({ files, api }) {
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string>();
  const [uploadStatus, setUploadStatus] = useState<UploadStatusType>('init');
  const checkVideoLockRef = useRef(false);
  const width = '370px';
  useEffect(() => {
    if (!files || files.length === 0) {
      setUploadStatus('init');
      setPreviewVideoUrl(undefined);
      return;
    }
    console.log('文件处理files: ', files);
    if (files[0].status === 'progress') {
      setUploadStatus('progress');
      return;
    }
    if (files[0].status === 'success') {
      setUploadStatus('success');
      if (files[0].url) {
        setPreviewVideoUrl(files[0].url);
      }
      return;
    }
    if (files[0].status === 'fail') {
      setUploadStatus('init');
      return;
    }

    let blobUrl = '';
    if (uploadStatus === 'init' && !checkVideoLockRef.current) {
      checkVideoLockRef.current = true;
      checkVideoCanplay(files[0].raw!)
        .then(() => {
          blobUrl = window.URL.createObjectURL(files[0].raw!);
          setPreviewVideoUrl(blobUrl);
          setUploadStatus('success');
          // setUploadStatus('waitupload');
          // api.doUpload();
        })
        .catch(() => {
          setUploadStatus('waitupload');
          api.doUpload();
        })
        .finally(() => {
          checkVideoLockRef.current = false;
        });
    }
    return () => {
      if (blobUrl) {
        window.URL.revokeObjectURL(blobUrl);
      }
    };
  }, [api, files, uploadStatus]);
  if (previewVideoUrl && uploadStatus === 'success') {
    let statusIcon: React.ReactElement = <></>;
    let name = '';
    if (files[0]) {
      name = files[0].name || '';
      //   let audioIcon: React.ReactElement = <></>;
      if (uploadStatus === 'success') {
        statusIcon = (
          <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.99935 15.8332C12.0494 15.8332 15.3327 12.5499 15.3327 8.49984C15.3327 4.44975 12.0494 1.1665 7.99935 1.1665C3.94926 1.1665 0.666016 4.44975 0.666016 8.49984C0.666016 12.5499 3.94926 15.8332 7.99935 15.8332ZM4.99925 7.55702L6.99925 9.55702L10.9993 5.55702L11.9421 6.49983L6.99925 11.4426L4.05644 8.49983L4.99925 7.55702Z"
              fill="url(#paint0_linear_7877_22737)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_7877_22737"
                x1="0.666016"
                y1="1.1665"
                x2="15.3327"
                y2="1.1665"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#00C653" />
                <stop offset="1" stopColor="#01D766" />
              </linearGradient>
            </defs>
          </svg>
        );
      }
    }
    return (
      <div
        css={css`
          width: ${width};
          display: flex;
          border-radius: 8px;
          flex-direction: column;
          gap: 16px;
        `}
      >
        <video
          src={previewVideoUrl}
          autoPlay={false}
          controls
          // width={370}
          // height={190}
          style={{
            height: '190px',
          }}
        />
        <div>
          <span>{name}</span>
          {statusIcon}
        </div>
      </div>
    );
  }
  if (uploadStatus === 'waitupload') {
    return (
      <div
        css={css`
          width: ${width};
        `}
        className="h-full flex flex-col justify-center items-center"
      >
        <span>等待上传...</span>
      </div>
    );
  }
  if (uploadStatus === 'progress') {
    return (
      <div
        css={css`
          width: ${width};
          border-radius: 8px;
        `}
        className={classNames(
          'h-full',
          'flex',
          'flex-col',
          'gap-4',
          'justify-center'
        )}
      >
        <div>
          <div
            className="flex items-center gap-4"
            x-if={typeof api.progress === 'number'}
          >
            <span>上传中...</span>
            <Progress
              className="flex-1"
              percentage={api.progress}
              // css={css`
              //   margin: 16px 0;
              //   width: 100%;
              // `}
            />
          </div>
          <div
            className="flex items-center gap-4"
            x-if={typeof api.transcodeProgress === 'number'}
          >
            <span>转码中...</span>
            <Progress className="flex-1" percentage={api.transcodeProgress} />
          </div>
        </div>
      </div>
    );
  }
  return (
    <div
      style={{ display: uploadStatus !== 'init' ? 'none' : 'flex' }}
      css={css`
        display: flex;
        align-items: center;
        flex-direction: column;
      `}
    >
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          width="48"
          height="48"
          rx="24"
          fill="url(#paint0_linear_19550_66166)"
        />
        <path
          d="M25.0461 10.668C30.2867 10.668 34.5249 13.0004 36.3818 16.9071V16.9048C38.3649 21.0821 37.2083 26.226 33.2068 31.0636V34.8595C33.2068 35.1219 33.1007 35.3735 32.9118 35.559C32.7229 35.7445 32.4666 35.8487 32.1995 35.8487C31.9323 35.8487 31.6761 35.7445 31.4872 35.559C31.2983 35.3735 31.1921 35.1219 31.1921 34.8595V30.7072C31.1917 30.4752 31.2742 30.2505 31.4252 30.072C35.0941 25.7671 36.2349 21.274 34.5564 17.7426C33.0381 14.5497 29.4833 12.6464 25.0437 12.6464C16.1657 12.6464 15.4739 22.1249 15.4496 22.5278C15.4401 22.6737 15.3981 22.8159 15.3266 22.9441C15.2551 23.0724 15.1558 23.1838 15.0358 23.2703C14.9999 23.2966 13.4719 24.4158 12.7043 26.108C12.8621 26.4727 14.3427 26.8624 15.6535 26.9256C15.912 26.9378 16.1557 27.0473 16.3342 27.2312C16.5127 27.4152 16.6123 27.6595 16.6123 27.9136V34.0229H21.6733C21.9405 34.0229 22.1967 34.1271 22.3856 34.3126C22.5745 34.4981 22.6807 34.7497 22.6807 35.0121C22.6807 35.2744 22.5745 35.5261 22.3856 35.7116C22.1967 35.8971 21.9405 36.0013 21.6733 36.0013H15.605C15.0479 36.0013 14.5976 35.5591 14.5976 35.0121V28.817C13.3937 28.6633 11.629 28.2569 10.9505 27.1747C10.601 26.6169 10.5707 25.9543 10.8632 25.3059C11.6083 23.6588 12.8633 22.4741 13.4847 21.9569C13.7602 19.7925 15.4872 10.668 25.0461 10.668Z"
          fill="#0047F9"
        />
        <path
          d="M28.6214 17.6199C28.7439 17.5701 28.8751 17.5446 29.0076 17.5448V17.546C29.2747 17.546 29.5309 17.6502 29.7199 17.8357C29.9088 18.0212 30.0149 18.2728 30.0149 18.5352V27.2879C30.0149 27.4179 29.9888 27.5466 29.9382 27.6667C29.8875 27.7868 29.8133 27.8959 29.7197 27.9878C29.6261 28.0797 29.515 28.1526 29.3927 28.2024C29.2704 28.2521 29.1393 28.2777 29.007 28.2777C28.8746 28.2777 28.7435 28.2521 28.6212 28.2024C28.4989 28.1526 28.3878 28.0797 28.2942 27.9878C28.2006 27.8959 28.1264 27.7868 28.0757 27.6667C28.0251 27.5466 27.999 27.4179 27.999 27.2879V18.5352C27.9988 18.4051 28.0248 18.2762 28.0754 18.156C28.1261 18.0358 28.2004 17.9265 28.294 17.8345C28.3877 17.7425 28.499 17.6696 28.6214 17.6199Z"
          fill="#0047F9"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M23.6078 18.2837C23.7774 18.4039 23.9047 18.5731 23.972 18.7676V18.764L26.931 27.2629C26.9737 27.3859 26.9913 27.516 26.9828 27.6457C26.9744 27.7754 26.9399 27.9022 26.8816 28.0188C26.8232 28.1355 26.742 28.2397 26.6426 28.3255C26.5432 28.4114 26.4276 28.4771 26.3023 28.5191C26.177 28.561 26.0446 28.5783 25.9125 28.57C25.7804 28.5617 25.6513 28.5279 25.5325 28.4705C25.4137 28.4132 25.3076 28.3335 25.2202 28.2359C25.1328 28.1383 25.0658 28.0247 25.0231 27.9017L24.4551 26.2618H21.4622L20.8578 27.9184C20.767 28.1653 20.5801 28.3666 20.3381 28.4781C20.0962 28.5897 19.819 28.6022 19.5676 28.5131C19.3162 28.424 19.1112 28.2404 18.9976 28.0028C18.8841 27.7652 18.8712 27.4931 18.962 27.2462L22.0714 18.7521C22.1414 18.5603 22.2701 18.3944 22.4397 18.277C22.6094 18.1597 22.8118 18.0967 23.0193 18.0966H23.0254C23.2346 18.0981 23.4382 18.1634 23.6078 18.2837ZM22.9963 22.0773L22.1867 24.2857H23.7633L22.9963 22.0773Z"
          fill="#0047F9"
        />
        <g opacity="0.88">
          <path
            d="M28.6256 17.618C28.748 17.5683 28.8792 17.5428 29.0117 17.543C29.2789 17.543 29.5351 17.6484 29.724 17.8339C29.9129 18.0194 30.019 18.271 30.019 18.5334V27.2861C30.019 27.4161 29.993 27.5448 29.9423 27.6649C29.8917 27.785 29.8174 27.8941 29.7238 27.986C29.6302 28.0779 29.5191 28.1508 29.3968 28.2006C29.2745 28.2503 29.1435 28.2759 29.0111 28.2759C28.8787 28.2759 28.7477 28.2503 28.6254 28.2006C28.5031 28.1508 28.392 28.0779 28.2984 27.986C28.2048 27.8941 28.1305 27.785 28.0799 27.6649C28.0292 27.5448 28.0031 27.4161 28.0031 27.2861V18.5334C28.003 18.4033 28.029 18.2744 28.0796 18.1542C28.1302 18.0339 28.2045 17.9247 28.2982 17.8327C28.3919 17.7407 28.5031 17.6678 28.6256 17.618Z"
            fill="url(#paint1_linear_19550_66166)"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M23.6119 18.2819C23.7816 18.4021 23.9089 18.5712 23.9762 18.7658V18.7622L26.9351 27.2611C26.9778 27.3841 26.9954 27.5142 26.987 27.6439C26.9785 27.7736 26.9441 27.9004 26.8857 28.017C26.8273 28.1337 26.7461 28.2379 26.6467 28.3237C26.5473 28.4095 26.4317 28.4753 26.3064 28.5173C26.1812 28.5592 26.0487 28.5765 25.9166 28.5682C25.7845 28.5599 25.6554 28.5261 25.5366 28.4687C25.4178 28.4114 25.3117 28.3317 25.2243 28.2341C25.1369 28.1365 25.0699 28.0229 25.0272 27.8999L24.4592 26.2599H21.4663L20.8619 27.9166C20.7711 28.1634 20.5842 28.3648 20.3423 28.4763C20.1003 28.5878 19.8232 28.6004 19.5718 28.5113C19.3204 28.4222 19.1153 28.2386 19.0018 28.001C18.8882 27.7634 18.8754 27.4913 18.9661 27.2444L22.0756 18.7503C22.1456 18.5585 22.2742 18.3925 22.4439 18.2752C22.6135 18.1579 22.8159 18.0949 23.0234 18.0948H23.0295C23.2387 18.0962 23.4423 18.1616 23.6119 18.2819ZM23.0004 22.0755L22.1909 24.2839H23.7674L23.0004 22.0755Z"
            fill="url(#paint2_linear_19550_66166)"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_19550_66166"
            x1="-6.61627e-08"
            y1="48"
            x2="49.8023"
            y2="45.9458"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E0EAFF" />
            <stop offset="0.470486" stopColor="#E2EFFF" />
            <stop offset="1" stopColor="#F5F3FF" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_19550_66166"
            x1="18.9051"
            y1="28.5702"
            x2="35.5469"
            y2="28.3398"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#0153FF" />
            <stop offset="1" stopColor="#8649FF" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_19550_66166"
            x1="18.9051"
            y1="28.5702"
            x2="35.5469"
            y2="28.3398"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#0153FF" />
            <stop offset="1" stopColor="#8649FF" />
          </linearGradient>
        </defs>
      </svg>

      <span style={{ color: '#000000E5', margin: '16px' }}>
        上传视频制作您的数字人分身
      </span>
      <div
        css={css`
          background: linear-gradient(
            88.08deg,
            #0153ff -0.01%,
            #2e7ffd 49.89%,
            #c1a3fd 99.99%
          );
          border-radius: 4px;
          height: 32px;
          width: 103px;
          cursor: pointer;
          text-align: center;
          color: #ffffff;
          line-height: 32px;
        `}
        onClick={() => {
          api.triggerUpload();
        }}
      >
        上传视频
      </div>

      <div
        css={css`
          color: #00000066;
          font-weight: 400;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          margin: 38px;
        `}
      >
        <span>1、大小不超过5GB，1-10分钟</span>
        <span>2、格式为mp4、mov</span>
        <span>3、分辨率1080P、4K，宽高比16:9（9:16）</span>
        <span>4、帧率不低于25-60fps</span>
      </div>
    </div>
  );
};

function BottomRender() {
  const [showQRCode, setShowQRCode] = useState(false);
  return (
    <>
      <span
        style={{
          marginLeft: '4px',
        }}
      >
        若没有现成视频，可体验小程序制作哦
      </span>
      <Button
        variant="text"
        style={{ color: '#0047F9' }}
        onClick={() => setShowQRCode(true)}
      >
        去使用
      </Button>
      <MiniQRCodeDialog
        show={showQRCode}
        onClose={() => setShowQRCode(false)}
      />
    </>
  );
}

export const VirtualImageUploader = forwardRef<
  IVoiceUploadInstance,
  IVirtualImageUploaderProps
>((props, ref) => {
  const { onChange, ...rest } = props;

  return (
    <VoiceUpload
      {...rest}
      style={{
        height: '384px',
      }}
      ref={ref}
      onFail={() => {
        void MessagePlugin.error('上传失败,请重新上传');
      }}
      onSuccess={() => {
        void MessagePlugin.success('上传成功');
      }}
      onFileUrlChange={(url) => {
        console.debug(url);
      }}
      onChange={onChange}
      disableAutoUpload
      fileInitialStatus="waiting"
      ignoreErrorFiles
    >
      {(files, api) => {
        return {
          MainRender: <MainRender files={files} api={api} />,
          BottomRender: <BottomRender />,
        };
      }}
    </VoiceUpload>
  );
});
