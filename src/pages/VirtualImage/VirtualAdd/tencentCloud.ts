import COS from 'cos-js-sdk-v5';
import { RespType } from '@/pb/config';
import { ShuzirenImageMngSvr } from '@/pb/pb';

type UploadCredentials = RespType<
  typeof ShuzirenImageMngSvr.GetUploadCredentials
>;

export class CosUpload {
  protected cos: COS;

  constructor(uploadCredentials: UploadCredentials) {
    this.cos = new COS({
      getAuthorization(_, callback) {
        const authInfo = {
          TmpSecretId: uploadCredentials.Credentials.TmpSecretId,
          TmpSecretKey: uploadCredentials.Credentials.TmpSecretKey,
          SecurityToken: uploadCredentials.Credentials.Token,
          ExpiredTime: uploadCredentials.ExpiredTime,
          // StartTime: uploadCredentials.ExpiredTime - 7200,
          // ScopeLimit: true,
        };
        callback(authInfo);
      },
    });
  }

  uploadFileToCos({
    fileName,
    file,
    onProgress,
    onFinish,
    onStart,
  }: {
    fileName: string;
    file: File | Blob;
    onProgress?: COS.onProgress;
    onFinish?: COS.onFileFinish;
    onStart?: COS.PutObjectParams['onTaskReady'];
  }) {
    return new Promise<void>((resolve, reject) => {
      // console.log('cos upload file', fileName, filePath);
      this.cos.uploadFile(
        {
          Bucket: 'virtualhuman-cos-prod-1251316161',
          Region: 'accelerate' /* 存储桶所在地域，必须字段 */,
          Key: fileName /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */,
          Body: file,
          SliceSize:
            1024 *
            1024 *
            5 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
          ...[
            { name: 'onProgress', fn: onProgress },
            { name: 'onFinish', fn: onFinish },
            {
              name: 'onStart',
              fn: onStart,
            },
          ].reduce((prev, cur) => {
            return {
              ...prev,
              ...(typeof cur.fn === 'function'
                ? {
                    [cur.name]: cur.fn,
                  }
                : undefined),
            };
          }, {}),
        },
        (err) => {
          if (err) {
            reject(err);
            console.log('上传失败', err);
          } else {
            resolve();
            console.log('上传成功');
          }
        }
      );
    });
  }
}
