import { Steps } from 'tdesign-react';
interface IProps {
  timelineList: {
    label: string;
    text: string;
    state: string;
  }[];
  state: string;
  config?: { [key: string]: any };
}
const { StepItem } = Steps;
export function CustomSteps(props: IProps) {
  return (
    <Steps
      layout="vertical"
      theme="dot"
      current={props.state}
      {...(props.config || {})}
    >
      {props.timelineList.map((item) => {
        return (
          <StepItem
            key={`${item.label}${item.state}`}
            value={item.state}
            title={item.label}
            content={item.text}
          />
        );
      })}
    </Steps>
  );
}
