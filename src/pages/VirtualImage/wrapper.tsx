// 数字人形象使用tab包装

import { Page } from '@/components/Layout';
import { AssetsLibConfigContext } from '@/configs/config_context';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { Tabs } from 'tdesign-react';
import VirtualImage from '.';
import { VirtualLibrary } from './VirtualLibrary';
import Styles from './wrapper.module.less';

const { TabPanel } = Tabs;
const classPrefix = 'virtual-image-page';

export default function VirtualWrapper() {
  return (
    <AssetsLibConfigContext.Provider
      value={MatchedGlobalConfigItem.assetsLibraryConfig}
    >
      <Page
        title="形象定制"
        className={[classPrefix, Styles['virtual-image__wrapper']].join(' ')}
        style={{
          overflow: 'hidden',
        }}
      >
        <Tabs
          defaultValue="1"
          placement="top"
          size="medium"
          theme="normal"
          style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <TabPanel
            label="形象库"
            value="1"
            style={{
              // padding: '20px 20px 0 20px',
              height: 'calc(100%)',
            }}
          >
            <VirtualLibrary
              style={{
                height: '100%',
              }}
            />
          </TabPanel>
          <TabPanel label="定制任务" value="2">
            <VirtualImage disablePage />
          </TabPanel>
        </Tabs>
      </Page>
    </AssetsLibConfigContext.Provider>
  );
}
