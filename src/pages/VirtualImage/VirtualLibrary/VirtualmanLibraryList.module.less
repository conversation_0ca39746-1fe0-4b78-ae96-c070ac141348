@import "../../../components/Style/mixins/_scrollbar.less";
.list {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    overflow: hidden auto;
    column-gap: calc((1252px - 188px * 6) / 5);
    row-gap: 8px;
    .scrollbar();
}
.mask_container {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(50, 50, 50, 0.62);
    width: 100%;
    height: 100%;
    z-index: 1;
    border-radius: 4px;
    transition: opacity 100ms linear;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.mask_desc {
    color: #fff;
    font-weight: 400;
    font-size: 14px;
    max-width: 80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mask_btn_group {
    position: absolute;
    bottom: 19px;
    width: 148px;
}