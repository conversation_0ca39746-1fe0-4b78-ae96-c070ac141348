import { CommonCategoryWrapper } from '@/components/Category';
import { CategoryHandlerType } from '@/components/Category/typings';
import { FlattenSelect } from '@/components/FlattenSelect';
import { ResourceCategoryButton } from '@/pages/VirtualImage/CategoryButton';
import { css } from '@emotion/react';
import { useMemo, useState } from 'react';
import { useAscription } from '../hooks/useAscription';
import { VirtualmanLibraryList } from './VirtualmanLibraryList';
import { VirtualAddActionBar } from '../VirtualAddActionBar';

export type IVirtualLibraryProps = Pick<
  React.HTMLAttributes<HTMLElement>,
  'style'
>;

export function VirtualLibrary(props: IVirtualLibraryProps) {
  const { style } = props;
  const [searchText, setSearchText] = useState('');
  const { choices, currentValue, setCurrentValue } = useAscription(
    useMemo(
      () => ({
        choices: [
          {
            value: 'private',
            name: '我的',
          },
          {
            value: 'public',
            name: '公共',
          },
        ],
      }),
      []
    )
  );
  return (
    <div
      css={css`
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: 18px;
      `}
      style={{ ...style }}
    >
      <VirtualAddActionBar
        enableAutoSearch
        searchPlaceholder="请输入你需要搜索的内容"
        onSearch={(text: string) => {
          setSearchText(text);
        }}
      />
      <CommonCategoryWrapper
        categoryType="dip"
        renderCategoryList={({ categoryList }) => {
          const second = categoryList[1];
          const first = categoryList[0];
          const labelStyle: React.CSSProperties = {
            width: 'auto',
            color: 'rgba(0, 0, 0, 0.6)',
          };
          return (
            <div
              css={css`
                display: flex;
                flex-direction: column;
                gap: 20px;
              `}
            >
              <FlattenSelect<string>
                title="归属"
                site="left"
                labelStyle={labelStyle}
                value={currentValue}
                options={choices.map((choice) => ({
                  key: choice.value,
                  label: choice.name,
                  value: choice.value,
                  render: ({ active, value, onSelect, label }) => {
                    return (
                      <ResourceCategoryButton
                        key={value}
                        active={active}
                        onClick={() => onSelect(value)}
                      >
                        {label}
                      </ResourceCategoryButton>
                    );
                  },
                }))}
                onChange={async (value) => setCurrentValue(value)}
              />
              <FlattenSelect<CategoryHandlerType | undefined>
                value={first.value}
                title="类型"
                site="left"
                labelStyle={labelStyle}
                // style={{
                //   marginTop: '20px',
                // }}
                options={first.data.map((item) => ({
                  key: item.id,
                  label: item.name,
                  value: item,
                  render: ({ active, onSelect, label, value }) => {
                    return (
                      <ResourceCategoryButton
                        // key={value}
                        key={value.id}
                        active={active}
                        onClick={() => onSelect(value)}
                      >
                        {label}
                      </ResourceCategoryButton>
                    );
                  },
                }))}
                onChange={async (value) => {
                  value?.onSelect();
                }}
              />
              {second && (
                <FlattenSelect<CategoryHandlerType | undefined>
                  value={second.value}
                  title="风格"
                  site="left"
                  labelStyle={labelStyle}
                  // style={{
                  //   marginTop: '20px',
                  // }}
                  options={second.data.map((item) => ({
                    key: item.id,
                    label: item.name,
                    value: item,
                    render: ({ active, onSelect, label, value }) => {
                      return (
                        <ResourceCategoryButton
                          key={value.id}
                          active={active}
                          onClick={() => onSelect(value)}
                        >
                          {label}
                        </ResourceCategoryButton>
                      );
                    },
                  }))}
                  onChange={async (value) => {
                    value?.onSelect();
                  }}
                />
              )}
              {/* <div
                css={css`
                  display: flex;
                  gap: 8px;
                `}
              >
                {first.data.map((item) => {
                  return (
                    <ResourceCategoryButton active={first.value === item}>
                      {item.name}
                    </ResourceCategoryButton>
                  );
                })}
              </div> */}
            </div>
          );
        }}
        defaultCategory={['all', 'all']}
      >
        {({ categories, categoryType }) => {
          console.log('categories', categories, categoryType);
          return (
            <VirtualmanLibraryList
              categories={[currentValue, ...categories]}
              filter={{
                name: searchText,
              }}
            />
          );
        }}
      </CommonCategoryWrapper>
    </div>
  );
}
