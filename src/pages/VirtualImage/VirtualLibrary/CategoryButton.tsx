import React from 'react';

export function CategoryButton(
  props: {
    children?: React.ReactNode;
    selected?: boolean;
  } & React.HTMLAttributes<HTMLDivElement>
) {
  const { children, selected, ...rest } = props;
  return (
    <div
      style={{
        padding: selected ? '0 18px' : '0 27px',
        height: 38,
        background: selected
          ? 'linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%)'
          : 'linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%)',
        borderRadius: 50,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 4,
        fontWeight: selected ? 600 : 400,
        fontSize: 14,
        lineHeight: '22px',
        textAlign: 'center',
        color: selected ? '#0047F9' : 'rgba(0, 0, 0, 0.6)',
        cursor: 'pointer',
      }}
      {...rest}
    >
      {selected && (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.47613 7.14883C2.28087 7.34409 2.28087 7.66068 2.47613 7.85594L6.22141 11.6012C6.41667 11.7965 6.73325 11.7965 6.92852 11.6012C6.94961 11.5801 6.96842 11.5576 6.98496 11.534L13.6559 4.86304C13.8512 4.66778 13.8512 4.3512 13.6559 4.15594C13.4606 3.96068 13.1441 3.96068 12.9488 4.15594L6.56957 10.5352L3.18323 7.14883C2.98797 6.95357 2.67139 6.95357 2.47613 7.14883Z"
            fill="#0047F9"
          />
        </svg>
      )}

      {props.children}
    </div>
  );
}
