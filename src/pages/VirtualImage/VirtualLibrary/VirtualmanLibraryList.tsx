import { SelectCard } from '@/components/SelectCard';
import { AssetsLibConfigContext } from '@/configs/config_context';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { IIFlattenSelectOption } from '@/components/FlattenSelect';
import { ResourceSvr } from '@/pb/pb';
import { DipListItem } from '@/type/api';
import { css } from '@emotion/react';
import { useDeepCompareLayoutEffect } from 'ahooks';
import to from 'await-to-js';
import { useCallback, useContext, useMemo, useState } from 'react';
import { Button, MessagePlugin } from 'tdesign-react';
import { CategoryButton } from './CategoryButton';
import Styles from './VirtualmanLibraryList.module.less';

export interface IVirtualmanLibraryListProps {
  categories: string[];
  filter?: {
    // 前端按数字人名称或描述进行过滤
    name?: string;
  };
}

export function VirtualmanLibraryList(props: IVirtualmanLibraryListProps) {
  const { categories, filter } = props;
  const [dipList, setDipList] = useState<DipListItem[]>([]);
  const [candidateDip, setCandidateDip] = useState<DipListItem>();
  const configCtx = useContext(AssetsLibConfigContext);

  const filteredList = useMemo(() => {
    if (!filter?.name) return dipList;
    return dipList.filter(
      (item) =>
        item.dip_name.includes(filter.name as string) ||
        item.dip_description.includes(filter.name as string)
    );
  }, [dipList, filter?.name]);

  useDeepCompareLayoutEffect(() => {
    (async () => {
      const [err, resp] = await to(
        ResourceSvr.GetDipResourceList({
          auth_level: categories[0] || 'all',
          category_level1: categories[1] || 'all',
          category_level2: categories[2] || 'all',
          app_code: MatchedGlobalConfigItem.appcode,
          use_virtual_man_key: true,
          user_id: '',
        })
      );
      if (err) {
        MessagePlugin.error('获取数字人形象失败，请刷新重试');
        return;
      }
      setDipList(resp.dip_info_list);
    })();
  }, [categories]);

  const customCategoryItemRender = useCallback<
    Required<IIFlattenSelectOption>['render']
  >(({ active, label, onSelect, value }) => {
    return (
      <CategoryButton selected={active} onClick={() => onSelect(value)}>
        {label}
      </CategoryButton>
    );
  }, []);

  return (
    <div className={Styles.list}>
      {filteredList.map((item) => {
        const key = `${item.app_code}_${item.platform_account_id}_${
          item.user_id
        }_${item.dip_id || item.virtual_man_key}_${item.dip_authority}`;
        return (
          <SelectCard
            data={item}
            title={item.dip_name}
            image={item.dip_image}
            mine={!!item.dip_authority}
            key={key}
            imageProps={{
              fit: 'cover',
              style: {
                height: '216px',
                flex: 1,
              },
            }}
            style={{
              width: '188px',
              height: '254px',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
            }}
            bottomStyle={{
              container: {
                height: '38px',
                // flex: 1,
              },
            }}
          >
            {({ uiState }) => {
              if (configCtx.readonlyLibList) return null;
              return (
                <div
                  className={Styles.mask_container}
                  style={{
                    opacity: uiState.hover ? '1' : '0',
                  }}
                >
                  <p className={Styles.mask_desc}>
                    {item.dip_description || item.dip_name}
                  </p>
                  <div className={Styles.mask_btn_group}>
                    <Button
                      className="gradient-primary"
                      theme="primary"
                      style={{
                        width: '100%',
                      }}
                      onClick={() => {
                        setCandidateDip(item);
                      }}
                    >
                      使用
                    </Button>
                  </div>
                </div>
              );
            }}
          </SelectCard>
        );
      })}
      {configCtx.renderUseDip?.({
        dipItem: candidateDip,
        destroy: () => {
          setCandidateDip(undefined);
        },
      })}
    </div>
  );
}
