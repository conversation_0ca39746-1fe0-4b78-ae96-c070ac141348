import { Loading } from '@/components/Loading';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { EllipsisIcon } from 'tdesign-icons-react';
import {
  Image,
  Link,
  MessagePlugin,
  Popconfirm,
  Popup,
  Space,
  Table,
} from 'tdesign-react';
import { CustomSteps } from './CustomSteps';
import { ImageTaskInfo, useImageInfo } from './hooks/useImageInfo';
import './index.less';
import { MainContent, Page } from '@/components/Layout';
import { VirtualAddActionBar } from './VirtualAddActionBar';
import { useLatest } from 'ahooks';
import { ImageStatusMap } from '@/pages/VirtualImage/const';
import { ResourceSvr } from '@/pb/pb';

const classPrefix = 'virtual-image-page';

export default function VirtualImage(props: { disablePage?: boolean }) {
  // 搜索条件
  const [searchVal, setSearchVal] = useState('');
  const [processMap, setProcessMap] = useState<{ [key: string]: any }>({});
  const latestSearchVal = useLatest(searchVal);

  const {
    getImageInfoList,
    loading,
    pagination,
    params,
    imageInfo,
    deleteImageInfo,
    getImageLogList,
    STATUS_TEXT,
  } = useImageInfo();

  const navigator = useNavigate();

  // 搜索
  const doSearch = (current?: number) => {
    getImageInfoList({
      searchText: latestSearchVal.current,
      current: current || 1,
      pageSize: params?.[0]?.pageSize || 10,
    });
  };

  useEffect(() => {
    doSearch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 删除
  const deleteImage = async (image_id: string) => {
    const ins = MessagePlugin.loading('删除中，请稍后~', 0);
    try {
      await deleteImageInfo(image_id);
      MessagePlugin.close(ins);
      doSearch(params?.[0]?.current);
    } catch (error) {
      MessagePlugin.close(ins);
    }
  };
  // 定制进度
  const viewProcess = async (image_id: string) => {
    if (processMap[image_id]) {
      return processMap[image_id];
    }
    try {
      const res = await getImageLogList({ image_id });
      setProcessMap({
        ...processMap,
        [image_id]:
          res?.images_operator_logs_list
            ?.map((i, idx) => {
              return {
                label: new Date(i.update_time).toLocaleString(),
                text: i.notes,
                state: `${idx}`,
              };
            })
            ?.sort(
              (a, b) =>
                new Date(a.label).getTime() - new Date(b.label).getTime()
            ) || [],
      });
    } catch (error) {}
  };

  const addToEditor = async (imageInfo: ImageTaskInfo) => {
    if (
      imageInfo?.image_id &&
      imageInfo?.process_status === ImageStatusMap.finishing &&
      imageInfo?.store_status !== 1
    ) {
      try {
        await ResourceSvr.StoreDipImageResource({
          image_id: imageInfo.image_id,
        });
        void MessagePlugin.success('入库成功');
        doSearch(params?.[0]?.current);
      } catch (e) {
        void MessagePlugin.error('入库失败，请稍后重试');
      }
    }
  };

  const mainContent = (
    <MainContent>
      <div className={`${classPrefix}-content-inner-box flex flex-col`}>
        <VirtualAddActionBar
          style={{
            padding: '8px',
            borderBottom: '1px solid #e8e8e8',
          }}
          onSearch={(text) => {
            setSearchVal(text);
            latestSearchVal.current = text;
            doSearch();
          }}
        />
        {/* <div className="action_bar">
          <div className="btn" onClick={() => setVisible(!visible)}>
            新增形象定制
            <ChevronDownIcon />
            {visible && (
              <div className="add_select">
                <div onClick={() => doAdd('PC')}>通过PC端定制</div>
                <div onClick={() => doAdd('miniProgram')}>通过小程序定制</div>
              </div>
            )}
          </div>
          <Input
            className="search_input"
            value={searchVal}
            onChange={setSearchVal}
            onEnter={() => doSearch()}
            suffixIcon={<SearchIcon onClick={() => doSearch()} />}
          />
        </div> */}
        {loading ? (
          <div className="loading_wrap">
            <Loading />
          </div>
        ) : (
          <>
            <div className="table h-0 flex-1">
              <Table
                rowKey="image_id"
                tableLayout="fixed"
                maxHeight="calc(-280px + 100vh)"
                lazyLoad
                data={imageInfo?.list || []}
                columns={[
                  {
                    title: 'demo预览',
                    colKey: 'material_background_pic_url',
                    width: 104,
                    cell: ({ row }) => {
                      return row.material_background_pic_url ? (
                        <Image
                          src={row.material_background_pic_url}
                          style={{
                            width: 70,
                            height: 135,
                            cursor: 'pointer',
                          }}
                          lazy
                          onClick={() =>
                            navigator(`/virtual-image/detail/${row.image_id}`)
                          }
                        />
                      ) : (
                        <video
                          src={row.material_video_url}
                          height={135}
                          width={170}
                          className="cursor-pointer"
                          onClick={() =>
                            navigator(`/virtual-image/detail/${row.image_id}`)
                          }
                        />
                      );
                    },
                  },
                  { title: '任务名称', colKey: 'task_name', width: 200 },
                  { title: '主播名称', colKey: 'anchor_name', width: 120 },
                  { title: 'ID', colKey: 'tc_task_id', width: 200 },
                  {
                    title: '定制进度',
                    colKey: 'process_status',
                    width: 150,
                    cell: ({ row }) => {
                      const statusText =
                        STATUS_TEXT[
                          row.process_status as keyof typeof STATUS_TEXT
                        ] || '未知状态';
                      return (
                        <>
                          <div
                            className={`status_tag video_status_${row.process_status}`}
                          >
                            {statusText}
                          </div>
                          <Popup
                            content={
                              <div
                                style={{
                                  padding: '15px 15px 0',
                                  maxHeight: '500px',
                                  overflow: 'scroll',
                                }}
                              >
                                {processMap[row.image_id] && (
                                  <>
                                    {processMap[row.image_id]?.length ? (
                                      <CustomSteps
                                        timelineList={processMap[row.image_id]}
                                        state={`${
                                          processMap[row.image_id][
                                            processMap[row.image_id].length - 1
                                          ]?.state
                                        }`}
                                      />
                                    ) : (
                                      <div>暂无数据</div>
                                    )}
                                  </>
                                )}
                              </div>
                            }
                            trigger="click"
                            placement="bottom-right"
                            showArrow
                          >
                            <EllipsisIcon
                              onClick={() => viewProcess(row.image_id)}
                            />
                          </Popup>
                        </>
                      );
                    },
                  },
                  {
                    title: '自然度评分（100）',
                    colKey: 'nature_score',
                    width: 200,
                    cell: ({ row }) => {
                      return (
                        <div className="score">
                          {row?.nature_score ? (
                            <div className="score_box">
                              <div className="score_num">
                                {/* 总分{row.video_size} <span>详情</span> */}
                              </div>
                              <div className="score_num">
                                自然度：{row.nature_score}
                              </div>
                              <div className="score_num">
                                亲和度：{row.affinity_score}
                              </div>
                              <div className="score_num">
                                流畅度：{row.fluency_score}
                              </div>
                            </div>
                          ) : (
                            <div className="score_not">
                              未评分 <span>去评分</span>
                            </div>
                          )}
                        </div>
                      );
                    },
                  },
                  {
                    title: '是否入库',
                    colKey: 'add_to_editor',
                    width: 120,
                    cell: ({ row }) => {
                      const msg = row.store_status === 1 ? '已入库' : '未入库';
                      return (
                        <>
                          {row.process_status === ImageStatusMap.confirmSubmit
                            ? '确认中'
                            : msg}
                        </>
                      );
                    },
                  },
                  {
                    title: '创建人 | 创建时间',
                    colKey: 'create_time',
                    width: 150,
                    cell: ({ row }) => (
                      <>
                        <div>{row.user_nick}</div>
                        {new Date(row.create_time).toLocaleString()}
                      </>
                    ),
                  },
                  {
                    title: '更多信息',
                    colKey: 'video_size',
                    width: 200,
                    cell: ({ row }) => {
                      const sexyText =
                        {
                          1: '男',
                          2: '女',
                        }[row.image_sexy] || '';
                      return (
                        <div className="more">
                          <div className="item">形象性别： {sexyText}</div>
                          <div className="item">
                            是否带原始背景：{' '}
                            {row.is_image_have_background === 1 ? '是' : '否'}
                          </div>
                          <div className="item">
                            原始素材：{' '}
                            {row.material_video_url ? (
                              <a
                                href={row.material_video_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                download={row.material_video_url}
                              >
                                原始素材
                              </a>
                            ) : (
                              '无'
                            )}
                          </div>
                          <div className="item">
                            授权材料：{' '}
                            {row.identity_video_cos_url ? (
                              <a
                                href={row.identity_video_cos_url}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                授权材料
                              </a>
                            ) : (
                              '无'
                            )}
                          </div>
                        </div>
                      );
                    },
                  },
                  {
                    title: '操作',
                    colKey: 'operation',
                    width: 140,
                    fixed: 'right',
                    cell: ({ row }) => (
                      <Space direction="vertical" size={4}>
                        <Popconfirm
                          x-if={
                            row.process_status === ImageStatusMap.finishing &&
                            row.store_status === 0
                          }
                          content="确认添加入库吗？"
                          onConfirm={() => addToEditor(row)}
                        >
                          <Link className="block" theme="primary" hover="color">
                            添加入库
                          </Link>
                        </Popconfirm>
                        {row.process_status === ImageStatusMap.confirm ? (
                          <Link
                            className="block"
                            theme="primary"
                            hover="color"
                            onClick={() =>
                              navigator(`/virtual-image/detail/${row.image_id}`)
                            }
                          >
                            确认效果
                          </Link>
                        ) : (
                          <Link
                            className="block"
                            theme="primary"
                            hover="color"
                            onClick={() =>
                              navigator(`/virtual-image/detail/${row.image_id}`)
                            }
                          >
                            查看demo
                          </Link>
                        )}
                        {/* <Popconfirm*/}
                        {/*  content="确认删除吗？"*/}
                        {/*  theme="danger"*/}
                        {/*  onConfirm={() => deleteImage(row.image_id)}*/}
                        {/* >*/}
                        {/*  <Link theme="danger" hover="color" className="block">*/}
                        {/*    删除*/}
                        {/*  </Link>*/}
                        {/* </Popconfirm>*/}
                      </Space>
                    ),
                  },
                ]}
                pagination={{
                  total: pagination.total,
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                }}
                onPageChange={(pageInfo) =>
                  pagination.onChange(pageInfo.current, pageInfo.pageSize)
                }
              />
            </div>
          </>
        )}
      </div>
    </MainContent>
  );

  if (props.disablePage) {
    return mainContent;
  }
  return (
    <Page title="形象定制" className={classPrefix}>
      {mainContent}
    </Page>
    // <div className={classPrefix}>
    //   <div className={`${classPrefix}-title`}>形象定制</div>
    //   <div className={`${classPrefix}-content`}>

    //   </div>
    // </div>
  );
}
