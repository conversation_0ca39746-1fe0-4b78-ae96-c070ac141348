import { Loading } from '@/components/Loading';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Input,
  Link,
  MessagePlugin,
  Popconfirm,
  Loading as TDLoading,
  Timeline,
  Tooltip,
} from 'tdesign-react';
import { VideoViewer } from '../VideoViewer';
import { useImageInfo } from '../hooks/useImageInfo';
import './index.less';

const classPrefix = 'virtual-image-detail-page';

export default function ImageDetail() {
  const [detail, setDetail] = useState<null | any>({});
  const [records, setRecords] = useState<any[]>([]);
  const [trainResult, setTrainResult] = useState<any[]>([]);
  const [reason, setReason] = useState<string>('');
  const navigator = useNavigate();
  const {
    getImageDetail,
    detailLoading,
    imagePractiseStatusConfirm,
    confirmLoading,
    STATUS_TEXT,
  } = useImageInfo();

  const { imageId } = useParams();

  const processStatusText = useMemo(() => {
    return STATUS_TEXT[detail?.process_status] || '未知状态';
  }, [detail?.process_status, STATUS_TEXT]);

  const imageSexyText = useMemo(() => {
    const map = {
      1: '男性',
      2: '女性',
    };
    return map[detail?.image_sexy] || '';
  }, [detail?.image_sexy]);

  const getImageDetailInfo = useCallback(
    (imageId: string) => {
      if (!imageId) return;
      getImageDetail(imageId).then((res) => {
        if (res?.image_task_info) {
          setDetail(res.image_task_info);
          try {
            const stageInfo = JSON.parse(
              decodeURIComponent(
                res?.image_task_info?.confirm_stage_info || '{}'
              )
            );
            if (stageInfo?.TrainResult) {
              setTrainResult(stageInfo.TrainResult?.splice(0, 2) || []);
            }
            if (stageInfo?.TrainRecord) {
              setRecords(stageInfo.TrainRecord || []);
            }
          } catch (error) {}
        }
      });
    },
    [getImageDetail]
  );

  // 确认效果
  const confirm = async (type: 'AGREE' | 'REJECT', reason?: string) => {
    if (!imageId) return;
    const param = {
      image_id: imageId,
      operate: type,
    };
    if (type === 'REJECT') {
      if (!reason) {
        void MessagePlugin.error({ content: '请填写不认可原因' });
        return;
      }
      param.reason = reason;
    }
    await imagePractiseStatusConfirm(param);
    if (type === 'AGREE') {
      void MessagePlugin.success({ content: '您已认可该训练结果' });
    }
    if (type === 'REJECT') {
      void MessagePlugin.success({ content: '已提交反馈，将为您返回额度' });
    }
    getImageDetailInfo(imageId);
  };

  // 获取详情
  useEffect(() => {
    if (imageId) {
      getImageDetailInfo(imageId);
    }
  }, [imageId, getImageDetailInfo]);

  return (
    <>
      <div className={classPrefix}>
        <div className={`${classPrefix}-title`}>
          <Link
            theme="primary"
            hover="color"
            className="link"
            onClick={() => {
              navigator('/virtual-image');
            }}
          >
            形象定制
          </Link>{' '}
          &gt;
          <span className={`${classPrefix}-subtitle`}>demo详情</span>
        </div>
        {detailLoading ? (
          <div className={`${classPrefix}-content`}>
            <div className="loading_wrap">
              <Loading />
            </div>
            <div className="left" />
            <div className="right">
              <div className="right_schedule" />
              <div className="right_score" />
              <div className="right_records" />
            </div>
          </div>
        ) : (
          <div className={`${classPrefix}-content`}>
            <div className="left">
              <div className="action_bar">
                <Tooltip content={detail?.task_name}>
                  <span className="action_bar_name">{detail?.task_name}</span>
                </Tooltip>
                <Tooltip content={detail?.tc_task_id}>
                  <span className="action_bar_id">
                    ID: {detail?.tc_task_id}
                  </span>
                </Tooltip>
                <span>
                  | {imageSexyText} |{' '}
                  {detail.is_image_have_background === 1
                    ? '带原始背景'
                    : '不带原始背景'}{' '}
                  |{' '}
                  {detail.create_time &&
                    new Date(detail.create_time).toLocaleString()}
                  {detail?.user_nick}
                  <div
                    className={`status_tag video_status_${detail.process_status}`}
                  >
                    {processStatusText}
                  </div>
                </span>
              </div>
              {trainResult?.length ? (
                <>
                  <div className="left_title">训练结果</div>
                  <div className="left_box">
                    {trainResult.map((item: string) => {
                      return (
                        <div className="video_box" key={`video_${item}`}>
                          <VideoViewer url={item} />
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : null}
              <div className="left_title">原素材视频</div>
              <div className="left_box">
                {detail.material_video_url ? (
                  <div className="video_box">
                    <VideoViewer
                      key={detail.material_video_url}
                      url={detail.material_video_url}
                      poster={detail.material_background_pic_url}
                    />
                  </div>
                ) : (
                  <div className="empty">暂无demo视频</div>
                )}
              </div>
            </div>
            <div className="right">
              {detail.process_status === 3 ? (
                <div className="right_schedule">
                  <div className="right_schedule_title">训练进度</div>
                  <div className="btn_box">
                    <div className="btn" onClick={() => confirm('AGREE')}>
                      认可结果
                    </div>
                    <Popconfirm
                      theme="default"
                      icon={<></>}
                      placement="bottom-left"
                      content={
                        <>
                          <div
                            style={{
                              fontSize: 14,
                              fontWeight: 600,
                              lineHeight: '28px',
                            }}
                          >
                            不认可原因
                          </div>
                          <Input
                            clearable
                            value={reason}
                            onChange={(values) => {
                              setReason(values);
                            }}
                            maxlength={300}
                            showLimitNumber
                          />
                        </>
                      }
                      onConfirm={() => confirm('REJECT', reason)}
                    >
                      <div className="btn btn_disagree">不认可，返回额度</div>
                    </Popconfirm>
                  </div>
                  <div className="btn_box">
                    <TDLoading
                      loading={confirmLoading}
                      fullscreen
                      preventScrollThrough
                      text="加载中"
                    />
                  </div>
                </div>
              ) : null}
              {detail.process_status === 1000 ? (
                <div className="right_score">
                  <div className="right_score_title">自然度评分</div>
                  {detail?.score ? (
                    <div className="right_score_box" />
                  ) : (
                    <>
                      <div className="right_score_subtitle">
                        生成问卷进行自然度灰度评分 <span>去生成</span>
                      </div>
                      <div className="empty">
                        <div className="empty_icon" />
                        <div>还未评分</div>
                        <div className="btn">去自评</div>
                      </div>
                    </>
                  )}
                </div>
              ) : null}
              <div className="right_records">
                <div className="right_records_title">训练记录</div>
                <div className="right_records_steps">
                  {records?.length ? (
                    <Timeline mode="same">
                      {records.map((item, idx) => {
                        return (
                          <Timeline.Item
                            key={`timeline_${item.StartTime}`}
                            label={item.StartTime}
                          >
                            <div>{`Demo${idx + 1}`}</div>
                          </Timeline.Item>
                        );
                      })}
                    </Timeline>
                  ) : (
                    <div className="empty">
                      <div className="empty_icon" />
                      训练中，暂无记录
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
