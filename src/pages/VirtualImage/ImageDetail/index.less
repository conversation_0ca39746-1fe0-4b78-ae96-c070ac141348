.virtual-image-detail-page {
  width: 100%;

  .t-tabs__content {
    width: 100%;
    padding: 20px;
  }
  &-title {
    color: rgba(0, 0, 0, 0.4);
    font-style: normal;
    margin-top: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    .link {
      color: rgba(0, 0, 0, 0.4);
    }
  }
  &-subtitle {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    margin-left: 5px;
  }
  &-content {
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    position: relative;
    .loading_wrap {
      width: 100%;
      height: calc(100vh - 250px);
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
    }

    .left,
    .right {
      margin-right: 15px;
      border-radius: 10px;
      flex: 1;
    }
    .left {
      flex: 2.5;
      display: flex;
      flex-direction: column;
      background: #ffffff;
      padding: 15px;

      .action_bar {
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        font-size: 14px;
        &_name,
        &_id {
          max-width: 200px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-right: 10px;
        }
        &_name {
          font-size: 18px;
          font-weight: 500;
        }
        .status_tag {
          --c: #f4f6ff;
          position: relative;
          height: 24px;
          padding: 0 10px 0 8px;
          line-height: 24px;
          font-size: 14px;
          color: rgba(0, 10, 41, 0.26);
          background: linear-gradient(to right, var(--c), #faf5fc);
          display: inline-block;
          border-radius: 0 12px 16px 0;
          margin-left: 10px;
          font-weight: 400;
          &::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            bottom: 0;
            width: 6px;
            border-radius: 4px;
            transform: skewX(-10deg);
            background: var(--c);
            display: block;
          }

          &.video_status_2 {
            --c: rgb(248, 210, 18);
            color: rgb(198, 119, 0);
            background: linear-gradient(to right, var(--c), rgb(247, 235, 93));

            &::before {
              background: var(--c);
            }
          }

          &.video_status_3,
          &.video_status_4 {
            --c: #ffd513;
            color: #b09d00;
            background: linear-gradient(to right, var(--c), #fff181);

            &::before {
              background: var(--c);
            }
          }

          &.video_status_10001 {
            --c: #e44527;
            color: #fff;
            background: linear-gradient(to right, var(--c), #f7a59e);

            &::before {
              background: var(--c);
            }
          }

          &.video_status_1000 {
            --c: rgba(235, 248, 240, 1);
            color: rgba(0, 198, 83, 1);
            background: linear-gradient(
              to right,
              var(--c),
              rgba(222, 248, 232, 1)
            );

            &::before {
              background: var(--c);
            }
          }
        }
      }
      &_title {
        font-size: 14px;
        font-weight: 600;
        margin: 15px 0;
      }
      &_box {
        --c: #f4f6ff;
        background: linear-gradient(to right, var(--c), #faf5fc);
        flex: 2;
        display: flex;
        justify-content: space-between;
        .video_box {
          width: 48%;
          height: 426px;
          border-radius: 4px;
          overflow: hidden;
        }
        .empty {
          color: rgba(0, 0, 0, 0.6);
          height: 426px;
          line-height: 426px;
          width: 100%;
        }
      }
    }
    .right {
      .right_schedule,
      .right_score,
      .right_records {
        background: #ffffff;
        padding: 15px;
        border-radius: 10px;
        &_title {
          font-size: 14px;
          font-weight: 600;
        }
      }
      .right_schedule {
        margin-bottom: 10px;
        height: 109px;
        .btn_box {
          display: flex;
        }
        .btn {
          width: 130px;
          height: 34px;
          color: rgba(255, 255, 255, 0.9);
          text-align: center;
          line-height: 32px;
          border-radius: 4px;
          background-image: linear-gradient(
            to right,
            rgba(1, 83, 255, 1),
            rgba(134, 73, 255, 1)
          );
          cursor: pointer;
          user-select: none;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          margin: 15px 10px 10px 0;
        }
        .btn_disagree {
          background-image: linear-gradient(
            to right,
            rgba(255, 94, 85, 1),
            rgba(255, 142, 134, 1)
          );
        }
      }
      .right_score {
        flex: 3;
        margin-bottom: 10px;
        &_box {
          padding: 10px;
        }
        &_subtitle {
          background-image: linear-gradient(
            to right,
            rgba(224, 234, 255, 1),
            rgba(226, 239, 255, 1),
            rgba(245, 243, 255, 1)
          );
          color: rgba(0, 0, 0, 0.6);
          padding: 10px 15px;
          display: flex;
          justify-content: space-between;
          border-radius: 4px;
          margin: 10px 0;
          span {
            color: rgba(0, 71, 249, 1);
            cursor: pointer;
          }
        }
        .empty {
          margin: 50px;
          color: rgba(0, 0, 0, 0.6);
          &_icon {
            font-size: 26px;
            margin-bottom: 5px;
          }
          .btn {
            width: 103px;
            height: 32px;
            line-height: 32px;
            border-radius: 4px;
            margin: 10px auto;
            color: rgba(0, 0, 0, 0.9);
            background-image: linear-gradient(
              to right,
              rgba(246, 247, 251, 1),
              rgba(251, 248, 251, 1)
            );
            cursor: pointer;
          }
        }
      }
      .right_records {
        flex: 4;
        &_steps {
          overflow-y: scroll;
          margin: 10px;
          font-size: 12px;
          height: 300px;
          &::-webkit-scrollbar {
            width: 0;
          }
          .t-timeline-item__content {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
          }
          .t-timeline-item__label {
            font-size: 12px;
          }
        }
      }
      .empty {
        margin-top: 90px;
        &_icon {
          background-image: url('@/assets/images/empty-icon.png');
          background-position: center;
          background-repeat: no-repeat;
          background-size: 100px 70px;
          width: 100px;
          height: 70px;
          margin: 10px auto;
        }
      }
    }
  }
}
