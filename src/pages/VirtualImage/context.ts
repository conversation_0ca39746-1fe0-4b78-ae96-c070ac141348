import { DipListItem } from '@/type/api';
import { createContext } from 'react';
import { IVoiceItem } from '../VoiceList/VoiceLibrary/type';

export interface IVirtualImageConfigContextValue {
  // 处理使用该数字人后的逻辑
  renderUseDip: (data: {
    dipItem?: DipListItem;
    voiceItem?: IVoiceItem;
    destroy: () => void;
  }) => React.ReactNode;
}

// 配置context 用来实现差异化逻辑
export const VirtualImageConfigContext =
  createContext<IVirtualImageConfigContextValue>({
    renderUseDip: () => {
      return null;
    },
  });
