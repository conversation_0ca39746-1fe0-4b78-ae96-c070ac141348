import './index.less';
interface IProps {
  url: string;
  poster?: string;
}
export function VideoViewer(props: IProps) {
  return (
    <video
      controls
      className="video_preview"
      poster={props.poster}
      muted
      preload="metadata"
      controlsList="nodownload noremoteplayback noplaybackrate"
      playsInline
      disablePictureInPicture
    >
      <source src={props.url} type="video/webm" />
      <source src={props.url} type="video/mp4" />
      Download the
      <a href={props.url}>WEBM</a>
      or
      <a href={props.url}>MP4</a>
      video.
      <p>
        你的浏览器不支持 HTML5 视频。这里有一个
        <a href={props.url} download={props.url}>
          视频
        </a>
        链接。
      </p>
    </video>
  );
}
