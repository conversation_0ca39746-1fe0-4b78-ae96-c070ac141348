@import url('./reset.css');

/* overwrite the default reset CSS in tailwind CSS, fix the bug of vertical align bug in TDesign Table component  */
th, td {
    /* `vertical-align: middle` is the default setting for `td` and `th` elements in most browsers */
    vertical-align: middle; 
}

html,
body {
  width: 100%;
  height: 100%;
}

#root {
  width: 100%;
  height: 100%;

  /* 分页 */
  .t-pagination .t-pagination__pager .t-pagination__number.t-is-current {
    background: linear-gradient(96.87deg, #0048FF 10.75%, #6E87FF 100%);
    border: none;
  }

  .t-pagination .t-pagination__jump {
    background: linear-gradient(82.56deg, #F6F7FB -0.02%, #FCF8FB 99.98%);
  }

  /* tab */
  .t-tabs__bar {
    background: linear-gradient(88deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%);
  }
  /* menu */
  .t-head-menu .t-menu__logo:not(:empty) {
    margin-right: 0;
  }
  .t-menu__logo > * {
    margin-left: 0;
  }
}