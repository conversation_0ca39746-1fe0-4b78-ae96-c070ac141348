/**
 * <AUTHOR>
 * @date 2024/6/6 下午3:58
 * @desc globalConfig
 */

export interface IGlobalConfigItem {
  buildConf: {
    favicon: string;
    title: string;
    devBaseCDN: string;
    baseCDN: string;
    routesPath: string;
    cssCDN: string[];
  };
}

type IGlobalConfig = {
  [key in ImportMetaEnv['VITE_RUNNING_SYSTEM']]: IGlobalConfigItem;
};

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const GlobalConfig: IGlobalConfig = {
  PAGEDOO: {
    buildConf: {
      favicon: './logo.svg',
      title: '页匠智能化营销',
      devBaseCDN: '//dev-avatarcdn.pay.qq.com/dman/',
      baseCDN: '//avatarcdn.pay.qq.com/dman/',
      routesPath: '/src/routes.tsx',
      cssCDN: [],
    },
  },
  AD: {
    buildConf: {
      favicon: './ad-logo.svg',
      title: '腾讯广告 ｜ 妙播',
      devBaseCDN: '//avatarcdnams.pay.qq.com/intelligent/live/page/',
      baseCDN: '//avatarcdnams.pay.qq.com/intelligent/live/page/',
      routesPath: '/src/ad-routes.tsx',
      cssCDN: [
        // 'https://public.gdtimg.com/static/onex/lib/spaui-components/spaui/3.0.335-beta.0/spaui.css?max_age=31536000',
        // '//public.gdtimg.com/static/onex/lib/odc/1.0.270/odc.css',
        // '//qzonestyle.gdtimg.com/gdt_ui_proj/dist/tad-project/apps/creative-mb/index-176e6177.css',
      ],
    },
  },
} as IGlobalConfig;
GlobalConfig.debug = GlobalConfig.PAGEDOO;
