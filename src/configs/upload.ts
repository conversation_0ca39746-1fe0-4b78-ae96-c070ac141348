enum MATERIAL_TYPE {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  FILE = 'file',
}
enum UPLOAD_CODE {
  SUCCESS = 200,
  ERROR = 500,
}

const UPLOAD_FORM_CONFIG = {
  [MATERIAL_TYPE.IMAGE]: {
    multiple: true, // 是否支持多文件上传
    accept: ['.png', '.jpg', '.jpeg', '.gif', '.txt', '.svga'], // 接受的文件类型
    maxSize: 10240, // 上传文件大小限制，单位 kb
    maxLength: 20, // 多文件上传, 最大个数
  },
  [MATERIAL_TYPE.VIDEO]: {
    multiple: true, // 是否支持多文件上传
    accept: ['.mp4', '.mov', '.m4v'], // 接受的文件类型
    maxSize: 307200, // 上传文件大小限制，单位 kb
    ratio: '16:9', // 建议宽高比
  },
  [MATERIAL_TYPE.AUDIO]: {
    multiple: true, // 是否支持多文件上传
    accept: ['.mp3', '.amr', '.mpeg', '.wav'], // 接受的文件类型
    maxSize: 10240, // 上传文件大小限制，单位 kb
  },
  [MATERIAL_TYPE.FILE]: {
    multiple: true, // 是否支持多文件上传
    accept: [
      '.png',
      '.jpg',
      '.jpeg',
      '.gif',
      '.pdf',
      '.ppt',
      '.pptx',
      '.docx',
      '.doc',
      '.xlsx',
      '.xls',
      '.txt',
      '.mp4',
      '.mov',
      '.m4v',
      '.mp3',
      '.amr',
      '.mpeg',
      '.wav',
      '.zip',
      '.rar',
      '.7z',
      '.tar',
      '.gz',
    ], // 接受的文件类型
    maxSize: 204800, // 上传文件大小限制，单位 kb
    // maxSize: 1, // 上传文件大小限制，单位 kb
    maxLength: 10, // 多文件上传, 最大个数
  },
};

const COS_CONFIG = (() => {
  return {
    cdnBaseUrl: import.meta.env.VITE_UPLOAD_CDN_BASE_URL,
    cosBaseUrl: import.meta.env.VITE_UPLOAD_COS_BASE_URL,
  };
})();

export { UPLOAD_FORM_CONFIG, COS_CONFIG, MATERIAL_TYPE, UPLOAD_CODE };
