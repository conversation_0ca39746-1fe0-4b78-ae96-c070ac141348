import { renderUseVoice } from './voice';
import { renderUseDip } from './virtual-image';
import { IAssetsLibConfigContextValue } from '@/configs/config_context';
import { registerXNode } from '../xnodes';
import XNodes from '@/pages/ADPages/XNode';
import type { IGlobalRuntimeConfig } from '../global_runtime';
import { IUserInfoAtom } from '@/model/user';

export default {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  assetsLibConfig: {
    renderUseDip,
    renderUseVoice,
    virtualImageCustom: {
      OTQRCode: true,
    },
  } as IAssetsLibConfigContextValue,
  login: {
    checkUserInfoValid: async (userInfo: IUserInfoAtom): Promise<boolean> => {
      if (!userInfo?.adExtend?.account_id) return false;
      return true;
    },
  },
};

registerXNode('adAIServiceQRCode', XNodes.mobile);
