import { IAssetsLibConfigContextValue } from '@/configs/config_context';
import { lazy, Suspense } from 'react';
import { Loading } from 'tdesign-react';

export const LazyTemplateSelectorWrapper = lazy(() =>
  import('../components/ADTemplateSelector').then((mod) => {
    return {
      default: mod.Wrapper,
    };
  })
);

export const renderUseDip: IAssetsLibConfigContextValue['renderUseDip'] = (
  data
) => {
  return (
    <Suspense fallback={<Loading />}>
      <LazyTemplateSelectorWrapper {...data} />
    </Suspense>
  );
};
