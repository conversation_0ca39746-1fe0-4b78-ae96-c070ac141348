import { BindShopEntry } from '@/configs/admuse/product-lib/BindShop/BindShopEntry';
import LayoutsWithLogin, { LoginWrapper } from '@/layouts';
import { ADGlobal } from '@/pages/ADPages/AdGlobal';
import { cloneElement, ReactElement } from 'react';
import { RouteObject } from 'react-router-dom';

const blackList = ['/Editor'];
export const patchRoutes = (routes: RouteObject[]) => {
  routes.forEach((route) => {
    if (route.element) {
      const routeEl = route.element as ReactElement;
      if (
        typeof routeEl.type === 'function' &&
        [LayoutsWithLogin, LoginWrapper]
          .map((el) => el.displayName)
          .includes((routeEl.type as any)?.displayName)
      ) {
        route.element = cloneElement(routeEl, {
          disableAutoLogin: false,
          renderCustom: (old: React.ReactNode) => {
            // const isBlack = blackList.includes(route.path || '');
            // TODO: 待确认
            // const el = isBlack ? old : <BindShopEntry>{old}</BindShopEntry>;
            return <ADGlobal>{old}</ADGlobal>;
          },
        });
      }
    }
  });
};
