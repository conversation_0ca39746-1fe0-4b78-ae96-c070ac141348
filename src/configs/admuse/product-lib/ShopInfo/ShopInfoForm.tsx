import { createDelayPromise } from '@/utils/delay-promise';
import { css } from '@emotion/react';
import React, {
  forwardRef,
  PropsWithChildren,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import { Button, Form, Input, Space } from 'tdesign-react';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { ACCOUNT_ROLE_MAP } from '@/pages/MuseLogin/config';
const { FormItem } = Form;
export interface IShopInfoValues {
  mini_shop_id: string;
  mini_shop_key?: string;
}
export interface IShopInfoFormProps {
  /**
   * submit
   */
  onSubmit?: (value: IShopInfoValues) => void;
  defaultValues?: IShopInfoValues;
}
export interface IShopInfoFormInstance {
  /**
   * @throws
   */
  submit: () => Promise<IShopInfoValues>;
}

export const ShopInfoForm = forwardRef<
  IShopInfoFormInstance,
  PropsWithChildren<IShopInfoFormProps>
>((props: PropsWithChildren<IShopInfoFormProps>, ref) => {
  const { children, defaultValues, onSubmit } = props;
  const [form] = Form.useForm();
  const [userState] = useRecoilState(UserInfoAtom);
  const submitPromiseRef =
    useRef<ReturnType<typeof createDelayPromise<IShopInfoValues>>>();
  useImperativeHandle(ref, () => ({
    submit: async () => {
      if (!submitPromiseRef.current) {
        submitPromiseRef.current = createDelayPromise<IShopInfoValues>();
      }
      form.submit();
      return await submitPromiseRef.current.promise;
    },
  }));
  const memoDefaultValues = useMemo(() => {
    return {
      ...defaultValues,
      mini_shop_key: defaultValues?.mini_shop_key || '',
    };
  }, [defaultValues]);

  const labelValue = useMemo(() => {
    return {
      mini_shop_id:
        `${ACCOUNT_ROLE_MAP[userState?.adExtend?.appType]}ID` || 'ID',
      mini_shop_key:
        `${ACCOUNT_ROLE_MAP[userState?.adExtend?.appType]}密钥` || '密钥',
    };
  }, [userState?.adExtend?.appType]);

  return (
    <Form
      form={form}
      layout="vertical"
      labelWidth={65}
      labelAlign="left"
      initialData={memoDefaultValues}
      onSubmit={() => {
        const values = form.getFieldsValue(true) as IShopInfoValues;
        if (submitPromiseRef.current) {
          submitPromiseRef.current.resolve(values);
          submitPromiseRef.current = undefined;
        }
        onSubmit?.(values);
      }}
      css={css`
        .t-form__label {
          color: rgba(0, 0, 0, 0.6);
        }
      `}
    >
      <FormItem label={labelValue.mini_shop_id} name="mini_shop_id">
        <Input
          placeholder="请输入小店id"
          disabled={!!defaultValues?.mini_shop_id}
        />
      </FormItem>
      <FormItem label={labelValue.mini_shop_key} name="mini_shop_key">
        <Input placeholder="请输入小店秘钥" />
      </FormItem>
      {children}
    </Form>
  );
});
