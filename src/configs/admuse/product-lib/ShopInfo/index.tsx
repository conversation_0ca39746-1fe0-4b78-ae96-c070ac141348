import successIcon from '@/assets/images/create-condition-success.png';
import {
  ShopInfoApiContext,
  useOperateShopInfo,
} from '@/configs/admuse/product-lib/hooks/useShopInfo';
import { ShopInfoModifyDialog } from '@/configs/admuse/product-lib/ShopInfo/ShopInfoModifyDialog';
import { useMemoizedFn } from 'ahooks';
import React, { useMemo, useState } from 'react';
import { ErrorCircleIcon } from 'tdesign-icons-react';
import { Button, DialogPlugin, Image } from 'tdesign-react';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { ACCOUNT_ROLE_MAP } from '@/pages/MuseLogin/config';

export interface IShopInfoProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'className' | 'style'> {
  children: (api: ReturnType<typeof useOperateShopInfo>) => React.ReactNode;
}
/**
 * 小店信息组件
 */
export function ShopInfo(props: IShopInfoProps) {
  const { children, className, style } = props;
  const [userState] = useRecoilState(UserInfoAtom);
  const [showModify, setShowModify] = useState(false);
  const queryShopApi = useOperateShopInfo();
  const memoQueryShopApi = useMemo(
    () => ({
      ...queryShopApi,
      shopInfo: queryShopApi.selectedShopInfo!,
    }),
    [queryShopApi]
  );

  const showSuccessDialog = useMemoizedFn(async () => {
    const dialogInstance = DialogPlugin.alert({
      destroyOnClose: true,
      footer: null,
      width: '425px',
      body: (
        <div className="pagedoo-meta-live-global flex flex-col items-center">
          <Image
            src={successIcon}
            style={{
              width: '113px',
              background: 'transparent',
            }}
            fit="contain"
          />
          <h3 className="text-16 font-semibold text-black mt-16 mb-16">
            小店修改成功
          </h3>
          <span className="mb-32">
            将重新拉取该小店下的商品，请及时补充商品知识点，确保商品话术能正常生成
          </span>
          <Button
            theme="primary"
            className="gradient-primary"
            style={{
              width: '126px',
              height: '32px',
            }}
            onClick={() => {
              dialogInstance.hide();
            }}
          >
            我知道了
          </Button>
        </div>
      ),
    });
  });

  return (
    <ShopInfoApiContext.Provider value={memoQueryShopApi}>
      <div
        className={`rounded-8 flex justify-between ${className} items-center`}
        style={{
          ...style,
          height: '62px',
          background: '#ffffff',
          padding: '0 24px 0 20px',
        }}
      >
        <div
          className="flex items-center"
          style={{
            gap: '18px',
          }}
        >
          <h3
            x-if={memoQueryShopApi.shopInfo?.shopName}
            className="text-18"
            style={{ margin: 0 }}
          >
            {memoQueryShopApi.shopInfo?.shopName || ''}
          </h3>
          <span
            x-if={memoQueryShopApi.shopInfo?.shopId}
            className="text-14"
            style={{
              color: 'rgba(0, 0, 0, 0.4)',
            }}
          >
            id：{memoQueryShopApi.shopInfo?.shopId}
          </span>
          <div
            x-if={queryShopApi.syncing}
            className="flex items-center gap justify-center p-8 rounded-4 cursor-default select-none"
            style={{
              background: 'linear-gradient(90deg, #FFEFE5 0%, #FFF4ED 100%)',
              color: 'rgba(234, 112, 23, 1)',
            }}
          >
            <ErrorCircleIcon
              width={14}
              height={14}
              style={{
                width: '14px',
                height: '14px',
              }}
            />
            <span>小店商品正在同步中，请耐心等待...</span>
          </div>
        </div>
        <div
          className="text-brand-7 flex items-center cursor-pointer"
          style={{
            gap: '3px',
          }}
          onClick={() => setShowModify(true)}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.881 1.73738L14.1243 4.98076L14.8314 4.27365L11.5881 1.03027L10.881 1.73738Z"
              fill="currentColor"
            />
            <path
              d="M2.35119 13.8637L5.96415 13.1411L13.2618 5.84347L10.0184 2.60009L2.72077 9.89769L1.99818 13.5107C1.9562 13.7206 2.14127 13.9056 2.35119 13.8637ZM10.0184 4.0143L11.8475 5.84347L5.47114 12.2199L3.18468 12.6772L3.64197 10.3907L10.0184 4.0143Z"
              fill="currentColor"
            />
          </svg>
          <span>
            修改{ACCOUNT_ROLE_MAP[userState?.adExtend?.appType] || ''}信息
          </span>
        </div>
      </div>
      {children(queryShopApi)}
      <ShopInfoModifyDialog
        show={showModify}
        onClose={() => {
          setShowModify(false);
        }}
        onSuccess={() => {
          setShowModify(false);
          showSuccessDialog();
        }}
        defaultValues={
          memoQueryShopApi.shopInfo
            ? {
                mini_shop_id: memoQueryShopApi.shopInfo.appId,
                mini_shop_key: memoQueryShopApi.shopInfo.appId
                  ? '*'.repeat(6)
                  : '',
              }
            : undefined
        }
      />
    </ShopInfoApiContext.Provider>
  );
}
