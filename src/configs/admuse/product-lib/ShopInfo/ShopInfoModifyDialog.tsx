import { useCallback, useContext, useRef } from 'react';
import {
  Button,
  Dialog,
  DialogInstance,
  DialogProps,
  MessagePlugin,
  Space,
} from 'tdesign-react';
import {
  IShopInfoFormInstance,
  IShopInfoFormProps,
  ShopInfoForm,
} from './ShopInfoForm';
import '@/global.less';
import { css } from '@emotion/react';
import { ShopInfoApiContext } from '@/configs/admuse/product-lib/hooks/useShopInfo';
import to from 'await-to-js';
import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { ACCOUNT_ROLE_MAP } from '@/pages/MuseLogin/config';
import { CreateAccount } from '@/pb/api/DigitalManProc';
export interface IShopInfoModifyDialogProps
  extends Pick<IShopInfoFormProps, 'defaultValues'>,
    Pick<DialogProps, 'onClose'> {
  show: boolean;
  onSuccess?: () => void;
}
export function ShopInfoModifyDialog(props: IShopInfoModifyDialogProps) {
  const { show, onSuccess, defaultValues, ...rest } = props;
  const { onClose } = rest;
  const [userState] = useRecoilState(UserInfoAtom);
  const formRef = useRef<IShopInfoFormInstance>(null);
  const dialogRef = useRef<DialogInstance>(null);
  const shopInfoApi = useContext(ShopInfoApiContext)!;

  const handleSubmit = useCallback<Required<IShopInfoFormProps>['onSubmit']>(
    async (values) => {
      // TODO： 调用修改小店信息接口
      // if (isEqual(values, defaultValues)) {
      //   onClose?.({
      //     e: new MouseEvent(
      //       'click'
      //     ) as unknown as React.MouseEvent<HTMLElement>,
      //     trigger: 'cancel',
      //   });
      //   return;
      // }
      if (!defaultValues?.mini_shop_id) {
        const [err] = await to(
          CreateAccount({
            host: {
              appid: values.mini_shop_id,
              secret: values.mini_shop_key,
            },
            shop: {
              appid: values.mini_shop_id,
              secret: values.mini_shop_key,
            },
          })
        );
        if (!err) {
          onSuccess?.();
        } else {
          console.error(err);
          void MessagePlugin.error(err.message ?? '提交失败，请稍后重试');
        }
        return;
      }
      const key =
        values.mini_shop_key === defaultValues?.mini_shop_key
          ? ''
          : values.mini_shop_key;
      const [err] = await to(
        shopInfoApi.modifyShopInfo({
          appId: values.mini_shop_id,
          shopKey: key || '',
        })
      );
      if (!err) {
        onSuccess?.();
      } else {
        MessagePlugin.error('修改小店信息失败');
      }
    },
    [defaultValues, onSuccess, shopInfoApi]
  );
  return (
    <Dialog
      {...rest}
      visible={show}
      destroyOnClose
      header={`${ACCOUNT_ROLE_MAP[userState?.adExtend?.appType] || ''}信息`}
      ref={dialogRef}
      css={css`
        .t-dialog {
          padding: 0 !important;
        }
        .t-dialog__header {
          padding: 16px 24px 0 24px;
        }
        .t-dialog__footer {
          border-top: 1px solid rgba(231, 231, 231, 1);
          padding: 11px 0 !important;
          margin-top: 24px;
        }
        .t-dialog__body {
          padding: 48px 24px 0 24px !important;
        }
      `}
      footer={
        <div
          className="pagedoo-meta-live-global"
          style={{
            marginRight: '23px',
          }}
        >
          <Space>
            <Button
              theme="default"
              className="gradient-default"
              onClick={(e) => {
                rest.onClose?.({
                  e,
                  trigger: 'cancel',
                });
              }}
            >
              取消
            </Button>
            <Button
              theme="primary"
              className="gradient-primary"
              onClick={() => formRef.current?.submit()}
            >
              确定修改
            </Button>
          </Space>
        </div>
      }
    >
      <ShopInfoForm
        x-if={defaultValues}
        ref={formRef}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
      />
    </Dialog>
  );
}
