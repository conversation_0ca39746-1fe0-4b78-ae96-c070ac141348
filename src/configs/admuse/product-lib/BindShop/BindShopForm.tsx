import to from 'await-to-js';
import React, { useCallback, useState } from 'react';
import { Button, Form, Input, Space } from 'tdesign-react';
import { IShopInfoValues, ShopInfoForm } from '../ShopInfo/ShopInfoForm';
import { RespError } from '@/pb/config';
import { useMemoizedFn } from 'ahooks';
import { CloseCircleFilledIcon } from 'tdesign-icons-react';

const { FormItem } = Form;
export interface IBindShopFormProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'style' | 'className'> {
  /**
   * @throws {Error | RespError}
   */
  requestBindShop: (data: IShopInfoValues) => Promise<void>;
  onSubmit?: (values: IShopInfoValues) => Promise<void> | void;
  onBindSuccess?: () => void;
  onSkip?: () => void;
  /**
   * 隐藏跳过绑定的按钮
   */
  hideSkipBtn?: boolean;
}
export function BindShopForm(props: IBindShopFormProps) {
  const {
    className,
    style,
    onSubmit,
    onSkip,
    hideSkipBtn,
    requestBindShop,
    onBindSuccess,
  } = props;
  const [submitting, setSubmitting] = useState(false);

  const [submitErr, setSubmitErr] = useState<{
    errMsg: string;
  }>();

  const handleSubmit = useMemoizedFn(async (values: IShopInfoValues) => {
    setSubmitErr(void 0);
    let errMsg = '';
    if (!values.mini_shop_id) errMsg = '请输入小店ID';
    if (!values.mini_shop_key) errMsg = '请输入小店秘钥';
    if (errMsg) {
      setSubmitErr({ errMsg });
      return;
    }
    setSubmitting(true);
    (async () => {
      const [err] = await to(
        requestBindShop({
          ...values,
          mini_shop_key: values.mini_shop_key || '',
        })
      );
      if (err) {
        // 展示错误
        let errMsg = '绑定失败';
        if (err instanceof RespError) {
          errMsg = err.resultInfo || err.message;
        }
        setSubmitErr({ errMsg });
        return;
      }
      if (typeof onSubmit === 'function') {
        try {
          await onSubmit(values);
        } catch (e) {
          setSubmitErr({ errMsg: '获取小店绑定信息失败' });
          return;
        } finally {
        }
      }
      onBindSuccess?.();
    })().finally(() => {
      setSubmitting(false);
    });
  });
  return (
    <div
      className={`${
        className || ''
      } pagedoo-meta-live-global tdesign-disable__ripple flex flex-col`}
      style={{
        ...style,
      }}
    >
      <div className="flex justify-between mb-24">
        <h3
          className="text-14 font-semibold"
          style={{
            color: 'rgba(0, 0, 0, 0.9)',
          }}
        >
          请填写小店信息
        </h3>
        <div
          x-if={!!submitErr}
          className="flex gap-1 items-center text-14 font-normal"
          style={{
            color: 'rgba(255, 94, 85, 1)',
          }}
        >
          <CloseCircleFilledIcon />
          <span>{submitErr?.errMsg || ''}</span>
        </div>
      </div>
      <ShopInfoForm onSubmit={handleSubmit}>
        <FormItem>
          <Space>
            <Button
              type="submit"
              theme="primary"
              className="gradient-primary"
              style={{
                width: '126px',
              }}
              disabled={submitting}
              loading={submitting}
            >
              提交并绑定
            </Button>
            <Button
              x-if={!hideSkipBtn}
              theme="default"
              className="gradient-default"
              onClick={() => onSkip?.()}
            >
              跳过绑定
            </Button>
          </Space>
        </FormItem>
      </ShopInfoForm>
    </div>
  );
}
