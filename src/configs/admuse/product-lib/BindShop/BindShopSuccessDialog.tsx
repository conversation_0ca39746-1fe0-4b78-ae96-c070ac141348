import React from 'react';
import { But<PERSON>, Dialog, Image } from 'tdesign-react';
import successIcon from '@/assets/images/create-condition-success.png';
import '@/global.less';

export interface IBindShopSuccessDialogProps {
  show: boolean;
  onClose: () => void;
}
export function BindShopSuccessDialog(props: IBindShopSuccessDialogProps) {
  const { show, onClose } = props;
  return (
    <Dialog visible={show} destroyOnClose footer={null} onClose={onClose}>
      <div className="pagedoo-meta-live-global flex flex-col items-center">
        <Image
          src={successIcon}
          style={{
            width: '113px',
            background: 'transparent',
          }}
          fit="contain"
        />
        <h3 className="text-16 font-semibold text-black mt-16 mb-16">
          小店绑定成功
        </h3>
        <span className="mb-32">
          接下来，您可以您可以对该小店商品知识库进行维护
        </span>
        <Button
          theme="primary"
          className="gradient-primary"
          style={{
            width: '126px',
            height: '32px',
          }}
          onClick={onClose}
        >
          我知道了
        </Button>
      </div>
    </Dialog>
  );
}
