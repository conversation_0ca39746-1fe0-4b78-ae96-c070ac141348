import { Topbar } from '@/components/Layout';
import { BindShopSuccessDialog } from '@/configs/admuse/product-lib/BindShop/BindShopSuccessDialog';
import {
  useCheckBindShop,
  useOperateShopInfo,
} from '@/configs/admuse/product-lib/hooks/useShopInfo';
import '@/global.less';
import { PropsWithChildren, ReactNode, useLayoutEffect, useState } from 'react';
import { Loading } from 'tdesign-react';
import { IBindShopFormProps } from './BindShopForm';

export interface IBindShopEntryProps
  extends Pick<IBindShopFormProps, 'hideSkipBtn'> {
  /**
   * 忽略 skip跳过校验
   */
  ignoreSkip?: boolean;
  /**
   * 嵌入模式
   */
  nested?: boolean;
}
/**
 * 绑定视频号小店
 */
export function BindShopEntry(props: PropsWithChildren<IBindShopEntryProps>) {
  const { children, ignoreSkip, nested } = props;
  const { inited, loading } = useCheckBindShop({
    ignoreSkip,
  });
  const queryShopApi = useOperateShopInfo();
  const { setSelectedShopInfo, shopInfoList } = queryShopApi;

  const [showBindSuccess, setShowBindSucc] = useState(false);
  /**
   * 默认选中逻辑
   */
  useLayoutEffect(() => {
    if (!(Array.isArray(shopInfoList) && shopInfoList.length > 0)) return;
    let maxShopInfo = shopInfoList[0];
    /** 取最大的shopId */
    shopInfoList.forEach((info) => {
      if (info.shopId > maxShopInfo.shopId) {
        maxShopInfo = info;
      }
    });
    setSelectedShopInfo(maxShopInfo);
  }, [
    queryShopApi.setSelectedShopInfo,
    queryShopApi.shopInfoList,
    setSelectedShopInfo,
    shopInfoList,
  ]);
  let el: React.ReactNode;
  if (loading) el = <Loading fullscreen />;
  else if (!inited) el = null;
  else {
    const innerEl: ReactNode = (
      <>
        <BindShopSuccessDialog
          show={showBindSuccess}
          onClose={() => {
            setShowBindSucc(false);
          }}
        />
        {children}
      </>
    );
    return innerEl;
  }
  return (
    <div className="h-full flex flex-col">
      <Topbar x-if={!nested} />
      {el}
    </div>
  );
}
