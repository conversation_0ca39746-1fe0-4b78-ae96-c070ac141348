import { BindShopInfo<PERSON>tom, SelectedShopInfoAtom, ShopInfoAtom } from '../store';
import { createDelayPromise } from '@/utils/delay-promise';
import { createContext, useCallback, useMemo, useRef, useState } from 'react';
import { useRecoilState } from 'recoil';
import { IShopInfo } from '../typings';
import {
  ListShops,
  CreateShop,
  CreateShopRequest,
  GetAccountInfo,
  SyncProduct,
  UpdateAccountInfo,
} from '@/pb/api/DigitalManProc';
import to from 'await-to-js';
import { useMemoizedFn, useMount, useUnmount } from 'ahooks';
import {
  clearLocalStorage,
  getLocalStorageWithExpiry,
  setLocalStorageWithExpiry,
} from '@/utils/localStorage';

const skipCacheKey = '__avatar_ad_skip_bind_shop';
/**
 * 检查账号下小点的绑定状态
 */
export const useCheckBindShop = (options?: { ignoreSkip?: boolean }) => {
  const { ignoreSkip } = options || {};
  const [bindShopInfoState, setBindShopInfoState] =
    useRecoilState(BindShopInfoAtom);
  const { skipped } = bindShopInfoState;
  const [inited, setInited] = useState(false);
  const [loading, setLoading] = useState(false);
  const { queryShopInfo, bindShop, hasBinded } = useOperateShopInfo();
  const skip = useCallback(() => {
    // 本地记录跳过小店绑定
    setLocalStorageWithExpiry(skipCacheKey, '1', 30 * 24 * 3600);
    setBindShopInfoState((prev) => ({ ...prev, skipped: true }));
  }, [setBindShopInfoState]);

  const doQueryShopInfo = useCallback(async () => {
    setLoading(true);
    return queryShopInfo()
      .then(() => {
        setInited((prev) => {
          if (!prev) return true;
          return prev;
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, [queryShopInfo]);

  /**
   * 如果已绑定，清理 skip标记
   */
  useMemo(() => {
    if (!hasBinded) return;
    clearLocalStorage(skipCacheKey);
  }, [hasBinded]);

  /**
   * 挂载时初始化
   */
  useMount(() => {
    // 读取localStorage 设置skip 标记位
    const flag = getLocalStorageWithExpiry(skipCacheKey);
    if (flag === '1') {
      setBindShopInfoState((prev) => ({ ...prev, skipped: true }));
    }
    if (!hasBinded) {
      // 未绑定时首次挂载时进行查询
      doQueryShopInfo();
    } else {
      setInited(true);
    }
  });

  return {
    loading,
    inited,
    skip,
    queryShopInfo: doQueryShopInfo,
    bindShop,
    hasBinded,
  };
};

/**
 * 查询小店信息
 */
export const useOperateShopInfo = () => {
  const [shopInfoList, setShopInfoList] = useRecoilState(ShopInfoAtom);

  /**
   * 当前选中的小店
   */
  const [selectedShopInfo, setSelectedShopInfo] =
    useRecoilState(SelectedShopInfoAtom);
  // const [shopInfo, setShopInfo] = useState<IShopInfo[]>();
  const [synced, setSynced] = useState(false);

  /**
   * 小店商品同步状态
   */
  const [syncing, setSyncing] = useState(false);

  // const eventBusRef = useRef(new EventBus<{ modified: () => void }>());
  const [modifiedPromise, setModifiedPromise] =
    useState<ReturnType<typeof createDelayPromise<void>>>();

  const syncAbortCtrlRef = useRef<AbortController>();

  // const hasBinded = shopInfoList.length > 0;

  const hasBinded = useMemo(() => {
    return !!shopInfoList?.[0]?.appId;
  }, [shopInfoList]);
  /**
   * 绑定小店
   */
  const bindShop = useCallback(async (data: CreateShopRequest) => {
    return await CreateShop(data);
  }, []);

  /**
   * @throws
   */
  // const queryShopInfo = useCallback(async () => {
  //   const [err, shopList] = await to(ListShops({}));
  //   if (err) throw err;
  //   const shopInfoList: IShopInfo[] = shopList.map((sh) => ({
  //     appId: sh.shop_app_id,
  //     shopId: sh.shop_id,
  //     lastSyncTime: sh.last_sync_time,
  //     shopName: sh.shop_name,
  //   }));
  //   setShopInfoList(shopInfoList);
  //   return shopInfoList;
  // }, [setShopInfoList]);

  const queryShopInfo = useCallback(async () => {
    const [err, res] = await to(
      GetAccountInfo({
        // account_id: '************',
        // account_type: 'AI_LIVE_ACCOUNT_TYPE_SHOP',
        // account_id: '************',
        // account_type: 'AI_LIVE_ACCOUNT_TYPE_HOST',
      })
    );
    // debugger;
    // 兼容广告新的登录兜底
    if (err) {
      setShopInfoList([
        {
          appId: '',
          shopId: '',
          lastSyncTime: '',
          shopName: '',
        },
      ]);
      return;
    }
    const shopInfoList: IShopInfo[] = [
      {
        appId: res?.account?.shop?.appid || res?.account?.host?.appid,
        shopId: res?.account?.shop?.shop_id || '',
        lastSyncTime: res?.account.last_sync_time || '',
        shopName:
          res?.account?.shop?.shop_name || res?.account?.host?.nickname || '',
      },
    ];

    setShopInfoList(shopInfoList);
    return shopInfoList;
  }, [setShopInfoList]);

  /**
   * 轮询小店商品同步状态
   * @throws {Error | RespError} 超时，或者aborted
   */
  const pollSelectedShopInfo = useMemoizedFn(
    (
      selectedShopInfo: IShopInfo,
      options: {
        interval: number;
        timeout: number;
        abortController?: AbortController;
      }
    ) => {
      const { abortController = new AbortController() } = options;
      const { promise, reject, resolve } = createDelayPromise<void>();
      let timeout: NodeJS.Timeout;
      const startTime = new Date();
      const query = async () => {
        if (Date.now() - startTime.getTime() > options.timeout) {
          reject(new Error('timeout'));
          return;
        }
        const [err, shopInfo] = await to(
          ListShops({
            shop_id: selectedShopInfo.shopId,
          })
        );
        if (abortController.signal.aborted) return;
        if (err) {
          reject(err);
          return;
        }
        const [singleShopInfo] = shopInfo;
        if (!singleShopInfo) {
          return reject(new Error('shop not found'));
        }
        if (singleShopInfo.last_sync_time > selectedShopInfo.lastSyncTime) {
          resolve();
          return;
        }
        timeout = setTimeout(query, options.interval);
      };
      const handleAbort = () => {
        abortController.signal.removeEventListener('abort', handleAbort);
        clearTimeout(timeout);
        reject(new Error('aborted'));
      };
      abortController.signal.addEventListener('abort', handleAbort);
      timeout = setTimeout(query, options.interval);
      return promise;
    }
  );

  /**
   * 同步小店信息，默认一分钟超时时间
   * @throws {Error | RespError}
   */
  const syncShopInfo = useCallback(
    async (options?: { timeout?: number }) => {
      if (!selectedShopInfo) return;
      // setSyncing(true);
      if (syncAbortCtrlRef.current) syncAbortCtrlRef.current.abort();
      syncAbortCtrlRef.current = new AbortController();
      return (async () => {
        const { timeout = 30 * 1000 } = options ?? {};
        setSyncing(true);
        setSynced(false);
        const [err] = await to(
          // SyncShopProducts({
          //   shop_id: selectedShopInfo.shopId,
          // })
          SyncProduct({
            // account_id: '************',
            // account_type: 'AI_LIVE_ACCOUNT_TYPE_SHOP',
            // account_id: '************',
            // account_type: 'AI_LIVE_ACCOUNT_TYPE_HOST',
          })
        );
        if (err) throw err;
        await pollSelectedShopInfo(selectedShopInfo, {
          interval: 5000,
          timeout,
          abortController: syncAbortCtrlRef.current,
        });
        await queryShopInfo();
        setSynced(true);
      })().finally(() => {
        syncAbortCtrlRef.current = undefined;
        setSyncing(false);
      });
    },
    [pollSelectedShopInfo, queryShopInfo, selectedShopInfo]
  );

  /**
   * @throws
   */
  const modifyShopInfo = useCallback(
    async (
      shopInfo: Omit<IShopInfo, 'lastSyncTime' | 'shopId' | 'shopName'> & {
        shopKey: string;
      }
    ) => {
      if (!selectedShopInfo) return;
      const promise = modifiedPromise || createDelayPromise<void>();
      if (modifiedPromise) return modifiedPromise;
      // 提交修改小店信息
      setModifiedPromise(promise);
      const [err] = await to(
        UpdateAccountInfo({
          // account_id: '************',
          // account_type: 'AI_LIVE_ACCOUNT_TYPE_SHOP',
          // account_id: '************',
          // account_type: 'AI_LIVE_ACCOUNT_TYPE_HOST',
          host: {
            appid: shopInfo.appId,
            secret: shopInfo.shopKey,
          },
          shop: {
            appid: shopInfo.appId,
            secret: shopInfo.shopKey,
          },
        })
      );
      await queryShopInfo();
      err ? promise.reject(err) : promise.resolve();
      setModifiedPromise(undefined);
      if (err) throw err;
    },
    [modifiedPromise, queryShopInfo, selectedShopInfo]
  );

  useUnmount(() => {
    if (syncAbortCtrlRef.current) syncAbortCtrlRef.current.abort();
  });

  return useMemo(
    () => ({
      shopInfoList,
      queryShopInfo,
      modifyShopInfo,
      syncShopInfo,
      modifiedPromise,
      synced,
      bindShop,
      selectedShopInfo,
      setSelectedShopInfo,
      syncing,
      hasBinded,
    }),
    [
      syncing,
      shopInfoList,
      queryShopInfo,
      modifyShopInfo,
      syncShopInfo,
      modifiedPromise,
      synced,
      bindShop,
      selectedShopInfo,
      setSelectedShopInfo,
      hasBinded,
    ]
  );
};

export type ShopInfoApiContextType = ReturnType<typeof useOperateShopInfo> & {
  shopInfo: IShopInfo;
};

export const ShopInfoApiContext = createContext<
  ShopInfoApiContextType | undefined
>(undefined);
