import { IShopInfo } from './typings';
import { atom } from 'recoil';

export const BindShopInfoAtom = atom<{
  skipped: boolean;
}>({
  key: 'BindShopInfoAtom',
  default: {
    skipped: false,
  },
});

/**
 * 全局小店信息
 */
export const ShopInfoAtom = atom<IShopInfo[]>({
  key: 'ShopInfoAtom',
  default: [],
});

export const SelectedShopInfoAtom = atom<IShopInfo | null>({
  key: 'SelectedShopInfoAtom',
  default: null,
});
