import { CommonCategoryWrapper } from '@/components/Category';
import { CategoryButton } from '@/components/Category/CategoryButton';
import { CategoryHandlerType } from '@/components/Category/typings';
import { SelectCard } from '@/components/SelectCard';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { ADSelectProductModal } from '@/pages/ADMetaList/ADSelectProductModal';
import { FlattenSelect } from '@/components/FlattenSelect';
import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';
import { ResourceSvr } from '@/pb/pb';
import { DipListItem } from '@/type/api';
import { PromiseType } from '@/utils/type-util';
import { css } from '@emotion/react';
import { useDeepCompareLayoutEffect } from 'ahooks';
import to from 'await-to-js';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { But<PERSON>, Dialog, Loading } from 'tdesign-react';
import Styles from './selector.module.less';

export interface ITemplateItem {
  // 模版I
  templateId: string;
  title: string;
  src: string;
  desc: string;
}
export interface IAdTemplateSelectorProps {
  onSelect: (item?: ITemplateItem) => void;
}

type ResourceListItem = PromiseType<
  ReturnType<(typeof ResourceSvr)['QueryResourceList']>
>['resource_info_list'][0];

function TemplateList(props: {
  categories: string[];
  categoryType: string;
  onSelect: (item?: ResourceListItem) => void;
}) {
  const { categories, categoryType } = props;
  const [resourceList, setResourceList] = useState<ResourceListItem[]>([]);
  const [queryResourceErr, setQueryResourceErr] = useState('');
  const [currentItem, setCurrentItem] = useState<ResourceListItem>();

  const queryResourceList = useCallback(async () => {
    setQueryResourceErr('');
    const [err, resp] = await to(
      ResourceSvr.QueryResourceList({
        app_code: MatchedGlobalConfigItem.appcode,
        category_level1: categories?.[0] || 'all',
        category_level2: categories?.[1] || 'all',
        category_type: categoryType,
        page_num: 1,
        page_size: 999,
      })
    );
    if (err) {
      setQueryResourceErr(`查询模版列表失败，${err.message}`);
      return;
    }
    setResourceList(resp.resource_info_list);
  }, [categories, categoryType]);

  useDeepCompareLayoutEffect(() => {
    queryResourceList();
    // 分类变化后置空
    setCurrentItem(undefined);
    props.onSelect(undefined);
  }, [categories]);

  // useDeepCompareEffect(() => {
  //   if (!currentItem) return;
  // }, [currentItem]);

  return (
    <div
      css={css`
        display: flex;
        flex-wrap: wrap;
        overflow: hidden auto;
        column-gap: calc((100% - 145px * 5) / 4);
        row-gap: 8px;
        padding: 4px 0 0 4px;
        margin-top: 16px;
      `}
    >
      {resourceList.map((item) => {
        return (
          <SelectCard
            data={item}
            title={item.resource_name}
            image={item.image_address}
            imageProps={{
              fit: 'cover',
              style: {
                width: '100%',
                height: '262px',
                flex: 1,
              },
              loading: <Loading />,
            }}
            active={currentItem === item}
            style={{
              // width: 'calc(((100% - 40px) - 4 * 20px) / 5)',
              width: '141px',
              height: '300px',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              cursor: 'pointer',
            }}
            bottomStyle={{
              container: {
                // flex: 1,
              },
              title: {
                margin: 0,
              },
            }}
            onSelect={() => {
              console.log('TemplateListSelect ', item);
              setCurrentItem(item);
              props.onSelect(item);
            }}
          />
        );
      })}
    </div>
  );
}

// 广告模版选择器
export function ADTemplateSelector(props: IAdTemplateSelectorProps) {
  return (
    <CommonCategoryWrapper
      categoryType="template"
      defaultCategory={['all', 'all']}
      renderCategoryList={({ categoryList }) => {
        const second = categoryList[1];
        const first = categoryList[0];
        return (
          <div
            css={css`
              display: flex;
              flex-direction: column;
              gap: 20px;
            `}
          >
            <FlattenSelect<CategoryHandlerType | undefined>
              value={first.value}
              // style={{
              //   marginTop: '20px',
              // }}
              options={first.data.map((item) => ({
                key: item.id,
                label: item.name,
                value: item,
                render: ({ active, onSelect, label, value }) => {
                  return (
                    <CategoryButton
                      key={value.id}
                      selected={active}
                      onClick={() => onSelect(value)}
                    >
                      {label}
                    </CategoryButton>
                  );
                },
              }))}
              onChange={async (value) => {
                value?.onSelect();
              }}
            />
            {second && (
              <FlattenSelect<CategoryHandlerType | undefined>
                value={second.value}
                // style={{
                //   marginTop: '20px',
                // }}
                options={second.data.map((item) => ({
                  key: item.id,
                  label: item.name,
                  value: item,
                  render: ({ active, onSelect, label, value }) => {
                    return (
                      <CategoryButton
                        key={value.id}
                        selected={active}
                        onClick={() => onSelect(value)}
                      >
                        {label}
                      </CategoryButton>
                    );
                  },
                }))}
                onChange={async (value) => {
                  value?.onSelect();
                }}
              />
            )}
          </div>
        );
      }}
    >
      {({ categories, categoryType }) => {
        return (
          <TemplateList
            categories={categories}
            categoryType={categoryType}
            onSelect={(item) => {
              if (!item) {
                props.onSelect();
                return;
              }
              props.onSelect({
                templateId: item.resource_id,
                desc: item.resource_name,
                title: item.resource_name,
                src: item.image_address,
              });
            }}
          />
        );
      }}
    </CommonCategoryWrapper>
  );
}

export function Wrapper(props: {
  dipItem?: DipListItem;
  voiceItem?: IVoiceItem;
  destroy?: () => void;
}) {
  const [visible, setVisible] = useState(false);
  const [templateItem, setTemplateItem] = useState<ITemplateItem>();
  const [showProductModal, setShowProductModal] = useState(false);
  const { destroy, dipItem, voiceItem } = props;

  const defaultItems = useMemo(() => {
    return {
      hasItem: !!dipItem || !!voiceItem,
    };
  }, [dipItem, voiceItem]);
  useEffect(() => {
    setVisible(defaultItems.hasItem);
    if (!defaultItems.hasItem) {
      setTemplateItem(undefined);
      setShowProductModal(false);
    }
  }, [defaultItems.hasItem]);

  const handleCreate = useCallback(() => {
    if (templateItem) {
      setShowProductModal(true);
    }
  }, [templateItem]);

  const clear = useCallback(() => {
    destroy?.();
  }, [destroy]);

  return (
    <>
      <Dialog
        header="请从下方选择模版"
        destroyOnClose
        visible={visible}
        onClose={() => clear()}
        width="860px"
        className={Styles.container}
        placement="center"
        closeOnOverlayClick={false}
        style={{
          borderRadius: '8px',
          padding: '20px',
          height: '667px',
          display: 'flex',
          flexDirection: 'column',
        }}
        footer={
          <div className="pagedoo-meta-live-global">
            <Button
              className="gradient-default"
              theme="default"
              onClick={() => clear()}
            >
              取消
            </Button>
            <Button
              disabled={!templateItem}
              className="gradient-primary"
              onClick={handleCreate}
            >
              开始创建
            </Button>
          </div>
        }
      >
        {defaultItems.hasItem && (
          <div
            css={css`
              overflow: hidden;
              display: flex;
              flex-direction: column;
              height: 100%;
            `}
          >
            <ADTemplateSelector onSelect={setTemplateItem} />
          </div>
        )}
      </Dialog>
      {/* 开始创建后，复用广告商品弹窗准备进入编辑器 */}
      <Dialog
        zIndex={2501}
        visible={showProductModal}
        header="创建直播间"
        footer={null}
        closeOnOverlayClick={false}
        width={580}
        onClose={() => {
          setShowProductModal(false);
        }}
        destroyOnClose
      >
        <ADSelectProductModal
          onClose={() => {
            setShowProductModal(false);
          }}
          templateId={templateItem?.templateId}
          defaultDipItem={dipItem}
          defaultVoiceItem={voiceItem}
        />
      </Dialog>
    </>
  );
}
