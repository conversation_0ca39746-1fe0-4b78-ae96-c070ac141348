import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';
import { DipListItem } from '@/type/api';
import { createContext } from 'react';

export interface IAssetsLibConfigContextValue {
  // 处理使用该数字人后的逻辑
  renderUseDip?: (data: {
    dipItem?: DipListItem;
    destroy: () => void;
  }) => React.ReactNode;

  // 渲染使用该音色后逻辑
  renderUseVoice?: (data: {
    voiceItem?: IVoiceItem;
    destroy: () => void;
  }) => React.ReactNode;

  virtualImageCustom?: {
    // 形象定制是否为一次性小程序码
    OTQRCode: boolean;
  };
  /**
   * 库列表是否只读
   */
  readonlyLibList?: boolean;
}

// 资产库配置context 用来实现差异化逻辑
export const AssetsLibConfigContext =
  createContext<IAssetsLibConfigContextValue>({
    renderUseDip: () => null,
    renderUseVoice: () => null,
  });
