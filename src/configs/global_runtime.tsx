import {
  LoginMethod,
  LoginOptionsMappingType,
  LoginTypes,
} from '@/utils/login/typings';
import { ReactElement } from 'react';
import { GiftIcon, GridViewIcon, Icon, SensorsIcon } from 'tdesign-icons-react';

import admuseConfig from './admuse';
import { IAssetsLibConfigContextValue } from '@/configs/config_context';
import {
  AdVoice,
  BaseVoice,
  CustomTTSVoice,
} from '@/pages/VoiceList/voice/baseVoice';
import { uploadRequest, UploadRequestProps } from '@/utils/cos';
import { uploadAdRequest } from '@/pages/VoiceList/voice/uploadRequest';
import {
  ADCheckBarrageFn,
  ADLiveQRContentMask,
  ADLiveQRTitle,
  LiveQRConfigType,
  MetaHumanCheckBarrageFn,
  MetaHumanLiveQRTitle,
} from '@/components/LiveQRCode/LiveQRSlot';
import svgCodes from './svgCodes?raw';
import { EditorSystemMap } from '@/pages/Editor/editorConfig';
import { LIVE_CHECK_CONF } from '@/pages/ADMetaList/MetaPushStepModal/check-conf.typing';
import { runningInClient } from '@/utils/electron';
import { TNode } from 'tdesign-react/es/common';
import type { IUserInfoAtom } from '@/model/user';

export type MenuListType = {
  title: string;
  children: Array<{
    title: string;
    value: string;
    icon: ReactElement;
    selectedIcon?: ReactElement;
    matchPath?: string[];
  }>;
};

// 顶部导航栏右侧需要渲染的XNode
export type HeadOperation = {
  x_node_id: string;
};

export interface IGlobalRuntimeConfig {
  appcode: 'advertisement' | 'pagedoo';
  apiBaseUri?: string;
  runtimeConf: {
    loginConf: {
      type: LoginTypes;
      method: LoginMethod;
      loginHash?: string;
      logoutIndex: string;
      options: LoginOptionsMappingType[LoginTypes];
      useProxy?: boolean;
      /**
       * 校验userInfo 是否有效
       */
      checkUserInfoValid?: (userInfo: IUserInfoAtom) => Promise<boolean>;
    };
    monitorConf: {
      aegisReportId: string;
    };
    // 聚合版和广告的音色训练试听逻辑不同，提取出来
    voiceConf: {
      voice: BaseVoice;
      // 聚合版和广告的音频上传逻辑不同，提取出来
      uploadFn: UploadRequestProps;
    };
  };
  layoutConf: {
    leftMenu: boolean;
    LEFT_MENU_LIST: MenuListType[];
    headOperation?: HeadOperation[];
  };
  liveConf: {
    /**
     * 是否允许无权限推流开播入口
     */
    allowNoPermissionPush?: boolean;
    /**
     * 无权限推流指引
     */
    obsGuideLink?: string;
    liveQRConf: LiveQRConfigType;
    liveEditor: {
      system: EditorSystemMap;
      contentType: string;
    };
    livePushCheckList: Partial<LIVE_CHECK_CONF>[];
    // 互动配置
    interactionConfig?: {
      // 是否允许互动中断通知
      enableNotification: boolean;
    };
    clientDownloadLink?: string;
  };
  logoConf: {
    topbar: {
      logoClassName?: string;
      url: string;
    };
  };
  headMenu?: {
    label: string;
    value: string;
  }[];
  appIdInfo: {
    virtualmanAppId: string;
  };
  assetsLibraryConfig: IAssetsLibConfigContextValue;
}

type IGlobalConfig = {
  [key in Exclude<
    ImportMetaEnv['VITE_RUNNING_SYSTEM'],
    'debug'
  >]: IGlobalRuntimeConfig;
} & {
  debug?: IGlobalRuntimeConfig;
};

const svgCodesUrl = window.URL.createObjectURL(
  new Blob([svgCodes], {
    type: 'application/javascript',
  })
);
// `https://pagedoo.pay.qq.com/icon/svgCodes.js`;

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const GlobalRuntimeConfig: IGlobalConfig = {
  PAGEDOO: {
    appcode: 'pagedoo',
    runtimeConf: {
      loginConf: {
        type: 'wx',
        method: 'reload',
        useProxy: false,
        logoutIndex: 'https://avatar.pay.qq.com/dman/index.html#',
        options: {
          domain: 'pay.qq.com',
          scope: 'snsapi_login',
        } satisfies LoginOptionsMappingType['wx'],
      },
      monitorConf: {
        aegisReportId: 'e7Xy6clqGg6qv1X9ga',
      },
      voiceConf: {
        voice: new CustomTTSVoice(),
        uploadFn: uploadRequest,
      },
    },
    layoutConf: {
      leftMenu: true,
      LEFT_MENU_LIST: [
        {
          title: '',
          children: [
            {
              title: '工作台',
              value: '/workbench',
              icon: <Icon name="work-bench" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="work-bench-is-current" url={svgCodesUrl} />
              ),
              matchPath: ['/workbench-create'],
            },
          ],
        },
        {
          title: '短视频',
          children: [
            {
              title: '视频创建',
              value: '/video-template',
              icon: <Icon name="video-template" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="video-template-is-current" url={svgCodesUrl} />
              ),
            },
            {
              title: '视频管理',
              value: '/video-list',
              icon: <Icon name="video-list" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="video-list-is-current" url={svgCodesUrl} />
              ),
            },
          ],
        },
        {
          title: '直播',
          children: [
            {
              title: '直播创建',
              value: '/live-template',
              icon: <Icon name="live-template" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="live-template-is-current" url={svgCodesUrl} />
              ),
            },
            {
              title: '直播管理',
              value: '/live-list',
              icon: <Icon name="live-list" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="live-list-is-current" url={svgCodesUrl} />
              ),
            },
          ],
        },
        {
          title: '数字人',
          children: [
            {
              title: '形象定制',
              value: '/virtual-image',
              icon: <Icon name="virtual-image" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="virtual-image-is-current" url={svgCodesUrl} />
              ),
            },
            {
              title: '声音定制',
              value: '/voice-list',
              icon: <Icon name="voice-list" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="voice-list-is-current" url={svgCodesUrl} />
              ),
            },
          ],
        },
        {
          title: '素材',
          children: [
            {
              title: '脚本',
              value: '/script-list',
              icon: <Icon name="script-list" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="script-list-is-current" url={svgCodesUrl} />
              ),
              matchPath: ['/question'],
            },
            {
              title: '视频',
              value: '/videoMaterial-list',
              icon: <Icon name="video-template" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="video-template-is-current" url={svgCodesUrl} />
              ),
              matchPath: ['/videoMaterial-create'],
            },
            {
              title: '互动问答',
              value: '/qa-lib',
              icon: <Icon name="qa-lib" url={svgCodesUrl} />,
              selectedIcon: <Icon name="qa-lib-is-current" url={svgCodesUrl} />,
            },
          ],
        },
      ],
    },
    liveConf: {
      allowNoPermissionPush: true,
      liveQRConf: {
        titleRender: MetaHumanLiveQRTitle,
        barrageCheckFn: MetaHumanCheckBarrageFn,
      },
      liveEditor: {
        system: EditorSystemMap.META_HUMAN,
        contentType: 'text_live',
      },
      livePushCheckList: [
        LIVE_CHECK_CONF.BASE_CHECK,
        ...(runningInClient
          ? [
              LIVE_CHECK_CONF.Client_CPU_DETECTION,
              LIVE_CHECK_CONF.Client_GPU_DETECTION,
              LIVE_CHECK_CONF.Client_Network_DETECTION,
            ]
          : []),
      ],
      obsGuideLink:
        'https://doc.weixin.qq.com/doc/w3_ABUAjAagAAkNmoCVopcToOdGyB1rA?scode=AJEAIQdfAAoSB0ScdHABUAjAagAAk',
    },
    logoConf: {
      topbar: {
        logoClassName: 'py-16',
        url: 'https://avatarcdn.pay.qq.com/material/06e9190a28f17102ca976222eb0d7880.png',
      },
    },
    headMenu: [
      // {
      //   label: 'AI内容生成工具',
      //   value: 'avatar',
      // },
    ],
    appIdInfo: {
      virtualmanAppId: atob(
        JSON.parse(
          '}"=IGZxUDZlVmYwcDM5YjMygjYmVGNyAzNkFmZ3gDMmZDO":"k"{'
            .split('')
            .reverse()
            .join('')
        ).k
      ),
    },
    assetsLibraryConfig: {
      renderUseDip: () => {
        return null;
      },
      renderUseVoice: () => null,
      readonlyLibList: true,
    },
  },
  AD: {
    appcode: 'advertisement',
    apiBaseUri: `//${location.host}/intelligent/live`,
    runtimeConf: {
      loginConf: {
        type: 'ad_sso',
        method: 'router_link',
        loginHash: '#/login',
        useProxy: true,
        logoutIndex: `https://${location.host}/intelligent/live/page/index.html#/`,
        options: {
          serviceTag: '97',
        } satisfies LoginOptionsMappingType['ad_sso'],
        checkUserInfoValid: admuseConfig.login.checkUserInfoValid,
      },
      monitorConf: {
        aegisReportId: '9GJLrcL6Vrx6lEwopG',
      },
      voiceConf: {
        voice: new CustomTTSVoice(),
        uploadFn: uploadAdRequest,
      },
    },
    layoutConf: {
      leftMenu: true,
      LEFT_MENU_LIST: [
        {
          title: '直播',
          children: [
            {
              title: '直播管理',
              value: '/ad-list',
              icon: <GridViewIcon />,
              selectedIcon: <GridViewIcon />,
            },
          ],
        },
        {
          title: 'AI主播',
          children: [
            {
              title: '形象',
              value: '/virtual-image',
              icon: <GiftIcon />,
              selectedIcon: <GiftIcon />,
            },
            {
              title: '音色',
              value: '/voice-list',
              icon: <SensorsIcon />,
              selectedIcon: <SensorsIcon />,
            },
          ],
        },
        {
          title: '配置',
          children: [
            // {
            //   title: '互动配置',
            //   value: '/qa-lib',
            //   icon: <QuestionnaireIcon />,
            //   selectedIcon: <QuestionnaireIcon />,
            // },
            {
              title: '商品知识库',
              value: '/product-lib',
              icon: <Icon name="icon-gift" url={svgCodesUrl} />,
              selectedIcon: (
                <Icon name="icon-gift-is-current" url={svgCodesUrl} />
              ),
            },
          ],
        },
      ],
      headOperation: [
        {
          x_node_id: 'adAIServiceQRCode',
        },
      ],
    },
    liveConf: {
      allowNoPermissionPush: true,
      liveQRConf: {
        titleRender: ADLiveQRTitle,
        barrageCheckFn: ADCheckBarrageFn,
        contentRender: ADLiveQRContentMask,
      },
      liveEditor: {
        system: EditorSystemMap.AD_LIVE,
        contentType: 'ad_script_live',
      },
      livePushCheckList: [
        LIVE_CHECK_CONF.QA_QUANTITY_DETECTION,
        LIVE_CHECK_CONF.VMAN_AVAILABLE_DETECTION,
        LIVE_CHECK_CONF.SPEECH_DETECTION,
        LIVE_CHECK_CONF.LIVE_COUNT_DETECTION,
        ...(runningInClient
          ? [
              LIVE_CHECK_CONF.Client_CPU_DETECTION,
              LIVE_CHECK_CONF.Client_GPU_DETECTION,
              LIVE_CHECK_CONF.Client_Network_DETECTION,
            ]
          : []),
      ],
      obsGuideLink:
        'https://doc.weixin.qq.com/doc/w3_ARYAvQYhAF834JWDTcTSg0ZAml15C?scode=AJEAIQdfAAoU7s4OXuARYAvQYhAF8',
      interactionConfig: {
        enableNotification: true,
      },
      clientDownloadLink: 'https://drive.weixin.qq.com/s?k=AJEAIQdfAAoa964Cwi',
    },
    logoConf: {
      topbar: {
        // logoClassName: '',
        url: 'https://avatarcdn.pay.qq.com/material/c71b2b525eed25b268a2951d001cfcea.png',
      },
    },
    appIdInfo: {
      virtualmanAppId: atob(
        JSON.parse(
          '}"=YDO1ADNlVGOlVWNkhzMxkTM0kDNhlzMiF2M4IjN3UTM":"k"{'
            .split('')
            .reverse()
            .join('')
        ).k
      ),
    },
    assetsLibraryConfig: admuseConfig.assetsLibConfig,
  },
};
GlobalRuntimeConfig.debug = GlobalRuntimeConfig.PAGEDOO;

export const MatchedGlobalConfigItem =
  GlobalRuntimeConfig[import.meta.env.VITE_RUNNING_SYSTEM]!;
