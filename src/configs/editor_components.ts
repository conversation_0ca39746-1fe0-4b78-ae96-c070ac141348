import { ComponentGroupType } from '@/pages/Editor/common/type';
import { MaterialsAvatar } from '@/utils/play-component';

export const EditorComponents: ComponentGroupType = [
  {
    id: '90001',
    name: '直播组件',
    is_closed: false,
    components: [
      {
        id: '1',
        name: '数字人',
        icon: 'USER_LIST',
        disabled: '',
        components: [
          {
            name: '数字人',
            desc: '数字人',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: '202403291333374521196032_mobile',
            terminal_type: 'mobile',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/Virtualman`,
            mutex_desc: '',
          },
          {
            name: '直播间音色',
            desc: '直播间音色',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LiveSound',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveSound`,
            mutex_desc: '',
          },
          {
            name: '直播间话术',
            desc: '直播间话术',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LiveSpeech',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveSpeech`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '15',
        name: '直播背景',
        icon: 'tupian',
        disabled: '',
        components: [
          {
            name: '直播间背景',
            desc: '直播间背景',
            col: 12,
            is_float: false,
            poster_url:
              'https://pagedoo-release-1258344706.cos.ap-guangzhou.myqcloud.com/cdn/static/compontent-icon/Image.png',
            mutex_info: [],
            id: 'LiveBkg',
            terminal_type: 'common',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveBkg`,
            mutex_desc: '',
          },
          {
            name: '背景音乐',
            desc: '背景音乐',
            col: 12,
            is_float: false,
            poster_url:
              'https://pagedoo-release-1258344706.cos.ap-guangzhou.myqcloud.com/cdn/static/compontent-icon/Image.png',
            mutex_info: [],
            id: 'BackgroundMusic',
            terminal_type: 'common',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/BackgroundMusic`,
            mutex_desc: '',
          },
        ],
      },
      {
        id: '18',
        name: '展示框',
        icon: 'wangye',
        disabled: '',
        components: [
          {
            name: '展示台',
            desc: '展示台',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LiveTable',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveTable`,
            mutex_desc: '',
          },
          {
            name: '直播间贴片',
            desc: '直播间贴片',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LiveImage',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveImage`,
            mutex_desc: '',
          },
          {
            name: '展示框',
            desc: '展示框',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LiveBox',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveBox`,
            mutex_desc: '',
          },
          {
            name: '单行文本',
            desc: '单行文本',
            col: 12,
            is_float: false,
            poster_url:
              'https://dev-avatarcdn.pay.qq.com/material/ba61adc7f4383876904634144eda7d3d.png',
            mutex_info: [],
            id: 'LiveText',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveText`,
            mutex_desc: '',
          },
          {
            name: '视频播放',
            desc: '视频播放',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LiveVideo',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LiveVideo`,
            mutex_desc: '',
          },
          {
            name: '直播间拉流播放',
            desc: '直播间拉流播放',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'LivePlayPull',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/LivePlayPull`,
            mutex_desc: '',
          },
          {
            name: '直播间时钟',
            desc: '直播间时钟',
            col: 12,
            is_float: false,
            poster_url: '',
            mutex_info: [],
            id: 'Clock',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/Clock`,
            mutex_desc: '',
          },
          {
            name: '字幕',
            desc: '字幕',
            col: 12,
            is_float: false,
            poster_url:
              'https://dev-avatarcdn.pay.qq.com/material/ba61adc7f4383876904634144eda7d3d.png',
            mutex_info: [],
            id: 'VideoSubtitle',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/VideoSubtitle`,
            mutex_desc: '',
          },
          {
            name: '进度条',
            desc: '进度条',
            col: 12,
            is_float: false,
            poster_url:
              'https://dev-avatarcdn.pay.qq.com/material/ba61adc7f4383876904634144eda7d3d.png',
            mutex_info: [],
            id: 'VideoProgressBar',
            terminal_type: '',
            disabled: '',
            component_id: `component/${MaterialsAvatar}/VideoProgressBar`,
            mutex_desc: '',
          },
        ],
      },
    ],
  },
];
