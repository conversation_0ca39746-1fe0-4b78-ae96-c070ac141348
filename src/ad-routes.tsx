import LayoutsWithLogin, { LoginWrapper } from '@/layouts';
import { LoginCallback } from '@/pages/Login/LoginCallback';
import { lazy } from 'react';
import { RouteObject, useRoutes } from 'react-router-dom';
import SSOLogin from './pages/SSOLogin';
import { ProductLibPage } from '@/pages/ADPages/ProductLib';
import { MuseLogin } from '@/pages/MuseLogin';
import { MuseSSOLogin } from '@/pages/MuseLogin/Login';
import { patchRoutes } from '@/configs/admuse/route/patch-routes';

const EditorEntry = lazy(() => import('@/pages/Editor/SimpleEditor'));
const ADMetaLiveList = lazy(() => import('@/pages/ADMetaList'));
const VoiceIndex = lazy(() => import('@/pages/VoiceList/wrapper'));
const VoiceAdd = lazy(() => import('@/pages/VoiceList/VoiceAdd'));
const ImageDetail = lazy(() => import('@/pages/VirtualImage/ImageDetail'));
const VirtualImageWrapper = lazy(() => import('@/pages/VirtualImage/wrapper'));
const VirtualImageAdd = lazy(() => import('@/pages/VirtualImage/VirtualAdd'));
export const routes: RouteObject[] = [
  {
    path: '/',
    element: <MuseLogin />,
  },
  {
    path: '/login',
    element: <MuseSSOLogin />,
  },
  {
    path: '/sso-login',
    element: <SSOLogin />,
  },
  {
    path: '/ad-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <ADMetaLiveList />,
      },
    ],
  },
  {
    path: '/Editor',
    element: <LoginWrapper />,
    children: [
      {
        path: '',
        element: <EditorEntry />,
      },
    ],
  },
  {
    path: '/login_callback',
    element: <LoginCallback />,
  },
  {
    path: '/virtual-image',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VirtualImageWrapper />,
      },
      {
        path: 'detail/:imageId',
        element: <ImageDetail />,
      },
      {
        path: 'add',
        element: <VirtualImageAdd />,
      },
    ],
  },
  {
    path: '/voice-list',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <VoiceIndex />,
      },
      {
        path: 'add',
        element: <VoiceAdd />,
      },
    ],
  },
  {
    path: '/product-lib',
    element: <LayoutsWithLogin />,
    children: [
      {
        path: '',
        element: <ProductLibPage />,
      },
    ],
  },
];
patchRoutes(routes);

function MyRouter() {
  return useRoutes(routes);
}

export default MyRouter;
