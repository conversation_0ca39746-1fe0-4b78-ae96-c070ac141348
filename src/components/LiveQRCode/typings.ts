export interface IVirtualManLiveData {
  /**
   * 平台ID
   */
  platformAcctId?: string;

  /** 数字人key */
  virtualManKey?: string;

  /**
   * 数字人平台
   */
  platform?: string;
}

/**
 * 推流方式
 * thirdparty_rtmp = 用户手动输入第三方平台进行推流
 * hosted_rtmp = 推流到后台提供的rtmp，用户需要复制拉流地址进行本地拉流
 */
export type LivePushMethodDef = 'thirdparty_rtmp' | 'hosted_rtmp';

/**
 * 定义是否具有第三方平台推流权限
 */
export type ThirdPartyLivePushPermission = 'allow' | 'deny';

/**
 * 扫码登录状态
 */
export enum DialogState {
  NOT_SCANNED = 'NOT_SCANNED',
  SCANNED_PENDING_CONFIRMATION = 'SCANNED_PENDING_CONFIRMATION',
  LOGGED_IN = 'LOGGED_IN',
  QR_CODE_EXPIRED = 'QR_CODE_EXPIRED',
  SCANNED_BARRAGE_FAIL = 'SCANNED_BARRAGE_FAIL',
}

/**
 * 自定义渲染QRCode 标题的属性
 */
export interface ILiveQRTitleProps {
  barrageState?: { status: 'success' | 'fail' };
  qrCodeState: DialogState;
}
/**
 * 自定义渲染qrcodeContent的属性
 */
export interface ILiveQRContentProps {
  onRefresh: () => void;
  barrageState?: {
    status: 'success' | 'fail';
  };
  qrCodeState: DialogState;
}
