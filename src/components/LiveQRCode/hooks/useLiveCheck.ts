// 直播前置检查的hooks

import { RespError } from '@/pb/config';
import { IVirtualManLiveData } from '../typings';
import { MetaLiveSvr } from '@/pb/pb';
import to from 'await-to-js';
import { useCallback, useLayoutEffect, useState } from 'react';

export interface IUseLiveCheckOptions {
  liveId: string;
  virtualmanLiveList: IVirtualManLiveData[];
}

type CheckType = 'NOT_ALLOW_PUSH' | 'UNKNOWN';

interface ICheckState {
  pass: boolean;
  showError: boolean;
  errMsg: string;
  errDesc: string;
  checkType?: CheckType;
}

export const useCheckAllowPush = (options: IUseLiveCheckOptions) => {
  const { liveId, virtualmanLiveList } = options;
  const [checkState, setCheckState] = useState<ICheckState>({
    pass: false,
    showError: false,
    errMsg: '',
    errDesc: '',
  });
  const [isCheckingAllowPush, setIsChecking] = useState(false);
  const checkVirtualmanAllow = useCallback(async () => {
    const [err, result] = await to(
      MetaLiveSvr.CheckMetaLiveStatus({
        meta_live_id: liveId,
        virtual_man_live_data: virtualmanLiveList.map((item) => ({
          platform: item.platform || '',
          platform_account_id: item.platformAcctId || '',
          virtualman_key: item.virtualManKey || '',
        })),
      })
    );
    if (err) {
      const msg = '开播失败';
      let desc = '';
      let checkType: CheckType = 'UNKNOWN';
      if (err instanceof RespError) {
        if (err.resultCode == '1126001') {
          desc =
            '当前直播间所使用的主播形象已在其他直播间开播，请更换形象再开播';
          checkType = 'NOT_ALLOW_PUSH';
        }
      }
      setCheckState((prev) => ({
        ...prev,
        errMsg: msg,
        errDesc: desc,
        checkType,
      }));
      return false;
    }
    return true;
  }, [liveId, virtualmanLiveList]);
  const reset = useCallback(
    () =>
      setCheckState({
        pass: false,
        showError: false,
        errDesc: '',
        errMsg: '',
      }),
    []
  );
  const execCheck = useCallback(async () => {
    if (virtualmanLiveList.length === 0) return;
    if (!liveId) return;
    const checkList = [checkVirtualmanAllow];
    setIsChecking(true);
    let flag = false;
    for (const validate of checkList) {
      const pass = await validate();
      if (!pass) {
        flag = true;
        setCheckState((prev) => ({
          ...prev,
          pass: false,
          showError: true,
        }));
      }
    }
    if (!flag) {
      setCheckState((prev) => ({ ...prev, pass: true }));
    }
    setIsChecking(false);
  }, [checkVirtualmanAllow, liveId, virtualmanLiveList.length]);
  useLayoutEffect(() => {
    execCheck();
  }, [execCheck]);
  return {
    checkState,
    resetCheck: reset,
    isCheckingAllowPush,
    execCheck,
  };
};
