import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { UpdateLiveExtendConfigItem } from '@/pb/api/Development';
import { useMemoizedFn } from 'ahooks';
import {
  forwardRef,
  PropsWithChildren,
  useImperativeHandle,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
  ReactNode,
} from 'react';
import { HelpCircleFilledIcon } from 'tdesign-icons-react';
import { Checkbox, Input, Popup } from 'tdesign-react';
import to from 'await-to-js';

export interface InteractionNotificationChildrenProps {
  enabled: boolean;
}

export interface InteractionNotificationProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'className' | 'style'> {
  liveID: string;
  onAvailableStateChange: (available: boolean) => void;
  children:
    | ReactNode
    | ((ctx: InteractionNotificationChildrenProps) => ReactNode);
  direction?: 'vertical' | 'horizontal';
  width?: string;
  onIsEnabled?: (enabled: boolean) => void;
  // children?: (props: InteractionNotificationChildrenProps) => ReactNode;
}

export interface InteractionNotificationInstance {
  confirm: () => Promise<Error | null>;
}

export const InteractionNotification = forwardRef<
  InteractionNotificationInstance,
  InteractionNotificationProps
>((props, ref) => {
  const {
    liveID,
    onAvailableStateChange,
    className,
    style,
    children,
    direction = 'vertical',
    width = '272px',
    onIsEnabled = () => {
      /** leave it empty */
    },
  } = props;
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [allow, setAllow] = useState(false);

  const validPhone = phoneNumber.length === 11;

  const memoOnIsEnabled = useMemoizedFn(onIsEnabled);

  const enabled = useMemo(() => {
    const enabled =
      MatchedGlobalConfigItem.liveConf.interactionConfig?.enableNotification ??
      false;
    memoOnIsEnabled(enabled);
    return enabled;
  }, [memoOnIsEnabled]);

  useLayoutEffect(() => {
    if (!enabled) {
      onAvailableStateChange(true);
      return;
    }
    onAvailableStateChange(validPhone && allow);
  }, [allow, enabled, onAvailableStateChange, validPhone]);

  const wrapperCls = useMemo(() => {
    const cls: string[] = ['flex', 'pagedoo-meta-live-global'];
    if (direction === 'vertical') {
      cls.push('flex-col');
    }
    cls.push(className || '');
    return cls.join(' ');
  }, [className, direction]);

  const childrenProps = useMemo<InteractionNotificationChildrenProps>(() => {
    return {
      enabled,
    };
  }, [enabled]);

  const confirm = useMemoizedFn(async () => {
    if (!enabled) return null;
    const [err] = await to(
      UpdateLiveExtendConfigItem({
        config_item_id: 'live_notice_phone',
        config_item_value: phoneNumber,
        live_id: liveID,
        node_id: 'global',
      })
    );
    return err;
  });
  useImperativeHandle(ref, () => ({
    confirm,
  }));
  const normalizedChildren =
    typeof children === 'function' ? children(childrenProps) : children;

  if (!enabled) return normalizedChildren;
  return (
    <div className={wrapperCls} style={{ ...style }}>
      {normalizedChildren}
      <div className="flex justify-center">
        <div
          className="flex flex-col"
          style={{
            width,
          }}
        >
          <div className="flex flex-col items-center w-full gap-2">
            <div className="flex items-center gap-1">
              <h3
                style={{
                  fontWeight: 600,
                  color: 'rgba(0, 0, 0, 0.95)',
                }}
              >
                互动中断时通知该手机
              </h3>
              <Popup
                content={
                  <div
                    style={{
                      padding: '7px',
                    }}
                  >
                    仅在 9:00-22:00 间通
                    <br />
                    过电话通知互动中断
                  </div>
                }
                showArrow
              >
                <HelpCircleFilledIcon
                  style={{
                    color: 'rgba(186, 188, 193, 1)',
                  }}
                />
              </Popup>
            </div>
            <Input
              placeholder="请输入手机号"
              value={phoneNumber}
              onChange={setPhoneNumber}
              // css={css`
              //   .t-input {
              //     background: rgba(241, 242, 246, 1);
              //     border: none;
              //     border-radius: 8px;
              //     height: 36px;
              //   }
              //   .t-input__focused {
              //     box-shadow: none;
              //   }
              // `}
              style={{
                width: '100%',
                height: '36px',
              }}
            />
          </div>
          <div className="flex gap-1 ">
            <Checkbox
              value={allow}
              onChange={setAllow}
              className="gradient-default_checkbox"
            >
              <span
                style={{ fontSize: '12px', color: 'rgba(137, 139, 143, 1)' }}
              >
                您同意收集电话号码
                <Popup
                  content={
                    <div
                      style={{
                        padding: '7px',
                      }}
                    >
                      您同意收集电话号码用于通知【在互动中断时电话提醒您重新扫码链接】。
                      请您确保填写号码的真实性和准确性，如因您提供的号码信息有误导致您错过重要提示信息，或者导致其他人利益受到损害，由您独立承担责任。我们将严格遵守相关法律法规，保护您的个人信息安全
                    </div>
                  }
                  overlayInnerStyle={{
                    maxWidth: '296px',
                  }}
                >
                  <span
                    style={{
                      fontSize: '12px',
                      textDecoration: 'underline',
                      color: '#000',
                      cursor: 'pointer',
                    }}
                  >
                    服务政策
                  </span>
                </Popup>
              </span>
            </Checkbox>
          </div>
        </div>
      </div>
    </div>
  );
});

InteractionNotification.displayName = 'InteractionNotification';

export const useInteractionNotification = () => {
  const [available, setAvailable] = useState(false);
  const [enabled, setEnabled] = useState(false);
  const ref = useRef<InteractionNotificationInstance>(null);

  return {
    ref,
    available,
    onAvailableStateChange: setAvailable,
    enabled,
    onEnabled: setEnabled,
  };
};
