import SuccessIcon from '@/assets/images/success-icon.png';
import { MetaLiveSvr } from '@/pb/pb';
import { useLatest } from 'ahooks';
import qrcode from 'qrcode';
import {
  ReactElement,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { RefreshIcon } from 'tdesign-icons-react';
import { SuspenseView } from '../SuspenseView';
import { DialogState, ILiveQRContentProps, ILiveQRTitleProps } from './typings';
import { LiveQRConfigType } from './LiveQRSlot';

export interface ILoginCustomRenderData {
  qrCodeUrl: string;
  metaLiveID: string;
  refreshQRCode: () => void;
}

export function InteractionLogin(props: {
  meta_live_id: string;
  onLogin?: () => void;
  barrageCheckFn?: LiveQRConfigType['barrageCheckFn'];
  titleRender: LiveQRConfigType['titleRender'];
  contentRender?: LiveQRConfigType['contentRender'];
  onQRCodeUrl?: (url: string) => void;
  onBeforeQRCodeUrl?: () => void;
  /**
   * 自定义渲染已登录
   */
  renderLoggined?: (data: ILoginCustomRenderData) => ReactNode;
  renderExpired?: (data: ILoginCustomRenderData) => ReactNode;
  /**
   * 登录后立即回调
   */
  immediate?: boolean;
}) {
  const {
    meta_live_id,
    onLogin,
    titleRender,
    barrageCheckFn,
    contentRender,
    onQRCodeUrl,
    renderLoggined,
    renderExpired,
    immediate,
    onBeforeQRCodeUrl = () => {
      /** placeholder */
    },
  } = props;
  const [qrcodeDataUrl, setQrcodeDataUrl] = useState<string>('');
  const [dialogState, setDialogState] = useState<DialogState>(
    DialogState.NOT_SCANNED
  );
  const [countDownTime, setCountDownTime] = useState<number>(3);
  // 记录弹幕通路检测的状态
  const [barrageState, setBarrageState] = useState<{
    status: 'success' | 'fail';
  }>();
  // 获取barrageState的ref，最新状态
  const latestBarrageState = useLatest(barrageState);
  const loginedRef = useRef(false);
  const latestOnQRCodeUrl = useLatest(onQRCodeUrl);
  const latestOnBeforeQRCodeUrl = useLatest(onBeforeQRCodeUrl);

  const queryLoginQrCode = useCallback(() => {
    latestOnBeforeQRCodeUrl.current();
    if (meta_live_id) {
      MetaLiveSvr.GetLoginQRCode({
        meta_live_id,
      }).then((res) => {
        qrcode
          .toDataURL(
            `https://channels.weixin.qq.com/mobile/confirm_login.html?token=${res.qr_code}`,
            {
              margin: 2,
            }
          )
          .then((dataUrl) => {
            setQrcodeDataUrl(dataUrl);
            setDialogState(DialogState.NOT_SCANNED);
            setCountDownTime(3);
            loginedRef.current = false;
          });
      });
    }
  }, [
    meta_live_id,
    setQrcodeDataUrl,
    setDialogState,
    setCountDownTime,
    latestOnBeforeQRCodeUrl,
  ]);

  // 扫码确认后，检测弹幕通路是否异常
  const flag = useRef<boolean>(false);
  const ensureBarrage = useCallback(async () => {
    // 定时器会反复执行该函数，加锁保证执行一次
    if (flag.current) return;
    return new Promise((resolve, reject) => {
      if (barrageCheckFn && !flag.current) {
        flag.current = true;
        barrageCheckFn(meta_live_id)
          .then((res) => {
            if (res.status === 'fail') {
              // 通路异常，更新弹幕通路状态为fail
              setBarrageState(res);
              reject('fail');
            } else {
              // 通路正常，设置success状态
              setBarrageState(res);
              resolve('success');
            }
          })
          .catch((err) => {
            console.error('弹幕通路检测请求异常', err);
            // 刷新二维码
            setBarrageState(err);
            reject('fail');
          })
          .finally(() => {
            flag.current = false;
          });
      }
    });
  }, [barrageCheckFn, meta_live_id]);

  const refreshQRCode = useCallback(() => {
    setBarrageState(undefined);
    setDialogState(DialogState.NOT_SCANNED);
    queryLoginQrCode();
  }, [queryLoginQrCode]);

  useEffect(() => {
    queryLoginQrCode();
  }, [queryLoginQrCode]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    let loginTimer: NodeJS.Timeout | null = null;
    if (qrcodeDataUrl) {
      latestOnQRCodeUrl.current?.(qrcodeDataUrl);
    }
    const stop = () => {
      timer && clearInterval(timer);
      timer = null;
      loginTimer && clearInterval(loginTimer);
      loginTimer = null;
    };
    if (qrcodeDataUrl && meta_live_id && !timer) {
      timer = setInterval(() => {
        console.log(1, '查询二维码状态');
        MetaLiveSvr.QueryQRCodeStatus({
          meta_live_id,
        })
          .then((res) => {
            switch (res?.status) {
              case 1:
                break;
              case 2:
                setDialogState(DialogState.SCANNED_PENDING_CONFIRMATION);
                break;
              case 3:
                setDialogState(DialogState.LOGGED_IN);
                // eslint-disable-next-line no-case-declarations
                const promise = ensureBarrage().catch((res) => {
                  stop();
                });
                if (immediate) {
                  // 登录后立即回调
                  promise.finally(() => {
                    if (!loginedRef.current) {
                      loginedRef.current = true;
                      onLogin?.();
                    }
                    stop();
                  });
                  break;
                }
                if (!loginTimer) {
                  loginTimer = setInterval(() => {
                    console.log(1, '登录中');
                    setCountDownTime((prev) => {
                      const val = prev - 1;
                      if (
                        latestBarrageState.current &&
                        latestBarrageState.current.status === 'fail'
                      ) {
                        // 当弹幕通路异常时停止
                        stop();
                      }
                      if (val <= 0) {
                        stop();
                        if (!loginedRef.current) {
                          loginedRef.current = true;
                          onLogin?.();
                        }
                      }
                      return Math.max(val, 0);
                    });
                  }, 1000);
                }
                break;
              case 4:
                setDialogState(DialogState.QR_CODE_EXPIRED);
                stop();
                break;
              default:
                setDialogState(DialogState.QR_CODE_EXPIRED);
                stop();
            }
          })
          .catch(() => {
            // 如果轮训结果报错，直接停止轮训，并提示二维码过期
            // setDialogState(DialogState.QR_CODE_EXPIRED);
            // stop();
          });
      }, 1000);
    }

    return () => {
      stop();
    };
  }, [
    qrcodeDataUrl,
    meta_live_id,
    onLogin,
    latestBarrageState,
    ensureBarrage,
    latestOnQRCodeUrl,
    immediate,
  ]);

  // 扫码确认后，检测弹幕通路是否异常
  // useEffect(() => {
  //   if (dialogState === DialogState.LOGGED_IN && barrageCheckFn) {
  //     barrageCheckFn(meta_live_id)
  //       .then((res) => {
  //         if (res.status === 'fail') {
  //           // 通路异常，更新弹幕通路状态为fail
  //           setBarrageState(res);
  //           // 刷新二维码
  //           queryLoginQrCode();
  //           // 重置二维码状态为未扫码
  //           setDialogState(DialogState.NOT_SCANNED);
  //         } else {
  //           // 通路正常，设置success状态
  //           setBarrageState(res);
  //         }
  //       })
  //       .catch((err) => {
  //         console.error('弹幕通路检测请求异常', err);
  //         // 请求异常
  //         MessagePlugin.error('弹幕通路检测请求异常');
  //         // 刷新二维码
  //         setBarrageState(err);
  //         setDialogState(DialogState.NOT_SCANNED);
  //         queryLoginQrCode();
  //       });
  //   }
  // }, [dialogState, queryLoginQrCode, barrageCheckFn, meta_live_id]);

  // 记忆住传给子组件 titleRender的 props ，防止重复渲染
  const renderProps = useMemo<ILiveQRTitleProps>(
    () => ({
      barrageState: barrageState || { status: 'success' },
      qrCodeState: dialogState,
    }),
    [barrageState, dialogState]
  );
  // 传给 contentRender 的 props
  const contentProps = useMemo<ILiveQRContentProps>(
    () => ({
      onRefresh: refreshQRCode,
      barrageState,
      qrCodeState: dialogState,
    }),
    [barrageState, dialogState, refreshQRCode]
  );
  // 插槽形式的二维码遮罩层
  const liveContentMask = useMemo(
    () => contentRender?.(contentProps as ILiveQRContentProps),
    [contentProps, contentRender]
  );
  return (
    <div className="interaction-login">
      {titleRender(renderProps)}
      <div className="qrcode-wrap">
        <div className="qrcode-wrap-inner">
          {!!qrcodeDataUrl && (
            <img className="qrcode-img" src={qrcodeDataUrl} alt="" />
          )}
          {dialogState !== DialogState.NOT_SCANNED && (
            <div className="scanned-dialog">
              <SuspenseView
                condition={!!!liveContentMask}
                suspense={liveContentMask as ReactElement}
              >
                <>
                  {dialogState === DialogState.SCANNED_PENDING_CONFIRMATION && (
                    <div className="scanned-dialog-content">
                      <img
                        src={SuccessIcon}
                        alt=""
                        width={34}
                        style={{ marginBottom: '16px' }}
                      />
                      <div>已扫码</div>
                      <div>请在手机上进行确认</div>
                    </div>
                  )}
                  {dialogState === DialogState.LOGGED_IN &&
                    (renderLoggined?.({
                      metaLiveID: meta_live_id,
                      qrCodeUrl: qrcodeDataUrl,
                      refreshQRCode,
                    }) || (
                      <div className="scanned-dialog-content">
                        <img
                          src={SuccessIcon}
                          alt=""
                          style={{
                            width: 34,
                            height: 34,
                            marginBottom: '16px',
                          }}
                        />
                        <div>已登录</div>
                        <div>即将跳转 {countDownTime}s</div>
                      </div>
                    ))}
                  {dialogState === DialogState.QR_CODE_EXPIRED &&
                    (renderExpired?.({
                      qrCodeUrl: qrcodeDataUrl,
                      metaLiveID: meta_live_id,
                      refreshQRCode,
                    }) || (
                      <div
                        className="scanned-dialog-content"
                        style={{ cursor: 'pointer' }}
                        onClick={() => queryLoginQrCode()}
                      >
                        <RefreshIcon
                          style={{
                            width: 34,
                            height: 34,
                            marginBottom: '16px',
                          }}
                        />
                        <div>二维码已过期，点击刷新</div>
                      </div>
                    ))}
                </>
              </SuspenseView>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
