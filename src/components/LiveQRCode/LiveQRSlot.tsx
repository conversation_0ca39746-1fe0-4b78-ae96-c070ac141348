import InfoIcon from '@/assets/images/info-icon.png';
import { MetaLiveSvr } from '@/pb/pb';
import { ReactElement } from 'react';
import { Alert, Button } from 'tdesign-react';
import { ILiveQRContentProps, ILiveQRTitleProps } from './typings';
type checkBarrageFnType = (
  meta_live_id: string
) => Promise<{ status: 'success' | 'fail' }>;
export type LiveQRConfigType = {
  titleRender: (props: ILiveQRTitleProps) => ReactElement;
  contentRender?: (props: ILiveQRContentProps) => ReactElement | undefined;
  barrageCheckFn?: checkBarrageFnType;
};

export function ADLiveQRTitle(props: ILiveQRTitleProps) {
  const { barrageState } = props;

  return (
    <>
      <div className="title">
        当前直播间，包含互动弹幕环节，需要您使用“微信账号2”扫描二维码以获取此账号信息
      </div>
      <Alert
        icon={<img src={InfoIcon} width={20} alt="" />}
        className="login-explain"
        message={
          <>
            <div>微信账号1：用于扫描视频号助手的二维码，操作开播。</div>
            <div>微信账号2：用于扫描此二维码，以便进行弹幕互动。</div>
            <div>-此微信账号2：应具有该开播视频号助手的运营权限。</div>
            <div>
              -开播中，此微信账号2不要再扫视频号助手和视频号小店的登录码。
            </div>
          </>
        }
      />
    </>
  );
}

export function MetaHumanLiveQRTitle(props: ILiveQRTitleProps) {
  return (
    <>
      <div className="title">
        当前直播间，包含互动/弹幕环节，需要您扫码二维码以此获取账号信息
      </div>
      <Alert
        icon={<img src={InfoIcon} width={20} alt="" />}
        className="login-explain"
        message={
          <>
            <div>
              请使用当前视频号有后台操作权限的账号进行扫码，用以进行弹幕抓取；
            </div>
            <div>如开启了评论区自动回复，还将使用此管理员身份回答用户评论</div>
          </>
        }
      />
    </>
  );
}

export function ADLiveQRContentMask(props: ILiveQRContentProps) {
  const { onRefresh, barrageState } = props;
  if (!barrageState) return undefined;
  if (barrageState.status === 'success') return undefined;
  return (
    <div className="scanned-dialog-content">
      <svg
        width="30"
        height="30"
        viewBox="0 0 30 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={{ marginBottom: '18px' }}
      >
        <path
          d="M29.875 15C29.875 6.78477 23.2152 0.125003 15 0.125C6.78477 0.124995 0.125003 6.78476 0.125 15C0.124995 23.2152 6.78476 29.875 15 29.875C23.2152 29.875 29.875 23.2152 29.875 15ZM9.34144 9.21849C9.73314 8.82914 10.3663 8.83106 10.7556 9.22276L15 13.4928L19.2444 9.22276C19.6337 8.83106 20.2669 8.82914 20.6586 9.21849L20.7473 9.30661C21.139 9.69595 21.1409 10.3291 20.7515 10.7208L16.4981 15L20.7515 19.2792C21.1409 19.6709 21.139 20.304 20.7472 20.6934L20.6586 20.7815C20.2669 21.1708 19.6337 21.1689 19.2444 20.7772L15 16.5072L10.7557 20.7772C10.3663 21.1689 9.73316 21.1708 9.34146 20.7815L9.2528 20.6934C8.8611 20.304 8.85919 19.6709 9.24853 19.2792L13.5019 15L9.24851 10.7208C8.85917 10.3291 8.86108 9.69595 9.25278 9.30661L9.34144 9.21849Z"
          fill="url(#paint0_linear_12471_109799)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_12471_109799"
            x1="0.11957"
            y1="29.875"
            x2="29.9586"
            y2="0.214557"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FF5E55" />
            <stop offset="1" stopColor="#FF8E86" />
          </linearGradient>
        </defs>
      </svg>

      <div style={{ marginBottom: '18px' }}>
        弹幕通路检测未通过，请自查
        <br />
        是否按照如下提示指引操作
      </div>
      <Button
        style={{
          background:
            'linear-gradient(88.08deg, #0153FF -0.01%, #2E7FFD 49.89%, #C1A3FD 99.99%)',
        }}
        onClick={() => {
          onRefresh();
        }}
      >
        重新扫码
      </Button>
    </div>
  );
}

export const ADCheckBarrageFn: checkBarrageFnType = (meta_live_id: string) => {
  return new Promise((resolve, reject) => {
    MetaLiveSvr.CheckBarrageStatus({ meta_live_id })
      .then((res) => {
        // check_barrage_status:1---成功 2---校验失败 3---未知错误
        if (res.check_barrage_status === 1) {
          resolve({ status: 'success' });
        } else {
          console.error('弹幕通路检测出现异常');
          resolve({ status: 'fail' });
        }
      })
      .catch((err: unknown) => {
        console.error('弹幕通路检测请求异常', err);
        reject({ status: 'fail' });
      });
  });
};

export const MetaHumanCheckBarrageFn: checkBarrageFnType = () => {
  return Promise.resolve({ status: 'success' });
};
