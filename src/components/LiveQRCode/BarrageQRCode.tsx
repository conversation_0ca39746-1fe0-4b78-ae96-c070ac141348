import { Button, Dialog, MessagePlugin } from 'tdesign-react';
import { InteractionLogin } from './InteractionLogin';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { useCallback, useState } from 'react';
import successTip from '@/assets/images/success.png';
import './index.less';
import { MetaLiveSvr } from '@/pb/pb';
import to from 'await-to-js';
export function BarrageQRCode(props: {
  meta_live_id: string;
  onClose: () => void;
}) {
  const { meta_live_id: metaLiveId, onClose } = props;
  const [status, setStatus] = useState<'success' | 'interactionLogin'>(
    'interactionLogin'
  );
  const handleLogin = useCallback(async () => {
    const [err] = await to(
      MetaLiveSvr.CrawlBarrage({
        meta_live_id: metaLiveId,
      })
    );
    if (err) throw err;
  }, [metaLiveId]);
  return (
    <Dialog
      header="开播"
      visible
      onClose={() => {
        onClose();
      }}
      footer={null}
      style={{ width: '698px' }}
      destroyOnClose
    >
      <div className="live_qrcode_comp pagedoo-meta-live-global">
        {status === 'interactionLogin' && (
          <InteractionLogin
            titleRender={
              MatchedGlobalConfigItem.liveConf.liveQRConf.titleRender
            }
            barrageCheckFn={
              MatchedGlobalConfigItem.liveConf.liveQRConf.barrageCheckFn
            }
            contentRender={
              MatchedGlobalConfigItem.liveConf.liveQRConf.contentRender
            }
            meta_live_id={metaLiveId}
            onLogin={() => {
              handleLogin()
                .then(() => {
                  setStatus('success');
                })
                .catch((e) => {
                  MessagePlugin.error('互动功能启动失败，请重新扫码');
                  setStatus('interactionLogin');
                });
            }}
          />
        )}
        {status === 'success' && (
          <div
            className="live_qrcode_success"
            style={{ minHeight: '500px', justifyContent: 'center' }}
          >
            <img src={successTip} width={100} height={100} alt="" />
            <div className="mt-8 mb-16">配置成功，互动功能已生效！</div>

            <div className="mt-20">
              <Button
                className="gradient-primary"
                onClick={() => {
                  onClose();
                }}
              >
                我知道了
              </Button>
            </div>
          </div>
        )}
      </div>
    </Dialog>
  );
}
