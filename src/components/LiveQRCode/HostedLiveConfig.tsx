// 无直播推流权限，使用本组件进行展示

import {
  ILoginCustomRenderData,
  InteractionLogin,
} from '@/components/LiveQRCode/InteractionLogin';
import {
  InteractionNotification,
  useInteractionNotification,
} from '@/components/LiveQRCode/InteractionNotification';
import { DialogState } from '@/components/LiveQRCode/typings';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import {
  GetLiveUrl,
  GetLoginQRCode,
  GetPushUrl,
  QueryQRCodeStatus,
} from '@/pb/api/MetaLiveSvr';
import to from 'await-to-js';
import copy from 'copy-to-clipboard';
import React, { Fragment, useCallback, useState } from 'react';
import { FileCopyIcon } from 'tdesign-icons-react';
import {
  Button,
  Divider,
  Form,
  FormInstanceFunctions,
  FormRules,
  Input,
  Loading,
  MessagePlugin,
  Space,
} from 'tdesign-react';
import Styles from './styles/hosted-live.module.less';
const { FormItem } = Form;

(window as any).__getLiveUrlPairs = async (metaLiveID: string) => {
  const [err, result] = await to(
    Promise.all([
      GetPushUrl({
        meta_live_id: metaLiveID,
      }),
      GetLiveUrl({
        meta_live_id: metaLiveID,
      }),
    ])
  );
  if (err) return;
  return {
    push: result[0],
    pull: result[1],
  };
};

interface IHostLiveBaseProps {
  /**
   * 直播id
   */
  metaLiveID: string;
}

interface IFooterProps {
  disabledConfirmed: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export interface IHostedLiveConfigProps
  extends IHostLiveBaseProps,
    Pick<IFooterProps, 'onCancel'> {
  /**
   * 确认拉流地址
   */
  onConfirm: (data: { pushUrl: string; pullUrl: string }) => void;
}

function InitTitle() {
  return (
    <>
      <div
        className="font-medium text-sm"
        style={{
          color: 'rgba(0,0,0,0.9)',
        }}
      >
        使用微信扫码下方二维码，获得拉流地址
      </div>
      <p className={Styles.loginDesc}>
        需要使用开播视频号管理员的账号扫码，用于获取用户互动信息
      </p>
    </>
  );
}

function AlreadyLoginTitle() {
  return (
    <>
      <div className="font-medium text-sm flex items-center gap-x-2">
        <svg
          style={{
            width: '22px',
            height: '22px',
          }}
          width="22"
          height="22"
          viewBox="0 0 22 22"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M11 22C17.0751 22 22 17.0751 22 11C22 4.92487 17.0751 0 11 0C4.92487 0 0 4.92487 0 11C0 17.0751 4.92487 22 11 22ZM6.05506 11.8797C5.7485 11.5731 5.74853 11.076 6.05512 10.7694C6.3617 10.4628 6.85876 10.4628 7.16539 10.7693L9.42857 13.0319L14.8324 7.62736C15.1393 7.32033 15.6371 7.32032 15.9441 7.62732C16.2511 7.93433 16.2511 8.43209 15.9441 8.73907L10.1357 14.5468C9.74519 14.9373 9.112 14.9372 8.7215 14.5466L6.05506 11.8797Z"
            fill="url(#paint0_linear_44_54094)"
            // style=""
          />
          <defs>
            <linearGradient
              id="paint0_linear_44_54094"
              x1="0"
              y1="0"
              x2="22"
              y2="0"
              gradientUnits="userSpaceOnUse"
            >
              <stop
                stopColor="#00C653"
                // style="stop-color:#00C653;stop-color:color(display-p3 0.0000 0.7765 0.3255);stop-opacity:1;"
              />
              <stop
                offset="1"
                stopColor="#01D766"
                // style="stop-color:#01D766;stop-color:color(display-p3 0.0039 0.8431 0.4000);stop-opacity:1;"
              />
            </linearGradient>
          </defs>
        </svg>

        <span
          style={{
            color: 'rgba(0,0,0,0.9)',
          }}
        >
          扫码成功，请复制下方拉流地址
        </span>
      </div>
      <p className={Styles.loginDesc}>
        在开播期间，请勿使用相同微信号扫描进入视频号助手后台，否则会导致弹幕抓取异常
      </p>
    </>
  );
}

function LoginExpireTitle() {
  return (
    <>
      <div className="font-medium text-sm flex items-center gap-x-2">
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M17.75 9C17.75 4.16751 13.8325 0.250001 9 0.25C4.16751 0.249997 0.250001 4.16751 0.25 9C0.249997 13.8325 4.16751 17.75 9 17.75C13.8325 17.75 17.75 13.8325 17.75 9ZM9 4.00038C9.34518 4.00038 9.625 4.2802 9.625 4.62538V10.25C9.625 10.5952 9.34518 10.875 9 10.875C8.65482 10.875 8.375 10.5952 8.375 10.25V4.62538C8.375 4.2802 8.65482 4.00038 9 4.00038ZM8.24286 13.5C8.24286 13.0858 8.57864 12.75 8.99286 12.75C9.40707 12.75 9.74286 13.0858 9.74286 13.5C9.74286 13.9142 9.40707 14.25 8.99286 14.25C8.57864 14.25 8.24286 13.9142 8.24286 13.5Z"
            fill="#F2995F"
            // style="fill:#F2995F;fill:color(display-p3 0.9490 0.6000 0.3725);fill-opacity:1;"
          />
        </svg>

        <span
          style={{
            color: 'rgba(0,0,0,0.9)',
          }}
        >
          已超时，请重新扫码
        </span>
      </div>
      <p className={Styles.loginDesc}>
        需要使用开播视频号管理员的账号扫码，用于获取用户互动信息
      </p>
    </>
  );
}

/**
 * 开播指引
 */
function LiveGuide(
  props: Pick<React.HTMLAttributes<HTMLElement>, 'className' | 'style'>
) {
  const { className, style } = props;
  const handleOpenGuide = () => {
    window.open(MatchedGlobalConfigItem.liveConf.obsGuideLink);
  };
  return (
    <div
      className={`step-guide ${Styles.stepGuideMini} ${className || ''}`}
      style={{
        ...style,
      }}
    >
      <div
        className="flex items-center justify-between"
        style={{
          marginBottom: '27px',
        }}
      >
        <h1
          style={{
            margin: 0,
          }}
        >
          开播引导
        </h1>
        <div>
          <span
            className="text-sm cursor-pointer"
            style={{
              color: 'rgba(0, 71, 249, 1)',
            }}
            onClick={handleOpenGuide}
          >
            指引文档
          </span>
        </div>
      </div>
      <ul>
        <li>
          <div>
            <div>扫码启动互动通路</div>
          </div>
        </li>
        <li>
          <div>
            <p>配置拉流地址</p>
          </div>
        </li>
        <li>
          <div>
            <p>启动虚拟摄像头</p>
          </div>
        </li>
        <li>
          <div>
            <p>开始推流</p>
          </div>
        </li>
        <li>
          <div>
            <p>开启直播</p>
          </div>
        </li>
      </ul>
    </div>
  );
}

function InnerHostedLiveConfig(props: IHostedLiveConfigProps) {
  const { metaLiveID, onCancel, onConfirm } = props;
  const [urlInfo, setUrlInfo] = useState<{
    pushUrl: string;
    playUrl: string;
  }>();

  /**
   * 获取 url 的loading
   */
  const [loadingUrl, setLoadingUrl] = useState(false);

  const {
    available: interactionAvailable,
    onAvailableStateChange: onInteractionAvailableStateChange,
    ref: interactionRef,
  } = useInteractionNotification();

  const queryLiveUrl = useCallback(async () => {
    setLoadingUrl(true);
    const [err, result] = await to(
      Promise.all([
        GetPushUrl({
          meta_live_id: metaLiveID,
        }),
        GetLiveUrl({
          meta_live_id: metaLiveID,
        }),
      ])
    );
    setLoadingUrl(false);
    if (err) {
      MessagePlugin.error('获取拉流地址失败，请重新扫码');
      return;
    }
    const [{ push_url }, { live_url }] = result;
    setUrlInfo({
      pushUrl: push_url,
      playUrl: live_url,
    });
  }, [metaLiveID]);
  /**
   * 处理登录后逻辑
   */
  const handleLogin = useCallback(async () => {
    await queryLiveUrl();
  }, [queryLiveUrl]);

  const resetUrl = useCallback(() => {
    setUrlInfo(undefined);
  }, []);

  /**
   * 渲染二维码刷新
   */
  const renderRefreshQRCode = useCallback(
    (options: ILoginCustomRenderData) => {
      const { refreshQRCode } = options;
      return (
        <div>
          <Button
            onClick={() => {
              resetUrl();
              refreshQRCode();
            }}
            shape="rectangle"
            theme="default"
            style={{ background: '#fff' }}
          >
            刷新
          </Button>
        </div>
      );
    },
    [resetUrl]
  );

  return (
    <>
      <div
        className={`live_qrcode_container ${Styles.hostedLiveContainer} flex`}
      >
        {/* 左侧二维码区域 */}
        <div
          className="flex flex-col justify-between"
          style={{
            width: '54%',
          }}
        >
          <div
            style={{
              padding: '23px 41px 11px 41px',
            }}
          >
            <InteractionNotification
              ref={interactionRef}
              onAvailableStateChange={onInteractionAvailableStateChange}
              width="238px"
              liveID={metaLiveID}
              className="gap-5"
            >
              <InteractionLogin
                titleRender={(props) => {
                  let el;
                  if (
                    [
                      DialogState.NOT_SCANNED,
                      DialogState.SCANNED_PENDING_CONFIRMATION,
                    ].includes(props.qrCodeState)
                  )
                    el = <InitTitle />;
                  else if (props.qrCodeState === DialogState.LOGGED_IN)
                    el = <AlreadyLoginTitle />;
                  else {
                    el = <LoginExpireTitle />;
                  }
                  return (
                    <div className="flex flex-col justify-center items-center w-[270px]">
                      {el}
                    </div>
                  );
                }}
                meta_live_id={metaLiveID}
                barrageCheckFn={
                  MatchedGlobalConfigItem.liveConf.liveQRConf.barrageCheckFn
                }
                contentRender={
                  MatchedGlobalConfigItem.liveConf.liveQRConf.contentRender
                }
                onLogin={handleLogin}
                immediate
                renderExpired={renderRefreshQRCode}
                renderLoggined={renderRefreshQRCode}
              />
            </InteractionNotification>
          </div>
          <div
            className={`${Styles.livePlayUrlContainer} flex items-center`}
            style={{
              paddingRight: '23px',
            }}
          >
            <div
              style={{
                padding: '0 0 0 11px',
              }}
              className="flex w-full"
            >
              <span>拉流地址：</span>
              <span className="flex-1 whitespace-nowrap overflow-hidden text-ellipsis">
                <Fragment x-if={loadingUrl}>
                  <Loading
                    style={{
                      width: '14px',
                      height: '14px',
                    }}
                  />
                </Fragment>
                <Fragment x-else>{urlInfo?.playUrl || '-'}</Fragment>
              </span>
            </div>
            <div
              x-if={urlInfo?.playUrl}
              className="cursor-pointer"
              onClick={() => {
                if (urlInfo?.playUrl) {
                  copy(urlInfo?.playUrl);
                  MessagePlugin.success('已复制');
                }
              }}
            >
              <FileCopyIcon
                style={{
                  color: 'rgba(0, 71, 249, 1)',
                  width: '12px',
                  height: '14px',
                }}
              />
            </div>
          </div>
        </div>
        {/* 右侧指引 */}
        <div
          className="border-l border-solid flex-1"
          style={{
            borderColor: ' rgba(232, 231, 239, 1)',
            padding: '23px 13px 0 29px',
          }}
        >
          <LiveGuide />
        </div>
      </div>
      <HostedLiveFooter
        onCancel={onCancel}
        onConfirm={() => {
          if (!interactionRef.current) {
            MessagePlugin.error('组件异常!');
            return;
          }
          interactionRef.current
            .confirm()
            .then(() => {
              onConfirm({
                pushUrl: urlInfo!.pushUrl,
                pullUrl: urlInfo!.playUrl,
              });
            })
            .catch((e) => {
              MessagePlugin.error(`系统错误，请稍后重试（${e.message}）`);
            });
        }}
        disabledConfirmed={!(urlInfo?.playUrl && interactionAvailable)}
      />
    </>
  );
}

function HostedLiveFooter(props: IFooterProps) {
  const { onCancel, onConfirm, disabledConfirmed } = props;
  return (
    <div className="live_qrcode_footer">
      <Space>
        <Button theme="default" className="gradient-default" onClick={onCancel}>
          取消
        </Button>
        <Button
          className="gradient-primary"
          onClick={onConfirm}
          disabled={disabledConfirmed}
        >
          开始推流
        </Button>
      </Space>
    </div>
  );
}

interface UrlInfo {
  pushUrl: string;
  playUrl: string;
}

interface YoutubeFormValues {
  id: string;
  password: string;
}

function YoutubeForm(props: {
  form: FormInstanceFunctions;
  metaLiveID: string;
  onUpdateUrlInfo: (urlInfo: UrlInfo) => void;
}) {
  const { form, metaLiveID, onUpdateUrlInfo } = props;
  const youtubeRules: FormRules<YoutubeFormValues> = {
    id: [
      { whitespace: true, message: '开播账号不能为空' },
      { required: true, message: '开播账号不能为空', type: 'error' },
    ],
    password: [
      { whitespace: true, message: '开播密码不能为空' },
      { required: true, message: '开播密码不能为空', type: 'error' },
    ],
  };
  const [urlInfo, setUrlInfo] = useState<UrlInfo>();
  const [loadingUrl, setLoadingUrl] = useState(false);

  const onSubmit = async () => {
    const res = await form.validate();
    if (res !== true) return;
    const values = form.getFieldsValue(true) as YoutubeFormValues;
    try {
      setLoadingUrl(true);
      await GetLoginQRCode({
        features: JSON.stringify(values),
        resource_id: 'youtube',
        meta_live_id: metaLiveID,
      });
      await QueryQRCodeStatus({
        meta_live_id: metaLiveID,
      });
      const result = await Promise.all([
        GetPushUrl({
          meta_live_id: metaLiveID,
        }),
        GetLiveUrl({
          meta_live_id: metaLiveID,
        }),
      ]);
      const [{ push_url }, { live_url }] = result;
      const urlInfo = {
        pushUrl: push_url,
        playUrl: live_url,
      };
      setUrlInfo(urlInfo);
      onUpdateUrlInfo(urlInfo);
    } catch (error) {
      MessagePlugin.error('获取拉流地址失败，请检查账号密码是否正确');
      return;
    } finally {
      setLoadingUrl(false);
    }
  };

  return (
    <div className="form" style={{ width: '54%', margin: 16 }}>
      <Form
        form={props.form}
        layout="vertical"
        labelWidth={60}
        labelAlign="top"
        rules={youtubeRules}
        onSubmit={onSubmit}
      >
        <FormItem
          requiredMark
          label="开播账号"
          name="id"
          initialData="midasbuy"
        >
          <Input placeholder="请输入开播账号" style={{ width: '100%' }} />
        </FormItem>
        <FormItem requiredMark label="开播密码" name="password">
          <Input
            type="password"
            placeholder="请输入开播密码"
            style={{ width: '100%' }}
          />
        </FormItem>
        <FormItem>
          <Button block type="submit">
            获取拉流地址
          </Button>
        </FormItem>
      </Form>
      <div
        className={`${Styles.livePlayUrlContainer} flex items-center pr-4 mt-24 rounded-[4px]`}
      >
        <div className="flex w-full pl-12">
          <p>拉流地址：</p>
          <p className="flex-1 whitespace-nowrap overflow-hidden text-ellipsis">
            <Fragment x-if={loadingUrl}>
              <Loading
                style={{
                  width: '14px',
                  height: '14px',
                }}
              />
            </Fragment>
            <Fragment x-else>{urlInfo?.playUrl || '-'}</Fragment>
          </p>
          <div
            x-if={urlInfo?.playUrl}
            className="cursor-pointer pr-8"
            onClick={() => {
              if (urlInfo?.playUrl) {
                copy(urlInfo?.playUrl);
                MessagePlugin.success('已复制');
              }
            }}
          >
            <FileCopyIcon
              style={{
                color: 'rgba(0, 71, 249, 1)',
                width: '12px',
                height: '14px',
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export function YoutubeHostedLiveConfig(props: IHostedLiveConfigProps) {
  const { metaLiveID, onCancel, onConfirm } = props;
  const [urlInfo, setUrlInfo] = useState<UrlInfo>();
  const [youtubeForm] = Form.useForm();

  return (
    <>
      <div
        className={`live_qrcode_container ${Styles.hostedLiveContainer} flex`}
      >
        {/* 左侧账密区域 */}
        <YoutubeForm
          form={youtubeForm}
          metaLiveID={metaLiveID}
          onUpdateUrlInfo={setUrlInfo}
        />
        {/* 右侧指引 */}
        <div
          className="border-l border-solid flex-1"
          style={{
            borderColor: ' rgba(232, 231, 239, 1)',
            padding: '23px 13px 0 29px',
          }}
        >
          <LiveGuide />
          <Divider />
        </div>
      </div>
      <HostedLiveFooter
        onCancel={onCancel}
        onConfirm={async () => {
          if (!urlInfo) return;
          onConfirm({
            pushUrl: urlInfo.pushUrl,
            pullUrl: urlInfo.playUrl,
          });
        }}
        disabledConfirmed={!urlInfo?.playUrl}
      />
    </>
  );
}

export const HostedLiveConfig = InnerHostedLiveConfig;
