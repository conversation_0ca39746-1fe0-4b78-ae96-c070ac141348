/**
 * <AUTHOR>
 * @date 2024/11/8 15:00
 */
import { ContentDetailQuery } from '@/pb/api/ContentSvr';
import { IVirtualManLiveData } from '@/components/LiveQRCode/typings';
import { VirtualManPlatformMap } from '@/components/LiveQRCode/constants';
import { PlayConfig } from '@/type/pagedoo';

const Virtualman_Component_Id = 'Virtualman';
const ADSpeechComponetID = 'LiveSpeechAD';

// 迁移判断逻辑
export const checkLiveConf = async (liveId: string) => {
  if (!liveId) return;

  const res = await ContentDetailQuery({
    content_id: liveId,
  });

  const playScript = JSON.parse(res.extend.playScript);

  return checkPlayConfig(playScript);
};

export const checkPlayConfig = (playScriptConf: PlayConfig) => {
  // 是否需要交互，是的话需要扫码登录抓弹幕
  let needInteractionLogin = false;
  const timeLine = playScriptConf.timeline;
  const virtualmanMetaLiveData: IVirtualManLiveData[] = [];

  timeLine.map((item) => {
    const nodeList = item.node;
    nodeList.map((node) => {
      const { component } = node;
      const idSplits = component.id.split('/');
      const isVirtualman = idSplits.includes(Virtualman_Component_Id);
      if (isVirtualman) {
        const { data = {} } = component;
        const virtualmanConfig = data.virtualMan;
        if (virtualmanConfig) {
          const targetPlt =
            VirtualManPlatformMap[virtualmanConfig.type as string] ||
            virtualmanConfig.type;
          virtualmanMetaLiveData.push({
            platform: targetPlt,
            platformAcctId: virtualmanConfig.appkey || '',
            virtualManKey: virtualmanConfig.key || '',
          });
        }
      }
      if (idSplits.includes('LiveSpeech') && !!component.data.__liveNeedLogin) {
        needInteractionLogin = true;
      } else if (idSplits.includes(ADSpeechComponetID)) {
        // 存在广告话术组件，强制登录
        needInteractionLogin = true;
      }
    });
  });

  return {
    virtualmanMetaLiveData,
    needInteractionLogin,
  };
};
