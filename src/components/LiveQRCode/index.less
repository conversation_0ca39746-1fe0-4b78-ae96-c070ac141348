.live_qrcode_comp {
  .live_qrcode_header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 20px;
  }

  .live_qrcode_container {
    display: flex;
    padding: 20px;
    border-radius: 4px;
    background: linear-gradient(rgba(244, 246, 255, 1), rgba(250, 245, 252, 1));

    .form {
      width: 282px;

      .t-form__label {
        color: rgba(0, 0, 0, 0.60);
      }
    }

    .divider {
      margin: 0 16px 0 0;
      background-color: #E8E7EF;
      width: 1px;
    }

    .step-guide {
      flex: 1;
    }

    .step-guide {
      h1 {
        color: rgba(0, 0, 0, 0.9);
        margin-bottom: 20px;
      }

      ul {
        list-style-type: none;
        counter-reset: sectioncounter;
      }

      li {
        color: rgba(0, 0, 0, 0.9);
        padding-bottom: 10px;
        margin-bottom: 2px;
        position: relative;
        display: flex;
        justify-content: flex-start;
        font-size: 16px;

        .tip {
          color: rgba(0, 0, 0, 0.4);
          font-size: 13px;
        }
      }

      li::before {
        content: counter(sectioncounter) '  ';
        counter-increment: sectioncounter;
        width: 24px;
        height: 24px;
        display: inline-block;
        border: 1px solid rgba(0, 0, 0, 0.4);
        border-radius: 50%;
        text-align: center;
        margin-right: 10px;
        color: rgba(0, 0, 0, 0.4);
      }

      li:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 12px;
        top: 26px;
        bottom: 0;
        width: 2px;
        background-color: rgba(0, 0, 0, 0.06);
      }
    }
  }

  .live_qrcode_footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .live_qrcode_success {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.interaction-login {
  .title {
    color: #00000099;
  }
  .login-explain {
    align-items : center;
    margin-top: 20px;
    background: linear-gradient(88.08deg, #C2D6FF -0.01%, #CDE0FF 49.89%, #F0E9FF 99.99%);
    color: #000000E5;
  }
  .qrcode-wrap {
    padding: 34px 0;
    background: linear-gradient(84.64deg, #F4F6FF 0%, #FAF5FC 100%);
    .qrcode-wrap-inner {
      position: relative;
      margin: 0 auto;
      width: 272px;
      height: 272px;
      .qrcode-img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
      .scanned-dialog {
        background: rgba(57, 57, 57, 0.73);
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        .scanned-dialog-content {
          color: #fff;
          text-align: center;
        }
      }
    }
  }
}