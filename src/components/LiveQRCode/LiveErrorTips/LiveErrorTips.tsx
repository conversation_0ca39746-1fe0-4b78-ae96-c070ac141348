import { Dialog, DialogPlugin } from 'tdesign-react';
import { LiveTipsSvgs } from './svgs';
import { css } from '@emotion/react';

export interface ILiveErrorTipsProps {
  msg?: string;
  desc?: string;
  cancelButtonText: string | null;
  confirmButtonText: string | null;
  onCancel?: () => void;
  onConfirm?: () => void;
}
export function LiveErrorTips(props: ILiveErrorTipsProps) {
  const {
    msg,
    desc,
    cancelButtonText,
    confirmButtonText,
    onCancel,
    onConfirm,
  } = props;
  return (
    <div className="flex flex-col items-center w-[264px] pagedoo-meta-live-global gap-y-[57px]">
      <div className="flex flex-col items-center">
        <section className="w-[83px] h-[74px] mb-[4px]">
          {LiveTipsSvgs.fail}
        </section>
        <h3 className="mb-16">{msg}</h3>
        <p style={{ margin: 0 }} className="text-sm">
          {desc}
        </p>
      </div>
      <div className="flex gap-x-2">
        <div
          className="px-[20px] py-[5px] cursor-pointer"
          css={css`
            background: linear-gradient(84.64deg, #f4f6ff 0%, #faf5fc 100%);
            color: rgba(0, 0, 0, 0.9);
            border-radius: 4px;
          `}
          onClick={onCancel}
        >
          {cancelButtonText}
        </div>
        <div
          className="gradient-primary px-[20px] py-[5px] cursor-pointer"
          css={css`
            border-radius: 4px;
            color: rgba(255, 255, 255, 0.9);
          `}
          onClick={onConfirm}
        >
          {confirmButtonText}
        </div>
      </div>
    </div>
  );
}

export const showLiveErrorTipsDialog = (
  options: {
    title: string;
  } & ILiveErrorTipsProps
) => {
  const { title, ...rest } = options;
  const onCancel = () => {
    inst?.destroy();
    rest?.onCancel?.();
  };
  const onConfirm = () => {
    inst?.destroy();
    rest?.onConfirm?.();
  };
  const inst = DialogPlugin.confirm({
    footer: false,
    destroyOnClose: true,
    header: title,
    body: (
      <div className="w-full flex justify-center mt-[49px]">
        <LiveErrorTips {...rest} onCancel={onCancel} onConfirm={onConfirm} />
      </div>
    ),
    closeOnOverlayClick: false,
    closeOnEscKeydown: false,
    onClose: () => inst?.destroy(),
  });
  return {
    destroy: () => {
      inst?.destroy();
    },
  };
};
