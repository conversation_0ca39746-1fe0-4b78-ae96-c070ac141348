// 小程序码组件
import { css } from '@emotion/react';
import { useLatest } from 'ahooks';
import omit from 'lodash-es/omit';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  CheckCircleFilledIcon,
  InfoCircleFilledIcon,
} from 'tdesign-icons-react';
import { But<PERSON>, Loading } from 'tdesign-react';
import Styles from './style.module.less';

interface ICustomRenderApi {
  // 主动触发刷新二维码
  refresh: () => void;
  changeQRCodeState: (state: QRCodeState) => void;
  state: QRCodeState;
}
export interface IBaseQRCodeProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'style' | 'className'> {
  qrCode: string;
  loading: boolean;
  onRefresh?: () => void;
  title?: string;
  imgStyle?: React.CSSProperties;
  loadingStyle?: React.CSSProperties;
  renderScanState?: (api: ICustomRenderApi) => React.ReactNode;
  // 二维码状态受控
  qrcodeState?: QRCodeState;
  onStateChange?: (current: QRCodeState, prev: QRCodeState) => void;
}

export interface IMiniProgramInstance
  extends Pick<ICustomRenderApi, 'refresh'> {
  getQRCodeState: () => QRCodeState;
}

// 二维码状态
type QRCodeState = 'normal' | 'scan-success' | 'scan-timeout' | 'success';

const defaultScanStateRender: IBaseQRCodeProps['renderScanState'] = (api) => {
  //   return null;
  let el: React.ReactNode = null;
  if (api.state === 'scan-success') {
    el = (
      <div className={Styles.default_state_render__content}>
        <svg
          width="30"
          height="30"
          viewBox="0 0 30 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          shapeRendering="geometricPrecision"
        >
          <path
            d="M15 29.875C23.2152 29.875 29.875 23.2152 29.875 15C29.875 6.78476 23.2152 0.125 15 0.125C6.78476 0.125 0.125 6.78476 0.125 15C0.125 23.2152 6.78476 29.875 15 29.875ZM8.26947 16.1459C7.87901 15.7554 7.87904 15.1223 8.26954 14.7318L8.3568 14.6445C8.74729 14.254 9.38038 14.254 9.77092 14.6444L12.875 17.7476L20.227 10.3947C20.6175 10.0041 21.2507 10.0041 21.6412 10.3947L21.7304 10.4838C22.1209 10.8743 22.1209 11.5075 21.7303 11.898L13.5822 20.0453C13.1916 20.4358 12.5584 20.4357 12.1679 20.0452L8.26947 16.1459Z"
            fill="url(#paint0_linear_10349_39611)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_10349_39611"
              x1="0.125"
              y1="0.125"
              x2="29.875"
              y2="0.125"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#00C653" />
              <stop offset="1" stopColor="#01D766" />
            </linearGradient>
          </defs>
        </svg>

        <span
          css={css`
            margin-top: 8px;
            color: rgba(255, 255, 255, 1);
          `}
        >
          扫码成功
        </span>
      </div>
    );
  } else if (api.state === 'scan-timeout') {
    el = (
      <div
        className={`pagedoo-meta-live-global ${Styles.default_state_render__content}`}
      >
        <InfoCircleFilledIcon
          css={css`
            color: rgba(242, 153, 95, 1);
          `}
          width={30}
          height={30}
          style={{
            width: '30px',
            height: '30px',
          }}
        />
        <span
          css={css`
            color: rgba(255, 255, 255, 1);
            margin-top: 8px;
          `}
        >
          已超时，请刷新
        </span>
        <Button
          theme="primary"
          className="gradient-primary"
          css={css`
            margin-top: 4px;
            width: 48px;
            height: 24px;
            font-size: 12px;
          `}
          onClick={() => api.refresh()}
        >
          刷新
        </Button>
      </div>
    );
  }
  if (el) {
    el = <div className={Styles.default_state_render__wrapper}>{el}</div>;
  }
  return el;
};

export const BaseQRCode = forwardRef<IMiniProgramInstance, IBaseQRCodeProps>(
  (props: IBaseQRCodeProps, ref) => {
    const {
      loading,
      qrCode,
      title,
      imgStyle,
      style,
      className,
      onRefresh,
      onStateChange,
      renderScanState = defaultScanStateRender,
      loadingStyle,
      qrcodeState: controlledQRCodeState,
    } = props;
    const [qrcodeState, setQRCodeState] = useState<QRCodeState>(
      typeof controlledQRCodeState !== 'undefined'
        ? controlledQRCodeState
        : 'normal'
    );
    const lastQRCodeStateRef = useRef(qrcodeState);
    const latestQRCodeState = useLatest(qrcodeState);
    const api = useMemo<ICustomRenderApi>(() => {
      return {
        refresh: () => {
          setQRCodeState('normal');
          onRefresh?.();
        },
        changeQRCodeState: setQRCodeState,
        state: qrcodeState,
      };
    }, [onRefresh, qrcodeState]);

    useImperativeHandle(ref, () => ({
      ...omit(api, ['state']),
      getQRCodeState: () => qrcodeState,
    }));

    useEffect(() => {
      if (qrcodeState !== lastQRCodeStateRef.current) {
        const last = lastQRCodeStateRef.current;
        lastQRCodeStateRef.current = qrcodeState;
        onStateChange?.(qrcodeState, last);
      }
    }, [qrcodeState, onStateChange]);

    // 同步受控状态
    useEffect(() => {
      if (!controlledQRCodeState) return;
      if (latestQRCodeState.current !== controlledQRCodeState) {
        setQRCodeState(controlledQRCodeState);
      }
    }, [controlledQRCodeState, latestQRCodeState]);

    let imgEl = (
      <img
        style={{
          ...imgStyle,
          //   height: '260px',
          //   width: '260px',
          //   marginTop: '10px',
          //   borderRadius: '8px',
        }}
        src={qrCode}
        alt=""
      />
    );
    // let imgEl;
    if (loading) {
      imgEl = (
        <Loading style={{ ...loadingStyle }}>
          <></>
        </Loading>
      );
    }
    return (
      <div
        style={{ ...style }}
        className={`${Styles.container} ${className || ''}`}
      >
        <div>{title}</div>
        <div
          style={{
            position: 'relative',
          }}
        >
          {imgEl}
          {renderScanState?.(api)}
        </div>
      </div>
    );
  }
);

BaseQRCode.displayName = 'BaseQRCode';
