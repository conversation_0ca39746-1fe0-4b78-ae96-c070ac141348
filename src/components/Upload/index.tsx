import { Upload, UploadFile } from 'tdesign-react';
import { UploadCosProps } from './type';

export function UploadCos(props: UploadCosProps) {
  const { uploadRequest, type, value, className, ...p } = props;

  /**
   * @description 保留函数调用
   */
  const getTask = () => {
    return;
  };

  const requestSingle = async (file: UploadFile) => {
    const result = await uploadRequest(type, file.raw!, 0, getTask);
    const res = {
      status: result.code === 200 ? 'success' : 'fail',
      response: { url: result.url, cosKey: result.key },
    } as const;
    return res;
  };

  /**
   * 上传 cos 接口
   * @param compFile
   * @returns
   */
  const requestCos = async (compFile: UploadFile[] | UploadFile) => {
    try {
      if (!props.multiple) {
        const res = await requestSingle(compFile);
        return res;
      }
      // TODO: 这里 不明确 compFile 什么时候会有多个值
      const result = await requestSingle((compFile as UploadFile[])[0]);
      return result;
    } catch (e) {
      return {
        status: 'fail' as 'success' | 'fail',
        response: {},
      };
    }
  };

  // 基于 tdesign 封装 Upload 组件，上传 Cos
  return (
    <Upload
      key={Math.random()}
      files={value || props.files}
      {...p}
      requestMethod={requestCos}
      className={className}
    />
  );
}
