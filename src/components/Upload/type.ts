import { UploadFile, UploadProps } from 'tdesign-react';

/**
 * cos 上传支持的文件类型
 */
type TYPE = 'image' | 'video' | 'audio' | 'file';

/**
 * Upload 原生 props 和额外的 props
 */
export interface UploadCosProps extends Omit<UploadProps, 'requestMethod'> {
  /**
   * @description 上传内容类型 'image' | 'video' | 'audio' | 'file'
   */
  type: TYPE;
  /**
   * 将 theme 转成是必传属性
   */
  theme: UploadProps['theme'];
  /**
   * 为了融入form上下文
   */
  value?: UploadFile[];
  /**
   * @description 上传 cos 的请求方法
   */
  uploadRequest: UploadRequest;
  className?: string;
}

export interface UploadResponseProps {
  /**
   * @description cos 参数
   */
  key: string;
  /**
   * @description 上传成功后返回的url
   */
  url: string;
  /**
   * @description 状态码
   */
  code?: number;
}

/**
 * 对照 sr-ui 的 uploadRequest API
 * 降低改造复杂度
 */
export type UploadRequest<T = UploadResponseProps> = (
  type: any,
  file: File,
  index: number,
  getTask: () => void
) => Promise<T>;
