/**
 * <AUTHOR>
 * @date 2024/8/15 下午4:11
 * @desc AddFormItemBtn
 */

import React from 'react';
import { Tag } from 'tdesign-react';
import { AddIcon } from 'tdesign-icons-react';

interface IProps {
  text: string;
  tagText?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export function AddFormItemBtn(props: IProps) {
  const { text, tagText, onClick, disabled } = props;
  return (
    <div
      onClick={() => {
        if (!disabled) {
          onClick?.();
        }
      }}
      style={{
        marginTop: 24,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 4,
        background:
          'linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%)',
        textAlign: 'center',
        fontSize: 16,
        height: 40,
        lineHeight: '40px',
        color: 'rgba(0, 71, 249, 1)',
        opacity: disabled ? '0.4' : '1',
        cursor: disabled ? 'not-allowed' : 'pointer',
      }}
    >
      <span className="flex items-center">
        <AddIcon />
        {text}
      </span>
      {tagText && (
        <Tag
          shape="round"
          style={{ marginLeft: '10px', border: 'none' }}
          theme="warning"
          variant="light-outline"
        >
          {tagText}
        </Tag>
      )}
    </div>
  );
}
