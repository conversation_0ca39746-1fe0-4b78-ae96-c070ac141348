/**
 * <AUTHOR>
 * @date 2024/8/16 上午10:40
 * @desc utils
 */
import { MetaFeedbackSvr, NpcScriptSvr } from '@/pb/pb';
import { GLOBAL_FIELDS } from '@/pages/Question/constant';
import { getParam } from '@tencent/midas-util';
import {
  IScriptForm,
  MaterialView,
  ResponseMaterialScriptView,
  ResponseScriptView,
  ResponseSubjectView,
  ScriptFormInputType,
  ScriptGlobalData,
  ScriptView,
} from '@/components/ScriptForm/type';
import { cloneDeep } from 'lodash-es';
import { VideoInfo } from '@/pages/ScriptList/hooks/useVisionScriptInfo';
import { SelectionResourceType } from '@/pages/ScriptList/components/Selections/typing';
import { ReqType } from '@/pb/config';

export const handleScriptFormInput = (value: IScriptForm) => {
  const res = cloneDeep(value);
  let inputType: ScriptFormInputType = 'html';
  // 电商脚本
  if (res.eCommerce) {
    inputType = 'eCommerce';
    res.eCommerce.goods.forEach(
      // eslint-disable-next-line no-param-reassign
      (item) => (item.img = [{ url: item?.img?.[0]?.url || '' }])
    );
  }
  if (res.content) {
    inputType = 'content';
    res.content.contentList = res.content.contentList.map((item) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const content_image = item?.content_image?.[0]?.url || '';
      return {
        ...item,
        content_image,
      };
    });
  }
  // 游戏推广视频脚本
  if (res.gameProm) {
    inputType = 'gameProm';
  }
  // ppt脚本
  if (res.ppt) {
    inputType = 'pptx';
  }
  if (res.doc) {
    inputType = 'html';
    const fileType = res.doc.fileUrl.split('.').pop()?.toLowerCase();
    if (fileType && ['doc', 'docs', 'docx'].includes(fileType)) {
      inputType = 'doc';
    }
  }
  return { formValue: res, inputType };
};

export const handleScriptViewOutput = (
  views: ScriptView[],
  formValue: IScriptForm
) => {
  const newViews = cloneDeep(views);
  // 电商处理，将商品图放入imageList
  if (formValue?.eCommerce?.goods) {
    const productImage = formValue?.eCommerce?.goods?.[0]?.img;

    if (productImage) {
      newViews.forEach((item) => {
        // eslint-disable-next-line no-param-reassign
        item.imageList = formValue?.eCommerce?.goods[0].img;
      });
    }
  }
  // 内容直播处理
  // FIXME: pino后续优化
  if (formValue?.content?.contentList) {
    const len = formValue?.mainPoint?.length ?? 0;
    if (len > 0) {
      newViews.forEach((item, index) => {
        const i = index / len;
        const url = formValue.content?.contentList[i]?.content_image || '';
        // eslint-disable-next-line no-param-reassign
        item.imageList = [{ url }];
      });
    } else {
      newViews.forEach((item, index) => {
        const url = formValue.content?.contentList[index]?.content_image || '';
        // eslint-disable-next-line no-param-reassign
        item.imageList = [{ url }];
      });
    }
  }

  return newViews;
};

export const getFromParams = () => {
  const fromParam = getParam('from');
  const [type, application_scenarios] = fromParam.split('__');
  return {
    fromParam,
    type,
    application_scenarios,
  };
};

export const transformScriptView = (
  originViews: ResponseScriptView[] | ScriptView[]
): ScriptView[] => {
  return originViews.map((item) => {
    if ('viewType' in item) {
      return item;
    }
    return {
      viewType: item.画面类型,
      viewName: item.画面名称,
      viewContent: item.画面内容,
      viewMainText: item.画面正文,
      duration: item.时长,
      speech: item.台词文案,
      flowerText: item.文本贴纸,
      isInteractive: item.互动画面,
      background: item.background,
      imageList: item.imageList,
      resources: item.resources,
    };
  });
};

export const transformMaterialScriptView = (
  originViews: ResponseMaterialScriptView[] | MaterialView[]
): MaterialView[] => {
  return originViews.map((item) => {
    if ('showTime' in item) {
      return item;
    }
    return {
      name: item.中文名,
      content: item.画面大纲,
      viewTime: item?.画面时长,
      showTime: item.出现时刻,
      flowerText: item?.文本贴纸,
      resourceId: item.资源id,
      resourceUrl: item.资源地址,
      resourceType: item.资源类型,
      resourceList: item.resourceList,
    };
  });
};

export const handleScriptGlobalData = ({
  originViews,
  info,
}: {
  originViews: ResponseSubjectView;
  info?: VideoInfo;
}): ScriptGlobalData => {
  return {
    title: originViews[GLOBAL_FIELDS.TITLE] || '',
    subTitle: originViews[GLOBAL_FIELDS.SUBTITLE] || '',
    info,
  };
};

export const getAllResource = (dataSource: MaterialView[]) => {
  const res: ScriptGlobalData['globalResourceList'] = [];

  dataSource?.forEach((item) => {
    const key =
      Object.keys(item.resourceList || {})?.[0] ||
      (item.flowerText ? SelectionResourceType.FLOWER_TEXT : '');

    res.push({
      resourceType: item.resourceType,
      resourceKey: key,
      resource: Object.assign(
        item.resourceList?.[key]?.[0] ?? {},
        item.flowerText ? { flowerText: item.flowerText } : {}
      ),
      showTime: item.showTime,
    });
  });
  return res;
};

export const queryScriptList = async (research_id: string) => {
  const scriptRes = await MetaFeedbackSvr.QueryScriptList({
    research_id,
  });
  const scriptList = scriptRes.script_list;
  scriptRes.script_list = scriptList.map((item) => {
    const scriptInfo = JSON.parse(item.script_info);
    let globalField = scriptInfo.globalField as
      | ScriptGlobalData
      | ResponseSubjectView;
    // 兼容旧的逻辑
    if (globalField) {
      if ('剧本大标题' in globalField) {
        globalField = handleScriptGlobalData({
          originViews: globalField,
          info: {
            video_duration: scriptInfo?.backgroundVideo || '',
            video_address: scriptInfo?.totalDuration || '',
          },
        });
      }
    }
    const scriptView = scriptInfo.views as ResponseScriptView[] | ScriptView[];
    const {
      [GLOBAL_FIELDS.TITLE]: title,
      [GLOBAL_FIELDS.SUBTITLE]: subTitle,
      ...rest
    } = scriptInfo?.globalField || {};

    return {
      ...item,
      script_info: JSON.stringify({
        ...scriptInfo,
        globalField: {
          ...(rest || {}),
          title: globalField?.title,
          subTitle: globalField?.subTitle,
        },
        views: transformScriptView(scriptView),
      }),
    };
  });
  return scriptRes;
};

// html:网页类
// ppt:ppt类型
// doc:文档doc类
// gameProm:游戏推广类
// eCommerce:电商脚本类
/**
 * 脚本生成
 * @param formValue
 * @param inputType
 * @param autoCreateScript
 */
export const createScriptJob = async ({
  formValue,
  inputType,
  autoCreateScript = true,
}: {
  formValue: IScriptForm;
  inputType: ScriptFormInputType;
  autoCreateScript?: boolean;
}) => {
  const params: ReqType<typeof NpcScriptSvr.CreateNpcJob> = {
    model_input_type: inputType,
    npc_script_input: {
      npc_comm_header: {
        model_type: formValue.type,
        model_domain: formValue?.domain,
        model_main_points: formValue?.mainPoint || [],
        model_script_time: formValue.time.toString(),
        auto_create_script: autoCreateScript,
        subject_parameters: Object.values(GLOBAL_FIELDS),
      },
      // 电商脚本
      npc_e_commerce_input: formValue.eCommerce
        ? {
            commerce_goods: formValue.eCommerce.goods.map((item) => {
              return {
                activity: item.activity,
                serve: item.serve,
                reward: item.reward,
                seller: item.seller,
                images: [{ url: item.img?.[0]?.url || '' }],
                name: item.name,
              };
            }),
          }
        : undefined,
      // 游戏脚本
      npc_game_prom_input: formValue.gameProm
        ? {
            // diu,先帮后端兼容一下吧
            themes: formValue.gameProm.themes.map((item) => {
              return {
                knowledge: item.desc,
                name: item.name,
              };
            }),
            videos: formValue.gameProm.themes.map((item) => {
              const url = item.selectVideo.video_url.startsWith('//')
                ? `https:${item.selectVideo.video_url}`
                : item.selectVideo.video_url;
              return {
                content: '',
                url,
                name: '',
                themes: [{ name: item.name, knowledge: item.desc }],
              };
            }),
          }
        : undefined,
      npc_ppt_input: formValue.ppt
        ? {
            ppt_url: formValue.ppt.fileUrl,
          }
        : undefined,
      npc_doc_input: formValue.doc
        ? {
            file_url: formValue.doc.fileUrl,
          }
        : undefined,
      npc_content_input: formValue.content
        ? {
            content_list: formValue.content.contentList.map((item) => {
              return {
                name: item.content_name,
                url: item.content_image || '',
                detail: item.content_detail || '',
              };
            }),
          }
        : undefined,
    },
  };
  return await NpcScriptSvr.CreateNpcJob(params);
};
