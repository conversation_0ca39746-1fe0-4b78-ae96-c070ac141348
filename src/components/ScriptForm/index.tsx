/**
 * <AUTHOR>
 * @date 2024/8/15 下午3:50
 * @desc 脚本表单
 */

import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { SCRIPT_FORM_DEFAULT } from '@/components/ScriptForm/const';
import { MainContent } from '@/components/Layout';
import { ScriptTypeWrap } from '@/components/ScriptForm/ScriptTypeWrap';
import { CategoryWrap } from '@/components/ScriptForm/CategoryWrap';
import { MainPointWrap } from '@/components/ScriptForm/MainPointWrap';
import {
  DialogPlugin,
  Form,
  InputNumber,
  Loading,
  message,
  MessagePlugin,
} from 'tdesign-react';
import {
  createScriptJob,
  handleScriptFormInput,
  handleScriptGlobalData,
  handleScriptViewOutput,
  transformMaterialScriptView,
  transformScriptView,
} from '@/components/ScriptForm/utils';
import { CommonScriptLoading } from '@/components/CommonScriptLoading';
import { NPC_STEP, useIntervalNpcStatus } from '@/hooks/useIntervalNpcStatus';
import { NpcScriptSvr } from '@/pb/pb';
import './index.less';
import { CloseCircleFilledIcon } from 'tdesign-icons-react';
import {
  IScriptForm,
  MaterialView,
  ResponseMaterialScriptView,
  ResponseScriptView,
  ResponseSubjectView,
  ScriptFormInputType,
  ScriptGlobalData,
  ScriptView,
} from '@/components/ScriptForm/type';
import { ECommerceForm } from '@/components/ScriptForm/CustomForm/ECommerceForm';
import { GamePromotionForm } from '@/components/ScriptForm/CustomForm/GamePromotionForm';
import { DocForm } from '@/components/ScriptForm/CustomForm/DocForm';
import { PPTForm } from '@/components/ScriptForm/CustomForm/PPTForm';
import { ContentForm } from '@/components/ScriptForm/CustomForm/ContentForm';
import { CONTENT_TYPE_MAP } from '@/const/common';
import {
  getDomainCategory,
  getMainPointList,
  getScriptCategory,
  ScriptFormConf,
  useQueryFormConf,
} from '@/components/ScriptForm/formConf';
import SessionStorageUtil from '@/utils/sessionStorageUtil';
import { cloneDeep } from 'lodash-es';

const { FormItem } = Form;

interface IProps {
  type: string;
  scenarios: string;
  onCreateFail?: () => void;
  onCreateSuccess: (data: ScriptResult) => void;
  uid?: string;
  initFormData?: Partial<IScriptForm>;
}

export interface IScriptFormRef {
  submit: () => Promise<void>;
}

export type ScriptResult = {
  scriptViews: ScriptView[];
  scriptElements: MaterialView[];
  scriptGlobalData: ScriptGlobalData;
};

export const ScriptForm = forwardRef<IScriptFormRef, IProps>((props, ref) => {
  const { type, scenarios, onCreateSuccess, onCreateFail, initFormData, uid } =
    props;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [jobId, setJobId] = useState(''); // 任务id

  const { runAsync, loading: requestLoading } = useQueryFormConf();
  const [scriptFormValue, setScriptFormValue] = useState<IScriptForm>();
  const [scriptFormConf, setScriptFormConf] = useState<ScriptFormConf>({});
  const [scriptTypeOptions, setScriptTypeOptions] = useState<string[]>([]);
  const [domainOptions, setDomainOptions] = useState<
    { value: string; children: string[] }[]
  >([]);
  const [mainPointOptions, setMainPointOptions] = useState<string[]>([]);

  useEffect(() => {
    const init = async () => {
      try {
        const confRes = await runAsync(type, scenarios);
        if (confRes) {
          const conf = JSON.parse(
            confRes.script_category_config.script_config || '{}'
          );
          setScriptFormConf(conf);
        }
      } catch (e) {
        void MessagePlugin.error('脚本配置请求失败');
        console.log(e);
      }
    };

    void init();
  }, [runAsync, scenarios, type]);

  const queryDomainOptions = useCallback(
    (type: string) => {
      const domainOpts = getDomainCategory(scriptFormConf[type]);
      setDomainOptions(domainOpts);
      return domainOpts;
    },
    [scriptFormConf]
  );

  useEffect(() => {
    const opt = getScriptCategory(scriptFormConf);
    setScriptTypeOptions(opt);
  }, [scriptFormConf]);

  const { percent, stepName, startPolling, stopPolling } = useIntervalNpcStatus(
    {
      onJobError: () => {
        onCreateFail?.();
        setLoading(false);
      },
      onJobFinish: () => {
        parseScriptContent().then((res) => {
          if (res) {
            onCreateSuccess(res);
          } else {
            onCreateFail?.();
          }
        });
      },
    }
  );

  const initFormValue = useMemo(() => {
    return {
      ...SCRIPT_FORM_DEFAULT,
      type: scriptTypeOptions?.[0],
      ...(initFormData ?? {}),
    };
  }, [initFormData, scriptTypeOptions]);

  const updateMainPoint = useCallback(
    (type: string, value: string) => {
      const options = getMainPointList(scriptFormConf[type as string], value);
      setMainPointOptions(options);
    },
    [scriptFormConf]
  );

  useEffect(() => {
    const v = cloneDeep(initFormValue);
    if (initFormValue.type) {
      const domainOpt = queryDomainOptions(initFormValue.type);
      const first = domainOpt[0]?.value || '';
      const second = domainOpt[0]?.children?.[0] || '';
      v.domain = `${first}_${second}`;
      updateMainPoint(initFormValue.type, v.domain);
    }
    form.setFieldsValue(v);
    // setTimeout(() => {
    //   form.clearValidate();
    // }, 0);
  }, [form, initFormValue, queryDomainOptions, updateMainPoint]);

  const parseScriptContent = useCallback(async () => {
    if (!scriptFormValue) return;
    try {
      // 调用接口
      const res = await NpcScriptSvr.QueryNpcJobData({
        job_id: jobId,
      });
      // 步骤确认
      if (res.job_stage !== NPC_STEP.GENERATE_SCRIPT) {
        void MessagePlugin.error('解析数据获取失败');
        console.error(
          `步骤校验有误，应该为${NPC_STEP.GENERATE_SCRIPT} 实际为${res.job_stage}`
        );
        setLoading(false);
        return;
      }
      let scriptViews = JSON.parse(res.script_content) as
        | ScriptView[]
        | ResponseScriptView[];
      let scriptElements = JSON.parse(res.script_element_content || '[]') as
        | MaterialView[]
        | ResponseMaterialScriptView[];

      scriptViews = transformScriptView(scriptViews);
      scriptElements = transformMaterialScriptView(scriptElements);

      scriptViews = handleScriptViewOutput(scriptViews, scriptFormValue);
      const globalField = JSON.parse(
        res.subject_content || '{}'
      ) as ResponseSubjectView;

      const scriptGlobalData = handleScriptGlobalData({
        originViews: globalField,
        info: {
          video_address: res?.info?.video_url,
          video_duration: res?.info?.video_duration,
        },
      });

      return {
        scriptViews,
        scriptElements,
        scriptGlobalData,
      } satisfies ScriptResult;
    } catch (e) {
      console.error(e, '解析数据获取失败');
      setLoading(false);
      void MessagePlugin.error('解析数据获取失败');
    } finally {
      setLoading(false);
    }
  }, [jobId, scriptFormValue]);

  const handleSubmit = async () => {
    if (requestLoading) return;
    const res = await form.validate();
    const value = form.getFieldsValue(true) as IScriptForm;
    console.debug(value, 'form-value');
    if (res !== true) return;
    if (value.domain && value.domain !== '_') {
      const [first] = value.domain.split('_');
      if (!first) {
        void message.error('请选择领域行业');
        return;
      }
    }
    const { formValue, inputType } = handleScriptFormInput(value);
    if (uid) {
      SessionStorageUtil.setItem(`script_${uid}`, formValue);
    }
    setScriptFormValue(formValue);
    console.log(formValue, 'formValue');
    await startCreateScript(formValue, inputType);
  };

  useImperativeHandle(ref, () => ({
    async submit() {
      return await handleSubmit();
    },
  }));

  const startCreateScript = async (
    value: IScriptForm,
    inputType: ScriptFormInputType
  ) => {
    setLoading(true);

    try {
      const createJobRes = await createScriptJob({
        formValue: value,
        inputType,
      });
      if (!createJobRes) return;
      setJobId(createJobRes.job_id);
      startPolling(createJobRes.job_id);
    } catch (err) {
      console.error(err);
      setLoading(false);
      const dialog = DialogPlugin.alert({
        header: (
          <>
            <CloseCircleFilledIcon style={{ color: 'rgb(227, 77, 89)' }} />
            <span>提示</span>
          </>
        ),
        body: '生成脚本失败，请重试!',
        onClose: () => dialog.hide(),
        onConfirm: () => dialog.hide(),
      });
    }
  };

  return (
    <>
      <Form
        className="script-form-comp"
        form={form}
        // initialData={initialData}
        labelWidth={97}
        labelAlign="left"
      >
        {requestLoading && (
          <MainContent className="question-wrap">
            <div className="flex items-center justify-center">
              <Loading loading text="表单配置加载中..." size="small" />
            </div>
          </MainContent>
        )}
        {/* 脚本类型*/}
        {!!scriptTypeOptions.length && (
          <MainContent className="question-wrap">
            <div className="question-wrap-title">脚本类型</div>
            <FormItem label="类型" name="type">
              <ScriptTypeWrap
                options={scriptTypeOptions}
                onSelectCb={(value) => {
                  const domainOpt = queryDomainOptions(value);
                  const first = domainOpt[0]?.value || '';
                  const second = domainOpt[0]?.children?.[0] || '';
                  const domain = `${first}_${second}`;
                  form.setFieldsValue({
                    domain,
                    mainPoint: [],
                  });
                  updateMainPoint(value, domain);
                }}
              />
            </FormItem>
          </MainContent>
        )}
        <MainContent className="question-wrap">
          <div className="question-wrap-title">脚本主题填写</div>
          <FormItem
            label="行业类目"
            name="domain"
            style={{ display: !!domainOptions.length ? 'block' : 'none' }}
          >
            <CategoryWrap
              options={domainOptions}
              onSelectCb={(value) => {
                const key = form.getFieldValue('type') || '';
                form.setFieldsValue({
                  mainPoint: [],
                });
                updateMainPoint(key as string, value);
              }}
            />
          </FormItem>

          <FormItem
            label="脚本侧重点"
            name="mainPoint"
            style={{ display: !!mainPointOptions.length ? 'block' : 'none' }}
          >
            <MainPointWrap options={mainPointOptions} />
          </FormItem>

          <FormItem label="脚本时长(s)" name="time">
            <InputNumber style={{ width: 250 }} min={1} max={60 * 60} />
          </FormItem>
        </MainContent>
        {/* 电商扩展表单*/}
        {scenarios === CONTENT_TYPE_MAP.Video.children.ECommerce.value && (
          <ECommerceForm />
        )}
        {/* 游戏扩展表单*/}
        {scenarios === CONTENT_TYPE_MAP.Video.children.Vision.value && (
          <GamePromotionForm form={form} />
        )}
        {/*/ !* PPT扩展表单*/}
        {scenarios === CONTENT_TYPE_MAP.Video.children.Course.value && (
          <PPTForm />
        )}
        {/*/ !* DOC扩展表单**/}
        {scenarios === CONTENT_TYPE_MAP.Video.children.Article.value && (
          <DocForm />
        )}
        {/*/ !* 内容直播扩展表单*!/*/}
        {scenarios === CONTENT_TYPE_MAP.Live.children.Live.value && (
          <ContentForm />
        )}
      </Form>
      {loading && (
        <CommonScriptLoading
          attachBody
          stepName={stepName}
          percent={percent}
          header="智能生成"
          onCancel={() => {
            setLoading(false);
            stopPolling();
          }}
        />
      )}
    </>
  );
});
