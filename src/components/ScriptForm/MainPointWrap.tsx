/**
 * <AUTHOR>
 * @date 2024/8/15 下午5:07
 * @desc MainPointWrap
 */

import React from 'react';
import { InfoCircleIcon } from 'tdesign-icons-react';
import { TypeSelector } from '@/components/ScriptForm/TypeSelector';

interface IProps {
  options: string[];
  value?: string[];
  onChange?: (value: string[]) => void;
}

export function MainPointWrap(props: IProps) {
  const { value = [], onChange, options } = props;
  return (
    <div>
      <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
        <InfoCircleIcon className="mr-8" />
        <span>请按照期望的顺序进行选择，将依据选中顺序生成脚本环节</span>
      </div>
      <div className="flex flex-wrap gap-[8px]">
        {options.map((item) => {
          const findIndex = value?.findIndex((i) => i === item);
          return (
            <TypeSelector
              active={findIndex > -1}
              activeIcon={
                <div className="w-[16px] h-[16px] text-[12px] text-[rgba(255,255,255,0.90)] flex items-center justify-center rounded-[50%] bg-[linear-gradient(89deg,#0153FF_-0.01%,#8649FF_147.74%)]">
                  {findIndex + 1}
                </div>
              }
              key={item}
              value={item}
              multiple
              onClick={(v) => {
                if (value.includes(v)) {
                  onChange?.(value?.filter((i: string) => i !== v));
                } else {
                  onChange?.(value?.concat(item));
                }
              }}
            />
          );
        })}
      </div>
    </div>
  );
}
