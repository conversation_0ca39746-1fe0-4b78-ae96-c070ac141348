/**
 * <AUTHOR>
 * @date 2024/8/29 上午11:32
 * @desc mock
 */
import { useRequest } from 'ahooks';
import { ResourceSvr } from '@/pb/pb';

type Domain = {
  mainPoint: string[];
  children?: {
    [domain: string]: Domain;
  };
};

export interface ScriptFormConf {
  [scriptType: string]: {
    [domain: string]: Domain;
  };
}

export const MOCK_DATA: ScriptFormConf = {
  广告投流: {
    // 一级行业类目
    游戏: {
      mainPoint: [],
      children: {
        小游戏: {
          mainPoint: [
            '游戏玩法介绍',
            '游戏亮点特色',
            '游戏画面制作',
            '游戏难易度',
            '引导微信搜索',
          ],
        },
        手游: {
          mainPoint: [
            '游戏玩法介绍',
            '游戏亮点特色',
            '游戏画面制作',
            '游戏难易度',
            '引导用户下载',
          ],
        },
      },
    },
  },
  高光集锦: {
    电商: {
      mainPoint: [
        '商品介绍',
        '直播互动',
        '销售高潮',
        '用户反馈',
        '商品演示',
        '品牌故事',
      ],
    },
    游戏: {
      mainPoint: [
        '精彩对战场面',
        '特殊技能道具',
        '玩家角色成长',
        '趣味搞笑时刻',
        '玩家社交互动',
        '出色成绩表现',
      ],
    },
  },
  资讯播报: {
    游戏: {
      mainPoint: ['快速吸引观众', '资讯内容展开', '观众互动呼吁'],
    },
  },
};

export const getScriptCategory = (conf: ScriptFormConf) => {
  return Object.keys(conf);
};

export const getDomainCategory = (conf: Record<string, Domain>) => {
  return Object.keys(conf ?? {}).map((item) => {
    return {
      value: item,
      children: Object.keys(conf[item].children ?? {}),
    };
  });
};

export const getMainPointList = (
  conf: Record<string, Domain>,
  value: string
) => {
  if (!conf || !value) return [];
  let mainPoint: string[] = [];
  const [first, second] = value.split('_');

  if (first) {
    mainPoint = conf[first].mainPoint || [];
    if (second) {
      mainPoint = conf[first].children?.[second]?.mainPoint || [];
    }

    return mainPoint;
  }

  return [];
};

export const useQueryFormConf = () => {
  const { runAsync, loading, error } = useRequest(
    (type: string, scenarios: string) => {
      return ResourceSvr.GetScriptCategoryConfig({
        category_id: type,
        script_type: scenarios,
      });
    },
    {
      manual: true,
    }
  );

  return { runAsync, loading, error };
};
