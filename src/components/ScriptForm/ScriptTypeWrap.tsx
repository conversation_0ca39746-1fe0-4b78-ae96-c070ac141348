/**
 * <AUTHOR>
 * @date 2024/8/15 下午4:40
 * @desc ScriptType
 */

import React from 'react';
import { TypeSelector } from '@/components/ScriptForm/TypeSelector';
import { CheckIcon } from 'tdesign-icons-react';

interface IProps {
  options: string[];
  value?: string;
  onChange?: (value: string) => void;
  onSelectCb?: (value: string) => void;
}

export function ScriptTypeWrap(props: IProps) {
  const { options, value, onChange, onSelectCb } = props;

  return (
    <div className="flex gap-[8px]">
      {options?.map((item) => {
        return (
          <TypeSelector
            active={value === item}
            activeIcon={<CheckIcon />}
            key={item}
            value={item}
            onClick={() => {
              onChange?.(item);
              onSelectCb?.(item);
            }}
          />
        );
      })}
    </div>
  );
}
