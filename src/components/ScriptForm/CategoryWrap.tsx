/**
 * <AUTHOR>
 * @date 2024/8/15 下午5:01
 * @desc CategoryWrap
 */

import React, { useMemo } from 'react';
import { TypeSelector } from '@/components/ScriptForm/TypeSelector';
import { CheckIcon } from 'tdesign-icons-react';

interface IProps {
  options: {
    value: string;
    children: string[];
  }[];
  value?: string;
  onChange?: (value: string) => void;
  onSelectCb?: (value: string) => void;
}

export function CategoryWrap(props: IProps) {
  const { value, onChange, options, onSelectCb } = props;

  const { first, second } = useMemo(() => {
    const [first, second] = (value ?? '')?.split('_');
    return {
      first: first || '',
      second: second || '',
    };
  }, [value]);

  const childrenOptions = useMemo(() => {
    return (
      options.find((i) => i.value === first)?.children.filter(Boolean) || []
    );
  }, [first, options]);

  return (
    <div className="w-full">
      <div className="flex gap-[8px]">
        {options.map((item) => {
          return (
            <TypeSelector
              active={first === item.value}
              activeIcon={<CheckIcon />}
              key={item.value}
              value={item.value}
              onClick={(v) => {
                if (first !== v) {
                  const val = `${v}_`;
                  onChange?.(val);
                  onSelectCb?.(val);
                }
              }}
            />
          );
        })}
      </div>
      {!!childrenOptions.length && (
        <div className="flex bg-[#fafafa] rounded-[50px] p-[12px] mt-[12px] gap-[8px]">
          {childrenOptions.map((item) => {
            return (
              <TypeSelector
                active={second === item}
                activeIcon={<CheckIcon />}
                key={item}
                value={item}
                style={{
                  padding: '8px 18px',
                  fontSize: '12px',
                }}
                onClick={(v) => {
                  if (v !== second) {
                    const val = `${first}_${v}`;
                    onChange?.(val);
                    onSelectCb?.(val);
                  }
                }}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
