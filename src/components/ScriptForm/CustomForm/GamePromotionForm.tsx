/**
 * <AUTHOR>
 * @date 2024/8/15 下午8:03
 * @desc GamePromotionForm
 */

import React, { useState } from 'react';
import { AddIcon, MinusCircleIcon, PlusIcon } from 'tdesign-icons-react';
import { Form, Input, InternalFormInstance, Textarea } from 'tdesign-react';
import { MainContent } from '@/components/Layout';
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from 'react-beautiful-dnd';
import { uuid } from '@tencent/midas-util';
import { IScriptForm } from '@/components/ScriptForm/type';
import { cloneDeep } from 'lodash-es';
import SelectVideoMaterial, {
  ISelectVideoMaterialProps,
} from '@/pages/videoMaterialList/components/selectVideoMaterial';
import { IVideoMaterialListRecord } from '@/pages/videoMaterialList/hooks/useVideoMaterialListRequest';
import { useVideoInfo } from '@/hooks/useVideoInfo';
import { useCounter } from 'ahooks';

const { FormItem, FormList } = Form;

const FORM_KEY = 'gameProm';

interface IVideoBlockProps {
  value?: IVideoMaterialListRecord;
}

export function GamePromotionForm({ form }: { form: InternalFormInstance }) {
  const [selectVideoMaterialParams, setSelectVideoMaterialParams] =
    useState<ISelectVideoMaterialProps>();
  const [counter, { inc, dec }] = useCounter(0);
  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }
    const formValue = form.getFieldsValue(true) as IScriptForm;
    if (formValue.gameProm?.themes) {
      const newThemes = cloneDeep(formValue.gameProm.themes);
      const [movedItem] = newThemes.splice(result.source.index, 1);
      newThemes.splice(result.destination.index, 0, movedItem);

      form.setFieldsValue({
        gameProm: {
          ...formValue.gameProm,
          themes: newThemes,
        },
      });
    }
  };

  const handleSelect = (selectVal: IVideoMaterialListRecord[]) => {
    const formValue = form.getFieldsValue(true) as IScriptForm;
    const originThemes = formValue.gameProm?.themes || [];
    const addThemes = selectVal.map((item) => ({
      selectVideo: item,
      desc: item.video_content || '',
      name: item.video_name || '',
      uid: uuid(),
    }));
    form.setFieldsValue({
      gameProm: {
        ...formValue.gameProm,
        themes: [...originThemes, ...addThemes],
      },
    });
    inc();
  };
  return (
    <MainContent className="question-wrap">
      <div className="question-wrap-title">视频素材选择与信息补充</div>
      <FormList name={[FORM_KEY, 'themes']}>
        {(fields, { remove }) => (
          <>
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="uid">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {fields.map(({ key, name, ...restField }, index) => {
                      return (
                        <Draggable
                          key={key}
                          draggableId={key.toString()}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              className="rounded-[4px] bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)] mt-16"
                              key={key}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <header
                                style={{
                                  padding: 20,
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  borderBottom:
                                    '1px solid rgba(225, 225, 225, 1)',
                                }}
                              >
                                <div className="text-[18px] font-medium">
                                  视频{index + 1}
                                </div>
                                <div>
                                  <div
                                    style={{
                                      width: 16,
                                      height: 16,
                                      cursor: 'pointer',
                                    }}
                                  >
                                    <MinusCircleIcon
                                      onClick={() => {
                                        remove(index);
                                        dec();
                                      }}
                                      style={{ fontSize: 16, color: '#FBC3C4' }}
                                    />
                                  </div>
                                </div>
                              </header>
                              <div className="p-20 flex items-center">
                                {/* <div*/}
                                {/*  style={{ width: '144px', marginRight: 16 }}*/}
                                {/* >*/}
                                {/*  <FormItem*/}
                                {/*    {...restField}*/}
                                {/*    name={[name, 'video']}*/}
                                {/*    rules={[*/}
                                {/*      {*/}
                                {/*        required: true,*/}
                                {/*        message: '请上传视频',*/}
                                {/*        type: 'error',*/}
                                {/*        trigger: 'submit',*/}
                                {/*      },*/}
                                {/*    ]}*/}
                                {/*  >*/}
                                {/*    <CustomUpload*/}
                                {/*      multiple={false}*/}
                                {/*      customUploadButton={*/}
                                {/*        <div className="w-[122px] border-dashed border-[#cccccc] border-[1px] cursor-pointer h-[122px] rounded-4 overflow-hidden flex justify-center items-center flex-col gap-[4px] bg-[#F4F6FF] hover:bg-[#f2f2f4] active:bg-[#ededf1] transition-all duration-200 ease-in-out">*/}
                                {/*          <PlusIcon size={20} />*/}
                                {/*          <div>添加视频</div>*/}
                                {/*        </div>*/}
                                {/*      }*/}
                                {/*    />*/}
                                {/*  </FormItem>*/}
                                {/* </div>*/}

                                <div className="w-[144px]">
                                  <FormItem
                                    {...restField}
                                    name={[name, 'selectVideo']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请上传视频',
                                        type: 'error',
                                        trigger: 'submit',
                                      },
                                    ]}
                                  >
                                    <VideoBlock />
                                  </FormItem>
                                </div>

                                <div className="flex-1">
                                  <FormItem
                                    {...restField}
                                    label="视频名称"
                                    name={[name, 'name']}
                                    labelAlign="right"
                                    rules={[
                                      {
                                        required: true,
                                        message: '请填写视频名称',
                                        type: 'error',
                                        trigger: 'submit',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="请输入视频名称" />
                                  </FormItem>
                                  <FormItem
                                    {...restField}
                                    label="视频描述"
                                    name={[name, 'desc']}
                                    labelAlign="right"
                                  >
                                    <Textarea
                                      autosize
                                      placeholder="可填入例如：主题介绍、主题特色、主题亮点等"
                                    />
                                  </FormItem>
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      );
                    })}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </>
        )}
      </FormList>

      <div
        x-if={counter === 0}
        className="mt-16 h-[124px] w-full flex flex-col justify-center items-center cursor-pointer"
        style={{
          background: 'linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%)',
          color: 'rgba(0, 0, 0, 0.30)',
        }}
        onClick={() =>
          setSelectVideoMaterialParams({
            visible: true,
          })
        }
      >
        <AddIcon
          style={{
            color: 'rgba(0, 0, 0, 0.30)',
          }}
        />
        添加视频
      </div>

      {selectVideoMaterialParams?.visible && (
        <SelectVideoMaterial
          selectMode="radio"
          {...selectVideoMaterialParams}
          onCancel={() =>
            setSelectVideoMaterialParams({
              ...selectVideoMaterialParams,
              visible: false,
            })
          }
          onOk={(data) => {
            // console.log(data);
            handleSelect(data);
            setSelectVideoMaterialParams({
              ...selectVideoMaterialParams,
              visible: false,
            });
          }}
        />
      )}
    </MainContent>
  );
}

function VideoBlock({ value }: IVideoBlockProps) {
  const { videoInfo } = useVideoInfo({
    videoUrl: value?.video_url || null,
    options: {
      fetchBaseInfo: true,
    },
  });
  return (
    <div className="w-[122px] cursor-pointer h-[122px] rounded-4 overflow-hidden flex justify-center items-center flex-col gap-[4px] bg-[#F4F6FF] hover:bg-[#f2f2f4] active:bg-[#ededf1] transition-all duration-200 ease-in-out">
      {value ? (
        <div className="w-full h-full flex justify-center items-center bg-black">
          <img
            src={value.pic_url}
            alt=""
            x-class={{
              'w-full': videoInfo?.info.direction === 'horizontal',
              'h-full': videoInfo?.info.direction === 'vertical',
            }}
          />
        </div>
      ) : (
        <div className="border-dashed border-[#cccccc] border-[1px]">
          <PlusIcon size={20} />
          <div>添加视频</div>
        </div>
      )}
    </div>
  );
}
