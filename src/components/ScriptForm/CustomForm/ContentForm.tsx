/**
 * <AUTHOR>
 * @date 2024/8/26 下午8:55
 * @desc 电商差异化表单
 */

import React from 'react';
import { InfoCircleIcon, MinusCircleIcon } from 'tdesign-icons-react';
import { Form, Input, Textarea } from 'tdesign-react';
import { UploadCos } from '@/components/Upload';
import { uploadRequest } from '@/utils/cos';
import { AddFormItemBtn } from '@/components/ScriptForm/AddFormItemBtn';
import { MainContent } from '@/components/Layout';
import { useReport } from '@/hooks/report';

const { FormItem, FormList } = Form;

// 字段对齐
// https://iwiki.woa.com/p/4012355618

const FORM_KEY = 'content';

export function ContentForm() {
  const domAction = useReport();
  return (
    <MainContent className="question-wrap">
      <div className="question-wrap-title">上传内容素材</div>
      <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
        <InfoCircleIcon className="mr-8" />
        <span>请尽可能多地提供您对内容的带货想法和诉求</span>
      </div>
      <FormList name={[FORM_KEY, 'contentList']}>
        {(fields, { add, remove }) => (
          <>
            {fields?.map(({ key, name, ...restField }, index) => {
              return (
                <div
                  key={key}
                  className="rounded-[4px] bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)]"
                >
                  <div>
                    <div
                      style={{
                        padding: 20,
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div className="text-[18px] font-medium">
                        内容{index + 1}
                      </div>
                      <div>
                        <div
                          style={{ width: 16, height: 16, cursor: 'pointer' }}
                        >
                          <MinusCircleIcon
                            onClick={() => {
                              if (fields.length > 1) {
                                remove(index);
                              }
                            }}
                            style={{ fontSize: 16, color: '#FBC3C4' }}
                          />
                        </div>
                      </div>
                    </div>
                    <div
                      style={{
                        borderTop: '1px solid rgba(225, 225, 225, 1)',
                      }}
                    />
                    <div
                      style={{
                        lineHeight: '22px',
                        padding: 20,
                      }}
                    >
                      <FormItem
                        {...restField}
                        label="内容名称"
                        name={[name, 'content_name']}
                        rules={[
                          {
                            required: true,
                            message: '请填写商品名称',
                            type: 'error',
                            trigger: 'submit',
                          },
                        ]}
                      >
                        <Input placeholder="请输入商品名称" />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="内容图片"
                        name={[name, 'content_image']}
                      >
                        <UploadCos
                          type="image"
                          theme="image"
                          accept=".jpg,.jpeg,.png"
                          showImageFileName={false}
                          uploadRequest={uploadRequest}
                          beforeUpload={() => {
                            domAction.emit('ItemClick', {
                              module_id: 'ProductImageUpload',
                            });
                            return true;
                          }}
                          tips={
                            <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
                              <InfoCircleIcon className="mr-8" />
                              <span>
                                建议上传png格式，如不上传商品，则视频无法精准展示您的商品图片，会影响视频展示效果~{' '}
                              </span>
                            </div>
                          }
                        />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="内容详情"
                        name={[name, 'content_detail']}
                      >
                        <Textarea
                          autosize
                          placeholder="可填入例如：业务介绍、商品介绍、活动规则等"
                        />
                      </FormItem>
                    </div>
                  </div>
                </div>
              );
            })}
            <AddFormItemBtn
              text="添加内容"
              onClick={() => {
                add({
                  content_name: '',
                  content_image: '',
                  content_detail: '',
                });
              }}
            />
          </>
        )}
      </FormList>
    </MainContent>
  );
}
