/**
 * <AUTHOR>
 * @date 2024/8/26 下午6:55
 * @desc DOC差异化表单
 */

import React from 'react';
import { CheckIcon, InfoCircleIcon } from 'tdesign-icons-react';
import { Form, MessagePlugin, Textarea } from 'tdesign-react';
import { MainContent } from '@/components/Layout';
import { DraggableUpload } from '@/components/DraggableUpload';
import { TypeSelector } from '@/components/ScriptForm/TypeSelector';
import { IScriptForm } from '@/components/ScriptForm/type';

const { FormItem } = Form;
// 字段对齐
// https://iwiki.woa.com/p/4012355618

const FORM_KEY = 'doc';

export function DocForm() {
  return (
    <MainContent className="question-wrap">
      <div className="question-wrap-title">上传文章素材</div>
      <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
        <InfoCircleIcon className="mr-8" />
        <span>基于上传的文章内容，为您智能生成培训讲稿</span>
      </div>

      <FormItem name={[FORM_KEY, 'fileType']}>
        <SelectType />
      </FormItem>

      <FormItem
        shouldUpdate={(prev, next) => {
          return prev?.[FORM_KEY]?.fileType !== next?.[FORM_KEY]?.fileType;
        }}
      >
        {({ getFieldsValue }) => {
          const formVal = getFieldsValue(true) as IScriptForm;
          if (formVal?.doc?.fileType === 'link')
            return (
              <FormItem
                label=""
                name={[FORM_KEY, 'fileUrl']}
                initialData=""
                key="linkUrl"
                rules={[
                  {
                    required: true,
                    message: '图文文章链接必填',
                    type: 'warning',
                    trigger: 'submit',
                  },
                  {
                    url: {
                      protocols: ['https'],
                      require_protocol: true,
                    },
                    message: '请输入正确的https链接',
                    trigger: 'submit',
                  },
                ]}
              >
                <Textarea
                  style={{ width: '420px' }}
                  placeholder="请输入链接"
                  rows={5}
                />
              </FormItem>
            );
          return (
            <FormItem
              label=""
              name={[FORM_KEY, 'fileUrl']}
              initialData=""
              key="fileUrl"
              rules={[
                {
                  required: true,
                  message: '请上传文件',
                  type: 'warning',
                  trigger: 'submit',
                },
              ]}
            >
              <DraggableUpload
                prefix="https:"
                onFail={() => {
                  void MessagePlugin.error('上传失败,请重新上传');
                }}
                onSuccess={() => {
                  void MessagePlugin.success('上传成功');
                }}
                onFileUrlChange={(url) => {
                  console.debug(url);
                }}
                desc={
                  <div className="mt-12" style={{ fontSize: '12px' }}>
                    <div>1.格式支持：DOC、DOCX</div>
                    <div>2.基于文章内的标题格式进行分段；</div>
                    <div>3.文章内图片会智能提取放入视频中；</div>
                  </div>
                }
                accept="application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, "
              />
            </FormItem>
          );
        }}
      </FormItem>
    </MainContent>
  );
}

function SelectType(props: {
  value?: string;
  onChange?: (value: string) => void;
}) {
  const { value, onChange } = props;

  return (
    <div className="flex gap-[8px]">
      {[
        {
          name: '文章链接',
          value: 'link',
        },
        {
          name: '文章文件',
          value: 'file',
        },
      ].map((item) => {
        return (
          <TypeSelector
            active={value === item.value}
            activeIcon={<CheckIcon />}
            key={item.value}
            value={item.name}
            onClick={() => {
              if (value === item.value) return;
              onChange?.(item.value);
            }}
          />
        );
      })}
    </div>
  );
}
