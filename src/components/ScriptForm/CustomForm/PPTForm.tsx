/**
 * <AUTHOR>
 * @date 2024/8/26 下午5:55
 * @desc PPT差异化表单
 */

import React from 'react';
import { InfoCircleIcon } from 'tdesign-icons-react';
import { Form, MessagePlugin } from 'tdesign-react';
import { MainContent } from '@/components/Layout';
import { DraggableUpload } from '@/components/DraggableUpload';

const { FormItem } = Form;
// 字段对齐
// https://iwiki.woa.com/p/4012355618

const FORM_KEY = 'ppt';

export function PPTForm() {
  return (
    <MainContent className="question-wrap">
      <div className="question-wrap-title">上传PPT素材</div>
      <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
        <InfoCircleIcon className="mr-8" />
        <span>基于上传的PPT内容，为您智能生成培训讲稿</span>
      </div>

      <FormItem
        label=""
        name={[FORM_KEY, 'fileUrl']}
        initialData=""
        key="fileUrl"
        rules={[
          {
            required: true,
            message: '请上传文件',
            type: 'warning',
            trigger: 'submit',
          },
        ]}
      >
        <DraggableUpload
          prefix="https:"
          onFail={() => {
            void MessagePlugin.error('上传失败,请重新上传');
          }}
          onSuccess={() => {
            void MessagePlugin.success('上传成功');
          }}
          onFileUrlChange={(url) => {
            console.debug(url);
          }}
          desc={
            <div className="mt-12" style={{ fontSize: '12px' }}>
              <div>1.格式支持：PPT、PPTX</div>
              <div>2.每页PPT会转为图片放入视频中</div>
            </div>
          }
          accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation"
        />
      </FormItem>
    </MainContent>
  );
}
