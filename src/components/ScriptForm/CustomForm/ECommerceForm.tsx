/**
 * <AUTHOR>
 * @date 2024/8/15 下午5:55
 * @desc 电商差异化表单
 */

import React from 'react';
import { InfoCircleIcon, MinusCircleIcon } from 'tdesign-icons-react';
import { Form, Input, Textarea } from 'tdesign-react';
import { UploadCos } from '@/components/Upload';
import { uploadRequest } from '@/utils/cos';
import { AddFormItemBtn } from '@/components/ScriptForm/AddFormItemBtn';
import { MainContent } from '@/components/Layout';
import { useReport } from '@/hooks/report';

const { FormItem, FormList } = Form;

// 字段对齐
// https://iwiki.woa.com/p/4012355618

const FORM_KEY = 'eCommerce';

export function ECommerceForm() {
  const domAction = useReport();
  return (
    <MainContent className="question-wrap">
      <div className="question-wrap-title">上传商品素材</div>
      <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
        <InfoCircleIcon className="mr-8" />
        <span>将请尽可能多地提供您对商品的带货想法和诉求</span>
      </div>
      <FormList name={[FORM_KEY, 'goods']}>
        {(fields) => (
          <>
            {fields?.map(({ key, name, ...restField }, index) => {
              return (
                <div
                  key={key}
                  className="rounded-[4px] bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)]"
                >
                  <div>
                    <div
                      style={{
                        padding: 20,
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div className="text-[18px] font-medium">
                        商品{index + 1}
                      </div>
                      <div>
                        <div
                          style={{ width: 16, height: 16, cursor: 'pointer' }}
                        >
                          <MinusCircleIcon
                            style={{ fontSize: 16, color: '#FBC3C4' }}
                          />
                        </div>
                      </div>
                    </div>
                    <div
                      style={{
                        borderTop: '1px solid rgba(225, 225, 225, 1)',
                      }}
                    />
                    <div
                      style={{
                        lineHeight: '22px',
                        padding: 20,
                      }}
                    >
                      <FormItem
                        {...restField}
                        label="商品名称"
                        name={[name, 'name']}
                        rules={[
                          {
                            required: true,
                            message: '请填写商品名称',
                            type: 'error',
                            trigger: 'submit',
                          },
                        ]}
                      >
                        <Input placeholder="请输入商品名称" />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="商品图片"
                        name={[name, 'img']}
                      >
                        <UploadCos
                          type="image"
                          theme="image"
                          accept=".jpg,.jpeg,.png"
                          showImageFileName={false}
                          uploadRequest={uploadRequest}
                          beforeUpload={() => {
                            domAction.emit('ItemClick', {
                              module_id: 'ProductImageUpload',
                            });
                            return true;
                          }}
                          tips={
                            <div className="flex items-center text-[rgba(0,0,0,0.40)] mb-[16px]">
                              <InfoCircleIcon className="mr-8" />
                              <span>
                                建议上传png格式，如不上传商品，则视频无法精准展示您的商品图片，会影响视频展示效果~{' '}
                              </span>
                            </div>
                          }
                        />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="商品卖点"
                        name={[name, 'seller']}
                      >
                        <Textarea
                          autosize
                          placeholder="可填入例如：产品成分、产品功效、适用人群等商品卖点"
                        />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="价格福利"
                        name={[name, 'reward']}
                      >
                        <Textarea
                          autosize
                          placeholder="可填入例如：买赠、满减、直降等商品价格优惠信息"
                        />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="服务保障"
                        name={[name, 'serve']}
                      >
                        <Textarea
                          autosize
                          placeholder="可填入例如：售后保障、源头保障、品牌保证等服务保障信息"
                        />
                      </FormItem>
                      <FormItem
                        {...restField}
                        label="节庆活动"
                        name={[name, 'activity']}
                      >
                        <Textarea
                          autosize
                          placeholder="可填入例如：平台大促、商家店庆、特殊节庆等周期性主题促销应用场景"
                        />
                      </FormItem>
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        )}
      </FormList>
      <AddFormItemBtn text="添加商品" tagText="多商品脚本即将开放" disabled />
    </MainContent>
  );
}
