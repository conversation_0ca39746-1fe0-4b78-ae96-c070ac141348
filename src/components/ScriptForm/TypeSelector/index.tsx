/**
 * <AUTHOR>
 * @date 2024/8/15 下午4:21
 * @desc TypeSelector
 */

import React, { CSSProperties, ReactNode } from 'react';
import './index.less';

interface IProps {
  active: boolean;
  value: string;
  onClick: (v: string) => void;
  activeIcon?: ReactNode;
  multiple?: boolean;
  style?: CSSProperties;
  className?: string;
}

export function TypeSelector(props: IProps) {
  const { active, value, onClick, activeIcon, multiple, style, className } =
    props;
  return (
    <div
      className={`${className || ''} type-select-comp__wrap ${
        // eslint-disable-next-line no-nested-ternary
        active ? (multiple ? 'active angle' : 'active') : ''
      }`}
      onClick={() => {
        onClick(value);
      }}
      style={style}
    >
      <div className="flex items-center">
        {active && !!activeIcon && (
          <div className="flex mr-[4px]">{activeIcon}</div>
        )}
        <span>{value}</span>
      </div>
    </div>
  );
}
