/**
 * <AUTHOR>
 * @date 2024/8/24 下午4:14
 * @desc useCategoryInfo
 */
import { useRequest } from 'ahooks';
import { ResourceSvr } from '@/pb/pb';
import { useCallback, useState } from 'react';
import { IVideoCreateMode } from '@/pages/WorkbenchVideo/components/create/utils';

const scenariosMap = {
  [IVideoCreateMode.VIDEO]: 'gamePromotion',
  [IVideoCreateMode.PRODUCT]: 'eCommerce',
  [IVideoCreateMode.PPT]: 'ppt',
  [IVideoCreateMode.ARTICLE]: 'article',
  [IVideoCreateMode.CONTENT]: 'content',
};

export const useCategoryInfo = (contentType: string, scenarios: string) => {
  const [mainPointOptions, setMainPointOptions] = useState<string[]>([]);
  const [domainOptions, setDomainOptions] = useState<
    {
      value: string;
      children: string[];
    }[]
  >([]);
  const { runAsync, loading, error } = useRequest(
    (categoryType: string) => {
      return ResourceSvr.GetCategoryInfoList({
        category_type: categoryType,
      });
    },
    {
      manual: true,
    }
  );

  const queryFormConf = useCallback(
    async (scriptType: string) => {
      const selectScript = scenariosMap[scenarios as IVideoCreateMode];
      const domainRes = await runAsync(
        `script_${contentType}_${scriptType}_${selectScript}_domain`
      );
      const mainPointRes = await runAsync(
        `script_${contentType}_${scriptType}_${selectScript}_mainPoint`
      );

      const mainPoint = mainPointRes.category_info_list
        .filter((i) => !!i.status)
        .map((item) => item.category_level1_name);

      const list = domainRes.category_info_list.filter((i) => !!i.status);
      const options: typeof domainOptions = [];
      list.forEach((category) => {
        const find = options.find(
          (i) => i.value === category.category_level1_name
        );
        // 如果一级没有，则写入一级分类
        if (!find) {
          options.push({
            value: category.category_level1_name,
            children: [category.category_level2_name],
          });
        } else {
          const secondFind = find.children.find(
            (i) => i === category.category_level2_name
          );
          if (!secondFind) {
            find.children.push(category.category_level2_name);
          }
        }
      });

      setMainPointOptions(mainPoint);
      setDomainOptions(options);
    },
    [contentType, runAsync, scenarios]
  );

  return {
    mainPointOptions,
    domainOptions,
    queryFormConf,
    loading,
    error,
  };
};
