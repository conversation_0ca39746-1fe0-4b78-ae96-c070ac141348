/**
 * <AUTHOR>
 * @date 2024/8/22 下午7:42
 * @desc 脚本类型，修改务必提CR
 */
import { LiveEditorOrigin } from '@/pages/Editor/common/openEditor';
import { VideoInfo } from '@/pages/ScriptList/hooks/useVisionScriptInfo';
import { IVideoMaterialListRecord } from '@/pages/videoMaterialList/hooks/useVideoMaterialListRequest';
import type { LineItem, resourceItem, Script } from '@/type/pagedoo';
import {
  PagedooMultiImage,
  PagedooSingleImage,
} from '@tencent/pagedoo-library';

// 后端返回的结构
export type ResponseScriptView = {
  画面类型: string;
  画面名称: string;
  // 画面大纲
  画面内容: string;
  // 画面正文
  画面正文: string;
  时长: string;
  台词文案: string;
  // 花字
  文本贴纸: string;
  // 是否是互动画面
  互动画面: '是' | '否';
  // 背景
  background?: string;
  //   配图
  imageList?: PagedooMultiImage;
  // 资源
  resources?: Record<string, Array<resourceItem>>;
};

export type ResponseMaterialScriptView = {
  中文名: string;
  画面大纲: string;
  出现时刻: string; // 单位是s
  资源id: string;
  资源地址: string;
  资源类型: string;
  画面时长: string;
  文本贴纸?: string;
  resourceList: Record<string, Array<resourceItem>>;
};

export type ResponseSubjectView = {
  剧本大标题: string;
  剧本小标题: string;
};

export type IGlobalResourceList = {
  /**
   * 资源类型
   */
  resourceType: string;
  /**
   * resourceKey
   * - featured-sound-effects
   * - ...
   */
  resourceKey: string;
  /**
   * 出现时间
   */
  showTime: string;
  /**
   * 资源
   */
  resource: resourceItem | { flowerText?: string };
};

export type ScriptGlobalData = {
  title: string;
  subTitle: string;
  info?: VideoInfo;
  globalResourceList?: IGlobalResourceList[];
};

// 转换后的结构
export type ScriptView = {
  // 画面类型
  viewType: string;
  // 画面名称
  viewName: string;
  // 画面内容
  viewContent: string;
  // 画面正文
  viewMainText: string;
  // 时长
  duration: string;
  // 台词文案
  speech: string;
  // 花字
  flowerText: string;
  // 是否是互动画面
  isInteractive: '是' | '否';
  // 背景
  background?: string;
  // 画面类型目前只有讲师有
  //   配图
  imageList?: PagedooMultiImage;
  // 资源
  resources?: Record<string, Array<resourceItem>>;
};

export type MaterialView = {
  name: string;
  content: string;
  showTime: string;
  viewTime: string;
  resourceId: string;
  resourceUrl: string;
  resourceType: string;
  resourceList: Record<string, Array<resourceItem>>;
  flowerText?: string;
};

export type BaseScript = {
  // 版本
  version?: string;
  // 全局背景图片
  backgroundImage: PagedooSingleImage;
  // 全局背景视频
  /**
   * @deprecated
   */
  backgroundVideo?: string;
  // 总时长
  /**
   * @deprecated
   */
  totalDuration?: number;
  // 全局话术文本
  globalSpeech?: string;
  // 全局配置
  globalField?: ScriptGlobalData;
  // 画面
  views: ScriptView[];
  /**
   * 请勿增加必填字段 增加的字段请使用可选字段
   */
  type: LiveEditorOrigin;
  // 分辨率
  size: [width: number, height: number];
  // 广告的扩展数据 type为ad_script时候有
  adExtendData?: Script['adExtendData'];
  // 游戏推广的扩展数据 type为game_script时候有
  gamePromotionExtendData?: {
    // 台词列表
    lineList?: LineItem[];
  };
};

export type ScriptFormInputType =
  | 'eCommerce'
  | 'gameProm'
  | 'pptx'
  | 'doc'
  | 'content'
  | 'html';

export interface IScriptForm {
  // 脚本类型
  type: string;
  // 领域行业
  domain?: string;
  // 脚本侧重点
  mainPoint?: string[];
  // 脚本时长 分钟
  time: number;
  eCommerce?: {
    // 商品列表
    goods: {
      // 商品图
      img: PagedooSingleImage;
      // 商品名称
      name: string;
      // 商品卖点
      seller: string;
      // 福利
      reward: string;
      // 服务保障
      serve: string;
      // 节庆活动
      activity: string;
      // 投喂的商品知识
      knowledge: string;
    }[];
  };
  gameProm?: {
    themes: {
      uid: string;
      // 名称
      name: string;
      // 描述
      desc: string;
      // 视频地址
      // video: PagedooSingleImage;
      selectVideo: IVideoMaterialListRecord;
    }[];
    // // 视频地址
    // video: PagedooSingleImage;
  };
  doc?: {
    fileType: string;
    fileUrl: string;
  };
  ppt?: {
    fileUrl: string;
  };
  content?: {
    contentList: {
      content_name: string;
      content_image?: PagedooSingleImage;
      content_detail?: string;
    }[];
  };
}
