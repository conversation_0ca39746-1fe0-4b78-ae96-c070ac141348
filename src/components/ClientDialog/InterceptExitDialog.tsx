/**
 * <AUTHOR>
 * @date 2024/11/13 21:19
 * @desc InterceptExitDialog
 */

import React, { useState } from 'react';
import { Dialog } from 'tdesign-react';
import { CommonDialog } from '@/components/CommonDialog';
import Tips from '@/assets/images/warningTips.png';
import {
  AVATAR_CLIENT_CLOSE,
  useListenClientClose,
} from '@/components/ClientDialog/useListenClientClose';
import { MetaLiveStop } from '@/pb/api/MetaLiveSvr';

export function InterceptExitDialog() {
  const [visible, setVisible] = useState(false);
  useListenClientClose(() => {
    setVisible(true);
  });
  return (
    <>
      <Dialog
        header="关闭提示"
        visible={visible}
        footer={null}
        closeOnEscKeydown={false}
        closeOnOverlayClick={false}
        width={700}
        onClose={() => {
          setVisible(false);
        }}
      >
        <CommonDialog
          theme="success"
          icon={
            <img
              style={{ height: '113px', width: '138px', marginTop: '24px' }}
              src={Tips}
              alt=""
            />
          }
          title="当前客户端有直播间正在推流中，若直接关闭客户端将会导致当前直播流异常切断，请确认是否关闭客户端?"
          onConfirm={() => {
            setVisible(false);
          }}
          onCancel={async () => {
            setVisible(false);
            window.avatarClientBridge?.removeCloseHandler(AVATAR_CLIENT_CLOSE);
            const list =
              (await window.avatarClientBridge?.pushTask.listPushTask()) || [];
            const { liveId } = JSON.parse(list[0].config.extra || '{}');
            if (liveId) {
              await MetaLiveStop({ meta_live_id: liveId });
            }
            window.close();
          }}
          confirmText="取消关闭"
          cancelText="确认关闭"
        />
      </Dialog>
    </>
  );
}
