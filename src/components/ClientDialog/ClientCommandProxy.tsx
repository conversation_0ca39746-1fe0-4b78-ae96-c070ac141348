import {
  ClientCommand,
  ClientCommandTypes,
  useClientCommandChannel,
} from './useClientCommandChannel';
import { useMemoizedFn, useMount } from 'ahooks';
import { useEffect, useMemo, useRef } from 'react';
import { restartLive } from './utils';
import { useRecoilValue } from 'recoil';
import { ClientLive } from '@/model/clientLive';

// 客户端指令通道代理
export function ClientCommandProxy() {
  const { subscribeCommand } = useClientCommandChannel();
  const pushTaskConfig = useRecoilValue(ClientLive);
  (window as any).__restartLive = restartLive;

  const restartEventsLock = useRef<Record<string, boolean>>({});

  const handleRestart = useMemoizedFn((command: ClientCommand<'restart'>) => {
    if (restartEventsLock.current[command.liveId]) {
      // 已经在重启中的liveId 不继续调用
      command.ack();
      return;
    }
    restartEventsLock.current[command.liveId] = true;
    console.log(
      `[ClientCommand][restartLive] liveId: ${command.liveId} before call restart`
    );
    restartLive({
      liveId: command.liveId,
    })
      .then(() => command.ack())
      .catch((err: Error) => {
        command.ack({ retry: true });
        console.error(
          `[ClientCommand][restartLive] liveId: ${command.liveId} restart failed: `,
          err?.message || ''
        );
      })
      .finally(() => {
        delete restartEventsLock.current[command.liveId];
      });
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const mapping = useMemo<
    Record<
      ClientCommandTypes,
      (cmd: ClientCommand<ClientCommandTypes>) => void | Promise<void>
    >
  >(() => {
    return {
      restart: handleRestart,
    };
  }, [handleRestart]);

  useEffect(() => {
    if (pushTaskConfig.length === 0) return;
    const unsubscribe = subscribeCommand((command) => {
      if (mapping[command.type]) mapping[command.type](command);
    });
    return unsubscribe;
  }, [mapping, pushTaskConfig.length, subscribeCommand]);

  return null;
}
