/**
 * <AUTHOR>
 * @date 2024/11/9 15:15
 * @desc useClientLiveCheck
 */
import { ClientLive } from '@/model/clientLive';
import { LocalLiveHeartbeat } from '@/pb/api/MetaLiveSvr';
import { hasRunningLive, runningInClient } from '@/utils/electron';
import { cloneDeep } from 'lodash-es';
import { useEffect } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { RespError } from '@/pb/config';
import { LoginStateAtom } from '@/model/login';
// import { hasRunningLive } from '@/utils/electron';

export const useClientLiveCheck = () => {
  const [pushTaskConfig, setPushTaskConfig] = useRecoilState(ClientLive);
  const loginState = useRecoilValue(LoginStateAtom);

  // 初始化客户端直播列表
  useEffect(() => {
    const initClientTask = async () => {
      if (!runningInClient) return;
      const list =
        (await window.avatarClientBridge?.pushTask.listPushTask()) || [];
      if (list.length) {
        setPushTaskConfig(cloneDeep(list));
      }
    };
    void initClientTask();
  }, [setPushTaskConfig]);

  useEffect(() => {
    // if (!loginState) return;
    let timer: NodeJS.Timeout;
    const fn = async () => {
      // 心跳检测
      if (pushTaskConfig.length) {
        const extraData = JSON.parse(pushTaskConfig[0].config.extra || '{}');
        timer = setInterval(async () => {
          const liveHasRunning = await hasRunningLive();
          if (!liveHasRunning) {
            clearInterval(timer);
            setPushTaskConfig([]);
            return;
          }
          LocalLiveHeartbeat({
            meta_live_id: extraData.liveId,
          }).catch((err) => {
            if (err instanceof RespError && err?.resultCode === '1122035') {
              console.error(
                err,
                `客户端心跳返回失效，准备停播${extraData.liveId}`
              );
              clearInterval(timer);
              const firstItem = pushTaskConfig[0];
              if (firstItem) {
                window.avatarClientBridge?.pushTask
                  .stopPushTask(firstItem.id)
                  .then(() => {
                    setPushTaskConfig((prev) =>
                      prev.filter((item) => item.id !== firstItem.id)
                    );
                  })
                  .catch();
              }
            }
          });
        }, 10000);
      } else {
        clearInterval(timer);
      }
    };
    void fn();
    return () => {
      clearInterval(timer);
    };
  }, [loginState, pushTaskConfig, setPushTaskConfig]);
};
