import { runningInClient } from '@/utils/electron';
import { useEffect } from 'react';

export const EditorWindowCloseEvent = '__close_editor_event__';
export const useEditorWindowClose = () => {
  useEffect(() => {
    if (!runningInClient) return;
    const handleStorageEvent = (ev: StorageEvent) => {
      if (ev.key === EditorWindowCloseEvent) {
        localStorage.removeItem(EditorWindowCloseEvent);
        location.reload();
      }
    };
    window.addEventListener('storage', handleStorageEvent);
    return () => {
      window.removeEventListener('storage', handleStorageEvent);
    };
  }, []);
};
