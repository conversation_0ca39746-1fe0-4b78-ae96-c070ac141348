/**
 * <AUTHOR>
 * @date 2024/10/29 15:57
 * @desc useCheckClientVersion
 */
import { GetConfig } from '@/pb/api/ClientSvr';
import { runningInClient } from '@/utils/electron';
import { useMemoizedFn } from 'ahooks';
import { useEffect, useState } from 'react';
import { lt } from 'semver';

export const useCheckClientVersion = (
  cb: (
    downLoadUrl: string,
    options?: {
      targetVersion?: string;
      fromInstaller?: boolean;
    }
  ) => void,
  manual = true
) => {
  const memoCb = useMemoizedFn(cb);

  const [needDownloadInstaller, setNeedDownloadInstaller] = useState(false);

  const checkNotInstaller = useMemoizedFn(async () => {
    if (
      typeof window.avatarClientBridge?.updateAppFromInstaller !== 'function' ||
      (await window.avatarClientBridge?.getAppPackageInfo?.())?.portable
    ) {
      setNeedDownloadInstaller(true);
      return true;
    }
    return false;
  });
  const fetchRemoteVersion = useMemoizedFn(async () => {
    if (!runningInClient) return;
    // 过渡升级，如果是旧客户端，需提提示下载安装包版本
    const old = await checkNotInstaller();
    const currentVersion =
      (await window.avatarClientBridge?.getVersion()) || '0.0.0';
    try {
      const res = await GetConfig({
        config_id: old ? 'version' : 'installer_version',
      });
      if (!res) return;
      const config = JSON.parse(res.config_data || '{}');
      const remoteVersion = config.version || '0.0.0';
      if (lt(currentVersion, remoteVersion)) {
        console.log('当前版本', currentVersion, '最新版本', remoteVersion);
        memoCb(config.download_url as string, {
          fromInstaller: !old,
          targetVersion: remoteVersion,
        });
        return config.download_url;
      }
    } catch (e) {
      console.error(e, '版本查询失败');
    }
  });
  useEffect(() => {
    if (!manual) return;
    fetchRemoteVersion().then();
  }, [fetchRemoteVersion, manual, memoCb]);

  return {
    needDownloadInstaller,
    fetchRemoteVersion,
  };
};
