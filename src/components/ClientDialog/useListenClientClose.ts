/**
 * <AUTHOR>
 * @date 2024/10/29 15:57
 * @desc useListenClientClose
 */
import { useEffect } from 'react';
import { hasRunningLive, runningInClient } from '@/utils/electron';
import { useMemoizedFn } from 'ahooks';

export const AVATAR_CLIENT_CLOSE = '__avatar_client_close';

export const useListenClientClose = (cb: () => void) => {
  const memoCb = useMemoizedFn(cb);

  useEffect(() => {
    if (!runningInClient) return;

    window.avatarClientBridge?.addCloseHandler(AVATAR_CLIENT_CLOSE);

    window[AVATAR_CLIENT_CLOSE] = function () {
      hasRunningLive().then((liveHasRunning) => {
        if (liveHasRunning) {
          memoCb();
        } else {
          window.avatarClientBridge?.removeCloseHandler(AVATAR_CLIENT_CLOSE);
          window.close();
        }
      });
    };

    return () => {
      window.avatarClientBridge?.removeCloseHandler(AVATAR_CLIENT_CLOSE);
    };
  }, [memoCb]);

  //   监听onBeforeUnload事件，关闭窗口时触发
  useEffect(() => {
    if (!runningInClient) return;

    window.addEventListener('beforeunload', function () {
      window.avatarClientBridge?.removeCloseHandler(AVATAR_CLIENT_CLOSE);
    });
  }, []);
};
