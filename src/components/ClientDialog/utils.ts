/**
 * <AUTHOR>
 * @date 2025/1/19 11:23
 */
import { ClientLive } from '@/model/clientLive';
import { ClientPushConfigExtraType } from '@/type/client';
import { runningInClient } from '@/utils/electron';
import { PushTaskConfig } from '@tencent/avatar-client-bridge-type/es';
import to from 'await-to-js';
import { enc, SHA256 } from 'crypto-js';
import { setRecoil } from 'recoil-nexus';

// 检查驱动安装情况
export const checkDriver = async () => {
  if (!runningInClient)
    return {
      audio: false,
      camera: false,
    };
  const checkRes = await window.avatarClientBridge?.pushTask.checkDriver();
  if (checkRes) {
    return {
      audio: checkRes.result !== '3' && checkRes.result !== '2',
      camera: checkRes.result !== '3' && checkRes.result !== '1',
    };
  }
};

// 重启直播
export const restartLive = async (options: { liveId: string }) => {
  const { liveId } = options;
  if (!runningInClient) return;
  if (!window.avatarClientBridge?.pushTask) return;
  const [err, result] = await to(
    window.avatarClientBridge.pushTask.listPushTask()
  );
  if (err) throw new Error(`获取直播任务列表失败`);
  const task = result?.find((item) => {
    const { config } = item;
    const { extra } = config;
    const extraObj = JSON.parse(extra || '{}');
    return extraObj.liveId === liveId;
  });
  if (!task) throw new Error(`未找到直播任务`);
  const { config } = task;
  const [stopErr] = await to(
    window.avatarClientBridge.pushTask.stopPushTask(task.id)
  );
  if (stopErr) throw new Error(`停止直播任务失败`);
  setRecoil(ClientLive, (prev) => {
    const index = prev.findIndex((item) => item.id === task.id);
    if (index > -1) {
      return [...prev.slice(0, index), ...prev.slice(index + 1)];
    }
    return prev;
  });
  const [startErr, newTask] = await to(
    window.avatarClientBridge.pushTask.startPushTask(config)
  );
  if (startErr) throw new Error(`启动直播任务失败`);
  setRecoil(ClientLive, (prev) => {
    return [...prev, newTask];
  });
};
/**
 * 根据clientSid 生成客户端 专属live_session_id
 */
export const getClientLiveSessionId = async (clientSid: string) => {
  return SHA256(clientSid).toString(enc.Hex);
};

export const parsePushTaskExtra = (
  config: PushTaskConfig
): ClientPushConfigExtraType | null => {
  try {
    return JSON.parse(config.extra);
  } catch {}
  return null;
};
