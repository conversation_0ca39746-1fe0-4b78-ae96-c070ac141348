/**
 * <AUTHOR>
 * @date 2024/11/11 15:47
 * @desc UpdateClientDialog
 */

import PrimaryTips from '@/assets/images/primaryTips.png';
import { useCheckClientVersion } from '@/components/ClientDialog/useCheckClientVersion';
import { CommonDialog } from '@/components/CommonDialog';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { downloadZipAsUint8Array } from '@/utils';
import { useMemoizedFn } from 'ahooks';
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Dialog, MessagePlugin, TdDialogProps } from 'tdesign-react';

type UpdateStep = '' | 'unzip' | 'unzipError' | 'prepareInstall';

export function UpdateClientDialog(props?: TdDialogProps) {
  const [downloadInfo, setDownloadInfo] = useState<{
    downloadUrl: string;
    version?: string;
    fromInstaller?: boolean;
  }>();
  const [percent, setPercent] = useState<number | null>(null);

  const [updateStep, setUpdateStep] = useState<UpdateStep>('');
  const [
    showDownloadInstallerDialogFrame,
    setShowDownloadInstallerDialogFrame,
  ] = useState(false);

  const { needDownloadInstaller } = useCheckClientVersion(
    (downloadUrl, options) => {
      setDownloadInfo({
        downloadUrl,
        version: options?.targetVersion,
        fromInstaller: options?.fromInstaller,
      });
    }
  );

  const buttonText = useMemo(() => {
    if (!percent) return '更新版本';
    if (updateStep === 'unzip') return '解压中...';
    if (updateStep === 'unzipError') return '安装包解压失败';
    if (updateStep === 'prepareInstall') return '准备安装...';
    return Number.isFinite(percent) ? `版本更新中${percent}%` : '版本更新中';
  }, [percent, updateStep]);

  const handleUpdateFromInstaller = useMemoizedFn(async () => {
    try {
      await window.avatarClientBridge?.updateAppFromInstaller?.({
        zipDownloadUrl: downloadInfo!.downloadUrl,
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        version: downloadInfo!.version!,
      });
    } catch (e) {
      console.error(e, '更新失败');
      void MessagePlugin.error('更新失败');
    } finally {
    }
  });
  const handleUpdate = async () => {
    if (!downloadInfo) return;
    if (downloadInfo.fromInstaller) {
      return handleUpdateFromInstaller();
    }
    try {
      const res = await downloadZipAsUint8Array(
        downloadInfo?.downloadUrl,
        async (percent) => {
          setPercent(percent);
        }
      );
      await window.avatarClientBridge?.updateApp(res);
      setPercent(null);
    } catch (e) {
      console.error(e, '更新失败');
      void MessagePlugin.error('更新失败');
    }
  };

  useEffect(() => {
    if (!downloadInfo?.fromInstaller) return;
    const unsubscribes = [
      window.avatarClientBridge?.subscribeEvents?.(
        'updater:updateProgress',
        (_ev, { percent }) => {
          setPercent(percent);
        }
      ),
      window.avatarClientBridge?.subscribeEvents?.('updater:unzipStart', () => {
        setUpdateStep('unzip');
      }),
      window.avatarClientBridge?.subscribeEvents?.(
        'updater:unzipFinish',
        (_, result: { status: 'success' | 'error' }) => {
          if (result.status === 'success') {
            setUpdateStep('prepareInstall');
          } else {
            setUpdateStep('unzipError');
          }
        }
      ),
    ];
    return () => {
      unsubscribes.forEach((un) => un?.());
    };
  }, [downloadInfo?.fromInstaller]);

  return (
    <>
      <Dialog
        header="更新提示"
        visible={!!downloadInfo || needDownloadInstaller}
        footer={null}
        closeOnEscKeydown={false}
        closeOnOverlayClick={false}
        width={700}
        onClose={() => {
          setDownloadInfo(undefined);
          window.close();
        }}
        {...props}
      >
        <CommonDialog
          icon={
            <img
              style={{ height: '113px', width: '138px', marginTop: '24px' }}
              src={PrimaryTips}
              alt=""
            />
          }
          title="你需要先进行版本更新"
          desc={
            needDownloadInstaller
              ? '新版本需重新安装后使用'
              : '当前非最新版本，需要先进行更新后方可使用'
          }
          confirmText={
            <Button
              disabled={!!percent}
              theme="primary"
              className="gradient-primary"
              onClick={() => {
                if (
                  needDownloadInstaller &&
                  MatchedGlobalConfigItem.liveConf.clientDownloadLink
                ) {
                  setShowDownloadInstallerDialogFrame(true);
                  return;
                }
                void handleUpdate();
              }}
            >
              {needDownloadInstaller ? '去下载' : buttonText}
            </Button>
          }
        />
      </Dialog>
      <Dialog
        header="下载安装包"
        visible={!!showDownloadInstallerDialogFrame}
        footer={null}
        closeOnEscKeydown={false}
        closeOnOverlayClick={false}
        width="80%"
        style={{
          height: '80%',
        }}
        onClose={() => {
          setShowDownloadInstallerDialogFrame(false);
          // 如果需要下载安装包，这里可关闭后继续使用
        }}
      >
        <iframe
          title="客户端安装包下载"
          name="client_download_installer_frame"
          style={{
            width: '100%',
            height: 'calc(75vh - 50px)',
          }}
          src={MatchedGlobalConfigItem.liveConf.clientDownloadLink}
        />
      </Dialog>
    </>
  );
}
