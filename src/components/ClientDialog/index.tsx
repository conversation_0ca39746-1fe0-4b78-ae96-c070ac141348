/**
 * <AUTHOR>
 * @date 2024/10/28 17:48
 * @desc 客户端弹窗，包括更新检查、退出登录检查等
 */

import Tips from '@/assets/images/warningTips.png';
import {
  ClientLogType,
  useClientErrorLog,
} from '@/components/ClientDialog/useClientErrorLog';
import { useClientLiveCheck } from '@/components/ClientDialog/useClientLiveCheck';
import { CommonDialog } from '@/components/CommonDialog';
import { runningInClient } from '@/utils/electron';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { DialogPlugin } from 'tdesign-react';
import { UpdateClientDialog } from '@/components/ClientDialog/UpdateClientDialog';
import { InterceptExitDialog } from '@/components/ClientDialog/InterceptExitDialog';
import { ClientDriverDialog } from '@/components/ClientDialog/ClientDriverDialog';
import { ClientCommandProxy } from '@/components/ClientDialog/ClientCommandProxy';
import { ClientLiveRecover } from './ClientLiveRecover';
import { useRecoilValue } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { useCheckValidUserInfo } from './useClientLogin';
import { useEditorWindowClose } from './useEditorWindowClose';

const COMMON_DIALOG = {
  width: 700,
  footer: null,
  closeOnOverlayClick: false,
  closeOnEscKeydown: false,
  header: '提示',
};

export function ClientDialog() {
  // useClientLogin();
  const loginState = useRecoilValue(LoginStateAtom);
  const { userInfoValid } = useCheckValidUserInfo();
  useClientLiveCheck();
  useEditorWindowClose();

  const showErrorLogDialog = useCallback((log: ClientLogType) => {
    if (!runningInClient) return;
    const { message, upload, clear } = log;
    const myDialog = DialogPlugin({
      ...COMMON_DIALOG,
      header: '异常提示',
      body: (
        <CommonDialog
          icon={
            <img
              style={{ height: '113px', width: '138px', marginTop: '24px' }}
              src={Tips}
              alt=""
            />
          }
          title="上次异常退出"
          desc="已为您保留本地日志，您可以点击上报日志，并找到您的运营人员反馈问题。"
          onConfirm={async () => {
            await upload();
            await clear();
            console.log(message);
            myDialog.hide();
          }}
          confirmText="上报日志"
          cancelText="忽略"
          onCancel={async () => {
            await clear();
            myDialog.hide();
          }}
        />
      ),
      onClose: () => {
        myDialog.hide();
      },
    });
  }, []);
  // 异常日志提示
  useClientErrorLog(showErrorLogDialog);

  return runningInClient ? (
    <>
      <UpdateClientDialog />
      <InterceptExitDialog />
      <ClientDriverDialog />
      <ClientCommandProxy />
      <ClientLiveRecover x-if={loginState && userInfoValid} />
    </>
  ) : null;
}
