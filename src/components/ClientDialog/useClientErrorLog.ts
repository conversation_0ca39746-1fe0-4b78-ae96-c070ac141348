/**
 * <AUTHOR>
 * @date 2024/10/29 15:57
 * @desc useCheckClientVersion
 */
import { useEffect } from 'react';
import { runningInClient } from '@/utils/electron';
import { AvatarClientBridge } from '@tencent/avatar-client-bridge-type';

export type ClientLogType = Exclude<
  Awaited<ReturnType<AvatarClientBridge['getCrashLog']>>,
  undefined
>;

export const useClientErrorLog = (cb: (logRes: ClientLogType) => void) => {
  useEffect(() => {
    if (!runningInClient) return;
    window.avatarClientBridge?.getCrashLog().then((res) => {
      if (res?.message) {
        cb(res);
      }
    });
  }, [cb]);
};
