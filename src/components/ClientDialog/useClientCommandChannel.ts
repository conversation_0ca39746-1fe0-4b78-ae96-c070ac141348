// 客户端和后台之间的指令通道
import { createDelayPromise } from '@/utils/delay-promise';
import { EventBus } from '@tencent/eventbus';
import { useMemoizedFn } from 'ahooks';
import to from 'await-to-js';
import { useEffect } from 'react';
import {
  GetEventList,
  EventConfirm,
  GetEventListResponse,
} from '@/pb/api/HeartbeatSvr';
import { ClientPushConfigExtraType } from '@/type/client';
import { getClientLiveSessionId, parsePushTaskExtra } from './utils';
import { PushTask } from '@tencent/avatar-client-bridge-type/es';

type ClientCommandChannelEvents = {
  restart: () => void;
};
enum ClientCommandChannelStatus {
  INITED = 'inited',
  STARTING = 'starting',
  STARTED = 'started',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
}

export type ClientCommandTypes = keyof ClientCommandChannelEvents;

// 指令通道生成的一条指令
export interface IClientCommandResult<
  T extends ClientCommandTypes | unknown,
  D extends Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
> {
  status: 'success' | 'fail' | '';
  // 指令id
  id: string;
  type: T;
  liveId: string;
  liveSessionId: string;
  data?: D;
  // 失败原因
  failReason?: string;
}
export class ClientCommand<
  T extends ClientCommandTypes,
  P extends Parameters<ClientCommandChannelEvents[T]> = Parameters<
    ClientCommandChannelEvents[T]
  >
> {
  // 指令id
  public id = '';
  // 直播id
  public liveId = '';
  // 直播会话id
  public liveSessionId = '';
  // 标志是否已经完成，无论是否成功或者失败
  public done!: Promise<IClientCommandResult<T, P>>;
  protected resolve!: (result: IClientCommandResult<T, P>) => void;
  constructor(public type: T, public data: P) {
    const { resolve, promise } =
      createDelayPromise<IClientCommandResult<T, P>>();
    this.done = promise;
    this.resolve = resolve;
  }
  public async ack(options?: { retry?: boolean }): Promise<boolean> {
    const ackResult: IClientCommandResult<T, P> = {
      status: '',
      id: this.id,
      type: this.type,
      data: this.data,
      liveId: this.liveId,
      liveSessionId: this.liveSessionId,
    };
    if (options?.retry) {
      // 重试的时候，不要调用后台确认，等待下一轮事件的拉取
      ackResult.status = 'fail';
      ackResult.failReason = 'need_retry';
      this.resolve(ackResult);
      return false;
    }
    const [err] = await to(
      EventConfirm({
        event_id: this.id,
        live_id: this.liveId,
        live_session_id: this.liveSessionId,
      })
    );
    if (err) {
      ackResult.status = 'fail';
      ackResult.failReason = err.message;
      this.resolve(ackResult);
      return false;
    }
    ackResult.status = 'success';
    this.resolve(ackResult);
    return true;
  }
}

// 指令通道生成指令
class ClientCommandChannel extends EventBus<{
  cmd: (
    cmd: ClientCommand<
      ClientCommandTypes,
      Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
    >
  ) => void;
}> {
  public status: ClientCommandChannelStatus = ClientCommandChannelStatus.INITED;
  protected abortCtrl: AbortController | null = null;
  /** 按直播间id 记录下次可以拉取的时间 */
  protected nextPollingTimeRecord: Map<string, number> = new Map();
  protected async start(): Promise<boolean> {
    // 开始监听指令通道
    if (
      this.status === ClientCommandChannelStatus.STARTED ||
      this.status === ClientCommandChannelStatus.STARTING ||
      this.status === ClientCommandChannelStatus.STOPPING
    )
      return false;
    this.status = ClientCommandChannelStatus.STARTING;
    this.abortCtrl = new AbortController();
    // ... 做监听指令通道相关的事情
    this.listenEvents();
    await to(this.startPolling());
    this.status = ClientCommandChannelStatus.STARTED;
    return true;
  }
  public on<T extends 'cmd'>(
    eventName: T,
    callback: {
      cmd: (
        cmd: ClientCommand<
          ClientCommandTypes,
          Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
        >
      ) => void;
    }[T]
  ): () => void {
    super.on(eventName, callback);
    this.start();
    return () => {
      super.off(eventName, callback);
      if (this.eventMap.get(eventName)?.length === 0) {
        this.eventMap.delete(eventName);
      }
      if (this.eventMap.size === 0) {
        this.stop();
      }
    };
  }
  public async stop() {
    // 停止监听指令通道
    if (this.status === ClientCommandChannelStatus.STOPPING) return;
    this.status = ClientCommandChannelStatus.STOPPING;
    // ... 停止监听指令通道相关的事情
    const e = new Error('client command channel stopped');
    this.abortCtrl?.abort(`reason: ${e.message} stack: ${e.stack}`);
    this.nextPollingTimeRecord.clear();
    this.status = ClientCommandChannelStatus.STOPPED;
  }
  protected async startPolling() {
    let timeout: NodeJS.Timeout | null = null;
    const abortSignal = this.abortCtrl?.signal;
    this.abortCtrl?.signal.addEventListener('abort', () => {
      console.log(
        '[CommandChannel] ',
        'aborted with reason:',
        abortSignal?.reason
      );
      timeout && clearTimeout(timeout);
    });
    const doPolling = async () => {
      // 处理业务逻辑
      if (this.abortCtrl?.signal.aborted) return;
      const [err] = await to(this.handleCommand());
      if (err) {
        console.error('handle command error: ', err?.message);
      }
      timeout = setTimeout(async () => {
        doPolling();
      }, 3000);
    };
    console.log('[CommandChannel]', 'start polling');
    doPolling();
  }
  // 从后台拉取指令
  // eslint-disable-next-line class-methods-use-this
  protected async handleCommand(): Promise<
    ClientCommand<
      ClientCommandTypes,
      Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
    >[]
  > {
    const tasks = await window.avatarClientBridge?.pushTask.listPushTask();
    if (!tasks?.length) return [];
    const commands: ClientCommand<
      ClientCommandTypes,
      Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
    >[] = [];
    // TODO: 拉取一轮事件
    for (const task of tasks) {
      let extraData: ClientPushConfigExtraType;
      try {
        extraData = JSON.parse(task.config.extra);
      } catch (e) {
        continue;
      }
      const now = Date.now();
      const nextTime = this.nextPollingTimeRecord.get(extraData.liveId);
      if (typeof nextTime === 'number' && now < nextTime) {
        // 本轮不拉取
        continue;
      }
      const liveSessionId = await getClientLiveSessionId(extraData.clientSid);
      const [err, resp] = await to(
        GetEventList({
          live_id: extraData.liveId,
          live_session_id: liveSessionId,
          terminal_type: 'client',
        })
      );
      if (err) {
        this.nextPollingTimeRecord.set(extraData.liveId, Date.now() + 3000);
        continue;
      }
      this.nextPollingTimeRecord.set(
        extraData.liveId,
        Date.now() + resp.call_interval_second * 1000
      );
      const { live_events_list } = resp;
      if (live_events_list.length === 0) {
        continue;
      }
      this.deDuplicatedEvents(live_events_list).forEach((ev) => {
        const cmd = new ClientCommand(
          ev.event_type as ClientCommandTypes,
          []
        ) as ClientCommand<
          ClientCommandTypes,
          Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
        >;
        cmd.id = ev.event_id;
        cmd.liveId = extraData.liveId;
        cmd.liveSessionId = liveSessionId;
        commands.push(cmd);
      });
    }
    commands.forEach((cmd) => {
      this.emit('cmd', cmd);
    });
    await to(Promise.all(commands.map((cmd) => cmd.done)));
    return commands;
  }
  private listenEvents() {
    if (!window.avatarClientBridge?.subscribeEvents) return;
    const unsubs = [
      window.avatarClientBridge.subscribeEvents(
        'pushTask:stopped',
        (_, task: PushTask) => {
          const { config } = task;
          const parsedExtra = parsePushTaskExtra(config);
          console.log('[ClientCommandChannel] receive pushTask:stopped ', {
            task,
          });
          if (parsedExtra?.liveId) {
            this.nextPollingTimeRecord.delete(parsedExtra.liveId);
          }
        }
      ),
    ];
    this.abortCtrl?.signal.addEventListener('abort', () => {
      unsubs.forEach((un) => un());
    });
  }
  // eslint-disable-next-line class-methods-use-this
  private deDuplicatedEvents(
    eventList: GetEventListResponse['live_events_list']
  ): GetEventListResponse['live_events_list'] {
    const newEventList: GetEventListResponse['live_events_list'] = [];
    const map = new Map<string, boolean>();
    eventList.forEach((item) => {
      const key = [item.event_type, item.event_id].join('%');
      if (map.has(key)) {
        return;
      }
      map.set(key, true);
      newEventList.push(item);
    });
    return newEventList;
  }
}

let defaultChannel: ClientCommandChannel | null = null;

export const useClientCommandChannel = () => {
  if (!defaultChannel) {
    defaultChannel = new ClientCommandChannel();
  }

  const subscribeCommand = useMemoizedFn(
    (
      fn: (
        cmd: ClientCommand<
          ClientCommandTypes,
          Parameters<ClientCommandChannelEvents[ClientCommandTypes]>
        >
      ) => void
    ) => {
      const unsubscribe = defaultChannel?.on('cmd', (cmd) => {
        fn(cmd);
      });
      return unsubscribe;
    }
  );

  useEffect(() => {
    const handleUnload = async () => {
      await defaultChannel?.stop();
    };
    window.addEventListener('beforeunload', handleUnload);
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, []);
  return {
    subscribeCommand,
  };
};
