/**
 * <AUTHOR>
 * @date 2025/1/18 14:44
 * @desc ClientDriverDialog
 */

import { ClientVBAudioCheck } from '@/components/ClientDialog/ClientVBAudioCheck';
import { useMemoizedFn } from 'ahooks';
import { useEffect, useState } from 'react';
import { Dialog } from 'tdesign-react';

export function ClientDriverDialog() {
  const [visible, setVisible] = useState(false);
  const [, setKeyPressCount] = useState(0);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);

  const triggerEvent = () => {
    setVisible(true);
  };

  const handleKeyDown = useMemoizedFn((event: KeyboardEvent) => {
    // 检查是否按下了 Ctrl 键和 "i" 键
    if (event.ctrlKey && event.key === 'i') {
      setKeyPressCount((prevCount) => {
        const newCount = prevCount + 1;

        // 如果是第一次按下 "i" 键，启动计时器
        if (newCount === 1) {
          const newTimer = setTimeout(() => {
            setKeyPressCount(0); // 重置计数
          }, 3000);
          setTimer(newTimer);
        }

        // 如果按下次数达到 3 次，触发事件
        if (newCount === 3) {
          clearTimeout(timer!); // 清除计时器
          setKeyPressCount(0); // 重置计数
          triggerEvent(); // 触发事件
        }

        return newCount >= 3 ? 0 : newCount;
      });
    }
  });

  useEffect(() => {
    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);

    // 清理事件监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [handleKeyDown, timer]);
  return (
    <Dialog
      className="pagedoo-meta-live-global"
      closeOnEscKeydown={false}
      closeOnOverlayClick={false}
      closeBtn={false}
      destroyOnClose
      header="直播驱动调整"
      width={700}
      placement="center"
      // top={60}
      visible={visible}
      // onClose={onClose}
      footer={null}
    >
      <ClientVBAudioCheck
        onClose={() => {
          setVisible(false);
        }}
        onSuccess={() => {
          setVisible(false);
        }}
      />
    </Dialog>
  );
}
