/**
 * <AUTHOR>
 * @date 2024/11/7 15:01
 * @desc useClientLogin
 */
import { LoginStateAtom } from '@/model/login';
import { runningInClient } from '@/utils/electron';
import { NewSession } from '@/pb/api/ClientSvr';
import { addParam, delParam } from '@tencent/midas-util';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { MessagePlugin } from 'tdesign-react';
import { UserInfoAtom } from '@/model/user';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';

export const useClientLogin = () => {
  const [loginState] = useRecoilState(LoginStateAtom);

  useEffect(() => {
    const fn = async () => {
      if (!runningInClient) return;
      // 这里需要在原有web登录之后再调用检查
      if (!loginState) return;

      await queryClientSid();
    };

    void fn();
  }, [loginState]);
};

export const queryClientSid = async () => {
  if (!runningInClient) return;
  // const clientSid = getLocalStorageWithExpiry('client_sid') as string;
  // if (clientSid) return clientSid;
  try {
    const clientInfo = await NewSession({ session_type: 'client' });
    return clientInfo?.session_id;
    // if (clientInfo?.session_id) {
    //   // 设置一个12小时的过期时间
    //   setLocalStorageWithExpiry(
    //     'client_sid',
    //     clientInfo.session_id,
    //     60 * 60 * 12
    //   );
    //   return clientInfo.session_id;
    // }
  } catch (e) {
    console.error('获取client_sid失败', e);
    void MessagePlugin.error('获取客户端登录态失败');
  }
};

export const handleClientUrl = (url: string, clientSid: string) => {
  return delParam(
    'inner',
    addParam({ client_sid: encodeURIComponent(clientSid) }, url)
  );
};

/**
 * 校验用户信息的有效性
 */
export const useCheckValidUserInfo = () => {
  const loginState = useRecoilValue(LoginStateAtom);
  const userInfoState = useRecoilValue(UserInfoAtom);
  const [userInfoValid, setUserInfoValid] = useState(() => {
    if (!loginState) return false;
    if (!userInfoState) return false;
    return !MatchedGlobalConfigItem.runtimeConf.loginConf.checkUserInfoValid;
  });
  useLayoutEffect(() => {
    if (!loginState) return;
    if (!userInfoState) return;
    if (MatchedGlobalConfigItem.runtimeConf.loginConf.checkUserInfoValid) {
      MatchedGlobalConfigItem.runtimeConf.loginConf
        .checkUserInfoValid(userInfoState)
        .then((valid) => setUserInfoValid(valid));
    }
  }, [loginState, userInfoState]);
  return {
    userInfoValid,
  };
};
