/**
 * <AUTHOR>
 * @date 2025/1/18 21:45
 * @desc ClientVBAudioCheck
 */

import Tips from '@/assets/images/warningTips.png';
import { checkDriver } from '@/components/ClientDialog/utils';
import { CommonDialog } from '@/components/CommonDialog';
import { StepModalFooter } from '@/pages/ADMetaList/MetaPushStepModal/ModalFooter';
import { useAsyncEffect } from 'ahooks';
import { useMemo, useState } from 'react';
import { Button, Loading, MessagePlugin, Popconfirm } from 'tdesign-react';

interface IProps {
  onClose: () => void;
  onSuccess: () => void;
}

export const FAKE_REMOVE_CLIENT_DRIVER = 'FAKE_REMOVE_CLIENT_DRIVER';

const INSTALL_DRIVER = [
  {
    key: 'virtualVoice',
    icon: 'https://avatarcdn.pay.qq.com/material/21d930b354477f3636206621e309fca2.png',
    title: '虚拟声卡',
    desc: '基于软件技术虚拟麦克风或者扬声器硬件，帮助您在直播平台拉取到数字人音频。',
  },
];

enum INSTALL_STATUS {
  LOADING = 'loading',
  WAIT_INSTALL = 'waitInstall',
  WAIT_UNINSTALL = 'waitUninstall',
  INSTALLING = 'installing',
  UNINSTALLING = 'uninstalling',
  SUCCESS_INSTALL = 'success_install',
  SUCCESS_UNINSTALL = 'success_uninstall',
  FAIL_INSTALL = 'fail_install',
}

const buttonText = {
  [INSTALL_STATUS.LOADING]: '检测中...',
  [INSTALL_STATUS.WAIT_INSTALL]: '一键安装',
  [INSTALL_STATUS.WAIT_UNINSTALL]: '一键卸载',
  [INSTALL_STATUS.INSTALLING]: '安装中...',
  [INSTALL_STATUS.UNINSTALLING]: '卸载中...',
  [INSTALL_STATUS.SUCCESS_INSTALL]: '重启电脑',
  [INSTALL_STATUS.SUCCESS_UNINSTALL]: '重启电脑',
  [INSTALL_STATUS.FAIL_INSTALL]: '重新安装',
  // [INSTALL_STATUS.FAIL_UNINSTALL]: '重新卸载',
};

export function ClientVBAudioCheck(props: IProps) {
  const { onClose } = props;

  const [installStatus, setInstallStatus] = useState<INSTALL_STATUS>(
    INSTALL_STATUS.LOADING
  );

  useAsyncEffect(async () => {
    try {
      // TODO: 验证逻辑
      // if (localStorage.getItem(FAKE_REMOVE_CLIENT_DRIVER)) {
      //   setInstallStatus(INSTALL_STATUS.WAIT_UNINSTALL);
      //   return;
      // }
      const checkRes = await checkDriver();
      setInstallStatus(
        checkRes?.audio
          ? INSTALL_STATUS.WAIT_UNINSTALL
          : INSTALL_STATUS.WAIT_INSTALL
      );
    } catch (e) {
      console.error(e);
    }
  }, []);

  const handleInstall = async () => {
    if (installStatus === INSTALL_STATUS.INSTALLING) return;
    setInstallStatus(INSTALL_STATUS.INSTALLING);

    const loading = await MessagePlugin.loading('正在安装中', 0);
    try {
      await window.avatarClientBridge?.pushTask.installDrivers({
        audio: true,
      });

      const checkRes = await checkDriver();

      if (checkRes?.audio) {
        setInstallStatus(INSTALL_STATUS.SUCCESS_INSTALL);
        localStorage.removeItem(FAKE_REMOVE_CLIENT_DRIVER);
      } else {
        setInstallStatus(INSTALL_STATUS.FAIL_INSTALL);
      }
    } catch (e) {
      setInstallStatus(INSTALL_STATUS.FAIL_INSTALL);
      console.error(e);
    } finally {
      loading.close();
    }
  };

  const handleUninstall = async () => {
    if (installStatus === INSTALL_STATUS.UNINSTALLING) return;
    setInstallStatus(INSTALL_STATUS.UNINSTALLING);

    const loading = await MessagePlugin.loading('正在卸载中', 0);
    try {
      await window.avatarClientBridge?.pushTask.removeDrivers({
        audio: true,
      });
      void MessagePlugin.success('卸载成功');
    } catch (e) {
      // 这里兜底一下，有可能卸载失败,设置个本地缓存
      console.error(e, '声卡卸载失败');
      localStorage.setItem(FAKE_REMOVE_CLIENT_DRIVER, '1');
      void MessagePlugin.success('已成功卸载');
    } finally {
      setInstallStatus(INSTALL_STATUS.SUCCESS_UNINSTALL);
      loading.close();
    }
  };

  const handleClick = async () => {
    // 安装中直接返回
    if (installStatus === INSTALL_STATUS.INSTALLING) return;
    // 安装/卸载成功需要重启
    if (
      installStatus === INSTALL_STATUS.SUCCESS_INSTALL ||
      installStatus === INSTALL_STATUS.SUCCESS_UNINSTALL
    ) {
      await window.avatarClientBridge?.system.reboot();
      return;
    }
    if (installStatus === INSTALL_STATUS.WAIT_UNINSTALL) {
      await handleUninstall();
      return;
    }
    // 未安装或者安装失败
    if (
      installStatus === INSTALL_STATUS.WAIT_INSTALL ||
      installStatus === INSTALL_STATUS.FAIL_INSTALL
    ) {
      await handleInstall();
      return;
    }
  };

  const btnText = useMemo(() => {
    return buttonText[installStatus];
  }, [installStatus]);

  return (
    <div className="relative">
      <div className="h-[240px] flex items-center justify-center">
        <Loading
          x-if={installStatus === INSTALL_STATUS.LOADING}
          loading
          text="正在检查驱动..."
          size="small"
        />
        {/* 安装驱动*/}
        <div
          x-if={
            installStatus === INSTALL_STATUS.WAIT_INSTALL ||
            installStatus === INSTALL_STATUS.INSTALLING
          }
          className="flex flex-col items-center gap-[24px]"
        >
          <header className="mb-8 text-[#000] font-medium">
            请先安装以下工具才能进行开播
          </header>
          {INSTALL_DRIVER.map((item) => {
            return (
              <div
                key={item.key}
                className="w-full p-20 flex gap-[24px] bg-[linear-gradient(85deg,#F4F6FF_0%,#FAF5FC_100%)]"
              >
                <img width={84} height={84} src={item.icon} alt="" />
                <div className="flex flex-col justify-around">
                  <div className="font-medium h-[24px] leading-[24px] text-[black]">
                    {item.title}
                  </div>
                  <div>{item.desc}</div>
                </div>
              </div>
            );
          })}
        </div>

        <div
          x-if={
            installStatus === INSTALL_STATUS.WAIT_UNINSTALL ||
            installStatus === INSTALL_STATUS.UNINSTALLING
          }
          className="flex flex-col items-center"
        >
          <img
            style={{ height: '113px', width: '138px', marginTop: '24px' }}
            src={Tips}
            alt=""
          />
          <div className="mb-8 text-[#000] font-medium">
            确定要卸载虚拟声卡吗？
          </div>
        </div>

        {/* 安装*/}
        <CommonDialog
          x-if={installStatus === INSTALL_STATUS.SUCCESS_INSTALL}
          theme="success"
          title="安装成功"
          desc="安装需要重新启动电脑后才可以进行开播"
        />
        {/* 卸载*/}
        <CommonDialog
          x-if={installStatus === INSTALL_STATUS.SUCCESS_UNINSTALL}
          theme="success"
          title="卸载成功"
          desc="卸载需要重新启动电脑后才可以进行开播"
        />
        <CommonDialog
          x-if={installStatus === INSTALL_STATUS.FAIL_INSTALL}
          theme="error"
          title="安装失败，请重新安装"
        />
        {/* 卸载*/}
      </div>
      <div className="h-[48px]" />
      <StepModalFooter
        primaryBtn={
          installStatus === INSTALL_STATUS.SUCCESS_INSTALL ||
          installStatus === INSTALL_STATUS.SUCCESS_UNINSTALL ? (
            <Popconfirm
              content="请您确认是否要立即对电脑进行重启？"
              theme="danger"
              confirmBtn="确认重启"
              onConfirm={handleClick}
            >
              <Button className="gradient-primary">{btnText}</Button>
            </Popconfirm>
          ) : (
            <Button
              className="gradient-primary"
              onClick={handleClick}
              disabled={installStatus === INSTALL_STATUS.INSTALLING}
            >
              {btnText}
            </Button>
          )
        }
        onClose={
          installStatus !== INSTALL_STATUS.SUCCESS_INSTALL ? onClose : undefined
        }
      />
    </div>
  );
}
