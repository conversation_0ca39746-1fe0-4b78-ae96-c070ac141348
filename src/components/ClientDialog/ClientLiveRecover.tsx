import { useEffect, useMemo, useState } from 'react';
import { RecoverStepType, useClientLiveRecover } from './useClientLiveRecover';
import { Button, Dialog, Loading } from 'tdesign-react';
import { useMemoizedFn } from 'ahooks';
import { CommonDialog } from '../CommonDialog';

const descMapping: Record<RecoverStepType, string> = {
  failed: '恢复直播失败',
  succeeded: '恢复直播成功',
  queryLiveInfo: '查询直播信息中...',
  recovering: '恢复直播中...',
  init: '',
};
/**
 * 客户端推流恢复
 */
export function ClientLiveRecover() {
  const { isStartRecover, prepareRecover, ready, recoverStep, resetStep } =
    useClientLiveRecover();

  const [visible, setVisible] = useState(false);
  const [failText, setFailText] = useState('');

  const reset = useMemoizedFn(() => {
    setVisible(false);
    resetStep();
    setFailText('');
  });

  useEffect(() => {
    if (!ready) return;
    prepareRecover().catch((err: Error) => {
      console.log('[ClientLiveRecover] prepareRecover err:', err);
      if (err.message) {
        setFailText(err.message);
      }
    });
  }, [prepareRecover, ready]);
  useEffect(() => {
    if (!ready) return;
    if (isStartRecover) {
      setVisible(true);
    }
  }, [isStartRecover, ready]);
  const recoverText = useMemo(() => {
    if (failText) return failText;
    return descMapping[recoverStep];
  }, [failText, recoverStep]);
  const memoIcon = useMemo(() => {
    if (recoverStep === 'failed' || recoverStep === 'succeeded')
      return undefined;
    if (recoverStep !== 'init') return <Loading />;
    return <span />;
  }, [recoverStep]);

  const memoTheme = useMemo(() => {
    if (recoverStep === 'failed') return 'error';
  }, [recoverStep]);

  if (!ready) return null;
  return (
    <Dialog
      className="pagedoo-meta-live-global"
      closeOnEscKeydown={false}
      closeOnOverlayClick={false}
      closeBtn={false}
      destroyOnClose
      width={400}
      placement="center"
      // top={60}
      visible={visible}
      // onClose={onClose}
      footer={null}
    >
      <CommonDialog
        icon={memoIcon}
        title="直播恢复提示"
        theme={memoTheme}
        desc={recoverText}
        confirmText={
          <Button
            theme="primary"
            className="gradient-primary"
            onClick={() => {
              reset();
            }}
          >
            确定
          </Button>
        }
      />
    </Dialog>
  );
}
