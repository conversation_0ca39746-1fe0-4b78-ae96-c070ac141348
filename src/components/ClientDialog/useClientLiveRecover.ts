import { <PERSON>gin<PERSON>pi<PERSON>tom } from '@/model/api';
import { ClientLive } from '@/model/clientLive';
import { LoginStateAtom } from '@/model/login';
import { MetaLiveStatus } from '@/pages/ADMetaList/const';
import { IMetaLiveExtendData } from '@/pages/Editor/MetaHumanLive/typings';
import { GetLiveExtendConfigItems } from '@/pb/api/Development';
import { MetaLiveList } from '@/pb/api/MetaLiveSvr';
import { ClientPushConfigExtraType } from '@/type/client';
import { hasRunningLive } from '@/utils/electron';
import {
  PushTask,
  PushTaskConfig,
} from '@tencent/avatar-client-bridge-type/es';
import { useMemoizedFn } from 'ahooks';
import { to } from 'await-to-js';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import cloneDeep from 'lodash-es/cloneDeep';
import { parsePushTaskExtra } from './utils';

const recoverKey = '_obs_last_live_record';

interface IRecordItem {
  liveId: string;
  machineId: string;
  pushTaskConfig: PushTaskConfig;
  /** 需额外记录用户当时开播的openid，可额外做判断 */
  openId?: string;
}
export type RecoverStepType =
  | 'init'
  | 'queryLiveInfo'
  | 'recovering'
  | 'failed'
  | 'succeeded';
interface IQueryLiveInfo {
  // 是否在直播中
  isRunning: boolean;
  /**
   * 对应metaPushContext.ts 中调用livePush 时写入的扩展信息,只在isRunning 为true时有效
   */
  extendMetaPushConfig?: IMetaLiveExtendData;
}

export enum QueryLiveReason {
  SUCCESS = 'success',
  /**
   * 未找到该直播id
   */
  NOT_FOUND = 'not_found',

  /**
   * 未直播
   */
  NOT_RUNNING = 'not_running',

  /** 不属于当前客户端 */
  NOT_BELONG_CURRENT_CLIENT = 'not_belong_current_client',

  /**
   * 获取不到机器id
   */
  FAIL_GET_MACHINE_ID = 'fail_get_machine_id',

  /**
   * 读取直播信息失败
   */
  FAIL_READ_LIVE_EXTEND = 'fail_read_live_extend',
}

/**
 * 客户端恢复直播
 */
export const useClientLiveRecover = () => {
  const [isStartRecover, setIsStartRecover] = useState(false);
  const [ready, setReady] = useState(false);
  const loginApi = useRecoilValue(LoginApiAtom);
  const loginState = useRecoilValue(LoginStateAtom);
  const [machineId, setMachineId] = useState('');
  const [, setPushTaskConfig] = useRecoilState(ClientLive);
  const [recoverStep, setRecoverStep] = useState<RecoverStepType>('init');
  // const [prepareRecoverLiveId, setPrepareRecoverLiveId] = useState('');
  const resetStep = useMemoizedFn(() => {
    setRecoverStep('init');
  });
  /**
   * 记录直播
   */
  const recordPushConfigForRecover = useMemoizedFn(
    async (record: IRecordItem) => {
      const { liveId } = record;
      const data = localStorage.getItem(recoverKey);
      //   const items = data?
      let items: IRecordItem[] = [];
      if (data) {
        try {
          items = JSON.parse(data);
        } catch {}
      }
      const targetIdx = items.findIndex((item) => item.liveId === liveId);
      if (targetIdx !== -1) {
        items[targetIdx] = record;
      } else {
        items.push(record);
      }
      localStorage.setItem(recoverKey, JSON.stringify(items));
    }
  );
  const clearRecoverInfo = useMemoizedFn(async (liveId: string) => {
    const data = localStorage.getItem(recoverKey);
    let items: IRecordItem[] = [];
    if (data) {
      try {
        items = JSON.parse(data);
      } catch {}
    }
    if (items.length > 0) {
      items = items.filter((item) => item.liveId !== liveId);
      localStorage.setItem(recoverKey, JSON.stringify(items));
    }
  });
  const batchClearRecoverInfo = useMemoizedFn(async (liveIds: string[]) => {
    liveIds.forEach((liveId) => {
      clearRecoverInfo(liveId);
    });
  });
  const clearAllRecoverInfo = useMemoizedFn(async () => {
    localStorage.removeItem(recoverKey);
  });
  /**
   * 查询直播信息
   * @throws
   */
  const queryLiveInfo = useMemoizedFn(
    async (
      liveId: string
    ): Promise<{
      isRunning: boolean;
      reason: QueryLiveReason;
    }> => {
      if (!window.avatarClientBridge?.getMachineIdentity)
        return {
          isRunning: false,
          reason: QueryLiveReason.FAIL_GET_MACHINE_ID,
        };
      const resp = await MetaLiveList({
        meta_live_name_or_id: liveId,
      });
      if (resp.meta_live_list.length === 0)
        return { isRunning: false, reason: QueryLiveReason.NOT_FOUND };
      const metaLiveItem = resp.meta_live_list[0];
      const { meta_live_status } = metaLiveItem;
      if (meta_live_status === MetaLiveStatus.running) {
        // 查询扩展信息
        const extendResp = await GetLiveExtendConfigItems({
          live_id: liveId,
          node_id: 'global',
        });
        const metaLivePushConfig = extendResp.records.find(
          (item) => item.config_item_id === 'MetaLivePushConfig'
        );
        if (!metaLivePushConfig)
          return {
            isRunning: false,
            reason: QueryLiveReason.FAIL_READ_LIVE_EXTEND,
          };
        let extendMetaPushConfig: IMetaLiveExtendData | undefined;
        try {
          extendMetaPushConfig = JSON.parse(
            metaLivePushConfig.config_item_value
          );
          const { uuid } = await window.avatarClientBridge.getMachineIdentity();
          if (uuid === extendMetaPushConfig?.clientMeta?.machineId) {
            // 说明该远端开播是本客户端开的，可以进行恢复
            return {
              isRunning: true,
              reason: QueryLiveReason.SUCCESS,
            };
          }
          return {
            isRunning: false,
            reason: QueryLiveReason.NOT_BELONG_CURRENT_CLIENT,
          };
        } catch (e) {}
      } else if (meta_live_status === MetaLiveStatus.localLiving) {
        // 本地渲染方式的，直接返回running
        return {
          isRunning: true,
          reason: QueryLiveReason.SUCCESS,
        };
      }
      return {
        isRunning: false,
        reason: QueryLiveReason.NOT_RUNNING,
      };
    }
  );
  /**
   * @throws
   */
  const prepareRecover = useMemoizedFn(async () => {
    const [checkRunningErr, running] = await to(hasRunningLive());
    if (checkRunningErr || running) {
      // 本地有正在占用虚拟摄像头的，不支持恢复
      console.log('[ClientLiveRecover] 本地有正在运行的直播，不支持恢复');
      return;
    }
    if (!machineId) return;
    if (!loginState) return;
    const data = localStorage.getItem(recoverKey);
    let items: IRecordItem[] = [];
    if (data) {
      try {
        items = JSON.parse(data);
      } catch {}
    }
    // 目前只支持单条恢复
    if (items.length === 0) {
      console.log('[ClientLiveRecover] 没有可恢复的直播记录');
      return;
    }
    // 目前不支持远端推流恢复
    setIsStartRecover(true);
    setRecoverStep('queryLiveInfo');
    const needClearLive: string[] = [];
    return new Promise<PushTask>(async (resolve, reject) => {
      let targetItem: IRecordItem | undefined;
      for (const item of items) {
        const { liveId, openId: lastLiveOpenId } = item;
        const [err, result] = await to(queryLiveInfo(liveId));
        if (err) {
          console.error('[ClientLiveRecover] 查询直播详情失败 ', err.message);
          continue;
        }
        console.log('[ClientLiveRecover] 查询直播信息结果 ', result);
        if (!result.isRunning) {
          let shouldClear = true;
          if (lastLiveOpenId && loginState.openid !== lastLiveOpenId) {
            // 如果有openId 在开播时记录，就做检查,如果openId 不匹配，不要进行清理
            shouldClear = false;
          }
          if (shouldClear) {
            needClearLive.push(liveId);
          }
          try {
            if (result.reason === QueryLiveReason.NOT_FOUND) {
              throw new Error(`未找到直播间：${liveId}`);
            }
            if (result.reason === QueryLiveReason.NOT_RUNNING) {
              throw new Error(`待恢复直播间 ${liveId} 未开播`);
            }
            if (result.reason === QueryLiveReason.NOT_BELONG_CURRENT_CLIENT) {
              throw new Error(`待恢复直播间 ${liveId} 不属于当前客户端开播`);
            }
            throw new Error(`直播间 ${liveId} 不可恢复 - ${result.reason})`);
          } catch (e) {
            console.error(`[ClientLiveRecover] err ${(e as Error).message}`);
            continue;
          }
        }
        targetItem = item;
        // 找到了可以恢复的直播
        break;
      }
      // 先进行清理
      batchClearRecoverInfo(needClearLive);
      if (targetItem) {
        const { pushTaskConfig } = targetItem;
        const [recoverErr, task] = await to(
          window.avatarClientBridge!.pushTask!.startPushTask(pushTaskConfig)
        );
        if (recoverErr)
          return reject(new Error(`恢复直播流失败: ${recoverErr?.message}`));
        setPushTaskConfig([cloneDeep(task)]);
        return resolve(task);
      }
      return reject(new Error('没有找到可恢复的直播'));
    })
      .then((task) => {
        setRecoverStep('succeeded');
        return task;
      })
      .catch((e) => {
        batchClearRecoverInfo(needClearLive);
        setRecoverStep('failed');
        throw e;
      })
      .finally(() => {
        setIsStartRecover(false);
      });
  });
  useLayoutEffect(() => {
    if (!loginApi) return;
    if (!loginState) return;
    if (!window.avatarClientBridge?.getMachineIdentity) return;
    if (ready) return;
    window.avatarClientBridge.getMachineIdentity().then(({ uuid }) => {
      setMachineId(uuid);
      setReady(true);
    });
  }, [loginApi, loginState, ready]);

  useLayoutEffect(() => {
    if (!window.avatarClientBridge?.subscribeEvents) return;
    if (!machineId) return;
    if (!loginState) return;
    // 监听pushTask事件
    const unsubscribes = [
      window.avatarClientBridge.subscribeEvents(
        'pushTask:started',
        (_, task: PushTask) => {
          const { config } = task;
          const parsedExtra = parsePushTaskExtra(config);
          if (parsedExtra) {
            recordPushConfigForRecover({
              liveId: parsedExtra.liveId,
              machineId,
              pushTaskConfig: config,
              // 记录开播openId, 便于后续恢复时换号码恢复失败时可以做检查
              openId: loginState.openid,
            });
          }
        }
      ),
      window.avatarClientBridge.subscribeEvents(
        'pushTask:stopped',
        (_, task: PushTask) => {
          const { config } = task;
          const parsedExtra = parsePushTaskExtra(config);
          if (parsedExtra) {
            clearRecoverInfo(parsedExtra.liveId);
          }
        }
      ),
    ];
    return () => unsubscribes.forEach((un) => un());
  }, [clearRecoverInfo, loginState, machineId, recordPushConfigForRecover]);

  return {
    ready,
    prepareRecover,
    isStartRecover,
    recoverStep,
    resetStep,
  };
};
