/**
 * <AUTHOR>
 * @date 2024/4/19 下午2:26
 * @desc index
 */

import React, { useEffect, useLayoutEffect, useState } from 'react';
import { loader } from '@/components/GemsPreviewer/loader';
import { createPagedooApi } from '@/pages/Editor/pagedoo-api';
import { useRecoilValue } from 'recoil';
import { LoginApiAtom } from '@/model/api';
import { LoginStateAtom } from '@/model/login';
import { useMonitor } from '@/hooks/useMonitor';

interface IProps {
  width: number;
  height: number;
  globalData: any;
}

export function GemsPreviewer(props: IProps) {
  const { globalData, width, height } = props;
  const [ref, setRef] = useState<HTMLIFrameElement | null>(null);

  const loginApi = useRecoilValue(LoginApiAtom);
  const loginState = useRecoilValue(LoginStateAtom);
  const { monitor } = useMonitor();

  useEffect(() => {
    if (!ref) return;
    const init = () => {
      (ref.contentWindow as any)?.init({
        global: globalData,
        loader,
      });
    };
    if ((ref.contentWindow as any)?.init) {
      init();
      return;
    }
    const cb = () => {
      (ref.contentWindow as any)?.init({
        global: globalData,
        loader,
      });
    };
    ref.addEventListener('load', cb);
    return () => {
      ref.removeEventListener('load', cb);
    };
  }, [ref, globalData]);
  useLayoutEffect(() => {
    if (!ref) return;
    const destroy = createPagedooApi({
      loginApi,
      loginState,
      monitorInstance: monitor,
    });
    ref.src = `./preview.html${window.location.search}`;
    return () => {
      // 触发销毁 否则不会执行closeSession
      ref.src = 'about:blank';
      destroy();
      delete window.__pagedoo_api;
    };
  }, [loginApi, loginState, monitor, ref]);

  return (
    <iframe
      width={width}
      height={height}
      scrolling="no"
      title="预览"
      ref={setRef}
    />
  );
}
