// 主按钮
// 蓝紫色渐变
@primaryBtnLinearColor1: #0153ff;
@primaryBtnLinearColor2: #2e7ffd;
@primaryBtnLinearColor3: #c1a3fd;


// 筛选器按钮
// 默认态
@filterBtnLinearColor1: #F6F7FB;
@filterBtnLinearColor2: #F9F8FB;
@filterBtnLinearColor3: #FBF8FB;
// hover、选中态
@filterBtnLinearHoverColor1: #E0EAFF;
@filterBtnLinearHoverColor2: #E2EFFF;
@filterBtnLinearHoverColor3: #F5F3FF;

// 模板筛选器 - 按钮渐变色（从左往右）
@property --filter-btn-linear-color-1 {
  syntax: '<color>';
  initial-value: @filterBtnLinearColor1;
  inherits: false;
}

@property --filter-btn-linear-color-2 {
  syntax: '<color>';
  initial-value: @filterBtnLinearColor2;
  inherits: false;
}

@property --filter-btn-linear-color-3 {
  syntax: '<color>';
  initial-value: @filterBtnLinearColor3;
  inherits: false;
}

// 动画
@animation-duration: 0.2s;