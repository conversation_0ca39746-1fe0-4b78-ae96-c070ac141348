// REFERENCE ⬇️
// https: //github.com/Tencent/tdesign-common/blob/develop/style/web/mixins/_scrollbar.less

@scrollbar-color: rgba(0, 0, 0, 10%);
@scrollbar-hover-color: rgba(0, 0, 0, 30%);

.scrollbar(@size: 6px, @borderSize: 0px) {

  // Firefox 等浏览器标准支持的滚动条样式设置
  // Chrome support after v121 https://developer.chrome.com/docs/css-ui/scrollbar-styling
  @-moz-document url-prefix() {
    scrollbar-color: @scrollbar-color transparent;
    scrollbar-width: thin;
  }

  // Safari/Chrome 中滚动条样式设置
  &::-webkit-scrollbar {
    width: @size;
    height: @size;
  }

  &::-webkit-scrollbar-thumb {
    border: @borderSize solid transparent;
    background-clip: content-box;
    background-color: @scrollbar-color;
    border-radius: @size * 2 - 1px;
  }

  // 滚动条比较窄，吸底时，实际操作点击选中比较困难，因此，增加滚动条需要悬浮态，引导用户使用和滚动
  &::-webkit-scrollbar-thumb:vertical:hover,
  &::-webkit-scrollbar-thumb:horizontal:hover {
    background-color: @scrollbar-hover-color;
  }
}

.hideScrollbar() {

  /* firefox */
  // Chrome support after v121 https://developer.chrome.com/docs/css-ui/scrollbar-styling
  @-moz-document url-prefix() {
    scrollbar-width: none;
    overflow: -moz-scrollbars-none;
  }

  /* IE 10+ */
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    /* Chrome Safari */
    display: none;
    width: 0 !important;
  }
}