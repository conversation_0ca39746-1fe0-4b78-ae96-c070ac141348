import { ReactFC } from '@formily/react';
import { ReactPropsWithChildren } from '@formily/reactive-react';
import { ReactElement } from 'react';

interface ISuspenseViewProps {
  condition: boolean;
  suspense: ReactElement;
}
// 当condition为true时，展示 children
export function SuspenseView(
  props: ReactPropsWithChildren<ISuspenseViewProps>
) {
  const { condition, suspense, children } = props;
  if (!condition) return suspense;
  return children;
}
