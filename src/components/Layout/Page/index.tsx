import { useRecoilState } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import { DefaultBreadCrumb } from '../BreadCrumb';

import './index.less';
import { useSize } from 'ahooks';
import { useEffect, useRef } from 'react';

const HeaderHeight = 56;
const ParentPadding = 20 + HeaderHeight;

const DefaultTitle = '页匠智能脚本';

export function Page({
  breadCrumb,
  children,
  title,
  style,
  contentStyle,
  className,
}: {
  breadCrumb?: React.ReactNode;
  children?: React.ReactNode;
  title?: string;
  style?: React.CSSProperties;
  className?: string;
  contentStyle?: React.CSSProperties;
}) {
  const [state] = useRecoilState(LoginStateAtom);
  const breadCrumbRef = useRef<HTMLDivElement>(null);
  const breadCrumbRefSize = useSize(breadCrumbRef);

  useEffect(() => {
    document.title = title ? `${title} - ${DefaultTitle}` : DefaultTitle;
  }, [title]);

  return state ? (
    <div className={`page ${className || ''}`} style={{ ...style }}>
      <div
        ref={breadCrumbRef}
        style={{ overflow: 'hidden', paddingBottom: '15px' }}
      >
        {!!breadCrumb ? breadCrumb : <DefaultBreadCrumb title={title} />}
      </div>
      <div
        style={{
          height: `calc(100% - ${breadCrumbRefSize?.height ?? 0}px)`,
          position: 'relative',
          ...contentStyle,
        }}
      >
        {children}
      </div>
    </div>
  ) : null;
}
