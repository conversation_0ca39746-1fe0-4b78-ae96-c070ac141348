import { useRecoilState } from 'recoil';
import { UserInfoAtom } from '@/model/user';
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { Menu, Image } from 'tdesign-react';
import { UserInfo } from '@/components/UserInfo';
import { XNodeRegistry } from '@/configs/xnodes';
const { HeadMenu, MenuItem } = Menu;

export function Topbar() {
  const [userInfo] = useRecoilState(UserInfoAtom);
  return (
    <div className="px-24" style={{ borderBottom: '1px solid #DFE6F1' }}>
      <HeadMenu
        theme="light"
        operations={
          <>
            {MatchedGlobalConfigItem.layoutConf.headOperation?.map(
              ({ x_node_id }) => {
                const XNode = XNodeRegistry[x_node_id];
                return <XNode />;
              }
            )}
            <UserInfo
              userInfo={userInfo}
              nickNameStyle={{
                lineHeight: '22px',
                color: 'rgba(0, 0, 0, 0.9)',
                fontWeight: 400,
              }}
            />
          </>
        }
        logo={
          <div>
            <Image
              className={
                MatchedGlobalConfigItem?.logoConf?.topbar?.logoClassName || ''
              }
              src={MatchedGlobalConfigItem.logoConf.topbar.url}
              style={{ height: 56, background: 'none' }}
            />
          </div>
        }
      >
        {MatchedGlobalConfigItem.headMenu?.map((item) => (
          <MenuItem key={item.value}>{item.label}</MenuItem>
        ))}
      </HeadMenu>
    </div>
  );
}
