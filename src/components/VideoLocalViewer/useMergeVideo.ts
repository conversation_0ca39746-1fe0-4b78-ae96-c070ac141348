import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { Script } from '@/type/pagedoo';
import { playService } from '@/utils/play-template';
import { useAsyncEffect, useRequest } from 'ahooks';
import { useState } from 'react';
import { queryScriptList } from '@/components/ScriptForm/utils';
import { BaseScript } from '../ScriptForm/type';

export const useMergeVideo = ({
  script,
  templateId,
  type,
  scriptId,
}: {
  script?: BaseScript;
  scriptId?: string;
  templateId: string;
  type: CONTETNT_TYPE;
}) => {
  const [videoSchema, setVideoSchema] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  const { runAsync: queryScript } = useRequest(
    (id: string) =>
      queryScriptList(id).then((r) => {
        const [first] = r.script_list || [];

        return JSON.parse(first.script_info) as BaseScript;
      }),
    { manual: true }
  );

  useAsyncEffect(async () => {
    setLoading(true);
    try {
      let currentScript = script;
      if (!currentScript && scriptId) {
        currentScript = await queryScript(scriptId);
      }
      const playScript = await playService.genPlayScriptByTemplate({
        isVideo: type === CONTETNT_TYPE.VIDEO,
        isLive: type === CONTETNT_TYPE.LIVE,
        script: currentScript!,
        templateId,
      });
      setVideoSchema({
        script,
        playConfig: playScript,
      });
    } catch (error) {
      setError(error);
    }

    setLoading(false);
  }, [script, templateId]);

  return {
    videoSchema,
    loading,
    error,
  };
};
