import { GemsPreviewer } from '@/components/GemsPreviewer';
import { BaseScript } from '@/components/ScriptForm/type';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { PlayConfig, PlayService, Script } from '@/type/pagedoo';
import { playService } from '@/utils/play-template';
import { useEffect, useState } from 'react';
import { MessagePlugin } from 'tdesign-react';

interface IProps {
  script?: BaseScript | null;
  templateId?: string | null;
  playConfig?: PlayConfig;
  contentId?: string;
  width?: number;
  height?: number;
  type?: CONTETNT_TYPE;
}

export function VideoPreview(props: IProps) {
  const { script, templateId, playConfig, contentId, width, height, type } =
    props;
  const [pageSchemaData, setPageSchemaData] = useState<Awaited<
    ReturnType<PlayService['getPageSchema']>
  > | null>(null);

  useEffect(() => {
    if (!playConfig && !script) return;
    const init = async () => {
      const currentPlayConfig =
        playConfig ||
        (script
          ? await playService.genPlayScriptByTemplate({
              script,
              templateId: templateId!,
              isVideo: type === CONTETNT_TYPE.VIDEO,
              isLive: type === CONTETNT_TYPE.LIVE,
            })
          : undefined);

      if (!currentPlayConfig) throw new Error('playConfig为空');
      const pageSchemaData = await playService.getPageSchemaFromConfig(
        currentPlayConfig
      );

      // 这里的预览没有 contentId
      pageSchemaData.global['pagedoo-live'] = {
        id: contentId || '-',
      };
      // 预览设置为不自动播放 需要点击才能播放
      (
        pageSchemaData as any
      ).global.pages[0].data.components[0].children[0].data.play = false;
      setPageSchemaData(pageSchemaData);
    };
    init().catch((e) => {
      console.error(e, '获取视频预览失败');
      void MessagePlugin.error('获取视频预览失败');
    });
  }, [script, playConfig, templateId, contentId, type]);

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '4px',
        backgroundColor: '#2e2e2e',
      }}
    >
      {pageSchemaData?.global && (
        <div>
          <GemsPreviewer
            width={width || 286}
            height={height || 358}
            globalData={pageSchemaData.global}
          />
        </div>
      )}
    </div>
  );
}
