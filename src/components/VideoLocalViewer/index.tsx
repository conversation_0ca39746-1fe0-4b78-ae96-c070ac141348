import { Loading } from '@/components/Loading';
import { useMergeVideo } from './useMergeVideo';
import { VideoPreview } from './components/VideoPreview';
import { CONTETNT_TYPE } from '@/pages/Workbench/constants';
import { BaseScript } from '../ScriptForm/type';

export function VideoLocalViewer({
  script,
  templateId,
  type,
  width,
  height,
  scriptId,
  injectStyle,
  speaker = true,
}: {
  script: BaseScript;
  templateId: string;
  type: CONTETNT_TYPE;
  width?: number;
  height?: number;
  scriptId?: string;
  injectStyle?: React.CSSProperties;
  speaker?: boolean;
}) {
  const { videoSchema, loading, error } = useMergeVideo({
    script: speaker ? script : Object.assign(script, { globalSpeech: '' }),
    templateId,
    type,
    scriptId,
  });

  if (loading) {
    return (
      <div className="flex align-center justify-center">
        <Loading />
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex align-center justify-center">{error.message}</div>
    );
  }

  return (
    <div
      id="preview-container"
      className="flex align-center justify-center"
      style={injectStyle}
    >
      <VideoPreview
        playConfig={videoSchema?.playConfig}
        width={width}
        height={height}
        type={type}
      />
    </div>
  );
}
