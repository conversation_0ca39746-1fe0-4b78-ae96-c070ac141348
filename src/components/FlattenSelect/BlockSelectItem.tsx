import { css } from '@emotion/react';
import React from 'react';

// 方形带圆角item
export interface IBlockSelectItemProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'onClick'> {
  text: string;
  active: boolean;
}

export function BlockSelectItem(props: IBlockSelectItemProps) {
  const { text, active, ...rest } = props;
  const styles: React.CSSProperties = {
    height: '28px',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    flex: 1,
    textAlign: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  };
  if (active) {
    styles.background = 'rgba(255, 255, 255, 1)';
  }
  return (
    <div
      {...rest}
      css={css`
        color: ${active ? 'rgba(0, 71, 249, 1)' : 'rgba(0, 0, 0, 0.6)'};
      `}
      style={styles}
    >
      {text}
    </div>
  );
}
