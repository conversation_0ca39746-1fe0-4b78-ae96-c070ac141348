// 是否问答开关
import React, { useCallback, useMemo } from 'react';
import { CheckIcon } from 'tdesign-icons-react';
import './style.less';
import omit from 'lodash-es/omit';
import isEqual from 'lodash-es/isEqual';
import { ExtractArray, UnArray } from '@/utils/type-util';
import themeStyle from './theme.module.less';
import { BlockSelectItem } from '@/components/FlattenSelect/BlockSelectItem';

export interface IIFlattenSelectOption<V = any> {
  // 标签
  label: string;
  // 值
  value: V;
  key: string;
  // 自定义类名
  classNames?: string[];
  style?: React.CSSProperties;
  disabled?: boolean;
  render?: (
    option: Omit<IIFlattenSelectOption, 'render'> & {
      active: boolean;
      onSelect: (value: V) => void;
    }
  ) => React.ReactNode;
}
export interface IIFlattenSelectProps<V = any> {
  value?: ExtractArray<V>;
  onChange?: (value: ExtractArray<V>) => Promise<void>;
  title?: string;
  site?: 'top' | 'left' | 'right';
  options: IIFlattenSelectOption<UnArray<V>>[];
  className?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  labelStyle?: React.CSSProperties;
  itemGap?: number;
  multiple?: boolean;
  /**
   * default: 默认是每个选择项独立展示
   * block: 使用一个容器的样式包装渲染项
   */
  theme?: 'default' | 'block';
}

export function FlattenSelect<V>(props: IIFlattenSelectProps<V>) {
  const {
    onChange,
    value,
    options,
    className,
    style,
    disabled,
    title,
    site,
    multiple,
    theme = 'default',
  } = props;

  const cls = ['flatten-select-container', className];
  if (theme === 'block') {
    cls.push(themeStyle.themeBlock);
  }
  if (disabled) {
    cls.push('flatten-select-disabled');
  }
  cls.push(`flatten-select-container-${site}`);
  const matchIdx = useMemo(() => {
    let idx = 0;
    options.filter((option, i) => {
      if (option.value === value) {
        idx = i;
        return true;
      }
      return false;
    });
    return idx;
  }, [value, options]);

  const memoValues = useMemo(() => {
    return Array.isArray(value) ? value : [value];
  }, [value]);

  const handleChange = useCallback(
    (cur: UnArray<V>) => {
      if (multiple && Array.isArray(value)) {
        if (value.includes(cur)) {
          onChange?.(
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            (value as unknown[] as Array<UnArray<V>>).filter(
              (item) => !isEqual(item, cur)
            )
          );
        } else {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          onChange?.([...value, cur]);
        }
      } else {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        onChange?.(cur as unknown);
      }
    },
    [multiple, onChange, value]
  );

  return (
    <div className={cls.join(' ')} style={{ ...style }}>
      {title ? (
        site === 'top' ? (
          <h3>{title}</h3>
        ) : (
          <div
            style={{
              width: 'auto',
              lineHeight: '20px',
              flexShrink: '0',
              ...props.labelStyle,
            }}
          >
            {title}
          </div>
        )
      ) : null}
      <div
        className="flatten-select-item-wrapper"
        style={{
          ...(typeof props.itemGap === 'number'
            ? { gap: `${props.itemGap}px` }
            : undefined),
        }}
      >
        {options.map((option, idx) => {
          const cls = ['flatten-select-item', ...(option.classNames || [])];
          const isActive = memoValues.some((v) => isEqual(v, option.value));
          if (isActive) {
            cls.push('flatten-select-active');
          }
          let { render } = option;
          if (theme === 'block') {
            render = (option) => {
              return (
                <BlockSelectItem
                  active={option.active}
                  text={option.label}
                  onClick={() => option.onSelect(option.value)}
                />
              );
            };
          }
          if (typeof render === 'function') {
            return render({
              ...omit(option, ['render']),
              active: isActive,
              onSelect: (value) => {
                if (disabled || option.disabled) return;
                if (isActive && !multiple) return;
                handleChange(value);
              },
              disabled: disabled || option.disabled,
            });
          }

          // if (option.disabled) {
          //   cls.push('live-switch__disabled');
          // }
          return (
            <div
              key={option.key}
              className={cls.join(' ')}
              style={{ ...option.style }}
              onClick={() => {
                if (disabled || option.disabled) return;
                if (isActive && !multiple) return;
                // onChange?.(option.value);
                handleChange(option.value);
              }}
            >
              <svg
                className="selected-arrow"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M2 7L5 10.5L11.5 4" strokeLinejoin="round" />
              </svg>

              {option.label}
            </div>
          );
        })}
      </div>
    </div>
  );
}

FlattenSelect.defaultProps = {
  className: '',
  style: {},
  disabled: false,
  title: '',
  site: 'top',
};
