@import "../Style/mixins/variables.less";

.flatten-select {

    &-container {
        display: flex;
        justify-content: start;
        gap: 4px;
    }

    &-container-top{
        flex-direction: column;
    }

    &-container-left{
        flex-direction: row;
        align-items: center;
        gap:8px;
    }

    &-container-right{
        flex-direction: row-reverse;
        align-items: center;
        gap: 8px;
    }

    &-item-wrapper {
        display: flex;
        gap: 4px;
        flex-flow: wrap;
    }

    &-disabled {
        cursor: auto !important;
    }

    &-item {
        // 支持background-image渐变动画：https: //stackoverflow.com/a/63848864
        background-image: linear-gradient(82.56deg, var(--filter-btn-linear-color-1) 0%, var(--filter-btn-linear-color-2) 50%, var(--filter-btn-linear-color-3) 100%);
        border-radius: 50px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none !important;
        min-width: 78px;
        height: 38px;
        cursor: pointer;
        color: #00000099;
        // 避免文本过长，样式异常
        padding: 0 15px;
        transition: 
            --filter-btn-linear-color-1 0.2s ease-in-out,
            --filter-btn-linear-color-2 0.2s ease-in-out,
            --filter-btn-linear-color-3 0.2s ease-in-out,
        ;

        &:hover {
            --filter-btn-linear-color-1: @filterBtnLinearHoverColor1;
            --filter-btn-linear-color-2: @filterBtnLinearHoverColor2;
            --filter-btn-linear-color-3: @filterBtnLinearHoverColor3;
        }

    .selected-arrow {
        width: 0;
        margin-right: 0;
        transition: all 0.2s;
        path {
            stroke: currentColor;
            stroke-dasharray: 14;
            stroke-dashoffset: 0;
        }

        @keyframes dash {
            from {
                stroke-dashoffset: 14;
            }

            to {
                stroke-dashoffset: 0;
            }
            }
        }
    }

    

    &-active {
        // background: linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%);
        --filter-btn-linear-color-1: @filterBtnLinearHoverColor1;
        --filter-btn-linear-color-2: @filterBtnLinearHoverColor2;
        --filter-btn-linear-color-3: @filterBtnLinearHoverColor3;
        // background-image: linear-gradient(82.56deg, var(--filter-btn-linear-color-1) 0%, var(--filter-btn-linear-color-2) 50%, var(--filter-btn-linear-color-3) 100%);
        position: relative;
        color: #0047F9;

        .selected-arrow {
            width: 14px;
            margin-right: 6px;
            path {
                animation: dash .3s linear;
                animation-fill-mode: both;
            }
        }
    }
}