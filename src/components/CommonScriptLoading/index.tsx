/**
 * <AUTHOR>
 * @date 2024/7/25 下午3:56
 * @desc index
 */

import React, { useMemo } from 'react';
import IdooLoading from '@/assets/images/idooLoading.gif';
import { Button, Progress } from 'tdesign-react';
import './index.less';
import { isNull } from 'lodash-es';
import ReactDOM from 'react-dom';
import { CloseIcon } from 'tdesign-icons-react';

interface IProps {
  stepName: string;
  percent: number | null;
  onCancel?: () => void;
  header?: string;
  attachBody?: boolean;
  attachParentStyle?: React.CSSProperties;
  attachBodyStyle?: React.CSSProperties;
}

const DEFAULT_ATTACH_BODY_STYLE = {
  width: '480px',
  height: '350px',
  borderRadius: '8px',
  padding: '20px',
};

export function CommonScriptLoading(props: IProps) {
  const {
    stepName,
    percent,
    onCancel,
    header,
    attachBody,
    attachBodyStyle = {},
    attachParentStyle = {},
  } = props;

  const innerStyle = useMemo(() => {
    if (!attachBody) {
      return attachParentStyle;
    }
    return {
      ...DEFAULT_ATTACH_BODY_STYLE,
      ...attachBodyStyle,
    };
  }, [attachBody, attachBodyStyle, attachParentStyle]);

  const loadingElement = (
    <div className="common-script-loading-comp pagedoo-meta-live-global">
      <div className="common-script-loading-comp__inner" style={innerStyle}>
        {header && (
          <header className="header flex mb-16">
            <div className="flex-1">{header}</div>
            <CloseIcon />
          </header>
        )}

        <img src={IdooLoading} width={83} height={60} alt="" />
        <div className="mt-32">{`${stepName || '正在为您生成脚本'}...`}</div>
        {isNull(percent) ? null : (
          <Progress
            style={{
              width: '326px',
            }}
            color={['#0153FF', '#8649FF']}
            percentage={percent}
          />
        )}

        {onCancel && (
          <Button
            className="gradient-default"
            theme="default"
            onClick={() => {
              onCancel();
            }}
          >
            取消
          </Button>
        )}
      </div>
    </div>
  );

  if (attachBody) {
    return ReactDOM.createPortal(loadingElement, document.body);
  }

  return loadingElement;
}
