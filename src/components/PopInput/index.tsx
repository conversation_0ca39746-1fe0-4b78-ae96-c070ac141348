import React from 'react';
import {
  Popconfirm,
  PopconfirmProps,
  Input,
  Form,
  SubmitContext,
} from 'tdesign-react';
import { useBoolean } from 'ahooks';

import './index.less';

export type PopInputProps = Omit<PopconfirmProps, 'content' | 'onConfirm'> & {
  onConfirm?: (value: string) => void;
  title?: string;
  defaultValue?: string;
};

export default function PopInput(props: PopInputProps) {
  const { onConfirm, title, defaultValue = '', ...reset } = props;

  const [visible, { set, setFalse }] = useBoolean(false);

  const [form] = Form.useForm();

  const onSubmit = (e: SubmitContext) => {
    if (!e.validateResult) return;

    onConfirm?.(e.fields.value);

    setFalse();
  };

  return (
    <Popconfirm
      visible={visible}
      className="self-defined-popconfirm"
      icon={<></>}
      onVisibleChange={(v) => set(v)}
      confirmBtn={{
        className: 'gradient-primary',
        content: '确认',
        onClick: (e) => {
          form.submit();

          e.preventDefault();
        },
      }}
      cancelBtn={{
        className: 'gradient-default',
        content: '取消',
        onClick: () => {
          form.reset();

          setFalse();
        },
      }}
      placement="top-right"
      content={
        <Form className="mb-[-5px]" form={form} onSubmit={onSubmit}>
          <div className="text-black font-semibold pb-[5px]">{title}</div>
          <Form.FormItem
            name="value"
            initialData={defaultValue}
            rules={[{ required: true, message: '名称不能为空' }]}
          >
            <Input clearable className="w-full min-w-[230px]" />
          </Form.FormItem>
        </Form>
      }
      {...reset}
    >
      {props.children}
    </Popconfirm>
  );
}
