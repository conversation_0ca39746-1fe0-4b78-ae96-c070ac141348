import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { LoginContext, useLogin } from '@/hooks/login';
import { useInitReport } from '@/hooks/report';
import { IMonitorContextValue, MonitorContext } from '@/hooks/useMonitor';
import { LoginApiAtom } from '@/model/api';
import { LoginStateAtom } from '@/model/login';
import { getMonitor } from '@/utils/monitor';
import { useMount } from 'ahooks';
import React, { useEffect, useMemo, useState } from 'react';
import { useRecoilState } from 'recoil';

function GlobalDataWrapper({ children }: { children?: React.ReactElement }) {
  const [, setLoginApi] = useRecoilState(LoginApiAtom);
  const [loginState] = useRecoilState(LoginStateAtom);
  const [monitorInst, setMonitorInst] =
    useState<ReturnType<typeof getMonitor>>();
  // 登录hook
  const loginHook = useLogin();

  useEffect(() => {
    setLoginApi(loginHook);
  }, [setLoginApi, loginHook]);

  // useMount(() => {
  //   fetchLoginInfo().catch((e) => {
  //     console.error('GlobalDataWrapper 检测登录态失效', e);
  //   });
  // });
  // useEffect(() => {}, [fetchLoginInfo]);
  // 上报
  useInitReport();

  const ready = useMemo(() => {
    return monitorInst !== null;
  }, [monitorInst]);

  const monitorProvideValue = useMemo<IMonitorContextValue>(() => {
    return {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      monitor: monitorInst!,
    };
  }, [monitorInst]);

  useMemo(() => {
    setMonitorInst(
      getMonitor({
        aegisReportId:
          MatchedGlobalConfigItem.runtimeConf.monitorConf.aegisReportId,
        env:
          import.meta.env.VITE_ENV === 'production'
            ? 'production'
            : 'development',
      })
    );
  }, []);
  useEffect(() => {
    if (!monitorInst) return;
    if (!loginState?.openid) return;
    monitorInst.setUin(loginState?.openid);
  }, [loginState?.openid, monitorInst]);

  // return children;
  return (
    <LoginContext.Provider value={loginHook}>
      {ready && (
        <MonitorContext.Provider value={monitorProvideValue}>
          {children}
        </MonitorContext.Provider>
      )}
    </LoginContext.Provider>
  );
}

export default GlobalDataWrapper;
