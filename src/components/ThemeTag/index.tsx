import React from 'react';

interface ThemeTagProps {
  bgColor: string;
  textColor: string;
  text: string;
}

function ThemeTag({ bgColor, textColor, text }: ThemeTagProps) {
  return (
    <div className="relative w-[73px] h-[24px] py-2 text-sm font-medium flex items-center justify-center whitespace-nowrap">
      <div className="absolute inset-0">
        <svg
          width="73"
          height="24"
          viewBox="0 0 73 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.274639 22.7676C0.133839 23.3928 0.609141 23.9872 1.24996 23.9873L53.8731 24C62.8224 24 71.2754 18.6256 72.7724 11.9998C74.2695 5.37441 68.2234 0 59.2741 0H7.89088C6.51787 0 5.21931 0.823351 4.98775 1.84106L0.274639 22.7676Z"
            fill={`url(#${bgColor})`}
          />
          <defs>
            <linearGradient
              id={bgColor}
              x1="73"
              y1="15"
              x2="0"
              y2="15"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor={bgColor} />
              <stop offset="1" stopColor={bgColor} />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <span className="relative font-normal" style={{ color: textColor }}>
        {text}
      </span>
    </div>
  );
}

export default ThemeTag;
