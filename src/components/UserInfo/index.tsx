import { LoginContext } from '@/hooks/login';
import { IUserInfoAtom } from '@/model/user';
// import { AvatarUserInfo } from '@/vendor/AvatarUserInfo';
import React, { CSSProperties, useContext } from 'react';
import { Button, DialogPlugin, MessagePlugin, Popup } from 'tdesign-react';
import {
  CaretUpSmallIcon,
  CaretDownSmallIcon,
  CopyIcon,
  LogoutIcon,
} from 'tdesign-icons-react';
import { useBoolean } from 'ahooks';
import { useRecoilValue } from 'recoil';
import { LoginStateAtom } from '@/model/login';
import copy from 'copy-to-clipboard';
import { hasRunningLive, runningInClient } from '@/utils/electron';
import { CommonDialog } from '@/components/CommonDialog';
import Tips from '@/assets/images/warningTips.png';

interface IProps {
  userInfo?: IUserInfoAtom;
  nickNameStyle?: CSSProperties;
}

export function UserInfo(props: IProps) {
  const { userInfo, nickNameStyle } = props;
  const loginState = useRecoilValue(LoginStateAtom);

  const loginContext = useContext(LoginContext);

  const [visible, { set }] = useBoolean(false);

  const handleLogout = async () => {
    // 客户端判断当前是否有本地直播,有的话弹窗提示
    const hasLiveRunning = await hasRunningLive();
    // 不是客户端场景或者没有直播
    if (!hasLiveRunning) {
      loginContext?.goToLoginOut();
      return;
    }
    const myDialog = DialogPlugin({
      width: 700,
      header: '退出提示',
      body: (
        <CommonDialog
          theme="success"
          icon={
            <img
              style={{ height: '113px', width: '138px', marginTop: '24px' }}
              src={Tips}
              alt=""
            />
          }
          title="当前您的账号下有直播间正在推流中，若退出登录会造成当前直播流异常，请确认是否退出登录？"
          onConfirm={() => myDialog.hide()}
          onCancel={async () => {
            myDialog.hide();
            // await window.avatarClientBridge?.pushTask.stopPushTask(
            //   (
            //     await window.avatarClientBridge.pushTask.listPushTask()
            //   )[0].id
            // );
            loginContext?.goToLoginOut();
          }}
          confirmText="取消退出登录"
          cancelText="确认退出登录"
        />
      ),
      onClose: () => {
        myDialog.hide();
      },
      closeOnOverlayClick: false,
      footer: null,
    });
    return;
  };

  return (
    <Popup
      visible={visible}
      onVisibleChange={(v) => set(v)}
      trigger="hover"
      placement="bottom"
      content={
        <>
          <div
            className="cursor-pointer flex items-center gap-1"
            onClick={handleLogout}
          >
            <Button icon={<LogoutIcon />} theme="default" variant="text">
              退出登录
            </Button>
          </div>
          <div
            className="flex gap-1 items-center cursor-pointer"
            onClick={() => {
              copy(loginState?.openid || '');
              void MessagePlugin.success('已复制');
            }}
          >
            <Button icon={<CopyIcon />} theme="default" variant="text">
              复制uin
            </Button>
          </div>
        </>
      }
    >
      <div className="flex flex-row items-center cursor-pointer">
        <img
          className="w-[24px] h-[24px] rounded-[50%] border-[#fff] border-solid border-[1px]"
          src={userInfo?.avatar}
          alt="user-avatar"
        />
        <div
          style={nickNameStyle}
          className="text-[#fff] text-[14px] font-medium px-[8px]"
        >
          {userInfo?.nickname}
        </div>
        {visible ? (
          <CaretUpSmallIcon color="#fff" />
        ) : (
          <CaretDownSmallIcon color="#fff" />
        )}
      </div>
    </Popup>
  );
}
