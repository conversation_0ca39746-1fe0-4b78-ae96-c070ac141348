import { Radio, RadioGroupProps, RadioOptionObj } from 'tdesign-react';

import './index.less';

export default function RadioCircleGroup(
  props: Omit<RadioGroupProps, 'options'> & {
    dataSource: Array<RadioOptionObj>;
  }
) {
  return (
    <Radio.Group
      {...props}
      className={`define-radio-circle border-none !rounded-[50px] btn-background-light ${props.className}`}
      variant="default-filled"
    >
      {(props.dataSource || []).map((x) => (
        <Radio.Button
          style={{ width: `${(1 / props.dataSource.length) * 100}%` }}
          key={x?.value}
          value={x.value}
          disabled={x?.disabled}
        >
          {x?.label}
        </Radio.Button>
      ))}
    </Radio.Group>
  );
}
