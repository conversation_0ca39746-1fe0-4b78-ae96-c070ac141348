/**
 * <AUTHOR>
 * @date 2024/6/27 下午4:21
 * @desc index
 */

import React from 'react';
import ActionSuccess from '@/assets/images/create-condition-success2.png';
import { Button, Divider } from 'tdesign-react';
import { ArrowRightIcon } from 'tdesign-icons-react';

interface IProps {
  tips: string;
  onLink: () => void;
}

const STEPS = ['提交素材', '开始智能训练', '训练完成'];

export function TrainResultPage(props: IProps) {
  const { tips, onLink } = props;
  return (
    <div className="flex flex-col items-center pt-12">
      <img
        style={{ height: '120px', width: '140px' }}
        src={ActionSuccess}
        alt=""
      />
      <div className="my-12 font-semibold" style={{ color: '#000' }}>
        提交成功
      </div>
      <div className="mb-24 text-desc">{tips}</div>
      <div
        className="flex flex-col items-center pt-16 pb-24 px-20 rounded-8"
        style={{
          width: '620px',
          marginBottom: '64px',
          background:
            'linear-gradient(88deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%)',
        }}
      >
        <Divider align="center" layout="horizontal">
          <span className="text-desc">形象流程定制</span>
        </Divider>
        <div className="flex mt-4">
          {STEPS.map((item, index) => {
            return (
              <div className="flex items-center" key={item}>
                <span
                  style={{
                    display: 'inline-block',
                    width: '16px',
                    height: '16px',
                    marginRight: '4px',
                    lineHeight: '16px',
                    textAlign: 'center',
                    color: '#ffffff',
                    borderRadius: '50%',
                    background:
                      'linear-gradient(97deg, #0047F9 10.75%, #7386FF 100%)',
                  }}
                >
                  {index + 1}
                </span>
                <span>{item}</span>
                {index !== STEPS.length - 1 && (
                  <ArrowRightIcon style={{ margin: '0 8px' }} />
                )}
              </div>
            );
          })}
        </div>
      </div>
      <Button
        size="large"
        className="gradient-primary"
        onClick={() => {
          onLink();
        }}
      >
        前往管理列表
      </Button>
    </div>
  );
}
