import React from 'react';

interface EmptyInterface {
  style?: React.CSSProperties | undefined;
  children?: React.ReactNode | undefined;
}
// 无数据组件
export const Empty: React.FC<EmptyInterface> = function ({ style, children }) {
  return (
    <div
      style={{
        color: '#ccc',
        textAlign: 'center',
        lineHeight: '30px',
        ...style,
      }}
    >
      {children}
    </div>
  );
};

Empty.defaultProps = {
  style: {},
  children: '暂无数据',
};
