.custom-notification {
    position: fixed;
    right: 12px;
    bottom: 15px;

    width: 384px;
    padding: 16px;
    border-radius: 6px;

    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin-top: 10px; // Add margin between multiple notifications
    z-index: 9999;

    background-color: white;
    transition: opacity 0.3s;
  
    &-success {
      background-color: #d4edda;
      border-left: 5px solid #28a745;
    }
  
    &-info {
      background-color: #d1ecf1;
      border-left: 5px solid #17a2b8;
    }
  
    &-warning {
      background-color: #fff3cd;
      border-left: 5px solid #ffc107;
    }
  
    &-error {
      background-color: #f8d7da;
      border-left: 5px solid #dc3545;
    }
  }
  
  .custom-notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .custom-notification-title {
    font-weight: bold;
  }
  
  .custom-notification-close {
    display: inline-flex;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
    border-radius: 2px; // Optional: makes the button circular
    padding: 2px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1); // 灰色蒙层
    }
  }
  
  .custom-notification-content {
    font-size: 14px;
  }