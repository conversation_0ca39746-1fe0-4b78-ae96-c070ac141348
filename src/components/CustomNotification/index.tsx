import React, { useEffect } from 'react';
import './index.less';
import { CloseIcon } from 'tdesign-icons-react';

interface CustomNotificationProps {
  title: string;
  content: React.ReactNode;
  onClose: () => void;
  icon: React.ReactNode;
}

function CustomNotification({
  title,
  content,
  onClose,
  icon,
}: CustomNotificationProps) {
  return (
    <div className="custom-notification">
      <div className="custom-notification-header">
        <span className="custom-notification-title flex gap-[8px]">
          {icon}
          {title}
        </span>
        <div className="custom-notification-close" onClick={() => onClose()}>
          <CloseIcon />
        </div>
      </div>
      <div className="custom-notification-content">{content}</div>
    </div>
  );
}

export default CustomNotification;
