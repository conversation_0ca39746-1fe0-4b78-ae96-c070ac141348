/**
 * <AUTHOR>
 * @date 2024/9/9 下午9:02
 * @desc index
 */

import React from 'react';
import { TypeSelector } from '@/components/ScriptForm/TypeSelector';
import { CheckIcon } from 'tdesign-icons-react';

interface IProps {
  className?: string;
  options: {
    name: string;
    value: string;
  }[];
  value?: string;
  onChange?: (value: string) => void;
}

export function CustomSingleSelector(props: IProps) {
  const { options, value, onChange, className } = props;

  return (
    <div className={`flex gap-[8px] ${className || ''}`}>
      {options.map((item) => {
        return (
          <TypeSelector
            active={value === item.value}
            activeIcon={<CheckIcon />}
            key={item.value}
            value={item.name}
            onClick={() => {
              if (value === item.value) return;
              onChange?.(item.value);
            }}
          />
        );
      })}
    </div>
  );
}
