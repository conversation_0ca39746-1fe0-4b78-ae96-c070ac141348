import { VideoSvr } from '@/pb/pb';
import { sleep } from '@/utils/sleep';
import { useRef } from 'react';
import {
  DialogPlugin,
  Form,
  FormInstanceFunctions,
  MessagePlugin,
  Select,
} from 'tdesign-react';
const { FormItem } = Form;

async function releaseVideo(
  videoId: string,
  definition: string,
  format: string
) {
  try {
    const res = await VideoSvr.VideoRelease({
      video_id: videoId,
      video_definition: definition || '',
      video_format: format || '',
      release_flag: 1,
    });
    return res.result_code === '0';
  } catch (e) {
    return false;
  }
}

export const useVideoMaker = ({ loadingTs = 1000 }: { loadingTs: number }) => {
  // 制作参数
  const releaseFormRef = useRef<FormInstanceFunctions>();

  const videoMaker = (videoId: string, options?: { title?: string }) => {
    const { title = '制作视频' } = options || {};
    const dialog = DialogPlugin.confirm({
      className: 'make_video_dialog',
      header: title,
      body: (
        <div className="dialog_body">
          <Form ref={releaseFormRef}>
            <FormItem
              name="direction"
              label="横屏/竖屏"
              labelAlign="left"
              labelWidth={80}
              initialData="horizontal"
            >
              <Select
                options={[
                  { label: '横屏', value: 'horizontal' },
                  { label: '竖屏', value: 'vertical' },
                ]}
              />
            </FormItem>
            <FormItem
              name="definition"
              label="分辨率"
              labelAlign="left"
              labelWidth={80}
              initialData="720P"
            >
              <Select
                options={[
                  { label: '720P', value: '720P' },
                  { label: '1080P', value: '1080P' },
                  { label: '2K', value: '2K' },
                  { label: '4K', value: '4K' },
                ]}
              />
            </FormItem>
            <FormItem
              name="format"
              label="格式"
              labelAlign="left"
              labelWidth={80}
              initialData="flv"
            >
              <Select
                options={[
                  { label: 'flv', value: 'flv' },
                  { label: 'mp4', value: 'mp4' },
                ]}
              />
            </FormItem>
          </Form>
        </div>
      ),
      onCancel: () => dialog.destroy(),
      onClose: () => dialog.destroy(),
      onConfirm: async () => {
        const formVals = releaseFormRef?.current?.getFieldsValue([
          'direction',
          'definition',
          'format',
        ]);

        if (!formVals) return;

        const { direction, definition, format } = formVals as {
          direction: string;
          definition: string;
          format: string;
        };

        const loading = MessagePlugin.loading({
          content: '正在提交制作...',
          duration: 0,
        });
        const definitionMap: Record<string, any> = {
          horizontal: {
            '720P': '1280x720',
            '1080P': '1920x1080',
            '2K': '2560x1440',
            '4K': '3840x2160',
          },
          vertical: {
            '720P': '720x1280',
            '1080P': '1080x1920',
            '2K': '1440x2560',
            '4K': '2160x3840',
          },
        };
        const definitionTarget =
          definitionMap[direction as keyof typeof definitionMap][definition];
        const ts = +new Date();
        const res = await releaseVideo(
          videoId,
          definitionTarget,
          format as string
        );
        const costTime = +new Date() - ts;
        costTime < loadingTs && (await sleep(loadingTs - costTime));

        MessagePlugin.close(loading);

        if (!res) {
          MessagePlugin.error({ content: '提交制作失败，请稍候重试！' });
          return;
        }

        MessagePlugin.success({ content: '提交制作成功' });
        dialog.destroy();
      },
    });
  };
  return {
    videoMaker,
  };
};
