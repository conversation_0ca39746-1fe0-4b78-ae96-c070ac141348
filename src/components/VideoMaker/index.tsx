import {
  forwardRef,
  PropsWithChildren,
  ReactNode,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import { Form, InternalFormInstance, Select } from 'tdesign-react';

const { FormItem } = Form;
type VideoResolutions = '720P' | '1080P' | '2K' | '4K';
const definitionMap: Record<
  IVideoMakerFormData['direction'],
  {
    [K in VideoResolutions]: string;
  }
> = {
  horizontal: {
    '720P': '1280x720',
    '1080P': '1920x1080',
    '2K': '2560x1440',
    '4K': '3840x2160',
  },
  vertical: {
    '720P': '720x1280',
    '1080P': '1080x1920',
    '2K': '1440x2560',
    '4K': '2160x3840',
  },
};

export interface IVideoMakerFormData {
  direction: 'horizontal' | 'vertical';
  // 分辨率格式为 1920x1080
  definition: string;
  // 录制视频的格式
  format: 'mp4' | 'flv';
}

export interface IVideoMakerFormProps {
  defaultDirection?: IVideoMakerFormData['direction'];
  hideOrientationSelector?: boolean;
  onSubmit?: (values: IVideoMakerFormData) => void;
  children?: ReactNode | ((instance: IVideoMakerFormInstance) => ReactNode);
}
export interface IVideoMakerFormInstance {
  form: InternalFormInstance;
  getFormValues: () => IVideoMakerFormData;
  submit: () => Promise<IVideoMakerFormData>;
}
/**
 * 视频制作表单，支持选择分辨率，横竖屏，视频格式的选择
 */
export const VideoMakerForm = forwardRef<
  IVideoMakerFormInstance,
  IVideoMakerFormProps
>((props, ref) => {
  const [form] = Form.useForm();
  const submitPromise = useRef<{
    resolve: (val: IVideoMakerFormData) => void;
    reject: (e: Error) => void;
  }>();
  const instance = useMemo<IVideoMakerFormInstance>(
    () => ({
      form,
      getFormValues: () => {
        return form.getFieldsValue(true) as IVideoMakerFormData;
      },
      submit: async () => {
        const p = new Promise<IVideoMakerFormData>((resolve, reject) => {
          submitPromise.current = {
            resolve,
            reject,
          };
        });
        form.submit();
        return await p;
      },
    }),
    [form]
  );
  useImperativeHandle(ref, () => instance);
  const {
    children,
    onSubmit,
    defaultDirection = 'horizontal',
    hideOrientationSelector,
  } = props;
  return (
    <>
      <Form
        form={form}
        onSubmit={({ validateResult }) => {
          if (validateResult !== true) {
            submitPromise.current?.reject(new Error(`form validate error`));
            submitPromise.current = undefined;
            return;
          }
          const values: IVideoMakerFormData = form.getFieldsValue(
            true
          ) as IVideoMakerFormData;
          submitPromise.current?.resolve(values);
          submitPromise.current = undefined;
          onSubmit?.(values);
        }}
      >
        <FormItem
          name="direction"
          label="横屏/竖屏"
          labelAlign="left"
          labelWidth={80}
          initialData={defaultDirection}
          style={{
            ...(hideOrientationSelector
              ? {
                  display: 'none',
                }
              : undefined),
          }}
        >
          <Select
            options={[
              { label: '横屏', value: 'horizontal' },
              { label: '竖屏', value: 'vertical' },
            ]}
          />
        </FormItem>
        <FormItem
          shouldUpdate={(prev, next) => {
            return prev.direction !== next.direction;
          }}
        >
          {({ getFieldValue }) => {
            const direction: IVideoMakerFormData['direction'] = getFieldValue(
              'direction'
            ) as IVideoMakerFormData['direction'];
            if (!direction) return <></>;
            const config = definitionMap[direction];
            return (
              <FormItem
                key={`definition_${direction}`}
                name="definition"
                label="分辨率"
                labelAlign="left"
                labelWidth={80}
                initialData={config['720P']}
              >
                <Select
                  options={(
                    ['720P', '1080P', '2K', '4K'] as VideoResolutions[]
                  ).map((key) => {
                    return {
                      label: key,
                      value: config[key],
                    };
                  })}
                />
              </FormItem>
            );
          }}
        </FormItem>
        <FormItem
          name="format"
          label="格式"
          labelAlign="left"
          labelWidth={80}
          initialData="mp4"
        >
          <Select
            options={[
              { label: 'mp4', value: 'mp4' },
              { label: 'flv', value: 'flv' },
            ]}
          />
        </FormItem>
      </Form>
      {typeof children === 'function' ? children(instance) : children}
    </>
  );
});
