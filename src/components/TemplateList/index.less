.template-card__container {
  border: 1px solid #d8d8d8;
  width: 100%;
  aspect-ratio: 180 / 300;
  border-radius: 4px;
  position: relative;

  .template-card__mask {
    background-color: rgba(0, 0, 0, 0.62);
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: -10;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.01, 0.32, 0.58, 1);

    .primary-button__group {
      color: #ffffff;
      display: flex;
      flex-direction: column;

    }

    .template-stats__group {
      display: flex;
      color: #ffffff;

      p {
        text-align: center;
        margin: 89px 27px 20px 27px;
      }
    }
  }

  &:hover {
    .template-card__mask {
      z-index: 2;
      opacity: 1;
    }
  }
}

.template-name {
  height: 3.2em;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  color: rgba(0, 0, 0, 0.9);
  background: linear-gradient(97.99deg, #ebf4ff 12.3%, #f8f8ff 99.99%);
  span {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
}