import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Col, Loading, Row, Space } from 'tdesign-react';
import './index.less';
import { Empty } from '@/components/Empty';

export interface TemplateItem {
  id: string;
  name: string;
  poster_url: string;
}

export function TemplateList(props: {
  loading: boolean;
  style?: React.CSSProperties;
  className?: string;
  data: TemplateItem[];
  onCreate: (id: TemplateItem['id']) => void;
}) {
  const { loading, style, className, data, onCreate } = props;
  return (
    <section className={className || ''} style={{ ...style }}>
      {loading ? (
        <Space className="flex-center h-full">
          <Loading text="正在获取模版列表..." loading size="small" />
        </Space>
      ) : (
        <Row gutter={16} style={{ gap: '16px 0', height: '100%' }}>
          {data.length === 0 ? (
            <div className="flex-center h-full w-full">
              <Empty>暂无数据</Empty>
            </div>
          ) : (
            <>
              {data.map((template) => (
                <Col
                  span={2}
                  xs={6}
                  sm={4}
                  md={3}
                  xl={2}
                  key={template.id}
                  style={{}}
                >
                  <TemplateCard template={template} onClick={onCreate} />
                </Col>
              ))}
            </>
          )}
        </Row>
      )}
    </section>
  );
}

function TemplateCard(props: {
  template: TemplateItem;
  onClick: (id: TemplateItem['id']) => void;
}): JSX.Element {
  const { template, onClick } = props;
  const [imgBlob, setImgBlob] = useState<string>();

  const { id, name, poster_url } = template;
  const cardContainer = useRef<HTMLDivElement>(null);

  useMemo(() => {
    if (!poster_url) return;
    const img = new Image();
    (async () => {
      const resp = await fetch(poster_url);
      const blob = await resp.blob();
      const u = window.URL.createObjectURL(blob);
      setImgBlob(u);
      await new Promise<void>((resolve, reject) => {
        img.onload = (e) => {
          img.onload = null;
          const target = e.currentTarget! as unknown as Event & {
            width: number;
            height: number;
          };
          if (cardContainer.current) {
            cardContainer.current.style.backgroundImage = `url(${u})`;
            if (target?.width < target?.height) {
              cardContainer.current.style.backgroundSize = 'cover';
            }
          }
          resolve();
        };
        img.onerror = reject;
        img.src = u;
      });
    })()
      .catch(() => {
        if (cardContainer.current) {
          cardContainer.current.style.backgroundImage = `url(${poster_url})`;
        }
      })
      .finally(() => {
        if (cardContainer.current) {
          cardContainer.current.style.opacity = '1';
        }
      });
  }, [poster_url]);

  useEffect(() => {
    return () => {
      if (imgBlob) {
        window.URL.revokeObjectURL(imgBlob);
      }
    };
  }, [imgBlob]);

  return (
    <section style={{ borderRadius: '4px', overflow: 'hidden' }}>
      <div
        ref={cardContainer}
        className="template-card__container transition-opacity"
        style={{
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'contain',
          backgroundPosition: 'center',
          backgroundColor: 'rgba(0, 0, 0, 1)',
          opacity: '0',
        }}
      >
        {/* {!imgBlob && (
          <Loading className="absolute top-2/4 left-2/4 translate-x-[-50%] translate-y-[-50%]" />
        )} */}
        <div className="template-card__mask">
          <section className="primary-button__group">
            <Button
              className="gradient-primary"
              theme="primary"
              onClick={() => {
                onClick(id);
              }}
            >
              基于此创建
            </Button>
          </section>
          <section className="template-stats__group" />
        </div>
      </div>
      <div className="template-name">
        <span>{name}</span>
      </div>
    </section>
  );
}
