.loading_u7oxe4 {
  width: 100px;
  height: 100px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;

  .loader,
  .loader::before,
  .loader::after {
    width: 4px;
    height: 28px;
    border-radius: 4px;
  }

  .loader {
    display: inline-block;
    position: relative;
    background: currentColor;
    color: #6653fb;
    -webkit-animation: animloader61 0.3s 0.3s linear infinite alternate;
    animation: animloader61 0.3s 0.3s linear infinite alternate;
  }

  .loader::after,
  .loader::before {
    content: '';
    background: #29a5ff;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    -webkit-animation: animloader61 0.3s 0.45s linear infinite alternate;
    animation: animloader61 0.3s 0.45s linear infinite alternate;
  }

  .loader::before {
    left: -10px;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
    background: #ee54ed;
  }

  @keyframes animloader61 {
    0% {
      height: 28px;
    }

    100% {
      height: 2px;
    }
  }

  @keyframes animloader61m {
    0% {
      height: 40px;
      transform: translateY(0);
    }

    100% {
      height: 10px;
      transform: translateY(30px);
    }
  }
}
