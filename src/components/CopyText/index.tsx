// copy from pagedoo-shop-admin

import React, { useMemo, useState } from 'react';
import { FileCopyIcon } from 'tdesign-icons-react';
import { Tooltip } from 'tdesign-react';

export const Text: React.FC<{
  children: string;
  style?: React.CSSProperties;
  copyable?: boolean;
  theme?: 'link' | 'error' | 'default';
  lineClamp?: number;
  iconStyle?: React.CSSProperties;
}> = function (props) {
  const [toolTipContent, setToolTipContent] = useState('复制');
  const { theme, lineClamp, iconStyle } = props;
  // 默认样式
  const defaultStyle: React.CSSProperties = {
    lineHeight: '26px',
    wordBreak: 'break-all',
    alignItems: 'center',
  };
  // 内置主题样式
  const themeStyle: React.CSSProperties = {
    link: {
      color: '#1890ff',
    },
    error: {
      color: '#ff4d4f',
    },
    default: {},
  }[theme ?? 'default'];
  // lineClamp 样式
  const lineClampStyle: React.CSSProperties = useMemo(() => {
    if (lineClamp === undefined) {
      return {};
    }
    // 一行 不能使用 WebkitLineClamp
    if (lineClamp === 1) {
      return {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
      };
    }
    return {
      display: '-webkit-box',
      WebkitBoxOrient: 'vertical',
      WebkitLineClamp: lineClamp,
      overflow: 'hidden',
    };
  }, [lineClamp]);
  return (
    <div
      style={{
        ...defaultStyle,
        ...themeStyle,
        ...lineClampStyle,
        ...props.style,
      }}
    >
      {props.children}
      {props.copyable && (
        <Tooltip
          content={toolTipContent}
          theme="light"
          placement="top"
          key={toolTipContent}
        >
          <FileCopyIcon
            style={{
              ...(iconStyle || {}),
              display: 'inline-block',
              marginLeft: '6px',
              flexShrink: 0,
            }}
            onClick={(event) => {
              event.stopPropagation();
              navigator.clipboard.writeText(props.children);
              setToolTipContent('复制成功');
            }}
            onMouseOut={() => {
              setToolTipContent('复制');
            }}
          />
        </Tooltip>
      )}
    </div>
  );
};
Text.defaultProps = {
  style: {},
  copyable: false,
  theme: 'default',
  lineClamp: undefined,
  iconStyle: {},
};
export default Text;
