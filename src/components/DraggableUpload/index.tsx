/**
 * <AUTHOR>
 * @date 2024/5/8 下午4:57
 * @desc index
 */

import React, {
  forwardRef,
  ReactElement,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { Loading, Upload } from 'tdesign-react';
import moment from 'moment/moment';
import { TdUploadProps, UploadFile } from 'tdesign-react/es/upload/type';
import { UploadRef } from 'tdesign-react/es/upload/interface';
import {
  AddIcon,
  CheckCircleFilledIcon,
  ErrorCircleFilledIcon,
} from 'tdesign-icons-react';
import { convertFileSize } from '@/utils/file';
import { uploadRequest } from '@/utils/cos';
import { MATERIAL_TYPE } from '@/configs/upload';
import './index.less';

interface IProps {
  accept: string;
  desc?: string | ReactElement;
  onFail?: TdUploadProps['onFail'];
  onSuccess?: TdUploadProps['onSuccess'];
  onFileUrlChange?: (url: string) => void;
  onFileChange?: (file: UploadFile[]) => void;
  prefix?: string;
  addIcon?: boolean;
  //   表单能力
  value?: string;
  onChange?: (value: string) => void;
  autoUpload?: boolean;
}

export interface IDraggableUploadRef {
  getFiles: () => UploadFile[];
}

export const DraggableUpload = forwardRef<IDraggableUploadRef, IProps>(
  (props, ref) => {
    const {
      onSuccess,
      onFail,
      onFileUrlChange,
      onFileChange,
      accept,
      desc,
      addIcon,
      onChange,
      prefix,
      autoUpload,
    } = props;
    const [files, setFiles] = useState<UploadFile[]>([]);
    const uploadDom = React.createRef<UploadRef>();

    useImperativeHandle(ref, () => ({
      getFiles() {
        return files;
      },
    }));

    useEffect(() => {
      if (files.length > 0 && uploadDom.current && autoUpload) {
        // 这里用ref调用来代替自动上传过程屏蔽掉的process状态
        files.every((item) => item.status === 'waiting') &&
          uploadDom.current.uploadFiles();
      }
    }, [autoUpload, files, uploadDom]);

    const descRender = useMemo(() => {
      if (!desc) return <></>;

      if (typeof desc === 'string') {
        return <div className="desc">{desc}</div>;
      }
      return desc;
    }, [desc]);

    const requestFile = async (file: UploadFile) => {
      const result = await uploadRequest(
        MATERIAL_TYPE.FILE,
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        file.raw!,
        0,
        () => void 0
      );
      return {
        status: result.code === 200 ? 'success' : 'fail',
        response: { url: result.url, cosKey: result.key },
      } as const;
    };

    const fileListDisplay = () => {
      const file = files[0];
      if (!file)
        return (
          <div className="before-upload-box">
            {addIcon && (
              <span>
                <AddIcon
                  style={{
                    color: '#0047F9',
                    fontSize: '24px',
                    marginBottom: '12px',
                  }}
                />
              </span>
            )}
            <div>
              <span className="text-[#0052D9]">点击上传</span>
              <span className="mx-8">/</span>
              <span>拖拽到此区域</span>
            </div>
            {descRender}
          </div>
        );
      const { status } = file;
      let icon: React.ReactElement;
      if (status === 'success') {
        icon = <CheckCircleFilledIcon />;
      } else if (status === 'fail') {
        icon = <ErrorCircleFilledIcon style={{ color: '#D54941' }} />;
      } else if (status === 'progress') {
        icon = <Loading loading size="small" />;
      } else {
        icon = <></>;
      }
      return (
        <div
          className="flex items-center justify-center"
          style={{ height: '100%' }}
        >
          <div className="upload-box">
            <div className="file-name flex items-center">
              <span className="mr-4">{file.name}</span>
              {icon}
            </div>
            {/* <div className="file-msg">文件大小：（{file.size} B）</div>*/}
            <div className="file-msg">
              文件大小：{convertFileSize(file.size || 0)}
            </div>
            <div className="file-msg">上传日期：{file?.uploadTime || '-'}</div>

            <div className="mt-20 flex" style={{ gap: '8px' }}>
              {status !== 'progress' && (
                <div
                  style={{ color: '#0052D9', cursor: 'pointer' }}
                  onClick={(e) => {
                    uploadDom.current?.triggerUpload();
                    e.stopPropagation();
                  }}
                >
                  重新上传
                </div>
              )}

              {status !== 'progress' && (
                <div
                  style={{ color: '#0052D9', cursor: 'pointer' }}
                  onClick={(e) => {
                    setFiles([]);
                    onFileUrlChange?.('');
                    e.stopPropagation();
                  }}
                >
                  {status === 'waiting' ? '取消上传' : '删除'}
                </div>
              )}
            </div>
          </div>
        </div>
      );
    };
    return (
      <div className="draggable-upload-comp">
        <Upload
          accept={accept}
          ref={uploadDom}
          theme="custom"
          autoUpload={false}
          dragContent={fileListDisplay}
          draggable
          files={files}
          onChange={(file) => {
            if (file.length === 1 && uploadDom.current) {
              // eslint-disable-next-line no-param-reassign
              file[0].uploadTime = moment().format('YYYY-MM-DD');
            }
            setFiles(file);
            onFileChange?.(file);
          }}
          onFail={(options) => {
            onFail?.(options);
            onFileUrlChange?.('');
          }}
          onSuccess={(options) => {
            onSuccess?.(options);
            const url = `${prefix}${files[0]?.response?.url || ''}`;
            onFileUrlChange?.(url);
            onChange?.(url);
          }}
          fileListDisplay={fileListDisplay}
          requestMethod={(file) => {
            return requestFile(file);
          }}
        />
      </div>
    );
  }
);

DraggableUpload.defaultProps = {
  prefix: '',
  autoUpload: true,
};
