/* eslint-disable no-param-reassign */
/**
 * <AUTHOR>
 * @date 2024/8/12 下午5:16
 * @desc index
 */

import { useVideoInfo } from '@/hooks/useVideoInfo';
import { getVideoFirstFrame } from '@/utils/getVideoFirstFrame';
import { useAsyncEffect } from 'ahooks';
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

export interface IVideo2PosterRef {
  getPoster: (file: string) => Promise<PosterResult>;
}

type PosterResult = { blob: Blob | null; dataUrl: string } | null;

export const Video2Poster = forwardRef<IVideo2PosterRef>((_, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useImperativeHandle(ref, () => ({
    getPoster(file) {
      return handleFileChange(file);
    },
  }));

  const handleFileChange = async (file: string) => {
    const url = file;
    if (videoRef.current) {
      videoRef.current.src = url;
      await loadVideoMetadata(videoRef.current);
      return await captureThumbnail(videoRef.current, canvasRef.current);
    }
    return {
      blob: null,
      dataUrl: '',
    };
  };

  const loadVideoMetadata = (video: HTMLVideoElement) => {
    return new Promise<void>((resolve) => {
      video.onloadedmetadata = () => {
        // video.currentTime = 0;
        resolve();
      };
    });
  };

  const captureThumbnail = (
    video: HTMLVideoElement,
    canvas: HTMLCanvasElement | null
  ) => {
    return new Promise<PosterResult>((resolve) => {
      if (canvas) {
        const context = canvas.getContext('2d');
        if (context) {
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          const onTimeUpdate = () => {
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            const dataUrl = canvas.toDataURL('image/png');
            canvas.toBlob((blob) => {
              resolve({
                blob,
                dataUrl,
              });
            });
            // resolve(blob);
            // 确保只处理一次
            video.removeEventListener('timeupdate', onTimeUpdate);
          };

          video.addEventListener('timeupdate', onTimeUpdate);

          // 确保设置 currentTime 后事件能触发
          video.currentTime = 0.1;
          // };
        } else {
          resolve(null);
        }
      } else {
        resolve(null);
      }
    });
  };
  return (
    <div style={{ display: 'none' }}>
      <video ref={videoRef} crossOrigin="anonymous" />
      <canvas ref={canvasRef} />
    </div>
  );
});

interface IProps {
  videoUrl: string;
}
export function Video2PosterPro(props: IProps) {
  const { videoUrl } = props;

  const { videoInfo } = useVideoInfo({
    videoUrl,
    options: {
      fetchFirstFrame: true,
    },
  });

  return (
    <div className="flex justify-center items-center h-full bg-black">
      <img
        x-class={{
          'w-full': videoInfo?.info.direction === 'horizontal',
          'h-full': videoInfo?.info.direction === 'vertical',
        }}
        src={videoInfo?.url}
        alt=""
        x-if={videoInfo?.url}
      />
    </div>
  );
}
