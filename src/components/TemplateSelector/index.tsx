import React from 'react';
import { Radio } from 'tdesign-react';
import './index.less';

export function TemplateSelector(props: {
  label: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  style?: React.CSSProperties;
  options: {
    value: string;
    label: string;
  }[];
}) {
  const { label, value, className, options, onChange, style } = props;
  return (
    <div className={`flex template-selector ${className || ''}`} style={style}>
      <div className="label flex items-center" style={{ width: 86 }}>
        {label}
      </div>
      <div>
        <Radio.Group
          value={value}
          onChange={(v) => {
            onChange(v as string);
          }}
        >
          {options.map((option) => (
            <Radio.Button
              className="radio-button"
              key={option.value}
              value={option.value}
            >
              {option.label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
    </div>
  );
}
