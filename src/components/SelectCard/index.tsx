import React, { PropsWithChildren, useState } from 'react';
import './style.less';
import { Image, ImageProps } from 'tdesign-react';

interface IUIState {
  hover: boolean;
}
export interface ISelectCardProps<Data> {
  data: Data;
  image?: string;

  title: string;

  desc?: string;

  className?: string;
  style?: React.CSSProperties;
  active?: boolean;
  mine?: boolean;
  bottomStyle?: {
    container?: React.CSSProperties;
    title?: React.CSSProperties;
    description?: React.CSSProperties;
  };
  imageProps?: ImageProps;
  onSelect?: (
    data: Data,
    context: {
      el: HTMLElement;
    }
  ) => void;

  children?: (meta: { uiState: IUIState }) => React.ReactNode;
}

const prefix = 'select-card';

export function SelectCard<Data>(props: ISelectCardProps<Data>) {
  const {
    data,
    image,
    title,
    desc,
    className,
    style,
    mine,
    active,
    onSelect,
    bottomStyle,
    imageProps,
  } = props;

  const [uiState, setUiState] = useState<IUIState>({
    hover: false,
  });

  const cls = [`${prefix}__container`];
  if (active) {
    cls.push(`${prefix}__active`);
  }
  if (className) {
    cls.push(className);
  }
  return (
    <div
      className={cls.join(' ')}
      style={{ ...style }}
      onClick={(e) => onSelect?.(data, { el: e.target as HTMLElement })}
      onMouseEnter={() => setUiState((prev) => ({ ...prev, hover: true }))}
      onMouseLeave={() => setUiState((prev) => ({ ...prev, hover: false }))}
    >
      {mine && <div className={`${prefix}__mine`}>我的</div>}
      {image && (
        <Image
          {...imageProps}
          src={image}
          className={`${prefix}__image`}
          fit="contain"
        />
      )}
      <div
        className={`${prefix}__bottom`}
        style={{
          ...bottomStyle?.container,
        }}
      >
        <h3
          className={`${prefix}__bottom-title`}
          style={{
            ...bottomStyle?.title,
          }}
        >
          {title}
        </h3>
        {desc && (
          <span
            className={`${prefix}__bottom-desc`}
            style={{
              ...bottomStyle?.description,
            }}
          >
            {desc}
          </span>
        )}
      </div>
      {props.children?.({ uiState })}
    </div>
  );
}
