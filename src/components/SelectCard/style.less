@prefix: select-card;

.@{prefix} {
    &__container {
        width: calc(100% - 4px);
        margin-left: -2px;
        height: 248px;
        position: relative;
        border-radius: 8px;
        z-index: 0;
        box-sizing: content-box;
        overflow: hidden;
        padding: 2px;
    }
    &__mine {
        position: absolute;
        top:0;
        left:0;
        z-index: 10;
        width: 46px;
        height: 20px;
        border-radius: 8px 0 8px 0;
        background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%);
        line-height: 20px;
        color: #FFFFFF;
        text-align: center;
    }


    &__image {
        width: 100%;
        height: 168px;
        border-radius: 5px 5px 0 0;
        background: none;
    }

    &__bottom {
        //background: linear-gradient(97.99deg, #EBF4FF 12.3%, #F8F8FF 99.99%);
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-radius: 0 0 5px 5px;
        text-align: center;

        &-title {
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.9);
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }

        &-desc {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
        }
    }

    &__active {
        position: relative;
        &::after {
            position: absolute;
            top: -2px;
            bottom: -2px;
            left: -2px;
            right: -2px;
            background: linear-gradient(88.08deg, #0153FF -0.01%, #6E62FF 99.99%);
            content: '';
            z-index: -1;
            border-radius: 8px;
        }

        .@{prefix}__image {
            border-radius: 6px 6px 0 0;
        }

        .@{prefix}__bottom {
            background: linear-gradient(89.21deg, #0153FF -0.01%, #8649FF 147.74%) !important;

            &-title {
                color: #ffffff !important;
                font-weight: 600;
            }
        }
    }
}
