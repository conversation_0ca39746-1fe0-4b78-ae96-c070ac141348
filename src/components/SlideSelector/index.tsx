/**
 * <AUTHOR>
 * @date 2024/4/22 下午8:39
 * @desc 修改务必提CR
 */

import React, { useMemo } from 'react';
import './index.less';
import { Popup, Tag } from 'tdesign-react';

interface IProps {
  value?: string;
  options: SelectOption[];
  onChange?: (value: string) => void;
  disabled?: boolean;
  border?: boolean;
}

interface SelectOption {
  value: string;
  name: string;
  disable?: boolean;
  tips?: string;
}

export function SlideSelector(props: IProps) {
  const { options, value, onChange, disabled, border } = props;

  // 偏移量百分比
  const offset = useMemo(() => {
    // return 100 / options.length;
    return 100 / options.length;
  }, [options.length]);

  return (
    <div
      className="slide-selector-comp"
      style={border ? { border: '1px solid #E8E7EF' } : {}}
    >
      <div className="slide-selector-inner">
        <div
          className="slider"
          style={{
            left: `${
              options.findIndex((option) => option.value === value) * offset
            }%`,
            width: `${offset}%`,
          }}
        />
        <Popup trigger="hover" content={disabled ? '已锁定' : null}>
          <ul className="slide-option">
            {options.map((v) => (
              <li
                className={`slide-item ${
                  v.value === value ? 'active' : v.value
                }`}
                style={
                  disabled || v.disable
                    ? { color: 'rgba(0, 0, 0, 0.26)', cursor: 'not-allowed' }
                    : {}
                }
                key={v.value}
                onClick={() => {
                  if (disabled || v.disable) {
                    return;
                  }
                  onChange?.(v.value);
                }}
              >
                {v.name}
                {v.tips && (
                  <Tag
                    className="tags"
                    shape="round"
                    theme="warning"
                    variant="light"
                  >
                    {v.tips}
                  </Tag>
                )}
              </li>
            ))}
          </ul>
        </Popup>
      </div>
    </div>
  );
}
