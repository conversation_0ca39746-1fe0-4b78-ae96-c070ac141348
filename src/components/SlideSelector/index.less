.slide-selector-comp {
  width: 100%;
  height: 32px;
  //line-height: 32px;
  padding: 2px;
  background: linear-gradient(to right, rgba(244, 246, 255, 1), rgba(250, 245, 252, 1));
  border-radius: 3px;
  //overflow: hidden;

  .slide-selector-inner {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    //overflow: hidden;

    .slider,
    .slide-option {
      position: absolute;
      height: 26px;
    }

    .slider {
      background: #fff;
      transition: all ease-in-out 0.3s;
    }

    .slide-option {
      width: 100%;
      display: flex;
      z-index: 2;
    }

    .slide-item {
      position: relative;
      width: 100%;
      height: 100%;
      line-height: 28px;
      padding: 0 10px;
      color: rgba(0, 0, 0, 0.6);
      box-sizing: border-box;
      text-align: center;
      //overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      user-select: none;
      //background-color: rgba(255, 255, 255, .56);

      &.active {
        color: rgba(0, 71, 249, 1);
      }

      .tags {
        top: -16px;
        right: -2px;
        position: absolute;
      }
    }
  }
}
