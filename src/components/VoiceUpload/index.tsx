import { MATERIAL_TYPE } from '@/configs/upload';
import {
  UploadFilesParams,
  UploadRequestProps,
  uploadRequest,
} from '@/utils/cos';
import { convertFileSize } from '@/utils/file';
import { css } from '@emotion/react';
import { useMemoizedFn } from 'ahooks';
import moment from 'moment';
import React, {
  ForwardedRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Button,
  MessagePlugin,
  TdUploadProps,
  Upload,
  UploadFile,
} from 'tdesign-react';
import { UploadRef } from 'tdesign-react/es/upload/interface';
// import { uploadRequest } from '../../pages/VoiceList/voice/uploadRequest';

export interface IVoiceUploadSlotApi {
  triggerUpload: () => void;
  cancelUpload: () => void;
  doUpload: () => void;
  progress?: number;
  transcodeProgress?: number;
}
export interface IVoiceUploadProps
  extends Pick<React.HTMLAttributes<HTMLElement>, 'style' | 'className'>,
    Pick<UploadFilesParams, 'onBeforeTranscode' | 'onTranscodeProgress'> {
  accept: string;
  desc?: string | React.ReactElement;
  onFail?: TdUploadProps['onFail'];
  onSuccess?: TdUploadProps['onSuccess'];
  onFileUrlChange?: (url: string) => void;
  prefix?: string;
  addIcon?: boolean;
  //   表单能力
  value?: string;
  onChange?: (value: string) => void;
  disableAutoUpload?: boolean;
  children: (
    files: UploadFile[],
    api: IVoiceUploadSlotApi
  ) => {
    MainRender: React.ReactNode;
    BottomRender: React.ReactNode;
  };
  uploadFn?: UploadRequestProps;
  fileInitialStatus?: UploadFile['status'];
  ignoreErrorFiles?: boolean;
}

export interface IVoiceUploadInstance {
  getFiles: () => UploadFile[];
}

const VoiceUpload = forwardRef<IVoiceUploadInstance, IVoiceUploadProps>(
  (props, ref) => {
    const {
      onSuccess,
      onFail,
      onFileUrlChange,
      accept,
      desc,
      addIcon,
      onChange,
      prefix,
      children,
      disableAutoUpload,
      uploadFn,
      style,
      className,
      onBeforeTranscode,
      onTranscodeProgress,
      ignoreErrorFiles,
    } = props;
    const [files, setFiles] = useState<UploadFile[]>([]);
    const [uploadProgress, setUploadProgress] = useState<number>();
    const [transcodeProgress, setTranscodeProgress] = useState<number>();
    const [errText, setErrText] = useState('');
    const uploadAbortController = useRef<AbortController>();
    const file = useMemo(() => {
      return files[0];
    }, [files]);
    const status = useMemo(() => file?.status, [file]);
    const uploadDom = useRef<UploadRef>(null);
    const onFileTrigger = useCallback(() => {
      uploadDom.current?.triggerUpload();
    }, []);
    const cancelUpload = useMemoizedFn(() => {
      uploadDom.current?.cancelUpload();
      uploadAbortController.current?.abort('user abort upload');
      uploadAbortController.current = undefined;
      setUploadProgress(undefined);
      setTranscodeProgress(undefined);
    });
    const doUpload = useMemoizedFn(() => {
      if (!files || files.length === 0) return;
      uploadDom.current?.uploadFiles(files);
    });
    const renderNodes = useMemo(
      () =>
        children(files, {
          triggerUpload: onFileTrigger,
          progress: uploadProgress,
          cancelUpload,
          transcodeProgress,
          doUpload,
        }),
      [
        children,
        files,
        onFileTrigger,
        uploadProgress,
        cancelUpload,
        transcodeProgress,
        doUpload,
      ]
    );
    const requestFile = async (file: UploadFile) => {
      // await new Promise((resolve) => {
      //   setInterval(() => {
      //     setUploadProgress(1);
      //   }, 1000);
      // });
      // return {
      //   status: 'success',
      //   response: { url: 'https://www.qq.com', key: '' },
      // };
      let result;
      try {
        uploadAbortController.current?.abort?.('new upload begin');
        uploadAbortController.current = new AbortController();
        if (uploadFn) {
          result = await uploadFn(
            MATERIAL_TYPE.FILE,
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            file.raw!,
            0,
            () => void 0,
            {
              onProgress: (progress) => {
                console.log('文件上传进度', progress.percent);
                setUploadProgress(Math.round(progress.percent * 100));
              },
            }
          );
          console.log(result.url);
          onChange?.(result.url);
        } else {
          result = await uploadRequest(
            MATERIAL_TYPE.FILE,
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            file.raw!,
            0,
            () => void 0,
            {
              onProgress: (progress) => {
                console.log('文件上传进度', progress.percent);
                setUploadProgress(Math.round(progress.percent * 100));
              },
              abortSignal: uploadAbortController.current.signal,
              onBeforeTranscode: (data) => {
                setTranscodeProgress(1);
                setUploadProgress(undefined);
                onBeforeTranscode?.(data);
              },
              onTranscodeProgress: (data) => {
                setTranscodeProgress(Math.max(1, data.progress));
                onTranscodeProgress?.(data);
              },
            }
          );
        }
      } catch (err) {
        return { status: 'fail', error: err };
      }
      return {
        status: result.code === 200 ? 'success' : 'fail',
        response: { url: result.url, cosKey: result.key },
      } as const;
    };

    useImperativeHandle(ref, () => ({
      getFiles: () => files,
    }));

    return (
      <div
        css={css`
          width: 411px;
          height: 360px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: #ffffff;
          border-radius: 8px;
        `}
        style={{ ...style }}
        className={[...(className ? [className] : [])].join(' ')}
      >
        <Upload
          accept={accept}
          ref={uploadDom}
          theme="custom"
          files={files}
          style={{ zIndex: '-1', position: 'absolute' }}
          autoUpload={!disableAutoUpload}
          onChange={(file) => {
            if (file.length === 1 && uploadDom.current) {
              // eslint-disable-next-line no-param-reassign
              file[0].uploadTime = moment().format('YYYY-MM-DD');
            }
            if (ignoreErrorFiles && file[0].status === 'fail') return;
            setFiles(file);
          }}
          onSelectChange={(files) => {
            setUploadProgress(undefined);
            setTranscodeProgress(undefined);
            const newFiles = files.map((file) => ({
              raw: file,
              name: file.name,
              status: disableAutoUpload
                ? props.fileInitialStatus ?? 'success'
                : 'progress',
              size: file.size,
              type: file.type,
            }));
            setFiles(newFiles);
          }}
          onFail={(options) => {
            onFail?.(options);
            setUploadProgress(undefined);
            setTranscodeProgress(undefined);
            if (!ignoreErrorFiles) {
              setFiles([...options.failedFiles]);
              setErrText(options.response.error.message);
            }
            onFileUrlChange?.('');
          }}
          onSuccess={(options) => {
            onSuccess?.(options);
            const url = `${
              /^https:/.test(options.response.url)
                ? options.response.url
                : `${prefix}${options.response.url}`
            }`;
            onFileUrlChange?.(url);
            onChange?.(url);
          }}
          requestMethod={(file) => {
            return requestFile(file);
          }}
        />
        {/* 总的父容器 */}
        <div
          css={css`
            width: 100%;
            height: 100%;
            display: flex;
            padding: 20px;
            flex-direction: column;
            position: relative;
          `}
        >
          {renderNodes?.MainRender}
          {/* 底部：无文件出现 */}
          <div
            style={{ display: files[0] ? 'none' : 'block' }}
            css={css`
              position: absolute;
              bottom: 20px;
              left: 20px;
            `}
          >
            <div
              css={css`
                display: flex;
                align-items: center;
                width: 100%;
                justify-content: space-between;
                background: linear-gradient(
                  87.64deg,
                  #e0eaff 0%,
                  #e2efff 46.96%,
                  #f5f3ff 99.81%
                );
                align-items: center;
                padding: 6px 16px 6px 8px;
                border-radius: 50px;
              `}
            >
              <svg
                style={{
                  marginLeft: 4,
                  marginRight: 4,
                }}
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  width="24"
                  height="24"
                  rx="12"
                  fill="url(#paint0_linear_7877_22631)"
                />
                <path
                  d="M12 5C10.6247 5 9.36829 5.5546 8.46644 6.46644C7.5546 7.36829 7 8.6247 7 10C7 11.3746 7.554 12.6304 8.46498 13.5321C8.77388 13.8494 9.12363 14.1126 9.5 14.3289V16C9.5 16.5523 9.94772 17 10.5 17H13.5C14.0523 17 14.5 16.5523 14.5 16V14.3289C14.8764 14.1126 15.2261 13.8493 15.535 13.5321C16.446 12.6304 17 11.3746 17 10C17 7.23386 14.7661 5 12 5ZM9.17572 7.17138C9.89371 6.44452 10.8964 6 12 6C14.2139 6 16 7.78614 16 10C16 11.1036 15.5555 12.1063 14.8286 12.8243L14.8214 12.8316C14.5255 13.1362 14.173 13.3845 13.7764 13.5828L13.5 13.721V16H10.5V13.721L10.2236 13.5828C9.82703 13.3845 9.47452 13.1362 9.17864 12.8316L9.17138 12.8243C8.44452 12.1063 8 11.1036 8 10C8 8.8964 8.44452 7.89371 9.17138 7.17572L9.17572 7.17138Z"
                  fill="#0047F9"
                />
                <path d="M9.5 18V19H14.5V18H9.5Z" fill="#0047F9" />
                <defs>
                  <linearGradient
                    id="paint0_linear_7877_22631"
                    x1="9.13151e-07"
                    y1="24"
                    x2="23.5349"
                    y2="27.3014"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#EBF4FF" />
                    <stop offset="1" stopColor="#F8F8FF" />
                  </linearGradient>
                </defs>
              </svg>
              {renderNodes?.BottomRender}
            </div>
          </div>
          {/* 底部：有文件出现 */}
          <div
            style={{ display: files[0] ? 'flex' : 'none' }}
            css={css`
              display: flex;
              flex-direction: column;
              align-items: start;
              position: absolute;
              bottom: 20px;
              left: 20px;
            `}
          >
            <div
              css={css`
                margin: 20px 0;
                color: #00000066;
              `}
            >
              <div
                style={{
                  color: '#E25050',
                  fontSize: '12px',
                  fontWeight: '400',
                }}
                css={css`
                  width: 200px;
                  word-wrap: break-word;
                  white-space: pre-wrap;
                  overflow: hidden;
                `}
              >
                {errText}
              </div>
              <div>文件大小：{convertFileSize(file?.size || 0)}</div>
              <div className="file-msg">
                上传日期：{file?.uploadTime || '-'}
              </div>
            </div>

            <div className="flex" style={{ gap: '8px' }}>
              {status !== 'progress' && (
                <div
                  style={{ color: '#0052D9', cursor: 'pointer' }}
                  onClick={(e) => {
                    cancelUpload();
                    setFiles([]);
                    onFileUrlChange?.('');
                    setTimeout(() => {
                      uploadDom.current?.triggerUpload();
                    }, 100);
                    e.preventDefault();
                  }}
                >
                  重新上传
                </div>
              )}

              {/* {status !== 'progress' && (
                <div
                  style={{ color: '#0052D9', cursor: 'pointer' }}
                  onClick={(e) => {
                    setFiles([]);
                    onFileUrlChange?.('');
                    e.stopPropagation();
                  }}
                >
                  {status === 'waiting' ? '取消上传' : '删除'}
                </div>
              )} */}
              <div
                style={{ color: '#0052D9', cursor: 'pointer' }}
                onClick={(e) => {
                  // uploadDom.current?.cancelUpload();
                  cancelUpload();
                  setFiles([]);
                  onFileUrlChange?.('');
                  e.stopPropagation();
                }}
              >
                {status === 'progress' ? '取消上传' : '删除'}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default VoiceUpload;
