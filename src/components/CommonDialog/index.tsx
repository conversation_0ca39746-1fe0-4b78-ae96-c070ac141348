/**
 * <AUTHOR>
 * @date 2024/10/28 18:00
 * @desc 通用弹窗
 */

import React, { Fragment, ReactElement } from 'react';
import ActionSuccess from '@/assets/images/create-condition-success.png';
import ActionError from '@/assets/images/fail.png';
import { Button } from 'tdesign-react';

interface IProps {
  theme?: 'success' | 'error';
  icon?: ReactElement;
  title: string;
  desc?: string;
  onCancel?: () => void;
  onConfirm?: () => void;
  confirmText?: string | ReactElement;
  cancelText?: string;
}

export function CommonDialog(props: IProps) {
  const {
    title,
    icon,
    theme,
    desc,
    onCancel,
    onConfirm,
    confirmText,
    cancelText,
  } = props;
  return (
    <div className="pagedoo-meta-live-global flex flex-col items-center">
      <Fragment x-if={!!icon}>{icon}</Fragment>
      <img
        x-else
        style={{ height: '113px', width: '138px', marginTop: '24px' }}
        src={theme === 'error' ? ActionError : ActionSuccess}
        alt=""
      />

      <div
        className="mt-[36px] text-center	text-[#000]"
        style={{ fontSize: '16px', fontWeight: 500 }}
      >
        {title}
      </div>
      <div
        x-if={desc}
        className="mt-[17px] text-center"
        style={{ color: 'rgba(0, 0, 0, 0.6)' }}
      >
        {desc}
      </div>

      <div className="flex justify-around mt-[36px] gap-[24px]">
        <Button
          x-if={cancelText}
          theme="default"
          className="gradient-default"
          onClick={onCancel}
        >
          {cancelText}
        </Button>
        <Fragment x-if={confirmText}>
          {typeof confirmText === 'string' ? (
            <Button
              x-if={confirmText}
              theme="primary"
              className="gradient-primary"
              onClick={() => {
                onConfirm?.();
              }}
            >
              {confirmText}
            </Button>
          ) : (
            confirmText
          )}
        </Fragment>
      </div>
    </div>
  );
}
