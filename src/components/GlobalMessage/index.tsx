// 封装tdesign MessagePlugin 方法为React组件

import { useMount, useUnmount } from 'ahooks';
import { useState } from 'react';
import { MessageOptions, MessagePlugin } from 'tdesign-react';

export function GlobalMessage(props: MessageOptions) {
  const [messageInstance, setMessageInstance] =
    useState<ReturnType<typeof MessagePlugin>>();
  useMount(() => {
    const inst = MessagePlugin(props.theme || 'info', props);
    setMessageInstance(inst);
  });
  useUnmount(() => {
    if (messageInstance) {
      messageInstance.then((i) => i.close());
    }
  });
  return null;
}
