.template-dialog {
  .t-radio-group {
    border-radius: 3px;
    background: var(--Gray-Gray5-normal, linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%));
  }

  .t-radio-group.t-radio-group--filled .t-radio-button::before {
    width: 0;
    height: 0;
  }

  .t-radio-group.t-radio-group--filled .t-radio-button.t-is-checked {
    color: #0047F9;
  }

  .template-item {
    width: calc(25% - 17px);
    margin: 6px 22px 6px 0px;
    border-radius: 3px;
    box-sizing: border-box;
    border: 3px solid transparent;

    &:nth-child(4n) {
      margin-right: 0;
    }

    &.selected {
      border-color: #0047F9;
    }

    .template-poster {
      aspect-ratio: 180 / 320;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      border-radius: 4px 4px 0 0;
      background-color: #000;
    }

    .template-name {
      border-radius: 0 0 4px 4px;
      height: 38px;
      line-height: 38px;
      color: rgba(0, 0, 0, 0.9);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
      background: linear-gradient(97.99deg, #ebf4ff 12.3%, #f8f8ff 99.99%);
    }
  }
}

.choose-size-item {
  padding: 30px 18px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 4px;
  background: var(--44, linear-gradient(85deg, #F4F6FF 0%, #FAF5FC 100%));
  cursor: pointer;

  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    width: 75px;
    height: 75px;
    border-radius: 50%;
    background-color: #fff;
  }

  &-title {
    color: var(--text-icon-font-gy-190-primary, rgba(0, 0, 0, 0.90));
    font-weight: 600;
  }

  &-desc {
    color: rgba(0, 0, 0, 0.4);
    font-size: 12px;
  }
}