import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  Dialog,
  Radio,
  Input,
  Space,
  Loading,
  Row,
  Col,
  Pagination,
  DialogPlugin,
} from 'tdesign-react';
import './index.less';
import {
  TemplateDataItem,
  useGetTemplateList,
  useTemplateSelectorOptions,
} from '@/hooks/template';
import { SearchIcon } from 'tdesign-icons-react';
import { Empty } from '../Empty';
import { useDebounce } from 'ahooks';
import { CONTENT_TYPE_MAP } from '@/const/common';
import ICONSVG from './horizontal-icon.svg';

function ItemPoster(props: { url: string }) {
  const { url } = props;
  const [blobUrl, setBlobUrl] = useState<string>();
  const ref = useRef<HTMLDivElement>(null);
  useMemo(() => {
    if (!url) return;
    const img = new Image();
    (async () => {
      const resp = await fetch(url);
      const blob = await resp.blob();
      const u = window.URL.createObjectURL(blob);
      setBlobUrl(u);
      await new Promise<void>((resolve, reject) => {
        img.onload = () => {
          img.onload = null;
          if (ref.current) {
            ref.current.style.backgroundImage = `url(${u})`;
          }
          resolve();
        };
        img.onerror = reject;
        img.src = u;
      });
    })()
      .catch(() => {
        if (ref.current) {
          ref.current.style.backgroundImage = `url(${url})`;
        }
      })
      .finally(() => {
        if (ref.current) {
          ref.current.style.opacity = '1';
        }
      });
  }, [url]);

  useEffect(
    () => () => {
      if (blobUrl) {
        window.URL.revokeObjectURL(blobUrl);
      }
    },
    [blobUrl]
  );

  return (
    <div
      ref={ref}
      className="template-poster transition-opacity"
      style={{
        opacity: 0,
        // backgroundImage: `url(${template.poster_url})`,
      }}
    />
  );
}

export function TemplateDialog(props: {
  application_scenarios: string;
  type: string;
  visible: boolean;
  onClose?: () => void;
  onCreateWithoutTemplate?: () => void;
  onCreateWithTemplate?: (template: TemplateDataItem) => void;
}) {
  const {
    type,
    application_scenarios,
    visible,
    onClose,
    onCreateWithoutTemplate,
    onCreateWithTemplate,
  } = props;
  const { selectorOption } = useTemplateSelectorOptions(
    type as (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value']
  );
  const selectorList = useMemo(
    () =>
      selectorOption?.options.find((i) => i.value === application_scenarios)
        ?.childenSelector,
    [selectorOption, application_scenarios]
  );
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [selectorValue, setSelectorValue] = useState<Record<string, string>>(
    {}
  );
  const [searchKey, setSearchKey] = useState('');
  const debouncedSearchKey = useDebounce(searchKey, { wait: 500 });
  const { loading, list, count } = useGetTemplateList(
    selectorValue,
    pageNum,
    pageSize,
    type as (typeof CONTENT_TYPE_MAP)[keyof typeof CONTENT_TYPE_MAP]['value'],
    debouncedSearchKey
  );
  const [selectItem, setSelectItem] = useState<TemplateDataItem>();

  // 初始化默认值
  useEffect(() => {
    if (!selectorList || !visible) return;
    const value: typeof selectorValue = {
      application_scenarios,
    };
    selectorList.map((selector) => {
      value[selector.selectorId] = selector.options[0].value;
    });
    setSelectorValue(value);
  }, [selectorList, application_scenarios, visible]);

  // 只要loading就证明再发请求，则将选中的模板清空
  useEffect(() => {
    if (loading) {
      setSelectItem(undefined);
    }
  }, [loading]);

  return (
    <Dialog
      header="请从下方选择一个模板（可跳过）"
      visible={visible}
      className="template-dialog"
      width={860}
      onClose={onClose}
      footer={
        <div className="pagedoo-meta-live-global">
          <Button
            className="gradient-default"
            theme="default"
            onClick={onClose}
          >
            取消
          </Button>
          <Button
            className="gradient-default"
            theme="default"
            onClick={onCreateWithoutTemplate}
          >
            跳过，直接创建
          </Button>
          <Button
            className="gradient-primary"
            disabled={!selectItem}
            onClick={() => {
              if (selectItem) {
                onCreateWithTemplate?.(selectItem);
              }
            }}
          >
            开始创建
          </Button>
        </div>
      }
    >
      <div>
        <div
          className="flex items-center gap-2 justify-between"
          style={{ marginBottom: 6 }}
        >
          {selectorList?.map((selector) => (
            <Radio.Group
              key={selector.selectorId}
              variant="default-filled"
              value={selectorValue[selector.selectorId]}
              onChange={(v) => {
                setSelectorValue((pre) => {
                  return {
                    ...pre,
                    [selector.selectorId]: v as string,
                  };
                });
                setPageNum(1);
              }}
            >
              {selector.options.map((option) => (
                <Radio.Button key={option.value} value={option.value}>
                  {option.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          ))}
          <Input
            value={searchKey}
            onChange={(v) => {
              setSearchKey(v);
              setPageNum(1);
            }}
            placeholder="请输入模板名称"
            className="flex-1 max-w-[364px]"
            suffixIcon={<SearchIcon />}
          />
        </div>
        <div style={{ height: 400, overflowY: 'auto' }}>
          {loading ? (
            <Space className="flex-center h-full">
              <Loading text="正在获取模版列表..." loading size="small" />
            </Space>
          ) : (
            <Row className="h-full">
              {list.length === 0 ? (
                <div className="flex-center h-full w-full">
                  <Empty>暂无数据</Empty>
                </div>
              ) : (
                <>
                  {list.map((template) => (
                    <Col
                      className={`template-item ${
                        selectItem?.id === template.id ? 'selected' : ''
                      }`}
                      key={template.id}
                    >
                      <div onClick={() => setSelectItem(template)}>
                        <ItemPoster url={template.poster_url} />
                        {/* <div
                          className="template-poster"
                          style={{
                            backgroundImage: `url(${template.poster_url})`,
                          }}
                        /> */}
                        <div className="template-name">
                          <span>{template.name}</span>
                        </div>
                      </div>
                    </Col>
                  ))}
                </>
              )}
            </Row>
          )}
        </div>
        <Pagination
          size="small"
          style={{ paddingTop: '10px' }}
          showJumper
          current={pageNum}
          pageSize={pageSize}
          pageSizeOptions={[12, 24, 48, 96]}
          total={count}
          onChange={(pageInfo) => {
            setPageNum(pageInfo.current);
            setPageSize(pageInfo.pageSize);
          }}
        />
      </div>
    </Dialog>
  );
}

export const showSelectDeviceSizeModel = (props: {
  onChoose: (id: string) => void;
  onClose?: () => void;
}) => {
  const list = [
    {
      id: '1',
      title: '横屏',
      icon: <img src={ICONSVG} alt="" className="rotate-90" />,
      desc: '默认比例16:9，编辑器内可调整',
    },
    {
      id: '2',
      title: '竖屏',
      icon: <img src={ICONSVG} alt="" />,
      desc: '默认比例9:16，编辑器内可调整',
    },
  ];
  const sizeDialog = DialogPlugin.confirm({
    header: '请选择画面类型',
    body: (
      <div className="flex justify-between">
        {list.map((item) => (
          <div
            className="choose-size-item"
            key={item.id}
            onClick={() => {
              props.onChoose(item.id);
              sizeDialog.hide();
            }}
          >
            <div className="choose-size-item-icon">{item.icon}</div>
            <div className="choose-size-item-title">{item.title}</div>
            <div className="choose-size-item-desc">{item.desc}</div>
          </div>
        ))}
      </div>
    ),
    footer: null,
    closeOnOverlayClick: false,
    placement: 'center',
    width: 480,
    onClose: () => {
      sizeDialog.hide();
      props.onClose?.();
    },
  });
};
