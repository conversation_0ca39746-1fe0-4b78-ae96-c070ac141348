import { Login<PERSON>tate<PERSON>tom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { GenerateQRCode } from '@/pb/api/DigitalManProc';
import { useMount, useRequest } from 'ahooks';
import to from 'await-to-js';
import React, { useEffect, useLayoutEffect } from 'react';
import { useRecoilValue } from 'recoil';
import Dialog from 'tdesign-react/es/dialog/Dialog';
import { BaseQRCode } from '../BaseQRCode';
import { Button } from 'tdesign-react';
import { css } from '@emotion/css';
import { useAIServerCode } from './hooks/useAIServerCode';
import { ErrorCircleIcon, ErrorIcon } from 'tdesign-icons-react';

interface IAIServerCodeProps {
  visible: boolean;
  onClose: () => void;
}
export default function AIServerCode(props: IAIServerCodeProps) {
  const { visible, onClose } = props;
  const { qrcodeUrl, loading, error, fetchCode } = useAIServerCode();
  useMount(() => {
    fetchCode();
  });
  // useLayoutEffect(() => {
  //   if (!visible) return;
  // }, [visible]);
  return (
    <Dialog
      visible={visible}
      onClose={onClose}
      destroyOnClose
      width={500}
      footer={
        <div className="flex justify-center">
          <div
            className={css`
            background: linear-gradient(88.08deg, #0055FF -0.01%, #0081FF 49.89%, #C7A2FF 99.99%);
            linear-gradient(88.08deg, color(display-p3 0.004 0.325 1.000) -0.01%, color(display-p3 0.180 0.498 0.992) 49.89%, color(display-p3 0.757 0.639 0.992) 99.99%);
              width: Fixed (126px)px;
              height: Fixed (32px)px;
              padding: 5px 16px 5px 16px;
              gap: 8px;
              border-radius: 4px 0px 0px 0px;
              color:white;
              cursor: pointer;
            `}
            onClick={() => {
              onClose();
            }}
          >
            我知道了
          </div>
        </div>
      }
    >
      <div
        className="flex justify-center items-center"
        style={{
          minHeight: '320px',
        }}
      >
        {!!!error ? (
          <BaseQRCode
            loadingStyle={{
              height: '300px',
              width: '300px',
            }}
            style={{
              gap: '20px',
              minHeight: '320px',
            }}
            imgStyle={{
              height: '300px',
              width: '300px',
              borderRadius: '8px',
            }}
            qrCode={qrcodeUrl || ''}
            loading={loading}
          />
        ) : (
          <>
            <ErrorCircleIcon color="red" />
            <span>
              &nbsp;加载失败，
              <span
                onClick={() => {
                  fetchCode();
                }}
                className={css`
                  color: blue;
                  cursor: pointer;
                `}
              >
                重新加载
              </span>
            </span>
          </>
        )}
      </div>
    </Dialog>
  );
}
