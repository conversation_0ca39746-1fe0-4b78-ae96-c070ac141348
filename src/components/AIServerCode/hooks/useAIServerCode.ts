import { Login<PERSON>tate<PERSON>tom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import { GenerateQRCode } from '@/pb/api/DigitalManProc';
import { useMemoizedFn, useRequest, useUnmount } from 'ahooks';
import to from 'await-to-js';
import { useMemo, useRef } from 'react';
import { useRecoilValue } from 'recoil';

export const useAIServerCode = (options?: {
  autoRefresh?: boolean;
  autoRefreshInterval?: number;
}) => {
  const autoRefreshInterval = options?.autoRefreshInterval ?? 1000 * 60 * 5;
  const {
    data: qrcodeUrl,
    error,
    cancel,
    loading,
    runAsync: fetchCode,
  } = useRequest(
    async () => {
      const res = await GenerateQRCode({
        fan_code: 'adq_AIGC_miaobo',
      });
      // throw new Error('error');
      if (res?.qrcode_url) {
        return res.qrcode_url;
      }
    },
    {
      manual: true,
    }
  );
  const abortRef = useRef<AbortController>();

  const startAutoRefresh = useMemoizedFn(() => {
    let refreshTimeout: NodeJS.Timeout | null = null;
    abortRef.current = new AbortController();
    const abortCtrl = abortRef.current;
    abortRef.current.signal.addEventListener('abort', () => {
      cancel();
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
        refreshTimeout = null;
      }
    });
    const doRefresh = () => {
      fetchCode().finally(() => {
        if (!refreshTimeout) return;
        if (abortCtrl.signal.aborted) return;
        refreshTimeout = setTimeout(doRefresh, autoRefreshInterval);
      });
    };
    refreshTimeout = setTimeout(doRefresh, autoRefreshInterval);
  });

  const stopAutoRefresh = useMemoizedFn(() => {
    if (abortRef.current) {
      abortRef.current.abort();
      abortRef.current = undefined;
    }
  });

  const destroy = useMemoizedFn(() => {
    cancel();
    stopAutoRefresh();
  });

  const initCode = useMemoizedFn(async () => {
    cancel();
    return fetchCode().then(() => {
      if (options?.autoRefresh) startAutoRefresh();
    });
  });

  useUnmount(() => destroy());

  const res = useMemo(() => {
    return {
      qrcodeUrl,
      fetchCode,
      error,
      cancel,
      loading,
      initCode,
      startAutoRefresh,
      stopAutoRefresh,
    };
  }, [
    qrcodeUrl,
    fetchCode,
    error,
    cancel,
    loading,
    initCode,
    startAutoRefresh,
    stopAutoRefresh,
  ]);

  return res;
};
