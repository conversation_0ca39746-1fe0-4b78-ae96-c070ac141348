import { useMemoizedFn } from 'ahooks';
import { ResourceSvr } from '../../pb/pb';
import { useCallback } from 'react';
import type { CategoryHandlerType, CategoryType } from './typings';
export const useResourceCategory = (
  // ● 贴片：patch
  // ● 背景贴片：background-patch
  // ● 花字：flower-character
  // ● 组件动画：component-gif
  // ● 转场动画：transition-gif
  // ● 动画贴纸：special-effects-gif
  // ● 背景音乐：background-music
  // ● 视频：video
  // ● 模板：template
  //   数字人: dip
  //    音色：voice
  type:
    | 'patch'
    | 'background-patch'
    | 'flower-character'
    | 'component-gif'
    | 'transition-gif'
    | 'special-effects-gif'
    | 'background-music'
    | 'video'
    | 'template'
    | 'voice'
    | 'featured-sound-effects'
    | 'dip'
) => {
  const onCategoryList = useMemoizedFn(() =>
    ResourceSvr.GetCategoryInfoList({
      category_type: type,
    }).then((res) => {
      const all: CategoryType = {
        id: 'all',
        name: '全部',
        children: [
          {
            id: 'all',
            name: '全部',
            children: [],
          },
        ],
      };
      const category: CategoryType[] = [all];
      for (const item of res.category_info_list) {
        let gory = category.find((i) => i.id === item.category_level1);
        if (!gory) {
          // 一级分类的children 添加all 这个二级分类
          gory = {
            id: item.category_level1,
            name: item.category_level1_name,
            children: [
              {
                id: 'all',
                name: '全部',
                children: [],
              },
            ],
          };
          category.push(gory);
        }
        const list = gory;
        list.children?.push({
          id: item.category_level2,
          name: item.category_level2_name,
        });
        if (!all.children?.find((i) => i.id === item.category_level2)) {
          all.children?.push({
            id: item.category_level2,
            name: item.category_level2_name,
          });
        }
      }
      return category;
    })
  );
  const flattenCategory = useCallback((category: CategoryHandlerType[]) => {
    const categories: CategoryHandlerType[] = [];
    const recursive = (category: CategoryHandlerType[]) => {
      const selected = category.find((c) => c.selected);
      if (selected) {
        categories.push(selected);
        if (Array.isArray(selected.children) && selected.children.length > 0) {
          recursive(selected.children);
        }
      }
    };
    // debugger;
    recursive(category);
    console.log('flattenCategory', categories);
    return categories;
  }, []);
  return {
    onCategoryList,
    flattenCategory,
    // onResourceList,
  };
};
