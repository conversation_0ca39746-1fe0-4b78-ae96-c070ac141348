import React from 'react';
import { CheckCircleFilledIcon } from 'tdesign-icons-react';

export function CategoryButton(
  props: {
    children?: React.ReactNode;
    selected?: boolean;
    checkedStyle?: 'default' | 'filled';
  } & React.HTMLAttributes<HTMLDivElement>
) {
  const { children, selected, checkedStyle, ...rest } = props;
  let checkedIcon = (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.47613 7.14883C2.28087 7.34409 2.28087 7.66068 2.47613 7.85594L6.22141 11.6012C6.41667 11.7965 6.73325 11.7965 6.92852 11.6012C6.94961 11.5801 6.96842 11.5576 6.98496 11.534L13.6559 4.86304C13.8512 4.66778 13.8512 4.3512 13.6559 4.15594C13.4606 3.96068 13.1441 3.96068 12.9488 4.15594L6.56957 10.5352L3.18323 7.14883C2.98797 6.95357 2.67139 6.95357 2.47613 7.14883Z"
        fill="#0047F9"
      />
    </svg>
  );
  if (checkedStyle === 'filled') {
    checkedIcon = (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          width="16"
          height="16"
          rx="8"
          fill="url(#paint0_linear_1970_188133)"
        />
        <path
          d="M6.43593 9.92188L12.199 4.15884C12.4528 3.905 12.8644 3.905 13.1182 4.15884C13.372 4.41268 13.372 4.82424 13.1182 5.07808L7.14303 11.0532C6.75251 11.4438 6.11934 11.4438 5.72882 11.0532L2.43618 7.76061C2.18234 7.50677 2.18234 7.09522 2.43618 6.84138C2.69002 6.58753 3.10158 6.58753 3.35542 6.84138L6.43593 9.92188Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_1970_188133"
            x1="1.74723e-06"
            y1="16"
            x2="15.7712"
            y2="17.8997"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#0047F9" />
            <stop offset="1" stopColor="#7386FF" />
          </linearGradient>
        </defs>
      </svg>
    );
  }
  return (
    <div
      style={{
        padding: selected ? '0 18px' : '0 27px',
        height: 38,
        background: selected
          ? 'linear-gradient(87.64deg, #E0EAFF 0%, #E2EFFF 46.96%, #F5F3FF 99.81%)'
          : 'linear-gradient(82.56deg, #F6F7FB -0.02%, #FBF8FB 99.98%)',
        borderRadius: 50,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 4,
        fontWeight: selected ? 600 : 400,
        fontSize: 14,
        lineHeight: '22px',
        textAlign: 'center',
        color: selected ? '#0047F9' : 'rgba(0, 0, 0, 0.6)',
        cursor: 'pointer',
      }}
      {...rest}
    >
      {selected && checkedIcon}

      {props.children}
    </div>
  );
}
