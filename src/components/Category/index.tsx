import { useMemoizedFn } from 'ahooks';
import { CategoryHandlerType, CategoryType } from './typings';
import { startTransition, useEffect, useMemo, useState } from 'react';
import { useResourceCategory } from '@/components/Category/useCategory';
import { Loading } from 'tdesign-react';
import { css } from '@emotion/react';

export function Category(props: {
  defaultCategory?: string[];
  onCategoryList: () => Promise<CategoryType[]>;
  children?: ({
    category,
  }: {
    category: CategoryHandlerType[] | Error | undefined;
  }) => React.ReactElement;
}) {
  const onCategoryList = useMemoizedFn(props.onCategoryList);
  // 当前选中的目录
  const [selectedCategory, setSelectedCategory] = useState<string[]>(
    props.defaultCategory || []
  );
  // 总目录
  const [allCategory, setAllCategory] = useState<
    CategoryType[] | Error | undefined
  >([]);
  useEffect(() => {
    setAllCategory(undefined);
    onCategoryList()
      .then(setAllCategory)
      .catch((err) => {
        setAllCategory(new Error(`获取目录失败, err:${err.message}`));
      });
    return () => {
      setAllCategory([]);
      setSelectedCategory([]);
    };
  }, [onCategoryList]);
  const categoryHandler = useMemo<CategoryHandlerType[] | undefined>(() => {
    if (!allCategory) return;
    if (allCategory instanceof Error) return;
    const handle = (
      res: CategoryType[],
      category: string[]
    ): CategoryHandlerType[] =>
      res.map((i) => {
        const key = [...category, i.id];
        return {
          ...i,
          selected:
            key.join(',') === selectedCategory.slice(0, key.length).join(','),
          onSelect: () => {
            // 默认选中子的第一个
            setSelectedCategory([
              ...key.slice(),
              ...(i.children?.[0] ? [i.children[0]?.id] : []),
            ]);
          },
          children: handle(i.children || [], key),
        };
      });
    return handle(allCategory, []);
  }, [allCategory, selectedCategory]);
  return props.children?.({ category: categoryHandler }) || null;
}

export function CommonCategoryWrapper(props: {
  categoryType: Parameters<typeof useResourceCategory>[0];
  defaultCategory?: string[];
  renderCategoryList: (options: {
    categoryList: {
      value: CategoryHandlerType | undefined;
      data: CategoryHandlerType[];
    }[];
  }) => React.ReactNode;
  children?: (data: {
    categories: string[];
    categoryType: string;
  }) => React.ReactNode;
}) {
  const { categoryType, children, defaultCategory } = props;
  const { onCategoryList, flattenCategory } = useResourceCategory(categoryType);

  return (
    <Category defaultCategory={defaultCategory} onCategoryList={onCategoryList}>
      {({ category }) => {
        if (category instanceof Error) return <span>{category.message}</span>;
        if (!category || !Array.isArray(category)) return <Loading />;
        if (category.length === 0) return <> </>;
        console.log('render category ', category);
        // const hasSecondCategory =
        const secondChildren = category.find((i) => i.selected)?.children;
        const hasSecond =
          Array.isArray(secondChildren) && secondChildren.length > 0;
        return (
          <>
            {props.renderCategoryList({
              categoryList: [
                {
                  value: category.filter((c) => c.selected)[0],
                  data: category,
                },
                ...(hasSecond
                  ? [
                      {
                        data: category.find((i) => i.selected)?.children || [],
                        value: category
                          .find((i) => i.selected)
                          ?.children?.filter((item) => item.selected)?.[0],
                      },
                    ]
                  : []),
              ],
            })}
            {children?.({
              categories: flattenCategory(category).map((c) => c.id),
              categoryType,
            })}
          </>
        );
      }}
    </Category>
  );
}
