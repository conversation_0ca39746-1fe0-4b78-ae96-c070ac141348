import React, { useState } from 'react';
import { CustomFileListProps, FileItemType } from './type';
import { uploadRequest } from '@/utils/cos';
import CustomUpload from '.';
import { MessagePlugin } from 'tdesign-react';

const defaultValue = [
  {
    url: '//dev-avatarcdn.pay.qq.com/material/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4',
  },
];

export function ExampleUsageUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<Partial<FileItemType>[]>(
    []
  );

  const handleFilesChange = (files: FileItemType[]) => {
    setUploadedFiles(files);
    console.log('!!!onChanges', files);
  };

  const beforeUpload = async (file: File) => {
    // 例如：检查文件类型
    const isVideoOrImage =
      file.type.startsWith('video/') || file.type.startsWith('image/');
    if (!isVideoOrImage) {
      MessagePlugin.error('You can only upload video or image files!');
    }
    return isVideoOrImage;
  };

  const locale = {
    uploadButtonText: '上传文件',
    reuploadText: '重新上传',
    deleteText: '删除',
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold">Custom Upload Component</h1>
      <CustomUpload
        type="video"
        multiple
        accept="video/*"
        maxSize={10 * 1024 * 1024} // 10MB
        onChange={handleFilesChange}
        value={uploadedFiles}
        beforeUpload={beforeUpload}
        uploadRequest={uploadRequest}
        customUploadButton={<div>点我</div>}
        customFileList={CustomFileList2}
        locale={locale}
        wrapperClassName="custom-upload-wrapper"
      />
    </div>
  );
}

function CustomFileList2(props: CustomFileListProps) {
  const { files, onDelete, onReupload } = props;
  return (
    <div>
      <ul>
        {files.map((file) => (
          <li key={file._uid}>
            {file.name} - {file.status}
            <button>Delete</button>
            <button>Reupload</button>
          </li>
        ))}
      </ul>
    </div>
  );
}
