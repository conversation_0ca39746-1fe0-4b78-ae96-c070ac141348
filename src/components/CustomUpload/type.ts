import { EnumToUnion } from '@/utils/template/type';
import { UploadStatus } from './constants';
import { UploadRequest } from '../Upload/type';
import React, { ReactNode } from 'react';
import { Locale } from './locale';

/**
 * @description 单个文件项的类型
 * @example { uid: 'unique-id-1', raw: new File([], 'example.mp4'), name: 'example.mp4', size: 1024, type: 'video/mp4', percent: 100, status: 'success', url: 'https://example.com/example.mp4' }
 */
export interface FileItemType {
  /**
   * @description 文件唯一标识符
   * @example 'unique-id-1'
   */
  _uid: string;

  /**
   * @description 原始文件对象
   * @example new File([], 'example.mp4')
   */
  raw?: File;

  /**
   * @description 文件名
   * @example 'example.mp4'
   */
  name: string;

  /**
   * @description 文件大小，单位为字节
   * @example 1024
   */
  size: number;

  /**
   * @description 文件类型
   * @example 'video/mp4'
   */
  type: string;

  /**
   * @description 文件上传进度百分比
   * @example 100
   */
  percent: number;

  /**
   * @description 文件上传状态
   * @example 'success'
   */
  status: EnumToUnion<typeof UploadStatus>;

  /**
   * @description 文件的下载地址
   * @example 'https://example.com/example.mp4'
   */
  url?: string;
}

/**
 * cos 上传支持的文件类型
 */
export type TYPE = 'image' | 'video' | 'audio' | 'file';

/**
 * @description 上传组件的属性类型
 */
export interface UploadProps {
  /**
   * @description 上传文件的类型
   * @example 'video'
   */
  type?: TYPE;

  /**
   * @description 是否支持多文件上传
   * @example true
   */
  multiple?: boolean;

  /**
   * @description 接受的文件类型
   * @example 'video/*'
   */
  accept?: string;

  /**
   * @description 文件的最大大小，单位为字节
   * @example 10485760 // 10MB
   */
  maxSize?: number;

  /**
   * @description 当前文件列表
   * @example [{ name: 'example.mp4', size: 1024, type: 'video/mp4', percent: 100, status: 'success', uid: 'unique-id-1' }]
   */
  value?: Partial<FileItemType>[];

  /**
   * @description 自定义上传按钮组件
   * @example <CustomButton />
   */
  customUploadButton?: ReactNode;

  /**
   * @description 自定义文件列表组件
   * @example <CustomFileList />
   */
  customFileList?:
    | ((props: CustomFileListProps) => ReactNode)
    | ReactNode
    | null;

  /**
   * @description 文案配置
   * @example { uploadButtonText: '上传文件', reuploadText: '重新上传', deleteText: '删除' }
   */
  locale?: Locale;

  /**
   * 展示的元素
   * @example 'close;reload;'
   */
  include?: string;

  /**
   * @description
   */
  wrapperClassName?: string;

  /**
   * @description 文件上传前的钩子，返回 false 或者 Promise.reject 时，文件将停止上传
   * @example (file) => { return file.size < 10485760; } // 10MB
   */
  beforeUpload?: (file: File) => boolean | Promise<boolean>;

  /**
   * @description 自定义的文件上传请求
   * @example (type, file, index, getTask) => { return new Promise((resolve) => { resolve({ key: 'key', url: 'https://example.com/file.mp4' }); }); }
   */
  uploadRequest?: UploadRequest;

  /**
   * @description 文件列表变化时的回调函数
   * @example (files) => { console.log(files); }
   */
  onChange?: (files: FileItemType[]) => void;

  /**
   * @description **所有**文件上传成功时的回调函数
   * @example (file) => { console.log('Upload success', file); }
   */
  onUploadSuccess?: (file: FileItemType[]) => void;
}

/**
 * @description 自定义上传按钮组件的属性类型
 * @example <CustomButton onClick={() => { console.log('Button clicked'); }} />
 */
export interface CustomUploadButtonProps {
  /**
   * @description 点击上传按钮时的回调函数
   * @example () => { console.log('Button clicked'); }
   */
  onClick: () => void;
}

/**
 * @description 自定义文件列表组件的属性类型
 * @example <CustomFileList files={files} onDelete={(uid) => { console.log('Delete', uid); }} onReupload={(uid) => { console.log('Reupload', uid); }} />
 */
export interface CustomFileListProps {
  /**
   * @description 文件列表
   * @example [{ uid: 'unique-id-1', name: 'example.mp4', size: 1024, type: 'video/mp4', percent: 100, status: 'success', url: 'https://example.com/example.mp4' }]
   */
  files: FileItemType[];

  /**
   * @description 删除文件时的回调函数
   * @example (uid) => { console.log('Delete', uid); }
   */
  onDelete: (uid: string) => void;

  /**
   * @description 重新上传文件时的回调函数
   * @example (uid) => { console.log('Reupload', uid); }
   */
  onReupload: (uid: string) => void;
}

export interface LocaleProps {
  locale?: Locale;
}

export type CustomVideoButtonProps = React.HTMLAttributes<HTMLDivElement> &
  LocaleProps;
