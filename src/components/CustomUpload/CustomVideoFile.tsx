import React, { Fragment } from 'react';
import { UploadStatus } from './constants';
import { Loading } from 'tdesign-react';
import FilePreview from './FilePreview';
import { FileItemType, LocaleProps } from './type';

interface ICustomFile extends LocaleProps {
  file: FileItemType;
  onDelete: () => void;
  onReupload: () => void;
  include?: string;
}

export function CustomVideoFile({
  file,
  onDelete,
  onReupload,
  locale,
  include,
}: ICustomFile) {
  return (
    <div className="w-[122px] h-[122px] relative cursor-pointer group rounded-[4px] overflow-hidden">
      <Fragment x-if={file.status === UploadStatus.SUCCESS}>
        <FilePreview file={file} />
        <div
          onClick={onDelete}
          x-if={include?.includes('close')}
          className="absolute top-0 right-0 bg-opacity-85 bg-black w-[28px] h-[28px] flex justify-center items-center rounded-bl-[4px] opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <CloseIcon />
        </div>
      </Fragment>
      <Fragment x-if={file.status === UploadStatus.UPLOADING}>
        <div className="w-full h-full bg-[#606061] flex justify-center items-center text-[white]">
          <Loading inheritColor size="small" />
        </div>
      </Fragment>
      <Fragment x-if={file.status === UploadStatus.ERROR}>
        <div className="w-full h-full bg-[#606061] flex justify-center items-center text-[white]">
          <ErrorIcon />
        </div>
      </Fragment>
      <div
        x-if={include?.includes('reload')}
        onClick={onReupload}
        className="absolute bottom-0 bg-opacity-85 bg-black text-[#fff] text-[12px] w-full text-center leading-[27px]"
      >
        {locale?.reuploadText}
      </div>
    </div>
  );
}

function CloseIcon() {
  return (
    <div className="inline-flex">
      <svg
        width="10"
        height="10"
        viewBox="0 0 10 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.00002 6.15044L8.27434 9.42475C8.59202 9.74243 9.10709 9.74243 9.42478 9.42475C9.74246 9.10706 9.74246 8.59199 9.42478 8.27431L6.15046 5L9.42478 1.72569C9.74246 1.40801 9.74246 0.892936 9.42478 0.575251C9.10709 0.257567 8.59202 0.257567 8.27434 0.575251L5.00002 3.84956L1.72566 0.575217C1.40798 0.257533 0.892908 0.257533 0.575223 0.575217C0.257536 0.892903 0.257537 1.40797 0.575224 1.72566L3.84957 5L0.575223 8.27434C0.257536 8.59203 0.257536 9.1071 0.575223 9.42478C0.892908 9.74247 1.40798 9.74247 1.72566 9.42478L5.00002 6.15044Z"
          fill="white"
        />
      </svg>
    </div>
  );
}

function ErrorIcon() {
  return (
    <div className="inline-flex">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="28"
        height="28"
        viewBox="0 0 28 28"
        fill="none"
      >
        <path
          d="M28 14C28 6.26802 21.732 2.36583e-06 14 0C6.26802 -4.28371e-06 2.36583e-06 6.26801 0 14C-4.28371e-06 21.732 6.26801 28 14 28C21.732 28 28 21.732 28 14ZM15 6.00061V17H13V6.00061H15ZM12.7886 20H15.1886V22.4H12.7886V20Z"
          fill="url(#paint0_linear_11996_178806)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_11996_178806"
            x1="1.04308e-07"
            y1="14"
            x2="28"
            y2="14"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E25050" />
            <stop offset="1" stopColor="#FF5353" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
}

export default CustomVideoFile;
