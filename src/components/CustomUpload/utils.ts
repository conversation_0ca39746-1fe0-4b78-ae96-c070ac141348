import { uuid } from '@tencent/midas-util';
import { UploadStatus } from './constants';
import { FileItemType, TYPE } from './type';
import { assign, find } from 'lodash-es';

/**
 * 创建一个文件数据结构
 * @param file
 * @returns
 */
export const createNewFile = (file: File): FileItemType => {
  return {
    raw: file,
    name: file.name,
    size: file.size,
    type: file.type,
    percent: 0,
    status: UploadStatus.UPLOADING,
    _uid: uuid(),
  };
};

/**
 * 根据 url 判断 资源类型
 * @param url
 * @returns
 */
const getFileTypeFromUrl = (url: string): string => {
  const extension = url.split('.').pop()?.toLowerCase();
  if (extension) {
    if (['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv'].includes(extension)) {
      return 'video';
    }
    if (
      ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)
    ) {
      return 'image';
    }
    return 'file';
  }
  return 'file';
};

export const createDefaultFile = (
  file: Partial<FileItemType>
): FileItemType => {
  const defaultFile: FileItemType = {
    _uid: file._uid || uuid(),
    raw: file.raw || new File([], file.name || 'unknown'),
    name: file.name || 'unknown',
    size: file.size || 0,
    type: file.type || getFileTypeFromUrl(file.url || ''),
    percent: file.percent || 0,
    status: file.status || UploadStatus.SUCCESS,
    url: file.url || '',
  };

  return defaultFile;
};

export const genAccept = (type: TYPE) => {
  const defaultAccept =
    type === 'video' ? 'video/*' : type === 'image' ? 'image/*' : '*/*';

  return defaultAccept;
};

export const updateObjectInArray = (
  array: FileItemType[],
  id: string,
  updates: Partial<FileItemType>
): FileItemType[] => {
  // 查找目标对象的引用
  const target = find(array, { id });

  if (target) {
    // 直接修改目标对象的属性
    assign(target, updates);
  }
  return array;
};
