/**
 * @description 文案配置类型
 * @example { uploadButtonText: '上传文件', reuploadText: '重新上传', deleteText: '删除' }
 */
export interface Locale {
  /**
   * @description 上传按钮的文案
   * @example '添加视频'
   */
  uploadButtonText: string;

  /**
   * @description 重新上传按钮的文案
   * @example '重新上传'
   */
  reuploadText: string;

  /**
   * @description 删除按钮的文案
   * @example '删除'
   */
  deleteText: string;
}

export const DEFAULT_TEXT = {
  UPLOAD_TEXT: '添加视频',
  RELOAD_TEXT: '重新上传',
  DELETE_TEXT: '删除',
};

export const DEFAULT_LOCALE: Locale = {
  uploadButtonText: DEFAULT_TEXT.UPLOAD_TEXT,
  reuploadText: DEFAULT_TEXT.RELOAD_TEXT,
  deleteText: DEFAULT_TEXT.DELETE_TEXT,
};
