import React, { useMemo, useRef, useState } from 'react';
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks';
import { reject } from 'lodash';
import { PlusIcon } from 'tdesign-icons-react';
import CustomVideoFile from './CustomVideoFile';
import { UploadStatus } from './constants';
import { uploadRequest as uploadCosRequest } from '@/utils/cos';
import { CustomVideoButtonProps, FileItemType, UploadProps } from './type';
import {
  createDefaultFile,
  createNewFile,
  genAccept,
  updateObjectInArray,
} from './utils';
import { DEFAULT_LOCALE } from './locale';
import { isFunction, isNull, merge } from 'lodash-es';

/**
 * 纯自实现的 Upload 上传组件
 * @example 
 *  <CustomUpload
    multiple
    accept="video/*"
    maxSize={10 * 1024} // 10MB
    onChange={handleFilesChange}
    value={uploadedFiles}
    beforeUpload={beforeUpload}
    uploadRequest={uploadRequest}
    customUploadButton={<CustomButton />}
    customFileList={<CustomFileList />}
  />
 * 
 */
export function CustomUpload(props: UploadProps) {
  const {
    value = [],
    multiple,
    accept,
    maxSize,

    type = 'video',
    customUploadButton,
    customFileList,
    locale = DEFAULT_LOCALE,
    wrapperClassName,
    onUploadSuccess,
    onChange,
    beforeUpload,
    uploadRequest = uploadCosRequest,
    include = 'reload;close;',
  } = props;

  // 根据 type 设置 accept 的默认值
  const finalAccept = accept || genAccept(type);

  const [files, setFiles] = useState<FileItemType[]>(() =>
    value.map((file) => createDefaultFile(file))
  );

  useDeepCompareEffect(() => {
    setFiles(value.map((file) => createDefaultFile(file)));
  }, [value]);

  /**
   * 输入控件
   */
  const inputRef = useRef<HTMLInputElement>(null);
  /**
   * 用来标识是否是重新加载
   */
  const reuploadFileRef = useRef<string | null>(null);

  /**
   * 包装页匠上传代码
   */
  const requestCosWrap = useMemoizedFn((files: FileItemType) => {
    const getTask = () => void 0;
    return uploadRequest?.(type, files.raw!, 0, getTask, {
      disposition: true,
      protocol: 'https:',
    });
  });

  /**
   * 更新状态
   */
  const updateFileStatus = useMemoizedFn(
    (_uid: string, updates: Partial<FileItemType>) => {
      setFiles((prevFiles) => {
        const fileExists = prevFiles.some((f) => f._uid === _uid);
        const updatedFiles = fileExists
          ? prevFiles.map((f) => (f._uid === _uid ? { ...f, ...updates } : f))
          : [...prevFiles, { _uid, ...updates } as FileItemType];

        onChange?.(updatedFiles);
        return updatedFiles;
      });
    }
  );

  /**
   * 上传文件变化
   */
  const handleFileChange = useMemoizedFn(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      // 选择的多个文件
      const selectedFiles = Array.from(event.target.files || []);
      const validFiles = selectedFiles.filter(
        (file) => !maxSize || file.size <= maxSize
      );

      // 存储单次的数据，非状态
      const newFiles: FileItemType[] = [];
      // 先 dispatch 一次 loading 状态
      for (const file of validFiles) {
        const newFile = createNewFile(file);
        newFiles.push(newFile);
      }
      setFiles((prevFiles) => {
        const updatedFiles = multiple
          ? [...prevFiles, ...newFiles]
          : [newFiles[0]];
        onChange?.(updatedFiles);
        return updatedFiles;
      });

      // 轮训上传
      for (const [index, file] of validFiles.entries()) {
        // 上传前钩子
        if (beforeUpload) {
          const shouldUpload = await beforeUpload(file);
          if (!shouldUpload) {
            continue;
          }
        }
        const newFile = newFiles[index];

        try {
          const response = await requestCosWrap(newFile);
          const newUpdates = {
            url: response.url,
            percent: 100,
            status: UploadStatus.SUCCESS,
          };
          // 更新 newFiles 和 onChange
          updateObjectInArray(newFiles, newFile._uid, newUpdates);
          updateFileStatus(newFile._uid, newUpdates);
        } catch (error) {
          updateFileStatus(newFile._uid, { status: UploadStatus.ERROR });
        }
      }

      onUploadSuccess?.(newFiles);

      // 清空 input 的值以便可以重新选择相同的文件
      if (inputRef.current) {
        inputRef.current.value = '';
      }
    }
  );

  /**
   * 和直接选择分开，免得耦合了
   */
  const handleReuploadFileChange = useMemoizedFn(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      // 选择的文件
      const selectedFiles = Array.from(event.target.files || []);
      // 大小限制
      const validFiles = selectedFiles.filter(
        (file) => !maxSize || file.size <= maxSize
      );
      const newFiles: FileItemType[] = [];

      const _uid = reuploadFileRef.current;
      reuploadFileRef.current = null;

      if (!_uid) return;

      for (const [index, file] of validFiles.entries()) {
        if (beforeUpload) {
          const shouldUpload = await beforeUpload(file);
          if (!shouldUpload) {
            continue;
          }
        }

        const newFile = createNewFile(file);
        // 第一个文件的名称用原来的，用于替换 state
        const xUid = index === 0 ? _uid : newFile._uid;
        try {
          // 更新状态
          updateFileStatus(xUid, {
            status: UploadStatus.UPLOADING,
            percent: 0,
            raw: file,
          });

          const response = await requestCosWrap(newFile);
          const newUpdates = {
            status: UploadStatus.SUCCESS,
            parent: 100,
            url: response.url,
          };
          newFiles.push(merge(newFile, newUpdates));
          updateFileStatus(xUid, merge(newFile, newUpdates));
        } catch (e) {
          updateFileStatus(xUid, { status: UploadStatus.ERROR });
        }
      }
      onUploadSuccess?.(newFiles);

      // 清空 input 的值以便可以重新选择相同的文件
      if (inputRef.current) {
        inputRef.current.value = '';
      }
    }
  );

  /**
   * 删除某个文件
   */
  const handleDelete = useMemoizedFn((_uid: string) => {
    setFiles((prevFiles) => {
      const updatedFiles = reject(prevFiles, { _uid });
      onChange?.(updatedFiles);
      return updatedFiles;
    });
  });

  // 打开文件选择器
  const handleReupload = useMemoizedFn((_uid: string) => {
    reuploadFileRef.current = _uid;
    inputRef.current?.click();
  });

  /**
   * 打开文件选择器
   */
  const handleClick = useMemoizedFn(() => {
    inputRef.current?.click();
  });

  const handleAllChange = useMemoizedFn((e) => {
    reuploadFileRef.current ? handleReuploadFileChange(e) : handleFileChange(e);
  });

  const resolvedCustomFileList = useMemo(() => {
    if (isFunction(customFileList)) {
      return customFileList({
        files,
        onDelete: handleDelete,
        onReupload: handleReupload,
      });
    }
    if (isNull(customFileList)) {
      return <></>;
    }
    return customFileList;
  }, [customFileList, files, handleDelete, handleReupload]);

  return (
    <div className={`p-4 border rounded-lg ${wrapperClassName}`}>
      <input
        ref={inputRef}
        type="file"
        multiple={multiple}
        accept={finalAccept}
        className="hidden"
        onChange={handleAllChange}
      />
      {customUploadButton ? (
        React.cloneElement(customUploadButton as React.ReactElement, {
          onClick: handleClick,
          hidden: !(multiple || files.length === 0),
        })
      ) : (
        <CustomVideoButton
          locale={locale}
          onClick={handleClick}
          x-if={multiple || files.length === 0}
        />
      )}
      {resolvedCustomFileList ? (
        React.cloneElement(resolvedCustomFileList as React.ReactElement, {
          files,
          onDelete: handleDelete,
          onReupload: handleReupload,
        })
      ) : (
        <div className="mt-4 flex gap-[4px]" x-if={files.length}>
          {files.map((file) => (
            <CustomVideoFile
              include={include}
              locale={locale}
              key={file._uid}
              file={file}
              onDelete={() => handleDelete(file._uid)}
              onReupload={() => handleReupload(file._uid)}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function CustomVideoButton(props: CustomVideoButtonProps) {
  return (
    <div
      {...props}
      className="w-[122px] cursor-pointer h-[122px] rounded-4 overflow-hidden flex justify-center items-center flex-col gap-[4px] bg-[#F4F6FF] hover:bg-[#f2f2f4] active:bg-[#ededf1] transition-all duration-200 ease-in-out"
    >
      <PlusIcon size={20} />
      <div>{props.locale?.uploadButtonText}</div>
    </div>
  );
}

export default CustomUpload;
