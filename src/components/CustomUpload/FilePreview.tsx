import React from 'react';
import { Video2PosterPro } from '../Video2Poster';
import { FileIcon } from 'tdesign-icons-react';
import { FileItemType } from './type';

interface FilePreviewProps {
  file: FileItemType;
}

export function FilePreview({ file }: FilePreviewProps) {
  const { type, url, name } = file;
  if (type.startsWith('video')) {
    return <Video2PosterPro videoUrl={url!} />;
  }

  if (type.startsWith('image')) {
    return <img src={url} alt={name} className="w-full h-full object-cover" />;
  }

  return (
    <div className="w-full h-full bg-[#F4F6FF] flex flex-col justify-center items-center text-[#606061]">
      <FileIcon className="w-8 h-8" />
      <span className="mt-[10px] text-sm truncate  text-center w-[70%]">
        {file.name}
      </span>
    </div>
  );
}

export default FilePreview;
