/// <reference types="vite/client" />
import { AvatarClientBridge } from '@tencent/avatar-client-bridge-type';

interface ImportMetaEnv extends Readonly<Record<string, string>> {
  readonly VITE_ENV: 'dev' | 'production' | 'qa';
  readonly VITE_RUNNING_SYSTEM: 'PAGEDOO' | 'AD' | 'debug';
  readonly VITE_API_BASE_URL: string;
  readonly VITE_CLIENT_API_BASE_URL: string;
  readonly VITE_APP_ID: string;
  readonly VITE_WXAPP_ID: string;
  // cos上传
  readonly VITE_UPLOAD_CDN_BASE_URL: string;
  readonly VITE_UPLOAD_COS_BASE_URL: string;
  // 腾讯云cos视频转码模版id
  readonly VITE_COS_TRANSCODE_VIDEO_TEMPLATE_ID: string;
  // 混元模型文生图替换地址
  readonly VITE_GENERATE_HY_BASE_URI: string;
  readonly VITE_GENERATE_AVATAR_BASE_URI: string;
  // 素材库版本号
  readonly VITE_MATERIAL_AVATAR_VERSION?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare interface Window {
  __page_editor: any;
  handler: any;
  avatarClientBridge?: AvatarClientBridge;
}
