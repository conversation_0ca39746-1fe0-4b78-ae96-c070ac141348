import type { Context } from 'react';
import type { RecoilState } from 'recoil';
// 从 Promise 泛型中抽取类型定义
export type PromiseType<T extends Promise<any>> = T extends Promise<infer U>
  ? U
  : never;

export type DeepRequire<T> = {
  [K in keyof T]-?: Exclude<T[K], undefined> extends object
    ? DeepRequire<T[K]>
    : T[K];
};

export type UnArray<T> = T extends Array<infer U> ? U : T;

export type ExtractArray<T> = T extends Array<infer U> ? U[] : T;

export type ExtractAtomType<T extends RecoilState<any>> = T extends RecoilState<
  infer U
>
  ? U
  : never;

export type ExtractReactContextType<T extends Context<any>> = T extends Context<
  infer U
>
  ? U
  : never;
