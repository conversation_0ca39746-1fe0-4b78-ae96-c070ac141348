export const downloadVideo = (url: string, name: string, target = '_blank') => {
  // 非同源视频文件的URL
  const videoURL = url;

  // 创建一个临时的a标签
  const tempLink = document.createElement('a');
  tempLink.target = target;
  tempLink.href = videoURL.replace(/^http:\/\//, 'https://');
  tempLink.download = name; // 设置下载文件名
  tempLink.name = name;
  tempLink.style.display = 'none';
  document.body.appendChild(tempLink);
  tempLink.click(); // 触发点击事件以下载文件

  // 移除临时的a标签
  setTimeout(() => {
    document.body.removeChild(tempLink);
  }, 100);
};
