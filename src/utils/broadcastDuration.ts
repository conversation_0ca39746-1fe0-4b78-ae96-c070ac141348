/**
 * <AUTHOR>
 * @date 2024/5/6 下午4:26
 * @desc broadcastDuration
 */

export function getTimeIntervals(arr: string[]): string[] {
  const timeIntervals: string[] = [];
  let startMinutes = 0;

  for (let i = 0; i < arr.length; i++) {
    const timePerWord = Math.ceil(arr[i].length / 5);
    const endMinutes = startMinutes + timePerWord;
    const startTime = formatTime(startMinutes);
    const endTime = formatTime(endMinutes);
    timeIntervals.push(`${startTime}-${endTime}`);
    startMinutes = endMinutes;
  }

  return timeIntervals;
}

function formatTime(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${remainingMinutes
    .toString()
    .padStart(2, '0')}`;
}
