/**
 * <AUTHOR>
 * @date 2023/12/13 15:17
 * @desc localStorage 有效期设置
 */

// 定义存储在 localStorage 中的对象类型
interface ILocalStorageItem {
  value: any;
  expiry: number;
}

/**
 * 设置键值对和过期时间
 * @param key
 * @param value
 * @param ttl 秒为单位
 */
export function setLocalStorageWithExpiry(
  key: string,
  value: any,
  ttl: number
): void {
  const now = new Date();
  const item: ILocalStorageItem = {
    value,
    expiry: now.getTime() + ttl * 1000,
  };
  localStorage.setItem(key, JSON.stringify(item));
}

// 获取键值对，如果已过期，则删除它
export function getLocalStorageWithExpiry(key: string): any | null {
  const itemStr = localStorage.getItem(key);

  if (!itemStr) {
    return null;
  }

  const item: ILocalStorageItem = JSON.parse(itemStr);
  const now = new Date();

  if (now.getTime() > item.expiry) {
    localStorage.removeItem(key);
    return null;
  }

  return item.value;
}
export function clearLocalStorage(key: string) {
  localStorage.removeItem(key);
}

// 设置一个有效期为一个小时的键值对
// setLocalStorageWithExpiry('cache', 'your_value_here', 60 * 60);
//
// // 获取键值对
// const cacheValue = getLocalStorageWithExpiry('cache');
// console.log(cacheValue);
