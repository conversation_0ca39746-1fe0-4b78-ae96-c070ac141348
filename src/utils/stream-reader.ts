import { to } from 'await-to-js';
import { sleep } from './sleep';

export type StreamReaderCallbackType<Data = any> = (
  done: boolean,
  value: Data | undefined
) => Promise<void>;

export const createStreamReader = <Data = any>(
  stream: ReadableStream<Data>,
  options?: {
    log?: string;
  }
) => {
  const reader = stream.getReader();
  const log = (...str: any[]) => {
    if (options?.log) {
      console.log(options.log, ...str);
    }
  };
  let canceled = false;
  const read = async (cb: StreamReaderCallbackType<Data>) => {
    while (true) {
      let result: ReadableStreamReadResult<Data> = null as any;
      try {
        result = await reader.read();
      } catch (e) {
        if (!stream.locked) return;
        console.error('reader读取异常', e);
      }
      log('获取流结果', result, Date.now());
      if (!result) {
        await sleep(100);
        continue;
      }
      if (canceled) return await cb(true, undefined);
      const { value, done } = result;
      if (done) {
        await cb(done, value);
        break;
      }
      try {
        await cb(done, value);
      } catch (e) {
        console.error('reader read error', e);
      }
    }
  };

  return {
    isCancel: () => canceled,
    startRead: read,
    stopRead: async () => {
      canceled = true;
      try {
        await reader.releaseLock();
      } catch {}
      await to(reader.cancel('canceled'));
    },
  };
};
