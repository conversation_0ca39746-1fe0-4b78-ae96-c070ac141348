import * as XLSX from 'xlsx';
import axios, { AxiosResponse } from 'axios';

/**
 *
 * @param {Array} workSheetData 二维数组
 * [
 *   ['id', 'name', 'age'],
 *   [1, '张三', 20],
 *   [2, '李四', 25]
 * ]
 * @param {String} fileName 下载时文件名称
 */
export const exportExcelByArray = (
  workSheetData: any[],
  fileName = 'example.xlsx'
) => {
  const ws = XLSX.utils.aoa_to_sheet(workSheetData);
  const workSheetName = 'MySheet';
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, ws, workSheetName);
  return XLSX.writeFile(workbook, fileName, { type: 'binary' });
};

export const downloadZipAsUint8Array = async (
  url: string,
  onProgress?: (percent: number) => void
): Promise<Uint8Array> => {
  try {
    // 发送 GET 请求，设置响应类型为 'arraybuffer'
    const response: AxiosResponse<ArrayBuffer> = await axios.get(url, {
      responseType: 'arraybuffer',
      onDownloadProgress: (progressEvent: ProgressEvent) => {
        // 计算下载进度
        const total = progressEvent.total || 0; // 处理可能为 null 的情况
        const current = progressEvent.loaded;
        const percentCompleted = Math.round((current * 100) / total);

        // 调用传入的进度回调函数
        if (onProgress) {
          onProgress(percentCompleted);
        }
      },
    });

    // 将 ArrayBuffer 转换为 Uint8Array
    // 返回 Uint8Array
    return new Uint8Array(response.data);
  } catch (error) {
    console.error('Error downloading the ZIP file:', error);
    throw error;
  }
};
