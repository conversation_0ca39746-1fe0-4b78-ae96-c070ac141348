import { handleScreenShot } from '@/pages/Editor/common/handleScreenShot';
import { uploadRequest } from './cos';

function checkIsSolidColorBg(data: Uint8ClampedArray) {
  const firstPixelColor = {
    r: data[0],
    g: data[1],
    b: data[2],
  };
  for (let i = 0; i < data.length; i += 4) {
    if (
      data[i] !== firstPixelColor.r ||
      data[i + 1] !== firstPixelColor.g ||
      data[i + 2] !== firstPixelColor.b
    ) {
      return false;
    }
  }
  return true;
}

interface IVideoFirstFrame {
  videoUrl: string;
  second?: number;
  autoUpload?: boolean;
}
/**
 * 获取视频第一帧
 * @param videoUrl
 * @param second
 * @returns
 */
export function getVideoFirstFrame({
  videoUrl,
  second = 0.001,
  autoUpload = false,
}: IVideoFirstFrame): Promise<{
  status: 'error' | 'success';
  url: string;
  cosUrl?: string;
  info?: { duration: number; width: number; height: number };
}> {
  return new Promise((reslove) => {
    const video = document.createElement('video');
    video.setAttribute('crossOrigin', 'anonymous');
    video.setAttribute('src', videoUrl);
    // 静音操作，防止播放失败
    video.setAttribute('muted', 'muted');
    video.muted = true;
    video.volume = 0;
    video.addEventListener('loadeddata', async () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const { videoWidth: width, videoHeight: height } = video;
      const info = {
        duration: video.duration,
        width,
        height,
        direction: width > height ? 'horizontal' : 'vertical',
      };
      canvas.width = width;
      canvas.height = height;
      if (second) {
        video.currentTime = second;
        await video.play();
        await video.pause();
      }
      ctx?.drawImage(video, 0, 0, width, height);
      const imageData = ctx?.getImageData(0, 0, width, height);
      const data = imageData?.data;
      let cosUrl = '';

      if (data) {
        // 判断是否是纯色  纯色继续找下一帧
        if (!checkIsSolidColorBg(data)) {
          const base64Url = canvas.toDataURL();
          if (autoUpload) {
            try {
              cosUrl = await handleScreenShot({ imageBase64: base64Url });
            } catch (e) {
              console.error('上传 cos 失败', e);
            }
          }
          reslove({ status: 'success', url: base64Url, info, cosUrl });
        } else {
          getVideoFirstFrame({ videoUrl, second: second + second, autoUpload });
        }
      } else {
        reslove({ status: 'error', url: '', info: undefined });
      }
    });
  });
}
