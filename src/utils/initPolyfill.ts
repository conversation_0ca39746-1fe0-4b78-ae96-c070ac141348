let init = false;
/**
 * initPolyfill
 * @description 兼容
 * 1. AbortSignal.throwIfAborted
 * 2. Promise.withResolvers
 */
export const initPolyfill = () => {
  if (init) return;
  init = true;
  if (
    typeof AbortSignal !== 'undefined' &&
    !AbortSignal.prototype.throwIfAborted
  ) {
    AbortSignal.prototype.throwIfAborted = function () {
      if (this.aborted)
        throw (
          this.reason || new DOMException('signal is aborted without reason')
        );
    };
  }
  if (!Promise.withResolvers)
    Promise.withResolvers = function withResolvers<
      T
    >(): PromiseWithResolvers<T> {
      let a: PromiseWithResolvers<T>['resolve'];
      let b: PromiseWithResolvers<T>['reject'];
      const c = new this<T>(function (resolve, reject) {
        a = resolve;
        b = reject;
      });
      return { resolve: a!, reject: b!, promise: c };
    };
};
