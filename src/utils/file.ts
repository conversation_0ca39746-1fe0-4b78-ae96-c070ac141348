/**
 * <AUTHOR>
 * @date 2024/5/8 下午4:51
 * @desc file
 */

export function convertFileSize(sizeInBytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let unitIndex = 0;

  while (sizeInBytes >= 1024 && unitIndex < units.length - 1) {
    // eslint-disable-next-line no-param-reassign
    sizeInBytes /= 1024;
    // eslint-disable-next-line no-plusplus
    unitIndex++;
  }

  return `${sizeInBytes.toFixed(2)} ${units[unitIndex]}`;
}
