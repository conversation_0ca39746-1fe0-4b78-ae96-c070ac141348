async function getAudioDurations(urls: string[]): Promise<number[]> {
  // 创建一个函数，用于加载单个音频文件并获取其时长
  const getAudioDuration = (url: string): Promise<number> =>
    new Promise((resolve, reject) => {
      const audio = new Audio(url);
      audio.addEventListener('loadedmetadata', () => {
        resolve(audio.duration);
      });
      audio.addEventListener('error', (error) => {
        reject(error);
      });
    });

  // 使用 Promise.all 来并行获取所有音频文件的时长
  const durations = await Promise.all(urls.map((url) => getAudioDuration(url)));

  return durations;
}

function formatDuration(duration: number): string {
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  // 修正秒数，不会展示 00:00:00
  const seconds = duration > 0 && duration < 1 ? 1 : Math.floor(duration % 60);

  const paddedHours = hours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');
  const paddedSeconds = seconds.toString().padStart(2, '0');

  return `${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
}

export async function getFormattedAudioDurations(
  urls: string[]
): Promise<string[]> {
  // 使用之前的 getAudioDurations 函数来获取音频时长
  const durations = await getAudioDurations(urls);

  // 使用 formatDuration 函数来格式化音频时长
  const formattedDurations = durations.map((duration) =>
    formatDuration(duration)
  );

  return formattedDurations;
}
