import {
  IAudioDecodeMethodReturns,
  IAudioDecoder,
  IAudioPlayer,
  IAudioPlayerOptions,
  IPCMDecoderOptions,
} from '../typings';
import { createStreamReader } from '../../stream-reader';
import { createPCMStreamDecoder } from './pcm-stream-decoder';

export class PCMPlayer implements IAudioPlayer {
  protected decoder!: IAudioDecoder<IPCMDecoderOptions>;
  protected handle!: IAudioDecodeMethodReturns;
  protected reader!: ReturnType<typeof createStreamReader<Uint8Array>>;

  public constructor(protected options: IAudioPlayerOptions) {}
  volume(value: number) {
    this.decoder?.setVolume(value);
  }
  async play(stream: ReadableStream) {
    this.decoder = createPCMStreamDecoder(stream);
    this.handle = this.decoder.decode({
      ...this.options.pcmPlayerOptions!,
      volume: this.options.volume,
      onReceiveAudioBuffer: ({ context, source }) => {
        // 连接到输出,用于播放声音
        source.connect(context.destination);
      },
      onAudioStreamDone: ({ context }) => {
        this.options.onAudioStreamDone?.({ context });
        context?.destination?.disconnect();
      },
      onStop: ({ context }) => {
        context?.destination?.disconnect();
      },
    });
  }
  async pause() {
    // await this.reader.stopRead();
    // this.reader = null as any;
    // await this.pcmPlayer.pause();
    await this.handle.pause();
  }
  async destroy() {
    // await this.reader.stopRead();
    // this.reader = null as any;
    // await this.pcmPlayer.destroy();
    await this.handle.stop();
  }
}
