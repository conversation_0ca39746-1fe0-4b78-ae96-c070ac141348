import to from 'await-to-js';
import {
  createStream<PERSON>eader,
  StreamReaderCallbackType,
} from '../../stream-reader';
import { concatBuf, initAudioContext } from '../audio-utils';
import {
  AudioDecoderFactory,
  IAudioDecoder,
  IPCMDecoderOptions,
} from '../typings';

// pcm 编码位数
export type PCMInputCodecs = 'Int8' | 'Int16' | 'Int32' | 'Float32';

type TypedArray =
  | Int8Array
  | Int16Array
  | Int32Array
  | Float32Array
  | Uint8Array;
const getConvertValue = (codec: PCMInputCodecs) => {
  // 根据传入的目标编码位数
  // 选定转换数据所需要的基本值
  const inputCodecs = {
    Int8: 128,
    Int16: 32768,
    Int32: 2147483648,
    Float32: 1,
  };
  if (!inputCodecs[codec])
    throw new Error(
      'wrong codec.please input one of these codecs:Int8,Int16,Int32,Float32'
    );
  return inputCodecs[codec];
};
const getTypedArray = (codec: PCMInputCodecs) => {
  const typedArrays = {
    Int8: Int8Array,
    Int16: Int16Array,
    Int32: Int32Array,
    Float32: Float32Array,
  };
  if (!typedArrays[codec])
    throw new Error(
      'wrong codec.please input one of these codecs:Int8,Int16,Int32,Float32'
    );
  return typedArrays[codec];
};

//
export const transform2PCMBuffer = (
  buf: ArrayBuffer,
  codec: PCMInputCodecs
) => {
  const convertValue = getConvertValue(codec);
  const typedArray = getTypedArray(codec);
  // eslint-disable-next-line new-cap
  const data = new typedArray(buf);
  const float32 = new Float32Array(data.length);

  for (let i = 0; i < data.length; i++) {
    // buffer 缓冲区的数据，需要是IEEE754 里32位的线性PCM，范围从-1到+1
    // 所以对数据进行除法
    // 除以对应的位数范围，得到-1到+1的数据
    // float32[i] = data[i] / 0x8000;
    float32[i] = data[i] / convertValue;
  }
  return float32;
};

export const createPCMStreamDecoder: AudioDecoderFactory<IPCMDecoderOptions> = (
  stream: ReadableStream
) => {
  const defaultOptions: IPCMDecoderOptions = {
    waitForNoDataTimeout: 0,
    inputCodec: 'Int16', // 传入的数据是采用多少位编码，默认16位
    channels: 1, // 声道数
    sampleRate: 8000, // 采样率 单位Hz
    volume: 1,
  };
  let audioContext: AudioContext | null;
  let stopped = false;
  let paused = false;
  let segmentStack: {
    samples: Float32Array;
    // 标识是否是最后一段
    last: boolean;
    timestamp: number;
  }[] = [];
  let initedAudio: ReturnType<typeof initAudioContext>;

  const { isCancel, startRead, stopRead } = createStreamReader(stream);
  const decode: IAudioDecoder<IPCMDecoderOptions>['decode'] = (opts) => {
    const options = {
      ...defaultOptions,
      ...opts,
    };
    initedAudio = initAudioContext({
      volume: options.volume || 1,
      sampleRate: options.sampleRate,
    });
    audioContext = initedAudio.audioContext;
    const convertValue = getConvertValue(options.inputCodec);
    const typedArray = getTypedArray(options.inputCodec);
    initedAudio.gainNode.gain.value = options.volume || 1;
    let streamDone = false;
    audioContext = initedAudio.audioContext;
    // let startTime = initedAudio.audioContext.currentTime;
    let startTime = 0;
    let scheduleBuffersTimeoutId: any = null;
    const scheduleBuffers = async () => {
      if (isCancel()) {
        clearTimeout(scheduleBuffersTimeoutId);
        scheduleBuffersTimeoutId = null;
        return;
      }
      let concatBuf: Float32Array | null = null;
      if (audioContext && segmentStack.length > 0) {
        const len = segmentStack.reduce(
          (prev, cur) => prev + cur.samples.length,
          0
        );
        let offset = 0;
        concatBuf = new Float32Array(len);
        for (const { samples } of segmentStack) {
          concatBuf.set(samples, offset);
          offset += samples.length;
        }
        segmentStack.length = 0;
      }

      if (audioContext && concatBuf) {
        const bufferSource = audioContext.createBufferSource();
        const length = concatBuf.length / options.channels;
        const audioBuffer = audioContext.createBuffer(
          options.channels,
          length,
          options.sampleRate
        );

        for (let channel = 0; channel < options.channels; channel++) {
          const audioData = audioBuffer.getChannelData(channel);
          let offset = channel;
          let decrement = 50;
          for (let i = 0; i < length; i++) {
            audioData[i] = concatBuf[offset];
            // 平滑处理，防止一些破音
            if (i < 50) {
              audioData[i] = (audioData[i] * i) / 50;
            }
            if (i >= length - 51) {
              // eslint-disable-next-line no-plusplus
              audioData[i] = (audioData[i] * decrement--) / 50;
            }
            offset += options.channels;
          }
        }

        bufferSource.buffer = audioBuffer;
        bufferSource.connect(initedAudio.gainNode);
        options.onReceiveAudioBuffer?.({
          context: audioContext,
          source: bufferSource,
        });
        if (startTime === 0) {
          startTime = audioContext.currentTime + 0.2;
        }
        if (startTime < audioContext.currentTime) {
          startTime = audioContext.currentTime;
        }

        // 播放本段samples
        bufferSource.start(startTime);

        console.log(
          `buffersource end, start vs current ${startTime} vs ${audioContext.currentTime} duration: ${audioBuffer.duration}`,
          'audio buffer',
          bufferSource.buffer
        );
        //  等待本段buffersource 播放完毕
        await new Promise((resolve) => {
          bufferSource.onended = (ev) => {
            bufferSource.onended = null;
            resolve(void 0);
            // requestAnimationFrame(resolve);
          };
        });

        startTime += audioBuffer.duration;
      }
      if (streamDone && segmentStack.length === 0) {
        console.log('stream done');
        if (!stopped) {
          setTimeout(() => {
            options.onAudioStreamDone?.({
              context: audioContext,
            });
          }, 50);
        }
        return;
      }
      scheduleBuffersTimeoutId = setTimeout(scheduleBuffers, 0);
    };
    let cuttedBuf: ArrayBuffer | null = null;
    const handleReadData: StreamReaderCallbackType<Uint8Array> = async (
      done,
      value
    ) => {
      if (value) {
        const buffer = value;
        let formattedBuf = buffer.buffer;
        if (cuttedBuf !== null) {
          formattedBuf = concatBuf(cuttedBuf, formattedBuf);
        }

        if (formattedBuf.byteLength % 2 > 0) {
          cuttedBuf = formattedBuf.slice(formattedBuf.byteLength - 1);
          formattedBuf = formattedBuf.slice(0, formattedBuf.byteLength - 1);
        } else {
          cuttedBuf = null;
        }

        // console.log(
        //   'before create data buffer ',
        //   'cuttedBuf',
        //   cuttedBuf,
        //   'old buffer',
        //   buffer,
        //   'new buffer',
        //   new Uint8Array(formattedBuf),
        // );
        // eslint-disable-next-line new-cap
        const data = new typedArray(formattedBuf);

        const float32 = new Float32Array(data.length);

        for (let i = 0; i < data.length; i++) {
          // buffer 缓冲区的数据，需要是IEEE754 里32位的线性PCM，范围从-1到+1
          // 所以对数据进行除法
          // 除以对应的位数范围，得到-1到+1的数据
          float32[i] = data[i] / convertValue;
        }
        // 合并两个buf
        // const newBuf = concatBuf(samples.buffer, float32.buffer);
        // samples = new Float32Array(newBuf);
        const samples = float32;
        if (samples.length > 0) {
          segmentStack.push({
            samples,
            last: done,
            timestamp: new Date().getTime(),
          });
        }
        // bufStack.push(float32);
        if (scheduleBuffersTimeoutId === null) {
          scheduleBuffersTimeoutId = setTimeout(() => {
            scheduleBuffers();
          }, 0);
        }
      } else if (done) {
        //
        streamDone = true;
      }
    };
    startRead(handleReadData);
    const stop = async () => {
      console.log('decode stop');
      stopped = true;
      clearTimeout(scheduleBuffersTimeoutId);
      segmentStack = [];
      options?.onStop?.({
        context: audioContext,
      });
      await to(audioContext?.close?.() || Promise.resolve());
      await to(stopRead());
    };
    return {
      isPaused: () => paused,
      pause: async () => {
        paused = true;
        clearTimeout(scheduleBuffersTimeoutId);
        await stopRead();
      },
      stop,
      resume: async () => {
        if (stopped) throw new Error('cannot resume a stopped decoder');
        paused = false;
        startRead(handleReadData);
      },
    };
  };
  return {
    decode,
    setVolume: (value) => {
      initedAudio.gainNode.gain.value = value;
    },
  };
};
