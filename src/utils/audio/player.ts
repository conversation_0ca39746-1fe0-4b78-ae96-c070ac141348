import { WavPlayer } from './wav/wav-player';
/* eslint-disable class-methods-use-this */
import { MediaSourcePlayer } from './mediasource-player';
import { IAudioPlayer, IAudioPlayerOptions } from './typings';
import { PCMPlayer } from './pcm/pcm-player';

export class AudioPlayer implements IAudioPlayer {
  protected player!: IAudioPlayer;
  public constructor(protected options: IAudioPlayerOptions) {
    this.init();
  }
  async play<Data>(stream: ReadableStream<Data>) {
    return this.player.play(stream);
  }
  async pause() {
    await this.player.pause();
  }
  async destroy() {
    await this.player.destroy();
  }

  volume(value: number) {
    this.player?.volume?.(value);
  }

  protected init() {
    switch (this.options.codec) {
      case 'wav':
        this.player = new WavPlayer();
        break;
      case 'pcm':
        if (!this.options.pcmPlayerOptions)
          throw new Error(`must provide pcmPlayerOptions when codec is pcm`);
        this.player = new PCMPlayer(this.options);
        break;
      default:
        this.player = new MediaSourcePlayer(this.options);
    }
  }
}
