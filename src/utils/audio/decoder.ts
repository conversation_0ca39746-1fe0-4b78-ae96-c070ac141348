import { createPCMStreamDecoder } from './pcm/pcm-stream-decoder';
import {
  AudioCodecType,
  IAudioDecodeMethodReturns,
  IAudioDecoder,
  IAudioDecoderOptions,
} from './typings';
import { createWavStreamDecoder } from './wav/wav-stream-decoder';

export interface IComposeAudioDecoderOptions {
  codec: AudioCodecType;
}
export class AudioDecoder implements IAudioDecodeMethodReturns {
  protected options!: IComposeAudioDecoderOptions;
  protected innerDecoder!: IAudioDecoder<any>;
  protected handle!: IAudioDecodeMethodReturns;
  public constructor(opts?: IComposeAudioDecoderOptions) {
    if (opts) {
      this.init(opts!);
    }
  }

  public async init(opts: IComposeAudioDecoderOptions) {
    this.options = opts;
  }

  public async decode<Opts extends IAudioDecoderOptions>(
    stream: ReadableStream,
    codecOptions: Opts
  ) {
    switch (this.options.codec) {
      case 'pcm':
        this.innerDecoder = createPCMStreamDecoder(stream);
        break;
      case 'wav':
        this.innerDecoder = createWavStreamDecoder(stream);
        break;
      default:
        throw new Error(`codec: ${this.options.codec} 不支持`);
    }
    const ret = this.innerDecoder.decode(codecOptions);
    this.handle = ret;
  }
  public isPaused() {
    return this.handle?.isPaused() || false;
  }
  public async stop() {
    await this.handle?.stop();
  }
  public async pause(): Promise<void> {
    await this.handle.pause();
  }
  async resume() {
    await this.handle.resume();
  }
}
