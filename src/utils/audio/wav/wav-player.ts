import {
  createWavStreamDecoder,
  WavStreamDecodeHandle,
  WavStreamDecoderType,
} from './wav-stream-decoder';
import { IAudioPlayer } from '../typings';

export class WavPlayer implements IAudioPlayer {
  protected decoder!: WavStreamDecoderType | null;
  protected handle!: WavStreamDecodeHandle | null;
  async play(stream: ReadableStream) {
    this.decoder = createWavStreamDecoder(stream);
    this.handle = this.decoder.decode({
      onReceiveAudioBuffer: ({ context, source }) => {
        // console.log(context.);
        // 连接到输出,用于播放声音
        source.connect(context.destination);
      },
    });
  }
  async pause() {
    await this.handle?.pause();
  }
  async destroy() {
    await this.handle?.stop();
  }
  public volume(value: number) {
    this.decoder?.setVolume(value);
  }
}
