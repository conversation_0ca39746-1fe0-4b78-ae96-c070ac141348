import { to } from 'await-to-js';
import {
  createStreamReader,
  StreamReaderCallbackType,
} from '../../stream-reader';
import { concatBuf as concat, initAudioContext } from '../audio-utils';
import { AudioDecoderFactory, IAudioDecoderOptions } from '../typings';
import { pad, wavify } from './wav-utils';

interface IAudioSegment {
  buffer: AudioBuffer | null;
}

export interface IWavStreamDecoderOptions extends IAudioDecoderOptions {
  rawPCM?: boolean;
  onRawPCMBuffer?: (data: {
    // 声道数
    channels: number;
    // 采样率
    sampleRate: number;
    // 原始buffer
    buffer: ArrayBuffer;
  }) => void;
}

export type WavStreamDecoderType = ReturnType<typeof createWavStreamDecoder>;

export type WavStreamDecodeHandle = ReturnType<WavStreamDecoderType['decode']>;

export const createWavStreamDecoder: AudioDecoderFactory<
  IWavStreamDecoderOptions
> = (stream: ReadableStream) => {
  let context: AudioContext | null;
  let audioWrap: ReturnType<typeof initAudioContext> | null;
  const { startRead, stopRead, isCancel } =
    createStreamReader<Uint8Array>(stream);
  let stopped = false;
  let paused = false;
  const audioStack: IAudioSegment[] = [];
  let isFirstBuffer = true;
  let rest: ArrayBuffer | null = null;
  let nextTime = 0;
  let numberOfChannels: number;
  let sampleRate: number;
  let volume = 0;
  const defaultOptions: IWavStreamDecoderOptions = {
    waitForNoDataTimeout: 0,
    volume: 1,
  };
  const decode = (opts: IWavStreamDecoderOptions) => {
    if (stopped) throw new Error('cannot resume a stopped decoder');
    const options = {
      ...defaultOptions,
      ...opts,
    };
    volume = options.volume || 1;
    let scheduleBuffersTimeoutId: any = null;
    // context = new AudioContext({});
    let streamDone = false;
    const scheduleBuffers = async () => {
      if (isCancel()) {
        scheduleBuffersTimeoutId = null;

        return;
      }
      if (context && audioWrap) {
        while (
          audioStack.length > 0 &&
          audioStack[0].buffer !== undefined &&
          nextTime < context.currentTime + 2
        ) {
          console.log('process buffers ', audioStack, context);
          const { currentTime } = context;

          const source = context.createBufferSource();

          const segment = audioStack.shift();

          if (!segment?.buffer) continue;
          source.buffer = pad(segment!.buffer);
          source.connect(audioWrap.gainNode);
          options.onReceiveAudioBuffer?.({
            context,
            source,
          });

          if (nextTime === 0) {
            nextTime = currentTime + 0.2; /// add 700ms latency to work well across systems - tune this if you like
          }

          let { duration } = source.buffer;
          let offset = 0;

          if (currentTime > nextTime) {
            offset = currentTime - nextTime;
            nextTime = currentTime;
            duration = duration - offset;
          }

          // source.start(nextTime, offset);
          source.start(nextTime);
          // source.stop(nextTime + duration);
          //  等待本段buffersource 播放完毕
          await new Promise((resolve) => {
            source.onended = (ev) => {
              source.onended = null;
              resolve(void 0);
              // requestAnimationFrame(resolve);
            };
          });

          nextTime += duration; // Make the next buffer wait the length of the last buffer before being played
        }
      }
      if (streamDone) {
        if (!stopped) {
          setTimeout(() => {
            options.onAudioStreamDone?.({
              context: audioWrap?.audioContext || null,
            });
          }, 50);
        }
      }
      scheduleBuffersTimeoutId = setTimeout(() => scheduleBuffers(), 500);
    };

    const handleReadData: StreamReaderCallbackType<Uint8Array> = async (
      done,
      value
    ) => {
      if (value?.buffer) {
        let buffer: ArrayBuffer;
        // console.log(
        //   'on read data ',
        //   'done',
        //   done,
        //   'value: ',
        //   value,
        //   'arraybuf',
        //   value.buffer,
        //   'byteLength: ',
        //   value.buffer.byteLength,
        // );
        const segment: IAudioSegment = {
          buffer: null,
        };

        if (rest !== null) {
          buffer = concat(rest, value.buffer);
        } else {
          buffer = value.buffer;
        }

        // // Make sure that the first buffer is lager then 44 bytes.
        if (isFirstBuffer && buffer.byteLength <= 44) {
          rest = buffer;
          return;
        }

        // 获取到了wav头44个字节描述信息，获取通道数 和 采样率
        if (isFirstBuffer) {
          isFirstBuffer = false;

          const dataView = new DataView(buffer);

          numberOfChannels = dataView.getUint16(22, true);
          sampleRate = dataView.getUint32(24, true);

          buffer = buffer.slice(44);
          // 这里从wav header 中获取到采样率之后再初始化 audioContext
          audioWrap = initAudioContext({ sampleRate, volume });
          context = audioWrap.audioContext;
        }

        if (buffer.byteLength % 2 !== 0) {
          rest = buffer.slice(buffer.byteLength - 1);
          buffer = buffer.slice(0, buffer.byteLength - 1);
        } else {
          rest = null;
        }
        if (buffer.byteLength === 0) return;
        if (options.rawPCM) {
          // 获取脱头后的原始pcm
          options.onRawPCMBuffer?.({
            buffer,
            channels: numberOfChannels,
            sampleRate,
          });
          return;
        }
        // 给每一段buffer 加上wav header 再送入 decodeAudioData 进行解码，相当于每段buffer都送到浏览器解码后再去播放，TODO: 这里可以进行一定的优化，
        // 解码后直接复用 pcm-stream-decoder逻辑
        const transformedBuf = wavify(buffer, numberOfChannels, sampleRate);
        if (context) {
          try {
            const audioBuffer = await context.decodeAudioData(transformedBuf);
            segment.buffer = audioBuffer;
            audioStack.push(segment);
            if (scheduleBuffersTimeoutId === null) {
              scheduleBuffersTimeoutId = setTimeout(() => {
                scheduleBuffers();
              }, 0);
              // scheduleBuffers();
            }
          } catch (e) {
            console.error('wav buffer decode error ', transformedBuf, e);
          }
        }
      } else if (done) {
        //
        streamDone = true;
        if (options.rawPCM) {
          options.onAudioStreamDone?.({
            context: audioWrap?.audioContext || null,
          });
        }
      }
    };

    startRead(handleReadData);
    const handle = {
      isPaused: () => paused,
      stop: async () => {
        console.log('decode stop');
        stopped = true;
        clearTimeout(scheduleBuffersTimeoutId);
        options?.onStop?.({
          context: audioWrap?.audioContext || null,
        });
        if (context) {
          await to(context.close());
        }
        await to(stopRead());
      },
      pause: async () => {
        paused = true;
        clearTimeout(scheduleBuffersTimeoutId);
        await stopRead();
      },
      resume: async () => {
        if (stopped) throw new Error('cannot resume a stopped decoder');
        paused = false;
        startRead(handleReadData);
      },
    };
    return handle;
  };
  return {
    decode,
    setVolume: (value) => {
      volume = value;
      if (audioWrap) {
        audioWrap.gainNode.gain.value = value;
      }
    },
  };
};
