export const concatBuf = (buffer1: <PERSON><PERSON>yBuffer, buffer2: <PERSON><PERSON>y<PERSON>uffer) => {
  const tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);

  tmp.set(new Uint8Array(buffer1), 0);
  tmp.set(new Uint8Array(buffer2), buffer1.byteLength);

  return tmp.buffer;
};

export const copyBuf = (buf: ArrayBuffer) => {
  const tmp = new Uint8Array(buf.byteLength);
  tmp.set(new Uint8Array(buf), 0);
  return tmp.buffer;
};

// 初始化一个可调节音量的audioContext
export const initAudioContext = (options: {
  // 音量
  volume: number;
  sampleRate?: number;
}) => {
  const ctxOptions: AudioContextOptions = {};
  if (typeof options.sampleRate === 'number') {
    ctxOptions.sampleRate = options.sampleRate;
  }
  const audioContext = new AudioContext(ctxOptions);
  const gainNode = audioContext.createGain();
  gainNode.gain.value = options.volume;
  return {
    audioContext,
    gainNode,
  };
};

export function to16BitPCM(input: Float32Array): DataView {
  const dataLength: number = input.length * (16 / 8);
  const dataBuffer: ArrayBuffer = new ArrayBuffer(dataLength);
  const dataView: DataView = new DataView(dataBuffer);
  let offset = 0;
  for (let i = 0; i < input.length; i++, offset += 2) {
    const s: number = Math.max(-1, Math.min(1, input[i]));
    dataView.setUint16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
  }
  return dataView;
}

export function to16kHz(audioData: number[], sampleRate = 44100): Float32Array {
  const data: Float32Array = new Float32Array(audioData);
  const fitCount: number = Math.round(data.length * (16000 / sampleRate));
  const newData: Float32Array = new Float32Array(fitCount);
  const springFactor: number = (data.length - 1) / (fitCount - 1);
  newData[0] = data[0];
  for (let i = 1; i < fitCount - 1; i++) {
    const tmp: number = i * springFactor;
    const before: number = Math.floor(tmp);
    const after: number = Math.ceil(tmp);
    const atPoint: number = tmp - before;
    newData[i] = data[before] + (data[after] - data[before]) * atPoint;
  }
  newData[fitCount - 1] = data[data.length - 1];
  return newData;
}
