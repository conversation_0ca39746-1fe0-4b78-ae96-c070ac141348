export type AudioCodecType = 'pcm' | 'opus' | 'mp3' | 'aac' | 'wav';
export interface IAudioPlayerOptions
  extends Pick<
    IAudioDecoderOptions,
    'waitForNoDataTimeout' | 'onAudioStreamDone' | 'volume'
  > {
  // 编码格式
  codec: AudioCodecType;

  pcmPlayerOptions?: IPCMPlayerOptions;
}

export type IMediaSourcePlayerOptions = Pick<IAudioPlayerOptions, 'codec'>;

export interface IAudioPlayer {
  play: (stream: ReadableStream) => Promise<void>;

  pause: () => Promise<void>;

  destroy: () => Promise<void>;

  volume: (value: number) => void;
}

export interface IAudioDecoderOptions {
  // 音量大小
  volume?: number;
  // 等待多久没数据后停止
  waitForNoDataTimeout?: number;
  // 接收到audio buffer
  onReceiveAudioBuffer?: (event: {
    context: AudioContext;
    source: AudioBufferSourceNode;
  }) => void;

  // 音频流结束事件
  onAudioStreamDone?: (event: { context: AudioContext | null }) => void;

  // 外部触发stop事件
  onStop?: (event: { context: AudioContext | null }) => void;
}

export interface IAudioDecodeMethodReturns {
  isPaused: () => boolean;
  stop: () => Promise<void>;
  pause: () => Promise<void>;
  /**
   *
   * @returns
   * @throws
   */
  resume: () => Promise<void>;
}
/** 解码器接口 */
export interface IAudioDecoder<DO extends IAudioDecoderOptions> {
  /**
   *
   * @param options
   * @returns
   * @throws
   */
  decode: (options: DO) => IAudioDecodeMethodReturns;

  setVolume: (value: number) => void;
}

export type AudioDecoderFactory<DO extends IAudioDecoderOptions> = (
  stream: ReadableStream
) => IAudioDecoder<DO>;

// pcm 相关配置
// pcm 编码位数
export type PCMInputCodecs = 'Int8' | 'Int16' | 'Int32' | 'Float32';
export interface IPCMDecoderOptions extends IAudioDecoderOptions {
  // 采样精度
  inputCodec: PCMInputCodecs;
  // 通道数
  channels: number;

  // 采样率
  sampleRate: number;
}
export type IPCMPlayerOptions = Pick<
  IPCMDecoderOptions,
  'channels' | 'inputCodec' | 'sampleRate' | 'onAudioStreamDone'
>;
