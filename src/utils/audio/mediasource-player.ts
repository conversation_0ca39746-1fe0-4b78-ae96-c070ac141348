/* eslint-disable class-methods-use-this */
import { createStreamReader } from '../stream-reader';
import { IAudioPlayer, IMediaSourcePlayerOptions } from './typings';
import mime from 'mime';

// 通过media source 播放编码后的音频
export class MediaSourcePlayer implements IAudioPlayer {
  protected audio: HTMLAudioElement = new Audio();

  protected mediaSource = new MediaSource();

  protected sourceBuffer!: SourceBuffer;

  protected queue: ArrayBuffer[] = [];

  protected sourceStream!: ReadableStream;

  protected streamReader!: ReturnType<typeof createStreamReader<ArrayBuffer>>;

  protected mimeType: string;

  public constructor(protected options: IMediaSourcePlayerOptions) {
    const mimeType = mime.getType(this.options.codec);
    if (mimeType && !MediaSource.isTypeSupported(mimeType as string))
      throw new Error(`codec invalid: ${this.options.codec}`);
    this.mimeType = mimeType as string;
  }

  public async play(stream: ReadableStream) {
    this.sourceStream = stream;
    this.audio.src = URL.createObjectURL(this.mediaSource);

    this.mediaSource.addEventListener('sourceopen', this.handleSourceOpen);
  }

  public async destroy() {
    this.streamReader?.stopRead();
  }
  public volume(value: number) {
    this.audio.volume = value;
  }
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  public async pause() {}

  protected handleSourceOpen = () => {
    this.sourceBuffer = this.mediaSource.addSourceBuffer(this.mimeType);
    this.sourceBuffer.addEventListener('updateend', this.handleSourceBufferEnd);
    this.audio.play();
    this.streamReader = createStreamReader(this.sourceStream);
    // 开始接收流

    this.streamReader.startRead(async (done, res) => {
      if (!res) return;
      if (done) {
        return;
      }
      // 一段流赋值添加到sourceBuffer
      if (this.sourceBuffer && !this.sourceBuffer.updating) {
        // 当 appendBuffer 操作执行完毕 this.sourceBuffer.updating 为 false 的时候
        this.sourceBuffer.appendBuffer(res);
      } else {
        // 当 appendBuffer 操作未执行完毕时 将媒体流加入对列等候处理
        this.queue.push(res);
      }
    });
  };
  protected handleSourceBufferEnd = () => {
    if (this.queue.length > 0 && !this.sourceBuffer.updating) {
      this.sourceBuffer.appendBuffer(this.queue.shift()!);
    }
  };

  protected async readStream(cb: (value: ArrayBuffer) => Promise<void>) {
    const reader = this.sourceStream.getReader();
    const read = async () => {
      const result = await reader.read();
      const { value, done } = result;
      if (done) {
        await cb(value);
        return;
      }
      try {
        await cb(value);
      } catch (e) {
        console.error();
      }
      read();
    };
    await read();
  }
}
