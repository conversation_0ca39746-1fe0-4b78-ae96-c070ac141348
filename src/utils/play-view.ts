import { CommonStyle } from '@tencent/pagedoo-library';
export const baseSize = {
  width: 455,
  height: 812,
};

// 远景
const loneShot = {
  bkg: commonStyle(0, -98, 455),
  human: commonStyle(66, 104, 375),
  table: commonStyle(76, 494, 314),
  product: commonStyle(106, 402, 123),
};
// 中景
const mediumShot = {
  bkg: commonStyle(-45, -161, 546),
  human: commonStyle(22, 177, 450),
  table: commonStyle(39, 646, 375),
  product: commonStyle(89, 582, 140),
};
// 近景
const closeShot = {
  bkg: commonStyle(-114, -220, 685),
  human: commonStyle(16, 121, 525),
  table: commonStyle(-7, 645, 469),
  product: commonStyle(51, 521, 172.2),
};
// 特写
const closeUp = {
  bkg: commonStyle(0, 0, 455),
  human: commonStyle(5000, 5000, 1),
};

export const allViews = {
  远景: loneShot,
  中景: mediumShot,
  近景: closeShot,
  特写: closeUp,
  全屏: {
    // bkg: commonStyle(0, 0, 375),
    common: commonStyle(0, -58, 455),
  },
  隐藏: {
    // bkg: commonStyle(0, 0, 375),
    common: commonStyle(5000, 5000, 1),
  },
  横屏远景: {
    bkg: commonStyle(0, 0, 455),
    human: {
      position: {
        top: 9.477379095163807,
        bottom: 0,
        left: 61.54446177847114,
        right: 0,
        outset: 'TopLeft',
        unit: {
          left: 'percent',
          right: 'percent',
          top: 'percent',
          bottom: 'percent',
        },
        type: 'absolute',
      },
      size: {
        autoWidth: false,
        width: 43.91575663026521,
        autoHeight: true,
        height: 100,
        widthUnit: 'percent',
        heightUnit: 'percent',
      },
    } satisfies CommonStyle,
  },
  腾讯充值: {
    bkg: commonStyle(0, 0, 455),
    transition: commonStyle(0, 0, 455),
    phone_in_portrait_orientation: commonStyle(254, 140, 190), // 竖屏手机展示区
    phone_in_portrait_orientation_bkg: commonStyle(200, 16, 215), // 竖屏手机背景
    phone_in_landscape_orientation: commonStyle(38, 130, 379), // 横屏手机展示区
    phone_in_landscape_orientation_bkg: commonStyle(0, 0, 455), // 竖屏手机背景
    phone_in_fullscreen: commonStyle(0, 0, 455), // 全屏手机展示区
    phone_in_fullscreen_bkg: {
      position: {
        top: -5000,
        left: -5000,
        bottom: 0,
        right: 0,
        type: 'absolute',
        outset: 'TopLeft',
        unit: {
          left: 'percent',
          right: 'percent',
          top: 'percent',
          bottom: 'percent',
        },
      },
    } as CommonStyle, // 全屏手机背景（不需要背景）
    // 计算偏移量时，用 handler.getSelectedComponentSync() 里得到的值 * 9.1 即可
    // 例如 commonStyle.top = 32.37，则偏移量填入 32.37 * 9.1 ≈ 295
    顶部: commonStyle(0, 0, 455),
    顶部80: commonStyle(0, 80, 455),
    上面: commonStyle(0, 50, 455),
    中间: commonStyle(0, 295, 455),
    居中: commonStyle(0, 295, 455),
    右上角: commonStyle(254, 60, 190),
    左上角: commonStyle(22, 57, 190),
    左上: commonStyle(22, 57, 190),
    全屏: commonStyle(0, 0, 455),
    human_left: commonStyle(-17, 120, 286),
    human_right: commonStyle(185, 102.5, 315),
    human_primary: commonStyle(0, -10, 455),
    human_using_phone_in_portrait_orientation: commonStyle(0, -10, 455),
    human_using_phone_in_landscape_orientation: commonStyle(0, -10, 455),
    human_using_phone_in_fullscreen: commonStyle(0, 20, 455), // 小数字人
    foreground: commonStyle(0, 682, 455), // 前景
    foreground_double: commonStyle(0, 570, 455), // 双人前景
    hidden: {
      position: {
        top: -5000,
        left: -5000,
        bottom: 0,
        right: 0,
        type: 'absolute',
        outset: 'TopLeft',
        unit: {
          left: 'percent',
          right: 'percent',
          top: 'percent',
          bottom: 'percent',
        },
      },
    } as CommonStyle,
  },
  //   add 加一个横屏远景
  广告联调模板: {
    bkg: commonStyle(0, 0, 455),
    human: commonStyle(0, 160, 455),
    price: commonStyle(42, 230, 100),
    video: commonStyle(1000, 100, 200),
    speech: commonStyle(5000, 5000, 0),
    sound: commonStyle(5000, 5000, 0),
    qa: commonStyle(5000, 5000, 0),
    bkgMusic: commonStyle(5000, 5000, 0),
    title: commonStyle(35, 50, 400),
    fgd: commonStyle(-2, 650, 461),
    hidden: {
      position: {
        top: -5000,
        left: -5000,
        bottom: 0,
        right: 0,
        type: 'absolute',
        outset: 'TopLeft',
        unit: {
          left: 'percent',
          right: 'percent',
          top: 'percent',
          bottom: 'percent',
        },
      },
    } as CommonStyle,
  },
  hidden: {
    position: {
      top: -5000,
      left: -5000,
      bottom: 0,
      right: 0,
      type: 'absolute',
      outset: 'TopLeft',
      unit: {
        left: 'percent',
        right: 'percent',
        top: 'percent',
        bottom: 'percent',
      },
    },
  } as CommonStyle,
};

export const commonHiddenStyle = {
  position: {
    top: -5000,
    left: -5000,
    bottom: 0,
    right: 0,
    type: 'absolute',
    outset: 'TopLeft',
    unit: {
      left: 'percent',
      right: 'percent',
      top: 'percent',
      bottom: 'percent',
    },
  },
} as CommonStyle;

function toPercent(px: number) {
  return (px / baseSize.width) * 100;
}
function toWidthPercent(px: number) {
  return (px / baseSize.width) * 100;
}
export function commonStyle(
  left: number,
  top: number,
  width: number,
  ratio = baseSize.width
): CommonStyle {
  return {
    position: {
      top: toPercent((top * baseSize.width) / ratio) / 2,
      bottom: 0,
      left: toPercent((left * baseSize.width) / ratio),
      right: 0,
      outset: 'TopLeft',
      unit: {
        left: 'percent',
        right: 'percent',
        top: 'percent',
        bottom: 'percent',
      },
      type: 'absolute',
    },
    size: {
      autoWidth: false,
      width: toPercent((width * baseSize.width) / ratio),
      autoHeight: true,
      height: 100,
      widthUnit: 'percent',
      heightUnit: 'percent',
    },
  };
}
