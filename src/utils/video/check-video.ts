/**
 * 判断视频是否可以进行播放
 * 如果不能播放，可能是上传了浏览器不支持的
 */
export const checkVideoCanplay = async (file: File) => {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file);
    const video = document.createElement('video');

    video.preload = 'metadata';

    video.oncanplaythrough = function () {
      URL.revokeObjectURL(video.src); // 释放内存
      console.log('video canplay ');
      if (!video.videoWidth) {
        console.log('video cannot play');
        return reject(new Error('video cannot play'));
      }
      resolve(true);
    };

    video.onerror = function (e) {
      console.error('video error ', e);
      reject(e);
    };

    video.src = url;
  });
};
