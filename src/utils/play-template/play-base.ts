import { PlayConfig, PlayService, Script } from '@/type/pagedoo';
import {
  liveImage,
  MaterialsAvatar,
  MaterialsBase,
} from '@/utils/play-component';
import { newPlayConfig } from '@/utils/template';
import {
  genPagedooScriptPlugin,
  pagedooScriptPlugin,
} from '@/utils/template/common';
import { cloneDeep } from 'lodash';

export const playService: PlayService = {
  async getPlayScript(script: Script) {
    return await newPlayConfig({
      plugins: [await pagedooScriptPlugin(script)],
    });
  },
  async getPageSchema(script: Script) {
    const playScript = await this.getPlayScript(script);
    return this.getPageSchemaFromConfig(playScript);
  },
  async getPageSchemaFromConfig(playScript: PlayConfig) {
    return {
      global: {
        pages: [
          {
            data: {
              components: [
                {
                  id: `component/${MaterialsBase}/Background`,
                  key: 0,
                  name: 'Background',
                  style: {},
                  commonStyle: {},
                  wrapperStyle: {},
                  data: {
                    __component_name: '背景设置',
                    backgroundConf: {
                      type: 'color',
                      backgroundColor: {
                        color: '',
                        show: true,
                        realColor: 'unset',
                      },
                    },
                  },
                  actions: [],
                  children: [
                    {
                      id: `component/${MaterialsAvatar}/Director`,
                      name: 'Director',
                      key: 1,
                      style: {},
                      commonStyle: {},
                      wrapperStyle: {},
                      chosen: false,
                      data: {
                        _v: 0,
                        play: true,
                        __component_name: '导演(Director)',
                        __component_sub_name: '导演组件 负责脚本执行，渲染画面',
                        __component_id: 'Director',
                        __component_mutex_data: '',
                        __pagedoo_i18n: {},
                      },
                      actions: [],
                    },
                  ],
                },
              ],
              plugins: [],
              version: {
                versionId: 'v606055e',
                versionName: 'default',
              },
            },
            page_id: 'index',
          },
        ],
        'pagedoo-play-script': playScript,
      },
    };
  },
  async getPageSchemaShot(script: Script) {
    const playScript = await this.getPlayScript(script);
    return this.getPageSchemaShotFromConfig(playScript);
  },
  async getPageSchemaShotFromConfig(playScript: PlayConfig) {
    const schema = await playService.getPageSchemaFromConfig(
      cloneDeep(playScript)
    );
    for (const pagedooPlayTimeline of schema.global['pagedoo-play-script']
      .timeline) {
      if (!pagedooPlayTimeline.node[0]) {
        pagedooPlayTimeline.node = [];
        continue;
      }
      pagedooPlayTimeline.node = [pagedooPlayTimeline.node[0]];
      // 虚拟人替换为假人
      if (pagedooPlayTimeline.node[0]?.component.id.includes('Virtualman')) {
        pagedooPlayTimeline.node[0].component = liveImage(
          pagedooPlayTimeline.node[0].key,
          'https://pagedoo.pay.qq.com/material/@platform/6b1a359f29a07ab29a569037f4c5988a.png',
          pagedooPlayTimeline.node[0].component.commonStyle
        );
      }
    }
    if (schema.global['pagedoo-play-script'].fragment.length)
      schema.global['pagedoo-play-script'].fragment.length = 1;
    return schema;
  },
  async genPlayScriptByTemplate({ script, templateId, isLive, isVideo }) {
    return await newPlayConfig({
      plugins: [
        await genPagedooScriptPlugin({ script, templateId, isLive, isVideo }),
      ],
    });
  },
};
