/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
export function to16BitPCM(input: Float32Array): DataView {
  const dataLength: number = input.length * (16 / 8);
  const dataBuffer: ArrayBuffer = new ArrayBuffer(dataLength);
  const dataView: DataView = new DataView(dataBuffer);
  let offset = 0;
  for (let i = 0; i < input.length; i++, offset += 2) {
    const s: number = Math.max(-1, Math.min(1, input[i]));
    dataView.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
  }
  return dataView;
}

export function to16kHz(audioData: number[], sampleRate = 44100): Float32Array {
  const data: Float32Array = new Float32Array(audioData);
  const fitCount: number = Math.round(data.length * (16000 / sampleRate));
  const newData: Float32Array = new Float32Array(fitCount);
  const springFactor: number = (data.length - 1) / (fitCount - 1);
  newData[0] = data[0];
  for (let i = 1; i < fitCount - 1; i++) {
    const tmp: number = i * springFactor;
    const before: number = Math.floor(tmp);
    const after: number = Math.ceil(tmp);
    const atPoint: number = tmp - before;
    newData[i] = data[before] + (data[after] - data[before]) * atPoint;
  }
  newData[fitCount - 1] = data[data.length - 1];
  return newData;
}

const audioWorkletCode = `
class MyProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super(options);
    this.audioData = [];
    this.nextUpdateFrame = 40;
    this.sampleCount = 0;
    this.bitCount = 0;
  }

  get intervalInFrames() {
    return 40 / 1000 * sampleRate;
  }

  process(inputs) {
    if (inputs[0][0]) {
      const output = ${to16kHz}(inputs[0][0], sampleRate);
      this.sampleCount += 1;
      const audioData = ${to16BitPCM}(output);
      this.bitCount += 1;
      const data = [...new Int8Array(audioData.buffer)];
      this.audioData = this.audioData.concat(data);
      this.nextUpdateFrame -= inputs[0][0].length;
      if (this.nextUpdateFrame < 0) {
        this.nextUpdateFrame += this.intervalInFrames;
        this.port.postMessage({
          audioData: new Int8Array(this.audioData),
          sampleCount: this.sampleCount,
          bitCount: this.bitCount
        });
        this.audioData = [];
      }
      return true;
    }
    return false;
  }
}

registerProcessor('my-processor', MyProcessor);
`;

const TAG = 'WebRecorder';

class WebRecorder {
  audioData: number[];
  allAudioData: number[];
  stream: MediaStream | null;
  audioContext: AudioContext | null;
  requestId: string;
  frameTime: string[];
  frameCount: number;
  sampleCount: number;
  bitCount: number;
  mediaStreamSource: MediaStreamAudioSourceNode | null;
  isLog: boolean;
  getDataCount!: number;
  audioTrack: MediaStreamTrack | null = null;

  constructor(requestId: string, isLog: boolean) {
    this.audioData = [];
    this.allAudioData = [];
    this.stream = null;
    this.audioContext = null;
    this.requestId = requestId;
    this.frameTime = [];
    this.frameCount = 0;
    this.sampleCount = 0;
    this.bitCount = 0;
    this.mediaStreamSource = null;
    this.isLog = isLog;
  }

  static isSupportMediaDevicesMedia(): boolean {
    return !!navigator.mediaDevices?.getUserMedia;
  }

  static isSupportAudioContext(): boolean {
    return typeof AudioContext !== 'undefined';
  }

  static isSupportMediaStreamSource(
    _requestId: string,
    audioContext: AudioContext
  ): boolean {
    return typeof audioContext.createMediaStreamSource === 'function';
  }

  static isSupportAudioWorklet(audioContext: AudioContext): boolean {
    return (
      audioContext.audioWorklet &&
      typeof audioContext.audioWorklet.addModule === 'function' &&
      typeof AudioWorkletNode !== 'undefined'
    );
  }

  static isSupportCreateScriptProcessor(
    _requestId: string,
    audioContext: AudioContext
  ): boolean {
    return typeof audioContext.createScriptProcessor === 'function';
  }

  async start(): Promise<void> {
    this.frameTime = [];
    this.frameCount = 0;
    this.allAudioData = [];
    this.audioData = [];
    this.sampleCount = 0;
    this.bitCount = 0;
    this.getDataCount = 0;
    this.audioContext = null;
    this.mediaStreamSource = null;
    this.stream = null;
    try {
      if (WebRecorder.isSupportAudioContext()) {
        this.audioContext = new window.AudioContext();
      } else {
        this.isLog &&
          console.log(this.requestId, '浏览器不支持AudioContext', TAG);
        this.OnError('浏览器不支持AudioContext');
      }
    } catch (e) {
      this.isLog &&
        console.log(this.requestId, '浏览器不支持webAudioApi相关接口', e, TAG);
      this.OnError('浏览器不支持webAudioApi相关接口');
    }
    await this.getUserMedia(
      this.requestId,
      this.getAudioSuccess,
      this.getAudioFail
    );
  }

  stop(): void {
    if (
      !(
        /Safari/.test(navigator.userAgent) &&
        !/Chrome/.test(navigator.userAgent)
      )
    ) {
      this.audioContext?.suspend();
    }
    this.audioContext?.suspend();
    // 停止流，防止浏览器一直在录制
    this.audioTrack?.stop();
    this.isLog &&
      console.log(
        this.requestId,
        `webRecorder stop ${this.sampleCount}/${this.bitCount}/${this.getDataCount}`,
        JSON.stringify(this.frameTime),
        TAG
      );
    this.OnStop(this.allAudioData);
  }

  destroyStream(): void {
    if (this.stream) {
      this.stream.getTracks().map((val) => {
        val.stop();
      });
      this.stream = null;
    }
  }

  async getUserMedia(
    requestId: string,
    getStreamAudioSuccess: Function,
    getStreamAudioFail: Function
  ): Promise<void> {
    const mediaOption: MediaStreamConstraints = {
      audio: true,
      video: false,
    };
    if (WebRecorder.isSupportMediaDevicesMedia()) {
      try {
        const stream: MediaStream = await navigator.mediaDevices.getUserMedia(
          mediaOption
        );
        this.stream = stream;
        getStreamAudioSuccess.call(this, requestId, stream);
      } catch (e) {
        getStreamAudioFail.call(this, requestId, e);
      }
    } else {
      if (
        navigator.userAgent.toLowerCase().match(/chrome/) &&
        location.origin.indexOf('https://') < 0
      ) {
        this.isLog &&
          console.log(
            this.requestId,
            'chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限',
            TAG
          );
        this.OnError(
          'chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限'
        );
      } else {
        this.isLog &&
          console.log(
            this.requestId,
            '无法获取浏览器录音功能，请升级浏览器或使用chrome',
            TAG
          );
        this.OnError('无法获取浏览器录音功能，请升级浏览器或使用chrome');
      }
      this.audioContext?.close();
    }
  }

  async getAudioSuccess(requestId: string, stream: MediaStream): Promise<void> {
    if (!this.audioContext) {
      return;
    }
    if (this.mediaStreamSource) {
      this.mediaStreamSource.disconnect();
      this.mediaStreamSource = null;
    }
    this.audioTrack = stream.getAudioTracks()[0];
    const mediaStream: MediaStream = new MediaStream();
    mediaStream.addTrack(this.audioTrack);
    this.mediaStreamSource =
      this.audioContext.createMediaStreamSource(mediaStream);
    if (WebRecorder.isSupportMediaStreamSource(requestId, this.audioContext)) {
      if (WebRecorder.isSupportAudioWorklet(this.audioContext)) {
        this.audioWorkletNodeDealAudioData(this.mediaStreamSource, requestId);
      } else {
        this.scriptNodeDealAudioData(this.mediaStreamSource, requestId);
      }
    } else {
      this.isLog && console.log(this.requestId, '不支持MediaStreamSource', TAG);
      this.OnError('不支持MediaStreamSource');
    }
  }

  getAudioFail(requestId: string, err: any): void {
    if (err?.err && err.err.name === 'NotAllowedError') {
      this.isLog &&
        console.log(requestId, '授权失败', JSON.stringify(err.err), TAG);
    }
    this.isLog &&
      console.log(this.requestId, 'getAudioFail', JSON.stringify(err), TAG);
    this.OnError(err);
    this.stop();
  }

  scriptNodeDealAudioData(
    _mediaStreamSource: MediaStreamAudioSourceNode,
    requestId: string
  ): void {
    if (
      WebRecorder.isSupportCreateScriptProcessor(requestId, this.audioContext!)
    ) {
      const scriptProcessor: ScriptProcessorNode =
        this.audioContext!.createScriptProcessor(1024, 1, 1);
      this.mediaStreamSource?.connect(scriptProcessor);
      scriptProcessor?.connect(this.audioContext!.destination);
      scriptProcessor.onaudioprocess = (e: AudioProcessingEvent) => {
        this.getDataCount += 1;
        const inputData: Float32Array = e.inputBuffer.getChannelData(0);
        const output = to16kHz(
          Array.from(inputData),
          this.audioContext!.sampleRate
        );
        const audioData: DataView = to16BitPCM(output);
        this.audioData.push(...new Int8Array(audioData.buffer));
        this.allAudioData.push(...new Int8Array(audioData.buffer));
        if (this.audioData.length > 1280) {
          this.frameTime.push(`${Date.now()}-${this.frameCount}`);
          this.frameCount += 1;
          const audioDataArray: Int8Array = new Int8Array(this.audioData);
          this.OnReceivedData(audioDataArray);
          this.audioData = [];
          this.sampleCount += 1;
          this.bitCount += 1;
        }
      };
    } else {
      this.isLog &&
        console.log(this.requestId, '不支持createScriptProcessor', TAG);
    }
  }

  async audioWorkletNodeDealAudioData(
    mediaStreamSource: MediaStreamAudioSourceNode,
    requestId: string
  ): Promise<void> {
    try {
      const audioWorkletBlobURL: string = window.URL.createObjectURL(
        new Blob([audioWorkletCode], { type: 'text/javascript' })
      );
      await this.audioContext!.audioWorklet.addModule(audioWorkletBlobURL);
      const myNode: AudioWorkletNode = new AudioWorkletNode(
        this.audioContext!,
        'my-processor',
        {
          numberOfInputs: 1,
          numberOfOutputs: 1,
          channelCount: 1,
        }
      );
      myNode.onprocessorerror = () => {
        this.scriptNodeDealAudioData(mediaStreamSource, this.requestId);
        return false;
      };
      myNode.port.onmessage = (event: MessageEvent) => {
        this.frameTime.push(`${Date.now()}-${this.frameCount}`);
        this.OnReceivedData(event.data.audioData);
        this.frameCount += 1;
        this.allAudioData.push(...event.data.audioData);
        this.sampleCount = event.data.sampleCount;
        this.bitCount = event.data.bitCount;
      };
      myNode.port.onmessageerror = (_event: MessageEvent) => {
        this.scriptNodeDealAudioData(mediaStreamSource, requestId);
        return false;
      };
      mediaStreamSource
        ?.connect(myNode)
        .connect(this.audioContext!.destination);
    } catch (e) {
      this.isLog &&
        console.log(
          this.requestId,
          'audioWorkletNodeDealAudioData catch error',
          JSON.stringify(e),
          TAG
        );
      this.OnError(e as Error);
    }
  }

  OnReceivedData(_data: Int8Array): void {
    /** */
  }
  OnError(_res: Error | string): void {
    /** */
  }
  OnStop(_res: number[]): void {
    /** */
  }
}

export { WebRecorder as WebAudioRecorder };
