/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable class-methods-use-this */
/* eslint-disable no-prototype-builtins */

import HMACSHA1 from 'crypto-js/hmac-sha1';
const needFiltrationParams: string[] = ['appid', 'secretkey', 'signCallback'];

export interface IAudioRecognizeParams {
  // 用户参数
  // 临时秘钥
  secretid: string;
  // 临时secretkey
  secretkey: string;
  // 临时秘钥返回的token
  token?: string;
  // 腾讯云appid
  appid: string;
  // 实时识别接口参数
  engine_model_type: '16k_zh' | ''; // 引擎
  voice_format: number;
  signCallback?: (queryStr: string) => Promise<string>; // 鉴权函数 用户提供鉴权函数，不传则为null
  // 以下为非必填参数，可跟据业务自行修改
  hotword_id?: string;
  needvad?: number;
  filter_dirty?: number;
  filter_modal?: number;
  filter_punc?: number;
  convert_num_mode?: number;
  word_info?: number;
}

function formatSignString(
  query: Record<string, string>,
  params: Record<string, string>
): string {
  let strParam = '';
  let signStr = 'asr.cloud.tencent.com/asr/v2/';
  if (query.appid) {
    signStr += query.appid;
  }
  const keys = Object.keys(params);
  keys.sort();
  for (let i = 0, len = keys.length; i < len; i++) {
    strParam += `&${keys[i]}=${params[keys[i]]}`;
  }
  return `${signStr}?${strParam.slice(1)}`;
}

async function createQuery(
  query: IAudioRecognizeParams
): Promise<Record<string, string>> {
  let params: Record<string, any> = {};
  const time = new Date().getTime();

  async function getServerTime(): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'https://asr.cloud.tencent.com/server_time', true);
        xhr.send();
        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4 && xhr.status === 200) {
            resolve(xhr.responseText);
          }
        };
      } catch (error) {
        reject(error);
      }
    });
  }
  const serverTime = await getServerTime();
  params.secretid = query.secretid || '';
  params.engine_model_type = query.engine_model_type || '16k_zh';
  params.timestamp = parseInt(serverTime) || Math.round(time / 1000);
  params.expired = Math.round(time / 1000) + 24 * 60 * 60;
  params.nonce = Math.round(time / 100000);
  params.voice_id = guid();
  params.voice_format = query.voice_format || '1';

  const tempQuery: Record<string, any> = { ...query };
  for (let i = 0, len = needFiltrationParams.length; i < len; i++) {
    if (tempQuery.hasOwnProperty(needFiltrationParams[i])) {
      delete tempQuery[needFiltrationParams[i]];
    }
  }

  params = {
    ...tempQuery,
    ...params,
  };
  return params;
}

export const guid = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

async function getUrl(
  self: SpeechRecognizer,
  params: IAudioRecognizeParams
): Promise<string | false> {
  if (!params.appid || !params.secretid) {
    self.isLog && console.log(self.requestId, '请确认是否填入账号信息', TAG);
    self.OnError('请确认是否填入账号信息');
    return false;
  }
  const urlQuery = await createQuery(params);
  const queryStr = formatSignString(params as Record<string, any>, urlQuery);
  let signature = '';
  if (params.signCallback) {
    signature = await params.signCallback(queryStr);
  } else {
    signature = signCallback(params.secretkey, queryStr);
  }
  return `wss://${queryStr}&signature=${encodeURIComponent(signature)}`;
}

function toUint8Array(wordArray: any): Uint8Array {
  const { words } = wordArray;
  const { sigBytes } = wordArray;

  const u8 = new Uint8Array(sigBytes);
  for (let i = 0; i < sigBytes; i++) {
    u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  }
  return u8;
}

function Uint8ArrayToString(fileData: Uint8Array): string {
  let dataString = '';
  for (let i = 0; i < fileData.length; i++) {
    dataString += String.fromCharCode(fileData[i]);
  }
  return dataString;
}

function signCallback(secretKey: string, signStr: string): string {
  const hash = HMACSHA1(signStr, secretKey);
  const bytes = Uint8ArrayToString(toUint8Array(hash));
  return window.btoa(bytes);
}

const TAG = 'SpeechRecognizer';
export class SpeechRecognizer {
  socket: WebSocket | null;
  isSignSuccess: boolean;
  isSentenceBegin: boolean;
  query: IAudioRecognizeParams;
  isRecognizeComplete: boolean;
  requestId: string;
  isLog: boolean;
  sendCount: number;
  getMessageList: string[];

  constructor(
    params: IAudioRecognizeParams,
    requestId: string,
    isLog: boolean
  ) {
    this.socket = null;
    this.isSignSuccess = false;
    this.isSentenceBegin = false;
    this.query = {
      ...params,
    };
    this.isRecognizeComplete = false;
    this.requestId = requestId;
    this.isLog = isLog;
    this.sendCount = 0;
    this.getMessageList = [];
  }

  stop(): void {
    if (this.socket && this.socket.readyState === 1) {
      this.socket.send(JSON.stringify({ type: 'end' }));
      this.isRecognizeComplete = true;
    } else {
      if (this.socket && this.socket.readyState === 1) {
        this.socket.close();
      }
    }
  }

  async start(): Promise<void> {
    this.socket = null;
    this.getMessageList = [];
    const url = await getUrl(this, this.query);
    if (!url) {
      this.isLog && console.log(this.requestId, '鉴权失败', TAG);
      this.OnError('鉴权失败');
      return;
    }
    this.isLog && console.log(this.requestId, 'get ws url', url, TAG);
    if ('WebSocket' in window) {
      this.socket = new WebSocket(url);
    } else {
      this.isLog && console.log(this.requestId, '浏览器不支持WebSocket', TAG);
      this.OnError('浏览器不支持WebSocket');
      return;
    }
    this.socket.onmessage = async (e) => {
      try {
        this.getMessageList.push(JSON.stringify(e));
        const response = JSON.parse(e.data);
        if (response.code !== 0) {
          if (this.socket?.readyState === 1) {
            this.socket?.close();
          }
          this.isLog &&
            console.log(this.requestId, JSON.stringify(response), TAG);
          this.OnError(response);
        } else {
          if (!this.isSignSuccess) {
            this.OnRecognitionStart(response);
            this.isSignSuccess = true;
          }
          if (response.final === 1) {
            this.OnRecognitionComplete(response);
            return;
          }
          if (response.result) {
            if (response.result.slice_type === 0) {
              this.OnSentenceBegin(response);
              this.isSentenceBegin = true;
            } else if (response.result.slice_type === 2) {
              if (!this.isSentenceBegin) {
                this.OnSentenceBegin(response);
              }
              this.OnSentenceEnd(response);
            } else {
              this.OnRecognitionResultChange(response);
            }
          }
          this.isLog && console.log(this.requestId, response, TAG);
        }
      } catch (e) {
        this.isLog &&
          console.log(
            this.requestId,
            'socket.onmessage catch error',
            JSON.stringify(e),
            TAG
          );
      }
    };
    this.socket.onerror = (e) => {
      this.isLog &&
        console.log(this.requestId, 'socket error callback', e, TAG);
      this.socket?.close();
      this.OnError(e);
    };
    this.socket.onclose = (event) => {
      try {
        if (!this.isRecognizeComplete) {
          this.isLog &&
            console.log(
              this.requestId,
              'socket is close and error',
              JSON.stringify(event),
              TAG
            );
          this.OnError(event);
        }
      } catch (e) {
        this.isLog &&
          console.log(
            this.requestId,
            `socket is onclose catch${this.sendCount}`,
            JSON.stringify(e),
            TAG
          );
      }
    };
    this.socket.onopen = (e) => {
      this.isLog && console.log(this.requestId, '连接建立', e, TAG);
      this.OnConnected();
    };
  }

  close(): void {
    this.socket?.close(1000);
  }

  write(data: string | Int8Array): void {
    try {
      if (!this.socket || String(this.socket.readyState) !== '1') {
        setTimeout(() => {
          if (!this.socket || this.socket.readyState !== 1) {
            this.socket?.send(data);
          }
        }, 40);
      }
      this.sendCount += 1;
      this.socket?.send(data);
    } catch (e) {
      this.isLog && console.log(this.requestId, '发送数据 error catch', e, TAG);
    }
  }

  OnConnected(): void {}

  OnRecognitionStart(_res: any): void {}

  OnSentenceBegin(_res: any): void {}

  OnRecognitionResultChange(_response: Record<string, any>): void {}

  OnSentenceEnd(_response: Record<string, any>): void {}

  OnRecognitionComplete(_response: Record<string, any>): void {}

  OnError(_err: Error | string | Event): void {}
}

export { SpeechRecognizer as AudioRecognizer };
