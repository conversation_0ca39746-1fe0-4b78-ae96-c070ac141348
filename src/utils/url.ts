/**
 * 向当前 URL 追加参数
 * @param args
 */
export const addQueryParameters = (args: {
  name: string;
  value: string;
  mode: 'hash' | 'history';
}): void => {
  const { name, value, mode = 'history' } = args;
  if (mode === 'history') {
    const url = new URL(location.href);
    const params = new URLSearchParams(url.search);
    params.append(name, value);
    window.history.replaceState(
      {},
      '',
      decodeURIComponent(`${window.location.pathname}?${params}`)
    );
  } else if (mode === 'hash') {
    const { hash } = location;
    const queryList = hash.split('?');
    const hashName = queryList[0];
    let queryString = '';
    if (queryList.length === 1) {
      queryString = '?';
    } else if (queryList.length === 2) {
      queryString = `?${queryList[1]}&`;
    }
    queryString += `${name}=${value}`;
    location.href = `${location.protocol}//${location.hostname}${location.pathname}${hashName}${queryString}`;
  }
};
/**
 * 向当前 URL 替换参数
 * @param args
 */
export const replaceQueryParameters = (args: {
  name: string;
  value: string;
  mode: 'hash' | 'history';
}): void => {
  const { name, value, mode = 'history' } = args;
  if (mode === 'history') {
    const url = new URL(location.href);
    const params = new URLSearchParams(url.search);
    params.delete(name);
    params.append(name, value);
    window.history.replaceState(
      {},
      '',
      decodeURIComponent(`${window.location.pathname}?${params}`)
    );
  } else if (mode === 'hash') {
    const { hash } = location;
    const queryList = hash.split('?');
    const hashName = queryList[0];
    let queryString = '';
    if (queryList.length === 1) {
      queryString = '?';
    } else if (queryList.length === 2) {
      queryString = `?${queryList[1]}&`;
    }
    const re = new RegExp(`(${name}=)([^&]*)`, 'gi');
    queryString = queryString.replace(re, `${name}=${value}`);
    // queryString += `${name}=${value}`;
    location.href = `${location.protocol}//${location.hostname}${location.pathname}${hashName}${queryString}`;
  }
};

export function getParams() {
  const pageInfo = window.location.hash.split('?');
  if (pageInfo.length > 1) {
    const res: Record<string, string> = {};
    const search = pageInfo[1];
    const strs = search.split('&');
    for (let i = 0; i < strs.length; i++) {
      res[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1]);
    }
    return res;
  }
  return {};
}

/**
 *
 * 解析 URL 查询字符串中的参数
 */
export function parseQueryString(queryString: string) {
  const params = new URLSearchParams(queryString);
  const result: Record<string, any> = {};

  for (const [key, value] of params.entries()) {
    result[key] = value;
  }
  return result;
}

/**
 * 项目采用 hash 路由，多加一层解析
 */
export function parseHashQueryString(hashString: string) {
  const queryString = hashString.slice(hashString.indexOf('?') + 1);
  return parseQueryString(queryString);
}

/**
 * 清除 URL 中的 code 参数
 *
 * 有副作用的方法
 */
export function cleanUrlCodeParams() {
  const newHash = window.location.hash.replace(/([?&])code=[^&]*&?/, '$1');
  const cleanedHash = newHash.replace(/[?&]$/, '');
  window.history.replaceState(null, '', cleanedHash);
}
