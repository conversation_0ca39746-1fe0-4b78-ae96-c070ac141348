import {
  GlobalRuntimeConfig,
  MatchedGlobalConfigItem,
} from './../../../configs/global_runtime';
import to from 'await-to-js';
import { Development } from '../../../pb/pb';
import axios from 'axios';
import { pageSize } from '@/pages/Editor/SimpleEditor/TemplateModal/useCategory';
// 腾讯云试听接口封装

// 除了现网环境都用聚合版的appid
// const getAppKey = () =>
//   import.meta.env.VITE_ENV !== 'production'
//     ? GlobalRuntimeConfig.PAGEDOO.appIdInfo.virtualmanAppId
//     : MatchedGlobalConfigItem.appIdInfo.virtualmanAppId;
const getAppKey = () => MatchedGlobalConfigItem.appIdInfo.virtualmanAppId;
export interface IGetDemoTTSMediaParams {
  // 播报文本
  text: string;

  // 音色key
  timbreKey: string;

  // 音量大小
  volume: number;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IGetDemoTTSMediaOptions {
  /** */
  signal?: AbortSignal;
}

async function getSignUrl(params: Record<string, string | number>) {
  const sortedKeys = Object.keys(params).sort();
  const signArr: string[] = [];
  for (const k of sortedKeys) {
    signArr.push(`${k}=${params[k]}`);
  }
  let signStr = signArr.join('&');
  const sig = await getSignature(signStr);
  signStr += `&signature=${encodeURIComponent(sig)}`;
  return signStr;
}

const getSignature = async (queryStr: string) => {
  const [err, resp] = await to(
    Development.GetTencentCloudSign({
      query: queryStr,
      token_type: 'shuzhiren',
      appkey: getAppKey(),
    })
  );
  if (err) {
    throw err;
  }
  return resp.signature;
};

const baseUrl = 'https://gw.tvs.qq.com';

const waitForProcessEnd = async (
  data: { TaskId: string },
  options?: {
    abortSignal?: AbortSignal;
  }
): Promise<{
  audioUrl: string;
}> => {
  let timeout: unknown;
  if (options?.abortSignal) {
    options.abortSignal?.addEventListener('abort', () => {
      clearTimeout(timeout as any);
    });
  }
  return new Promise((resolve, reject) => {
    const polling = async () => {
      const params = {
        appkey: getAppKey(),
        TaskId: data.TaskId,
        timestamp: Math.floor(Date.now() / 1000),
      };
      const [err, queryStr] = await to(getSignUrl(params));
      if (err) return reject(err);
      const [getProgressErr, resp] = await to(
        axios.post<{
          Header: {
            Code: 0;
            Message: '';
            RequestID: '123';
          };
          Payload: {
            Progress: 100;
            MediaUrl: 'url';
            SubtitlesUrl: '';
            ArrayCount: 0;
            FailMessage: '';
            Duration: 11810;
            FailCode: 0;
            Status: 'COMMIT' | 'MAKING' | 'SUCCESS' | 'FAIL';
            TextTimestampResult: [
              {
                Sentence: string;
              }
            ];
          };
        }>(
          `/v2/ivh/videomaker/broadcastservice/getprogress?${queryStr}`,
          {
            Header: {},
            Payload: params,
          },
          {
            baseURL: baseUrl,
            signal: options?.abortSignal,
          }
        )
      );
      if (!getProgressErr) {
        const { data } = resp;
        if (data.Header.Code === 0) {
          if (data.Payload.Status === 'SUCCESS') {
            return resolve({
              audioUrl: data.Payload.MediaUrl,
            });
          }
          if (data.Payload.Status === 'FAIL') {
            return reject(
              new Error(
                `获取试听音频失败，${data.Payload.FailMessage}(${data.Payload.FailCode})`
              )
            );
          }
        }
      }
      timeout = setTimeout(() => {
        polling();
      }, 2000);
    };
    polling();
  });
};

export const getDemoTTSMediaUrl = async (
  params: IGetDemoTTSMediaParams,
  _options?: IGetDemoTTSMediaOptions
): Promise<{
  audioUrl: string;
}> => {
  const { text, timbreKey, volume } = params;
  const submitParams = {
    TimbreKey: timbreKey,
    InputSsml: text,
    Speed: 1.0,
    SampleRate: 16000,
    Volume: (volume / 10) >>> 0,
    appkey: getAppKey(),
    // 用户提供的时间戳应保证单位为秒且与当前时间相差不得超过五分钟
    // 示例时间戳:
    // timestamp: '1717639699',
    // 推荐使用下面的语句生成当前时间戳:
    timestamp: Math.floor(Date.now() / 1000), // 使用当前时间戳（单位：秒）
  };
  const submitQueryStr = await getSignUrl(submitParams);
  const [submitErr, submitResp] = await to(
    axios.post<{
      Header: {
        Code: 0;
        DialogID: '';
        Message: '';
        RequestID: '123';
      };
      Payload: {
        TaskId: '123';
      };
    }>(
      `/v2/ivh/videomaker/broadcastservice/tts?${submitQueryStr}`,
      {
        Header: {},
        Payload: submitParams,
      },
      {
        baseURL: baseUrl,
      }
    )
  );
  if (submitErr) {
    throw submitErr;
  }
  if (submitResp.data.Header.Code !== 0)
    throw new Error(`试听失败: ${submitResp.data.Header.Message}`);
  const { TaskId } = submitResp.data.Payload;
  // 使用taskId 轮询查询进度用以获取demo url
  return await waitForProcessEnd({ TaskId }, { abortSignal: _options?.signal });
};

// 分页查询音色列表
export const getAssetList = async () => {
  const submitParams = {
    PageIndex: 1,
    pageSize: 100,
    appkey: getAppKey(),
    timestamp: Math.floor(Date.now() / 1000),
  };
  const submitQueryStr = await getSignUrl(submitParams);
  const [err, res] = await to(
    axios.post(
      `/v2/ivh/crmserver/customerassetservice/describetimbre?${submitQueryStr}`
    )
  );
  console.debug(res);
};
