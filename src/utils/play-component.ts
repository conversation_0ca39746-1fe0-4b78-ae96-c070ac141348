import { ProductOption } from '@/pages/ADMetaList/ADSelectProductModal';
import type { FontSelectorData } from '@tencent/formily-cms/esm/font-selector/type';
import { getParam } from '@tencent/midas-util';
import { CommonStyle, PagedooFontStyle } from '@tencent/pagedoo-library';
import { commonHiddenStyle } from './play-view';
export const MaterialsBase = 'gems-materials-pagedoo-base@1707195919';
export const MaterialsAvatar = `gems-materials-pagedoo-avatar@${
  // 临时重写avatar素材库版本号
  getParam('_avatar') ||
  import.meta.env.VITE_MATERIAL_AVATAR_VERSION ||
  1749093531
}`;
// Whistle调试本地素材库代理配置：
// https://dev-pagedoo.pay.qq.com/cdn/materials/dist/gems-materials-pagedoo-avatar@1716793094/ file:///Users/<USER>/WebstormProjects/gems-materials-pagedoo-avatar/dist/
// # 本地需先构建出 npm run dist:debug 版本号需对应修改

export const liveBkg = (
  key: number,
  url: string,
  prompt: string,
  commonStyle: CommonStyle,
  // 商品图URL
  productUrl?: string
) => {
  const backgroundUrl =
    url ||
    'https://pagedoo.pay.qq.com/material/@platform/32dba2bc93b85f32d3c25082a803dc19.png';
  return {
    id: `component/${MaterialsAvatar}/LiveBkg`,
    name: 'LiveBkg',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      // 若想传入预设的ppt封面作为背景，将ppt封面图片的URL传给customBkg的url、img的url，再把mode设为'custom'
      img: [
        {
          url: backgroundUrl,
          name: '通用演播厅',
          type: 'img',
          width: 768,
          length: 1536,
        },
      ],
      presetBkg: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/a27f2dc5a90b4b2df19214dabfbff638.png',
          name: '通用演播厅',
          type: 'img',
          width: 768,
          length: 1536,
        },
      ],
      mode: url ? 'custom' : 'preset',
      customBkg: [
        {
          url: backgroundUrl,
          name: '用户本地上传背景',
          type: 'img',
          width: 768,
          length: 1536,
        },
      ],
      miaosiAIBkg: {
        // productImg代表商品特写图信息，url有值代表“已上传”，url为空代表“未上传”
        productImg: [
          {
            // url: 'https://pagedoo.pay.qq.com/material/202208301145014483768320/753669edb27e75faa3a118f31aafe30f.png',
            url: productUrl || '',
            name: '商品特写',
            type: 'img',
            width: 750,
            length: 622,
          },
        ],
        img: [
          {
            url: '',
            // url: 'https://pagedoo.pay.qq.com/material/@platform/32dba2bc93b85f32d3c25082a803dc19.png',
            name: 'AI背景',
            type: 'img',
            width: 750,
            length: 622,
          },
        ],
        showProduct: true,
        creativeText: '',
        negativePrompt: '',
        showNegativePrompt: false,
        moveTargetRefCSS: '',
      },
      // todo miaosi:{//}
      __component_name: '直播间背景(LiveBkg)',
      __component_sub_name: '直播间背景',
      __component_id: 'LiveBkg',
      __component_mutex_data: '',
    },
    actions: [],
  };
};

export type ComponentStyleItem = {
  id: string;
  name: string;
  disabled?: boolean;
  code: string;
  [x: string]: unknown;
};

export interface IAnimation {
  animation: string;
  speed: number;

  isFilter: boolean;
  blur: number;
}
export const liveImage = (
  key: number,
  url: string,
  commonStyle: CommonStyle,
  animationConfig?: IAnimation,
  corner?: React.CSSProperties['borderRadius'],
  componentStyle?: ComponentStyleItem[]
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveImage`,
    name: 'LiveImage',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      img: [
        {
          url,
          name: '直播间头图',
          type: 'img',
          width: 750,
          length: 750,
        },
      ],
      corner,
      componentStyle,
      animationConfig,
      height: 0,
      __component_name: '直播间贴片(LiveImage)',
      __component_sub_name: '直播间贴片',
      __component_id: 'LiveImage',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

export const midasbuyEvent = (
  key: number,
  mountedScript?: Record<string, unknown>,
  unmountedScript?: Record<string, unknown>
) => {
  return {
    id: `component/${MaterialsAvatar}/MidasBuyEvent`,
    name: 'MidasBuyEvent',
    key,
    style: {},
    commonStyle: commonHiddenStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      dataConf: {
        mountedScript,
        unmountedScript,
        delay: 10000,
      },
      __component_name: 'Midasbuy事件通知(MidasBuyEvent)',
      __component_sub_name: 'Midasbuy事件通知',
      __component_id: 'MidasBuyEvent',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

// AI标识
export const liveIdentify = (
  key: number,
  data:
    | string
    | {
        url: string;
        __resourceId: string;
      },
  commonStyle: CommonStyle,
  animationConfig?: IAnimation,
  corner?: React.CSSProperties['borderRadius'],
  componentStyle?: ComponentStyleItem[]
) => {
  let url = '';
  let __resourceId = 'purple_3';
  if (typeof data === 'string') {
    url = data;
  } else {
    url = data.url;
    __resourceId = data.__resourceId;
  }

  return {
    id: `component/${MaterialsAvatar}/LiveIdentify`,
    name: 'LiveIdentify',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      img: [
        {
          url,
          name: '直播间头图',
          type: 'img',
          width: 750,
          length: 750,
          __resourceId,
        },
      ],
      corner,
      componentStyle,
      animationConfig,
      height: 0,
      __component_name: '主播AI分身(LiveIdentify)',
      __component_sub_name: '主播AI分身',
      __component_id: 'LiveIdentify',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

export type TSpeed =
  | 1
  | 2
  | 3
  | 4
  | 5
  | 6
  | 7
  | 8
  | 9
  | 10
  | 11
  | 12
  | 13
  | 14
  | 15
  | 16
  | 17
  | 18
  | 19
  | 20
  | 21
  | 22
  | 23
  | 24
  | 25
  | 26
  | 27
  | 28
  | 29
  | 30
  | 31
  | 32
  | 33
  | 34
  | 35
  | 36
  | 37
  | 38
  | 39
  | 40
  | 41
  | 42
  | 43
  | 44
  | 45
  | 46
  | 47
  | 48
  | 49
  | 50
  | 51
  | 52
  | 53
  | 54
  | 55
  | 56
  | 57
  | 58
  | 59
  | 60
  | 61
  | 62
  | 63
  | 64
  | 65
  | 66
  | 67
  | 68
  | 69
  | 70
  | 71
  | 72
  | 73
  | 74
  | 75
  | 76
  | 77
  | 78
  | 79
  | 80
  | 81
  | 82
  | 83
  | 84
  | 85
  | 86
  | 87
  | 88
  | 89
  | 90
  | 91
  | 92
  | 93
  | 94
  | 95
  | 96
  | 97
  | 98
  | 99;
export const typeAnimationSpeed: {
  [x: string]: TSpeed;
} = {
  slow: 10,
  normal: 40,
  fast: 70,
};

export const liveText = (
  key: number,
  fontStyle: PagedooFontStyle,
  text: string,
  commonStyle: CommonStyle,
  animationConfig?: { animation: string; speed: TSpeed }
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveText`,
    name: 'LiveText',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      text,
      fontStyle,
      animationConfig: animationConfig ?? { animation: 'none', speed: 40 },
      height: 0,
      __component_name: text,
      __component_sub_name: '单行文本',
      __component_id: 'LiveText',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};
export const liveSpeechAD = (
  key: number,
  type: 'aigc' | 'custom',
  goodsInfoList: ProductOption[],
  commonStyle: CommonStyle
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveSpeechAD`,
    name: 'LiveSpeechAD',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      speechConfig: {
        type: type || 'aigc',
        aiSpeechInfo: {
          scriptId: '',
          speechStyle: '',
          speechText: '',
          speechTone: '',
        },
        goodsInfoList,
        customSpeechInfo: {
          fileName: '',
          size: 0,
          speechText: '',
        },
        version: 'v2',
      },
      height: 0,
      __component_name: '直播话术(LiveSpeechAD)',
      __component_sub_name: '直播话术',
      __component_id: 'LiveSpeechAD',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

// 话术数据
type ISpeechData = {
  // answer 纯互动
  // text 纯口播
  // remix 混合 互动插口播
  type: 'answer' | 'text' | 'remix';
  // text时的口播文案内容
  text: string;
};
// 话术驱动
type TSpeechDrive = {
  // 驱动方式
  // virtualman 数字人组件
  // speaker 喇叭播放
  type: 'virtualman' | 'speaker';
};
export const liveSpeech = (
  key: number,
  speechData: ISpeechData,
  speechDrive: TSpeechDrive,
  commonStyle: CommonStyle,
  block = false
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveSpeech`,
    name: 'LiveSpeech',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      speechData,
      speechDrive,
      height: 0,
      __component_name: '直播话术(LiveSpeech)',
      __component_sub_name: '直播话术',
      __component_id: 'LiveSpeech',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
      __liveNeedLogin: 1,
      remixAnswer: {
        separator: ['。', '！', '？'],
        enabled: true,
        block,
      },
    },
    actions: [],
  };
};

export const liveVideo = (
  key: number,
  url: string,
  commonStyle: CommonStyle,
  height = 0,
  isLoop = true
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveVideo`,
    name: 'LiveVideo',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      video: [
        {
          url,
          name: '直播间视频',
          type: 'video',
          width: 750,
          length: 750,
        },
      ],
      height,
      isLoop,
      __component_name: '直播间视频(LiveVideo)',
      __component_sub_name: '直播间视频',
      __component_id: 'LiveVideo',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

export const liveSound = (
  key: number,
  voiceItem: {
    // 平台
    platform: 'tencent' | 'azure' | 'custom_tts';
    // 音色id
    voiceId: string;
    // 语速
    speed: number;
    // 平台特定扩展配置
    voiceExtendConfig: string;
  },
  commonStyle: CommonStyle
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveSound`,
    name: 'LiveSound',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      voiceConfig: {
        currentVoiceItem: voiceItem,
      },
      height: 0,
      __component_name: '直播音色(LiveSound)',
      __component_sub_name: '直播音色',
      __component_id: 'LiveSound',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

export const liveQA = (
  key: number,
  qaList: {
    id: string;
    // 问题内容
    question: string;
    // 回答
    answer: string;
  }[],
  commonStyle: CommonStyle
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveQA`,
    name: 'LiveQA',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      qaConfig: {
        qaList,
      },
      height: 0,
      __component_name: '互动',
      __component_sub_name: '直播问答库',
      __component_id: 'LiveQA',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

export const liveSubtitle = (
  key: number,
  fontConf: FontSelectorData,
  commonStyle: CommonStyle
) => {
  return component(
    key,
    `component/${MaterialsAvatar}/VideoSubtitle`,
    commonStyle,
    {
      _v: 1,
      fontConf,
      isGlobalFontConf: false,
      height: 0,
      __component_name: '字幕(VideoSubtitle)',
      __component_sub_name: '字幕',
      __component_id: 'VideoSubtitle',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    }
  );
};

export const liveBackgroundMusic = (
  key: number,
  volume: number,
  backgroundMusicList: {
    id: string;
    title: string;
    duration: string;
    url: string;
  }[],
  isLoop: boolean,
  commonStyle: CommonStyle
) => {
  return {
    id: `component/${MaterialsAvatar}/BackgroundMusic`,
    name: 'BackgroundMusic',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      dataConf: {
        volume,
        backgroundMusicList,
        isLoop,
      },
      height: 0,
      __component_name: '直播背景音乐(BackgroundMusic)',
      __component_sub_name: '直播背景音乐',
      __component_id: 'BackgroundMusic',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

// TODO: 后续抽离
export interface ILiveFeaturedSoundConfig {
  sound: {
    id: string;
    title: string;
    duration?: string;
    url: string;
  };
  soundConfig: {
    /**
     * 淡入时间
     */
    fadeInTime: number;
    /**
     * 淡出时间
     */
    fadeOutTime: number;
    /**
     * 音量
     */
    volume: number;
  };
}

export const liveFeaturedSound = (
  key: number,
  commonStyle: CommonStyle,
  componentConfig: ILiveFeaturedSoundConfig
) => {
  const { sound, soundConfig } = componentConfig;
  return {
    id: `component/${MaterialsAvatar}/FeaturedSound`,
    name: 'FeaturedSound',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      dataConf: {
        sound,
        soundConfig,
      },
      height: 0,
      __component_name: '特色音效(FeaturedSound)',
      __component_sub_name: '特色音效',
      __component_id: 'FeaturedSound',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

export const customVirtualManWave = (
  key: number,
  liveID: string,
  commonStyle: CommonStyle,
  virtualMan: unknown,
  extra?: {
    isSegmentTexts?: true;
    // 用于合并了数字人形象和音色的配置
    voiceConfig?: {
      currentVoiceItem: {
        // 平台
        platform:
          | 'tencent'
          | 'azure'
          | 'custom_tts'
          | 'tencent_man'
          | 'ad_chattts';
        // 音色id
        voiceId: string;
        // 语速
        speed: number;
        // 平台特定扩展配置
        voiceExtendConfig: string;
        // 驱动模式，音频或文本
        // 默认：voice
        driverMode?: 'voice' | 'text';
      };
    };
  }
) => {
  return {
    id: `component/${MaterialsAvatar}/Virtualman`,
    name: 'Virtualman',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    chosen: false,
    data: {
      _v: 4,
      virtualMan,
      liveID,
      isWave: true,
      customScript: false,
      type: 'text',
      ...extra,
      __component_name: '数字人(Virtualman)',
      __component_sub_name: '数字人 虚拟人的渲染 背景抠图依赖WebGl2抠图',
      __component_id: 'Virtualman',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
      __env: 'ams',
      __vman_linkage_lock: false,
    },
    actions: [],
  };
};
export const virtualman = (
  key: number,
  text: string,
  commonStyle: CommonStyle,
  live = 'live',
  isWave?: boolean
) => {
  return {
    id: `component/${MaterialsAvatar}/Virtualman`,
    name: 'Virtualman',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    chosen: false,
    data: {
      _v: 3,
      keyLight: {
        enabled: true,
        tolerance: 0.2433,
      },
      virtualMan: {
        type: 'qq',
        key: '11b88e3ff7fa47a99fd0f6d0982c609a',
        img: 'https://pagedoo.pay.qq.com/material/@platform/d877fb664a2a08af0190b0a0097253fc.png',
        label: 'jinjin站姿',
        appkey: '86f087fad7024efb82269070beed51db',
      },
      liveID: live,
      isWave: isWave || false,
      customScript: false,
      type: 'text',
      __component_name: '数字人(Virtualman)',
      __component_sub_name: '数字人 虚拟人的渲染 背景抠图依赖WebGl2抠图',
      __component_id: 'Virtualman',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
      text,
      __env: 'ams',
    },
    actions: [],
  };
};
export const component = (
  key: number,
  id: string,
  commonStyle: CommonStyle,
  data: any
) => ({
  id,
  name: id.split('/')[2],
  key,
  style: {},
  commonStyle,
  wrapperStyle: {},
  chosen: false,
  data,
  actions: [],
});
export const virtualman1811comment = (
  prefix: string,
  suffix: string,
  commonStyle: CommonStyle,
  live = 'live'
) => {
  return {
    id: `component/${MaterialsAvatar}/Virtualman`,
    name: 'Virtualman',
    key: 1811,
    style: {},
    commonStyle,
    wrapperStyle: {},
    chosen: false,
    data: {
      _v: 3,
      keyLight: {
        enabled: true,
        tolerance: 0.2433,
      },
      virtualMan: {
        type: 'qq',
        key: '0a1dc152f9b04835aa582fd9791ce77c',
        img: 'https://pagedoo.pay.qq.com/material/@platform/d4108d661ba7294a5beb6e1d8dbc243b.png',
        label: '微澜-粉色短裙-站姿',
        appkey: '86f087fad7024efb82269070beed51db',
      },
      liveID: live,
      customScript: false,
      type: 'text',
      __component_name: '数字人(Virtualman)',
      __component_sub_name: '数字人 虚拟人的渲染 背景抠图依赖WebGl2抠图',
      __component_id: 'Virtualman',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
      prefix,
      suffix,
    },
    actions: [],
  };
};
export const table1823 = (url: string, commonStyle: CommonStyle) => ({
  id: `component/${MaterialsAvatar}/LiveTable`,
  name: 'LiveTable',
  key: 1823,
  style: {},
  commonStyle,
  wrapperStyle: {},
  data: {
    _v: 0,
    img: [
      {
        url:
          url ||
          'https://pagedoo.pay.qq.com/material/@platform/d3419e2bd86a262e59aab4b422c147f2.png',
        name: '桌子',
        type: 'img',
        width: 958,
        length: 426,
      },
    ],
    __component_name: '展示台(LiveTable)',
    __component_sub_name: '直播间桌子',
    __component_id: 'LiveTable',
    __component_mutex_data: '',
    __pagedoo_i18n: {},
  },
  actions: [],
});

// 数字人
export const virtualmanTXCZ = (
  key: number,
  commonStyle: CommonStyle,
  virtualManKeyOrConf: string | Record<string, any>,
  type = 'text',
  isWave: boolean,
  currentVoiceItem: any,
  wrapperStyle: React.CSSProperties
) => {
  return {
    id: `component/${MaterialsAvatar}/Virtualman`,
    name: 'Virtualman',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    chosen: false,
    data: {
      _v: 4,
      wrapperStyle,
      keyLight: {
        enabled: true,
        tolerance: 0.2433,
      },
      virtualMan:
        typeof virtualManKeyOrConf === 'string'
          ? {
              type: 'qq',
              key: virtualManKeyOrConf,
              img: 'https://pagedoo.pay.qq.com/material/@platform/d877fb664a2a08af0190b0a0097253fc.png',
              label: 'jinjin站姿',
              appkey: '86f087fad7024efb82269070beed51db',
              asset: true,
            }
          : virtualManKeyOrConf,
      voiceConfig: {
        currentVoiceItem,
      },
      isWave,
      liveID: 'live',
      customScript: false,
      type,
      __component_name: '数字人(Virtualman)',
      __component_sub_name: '数字人 虚拟人的渲染 背景抠图依赖WebGl2抠图',
      __component_id: 'Virtualman',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
      text: '',
      prefix: '',
      suffix: '',
    },
    actions: [],
  };
};

// liveBox
export const liveBox = (
  key: number,
  url: string,
  commonStyle: CommonStyle,
  type = 'video'
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveBox`,
    name: 'LiveBox',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      content: {
        type,
        videoUrl: [
          {
            url,
          },
        ],
      },
      height: 0,
      __component_name: '直播间展示框(LiveBox)',
      __component_sub_name: '直播间视频',
      __component_id: 'LiveBox',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

// 直播拉流播放
export const LivePlayPull = (
  key: number,
  liveURL: string,
  commonStyle: CommonStyle
) => {
  return {
    id: `component/${MaterialsAvatar}/LivePlayPull`,
    name: 'LivePlayPull',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      liveURL,
      height: 0,
      __component_name: '直播拉流播放(LivePlayPull)',
      __component_sub_name: '直播拉流播放',
      __component_id: 'LivePlayPull',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};

// 直播间商品
export const liveProduct = (
  key: number,
  config: {
    product: string;
  },
  commonStyle: CommonStyle
) => {
  return {
    id: `component/${MaterialsAvatar}/LiveProduct`,
    name: 'LiveProduct',
    key,
    style: {},
    commonStyle,
    wrapperStyle: {},
    data: {
      _v: 0,
      ...config,
      __component_name: '直播间商品',
      __component_sub_name: '直播间商品',
      __component_id: 'LiveProduct',
      __component_mutex_data: '',
      __pagedoo_i18n: {},
    },
    actions: [],
  };
};
