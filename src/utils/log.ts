import { gloablMonitor } from './monitor/index';

type LogLevel = keyof Pick<
  typeof console,
  'log' | 'info' | 'warn' | 'error' | 'debug'
>;

export const createLogger = (logType = 'common') => {
  let _logType = logType;
  const doLog = (logLevel: LogLevel, ...msg: unknown[]) => {
    try {
      const m = JSON.stringify(msg);
      console[logLevel](`[${_logType}] - `, m);
      if (logLevel === 'log') {
        logLevel = 'info';
      }
      gloablMonitor?.log(logLevel as Exclude<'log', LogLevel>, {
        msg: `[${_logType}] - ${m}`,
      });
    } catch {}
  };
  const alias: Record<LogLevel, (...msg: unknown[]) => void> = (
    ['log', 'debug', 'error', 'info', 'warn'] as LogLevel[]
  ).reduce((prev, level) => {
    return {
      ...prev,
      [level]: (...msg: unknown[]) => doLog(level, ...msg),
    };
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  }, {} as Record<LogLevel, (...msg: unknown[]) => void>);
  return {
    ...alias,
    setLogType: (type: string) => {
      _logType = type;
    },
  };
};
