import { commonStyle } from '@/utils/play-view';
import { CommonStyle } from '@tencent/pagedoo-library';
import { SOUND_OPTIONS, VIRTUALMAN_OPTIONS } from '../constants';
import { ECOMMERCE_ID_ENUM } from '../templates';

export type TEcommerceId = `${ECOMMERCE_ID_ENUM}`;
// 模板样式
export const tplStyle = {
  common: {
    human: commonStyle(0, 40, 455),
    background: commonStyle(0, 0, 455),
    foreground: commonStyle(0, 0, 455),
    productCloseUp: commonStyle(113, 260, 230), // 商品特写
    productTitle: commonStyle(321, 237, 64),
    productCloseUpBkg: commonStyle(0, 0, 455),
    transiton: commonStyle(0, 0, 455),
    showyText: commonStyle(80, 200, 344),
  },
  hidden: {
    position: {
      top: -5000,
      left: -5000,
      bottom: 0,
      right: 0,
      type: 'absolute',
      outset: 'TopLeft',
      unit: {
        left: 'percent',
        right: 'percent',
        top: 'percent',
        bottom: 'percent',
      },
    },
  } as CommonStyle,
};

export const ecTplConf: {
  [k in TEcommerceId]: {
    style: any;
    themeColor: string;
    components: any;
    fontStyle: any;
    materialMap: any;
  };
} = {
  // 花哨模板（绿）
  [ECOMMERCE_ID_ENUM.ECOMMERCE_CHINESE_TRADITIONAL_STYLE]: {
    // 模板样式
    style: {
      title: commonStyle(0, 100, 455),
      productDefault: commonStyle(56, 227, 95), // 商品贴片展示
      productCloseUp: commonStyle(113, 260, 230), // 商品特写
      showyText: commonStyle(130, 521, 242),
      subtitleBkg: commonStyle(62, 670, 333),
      subtitle: commonStyle(62, 684, 333),
    },
    // 主题色
    themeColor: '#03B47C',
    // 组件
    components: {
      virtuanman: {
        face: VIRTUALMAN_OPTIONS.jinjin,
        sound: SOUND_OPTIONS.xiaochen,
        style: {},
      },
    },
    // 单行文本fontStyle
    fontStyle: {
      title: {
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font28.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font28.woff',
        fontSize: 42,
        color: {
          color: '#008A69',
          realColor: '#008A69',
          show: true,
        },
        width: 455,
        textAlign: 'center',
        lineHeight: 1.5,
        letterSpacing: 0.24,
      },
      subtitle: {
        fontSize: 20,
        useImage: 1,
        color: {
          color: '#fff',
          realColor: '#fff',
          show: true,
        },
        width: 333,
        textAlign: 'center',
        lineHeight: 1.5,
      },
    },
    // 素材
    materialMap: {
      productTitle: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/849f42aafb5ef7ba9cdd7c130111b447.png',
        },
      ],
      foreground: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/29a26786d55ebd2d4d8f44f83e062010.png',
        },
      ],
      productCloseUpBkg: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/68ffb53098d666723aac72892545c039.png',
        },
      ],
      productImageBorderImage: [
        // 默认
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/3eeacb59610c23329ed01008ad080116.png',
        },
        // 特写
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/673bb26145b7b286101c50254d319a37.png',
        },
      ],
      subtitleBkg: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/0d8a1c9d3803d20c7c5bfb2cad355a1a.png',
        },
      ],
    },
  },
  // 花哨模板（蓝）
  [ECOMMERCE_ID_ENUM.ECOMMERCE_SUMMER]: {
    // 模板样式
    style: {
      title: commonStyle(0, 100, 455),
      showyText: commonStyle(120, 484, 242),
      productDefault: commonStyle(306, 227, 95), // 商品贴片展示
      productCloseUp: commonStyle(113, 260, 230), // 商品特写
      subtitleBkg: commonStyle(62, 623, 333),
      subtitle: commonStyle(62, 635, 333),
    },
    // 主题色
    themeColor: '#46B2F8',
    // 组件
    components: {
      virtuanman: {
        face: VIRTUALMAN_OPTIONS.caixuan,
        sound: SOUND_OPTIONS.mixuan,
        style: {},
      },
    },
    // 单行文本fontStyle
    fontStyle: {
      title: {
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff',
        fontSize: 48,
        color: {
          color: '#fff',
          realColor: '#fff',
          show: true,
        },
        width: 455,
        textAlign: 'center',
        lineHeight: 1.5,
        letterSpacing: 0,
      },
      subtitle: {
        fontSize: 20, // 写多少就会体现在“字号”上为多少
        useImage: 1,
        color: {
          color: '#fff',
          realColor: '#fff',
          show: true,
        },
        width: 333,
        textAlign: 'center',
        lineHeight: 1.5,
      },
    },
    // 素材
    materialMap: {
      productTitle: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/c7b1a75efcf82a3a6845e9daa7eec572.png',
        },
      ],
      foreground: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/8f05b17d3fc46103d075209b7ed33087.png',
        },
      ],
      productCloseUpBkg: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/0b31c7b48793ca6d2f8f166d557c01d8.png',
        },
      ],
      productImageBorderImage: [
        // 默认
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/7804771d660a37c139eb8e5fcf3ec55b.png',
        },
        // 特写
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/1b2b5c238001b92e9f33e46d0ccff715.png',
        },
      ],
      subtitleBkg: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/b8fc79724e21d5acc0e7ddd10f41e01b.png',
        },
      ],
    },
  },
};
