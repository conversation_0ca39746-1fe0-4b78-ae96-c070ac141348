import { BaseScript } from '@/components/ScriptForm/type';
import { PlayConfig } from '@/type/pagedoo';
import { SinglePlugin } from '@/utils/template/index';
import { uuid } from '@tencent/midas-util';
import { cloneDeep, isEmpty } from 'lodash-es';
import { component, MaterialsAvatar } from '../../play-component';
import { format, initPlugin, livePlugin, setMeta } from '../common';
import { RedYellowNormal } from '../config/showyText';
import { SPEECH_SEPARATOR } from '../constants';
import { ECommerceMockScript } from '../mock/ecommerce';
import {
  scriptLiveImagePlugin,
  scriptLiveShowyTextPlugin,
  scriptLiveSpeechPlugin,
  scriptLiveTextPlugin,
  scriptSubtitlePlugin,
  scriptVirtualManPlugin,
} from '../plugins';
import { LANGUAGE_SPEED } from '../txcz/constants';
import { genUniqueKey } from '../utils';
import { DEFAULT_ANIMATION } from '../video/config/baseConfig';
import { ecTplConf, TEcommerceId, tplStyle } from './config';
import {
  ECOMMERCE_TEMPLATE_COMPONENTS,
  NO_PRODUCT_PLACEHOLDER_IMAGE,
} from './constants';

const safeKey = genUniqueKey();

const transitionInFlagText = '转场入场';
const transitionOutFlagText = '转场退场';

function processTranslateLogic(script: BaseScript, depthOfFieldStr = '特写') {
  const _script = cloneDeep(script);
  // 上一个viewType
  let prev = '';
  // 当前viewType
  let current = '';

  const data = _script.views.flatMap((item, index) => {
    // 初始化
    if (index === 0) {
      prev = item.viewType;
    }

    current = item.viewType;

    const isTranslateOut = depthOfFieldStr
      ? current !== prev && current === depthOfFieldStr
      : current !== prev;

    const isTranslateIn = depthOfFieldStr
      ? current !== prev &&
        depthOfFieldStr === _script.views[index - 1].viewType
      : current !== prev;

    if (isTranslateOut || (index - 1 >= 0 && isTranslateIn)) {
      prev = current;
      return [
        {
          ..._script.views[index - 1],
          speech: transitionOutFlagText,
        }, // 上一个画面，无台词
        {
          ...item,
          speech: transitionInFlagText,
        }, // 特定（无台词）
        item, // 当前画面（特写）
      ];
    }

    return item;
  });

  let inIndex: number[] = [];
  let outIndex: number[] = [];

  data.forEach((view, index) => {
    if (view.speech === transitionInFlagText) {
      inIndex = [...inIndex, index];
    } else if (view.speech === transitionOutFlagText) {
      outIndex = [...outIndex, index];
    }
  });

  return { data, inIndex, outIndex };
}

// 退场/入场gif时长
const transitionDuration = 633;
const transitionImage = {
  [transitionInFlagText]: {
    url: 'https://pagedoo.pay.qq.com/material/@platform/f8117d5232542dd9d1169756c5f54c7f.gif',
    duration: transitionDuration,
  },
  [transitionOutFlagText]: {
    url: 'https://pagedoo.pay.qq.com/material/@platform/65ab82dc3d548dc1ade49d0fe977e0af.gif',
    duration: transitionDuration,
  },
};

const scriptTransitionPlugin = (
  script: BaseScript,
  key: number,
  timeLineIndex: number,
  preload = true
): SinglePlugin => {
  return (conf: PlayConfig) => {
    // 退场
    const transitionOutTimelineIndex = timeLineIndex;
    const transitionInTimelineIndex = transitionOutTimelineIndex + 1;
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (view.speech === transitionOutFlagText) {
        conf.timeline[transitionOutTimelineIndex].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: component(
            key,
            `component/${MaterialsAvatar}/LiveImage`,
            tplStyle.common.transiton,
            {
              _v: 0,
              img: [
                {
                  url: transitionImage[view.speech].url,
                  name: '转场图',
                  type: 'img',
                  width: 750,
                  length: 750,
                },
              ],
              height: 0,
              __component_name: '直播间贴片(LiveImage)',
              __component_sub_name: '直播间贴片',
              __component_id: 'LiveImage',
              __component_mutex_data: '',
              __pagedoo_i18n: {},
            }
          ),
          duration: transitionImage[view.speech].duration,
          id: uuid(),
          key,
          offset: 0,
        };
        // 预加载
        if (preload) {
          conf.timeline[transitionOutTimelineIndex].node[index - 1] = {
            __config: { thumbnail: '', title: '', type: 'component' },
            actualDuration: 0,
            component: component(
              key,
              `component/${MaterialsAvatar}/LiveImage`,
              tplStyle.common.transiton,
              {
                _v: 0,
                img: [
                  {
                    url: transitionImage[view.speech].url,
                    name: '转场图',
                    type: 'img',
                    width: 750,
                    length: 750,
                  },
                ],
                height: 0,
                __component_name: '直播间贴片(LiveImage)',
                __component_sub_name: '直播间贴片',
                __component_id: 'LiveImage',
                __component_mutex_data: '',
                __pagedoo_i18n: {},
              }
            ),
            duration: 0,
            hidden: true,
            id: uuid(),
            key,
            offset: 0,
          };
        }
      } else if (view.speech === transitionInFlagText) {
        conf.timeline[transitionInTimelineIndex].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: component(
            key + 1,
            `component/${MaterialsAvatar}/LiveImage`,
            tplStyle.common.transiton,
            {
              _v: 0,
              img: [
                {
                  url: transitionImage[view.speech].url,
                  name: '转场图',
                  type: 'img',
                  width: 750,
                  length: 750,
                },
              ],
              height: 0,
              __component_name: '直播间贴片(LiveImage)',
              __component_sub_name: '直播间贴片',
              __component_id: 'LiveImage',
              __component_mutex_data: '',
              __pagedoo_i18n: {},
            }
          ),
          duration: transitionImage[view.speech].duration,
          id: uuid(),
          key: key + 1,
          offset: 0,
        };
        // 预加载
        if (preload) {
          conf.timeline[transitionInTimelineIndex].node[index - 1] = {
            __config: { thumbnail: '', title: '', type: 'component' },
            actualDuration: 0,
            component: component(
              key + 1,
              `component/${MaterialsAvatar}/LiveImage`,
              tplStyle.common.transiton,
              {
                _v: 0,
                img: [
                  {
                    url: transitionImage[view.speech].url,
                    name: '转场图',
                    type: 'img',
                    width: 750,
                    length: 750,
                  },
                ],
                height: 0,
                __component_name: '直播间贴片(LiveImage)',
                __component_sub_name: '直播间贴片',
                __component_id: 'LiveImage',
                __component_mutex_data: '',
                __pagedoo_i18n: {},
              }
            ),
            duration: 0,
            hidden: true,
            id: uuid(),
            key: key + 1,
            offset: 0,
          };
        }
      }
    }
  };
};

const formatTransitionNodeDuration = (
  inIndex: number[],
  outIndex: number[]
): SinglePlugin => {
  return (conf: PlayConfig) => {
    // 重置与“入场”动画位于同一列所有节点的duration
    inIndex.map((_index) => {
      conf.timeline.map((item) => {
        if (item.node[_index]) {
          item.node[_index].duration = transitionDuration;
        }
      });
    });
    // 重置与“退场”动画位于同一列所有节点的duration
    outIndex.map((_index) => {
      conf.timeline.map((item) => {
        if (item.node[_index]) {
          item.node[_index].duration = transitionDuration;
        }
      });
    });
  };
};

// 计算节点时长(与common文件中的duration不同，待将转场逻辑整体切换为新的transition组件后统一)
export const ecommerceDuration: SinglePlugin = (conf) => {
  const max = Math.max(...conf.timeline.map((i) => i.node.length));
  let offset = 0;
  for (let i = 0; i < max; i++) {
    const speech = conf.timeline
      .map((timeline) => timeline.node[i])
      .filter(Boolean)
      .find((i) => i.component.id.includes('LiveSpeech'));
    // 计算“列”的时长，最小
    const duration = Math.max(
      speech
        ? speech.component.data.speechData.text.length * LANGUAGE_SPEED // 有话术的情况按口播文案估算时长
        : transitionDuration, // 没有话术则代表为转场画面，时长按转场动画时长
      100 // 不能大于transitionDuration
    );
    let calculatedDuration = -1;
    // 设置同一列所有节点的duration
    for (const pagedooPlayTimeline of conf.timeline) {
      const node = pagedooPlayTimeline.node[i];
      if (!node) continue;
      node.duration = node.duration > 0 ? node.duration : duration;
      node.offset = offset;
      // 只设置一次即可
      if (calculatedDuration === -1) {
        calculatedDuration = node.duration > 0 ? node.duration : duration;
      }
    }
    // 打环节标
    conf.fragment.push({
      __config: { name: '' },
      id: uuid(),
      offset,
    });
    offset += calculatedDuration;
  }
};

export function initECommerceTemplate(script: BaseScript, tplId: TEcommerceId) {
  const _script = !isEmpty(script)
    ? cloneDeep(script)
    : cloneDeep(ECommerceMockScript);

  // 处理转场逻辑
  const { inIndex, outIndex } = processTranslateLogic(_script);
  if (tplId) {
    _script.views = processTranslateLogic(_script).data;
  }

  const componentStartIndex = 700;
  const components = [
    ECOMMERCE_TEMPLATE_COMPONENTS.VIRTUALMAN, // 数字人
    ECOMMERCE_TEMPLATE_COMPONENTS.SPEECH, // 话术
    ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_CLOSEUP_BKG, // 商品特写画面虚化背景
    ECOMMERCE_TEMPLATE_COMPONENTS.FOREGROUND, // 前景
    ECOMMERCE_TEMPLATE_COMPONENTS.TITLE, // 标题
    // ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_TITLE_IMAGE, // 商品图标题
    ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_IMAGE, // 商品图
    ECOMMERCE_TEMPLATE_COMPONENTS.SHOWY_TEXT, // 带动画的文字配套贴纸（可选）
    // ECOMMERCE_TEMPLATE_COMPONENTS.LIVE_TEXT, // 带动画的文字
    // ECOMMERCE_TEMPLATE_COMPONENTS.SUBTITLE_BKG, // 字幕背景
    ECOMMERCE_TEMPLATE_COMPONENTS.SUBTITLE, // 字幕
    ECOMMERCE_TEMPLATE_COMPONENTS.TRANSITION_OUT, // 转场入场
    ECOMMERCE_TEMPLATE_COMPONENTS.TRANSITION_IN, // 转场退场
  ];
  const componentMap: Record<string, number> = components.reduce(
    (acc, cur, curIndex) => {
      return {
        ...acc,
        [cur]: componentStartIndex + curIndex,
      };
    },
    {}
  ); // 使用等号初始化对象

  return [
    initPlugin(_script, components.length),
    // 数字人
    scriptVirtualManPlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.VIRTUALMAN]),
      timelineIndex: components.indexOf(
        ECOMMERCE_TEMPLATE_COMPONENTS.VIRTUALMAN
      ),
      commonStyle: tplStyle.common.human,
      baseFn: () => {
        return {
          _v: 4,
          keyLight: {
            enabled: false,
            tolerance: 0.2433,
          },
          virtualMan: ecTplConf[tplId].components.virtuanman.face,
          voiceConfig: {
            currentVoiceItem: ecTplConf[tplId].components.virtuanman.sound,
          },
          liveID: '',
          type: 'text',
          isGlobalVirtualman: false,
          isWave: true,
          isSegmentTexts: false,
          customScript: false,
        };
      },
    }),
    // 话术
    scriptLiveSpeechPlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.SPEECH]),
      timelineIndex: components.indexOf(ECOMMERCE_TEMPLATE_COMPONENTS.SPEECH),
      commonStyle: tplStyle.hidden,
      getSpeechParams: ({ view, script: _script }) => ({
        speechData: {
          type: 'text',
          text: ![transitionInFlagText, transitionOutFlagText].includes(
            view.speech
          )
            ? view.speech
            : '',
        },
        speechDrive: { type: 'virtualman' },
        remixAnswer: {
          enabled: true,
          separator: SPEECH_SEPARATOR,
        },
      }),
    }),
    // 商品特写画面模糊背景
    scriptLiveImagePlugin({
      script: _script,
      key: safeKey(
        componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_CLOSEUP_BKG]
      ),
      timelineIndex: components.indexOf(
        ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_CLOSEUP_BKG
      ),
      isSkip: (view) => view.viewType !== '特写',
      baseFn: (view) =>
        (view.viewType === '特写' &&
          ecTplConf[tplId].materialMap.productCloseUpBkg[0].url) ||
        '',
      commonStyle: tplStyle.common.productCloseUpBkg,
    }),
    // 前景
    scriptLiveImagePlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.FOREGROUND]),
      timelineIndex: components.indexOf(
        ECOMMERCE_TEMPLATE_COMPONENTS.FOREGROUND
      ),
      baseFn: (view) => ecTplConf[tplId].materialMap.foreground[0].url,
      commonStyle: tplStyle.common.foreground,
    }),
    // 商品标题
    scriptLiveTextPlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.TITLE]),
      timelineIndex: components.indexOf(ECOMMERCE_TEMPLATE_COMPONENTS.TITLE),
      commonStyle: ecTplConf[tplId].style.title,
      baseFn: (view) => {
        return _script?.globalField?.title || '好物直播间';
      },
      fontStyle: {
        fontFamily: [ecTplConf[tplId].fontStyle.title.fontFamily],
        fontSize: ecTplConf[tplId].fontStyle.title.fontSize * (375 / 455),
        useImage: 1,
        color: {
          color: ecTplConf[tplId].fontStyle.title.color.color,
          realColor: ecTplConf[tplId].fontStyle.title.color.realColor,
          show: true,
        },
        width: ecTplConf[tplId].fontStyle.title.width * (375 / 455),
        textAlign: ecTplConf[tplId].fontStyle.title.textAlign,
        lineHeight: ecTplConf[tplId].fontStyle.title.lineHeight,
        letterSpacing: ecTplConf[tplId].fontStyle.title.letterSpacing,
      },
      // 不传getAnimationConfig会报错
      // getAnimationConfig: () => ({ animation: '', speed: 40 }),
    }),

    // 商品图（含背景，商品图动态变化，背景图跟模板绑定）
    scriptLiveImagePlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_IMAGE]),
      timelineIndex: components.indexOf(
        ECOMMERCE_TEMPLATE_COMPONENTS.PRODUCT_IMAGE
      ),
      baseFn: (view) =>
        view.imageList?.[0]?.url || NO_PRODUCT_PLACEHOLDER_IMAGE,
      commonStyle: (view) =>
        ecTplConf[tplId].style[
          view.viewType === '特写' ? 'productCloseUp' : 'productDefault'
        ],
      componentStyle: (view) => {
        return [
          {
            id: '33',
            name: 'test',
            code:
              view.viewType === '特写'
                ? `
                   box-sizing: border-box;
                    border-width: 0.3rem;
                    border-style: solid;
                    border-image-source: url('${ecTplConf[tplId].materialMap.productImageBorderImage[1].url}');
                    border-image-slice: 3.17% 4%;
                    border-image-width: 0.3rem;
                    border-radius: 0.24rem;
                    background-color: ${ecTplConf[tplId].themeColor};
                    > img {
                    border-radius: 0.3rem;
                    background: #fff;
                  }
                `
                : `

                    box-sizing: border-box;
                    border-width: 2.76rem 0.8rem 0.8rem;
                    border-style: solid;
                    border-image: url('${ecTplConf[tplId].materialMap.productImageBorderImage[0].url}');
                    border-image-slice: 24.4% 10.7% 10.7% 10.7%;
                    border-image-width: 2.76rem 0.8rem 0.8rem 0.8rem;
                    background-color: ${ecTplConf[tplId].themeColor};
                    border-radius: 1rem;
                    > img {
                      border-radius: 0.32rem;
                      background-color: #fff;
                    }
                `,
          },
        ];
      },
    }),
    // 花字
    scriptLiveShowyTextPlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.SHOWY_TEXT]),
      timelineIndex: components.indexOf(
        ECOMMERCE_TEMPLATE_COMPONENTS.SHOWY_TEXT
      ),
      commonStyle: ecTplConf[tplId].style.showyText,
      baseFn: (view) => view.flowerText ?? '',
      isSkip: (view) => !view.flowerText,
      getAnimationConfig: () => DEFAULT_ANIMATION,
      fontStyle: RedYellowNormal,
    }),
    // 字幕（subtitle，动态）
    scriptSubtitlePlugin({
      script: _script,
      key: safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.SUBTITLE]),
      timelineIndex: components.indexOf(ECOMMERCE_TEMPLATE_COMPONENTS.SUBTITLE),
      fontData: () => {
        return {
          fontSize: ecTplConf[tplId].fontStyle.subtitle.fontSize * (375 / 455), // 写多少就会体现在“字号”上为多少
          useImage: 1,
          color: {
            color: ecTplConf[tplId].fontStyle.subtitle.color.color,
            realColor: ecTplConf[tplId].fontStyle.subtitle.color.realColor,
            show: true,
          },
          width: ecTplConf[tplId].fontStyle.subtitle.width * (375 / 455),
          textAlign: ecTplConf[tplId].fontStyle.subtitle.textAlign,
          lineHeight: ecTplConf[tplId].fontStyle.subtitle.lineHeight,
        };
      },
      commonStyle: ecTplConf[tplId].style.subtitle,
      delayTime: 2500,
    }),
    // 转场（LiveImage，切特写时需要。复制转场前后节点，在最顶层叠加转场动效图）
    scriptTransitionPlugin(
      _script,
      safeKey(componentMap[ECOMMERCE_TEMPLATE_COMPONENTS.TRANSITION_OUT]),
      components.indexOf(ECOMMERCE_TEMPLATE_COMPONENTS.TRANSITION_OUT),
      true
    ),
    // 重置与转场相关的节点
    formatTransitionNodeDuration(inIndex, outIndex),
    ecommerceDuration,
    livePlugin,
    format,
    setMeta({ size: _script.size }),
  ];
}
