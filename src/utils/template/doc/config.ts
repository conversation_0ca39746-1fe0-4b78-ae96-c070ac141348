import { commonHiddenStyle } from '@/utils/play-view';
import {
  DOC_DEFAULT_MARGIN_LEFT,
  DOC_MAX_LIVETEXT_WIDTH,
  DOC_Q_TITLE_WIDTH,
  DOC_FONT,
  DOC_HEIGHT,
  DOC_VIEW_TYPES_CHINESE_NAME,
  DOC_TEMPLATE_COMPONENTS,
} from './constants';
import { calFontDisplayAreaWidth, calFontSize, docCommonStyle } from '../utils';
import { DOC_ID_ENUM } from '../templates';
import {
  SOUND_OPTIONS,
  TVirtualManConfV1,
  VIRTUALMAN_OPTIONS,
} from '../constants';
import { ILiveSound } from '../plugins/scriptVirtualManPlugin';
import { CommonStyle, PagedooFontStyle } from '@tencent/pagedoo-library';
import { MuiscConf } from '../plugins/scriptBackgroundMusicPlugin';

// 此模板除了封面/结尾以外的画面类型统称为“正文”，正文部分共用1个背景素材
export type TMainTextName = '正文';
export type TMaterialViewType =
  | DOC_VIEW_TYPES_CHINESE_NAME.TITLE_PAGE
  | TMainTextName
  | DOC_VIEW_TYPES_CHINESE_NAME.END_PAGE;

// 模板最少需要包含的文本样式
export type TMinimumRequiredTextStyle =
  | DOC_TEMPLATE_COMPONENTS.SECTIO_TITLE
  | DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1
  | DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2
  | DOC_TEMPLATE_COMPONENTS.PARAGRAPH_SECONDARY
  | DOC_TEMPLATE_COMPONENTS.A_TEXT // 同“三级正文”
  | DOC_TEMPLATE_COMPONENTS.SUBTITLE;

export type TDOCTemplateConfig = {
  virtualman: {
    face: TVirtualManConfV1;
    sound: ILiveSound;
    position: {
      [key in `${DOC_VIEW_TYPES_CHINESE_NAME}`]: CommonStyle;
    };
  };
  material: {
    logo: {
      config: {
        url: string;
      };
      position: CommonStyle;
    };
    background: {
      [key in TMaterialViewType]: {
        config: {
          url: string;
        };
        position: CommonStyle;
      };
    };
    backgroundMusic: {
      [key in TMaterialViewType]: {
        config: MuiscConf;
        position: CommonStyle;
      };
    };
  };
  textStyle: {
    [key in TMinimumRequiredTextStyle]: PagedooFontStyle;
  };
};

export const docStyle = {
  common: {
    bkg: docCommonStyle(0, 0, 1920),
    // 封面&结尾logo位置（正中央）
    sectionPageLogo: docCommonStyle(738, 373, 460),
    // 内页logo位置（右上角）
    logoSecondary: docCommonStyle(1430, 124, 376),
    // 数字人（画面下方，避免切换背景视频时数字人会突然显示）
    virtualman: docCommonStyle(1200, DOC_HEIGHT, 400),
    // progressbar,
    // backgroundMusic
    // subtitle字幕
    subtitle: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 966, 1488),
    h1title: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 152, 1488),
    hidden: commonHiddenStyle,
  },
  // 封面、结尾
  sectionPage: {
    title: docCommonStyle(564, 440, 200),
    smallerTitle: docCommonStyle(564, 480, 200), // 超过5个字字号变小，增大顶部间距
  },
  // 概述
  overview: {
    paragraph: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 278, 1488),
  },
  // 分点概述
  pointStatement: {
    h2title: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 316, 1488),
    paragraph: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 410, 1488),
  },
  // QA
  qa: {
    // 文字内容，宽度不会在commonStyle中生效，写多少都不影响\
    QTitle: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 296, DOC_Q_TITLE_WIDTH),
    ATitle: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 398, DOC_Q_TITLE_WIDTH),
    QText: docCommonStyle(
      DOC_DEFAULT_MARGIN_LEFT + DOC_Q_TITLE_WIDTH,
      327,
      DOC_MAX_LIVETEXT_WIDTH - DOC_Q_TITLE_WIDTH
    ),
    AText: docCommonStyle(
      DOC_DEFAULT_MARGIN_LEFT + DOC_Q_TITLE_WIDTH,
      428,
      DOC_MAX_LIVETEXT_WIDTH - DOC_Q_TITLE_WIDTH
    ),
  },
  // 结束语
  concluding: {
    paragraph: {
      noPic: docCommonStyle(DOC_DEFAULT_MARGIN_LEFT, 353, 878),
      withPic: docCommonStyle(906, 353, 878),
    },
  },
};

export const docTemplateConfig: {
  [key in `${DOC_ID_ENUM}`]: TDOCTemplateConfig;
} = {
  [DOC_ID_ENUM.LEARNING_CENTER]: {
    virtualman: {
      face: VIRTUALMAN_OPTIONS.none,
      sound: SOUND_OPTIONS.xiaochen,
      position: {
        封面: commonHiddenStyle,
        概述: commonHiddenStyle,
        分点概述: commonHiddenStyle,
        QA: commonHiddenStyle,
        结束语: commonHiddenStyle,
        结尾: commonHiddenStyle,
      },
    },
    material: {
      logo: {
        config: {
          url: 'https://pagedoo.pay.qq.com/material/@platform/27072ad59985694d6e8955dcd783c1eb.png',
        },
        position: docStyle.common.logoSecondary,
      },
      background: {
        封面: {
          config: {
            url: 'https://pagedoo.pay.qq.com/material/@platform/6027e74dd8a2f01463ae29ba935f9cff.mp4',
          },
          position: docStyle.common.bkg,
        },
        正文: {
          config: {
            url: 'https://pagedoo.pay.qq.com/material/@platform/8e5b1f21113c4e7663dca17406383f9b.mp4',
          },
          position: docStyle.common.bkg,
        },
        结尾: {
          config: {
            url: 'https://pagedoo.pay.qq.com/material/@platform/88d7fdfb38950f23069c2e4cb3f82021.mp4',
          },
          position: docStyle.common.bkg,
        },
      },
      backgroundMusic: {
        封面: {
          config: {
            id: 'start',
            title: '封面bgm',
            duration: '00:00:05',
            url: 'https://pagedoo.pay.qq.com/material/@platform/d1f99511357aedc3b22fedb54a45b480.MP3',
          },
          position: docStyle.common.hidden,
        },
        正文: {
          config: {
            id: 'loop',
            title: '循环bgm',
            duration: '00:01:00',
            url: 'https://pagedoo.pay.qq.com/material/@platform/454d4cda065583ce79c52f3610acc0a2.MP3',
          },
          position: docStyle.common.hidden,
        },
        结尾: {
          config: {
            id: 'end',
            title: '结尾bgm',
            duration: '00:00:06',
            url: 'https://pagedoo.pay.qq.com/material/@platform/04185b6887b13ee990e50364835f6329.MP3',
          },
          position: docStyle.common.hidden,
        },
      },
    },
    textStyle: {
      // 章节大标题
      [DOC_TEMPLATE_COMPONENTS.SECTIO_TITLE]: {
        fontFamily: [DOC_FONT.w7],
        fontSize: calFontSize(128),
        useImage: 1,
        color: {
          color: '#FA9D38',
          realColor: '#FA9D38',
          show: true,
        },
        width: calFontDisplayAreaWidth(800),
        textAlign: 'center',
        lineHeight: 1.2,
      },
      // 一级标题
      [DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1]: {
        fontFamily: [DOC_FONT.w7],
        fontSize: calFontSize(72),
        useImage: 1,
        color: {
          color: '#FA9D38',
          realColor: '#FA9D38',
          show: true,
        },
        width: calFontDisplayAreaWidth(DOC_MAX_LIVETEXT_WIDTH),
        textAlign: 'center',
        lineHeight: 1.5,
      },
      // 二级标题
      [DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2]: {
        fontFamily: [DOC_FONT.sourcesansBold],
        fontSize: calFontSize(50),
        useImage: 1,
        color: {
          color: '#FA9D38',
          realColor: '#FA9D38',
          show: true,
        },
        width: calFontDisplayAreaWidth(DOC_MAX_LIVETEXT_WIDTH),
        textAlign: 'left',
        lineHeight: 1.28,
      },
      // 二级正文
      [DOC_TEMPLATE_COMPONENTS.PARAGRAPH_SECONDARY]: {
        fontFamily: [DOC_FONT.sourcesansRegular],
        fontSize: calFontSize(45), // 写多少就会体现在“字号”上为多少
        useImage: 1,
        color: {
          color: '#000',
          realColor: '#000',
          show: true,
        },
        width: calFontDisplayAreaWidth(DOC_MAX_LIVETEXT_WIDTH),
        textAlign: 'left',
        lineHeight: 1.42,
      },
      // 三级正文(与A-text一致)
      [DOC_TEMPLATE_COMPONENTS.A_TEXT]: {
        fontFamily: [DOC_FONT.sourcesansRegular],
        fontSize: calFontSize(45), // 写多少就会体现在“字号”上为多少
        useImage: 1,
        color: {
          color: '#000',
          realColor: '#000',
          show: true,
        },
        // width: calFontDisplayAreaWidth(DOC_MAX_LIVETEXT_WIDTH),
        textAlign: 'left',
        lineHeight: 1.24,
      },
      [DOC_TEMPLATE_COMPONENTS.SUBTITLE]: {
        fontFamily: [DOC_FONT.w7],
        fontSize: calFontSize(48), // 写多少就会体现在“字号”上为多少
        useImage: 1,
        color: {
          color: '#fff',
          realColor: '#fff',
          show: true,
        },
        width: calFontDisplayAreaWidth(DOC_MAX_LIVETEXT_WIDTH),
        textAlign: 'center',
        lineHeight: 1.5,
        shadow: true,
        shadowConfig: [
          {
            color: {
              color: '#993200',
              realColor: '#993200',
              show: true,
            },
            hShadow: 0,
            vShadow: 1,
            radius: 1,
          },
        ],
      },
    },
  },
};
