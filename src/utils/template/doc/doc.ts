import { BaseScript } from '@/components/ScriptForm/type';
import { PlayConfig } from '@/type/pagedoo';
import { commonHiddenStyle } from '@/utils/play-view';
import { ScriptPlugin, SinglePlugin } from '@/utils/template/index';
import { uuid } from '@tencent/midas-util';
import { cloneDeep, has, isEmpty, max } from 'lodash-es';
import { liveImage, liveText, typeAnimationSpeed } from '../../play-component';
import { format, initPlugin, livePlugin, setMeta } from '../common';
import { SPEECH_SEPARATOR } from '../constants';
import { docEndPage, docMockScript, docTitlePage } from '../mock/doc';
import {
  scriptBackgroundMusicPlugin,
  scriptLiveImagePlugin,
  scriptLiveSpeechPlugin,
  scriptLiveTextPlugin,
  scriptSubtitlePlugin,
  scriptVideoPlugin,
  scriptVirtualManPlugin,
} from '../plugins';
import { DOC_ID_ENUM } from '../templates';
import { LANGUAGE_SPEED } from '../txcz/constants';
import {
  calFontDisplayAreaWidth,
  calFontSize,
  docCommonStyle,
  genUniqueKey,
  getBgmDuration,
} from '../utils';
import { docStyle, docTemplateConfig, TMaterialViewType } from './config';
import {
  DOC_FONT,
  DOC_MAX_LIVETEXT_WIDTH,
  DOC_Q_TITLE_WIDTH,
  DOC_TEMPLATE_COMPONENTS,
  DOC_VIEW_TYPES_CHINESE_NAME,
} from './constants';

const MAX_SECTION_STR_LENGTH = 5;
const safeKey = genUniqueKey();

const scriptDocImagesPlugin = (
  script: BaseScript,
  key: number,
  timelineIndex: number
): SinglePlugin => {
  return (conf: PlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);

      // 情况一、'概述'和'分点概述'图片排版规则

      // 图片总数不同，排版不同。下列数组中从左往右分别代表图片总数量为1、2、3、4、5的情况
      // 目前最多考虑图片总数为5张的情况，超过5张复用总数为5的逻辑处理

      const firstPicMarginLeft = [637, 216, 411, 215, 215]; // 第一张图片距离可视范围左边缘的距离
      const picRelativeLeft = [0, 752, 375, 375, 200]; // 相邻图片之间的间隔
      const picWidth = [646, 646, 360, 360, 350]; // 图片的预设宽度

      if (['分点概述', '概述'].includes(view.viewType) && view.imageList) {
        for (const pic of view.imageList) {
          const picIndex = view.imageList.indexOf(pic);
          if (view.imageList[picIndex]?.url) {
            const _key = key + picIndex;
            // 计算图片的位置信息
            // x坐标 = 第一张图片距离可视区域左边缘的距离 + 相邻图片之间的间隔
            const left =
              (firstPicMarginLeft[view.imageList.length - 1]
                ? firstPicMarginLeft[view.imageList.length - 1]
                : firstPicMarginLeft[firstPicMarginLeft.length - 1]) +
              (picRelativeLeft[view.imageList.length - 1]
                ? picRelativeLeft[view.imageList.length - 1] * picIndex
                : picRelativeLeft[picRelativeLeft.length - 1] * picIndex);
            const top = view.viewType === '概述' ? 520 : 620;
            const width = picWidth[view.imageList.length - 1]
              ? picWidth[view.imageList.length - 1]
              : picWidth[picWidth.length - 1];

            conf.timeline[timelineIndex + picIndex].node[index] = {
              __config: { thumbnail: '', title: '', type: 'component' },
              actualDuration: 0,
              component: liveImage(
                _key,
                view.imageList[picIndex].url,
                docCommonStyle(left, top, width),
                {
                  animation: 'gradually',
                  speed: 4, // (6-x)s
                  isFilter: false,
                  blur: 0,
                }
              ),
              duration: 0,
              id: uuid(),
              key: _key,
              offset: 0,
            };
          }
        }
      } else if (
        // 情况二、'QA'和'结束语'图片排版规则——显示在画面半侧
        ['QA', '结束语'].includes(view.viewType) &&
        view.imageList
      ) {
        for (const pic of view.imageList) {
          const picIndex = view.imageList.indexOf(pic);
          if (view?.imageList[picIndex]?.url) {
            const _key = key + picIndex;
            // 计算图片的位置信息
            // x坐标 = 第一张图片距离可视区域顶部边缘的距离 + 相邻图片之间的间隔
            const picStartTop = 310; // 第一张图片距离可视范围顶部边缘的距离
            const picRelativeLeft = 80; // 相邻图片之间的间隔
            const picWidth = 730; // 图片的预设宽度

            const left = view.viewType === '结束语' ? 128 : 1058;
            const top = picStartTop + picRelativeLeft * picIndex;
            const width = picWidth;

            conf.timeline[timelineIndex + picIndex].node[index] = {
              __config: { thumbnail: '', title: '', type: 'component' },
              actualDuration: 0,
              component: liveImage(
                _key,
                view.imageList[picIndex].url,
                docCommonStyle(left, top, width),
                {
                  animation: 'gradually',
                  speed: 4, // (6-x)s
                  isFilter: false,
                  blur: 0,
                }
              ),
              duration: 0,
              id: uuid(),
              key: _key,
              offset: 0,
            };
          }
        }
      }
    }
  };
};

// 字体预加载
const fontPreload = (
  key: number,
  timelineIndex: number,
  uniqueFont: (keyof typeof DOC_FONT)[]
): ScriptPlugin => {
  // 优化预加载顺序。按“腾讯体”“思源黑体常规”“思源黑体粗体”顺序加载
  return (conf: PlayConfig) => {
    if (!key || !timelineIndex) {
      return;
    }

    const sequence = ['w7', 'sourcesansRegular', 'sourcesansBold'];
    for (let i = 0; i < uniqueFont.length; i++) {
      conf.timeline[timelineIndex + i].node[0] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: liveText(
          key + i,
          {
            fontSize: calFontSize(0),
            fontFamily: [DOC_FONT[uniqueFont[i]]],
          },
          '字体预加载，隐藏不显示',
          docStyle.common.hidden,
          { animation: '', speed: typeAnimationSpeed.normal }
        ),
        hidden: true,
        duration: 2000,
        id: uuid(),
        key: key + i,
        offset: sequence.indexOf(uniqueFont[i]) * 1000,
      };
    }
  };
};

let mainContentDuration = 0;
// 计算节点时长
const scriptDocDuration: SinglePlugin = (conf) => {
  // timeline最大节点数量
  const max = Math.max(...conf.timeline.map((i) => i.node.length));
  let offset = 0;
  for (let i = 0; i < max; i++) {
    const speechWords: string | undefined = conf.timeline
      .map((timeline) => timeline.node[i])
      .filter(Boolean)
      .find(
        (i) =>
          i.component.id.includes('LiveSpeech') &&
          i.component.data.speechData.text
      )?.component.data.speechData.text;

    const backgroundMusic = conf.timeline
      .map((timeline) => timeline.node[i])
      .filter(Boolean)
      .find((i) => i.component.id.includes('BackgroundMusic'));

    // 计算“列”的时长
    const duration = Math.max(
      speechWords
        ? speechWords.length * LANGUAGE_SPEED
        : backgroundMusic && backgroundMusic?.duration > 0 // 如果有背景音乐且背景音乐显式定义了duration，则以背景音乐的时长为准
        ? backgroundMusic.duration
        : 10000, // 有数字人的情况按口播文案估算时长
      1000 // 无数字人的情况按1000估算时长（即10秒）
    );
    // 修改timeline这一列上所有Node的duration和offset
    let nodeDuration = 0;
    for (const pagedooPlayTimeline of conf.timeline) {
      const node = pagedooPlayTimeline.node[i];
      if (!node) continue;
      nodeDuration =
        node.duration < 0
          ? duration + node.duration
          : node.duration > 0
          ? node.duration
          : duration;
      node.offset += offset;
      node.duration = nodeDuration;
    }

    // 计算除封面&结尾以外所有节点的duration
    if (0 < i && i < max - 1) {
      mainContentDuration += nodeDuration;
    }
    // 打环节标
    conf.fragment.push({
      __config: { name: '' },
      id: uuid(),
      offset,
    });
    offset += duration;
  }
};

// 合并第2至倒数第二个节点的duration
const mergeMainContentNode: SinglePlugin = (conf) => {
  // 将封面&结尾中间所有结合合并为1个
  const firstColumnComponentId = conf.timeline.map((i) => {
    if (typeof i.node[0]?.component?.id === 'string') {
      return i.node[0]?.component?.id.split('/')[
        i.node[0]?.component?.id.split('/').length - 1
      ];
    }
    return '';
  });
  const mergeTimelineIndex = [
    firstColumnComponentId.indexOf('BackgroundMusic'), // 背景音乐
    firstColumnComponentId.indexOf('LiveVideo'), // 背景视频
  ];
  mergeTimelineIndex.map((i) => {
    conf.timeline[i].node.splice(1, conf.timeline[i].node.length - 2, {
      ...conf.timeline[i].node[1],
      duration: mainContentDuration,
    });
  });
};

export function initDOCTemplate(script: BaseScript, templateId: string) {
  switch (templateId) {
    case DOC_ID_ENUM.LEARNING_CENTER: {
      const copyScript = isEmpty(script)
        ? cloneDeep(docMockScript)
        : cloneDeep(script);
      const scriptData = {
        ...copyScript,
        views: [docTitlePage, ...copyScript.views, docEndPage],
      };

      const imagePlaceholder = 'image-placeholder';
      // const textPlaceholder = 'text-placeholder';
      const fontPlaceholder = 'font-placeholder';

      const titlePageCommon = [
        'background',
        'backgroundMusic',
        'section-title',
        'virtualman',
      ];
      const pageCommonBegin = ['background', 'backgroundMusic', 'progressbar'];
      const pageCommonEnd = ['text', 'subtitle', 'virtualman'];

      const layout = {
        // 封面
        titlePage: [...titlePageCommon],
        // 概述
        outline: [...pageCommonBegin, imagePlaceholder, ...pageCommonEnd],
        // 分点概述
        pointStatement: [
          ...pageCommonBegin,
          imagePlaceholder,
          ...pageCommonEnd,
        ],
        // 常见QA
        qa: [
          ...pageCommonBegin,
          'Q',
          'A',
          'Q-text',
          imagePlaceholder,
          ...pageCommonEnd,
        ],
        // 结束语
        concluding: [...pageCommonBegin, imagePlaceholder, ...pageCommonEnd],
        // 结尾
        endPage: [...titlePageCommon],
      };

      const fontMap: { [key in string]: keyof typeof DOC_FONT } = {
        'section-title': 'w7',
        'page-title-h1': 'w7',
        'page-title-h2': 'sourcesansBold',
        'paragraph-secondary': 'sourcesansRegular',
        Q: 'sourcesansBold',
        QText: 'sourcesansBold',
        A: 'sourcesansBold',
        AText: 'sourcesansRegular',
        subtitle: 'w7',
      };

      const uniqueFont: (keyof typeof DOC_FONT)[] = [];
      Object.values(fontMap).map((i) => {
        if (!uniqueFont.includes(i)) {
          uniqueFont.push(i);
        }
      });

      const component = [
        DOC_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC, // 如果调整 backgroundmusic 或 backgroundvideo 在 timeline 上的位置，需要配套修改 mergeMainContentNode 的逻
        DOC_TEMPLATE_COMPONENTS.VIRTUALMAN, // 数字人
        DOC_TEMPLATE_COMPONENTS.SPEECH, // 话术
        DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO, // 画面背景
        DOC_TEMPLATE_COMPONENTS.LOGO, // logo
        DOC_TEMPLATE_COMPONENTS.PROGRESSBAR, // 进度条
        DOC_TEMPLATE_COMPONENTS.SECTIO_TITLE, // 章节大标题，居中橙色
        DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1, // 一级标题，居中橙色
        DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2, // 二级标题，居左黑色
        DOC_TEMPLATE_COMPONENTS.Q,
        DOC_TEMPLATE_COMPONENTS.A,
        DOC_TEMPLATE_COMPONENTS.Q_TEXT,
        DOC_TEMPLATE_COMPONENTS.A_TEXT,
        DOC_TEMPLATE_COMPONENTS.PARAGRAPH_SECONDARY, // 二级正文
        // 'paragraph-thirdly', // 三级正文
        imagePlaceholder, // 画面配图占位
        // textPlaceholder, // 文本占位（暂时用不上）
        fontPlaceholder, // 字体预加载
        DOC_TEMPLATE_COMPONENTS.SUBTITLE, // 字幕
      ];

      // 计算所有类型模板需要的配图数量，为LiveImage组件预留Key值
      const maxNumOfImages = max(
        scriptData.views
          .filter((i) => has(i, 'imageList'))
          .map((i) => i.imageList)
          .map((i) => i?.length || 0)
      );

      // 替换 imagePlaceholder
      component.splice(
        component.indexOf(imagePlaceholder),
        1,
        ...new Array(maxNumOfImages).fill('').map((j, i) => `image${i}`)
      );

      // 替换 textPlaceholder
      component.splice(
        component.indexOf(fontPlaceholder),
        1,
        ...new Array(uniqueFont.length).fill('').map((j, i) => `font${i}`)
      );

      // 至此，component.length 大于等于时间轨道每一列最多需要的节点（node）数，为每一行Node分配Key值
      const startIndex = 2000; // 起始key值
      let componentMap: Record<string, number> = {};
      for (let i = 0; i < component.length; i++) {
        componentMap = {
          ...componentMap,
          [component[i]]: startIndex + i,
        };
      }

      const result = [
        // 初始化
        initPlugin(scriptData, component.length),

        // 背景音乐
        scriptBackgroundMusicPlugin({
          script: scriptData,
          key: componentMap.backgroundmusic,
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC
          ),
          commonStyle: commonHiddenStyle,
          volumn: 20,
          baseFn: (view) => {
            const type = ['封面', '结尾'].includes(view.viewType)
              ? view.viewType
              : '正文';
            return [
              docTemplateConfig[templateId].material.backgroundMusic[
                type as TMaterialViewType
              ].config,
            ];
          },
          isLoop: (view) =>
            view.viewType === '封面' ? false : view.viewType !== '结尾',
          duration: (view) => {
            const type = ['封面', '结尾'].includes(view.viewType)
              ? view.viewType
              : '正文';
            return getBgmDuration(
              docTemplateConfig[templateId].material.backgroundMusic[
                type as TMaterialViewType
              ].config.duration
            );
          },
        }),

        // 数字人
        scriptVirtualManPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.VIRTUALMAN)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.VIRTUALMAN),
          baseFn: (view) => ({
            keyLight: {
              enabled: true,
              tolerance: 0.2433,
            },
            virtualMan: docTemplateConfig[templateId].virtualman.face,
            voiceConfig: {
              currentVoiceItem: docTemplateConfig[templateId].virtualman.sound,
            },
            liveID: '',
            isWave: true,
            customScript: false,
            type: 'text',
            isGlobalVirtualman: false,
            isSegmentTexts: false,
            prefix: '',
            suffix: '',
          }),
          commonStyle: (view) =>
            docTemplateConfig[templateId].virtualman.position[
              view.viewType as `${DOC_VIEW_TYPES_CHINESE_NAME}`
            ],
        }),

        // 话术
        scriptLiveSpeechPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.SPEECH)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.SPEECH),
          commonStyle: commonHiddenStyle,
          // isSkip: (view) => ['封面', '结尾'].includes(view.viewType),
          getSpeechParams: ({ view, script }) => ({
            speechData: {
              type: view?.isInteractive === '是' ? 'answer' : 'text',
              text: ['封面', '结尾'].includes(view.viewType)
                ? '' // 封面&结尾没有台词
                : view?.speech || '',
            },
            speechDrive: { type: 'virtualman' },
            remixAnswer: {
              enabled: true,
              separator: SPEECH_SEPARATOR,
            },
          }),
        }),

        // 视频背景-封面
        scriptVideoPlugin({
          script: scriptData,
          key: safeKey(
            component.indexOf(DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO)
          ),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO
          ),
          isSkip: (view) => view.viewType !== '封面',
          setVideoUrl: ({ script }) => {
            return docTemplateConfig[templateId].material.background['封面']
              .config.url;
          },
          commonStyle: docStyle.common.bkg,
          setVideoStyle: () => docStyle.common.bkg,
        }),
        // 视频背景-正文
        scriptVideoPlugin({
          script: scriptData,
          key: safeKey(
            component.indexOf(DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO)
          ),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO
          ),
          isSkip: (view) =>
            view.viewType === '封面' || view.viewType === '结尾',
          setVideoUrl: ({ script }) => {
            return docTemplateConfig[templateId].material.background['正文']
              .config.url;
          },
          commonStyle: docStyle.common.bkg,
          setVideoStyle: () => docStyle.common.bkg,
        }),
        // 视频背景-结尾
        scriptVideoPlugin({
          script: scriptData,
          key: safeKey(
            component.indexOf(DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO)
          ),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.BACKGROUND_VIDEO
          ),
          isSkip: (view) => view.viewType !== '结尾',
          setVideoUrl: ({ script }) => {
            return docTemplateConfig[templateId].material.background['结尾']
              .config.url;
          },
          commonStyle: docStyle.common.bkg,
          setVideoStyle: () => docStyle.common.bkg,
        }),
        // Logo
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.LOGO)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.LOGO),
          isSkip: (view, index) =>
            !['概述', '分点概述', 'QA', '结束语'].includes(view.viewType),
          baseFn: (view) =>
            docTemplateConfig[templateId].material.logo.config.url,
          commonStyle: docTemplateConfig[templateId].material.logo.position,
        }),

        // 概述、分点概述、QA、结束页配图
        scriptDocImagesPlugin(
          scriptData,
          safeKey(componentMap.image0),
          component.indexOf('image0')
        ),

        // 封面&结尾 章节标题（section-title）
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.SECTIO_TITLE)),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.SECTIO_TITLE
          ),
          baseFn: (view) => scriptData.globalField?.title || '标题',
          isSkip: (view) => !['封面'].includes(view.viewType),
          fontStyle: {
            ...docTemplateConfig[templateId].textStyle[
              DOC_TEMPLATE_COMPONENTS.SECTIO_TITLE
            ],
            fontSize: calFontSize(
              (scriptData?.globalField?.title || '标题').length >
                MAX_SECTION_STR_LENGTH
                ? 100
                : 128
            ), // 超过5个字缩小字体
            lineHeight: 1.5,
            useImage: 3, // 文本转图像
          },
          getAnimationConfig: (view) => ({
            animation: '',
            speed: typeAnimationSpeed.normal,
          }),
          commonStyle:
            docStyle.sectionPage[
              (scriptData?.globalField?.title || '标题').length >
              MAX_SECTION_STR_LENGTH
                ? 'smallerTitle'
                : 'title'
            ],
        }),

        // 一级标题(page-title-h1)
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(
            component.indexOf(DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1)
          ),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1
          ),
          isSkip: (view) => !['概述', '分点概述', 'QA'].includes(view.viewType),
          baseFn: (view) =>
            (view.viewType === 'QA' ? '常见问题' : view.viewName) || '',
          fontStyle:
            docTemplateConfig[templateId].textStyle[
              DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1
            ],
          commonStyle: docStyle.common.h1title,
        }),

        // 二级标题(page-title-h2)
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(
            component.indexOf(DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2)
          ),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2
          ),
          isSkip: (view) =>
            !(['分点概述'].includes(view.viewType) && view?.viewName),
          baseFn: (view) => view.viewName,
          fontStyle:
            docTemplateConfig[templateId].textStyle[
              DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2
            ],
          commonStyle: docStyle.pointStatement.h2title,
        }),
        // 二级正文
        // 情况1、概述、分点概述（top值不同）
        // 情况2、结束语
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(
            component.indexOf(DOC_TEMPLATE_COMPONENTS.PARAGRAPH_SECONDARY)
          ),
          timelineIndex: component.indexOf(
            DOC_TEMPLATE_COMPONENTS.PARAGRAPH_SECONDARY
          ),
          isSkip: (view) =>
            !(
              ['概述', '分点概述', '结束语'].includes(view.viewType) &&
              view?.viewMainText
            ),
          baseFn: (view) => view.viewMainText,
          fontStyle: (view) => {
            const actualWidth = ['概述', '分点概述'].includes(view.viewType)
              ? DOC_MAX_LIVETEXT_WIDTH
              : view.imageList && view.imageList?.length > 0
              ? 768
              : DOC_MAX_LIVETEXT_WIDTH;
            return {
              ...docTemplateConfig[templateId].textStyle[
                DOC_TEMPLATE_COMPONENTS.PARAGRAPH_SECONDARY
              ],
              width: calFontDisplayAreaWidth(actualWidth),
            };
          },
          getAnimationConfig: () => ({
            animation: 'typewriter',
            speed: typeAnimationSpeed.fast,
          }),
          commonStyle: (view) => {
            const style = {
              概述: docStyle.overview.paragraph,
              分点概述: docStyle.pointStatement.paragraph,
              结束语:
                docStyle.concluding.paragraph[
                  view.imageList ? 'withPic' : 'noPic'
                ],
            };
            return style[view.viewType as '概述' | '分点概述' | '结束语'];
          },
        }),
        // 渲染“Q”
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.Q)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.Q),
          isSkip: (view) => !(view.viewType === 'QA'),
          baseFn: (view) => 'Q:',
          fontStyle: {
            ...docTemplateConfig[templateId].textStyle[
              DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1 // 复用H1的样式
            ],
            fontFamily: [DOC_FONT.sourcesansBold],
            width: calFontDisplayAreaWidth(DOC_Q_TITLE_WIDTH),
            color: {
              color: '#FA9D38',
              realColor: '#FA9D38',
              show: true,
            },
          },
          getAnimationConfig: () => ({
            animation: '',
            speed: typeAnimationSpeed.normal,
          }),
          commonStyle: docStyle.qa.QTitle,
        }),
        // 渲染“Q”的文本
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.Q_TEXT)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.Q_TEXT),
          isSkip: (view) => !(view.viewType === 'QA'),
          baseFn: (view) => view?.viewName || '',
          fontStyle: (view) => {
            const actualWidth = view.imageList
              ? 700
              : DOC_MAX_LIVETEXT_WIDTH - DOC_Q_TITLE_WIDTH;
            return {
              ...docTemplateConfig[templateId].textStyle[
                DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H2 // 复用H2的样式
              ],
              color: {
                color: '#FA9D38',
                realColor: '#FA9D38',
                show: true,
              },
              width: calFontDisplayAreaWidth(actualWidth),
            };
          },
          getAnimationConfig: () => ({
            animation: '',
            speed: typeAnimationSpeed.normal,
          }),
          commonStyle: docStyle.qa.QText,
        }),
        // 渲染“A”
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.A)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.A),
          isSkip: (view) => !(view.viewType === 'QA'),
          baseFn: (view) => 'A:',
          fontStyle: {
            ...docTemplateConfig[templateId].textStyle[
              DOC_TEMPLATE_COMPONENTS.PAGE_TITLE_H1 // 复用H1的样式
            ],
            fontFamily: [DOC_FONT.sourcesansBold],
            color: {
              color: '#4c4c4c',
              realColor: '#4c4c4c',
              show: true,
            },
            width: calFontDisplayAreaWidth(DOC_Q_TITLE_WIDTH),
          },
          getAnimationConfig: () => ({
            animation: '',
            speed: typeAnimationSpeed.normal,
          }),
          commonStyle: docStyle.qa.ATitle,
        }),
        // 渲染“A”的文本
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.A_TEXT)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.A_TEXT),
          isSkip: (view) => !(view.viewType === 'QA'),
          baseFn: (view) => view.viewMainText || view.speech || '暂无',
          fontStyle: (view) => {
            const actualWidth = view.imageList
              ? 700
              : DOC_MAX_LIVETEXT_WIDTH - DOC_Q_TITLE_WIDTH;
            return {
              ...docTemplateConfig[templateId].textStyle[
                DOC_TEMPLATE_COMPONENTS.A_TEXT
              ],
              width: calFontDisplayAreaWidth(actualWidth),
            };
          },
          getAnimationConfig: () => ({
            animation: 'typewriter',
            speed: typeAnimationSpeed.fast,
          }),
          commonStyle: docStyle.qa.AText,
        }),

        // 字幕
        scriptSubtitlePlugin({
          script: scriptData,
          key: safeKey(component.indexOf(DOC_TEMPLATE_COMPONENTS.SUBTITLE)),
          timelineIndex: component.indexOf(DOC_TEMPLATE_COMPONENTS.SUBTITLE),
          fontData: () =>
            docTemplateConfig[templateId].textStyle[
              DOC_TEMPLATE_COMPONENTS.SUBTITLE
            ],
          commonStyle: docStyle.common.subtitle,
          delayTime: 200,
        }),

        // 字体预加载
        fontPreload(componentMap.font0, component.indexOf('font0'), uniqueFont),

        scriptDocDuration,
        // 合并除封面&结尾以外中间部分的背景音乐和背景图片，同时调整第一、最后一列所有Node节点的duration，以匹配背景音乐的真实时长
        mergeMainContentNode,
        livePlugin,
        format,
        setMeta({ size: scriptData.size }),
      ];

      console.log('templateId LEARNING_CENTER', templateId);
      return result;
    }
    default:
      throw new Error('unknown template ID');
  }
}
