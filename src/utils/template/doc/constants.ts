// DOC模板画面类型枚举
export enum DOC_VIEW_TYPES_CHINESE_NAME {
  TITLE_PAGE = '封面',
  OVERVIEW = '概述',
  POINT_STATEMENT = '分点概述',
  QA = 'QA',
  CONCLUDING = '结束语',
  END_PAGE = '结尾',
}

// 模板尺寸
export const DOC_WIDTH = 1920;
export const DOC_HEIGHT = 1080;

// 文档模板组件枚举
export enum DOC_TEMPLATE_COMPONENTS {
  BACKGROUND_MUSIC = 'background_music',
  SPEECH = 'speech',
  VIRTUALMAN = 'virtualman',
  BACKGROUND_VIDEO = 'background_video',
  LOGO = 'logo',
  PROGRESSBAR = 'progressbar',
  SECTIO_TITLE = 'title-h1',
  PAGE_TITLE_H1 = 'page-title-h1',
  PAGE_TITLE_H2 = 'page-title-h2',
  Q = 'title-h3',
  A = 'A',
  Q_TEXT = 'Q-text',
  A_TEXT = 'A-text',
  PARAGRAPH_SECONDARY = 'paragraph-secondary',
  SUBTITLE = 'subtitle',
}

export const DOC_MAX_LIVETEXT_WIDTH = 1488;
export const DOC_DEFAULT_MARGIN_LEFT = (DOC_WIDTH - DOC_MAX_LIVETEXT_WIDTH) / 2;
export const DOC_Q_TITLE_WIDTH = 90;

// 字体
export enum DOC_FONT {
  // 腾讯体-W7
  w7 = 'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font26.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font26.woff',
  // 腾讯体-W3
  // w3: 'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font25.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font25.woff',
  // 思源黑体-Bold
  sourcesansBold = 'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
  // 思源黑体-Regular
  sourcesansRegular = 'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
}
