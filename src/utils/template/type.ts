import { LiveEditorOrigin } from '@/pages/Editor/common/openEditor';
import { IUsage } from './video/config';
import { BaseScript } from '@/components/ScriptForm/type';
import { View } from './plugins/type';

export enum TemplateContentType {
  Video,
  Live,
}

export enum SceneSpecification {
  Landscape,
  Portrait,
}

export interface Template {
  /**
   * 内容类型
   */
  contentType: TemplateContentType;
  /**
   * 模板来源
   */
  origin?: LiveEditorOrigin;
  /**
   * 尺寸
   */
  size?: [number, number];
  /**
   * 横竖屏
   */
  scene?: SceneSpecification;
  /**
   * 模板名称
   */
  name?: string;
  /**
   * 模板描述
   */
  description?: string;
  /**
   * 模板缩略图
   */
  pic_url?: string;
  /**
   * 应用场景ID
   */
  scenarios?: number;
}

export type TempalteType = LiveEditorOrigin | 'blank' | 'default';

export type TemplateMap = {
  [x in TempalteType]?: {
    [id: string]: Template;
  };
};

export type EnumToArray<T extends object> = {
  [K in keyof T]: T[K];
};

type GetUnionKeys<Unoin> = Unoin extends any
  ? {
      [key in keyof Unoin]: key;
    } extends {
      [key in keyof Unoin]: infer K;
    }
    ? K
    : never
  : never;

type UnionToInterByKeys<Union, Keys extends string | number | symbol> = {
  [key in Keys]: Union extends any
    ? {
        [k in keyof Union]: k extends key ? Union[k] : never;
      } extends {
        [k in keyof Union]: infer P;
      }
      ? P
      : never
    : never;
};

export type UnionToInter<Unoin> = UnionToInterByKeys<
  Unoin,
  GetUnionKeys<Unoin>
>;

export type EnumToUnion<T extends object> = T[keyof T];

export const GLOBAL_FIELDS = {
  TITLE: 'title',
  SUBTITLE: 'subTitle',
} as const;
export interface IHandleComponentConfig {
  componentConfig: IUsage['componentConfig'];
  view?: View;
  pagedooScript?: BaseScript;
}
