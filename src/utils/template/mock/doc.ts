import { BaseScript } from '@/components/ScriptForm/type';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { LANDSCAPE_DEFAULT_SIZE } from '@/pages/Question/constant';

export const docTitlePage: BaseScript['views'][0] = {
  viewType: '封面',
  viewName: '标题',
  viewContent: 'viewContent',
  duration: '01:25-01:36',
  viewMainText: '这里是结尾的画面正文',
  speech: '这里是台词，这里是台词，这里是台词。',
  imageList: [{ url: 'cosurl', width: '300', height: '400' }],
  flowerText: '',
  isInteractive: '否',
  background: '',
};

export const docEndPage: BaseScript['views'][0] = {
  viewType: '结尾',
  viewName: '感谢您的聆听',
  viewContent: 'viewContent',
  duration: '01:25-01:36',
  viewMainText: '这里是结尾的画面正文',
  speech: '这里是台词，这里是台词，这里是台词。',
  imageList: [
    {
      url: 'https://pagedoo.pay.qq.com/material/@platform/31c83c91296a4d2a1c3a16815cbf175f.png',
      width: '300',
      height: '400',
    },
  ],
  flowerText: '',
  isInteractive: '否',
  background: '',
};

export const docMockScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.DOC,
  size: LANDSCAPE_DEFAULT_SIZE, // 接口返回是[1280, 720]；设计稿是[1920, 1080]
  globalField: {
    title: '主标题',
    subTitle: '副标题',
  },
  views: [
    // docTitlePage,
    // 概述
    {
      isInteractive: '否',
      speech:
        '这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词。',
      duration: '00:00-00:19',
      flowerText: '',
      viewContent: '这里是画面内容。这里是画面内容。这里是画面内容。',
      viewName: '概述',
      viewMainText:
        '这里是画面正文。这里是画面正文。这里是画面正文。这里是画面正文。这里是画面正文。这里是画面正文。这里是画面正文。这里是画面正文。这里是画面正文。',
      viewType: '概述',
      background: '',
    },
    // 分点概述
    {
      imageList: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/6f6a2509f06b4eb330df785849b5c1b4.png',
        },
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/6f6a2509f06b4eb330df785849b5c1b4.png',
        },
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/6f6a2509f06b4eb330df785849b5c1b4.png',
        },
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/6f6a2509f06b4eb330df785849b5c1b4.png',
        },
      ],
      isInteractive: '否',
      speech:
        '这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词。',
      duration: '00:37-00:55',
      flowerText: '',
      viewContent: '这里是画面内容。这里是画面内容。这里是画面内容。',
      viewName: '分点概述',
      viewMainText:
        '这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。',
      viewType: '分点概述',
      background: '',
    },
    // qa
    {
      imageList: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/6f6a2509f06b4eb330df785849b5c1b4.png',
        },
      ],
      isInteractive: '否',
      speech: '这里是台词，这里是台词，这里是台词，这里是台词。',
      duration: '01:31-01:49',
      flowerText: '',
      viewContent: '这里是画面内容。这里是画面内容。这里是画面内容。',
      viewName: '这里是问题',
      viewMainText: '这里是回答这里是回答这里是回答这里是回答',
      viewType: 'QA',
      background: '',
    },
    // 结束语
    {
      imageList: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/6f6a2509f06b4eb330df785849b5c1b4.png',
        },
      ],
      isInteractive: '否',
      speech:
        '关于视频号橱窗评分，您是否还有疑问？我们为您解答常见问题，如商品销量与评分的关联、恶意差评的处理等。消除您的疑虑，让您更安心地经营店铺。视频号橱窗评分，助您店铺经营更上一层楼！',
      duration: '01:31-01:49',
      flowerText: '',
      viewContent: '这里是画面内容。这里是画面内容。这里是画面内容。',
      viewName: '常见问题',
      viewMainText: '这里是结束语。这里是结束语。这里是结束语。这里是结束语。',
      viewType: '结束语',
      background: '',
    },
    // docEndPage,
  ],
};
