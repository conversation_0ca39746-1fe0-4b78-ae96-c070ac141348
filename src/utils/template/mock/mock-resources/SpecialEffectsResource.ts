export const SpecialEffectsResource = {
  wow: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/7c7137a645e72032f607e6b4795700af.gif',
    resource_description: '',
    resource_id: '0d7af9b8-3de1-4dd7-a01d-cd2d0ff521a1',
    resource_name: 'wow',
  },
  wow1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/6e2c2127ac7d76fcdbeab56fe1b184ce.gif',
    resource_description: '',
    resource_id: 'c82b02cd-dce5-43e7-b3c6-7ec1be424da4',
    resource_name: 'wow',
  },
  wow2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/44fa61ea0793cb1e9a6ac25bbbf1ff13.gif',
    resource_description: '',
    resource_id: '669bec4f-e48c-4369-bff3-74d6a54905e5',
    resource_name: 'wow',
  },
  点赞: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/21b615ed99d44005bee5f828f03f4929.gif',
    resource_description: '',
    resource_id: 'b98eebed-669f-4f0f-b166-c0c394f7c50c',
    resource_name: '点赞',
  },
  点赞1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/a4fc8f7c722e10cfd694baf773b8ca4b.gif',
    resource_description: '',
    resource_id: '968922fb-33b5-4aa4-8474-a8e07873485b',
    resource_name: '点赞',
  },
  点赞2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/e4610a485e54488c234a3a3f0b3f0a87.gif',
    resource_description: '',
    resource_id: '3ed9f173-8841-4d22-8cad-80d9fe97d2a2',
    resource_name: '点赞',
  },
  否定: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/c625304d95fa00ff2b64569b222fe5aa.gif',
    resource_description: '',
    resource_id: '4b147c63-0be1-41d3-856b-e66a79515871',
    resource_name: '否定',
  },
  否定1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/49ad4f8e57f3298a294120085679a591.gif',
    resource_description: '',
    resource_id: 'a6ebc4fa-2b7e-4d12-8ee5-3cca613f539b',
    resource_name: '否定',
  },
  否定2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/6b348a1d2daa69cd06fc7c0775f9255d.gif',
    resource_description: '',
    resource_id: '2901ebe9-6fe6-4a50-8719-498eb28c333d',
    resource_name: '否定',
  },
  鼓掌: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/12574164d50f69cb4a338583cabe0308.gif',
    resource_description: '',
    resource_id: 'c9419b1f-67de-441a-9d82-b31da023eb76',
    resource_name: '鼓掌',
  },
  鼓掌1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/33f01f9876b853922ad340a1efbe5f1c.gif',
    resource_description: '',
    resource_id: 'a8ffcc23-4e37-4fe2-ad61-9df2bf2d67e8',
    resource_name: '鼓掌',
  },
  鼓掌2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/cdb5786fab6bbd53aaef5fc286cbe25d.gif',
    resource_description: '',
    resource_id: '33a10399-e45f-4d73-bbc5-623077d54379',
    resource_name: '鼓掌',
  },
  箭头: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/69b7bbabdd9594de7efbacfba51a0637.gif',
    resource_description: '',
    resource_id: '22bedd96-4f54-4609-b783-5c86f102a607',
    resource_name: '箭头',
  },
  箭头1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/fdce7894d604dca889c2755c78683fc0.gif',
    resource_description: '',
    resource_id: 'd51a80ac-5d8b-40ea-a275-c0661a91fe60',
    resource_name: '箭头',
  },
  箭头2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/538243215d2ec7916ed354badfece439.gif',
    resource_description: '',
    resource_id: 'a131902f-444c-491d-b207-869c290d0ae3',
    resource_name: '箭头',
  },
  警告: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/3377ed28ebaa37efe0553b7f2250e186.gif',
    resource_description: '',
    resource_id: '166e29f8-2360-4f88-b33d-ec312fec9b66',
    resource_name: '警告',
  },
  警告1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/0b39ee6e39a7f657f70612eedb795bad.gif',
    resource_description: '',
    resource_id: 'a2843387-37a4-4b4a-92ce-cdb02c8bd859',
    resource_name: '警告',
  },
  警告2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/b5cb48eca676ea4a5e731e83a36c455c.gif',
    resource_description: '',
    resource_id: 'd6bff95a-a609-48a0-a62a-f1ae375ed8f5',
    resource_name: '警告',
  },
  肯定: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/047dbc820dcbaf4c18e5a515e7954642.gif',
    resource_description: '',
    resource_id: '0cd6e1b2-ea34-4243-b547-7b42a5e0feaa',
    resource_name: '肯定',
  },
  肯定1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/f2c6989c92ef812f62304e44c20369c3.gif',
    resource_description: '',
    resource_id: '50fff7dd-2a8c-461b-9f80-91350e929a07',
    resource_name: '肯定',
  },
  肯定2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/d8770f4e7d09a04ded9b592b4e8b7f65.gif',
    resource_description: '',
    resource_id: '57058ca9-8d2f-42b3-9af4-c15bf1e860a3',
    resource_name: '肯定',
  },
  哭泣: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/8c95781bc9a20e1f13bcd41a2bd0bbd7.gif',
    resource_description: '',
    resource_id: 'e4e8c76b-9154-4724-a6ea-29d449473332',
    resource_name: '哭泣',
  },
  哭泣1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/dcaa37fe59497e3a58de2299e93ead82.gif',
    resource_description: '',
    resource_id: 'b20f773a-a8b1-43ce-a6aa-449dbae4298d',
    resource_name: '哭泣',
  },
  哭泣2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/271651bd20ee3734d1e1104eb9578424.gif',
    resource_description: '',
    resource_id: 'de2a75fb-c391-461f-a839-2978cb33e116',
    resource_name: '哭泣',
  },
  强调: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/1e0e481b3a22c93f9178c2c55c7ed65e.gif',
    resource_description: '',
    resource_id: '1bf51e3a-d501-4107-b44a-dd3de0a2d311',
    resource_name: '强调',
  },
  强调1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/81d4bdee108ba294bc5b9300be7ade2f.gif',
    resource_description: '',
    resource_id: '4e6a1a36-f02e-44e8-a30b-76f62f699b3f',
    resource_name: '强调',
  },
  强调2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/cc5c3015cd224242be29dd46b3e6b1bc.gif',
    resource_description: '',
    resource_id: '9e460a34-bc74-42bc-bf1a-6f451be7e2d4',
    resource_name: '强调',
  },
  撒花: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/5f6f70add015d3b6ff203d3bada3ae6e.gif',
    resource_description: '',
    resource_id: '973717ce-6d20-4f99-ad62-726aad013023',
    resource_name: '撒花',
  },
  撒花1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/44849862f96fb2d9ba1d27163b9eca0d.gif',
    resource_description: '',
    resource_id: 'd842b644-b827-4ced-b183-f94419f4df1c',
    resource_name: '撒花',
  },
  撒花2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/5dfb6629a7b062f13f82016e3f388987.gif',
    resource_description: '',
    resource_id: '5107f009-321c-4f8b-b2fb-10a50f1b75a3',
    resource_name: '撒花',
  },
  撒金币: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/0b5ae84a0586bdfcb70699a5a7034df7.gif',
    resource_description: '',
    resource_id: '87ab38f4-aae8-4c7e-8254-ebfeed3dc55f',
    resource_name: '撒金币',
  },
  撒金币1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/bf4aa2ff6b37a2206bc06e264fff7daf.gif',
    resource_description: '',
    resource_id: '611bd876-a929-4312-88eb-cd9c678039e0',
    resource_name: '撒金币',
  },
  撒金币2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/a95ad0c2e35d46cc9e95cb5c50c78a71.gif',
    resource_description: '',
    resource_id: '9e7201d0-6540-4509-a7df-99a8ee3cf72c',
    resource_name: '撒金币',
  },
  叹气: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/083a2bb0a17d08f8b49491975b7482ba.gif',
    resource_description: '',
    resource_id: '8922a590-30ab-4aac-bbf4-bc5c2ab27484',
    resource_name: '叹气',
  },
  叹气1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/a2faf3ba1a2a62c2ef23edc4da1546d5.gif',
    resource_description: '',
    resource_id: '5e97cb72-28ed-412b-b384-7ca7bff4902e',
    resource_name: '叹气',
  },
  叹气2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/9d4849082391ca9a29fe1d87fc84f4ab.gif',
    resource_description: '',
    resource_id: 'cf1caa38-be9a-4254-92c1-1266a7117f49',
    resource_name: '叹气',
  },
  疑问: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/0dbd51be40c1622775f3d29d2b024747.gif',
    resource_description: '',
    resource_id: '548b3568-f712-49d3-a74a-50fc0b911424',
    resource_name: '疑问',
  },
  疑问1: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/09ce26c0416be754731e5b0c94972fd0.gif',
    resource_description: '',
    resource_id: 'd1959a5e-8738-420e-b13a-d32f17903c4f',
    resource_name: '疑问',
  },
  疑问2: {
    audio_address: '',
    image_address:
      'https://pagedoo.pay.qq.com/material/@platform/d7433235b3bcaf10788b19bc2807e6ab.gif',
    resource_description: '',
    resource_id: '5f8fdc94-2c2c-448a-bb01-55de684d1a72',
    resource_name: '疑问',
  },
};

// resource 数组转 对象
// function arrayToObject(arr) {
//   const obj = {};

//   arr.forEach(item => {
//     let name = item.name;
//     let counter = 1;

//     while (obj[name]) {
//       name = `${item.name}${counter}`;
//       counter++;
//     }

//     obj[name] = item;
//   });

//   return obj;
// }
