import { TEXT_DEFAULT_SIZE } from '@/pages/Question/constant';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { BaseScript } from '@/components/ScriptForm/type';

export const ECommerceMockScript: BaseScript = {
  backgroundImage: [
    {
      url: '',
    },
  ],
  type: ORIGIN_TYPE.E_COMMERCE,
  size: TEXT_DEFAULT_SIZE,
  globalField: {
    title: '主标题',
    subTitle: '副标题',
  },
  views: [
    {
      viewName: '产品介绍 强化产品成分',
      viewType: '近景',
      viewContent: '主播介绍SK-II品牌和产品。 主播详细介绍产品的核心成分。',
      viewMainText: '',
      duration: '00:21-01:05',
      speech: '请在这里填入你的台词文案请在这里填入你的台词文案',
      flowerText: '展示画面亮点',
      isInteractive: '否',
      background: '',
    },
    {
      viewName: '强调产品效果 突出使用体验',
      viewType: '特写',
      viewContent:
        '主播展示产品使用前后的对比效果。 主播分享自己使用产品的感受。',
      viewMainText: '',
      duration: '01:05-01:48',
      speech: '请在这里填入你的台词文案请在这里填入你的台词文案',
      flowerText: '展示画面亮点',
      isInteractive: '否',
      background: '',
    },
    {
      viewName: '介绍适用人群 延伸场景介绍 限时限量优惠 服务质量保障',
      viewType: '近景',
      viewContent:
        '主播说明产品适合的人群。 主播模拟产品在不同场景下的使用效果。 主播宣布限时限量优惠活动。 主播强调服务质量保障。',
      viewMainText: '',
      duration: '01:48-03:16',
      speech: '请在这里填入你的台词文案请在这里填入你的台词文案',
      flowerText: '展示画面亮点',
      isInteractive: '否',
      background: '',
    },
  ],
};
