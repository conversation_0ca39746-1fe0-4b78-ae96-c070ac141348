import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { LANDSCAPE_DEFAULT_SIZE } from '@/pages/Question/constant';
import { viewMocker } from './utils';
import { BaseScript } from '@/components/ScriptForm/type';
import { GLOBAL_FIELDS } from '@/utils/template/type';

const mockVideo =
  'https://pagedoo.pay.qq.com/material/@platform/bb137960d1a78283a9e55cb6ab613ff5.mov';
export const gameOverseasScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.GAME_PROMOTION,
  size: LANDSCAPE_DEFAULT_SIZE, // 接口返回是1280, 720；设计稿是1920, 1080
  globalField: {
    [GLOBAL_FIELDS.TITLE]: '',
    [GLOBAL_FIELDS.SUBTITLE]: '',
    info: {
      video_address: mockVideo,
      video_duration: '101100',
    },
    globalResourceList: [],
  },
  globalSpeech:
    '臨場感溢れる戦闘，リアルタイムで変化する照明，やトリガーを引くたびに迫力のある銃声が響き渡る，360度のサラウンド音響効果',
  views: [
    // 概述
    viewMocker({
      duration: '00:00-00:05',
    }),
  ],
};
