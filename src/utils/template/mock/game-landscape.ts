import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { VIDEO_DEFAULT_SIZE } from '@/pages/Question/constant';
import { viewMocker } from './utils';
import { RESOURCES_TYPE } from '../video/constants';
import { FeaturedSoundResource } from './mock-resources/FeaturedSoundResource';
import { SpecialEffectsResource } from './mock-resources/SpecialEffectsResource';
import { BaseScript } from '@/components/ScriptForm/type';
import { GLOBAL_FIELDS } from '@/utils/template/type';

const video916 =
  'https://pagedoo.pay.qq.com/material/@platform/6d287c2ab1ba6449bb9f790b7a17cfb5.mp4';
const video11 =
  'https://pagedoo.pay.qq.com/material/@platform/05222ded455aad224cd682cf42c6f8e6.mp4';
const video169 =
  'https://pagedoo.pay.qq.com/material/@platform/beb0a5077b9fa169b69f68aacc03c046.mp4';

const mockVideo =
  'https://pagedoo.pay.qq.com/material/@platform/138cb407d2cd76599c280b747c8366d6.mp4';

export const gameLandScapeScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.GAME_PROMOTION,
  // backgroundVideo: mockVideo,
  // 'https://pagedoo.pay.qq.com/material/@platform/b173f0309842758a986ddd7cf15db8c1.mp4',
  //
  size: VIDEO_DEFAULT_SIZE, // 接口返回是1280, 720；设计稿是1920, 1080
  globalField: {
    [GLOBAL_FIELDS.TITLE]: '偏清新的模板',
    [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
    info: {
      video_address: mockVideo,
      video_duration: '27067',
    },
    globalResourceList: [
      {
        resource: SpecialEffectsResource.撒金币2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '2',
      },
      {
        resource: SpecialEffectsResource.鼓掌1,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '7',
      },
      {
        resource: SpecialEffectsResource.wow2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '12',
      },
      {
        resource: FeaturedSoundResource.开怀大笑,
        resourceKey: RESOURCES_TYPE.FEATURED_SOUND_EFFECTS,
        resourceType: 'xxx',
        showTime: '13',
      },
      {
        resource: SpecialEffectsResource.撒花,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '21',
      },
      {
        resource: {
          flowerText: '这一局 帅就完事了',
        },
        resourceKey: RESOURCES_TYPE.FLOWER_CHARACTER,
        resourceType: 'xxx',
        showTime: '5',
      },
    ],
  },
  globalSpeech:
    '棋牌，这项集合计算、推理、运气的运动，计算和推理增加了游戏的竞技性，而运气则一定程度上平衡了竞技性，增加了不可确定性，反而更有趣味，有时候新手上阵也能打败资深牌手。但不管是追求碰运气还是提升牌技，打牌最重要的事还是开心！它不仅仅是娱乐消遣、提升智力，也是聚会、联络感情的绝佳方式，无论运气好坏、牌技高低，都可以在同城游这样的棋牌平台上，和亲朋好友开心来一把',
  views: [
    // 概述
    viewMocker({
      duration: '00:00-00:05',
    }),
    viewMocker({
      duration: '00:05-00:10',
    }),
    viewMocker({
      duration: '00:10-00:15',
    }),
    viewMocker({
      duration: '00:15-00:20',
    }),
    viewMocker({
      duration: '00:20-00:25',
    }),
  ],
};
