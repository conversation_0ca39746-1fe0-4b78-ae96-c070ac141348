import { resourceItem } from '@/type/pagedoo';
import { EnumToUnion } from '../video/type';
import { RESOURCES_TYPE } from '../video/constants';
import { BaseScript } from '@/components/ScriptForm/type';

type Resources = {
  [K in EnumToUnion<typeof RESOURCES_TYPE>]?: resourceItem[];
};
// 用于mock创建 view
export const viewMocker = ({
  duration,
  resources,
  flowerText,
  speech,
}: {
  duration?: string;
  resources?: Resources;
  flowerText?: string;
  speech?: string;
}): BaseScript['views'][number] => {
  return {
    speech: speech || '',
    duration: duration || '',
    viewContent: '',
    viewName: '',
    viewMainText: '',
    viewType: '',
    flowerText: flowerText || '',
    background: '',
    imageList: [],
    isInteractive: '否',
    resources,
  };
};
