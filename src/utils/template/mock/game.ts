import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { VIDEO_DEFAULT_SIZE } from '@/pages/Question/constant';
import { viewMocker } from './utils';
import { RESOURCES_TYPE } from '../video/constants';
import { PatchResource } from './mock-resources/patchResource';
import { FeaturedSoundResource } from './mock-resources/FeaturedSoundResource';
import { SpecialEffectsResource } from './mock-resources/SpecialEffectsResource';
import { BaseScript } from '@/components/ScriptForm/type';

// 压缩前
const video169 =
  'https://pagedoo.pay.qq.com/material/@platform/beb0a5077b9fa169b69f68aacc03c046.mp4';
const mockVideoOrigin =
  'https://pagedoo.pay.qq.com/material/@platform/6f07c2b23871fd8f06686d0b688c30b1.mp4';
const video11 =
  'https://pagedoo.pay.qq.com/material/@platform/05222ded455aad224cd682cf42c6f8e6.mp4';
const mockVideo =
  'https://pagedoo.pay.qq.com/material/@platform/9634603b3df1c86b34ae3919a5e58434.mov';

// 竖版
export const gameMockScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.GAME_PROMOTION,

  globalField: {
    info: {
      video_address: mockVideo,
      video_duration: '64700',
    },
    title: '地铁跑酷',
    subTitle: '',
    globalResourceList: [
      {
        resource: SpecialEffectsResource.撒金币2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '2',
        viewTime: '00:00-00:05',
      },
      {
        resource: PatchResource.wow,
        resourceKey: RESOURCES_TYPE.PATCH,
        resourceType: 'xxx',
        showTime: '7',
        viewTime: '00:05-00:10',
      },
      {
        resource: SpecialEffectsResource.箭头1,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '12',
        viewTime: '00:10-00:15',
      },
      {
        resource: FeaturedSoundResource.失败退场,
        resourceKey: RESOURCES_TYPE.FEATURED_SOUND_EFFECTS,
        resourceType: 'xxx',
        showTime: '13',
        viewTime: '00:15-00:20',
      },
      {
        resource: SpecialEffectsResource.强调,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '21',
      },
      {
        resource: SpecialEffectsResource.肯定2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '26',
      },
      {
        resource: FeaturedSoundResource.警报,
        resourceKey: RESOURCES_TYPE.FEATURED_SOUND_EFFECTS,
        resourceType: 'xxx',
        showTime: '31',
      },
      {
        resource: SpecialEffectsResource.wow2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '37',
      },
      {
        resource: FeaturedSoundResource.芜湖,
        resourceKey: RESOURCES_TYPE.FEATURED_SOUND_EFFECTS,
        resourceType: 'xxx',
        showTime: '42',
      },
      {
        resource: SpecialEffectsResource.哭泣2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '47',
      },
      {
        resource: FeaturedSoundResource['叮叮叮 - 强调'],
        resourceKey: RESOURCES_TYPE.FEATURED_SOUND_EFFECTS,
        resourceType: 'xxx',
        showTime: '52',
      },
      {
        resource: SpecialEffectsResource.撒花2,
        resourceKey: RESOURCES_TYPE.SPECIAL_EFFECTS_GIF,
        resourceType: 'xxx',
        showTime: '57',
      },
      {
        resource: {
          flowerText: '你是我的神嘎嘎暴力',
        },
        resourceKey: RESOURCES_TYPE.FLOWER_CHARACTER,
        resourceType: 'xxx',
        showTime: '2',
      },
    ],
  },
  //
  size: VIDEO_DEFAULT_SIZE, // 接口返回是1280, 720；设计稿是1920, 1080
  globalSpeech:
    '来呀，各位小伙伴们，咱们一起加入《地铁跑酷》的世界吧！在这里，你将成为涂鸦少年，体验在地铁轨道上狂奔的刺激感觉。只需轻轻一触，就能跳跃、滑行，轻松躲避障碍物，还能顺手抓住金币哦！《地铁跑酷》可是个无尽模式的游戏，你得一直往前跑，收集金币，升级技能，挑战极限。还有多种角色供你选择，每个角色都有大招，玩起来超带劲！而且，每周都有新挑战和角色加入，让你永远停不下来！这游戏的画面，那可真是色彩斑斓，炫酷至极！看那涂鸦少年，在地铁轨道上穿梭，就像是在看一场动感十足的动画。而且，这游戏操作超简单，新手老手都能轻松上手，一玩就停不下来！小伙伴们，你们还在等啥？赶紧加入《地铁跑酷》，和我们一起体验速度与激情吧！在微信里搜索“地铁跑酷”，就能轻松找到这款游戏。别犹豫了，快来加入我们，一起在地铁轨道上狂奔，享受速度带来的快感吧！',
  views: [
    // 概述
    viewMocker({
      duration: '00:00-00:05',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.撒金币2],
      },
    }),
    viewMocker({
      duration: '00:05-00:10',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.PATCH]: [PatchResource.wow],
      },
    }),
    viewMocker({
      duration: '00:10-00:15',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.箭头1],
      },
    }),
    viewMocker({
      duration: '00:15-00:20',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.FEATURED_SOUND_EFFECTS]: [
          FeaturedSoundResource.失败退场,
        ],
      },
    }),
    viewMocker({
      duration: '00:20-00:25',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.强调],
      },
    }),
    viewMocker({
      duration: '00:25-00:30',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.肯定2],
      },
    }),
    viewMocker({
      duration: '00:30-00:35',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.FEATURED_SOUND_EFFECTS]: [FeaturedSoundResource.警报],
      },
    }),
    viewMocker({
      duration: '00:35-00:40',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.wow2],
      },
    }),
    viewMocker({
      duration: '00:40-00:45',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.FEATURED_SOUND_EFFECTS]: [FeaturedSoundResource.芜湖],
      },
    }),
    viewMocker({
      duration: '00:45-00:50',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.哭泣2],
      },
    }),
    viewMocker({
      duration: '00:50-00:55',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.FEATURED_SOUND_EFFECTS]: [
          FeaturedSoundResource['叮叮叮 - 强调'],
        ],
      },
    }),
    viewMocker({
      duration: '00:55-00:60',
      flowerText: '你是我的神嘎嘎暴力',
      resources: {
        [RESOURCES_TYPE.SPECIAL_EFFECTS_GIF]: [SpecialEffectsResource.撒花2],
      },
    }),
  ],
};
