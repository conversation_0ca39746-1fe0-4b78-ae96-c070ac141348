import { BaseScript } from '@/components/ScriptForm/type';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { LANDSCAPE_DEFAULT_SIZE } from '@/pages/Question/constant';

export const pptTitlePage: BaseScript['views'][0] = {
  viewType: '封面',
  viewName: '标题',
  viewContent: '画面内容',
  duration: '01:25-01:36',
  viewMainText: '这里是结尾的画面正文',
  speech: '这里是台词，这里是台词，这里是台词。',
  imageList: [{ url: 'cosurl', width: '300', height: '400' }],
  flowerText: '',
  isInteractive: '否',
  background: '',
};

export const pptEndPage: BaseScript['views'][0] = {
  viewType: '结尾',
  viewName: '感谢您的聆听',
  viewContent: '画面内容',
  duration: '01:25-01:36',
  viewMainText: '这里是结尾的画面正文',
  speech: '这里是台词，这里是台词，这里是台词。',
  imageList: [
    {
      url: 'https://pagedoo.pay.qq.com/material/@platform/31c83c91296a4d2a1c3a16815cbf175f.png',
      width: '300',
      height: '400',
    },
  ],
  flowerText: '',
  isInteractive: '否',
  background: '',
};

export const pptMockScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.PPT,
  size: LANDSCAPE_DEFAULT_SIZE, // 接口返回是[1280, 720]；设计稿是[1920, 1080]
  globalField: {
    title: '主标题',
    subTitle: '副标题',
  },
  views: [
    // 分点概述
    {
      imageList: [
        {
          url: 'https://pagedoo.pay.qq.com/material/@platform/0d42e779a6d9caef84d0f5e305add47f.png',
        },
      ],
      isInteractive: '否',
      speech:
        '这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词，这里是台词。',
      duration: '00:37-00:55',

      flowerText: '',
      viewContent: '这里是画面内容。这里是画面内容。这里是画面内容。',
      viewName: '分点概述',
      viewMainText:
        '这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。这里是正文。',
      viewType: '分点概述',
      background: '',
    },
  ],
};

export const pptOverseasMockScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.PPT,
  size: LANDSCAPE_DEFAULT_SIZE, // 接口返回是[1280, 720]；设计稿是[1920, 1080]
  globalField: {
    title: '',
    subTitle: '',
  },
  views: [
    {
      imageList: [],
      isInteractive: '否',
      speech:
        '最高のゲーム体験をあなたのもとに、Midasbuy公式決済プラットフォームはいつもスムーズな決済体験を提供してます。Midabsuyホームページを検索、Arena Breakout：Infiniteの専用決済ページに進みます。',
      duration: '',

      flowerText: '',
      viewContent: '',
      viewName: '分点概述',
      viewMainText: '',
      viewType: '分点概述',
      background: '',
    },
    {
      imageList: [],
      isInteractive: '否',
      speech:
        '好きなアイテムを選んでいただき、プレーヤー ID を入力し、多様な決済方法でご購入できます。',
      duration: '',

      flowerText: '',
      viewContent: '',
      viewName: '分点概述1',
      viewMainText: '',
      viewType: '分点概述1',
      background: '',
    },
    {
      imageList: [],
      isInteractive: '否',
      speech: 'ご購入後、アイテムはゲーム内に配布されます。',
      duration: '',

      flowerText: '',
      viewContent: '',
      viewName: '分点概述2',
      viewMainText: '',
      viewType: '分点概述2',
      background: '',
    },
    {
      imageList: [],
      isInteractive: '否',
      speech: '詳細をゲーム内でご確認ください。',
      duration: '',

      flowerText: '',
      viewContent: '',
      viewName: '分点概述3',
      viewMainText: '',
      viewType: '分点概述3',
      background: '',
    },
    {
      imageList: [],
      isInteractive: '否',
      speech:
        '通常のゲームチャージ以外に、ゲームCDKをここで引き換えることもできます。交換した後、アイテムはゲーム内に配布されます。詳細をゲーム内でご確認ください。',
      duration: '',

      flowerText: '',
      viewContent: '',
      viewName: '分点概述4',
      viewMainText: '',
      viewType: '分点概述4',
      background: '',
    },
    {
      imageList: [],
      isInteractive: '否',
      speech:
        'Midasbuy公式Xをフォローして、期間限定のAmazonギフトカード、そしてゲームCDKの抽選イベントにも参加できます。',
      duration: '',

      flowerText: '',
      viewContent: '',
      viewName: '分点概述5',
      viewMainText: '',
      viewType: '分点概述5',
      background: '',
    },
  ],
};
