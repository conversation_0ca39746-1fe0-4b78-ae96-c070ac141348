import { PlayConfig } from '@/type/pagedoo';

export type SinglePlugin = (
  conf: PlayConfig,
  context: { cache: Record<string, unknown>; meta?: Record<string, unknown> }
) => unknown | Promise<unknown>;
export type ScriptPlugin = SinglePlugin | SinglePlugin[];

export const newPlayConfig = async (options: {
  plugins: ScriptPlugin[];
  meta?: Record<string, unknown>;
}): Promise<PlayConfig> => {
  const script = getEmptyPlayScript();
  const cache = {};
  for (const plugin of options.plugins.flat(Infinity) as SinglePlugin[])
    await plugin(script, { cache, meta: options.meta });
  return script;
};

const getEmptyPlayScript = (): PlayConfig => {
  return {
    __config: {
      anchorTime: 0,
      position: { x: 0, y: 0 },
      scaleX: 80,
      selectedID: [],
    },
    fragment: [],
    timeline: [],
    version: 1,
  };
};
