import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, IBaseResourcePluginOptions, View } from './type';
import { component, MaterialsAvatar } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { IAnimationConfig } from './scriptLiveShowyTextPlugin';
import { PlayConfig } from '@/type/pagedoo';

interface IPluginOptions
  extends IBaseResourcePluginOptions,
    IBasePluginOptions {
  baseFn: (view: View) => string; // 图片配置
  getAnimationConfig: (view: View) => Partial<IAnimationConfig>; // 获取动效配置
}
export function scriptSpecialEffectsAnimationPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      getAnimationConfig,
      customDataSource,
      getOffset,
    } = pluginOptions;

    const dataSource = customDataSource ?? script.views;
    for (const view of dataSource) {
      const index = dataSource.indexOf(view);
      if (isSkip(view)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: component(
          key,
          `component/${MaterialsAvatar}/SpecialEffectsAnimation`,
          commonStyle,
          {
            _v: 0,
            img: [
              {
                url: baseFn(view) ?? '',
                name: '动画贴纸',
                type: 'img',
                width: 750,
                length: 750,
              },
            ],
            animationConf: {
              ...(getAnimationConfig(view) || {}),
            },
            __component_name: '动画贴纸(SpecialEffectsAnimation)',
            __component_sub_name: '动画贴纸',
            __component_id: 'SpecialEffectsAnimation',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
          }
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset: getOffset?.(view) || offset,
      };
    }
  };
}
