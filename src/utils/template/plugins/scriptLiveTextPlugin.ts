import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, View } from './type';
import { liveText, TSpeed } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';
import { CommonStyle, PagedooFontStyle } from '@tencent/pagedoo-library';

export interface ILiveTextAnimationConfig {
  animation: string;
  speed: TSpeed;
}

interface IPluginOptions
  extends Omit<IBasePluginOptions, 'fontStyle' | 'commonStyle'> {
  commonStyle: CommonStyle | ((view: View) => CommonStyle);
  fontStyle?: PagedooFontStyle | ((view: View) => PagedooFontStyle);
  baseFn: (view: View) => string; // 标题文本
  getAnimationConfig?: (view: View) => ILiveTextAnimationConfig; // 获取动效配置
}
export function scriptLiveTextPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      fontStyle = {},
      getAnimationConfig,
    } = pluginOptions;

    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: liveText(
          key,
          typeof fontStyle === 'object' ? fontStyle : fontStyle(view),
          baseFn(view) ?? '',
          typeof commonStyle === 'object' ? commonStyle : commonStyle(view),
          getAnimationConfig?.(view)
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset,
      };
    }
  };
}
