import { PlayConfig } from '@/type/pagedoo';
import { SinglePlugin } from '..';
import { liveBackgroundMusic } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { IBasePluginOptions, View } from './type';

export interface MuiscConf {
  id: string;
  title: string;
  duration: string;
  url: string;
}

interface IPluginOption extends IBasePluginOptions {
  volumn?: number;
  isLoop?: (view: View) => boolean;
  baseFn: (view: View) => MuiscConf[];
}

export const scriptBackgroundMusicPlugin = (
  pluginOptions: IPluginOption
): SinglePlugin => {
  const {
    script,
    key,
    timelineIndex,
    baseFn,
    commonStyle,
    volumn = 20,
    isLoop = (view) => false,
    duration = () => 0,
    isSkip = () => false,
  } = pluginOptions;

  return (conf: PlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip(view)) continue;

      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: liveBackgroundMusic(
          key,
          volumn,
          baseFn(view),
          isLoop(view),
          commonStyle
        ),
        // 封面&结尾根据音频时长确定duration
        duration: duration(view),
        id: uuid(),
        key,
        offset: 0,
      };
    }
  };
};
