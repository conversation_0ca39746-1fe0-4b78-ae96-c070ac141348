import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, View } from './type';
import { liveBkg } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';

interface IPluginOptions extends IBasePluginOptions {
  baseFn: (view: View) => string; // 背景图url地址
}
export function scriptLiveBkgPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
    } = pluginOptions;

    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: liveBkg(key, baseFn(view) ?? '', '', commonStyle),
        duration: duration(view),
        id: uuid(),
        key,
        offset,
      };
    }
  };
}
