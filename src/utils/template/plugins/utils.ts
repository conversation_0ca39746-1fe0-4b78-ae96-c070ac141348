export function getBgmDuration(duration: string) {
  if (!duration) return 0;
  const [h, m, s] = duration.split(':');
  return (
    (parseInt(h ? h : '0', 10) * 60 * 60 +
      parseInt(m ? m : '0', 10) * 60 +
      parseInt(s ? s : '0', 10)) *
    1000
  );
}

/**
 * 将时间处理为 ms
 * @param time
 * @returns
 */
export const twoTimeToSeconds = (time: string) => {
  const [minutes, seconds] = time.split(':').map(Number);
  return (minutes * 60 + seconds) * 1000;
};

export const timeToSeconds = (time: string) => {
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return (hours * 3600 + minutes * 60 + seconds) * 1000;
};

// 封装一个函数，接受视频 URL 作为参数，返回一个 Promise，解析为视频宽高比
export function getVideoAspectRatio(url: string): Promise<number> {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.src = url;
    video.addEventListener('loadedmetadata', () => {
      resolve(video.videoWidth / video.videoHeight);
    });
  });
}
