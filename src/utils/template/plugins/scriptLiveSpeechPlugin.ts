import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, View } from './type';
import { component, MaterialsAvatar } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';
import { BaseScript } from '@/components/ScriptForm/type';

export interface ISpeechData {
  // answer 纯互动
  // text 纯口播
  // remix 混合 互动插口播
  type: 'answer' | 'text';
  // text时的口播文案内容
  text: string;
  // 互动前序
  prefix?: string;
  // 互动后序
  suffix?: string;
}
export interface ISpeechDrive {
  // 驱动方式
  // virtualman 数字人组件
  // speaker 喇叭播放
  type: 'virtualman' | 'speaker';
}
export type ISeparator = '。' | ', ' | '，' | '！' | '？' | '——' | '：' | '；';
export interface IRemixAnswer {
  // 是否启用
  enabled: boolean;
  // 如果没有回答 是否需要堵塞
  block?: boolean;
  // 分割符
  separator: Array<ISeparator>;
  // 分句长度
  maxLength?: number;
}

interface IPluginOptions extends IBasePluginOptions {
  getSpeechParams: ({ view, script }: { view: View; script: BaseScript }) => {
    speechData: ISpeechData;
    speechDrive: ISpeechDrive;
    remixAnswer?: IRemixAnswer;
  };
  isSkip?: (view: View, index?: number) => boolean;
}

export function scriptLiveSpeechPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      commonStyle,
      getSpeechParams,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
    } = pluginOptions;
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: component(
          key,
          `component/${MaterialsAvatar}/LiveSpeech`,
          commonStyle,
          {
            _v: 0,
            ...(getSpeechParams({ view, script }) || {}),
            height: 0,
            __component_name: '直播间话术(LiveSpeech)',
            __component_sub_name: '直播间话术',
            __component_id: 'LiveSpeech',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
          }
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset,
      };
    }
  };
}
