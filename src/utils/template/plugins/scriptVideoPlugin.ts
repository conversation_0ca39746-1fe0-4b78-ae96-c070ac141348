import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, View } from './type';
import { liveVideo } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';
import { getVideoAspectRatio } from './utils';
import { CommonStyle } from '@tencent/pagedoo-library';
import { BaseScript } from '@/components/ScriptForm/type';
import { MessagePlugin } from 'tdesign-react';

interface IPluginOptions extends IBasePluginOptions {
  setVideoUrl: ({ script }: { script: BaseScript }) => string;
  isSkip?: (view: View, index?: number) => boolean;
  setVideoDuration?: ({
    script,
    view,
  }: {
    view: View;
    script: BaseScript;
  }) => number;
  setVideoHeight?: (size: number) => number;
  setVideoStyle: (size: number) => CommonStyle;
  isLoop?: boolean;
}
export function scriptVideoPlugin(pluginOptions: IPluginOptions): SinglePlugin {
  return async (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      setVideoUrl,
      setVideoDuration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      setVideoHeight = () => 0,
      setVideoStyle,
      isLoop,
    } = pluginOptions;

    const url = setVideoUrl({ script });
    let size;
    if (url) {
      size = await getVideoAspectRatio(url);
    }
    for (const [index, view] of script.views.entries()) {
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: liveVideo(
          key,
          url,
          setVideoStyle(size ?? 1),
          setVideoHeight(size ?? 1),
          isLoop
        ),
        duration: setVideoDuration({ script, view }),
        id: uuid(),
        key,
        offset,
      };
    }
  };
}
