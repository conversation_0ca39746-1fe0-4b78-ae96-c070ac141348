import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, View } from './type';
import { component, MaterialsAvatar } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import type { FontSelectorData } from '@tencent/formily-cms/esm/font-selector/type';
import { PlayConfig } from '@/type/pagedoo';

interface IPluginOptions extends IBasePluginOptions {
  fontData: () => FontSelectorData;
  isSkip?: (view: View, index?: number) => boolean;
  delayTime?: number | ((view: View, index?: number) => number);
}
export function scriptSubtitlePlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      fontData,
      commonStyle,
      duration = () => 0,
      //      if (!['封面', '结尾'].includes(view.画面类型) && view?.台词文案)
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      delayTime,
    } = pluginOptions;

    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: component(
          key,
          `component/${MaterialsAvatar}/VideoSubtitle`,
          commonStyle,
          {
            _v: 1,
            fontConf: fontData(),
            isGlobalFontConf: false,
            height: 0,
            delay: delayTime || 0,
            __component_name: '字幕(VideoSubtitle)',
            __component_sub_name: '字幕',
            __component_id: 'VideoSubtitle',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
          }
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset,
      };
    }
  };
}
