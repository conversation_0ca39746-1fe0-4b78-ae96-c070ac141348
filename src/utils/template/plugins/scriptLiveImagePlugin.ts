import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, IBaseResourcePluginOptions, View } from './type';
import {
  ComponentStyleItem,
  IAnimation,
  liveImage,
} from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';
import { CommonStyle } from '@tencent/pagedoo-library';

interface IPluginOptions
  extends IBaseResourcePluginOptions,
    Omit<IBasePluginOptions, 'commonStyle'> {
  baseFn: (view: View) => string; // 贴片url地址
  getAnimationConfig?: (view: View) => IAnimation; // 获取动效配置

  // 圆角
  corner?: React.CSSProperties['borderRadius'];
  commonStyle: CommonStyle | ((view: View) => CommonStyle);
  // style
  componentStyle?: (view: View) => ComponentStyleItem[];
}
export function scriptLiveImagePlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      corner = 0,
      componentStyle = () => [],
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      getAnimationConfig,
      customDataSource,
      getOffset,
    } = pluginOptions;
    const dataSource = customDataSource ?? script.views;

    for (const view of dataSource) {
      const index = dataSource.indexOf(view);
      if (isSkip(view, index)) continue;

      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: liveImage(
          key,
          baseFn(view) ?? '',
          typeof commonStyle === 'object' ? commonStyle : commonStyle(view),
          getAnimationConfig?.(view),
          corner,
          componentStyle(view)
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset: getOffset?.(view) || offset,
      };
    }
  };
}
