import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, View } from './type';
import { component, MaterialsAvatar } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';
import { CommonStyle } from '@tencent/pagedoo-library';

export interface IKeyLight {
  enabled: boolean; // 是否开启视频流抠图
  tolerance: number; // 容错率
}
export interface VirtualManCommon {
  // 数字人平台
  type: string;
  // 数字人唯一key
  key: string;
  // 抠图容错率 0～1
  // 为0则不抠图 没有则使用旧配置
  chromaKey?: number;
}
// 腾讯云数智人
export interface VirtualManQQ extends VirtualManCommon {
  type: 'qq';
  // 数智人账户
  appkey: string;
  // 数字人key
  key: string;
  // 数字人key是否为数字人资产key（形象key）
  asset?: boolean;
  timbre?: {
    // 音色key 数字人资产key时生效
    key?: string;
    speed?: number;
  };
  // 语速 数字人资产key时生效
}
// 腾讯智影
export interface VirtualManSogou extends VirtualManCommon {
  type: 'sogou';
  // 智影账户
  appid: string;
  // 数字人key
  key: string;
}
export interface VirtualManNoneImage extends VirtualManCommon {
  // 意思是不使用形象 这里作为数字人的一个选项
  type: 'none';
  key: 'none';
}
export type VirtualManConfV1 =
  | VirtualManNoneImage
  | VirtualManQQ
  | VirtualManSogou;
export type LiveSoundPlatform = 'tencent' | 'azure' | 'custom_tts';

export interface ILiveSound {
  // 平台
  platform: LiveSoundPlatform;
  // 音色id
  voiceId: string;
  // 语速
  speed: number;
  // 平台特定扩展配置
  voiceExtendConfig: string;
  driverMode: string;
}

export interface IVoiceConfig {
  currentVoiceItem: ILiveSound;
}
export type baseFnCb<T> = T extends { isWave: true }
  ? T
  : T & {
      type: 'text' | 'comment'; // 口播或交互 isWave为true时无效
      text: string; // 文本内容 口播时有效
      prefix: string; // 前序文本 交互时有效
      suffix: string; // 后序文本 交互时有效
    };

interface IPluginOptions extends Omit<IBasePluginOptions, 'commonStyle'> {
  // 不同环节位置可能不同
  commonStyle: CommonStyle | ((view: View) => CommonStyle);
  baseFn: (view: View) => baseFnCb<
    | {
        liveID: string; // 直播ID
        keyLight: IKeyLight;
        virtualMan: VirtualManConfV1;
        voiceConfig?: IVoiceConfig; // 音色配置
        isGlobalVirtualman: boolean; // 是否运用于所有环节
        isWave: true; // 是否使用语音驱动
        isSegmentTexts: boolean; // 是否使用连续文本播报
        customScript: boolean; // 是否开启自定义脚本
      }
    | {
        liveID: string; // 直播ID
        keyLight: IKeyLight;
        virtualMan: VirtualManConfV1;
        voiceConfig?: IVoiceConfig; // 音色配置
        isGlobalVirtualman: boolean; // 是否运用于所有环节
        isWave: false; // 是否使用语音驱动
        isSegmentTexts: boolean; // 是否使用连续文本播报
        customScript: boolean; // 是否开启自定义脚本
      }
  >;
  isSkip?: (view: View, index?: number) => boolean;
}
export function scriptVirtualManPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
    } = pluginOptions;

    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: component(
          key,
          `component/${MaterialsAvatar}/Virtualman`,
          typeof commonStyle === 'object' ? commonStyle : commonStyle(view),
          {
            _v: 4,
            ...(baseFn(view) || {}),
            __component_name: '数字人(Virtualman)',
            __component_sub_name: '数字人 虚拟人的渲染 背景抠图依赖WebGl2抠图',
            __component_id: 'Virtualman',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
          }
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset,
      };
    }
  };
}
