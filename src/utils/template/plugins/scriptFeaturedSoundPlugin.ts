import { SinglePlugin } from '..';
import { IBasePluginOptions, IBaseResourcePluginOptions, View } from './type';
import { uuid } from '@tencent/midas-util';
import {
  ILiveFeaturedSoundConfig,
  liveFeaturedSound,
} from '@/utils/play-component';
import { timeToSeconds } from './utils';
import { PlayConfig } from '@/type/pagedoo';
import { getAudioDuration } from '../video/utils';

interface IPluginOptions
  extends IBaseResourcePluginOptions,
    IBasePluginOptions {
  /**
   * 音效配置，定义如何获取音频数据
   * @param view
   * @returns
   */
  soundCreator: (view: View) => ILiveFeaturedSoundConfig;
}

export function scriptFeaturedSoundPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return async (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      soundCreator,
      commonStyle,
      duration,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      customDataSource,
      getOffset,
    } = pluginOptions;

    const dataSource = customDataSource ?? script.views;

    for (const view of dataSource) {
      const index = dataSource.indexOf(view);
      if (isSkip(view)) continue;

      const soundData = soundCreator(view);
      let currentDuration = soundData.sound.duration
        ? timeToSeconds(soundData.sound.duration!)
        : 0;

      if (!soundData.sound.duration) {
        const duration = await getAudioDuration(soundData.sound.url);
        currentDuration = duration * 1000;
      }

      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: liveFeaturedSound(key, commonStyle, soundData),
        duration: duration?.(view) || currentDuration,
        id: uuid(),
        key,
        offset: getOffset?.(view) || offset,
      };
    }
  };
}
