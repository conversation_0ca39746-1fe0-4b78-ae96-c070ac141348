import { SinglePlugin } from '@/utils/template';
import { IBasePluginOptions, IBaseResourcePluginOptions, View } from './type';
import { component, MaterialsAvatar } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';

export interface IResource {
  resource_id: string;
  image_address: string;
  resource_name: string;
  audio_address: string;
}
export interface AnimationConfigItem {
  name: string;
  key: string;
  timeConf: {
    duration: number;
    delay: number;
  };
  resource: IResource | null;
}

export interface IAnimationConfig {
  entranceAnimation: AnimationConfigItem | null;
  highlightAnimation: AnimationConfigItem | null;
  exitAnimation: AnimationConfigItem | null;
}

interface IPluginOptions
  extends IBaseResourcePluginOptions,
    IBasePluginOptions {
  baseFn: (view: View) => string; // 文本内容
  getAnimationConfig: (view: View) => Partial<IAnimationConfig>; // 获取动效配置
}
export function scriptLiveShowyTextPlugin(
  pluginOptions: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      fontStyle = {},
      getAnimationConfig,
      customDataSource,
      getOffset,
    } = pluginOptions;
    const dataSource = customDataSource ?? script.views;

    for (const view of dataSource) {
      const index = dataSource.indexOf(view);
      if (isSkip(view, index)) continue;
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration,
        component: component(
          key,
          `component/${MaterialsAvatar}/LiveShowyText`,
          commonStyle,
          {
            _v: 0,
            tabs: 'animationConf',
            fontStyle,
            text: baseFn(view),
            animationConf: {
              ...(getAnimationConfig(view) || {}),
            },
            __component_name: '花字(LiveShowyText)',
            __component_sub_name: '花字',
            __component_id: 'LiveShowyText',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
          }
        ),
        duration: duration(view),
        id: uuid(),
        key,
        offset: getOffset?.(view) || offset,
      };
    }
  };
}
