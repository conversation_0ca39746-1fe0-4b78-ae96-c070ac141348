import { BaseScript } from '@/components/ScriptForm/type';
import { CommonStyle, PagedooFontStyle } from '@tencent/pagedoo-library';

export type View = BaseScript['views'][number];

export type IBasePluginOptions = {
  /**
   * 脚本
   */
  script: BaseScript;
  key: number;
  timelineIndex: number;
  /**
   * 组件样式
   */
  commonStyle: CommonStyle;
  duration?: (view: View) => number;
  offset?: number;
  actualDuration?: number;
  fontStyle?: PagedooFontStyle;

  isSkip?: (view: View, index?: number) => boolean;
};

export type IBaseResourcePluginOptions = {
  customDataSource?: any[];
  getOffset?: (view: any) => number;
};
