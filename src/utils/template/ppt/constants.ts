import { PagedooFontStyle } from '@tencent/pagedoo-library';

// 模板组成
export const PPT_VIEW_TYPES = ['封面', '分点概述', '结尾'];

export enum PPT_VIEW_TYPES_CHINESE_NAME {
  TITLE_PAGE = '封面',
  POINT_STATEMENT = '分点概述',
  END_PAGE = '结尾',
}

// 模板尺寸
export const PPT_Width = 1920;
export const PPT_Height = 1080;

// PPT模板组件枚举
export enum PPT_TEMPLATE_COMPONENTS {
  // 背景
  BACKGROUND = 'background',
  // 数字人
  VIRTUALMAN = 'virtualman',
  // 背景（带透明镂空形状）
  BACKGROUND_WITH_TRANSPARENT_PORTION = 'background-with-transparent-portion',
  // PPT截图背景
  PPT_SCREENSHOT_BKG = 'ppt-screenshot-bkg',
  // PPT截图
  PPT_SCREENSHOT = 'ppt-screenshot',
  // LOGO = 'logo',
  // 封面/结尾大标题
  TITLE_H1 = 'title-h1',
  // 封面/结尾小标题
  TITLE_H2 = 'title-h2',
  // 分点概述-标题
  TITLE_H3 = 'title-h3',
  // 分点概述-小标题
  TITLE_H4 = 'title-h4',
  // 字幕
  SUBTITLE = 'subtitle',
  // 话术
  SPEECH = 'speech',
  // 转场
  TRANSITION = 'transition',
}

// 封面页内容区上内间距
export const TITLE_PAGE_MARGIN_TOP = 340;
// 封面页内容区左内间距
export const TITLE_PAGE_MARGIN_LEFT = 260;
// 封面页标题与小标题相对距离
export const DEFAULT_RELATIVE_DISTANCE_BETWEEN_TITLE_AND_TEXT = 274;

// 封面页标题位于画面右侧时距离PPT左边缘距离
export const PRIMARY_TITLE_PAGE_TITLE_LEFT = 920;
// 封面页小标题距离PPT左边缘距离
export const DEFAULT_TITLE_PAGE_TITLE_H2_TOP =
  TITLE_PAGE_MARGIN_TOP + DEFAULT_RELATIVE_DISTANCE_BETWEEN_TITLE_AND_TEXT; // 614

// 有数字人、无数字人模板字幕TOP
export const DEFAULT_SUBTITLE_TOP = 933;

// 有数字人、无数字人模板字幕LEFT
export const DEFAULT_SUBTITLE_LEFT = 260;

export type TPagedooFontStyle = Omit<
  PagedooFontStyle,
  'fontSize' | 'lineHeight' | 'width' | 'textAlign'
> & {
  fontSize: number;
  lineHeight: number;
  width: number;
  textAlign: 'left' | 'center' | 'right';
};

// 有数字人、无数字人-模板文本样式
export type TNormalFontSelectorData = {
  // 主标题
  titleH1: TPagedooFontStyle;
  // 副标题
  titleH2: TPagedooFontStyle;
  // 字幕
  subtitle: TPagedooFontStyle;
};
// 高级版-模板文本样式
export type TAdvanceFontSelectorData = TNormalFontSelectorData & {
  // 内页主标题(高级版才有)
  titleH3: TPagedooFontStyle;
  // 内页副标题(高级版才有)
  titleH4: TPagedooFontStyle;
};

// 文本样式

export const DEFAULT_VIRTUALMAN_VERSION_TEXTS: TNormalFontSelectorData = {
  // 封面、结尾标题 h1
  titleH1: {
    fontSize: 100,
    lineHeight: 1.24,
    width: 800,
    textAlign: 'left',
  },
  // 封面、结尾二级标题 h2
  titleH2: {
    fontSize: 48,
    lineHeight: 1.5,
    width: 768,
    textAlign: 'left',
  },
  subtitle: {
    fontSize: 48,
    lineHeight: 1.24,
    width: 1100,
    textAlign: 'center',
  },
};
export const DEFAULT_NO_VIRTUALMAN_VERSION_TEXTS: TNormalFontSelectorData = {
  // 封面、结尾标题 h1
  titleH1: {
    fontSize: 100,
    lineHeight: 1.24,
    width: 1200,
    textAlign: 'left',
  },
  // 封面、结尾二级标题 h2
  titleH2: {
    fontSize: 48,
    lineHeight: 1.5,
    width: 1200, // 建议与titleH1保持统一，保证双方“右对齐”时右侧边缘在同一垂直线上
    textAlign: 'left',
  },
  subtitle: {
    fontSize: 48,
    lineHeight: 1.24,
    width: 1440,
    textAlign: 'center',
  },
};
export const DEFAULT_ADVANED_VERSION_TEXTS: TAdvanceFontSelectorData = {
  // 封面、结尾标题 h1
  titleH1: {
    fontSize: 100,
    lineHeight: 1.24,
    width: 800,
    textAlign: 'left',
  },
  // 封面、结尾二级标题 h2
  titleH2: {
    fontSize: 48,
    lineHeight: 1.5,
    width: 768,
    textAlign: 'left',
  },
  // 内页-标题 h3
  titleH3: {
    fontSize: 72,
    lineHeight: 1.24,
    width: 500,
    textAlign: 'left',
  },
  // 内页-小标题 h4
  titleH4: {
    fontSize: 48,
    lineHeight: 1.24,
    width: 500,
    textAlign: 'left',
  },
  subtitle: {
    fontSize: 40,
    lineHeight: 1.24,
    width: 1000,
    textAlign: 'left',
  },
};
