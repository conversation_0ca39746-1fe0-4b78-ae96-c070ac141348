import { BaseScript } from '@/components/ScriptForm/type';
import { commonHiddenStyle } from '@/utils/play-view';
import { cloneDeep, isEmpty } from 'lodash-es';
import { duration, format, initPlugin, livePlugin, setMeta } from '../common';
import { SPEECH_SEPARATOR } from '../constants';
import {
  pptEndPage,
  pptMockScript,
  pptOverseasMockScript,
  pptTitlePage,
} from '../mock/ppt';
import {
  scriptLiveImagePlugin,
  scriptLiveSpeechPlugin,
  scriptLiveTextPlugin,
  scriptVirtualManPlugin,
} from '../plugins';
import { scriptSubtitlePlugin } from '../plugins/scriptSubtitlePlugin';
import { PPT_ID_ENUM } from '../templates';
import { LANGUAGE_SPEED } from '../txcz/constants';
import { genUniqueKey, pptCommonStyle } from '../utils';
import { pptTemplateConfig } from './config';
import {
  PPT_TEMPLATE_COMPONENTS,
  PPT_VIEW_TYPES,
  PPT_VIEW_TYPES_CHINESE_NAME,
  PPT_Width,
} from './constants';

const safeKey = genUniqueKey();

export function initPPTTemplate(script: BaseScript, templateId: string) {
  switch (templateId) {
    // PPT模板-不展示数字人
    case PPT_ID_ENUM.NO_VIRTUALMAN_1:
    case PPT_ID_ENUM.NO_VIRTUALMAN_2:
    case PPT_ID_ENUM.NO_VIRTUALMAN_3:
    case PPT_ID_ENUM.NO_VIRTUALMAN_4:
    case PPT_ID_ENUM.NO_VIRTUALMAN_5:
    case PPT_ID_ENUM.NO_VIRTUALMAN_6:
    case PPT_ID_ENUM.NO_VIRTUALMAN_7:
    case PPT_ID_ENUM.NO_VIRTUALMAN_8:
    case PPT_ID_ENUM.NO_VIRTUALMAN_9: {
      const copyScript = isEmpty(script)
        ? cloneDeep(pptMockScript)
        : cloneDeep(script);
      const scriptData = {
        ...copyScript,
        views: [pptTitlePage, ...copyScript.views, pptEndPage].filter((i) =>
          PPT_VIEW_TYPES.includes(i.viewType)
        ),
      };
      const elements = [
        PPT_TEMPLATE_COMPONENTS.BACKGROUND,
        PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT,
        PPT_TEMPLATE_COMPONENTS.TITLE_H1,
        PPT_TEMPLATE_COMPONENTS.TITLE_H2,
        PPT_TEMPLATE_COMPONENTS.VIRTUALMAN,
        PPT_TEMPLATE_COMPONENTS.SPEECH,
        PPT_TEMPLATE_COMPONENTS.SUBTITLE,
        PPT_TEMPLATE_COMPONENTS.TRANSITION,
      ];
      return [
        initPlugin(scriptData, elements.length),
        // background
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND),
          baseFn: (view) =>
            pptTemplateConfig[templateId].material.background.封面[0].config
              .url,
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // PPT截图
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(
            elements.indexOf(PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT)
          ),
          timelineIndex: elements.indexOf(
            PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT
          ),
          isSkip: (view) => view.viewType !== '分点概述',
          baseFn: (view) => view.imageList?.[0].url || '',
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // 封面、结尾大标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH1?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH1?.config,
          isSkip: (view) => {
            return !['封面', '结尾'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.title || '';
          },
        }),
        // 封面、结尾小标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH2?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH2?.config,
          isSkip: (view) => {
            return !['封面', '结尾'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.subTitle || '';
          },
        }),
        // 数字人
        scriptVirtualManPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN),
          baseFn: (view) => ({
            keyLight: {
              enabled: true,
              tolerance: 0.2433,
            },
            virtualMan: pptTemplateConfig[templateId].virtualman.face,
            voiceConfig: {
              currentVoiceItem: pptTemplateConfig[templateId].virtualman.sound,
            },
            liveID: '',
            isWave: true,
            type: 'text',
            customScript: false,
            isGlobalVirtualman: false,
            isSegmentTexts: false,
            prefix: '',
            suffix: '',
          }),
          commonStyle: (view) => {
            return pptTemplateConfig[templateId].virtualman.position[
              view.viewType as `${PPT_VIEW_TYPES_CHINESE_NAME}`
            ];
          },
        }),
        // 话术
        scriptLiveSpeechPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH),
          commonStyle: commonHiddenStyle,
          // isSkip: (view) => ['封面', '结尾'].includes(view.viewType),
          getSpeechParams: ({ view, script }) => ({
            speechData: {
              type: view?.isInteractive === '是' ? 'answer' : 'text',
              text: ['封面', '结尾'].includes(view.viewType)
                ? '' // 封面&结尾没有台词
                : view?.speech || '',
            },
            speechDrive: { type: 'virtualman' },
            remixAnswer: {
              enabled: true,
              separator: SPEECH_SEPARATOR,
            },
          }),
        }),
        // 字幕
        scriptSubtitlePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE),
          isSkip: (view) => !(view.viewType === '分点概述' && view?.speech),
          fontData: () =>
            pptTemplateConfig[templateId].textStyle.subtitle.config,
          commonStyle:
            pptTemplateConfig[templateId].textStyle.subtitle.position,
          delayTime: 200,
        }),
        // 转场
        // scriptTransitionPlugin(
        //   scriptData,
        //   elements.indexOf(PPT_TEMPLATE_COMPONENTS.TRANSITION),
        //   ['分点概述'],
        //   templateId
        // ),
        duration(2000),
        livePlugin,
        format,
        setMeta({ size: scriptData.size }),
      ];
    }
    // PPT模板-站姿数字人
    case PPT_ID_ENUM.VIRTUALMAN_1:
    case PPT_ID_ENUM.VIRTUALMAN_2:
    case PPT_ID_ENUM.VIRTUALMAN_3:
    case PPT_ID_ENUM.VIRTUALMAN_4:
    case PPT_ID_ENUM.VIRTUALMAN_5:
    case PPT_ID_ENUM.VIRTUALMAN_6:
    case PPT_ID_ENUM.VIRTUALMAN_7:
    case PPT_ID_ENUM.VIRTUALMAN_8:
    case PPT_ID_ENUM.VIRTUALMAN_9: {
      const copyScript = isEmpty(script)
        ? cloneDeep(pptMockScript)
        : cloneDeep(script);
      const scriptData = {
        ...copyScript,
        views: [pptTitlePage, ...copyScript.views, pptEndPage].filter((i) =>
          PPT_VIEW_TYPES.includes(i.viewType)
        ),
      };
      const elements = [
        PPT_TEMPLATE_COMPONENTS.BACKGROUND,
        PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT,
        PPT_TEMPLATE_COMPONENTS.TITLE_H1,
        PPT_TEMPLATE_COMPONENTS.TITLE_H2,
        PPT_TEMPLATE_COMPONENTS.VIRTUALMAN,
        PPT_TEMPLATE_COMPONENTS.SPEECH,
        PPT_TEMPLATE_COMPONENTS.SUBTITLE,
        PPT_TEMPLATE_COMPONENTS.TRANSITION,
      ];
      return [
        initPlugin(scriptData, elements.length),
        // 背景
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND),
          baseFn: (view) =>
            pptTemplateConfig[templateId].material.background.封面[0].config
              .url,
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // PPT截图
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(
            elements.indexOf(PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT)
          ),
          timelineIndex: elements.indexOf(
            PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT
          ),
          isSkip: (view) => view.viewType !== '分点概述',
          baseFn: (view) => view.imageList?.[0].url || '',
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // 封面、结尾大标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH1?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH1?.config,
          isSkip: (view) => {
            return !['封面', '结尾'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.title || '';
          },
        }),
        // 封面、结尾小标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH2?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH2?.config,
          isSkip: (view) => {
            return !['封面', '结尾'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.subTitle || '';
          },
        }),
        // 数字人
        scriptVirtualManPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN),
          baseFn: (view) => ({
            keyLight: {
              enabled: true,
              tolerance: 0.2433,
            },
            virtualMan: pptTemplateConfig[templateId].virtualman.face,
            voiceConfig: {
              currentVoiceItem: pptTemplateConfig[templateId].virtualman.sound,
            },
            liveID: '',
            isWave: true,
            customScript: false,
            type: 'text',
            isGlobalVirtualman: false,
            isSegmentTexts: false,
            prefix: '',
            suffix: '',
          }),
          commonStyle: (view) => {
            return pptTemplateConfig[templateId].virtualman.position[
              view.viewType as `${PPT_VIEW_TYPES_CHINESE_NAME}`
            ];
          },
        }),
        // 话术
        scriptLiveSpeechPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH),
          commonStyle: commonHiddenStyle,
          // isSkip: (view) => ['封面', '结尾'].includes(view.viewType),
          getSpeechParams: ({ view, script }) => ({
            speechData: {
              type: view?.isInteractive === '是' ? 'answer' : 'text',
              text: ['封面', '结尾'].includes(view.viewType)
                ? '' // 封面&结尾没有台词
                : view?.speech || '',
            },
            speechDrive: { type: 'virtualman' },
            remixAnswer: {
              enabled: true,
              separator: SPEECH_SEPARATOR,
            },
          }),
        }),
        // 字幕
        scriptSubtitlePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE),
          isSkip: (view) => !(view.viewType === '分点概述' && view?.speech),
          fontData: () =>
            pptTemplateConfig[templateId].textStyle.subtitle.config,
          commonStyle:
            pptTemplateConfig[templateId].textStyle.subtitle.position,
          delayTime: 2000,
        }),
        // 转场
        // scriptTransitionPlugin(
        //   scriptData,
        //   elements.indexOf(PPT_TEMPLATE_COMPONENTS.TRANSITION),
        //   ['分点概述'],
        //   templateId
        // ),
        duration(2000),
        livePlugin,
        format,
        setMeta({ size: scriptData.size }),
      ];
    }
    // PPT模板-高级版
    case PPT_ID_ENUM.ADVANCED_1:
    case PPT_ID_ENUM.ADVANCED_2:
    case PPT_ID_ENUM.ADVANCED_3:
    case PPT_ID_ENUM.ADVANCED_4:
    case PPT_ID_ENUM.ADVANCED_5:
    case PPT_ID_ENUM.ADVANCED_6:
    case PPT_ID_ENUM.ADVANCED_7:
    case PPT_ID_ENUM.ADVANCED_8:
    case PPT_ID_ENUM.ADVANCED_9: {
      const copyScript = isEmpty(script)
        ? cloneDeep(pptMockScript)
        : cloneDeep(script);
      const scriptData = {
        ...copyScript,
        views: [pptTitlePage, ...copyScript.views, pptEndPage].filter((i) =>
          PPT_VIEW_TYPES.includes(i.viewType)
        ),
      };
      const elements = [
        PPT_TEMPLATE_COMPONENTS.BACKGROUND,
        PPT_TEMPLATE_COMPONENTS.VIRTUALMAN,
        PPT_TEMPLATE_COMPONENTS.SPEECH,
        PPT_TEMPLATE_COMPONENTS.BACKGROUND_WITH_TRANSPARENT_PORTION, // 可选(如果有第2个元素就渲染，如果没有则不需要)
        PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT_BKG,
        PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT,
        PPT_TEMPLATE_COMPONENTS.TITLE_H1,
        PPT_TEMPLATE_COMPONENTS.TITLE_H2,
        PPT_TEMPLATE_COMPONENTS.TITLE_H3,
        PPT_TEMPLATE_COMPONENTS.TITLE_H4,
        PPT_TEMPLATE_COMPONENTS.SUBTITLE,
        PPT_TEMPLATE_COMPONENTS.TRANSITION,
      ];

      return [
        initPlugin(scriptData, elements.length),
        // 背景
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND),
          baseFn: (view) =>
            pptTemplateConfig[templateId].material.background[
              view.viewType as PPT_VIEW_TYPES_CHINESE_NAME
            ]?.[0].config.url || '',
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // 背景(带透明区域)
        scriptLiveImagePlugin({
          script: scriptData,
          key:
            elements.indexOf(
              PPT_TEMPLATE_COMPONENTS.BACKGROUND_WITH_TRANSPARENT_PORTION
            ) + 1,
          timelineIndex: elements.indexOf(
            PPT_TEMPLATE_COMPONENTS.BACKGROUND_WITH_TRANSPARENT_PORTION
          ),
          isSkip: (view) =>
            !pptTemplateConfig[templateId].material.background?.[
              view.viewType as PPT_VIEW_TYPES_CHINESE_NAME
            ]?.[1]?.config?.url,
          baseFn: (view) =>
            pptTemplateConfig[templateId].material.background?.[
              view.viewType as PPT_VIEW_TYPES_CHINESE_NAME
            ]?.[1]?.config?.url || '',
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // PPT背景
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(
            elements.indexOf(PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT_BKG)
          ),
          timelineIndex: elements.indexOf(
            PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT_BKG
          ),
          baseFn: () =>
            pptTemplateConfig[templateId].material.pptBkg?.config.url,
          isSkip: (view) => view.viewType !== '分点概述',
          commonStyle: pptTemplateConfig[templateId].material.pptBkg?.position,
        }),
        // PPT截图
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(
            elements.indexOf(PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT)
          ),
          timelineIndex: elements.indexOf(
            PPT_TEMPLATE_COMPONENTS.PPT_SCREENSHOT
          ),
          baseFn: (view) => view.imageList?.[0].url || '',
          isSkip: (view) => view.viewType !== '分点概述',
          commonStyle:
            pptTemplateConfig[templateId].ppt?.position ??
            pptCommonStyle(0, 0, PPT_Width), // 如果没有定义则全屏展示（兜底）
        }),
        // 封面大标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH1?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH1?.config,
          isSkip: (view) => {
            return !['封面'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.title || '';
          },
        }),
        // 结尾大标题（结尾与封面位置/样式可能会在差异）
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H1),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.endPageTitleH1.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.endPageTitleH1.config,
          isSkip: (view) => {
            return !['结尾'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.title || '';
          },
        }),
        // 封面小标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH2?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.titlePageTitleH2?.config,
          isSkip: (view) => {
            return !['封面'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.subTitle || '';
          },
        }),
        // 结尾小标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H2),
          commonStyle:
            pptTemplateConfig[templateId].textStyle.endPageTitleH2?.position,
          fontStyle:
            pptTemplateConfig[templateId].textStyle.endPageTitleH2?.config,
          isSkip: (view) => {
            return !['结尾'].includes(view.viewType);
          },
          baseFn: () => {
            return scriptData.globalField?.subTitle || '';
          },
        }),
        // 分点概述-标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H3)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H3),
          isSkip: (view) => view.viewType !== '分点概述',
          baseFn: (view) => view.viewName,
          fontStyle: pptTemplateConfig[templateId].textStyle.titleH3.config,
          commonStyle: pptTemplateConfig[templateId].textStyle.titleH3.position,
        }),
        // 分点概述-小标题
        scriptLiveTextPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H4)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.TITLE_H4),
          isSkip: (view) => view.viewType !== '分点概述',
          baseFn: (view) => view.viewContent,
          fontStyle: pptTemplateConfig[templateId].textStyle.titleH4.config,
          commonStyle: pptTemplateConfig[templateId].textStyle.titleH4.position,
        }),
        // 数字人
        scriptVirtualManPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN),
          baseFn: (view) => ({
            keyLight: {
              enabled: true,
              tolerance: 0.2433,
            },
            virtualMan: pptTemplateConfig[templateId].virtualman.face,
            voiceConfig: {
              currentVoiceItem: pptTemplateConfig[templateId].virtualman.sound,
            },
            liveID: '',
            isWave: true,
            customScript: false,
            isGlobalVirtualman: false,
            isSegmentTexts: false,
            type: 'text',
            prefix: '',
            suffix: '',
          }),
          commonStyle: (view) => {
            return pptTemplateConfig[templateId].virtualman.position[
              view.viewType as `${PPT_VIEW_TYPES_CHINESE_NAME}`
            ];
          },
        }),
        // 话术
        scriptLiveSpeechPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH),
          commonStyle: commonHiddenStyle,
          // isSkip: (view) => ['封面', '结尾'].includes(view.viewType),
          getSpeechParams: ({ view, script }) => ({
            speechData: {
              type: view?.isInteractive === '是' ? 'answer' : 'text',
              text: ['封面', '结尾'].includes(view.viewType)
                ? '' // 封面&结尾没有台词
                : view?.speech || '',
            },
            speechDrive: { type: 'virtualman' },
            remixAnswer: {
              enabled: true,
              separator: SPEECH_SEPARATOR,
            },
          }),
        }),
        // 字幕
        scriptSubtitlePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE),
          isSkip: (view) => !(view.viewType === '分点概述' && view?.speech),
          fontData: () =>
            pptTemplateConfig[templateId].textStyle.subtitle.config,
          commonStyle:
            pptTemplateConfig[templateId].textStyle.subtitle.position,
          delayTime: 2000,
        }),
        // 转场
        // scriptTransitionPlugin(
        //   scriptData,
        //   elements.indexOf(PPT_TEMPLATE_COMPONENTS.TRANSITION),
        //   ['分点概述'],
        //   templateId
        // ),
        duration(2000),
        livePlugin,
        format,
        setMeta({ size: scriptData.size }),
      ];
    }
    // 海外模版
    case PPT_ID_ENUM.ADVANCED_10: {
      const copyScript = isEmpty(script)
        ? cloneDeep(pptOverseasMockScript)
        : cloneDeep(script);
      const scriptData = {
        ...copyScript,
        views: [...copyScript.views],
      };
      const totalDuration = scriptData.views.reduce(
        (pre, cur) => pre + Number(cur.speech.length * LANGUAGE_SPEED || 0),
        0
      );
      const elements = [
        PPT_TEMPLATE_COMPONENTS.BACKGROUND,
        PPT_TEMPLATE_COMPONENTS.VIRTUALMAN,
        PPT_TEMPLATE_COMPONENTS.SPEECH,
        PPT_TEMPLATE_COMPONENTS.BACKGROUND_WITH_TRANSPARENT_PORTION, // 可选(如果有第2个元素就渲染，如果没有则不需要)
        PPT_TEMPLATE_COMPONENTS.SUBTITLE,
      ];
      return [
        initPlugin(scriptData, elements.length),
        // 背景
        scriptLiveImagePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.BACKGROUND),
          baseFn: (view) =>
            pptTemplateConfig[templateId].material.background[
              view.viewType as PPT_VIEW_TYPES_CHINESE_NAME
            ]?.[0].config.url || '',
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // 背景(带透明区域)
        scriptLiveImagePlugin({
          script: scriptData,
          key:
            elements.indexOf(
              PPT_TEMPLATE_COMPONENTS.BACKGROUND_WITH_TRANSPARENT_PORTION
            ) + 1,
          timelineIndex: elements.indexOf(
            PPT_TEMPLATE_COMPONENTS.BACKGROUND_WITH_TRANSPARENT_PORTION
          ),
          isSkip: (view) =>
            !pptTemplateConfig[templateId].material.background?.[
              view.viewType as PPT_VIEW_TYPES_CHINESE_NAME
            ]?.[1]?.config?.url,
          baseFn: (view) =>
            pptTemplateConfig[templateId].material.background?.[
              view.viewType as PPT_VIEW_TYPES_CHINESE_NAME
            ]?.[1]?.config?.url || '',
          commonStyle: pptCommonStyle(0, 0, PPT_Width),
        }),
        // 数字人
        scriptVirtualManPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.VIRTUALMAN),
          isSkip: (_, index) => {
            return index !== 0;
          },
          baseFn: () => ({
            keyLight: {
              enabled: true,
              tolerance: 0.2433,
            },
            virtualMan: pptTemplateConfig[templateId].virtualman.face,
            voiceConfig: {
              currentVoiceItem: pptTemplateConfig[templateId].virtualman.sound,
            },
            liveID: '',
            isWave: true,
            customScript: false,
            isGlobalVirtualman: false,
            isSegmentTexts: false,
            type: 'text',
            prefix: '',
            suffix: '',
          }),
          commonStyle: () => {
            return pptTemplateConfig[templateId].virtualman.position[
              '分点概述'
            ];
          },
          duration: () => totalDuration,
        }),
        // 话术
        scriptLiveSpeechPlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SPEECH),
          commonStyle: commonHiddenStyle,
          getSpeechParams: ({ view }) => ({
            speechData: {
              type: view?.isInteractive === '是' ? 'answer' : 'text',
              text: ['封面', '结尾'].includes(view.viewType)
                ? '' // 封面&结尾没有台词
                : view?.speech || '',
            },
            speechDrive: { type: 'virtualman' },
            remixAnswer: {
              enabled: true,
              separator: SPEECH_SEPARATOR,
            },
          }),
        }),
        // // 字幕
        scriptSubtitlePlugin({
          script: scriptData,
          key: safeKey(elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE)),
          timelineIndex: elements.indexOf(PPT_TEMPLATE_COMPONENTS.SUBTITLE),
          isSkip: (view) =>
            !(view.viewType.indexOf('分点概述') > -1 && view?.speech),
          fontData: () =>
            pptTemplateConfig[templateId].textStyle.subtitle.config,
          commonStyle:
            pptTemplateConfig[templateId].textStyle.subtitle.position,
          delayTime: (_, index) => (!index ? 2000 : 0),
        }),
        duration(2000),
        livePlugin,
        format,
        setMeta({ size: scriptData.size }),
      ];
    }
    default:
      throw new Error('unknown template ID');
  }
}
