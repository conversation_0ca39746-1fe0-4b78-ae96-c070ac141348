import { PlayConfig } from '@/type/pagedoo';
import {
  duration,
  format,
  getTimelineIndex,
  getTimelineNodeKey,
  initPlugin,
  setMeta,
} from './common';
import { SPEECH_SEPARATOR, VIRTUALMAN_OPTIONS } from './constants';
import { SinglePlugin } from '.';
import { getParams, uuid } from '@tencent/midas-util';
import { component, MaterialsAvatar } from '../play-component';
import { allViews } from '../play-view';
import { isEmpty } from 'lodash-es';
import {
  LANDSCAPE_DEFAULT_SIZE,
  TEMPLATE_ORIENTATION,
  TEXT_DEFAULT_SIZE,
} from '@/pages/Question/constant';
import { BaseScript } from '@/components/ScriptForm/type';

enum BLANK_TEMPLATE_COMPONENTS {
  LiveSpeech = 'LiveSpeech',
  Virtualman = 'Virtualman',
}

// 空白模板逻辑，视频/直播共用逻辑，暂未区分
// VIDEO_TEMPLATES.blank
// LIVE_TEMPLATES.blank
export function initBlankNoScriptTemplate(script: BaseScript) {
  const { deviceSize } = getParams();
  const paramDeviceSize = {
    [TEMPLATE_ORIENTATION.LANDSCAPE]: LANDSCAPE_DEFAULT_SIZE,
    [TEMPLATE_ORIENTATION.PORTRAIT]: TEXT_DEFAULT_SIZE,
  }[deviceSize];
  return [
    initPlugin(script, 0),
    format,
    setMeta({ size: script.size ?? paramDeviceSize }),
  ];
}

let timeLineIndex = -1;
const blankScriptTemplate = (script: BaseScript): SinglePlugin => {
  return (conf: PlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      let key = getTimelineNodeKey();
      // 话术
      timeLineIndex = getTimelineIndex();
      conf.timeline[timeLineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: component(
          key,
          `component/${MaterialsAvatar}/${BLANK_TEMPLATE_COMPONENTS.LiveSpeech}`,
          allViews.hidden,
          {
            _v: 0,
            speechData: {
              type: view?.isInteractive === '是' ? 'answer' : 'text',
              text: view?.speech || '',
            },
            speechDrive: { type: 'virtualman' },
            remixAnswer: {
              enabled: true,
              separator: SPEECH_SEPARATOR,
            },
            height: 0,
            __component_name: '直播间话术(LiveSpeech)',
            __component_sub_name: '直播间话术',
            __component_id: 'LiveSpeech',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
          }
        ),
        duration: 0,
        id: uuid(),
        key,
        offset: 0,
      };

      // 数字人
      key = getTimelineNodeKey(key);
      timeLineIndex = getTimelineIndex(timeLineIndex);
      conf.timeline[timeLineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: component(
          key,
          `component/${MaterialsAvatar}/${BLANK_TEMPLATE_COMPONENTS.Virtualman}`,
          allViews.hidden,
          {
            _v: 4,
            keyLight: {
              enabled: true,
              tolerance: 0.2433,
            },
            virtualMan: VIRTUALMAN_OPTIONS.none,
            voiceConfig: {
              currentVoiceItem: {
                platform: 'azure',
                speed: 1.2,
                voiceExtendConfig:
                  '{"ShortName":"zh-CN-XiaochenNeural","Gender":"Female","Locale":"zh-CN"}',
                voiceId: 'zh-CN-XiaochenNeural',
              },
            },
            liveID: '',
            isWave: true,
            customScript: false,
            type: 'text',
            __component_name: '数字人(Virtualman)',
            __component_sub_name: '数字人 虚拟人的渲染 背景抠图依赖WebGl2抠图',
            __component_id: 'Virtualman',
            __component_mutex_data: '',
            __pagedoo_i18n: {},
            prefix: '',
            suffix: '',
          }
        ),
        duration: 0,
        id: uuid(),
        key,
        offset: 0,
      };
    }
  };
};

// 只有话术+音色组件，视频/直播共用逻辑，暂未区分
// VIDEO_TEMPLATES.default
// LIVE_TEMPLATES.default
export function initBlankScriptTemplate(script: BaseScript) {
  const components = [
    BLANK_TEMPLATE_COMPONENTS.LiveSpeech,
    BLANK_TEMPLATE_COMPONENTS.Virtualman,
  ];

  const copyScript: BaseScript = isEmpty(script)
    ? {
        backgroundImage: [
          {
            url: '',
          },
        ],
        type: 'text',
        size: TEXT_DEFAULT_SIZE,
        views: [
          {
            viewName: '画面名称',
            viewType: '近景',
            viewContent: '画面内容',
            viewMainText: '',
            duration: '00:21-01:05',
            speech: '请在这里填入你的台词文案。请在这里填入你的台词文案。',
            isInteractive: '否',
            flowerText: '填写画面亮点画面亮点',
          },
        ],
      }
    : script;

  return [
    initPlugin(copyScript, components.length),
    blankScriptTemplate(copyScript),
    duration(),
    format,
    setMeta({ size: script.size }),
  ];
}
