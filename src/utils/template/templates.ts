import {
  LANDSCAPE_DEFAULT_SIZE,
  TEXT_DEFAULT_SIZE,
} from '@/pages/Question/constant';
import { TemplateContentType, SceneSpecification, TemplateMap } from './type';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { createVideoTemplate } from './video/utils';
import { ECOMMERCE_TEMPLATE_ENUM } from './live/constants';

export enum ECOMMERCE_ID_ENUM {
  // 电商模板
  // 国风祥云
  ECOMMERCE_CHINESE_TRADITIONAL_STYLE = 'C5BA3B90-6493-48EB-8632-5388F68AADED',
  // 清凉夏日
  ECOMMERCE_SUMMER = '753F6DEF-2CD5-46F0-B39D-815FF3F2074D',
}

export enum DOC_ID_ENUM {
  // 文章（DOC）
  // 1、学习中心（目前只有1个模板）
  LEARNING_CENTER = 'A19B621D-CB11-4847-A2E3-F05D2E67865C',
}

export enum PPT_ID_ENUM {
  // PPT
  // 通用-浅渐变，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_1 = 'A2C485C6-D74A-478D-92A1-B9BCC48302C9',
  NO_VIRTUALMAN_1 = '33AECCD9-114F-41E2-A3AA-8C0541D28593',
  ADVANCED_1 = 'E8304BC1-0A66-4B09-8A35-69F93274C94A',

  // 通用-中渐变，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_2 = '7B851367-EE40-4B1C-83FC-518736B046B2',
  NO_VIRTUALMAN_2 = 'BAB36BB7-7DC2-4613-82A0-B16599BD0B83',
  ADVANCED_2 = '1027AF3D-D9CA-4DE1-8DDA-C9BDEDEBB91F',

  // 通用-浅流体，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_3 = '8CE25805-65BB-4972-A30E-FE1CD57EAEBB',
  NO_VIRTUALMAN_3 = 'E5DE3910-7827-4394-B344-D21D7D91CD21',
  ADVANCED_3 = '435D1183-AC25-480F-8760-298E99FB5DCC',

  // 通用-深渐变，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_4 = 'B6101320-46E5-4714-A151-F89AC4F928C8',
  NO_VIRTUALMAN_4 = '7E6BE3BF-55FA-4A4A-A07C-A7E334C1400B',
  ADVANCED_4 = '95A9BB86-330C-4460-823C-26C52454E6C7',

  // 通用-黑金，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_5 = '0637EBF3-3819-4FF8-BC4B-0ECA863BEB35',
  NO_VIRTUALMAN_5 = '76ED9968-B284-4F8D-9849-668E05F0A02F',
  ADVANCED_5 = 'BFD146E6-ECB6-43D7-96DE-DD625B589BB9',

  // 通用-商务蓝，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_6 = '4321D675-C15E-4652-80CA-A14B796601CA',
  NO_VIRTUALMAN_6 = 'A1193EA2-D47D-40C0-8A68-26628477A8C4',
  ADVANCED_6 = '55B1B07C-5D22-46CA-A98F-4E43B06841E2',

  // 通用-磨砂，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_7 = '2A628B66-0C3C-4764-9FB1-5C6B67DB1F8B',
  NO_VIRTUALMAN_7 = '0A78915D-71F5-41BD-842F-5BF806911875',
  ADVANCED_7 = '0D939A0B-137B-4251-BCFC-980079CEE1B5',

  // 通用-可爱装饰，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_8 = '60E9FB93-9400-4474-9DC6-BC37FBE6F715',
  NO_VIRTUALMAN_8 = '4D158B8B-93DE-49CC-9A63-B435E610BE4A',
  ADVANCED_8 = 'D5AF6182-C1E6-4B89-8740-0D5654227379',

  // 通用-金属流，站姿数字人、不展示数字人、高级版
  VIRTUALMAN_9 = 'A1D372FD-24CC-47BB-95B3-5051C8CBEE6',
  NO_VIRTUALMAN_9 = '64BF764E-F264-4E64-98B4-8456B4B4283F',
  ADVANCED_9 = '39572917-72C0-485B-ABE7-B76CB7F53F32',

  // 全屏横版图片模板（高级）
  ADVANCED_10 = 'B8C86F72-EDC3-45FA-8C7C-577966F566E2',
}

// 视频id
export enum VIDEO_UNDERSTANDING_ID_ENUM {
  // 横屏视频模板
  /**
   * 全屏横板视频模板（站姿数字人）
   *    */
  GAME_HORIZONTAL_SCREEN_WITH_STAND_NUMBER_PERSON = '748E1AA6-D566-4013-8A54-B1F3148240A9',
  /**
   * 全屏视频含贴片有字幕
   */
  GAME_NARRATION_PORTRAIT = 'F893BC27-9C2B-A898-F6C7-06F6A2277A81',
  /**
   * 全屏视频含贴片无字幕
   */
  GAME_NO_NARRATION_PORTRAIT = '30A505CB-B677-3785-78BE-56B912D29288',

  // 横版视频模板 --- 丰富的

  /**
   * 清新&可爱  全部模版
   */
  // 无数字人&无字幕
  GAME_FRESHANDCUTE_TWILL_NO_NARRATION = '03E4392E-EA7B-C6CF-D1FE-5BA36ACD9415', // 清新斜条纹模板
  GAME_FRESHANDCUTE_PLAID_NO_NARRATION = '27ACAFB8-0BFF-7F6D-723C-C8EB6395240B', // 清新栅格纹模板
  GAME_FRESHANDCUTE_CLOUDS_NO_NARRATION = 'B2910D73-9573-2581-C544-71EB9DBEB1A5', // 清新云朵模版
  GAME_FRESHANDCUTE_FLOWERS_NO_NARRATION = '81FE2C49-F8C2-E90C-CB38-3ECDB0F4B9EF', // 清新花朵模版
  // 无数字人&有字幕
  GAME_FRESHANDCUTE_TWILL_NARRATION = 'D108B485-8B23-4436-B40B-B99B4B2B6308', // 清新斜条纹模板
  GAME_FRESHANDCUTE_PLAID_NARRATION = '85B6C637-E3A8-8483-18C6-D7AA85FFEC00', // 清新栅格纹模板
  GAME_FRESHANDCUTE_CLOUDS_NARRATION = 'C8F941EC-D402-8D66-0F8F-9F0659FB9693', // 清新云朵模版
  GAME_FRESHANDCUTE_FLOWERS_NARRATION = 'A49CA53C-2F75-C629-DC8F-C85308FFD647', // 清新花朵模版
  // 聚焦数字人&有字幕
  GAME_FRESHANDCUTE_TWILL_WITH_NUMBER_PERSON = 'A9B9B965-9711-432F-9573-3D561D63426F', // 清新斜条纹模板
  GAME_FRESHANDCUTE_PLAID_WITH_NUMBER_PERSON = '2D79F695-A6D9-1603-EFC0-DE153A713AED', // 清新栅格纹模板
  GAME_FRESHANDCUTE_CLOUDS_WITH_NUMBER_PERSON = '4973CC38-DBC3-9F29-0C77-92C7649B36B7', // 清新云朵模版
  GAME_FRESHANDCUTE_FLOWERS_WITH_NUMBER_PERSON = '7E8B02E2-BFCD-FCBC-F8F0-0318D519E7A5', // 清新花朵模版

  /**
   * 漫画&二次元  全部模版
   */
  // 无数字人&无字幕
  GAME_FRESHANDCUTE_COMICS1_NO_NARRATION = '0AE11421-97D9-C0BB-8933-C131F3E2380B',
  GAME_FRESHANDCUTE_COMICS2_NO_NARRATION = '96500693-578E-D093-1555-F14FD8A5BACC',
  GAME_FRESHANDCUTE_COMICS3_NO_NARRATION = '8A5B73FA-A71D-44F8-2CB4-BB7D25A0E4BB',
  GAME_FRESHANDCUTE_COMICS4_NO_NARRATION = '82964E16-6AC1-9A51-E7AD-B85B6B386D9F',
  // 无数字人&有字幕
  GAME_FRESHANDCUTE_COMICS1_NARRATION = '554CB737-8993-6CAC-C988-99DA12529629',
  GAME_FRESHANDCUTE_COMICS2_NARRATION = 'E7DF805B-2C7A-FB7A-69A9-82738EA5726C',
  GAME_FRESHANDCUTE_COMICS3_NARRATION = 'EB62CC95-433E-EAF5-F179-40BABFD0F509',
  GAME_FRESHANDCUTE_COMICS4_NARRATION = '496CDEE2-EEDC-283B-8C1E-A22BB31F3A5D',
  // 聚焦数字人&有字幕
  GAME_FRESHANDCUTE_COMICS1_WITH_NUMBER_PERSON = 'A6D38263-0750-F6CB-BD2B-2F5D85CA87CF',
  GAME_FRESHANDCUTE_COMICS2_WITH_NUMBER_PERSON = '9BA2F56C-29E3-2426-913D-97D24EFC94EC',
  GAME_FRESHANDCUTE_COMICS3_WITH_NUMBER_PERSON = '19C42DFF-E30A-0834-1E19-7C247226D444',
  GAME_FRESHANDCUTE_COMICS4_WITH_NUMBER_PERSON = '8BC5CD2C-0FF8-B36C-C672-04B83EF3D0EE',
  // 站姿数字人&有字幕
  GAME_FRESHANDCUTE_COMICS1_WITH_STAND_NUMBER_PERSON = '420297FE-E984-F77D-7333-42C91F3CF5EB',
  GAME_FRESHANDCUTE_COMICS2_WITH_STAND_NUMBER_PERSON = '937D3BE4-FE06-C2EF-32CA-4C10428F733D',
  GAME_FRESHANDCUTE_COMICS3_WITH_STAND_NUMBER_PERSON = '556F272D-9FD1-D2E3-1C54-DA2EBCFAB6A6',
  GAME_FRESHANDCUTE_COMICS4_WITH_STAND_NUMBER_PERSON = 'E0E7392F-03FF-7254-A231-3ECE34F17BE0',
  /**
   * 棋牌  全部模版
   */
  // 无数字人&无字幕
  GAME_FRESHANDCUTE_CHESS_CARDS1_NO_NARRATION = 'FAB8A5B8-F67F-3C43-91AB-2907E9F1C6F5',
  GAME_FRESHANDCUTE_CHESS_CARDS2_NO_NARRATION = '33B41639-0178-08BD-2AC0-F9496EE87C2E',
  GAME_FRESHANDCUTE_CHESS_CARDS3_NO_NARRATION = 'AAAB6BE1-937B-3A6F-FEF8-FE0EB1B50CBD',
  GAME_FRESHANDCUTE_CHESS_CARDS4_NO_NARRATION = '1C881417-0630-1357-727F-BE68E58C8EDF',
  // 无数字人&有字幕
  GAME_FRESHANDCUTE_CHESS_CARDS1_NARRATION = '204053D4-C03D-2CF3-9A94-740A818738E6',
  GAME_FRESHANDCUTE_CHESS_CARDS2_NARRATION = '7CBC0C90-FC70-3A60-FE73-9296CE366A79',
  GAME_FRESHANDCUTE_CHESS_CARDS3_NARRATION = '32530EE2-43D4-D541-481B-11A82410CD2A',
  GAME_FRESHANDCUTE_CHESS_CARDS4_NARRATION = 'CB05DD08-7179-394E-DD1C-A937559E14D4',
  // 聚焦数字人&有字幕
  GAME_FRESHANDCUTE_CHESS_CARDS1_WITH_NUMBER_PERSON = 'D64D830B-F6B1-6BA4-250F-4D7374329F7E',
  GAME_FRESHANDCUTE_CHESS_CARDS2_WITH_NUMBER_PERSON = '10DD99AC-D0EF-2944-44D6-F1514A53DC98',
  GAME_FRESHANDCUTE_CHESS_CARDS3_WITH_NUMBER_PERSON = 'C2B1EECF-387D-CBD6-9C43-4B35DB9F63C1',
  GAME_FRESHANDCUTE_CHESS_CARDS4_WITH_NUMBER_PERSON = '685965C7-7412-BC5F-FC18-D62E9F3EA343',
  // 站姿数字人&有字幕
  GAME_FRESHANDCUTE_CHESS_CARDS1_WITH_STAND_NUMBER_PERSON = 'F36B1FCE-9D3C-4DB3-A8B9-9F036E75FCA1',
  GAME_FRESHANDCUTE_CHESS_CARDS2_WITH_STAND_NUMBER_PERSON = '514E02C6-FF72-C4AC-3995-31F2D99E1493',
  GAME_FRESHANDCUTE_CHESS_CARDS3_WITH_STAND_NUMBER_PERSON = 'C1F78AC3-4390-4014-0937-860AE5EF1034',
  GAME_FRESHANDCUTE_CHESS_CARDS4_WITH_STAND_NUMBER_PERSON = 'ABEE95FF-D5F6-607D-D531-32A28D43AD10',
  /**
   * 商务  全部模版
   */
  // 无数字人&无字幕
  GAME_FRESHANDCUTE_BUSINESS1_NO_NARRATION = '90114334-C49A-D42F-DD17-06F86109421A',
  GAME_FRESHANDCUTE_BUSINESS2_NO_NARRATION = '79E98B24-1283-87B7-26CB-CA5316E88CA5',
  GAME_FRESHANDCUTE_BUSINESS3_NO_NARRATION = '4DF5774A-515D-F911-949E-FB3A0626A430',
  // 无数字人&有字幕
  GAME_FRESHANDCUTE_BUSINESS1_NARRATION = '5FC710C4-4F84-1737-6A5D-A7EFE04864FD',
  GAME_FRESHANDCUTE_BUSINESS2_NARRATION = '2C7E0DD4-8A2B-063F-D1B2-C190E4AFCF2F',
  GAME_FRESHANDCUTE_BUSINESS3_NARRATION = 'BE6FDC59-AD07-92E0-8BE7-D0FEFC664867',
  // 聚焦数字人&有字幕
  GAME_FRESHANDCUTE_BUSINESS1_WITH_NUMBER_PERSON = '320FAB77-C8A7-300F-85DB-FC7319DDFB0A',
  GAME_FRESHANDCUTE_BUSINESS2_WITH_NUMBER_PERSON = 'B047AD2E-3700-B457-2B98-3C2F84902736',
  GAME_FRESHANDCUTE_BUSINESS3_WITH_NUMBER_PERSON = '06F1D96D-808F-918B-C4E3-2E144D3F4DA9',
  // 站姿数字人&有字幕
  GAME_FRESHANDCUTE_BUSINESS1_WITH_STAND_NUMBER_PERSON = '71B09064-457F-32E4-36B3-DB0661E066E2',
  GAME_FRESHANDCUTE_BUSINESS2_WITH_STAND_NUMBER_PERSON = '7678A69A-D9DE-FCD7-800A-57940550993F',
  GAME_FRESHANDCUTE_BUSINESS3_WITH_STAND_NUMBER_PERSON = '7709D803-DFEF-2CAA-DF7F-A1BA7ED8DCC3',
  /**
   * 高级 战斗游戏
   */
  GAME_CIRCLE_GAME = '7FEE9809-8EC9-B37A-A254-91611BADBF00',
  GAME_CIRCLE_GAME_1 = 'F424B4BA-BB24-F354-36EB-4B11224B09BD',
  GAME_CIRCLE_GAME_2 = '6B8910EC-BF9F-11AA-DACB-6CE5EE7EB5AA',
  GAME_CIRCLE_GAME_3 = '30B05DD8-F54A-0C6E-CBE4-4445D9D199AC',

  // 站姿
  /**
   * 站姿数字人 国风类型 4 个
   */
  GAME_NPS_CN = '369A3B61-9B10-9D25-EE9D-D9E8FB63F78F',
  GAME_NPS_CN_1 = 'BD8F96DE-F0C1-13F3-780C-A58C7C34AD9D',
  GAME_NPS_CN_2 = 'C51BB3CF-8072-63FB-A956-B31E30F206ED',
  GAME_NPS_CN_3 = 'B874D988-2E51-B841-9638-88ECB6933959',
  /**
   * 国风 - 无数字人 有旁白
   */
  GAME_NL_CN = '3A8F5E8C-6D14-802A-B9F2-CCB2AABB4E75',
  GAME_NL_CN_1 = 'BA93F7FC-05A0-131C-FFEC-FE16FAC77A33',
  GAME_NL_CN_2 = 'E3A187E2-319B-0A9E-83FE-B758E89677AF',
  GAME_NL_CN_3 = 'F458272F-8802-1DF4-2D02-DDAC3FE095A3',
  // 国风 - 无数字人 无旁白

  GAME_NNL_CN = 'AD76411D-7BB4-F3F6-C14C-D9CD1928946B',
  GAME_NNL_CN_1 = '57296765-F54B-0B96-63C3-71F9026F65EC',
  GAME_NNL_CN_2 = 'DF58A2F3-D44E-47BE-BEE3-3C1164F10CCB',
  GAME_NNL_CN_3 = '05CCE60A-FE96-7FF0-2FF7-36EEC9DB88BD',

  // 战斗游戏
  /**
   * 站姿数字人，战斗游戏 4 个模板
   */
  GAME_NPS_GAME = '720E39BE-76FB-1AFF-228B-E5C0AFDFAFC7',
  GAME_NPS_GAME_1 = 'D6433237-C4E0-E2BE-3E2A-A2296A148B44',
  GAME_NPS_GAME_2 = '17F6D787-CAB6-5989-5B96-2220A56390E9',
  GAME_NPS_GAME_3 = 'E98AB21C-0054-AE48-9AB0-A054543FFB79', // 与GAME_NPS

  // 站姿数字人 - 清新&可爱
  GAME_FRESHANDCUTE_TWILL_WITH_STAND_NUMBER_PERSON = '11F5AE07-A931-8AFF-6CB1-1208A989DB4A', // 清新斜条纹模板
  GAME_FRESHANDCUTE_PLAID_WITH_STAND_NUMBER_PERSON = 'AAB49D25-0B47-8DD2-1C27-338ACF02D0C6', // 清新栅格纹模板
  GAME_FRESHANDCUTE_CLOUDS_WITH_STAND_NUMBER_PERSON = '67ADBD51-4351-05E3-EADC-F26E37332B81', // 清新云朵模版
  GAME_FRESHANDCUTE_FLOWERS_WITH_STAND_NUMBER_PERSON = '0329AE37-2914-BEEB-208B-C75895F1808E', // 清新花朵模版
  /**
   * 站姿数字人，点线面
   */
  GAME_NPS_DOT = '7D692577-835A-B51A-7488-06C1EB418AD2',
  GAME_NPS_DOT_1 = '2131ABB2-38DC-92E1-D2EB-0AB4326AE208',
  GAME_NPS_DOT_2 = 'BB78FE2E-FC76-9451-80B8-B130E3B5E428',
  GAME_NPS_DOT_3 = 'E5AED122-47B5-1220-0682-3D7D048D5BDC',

  /**
   * 站姿数字人，休闲模板
   */

  GAME_NPS_LEISURE = '83327AEE-2962-603F-2C6B-07721D3A09F1',
  GAME_NPS_LEISURE_1 = 'F1B05AB8-8B13-62B7-16BA-EA1E9BE269FD',
  GAME_NPS_LEISURE_2 = 'DA098406-6B15-0628-7F4C-50B2E0BA539E',
  GAME_NPS_LEISURE_3 = 'B1CB56FF-FCD4-E481-DC7A-3AFC6CDE7A11',
}

// 同时支持直播/视频的模板分别创建1个模板ID
export const VIDEO_TEMPLATES: TemplateMap = {
  // 当前（0718）视频/直播的“空白模板”（含不带脚本、带脚本）均为识别URL参数区分不同场景，blank和default下的模板(ID)实际上暂时不会用到
  // 但为了方便后续扩展，这里还是保留了
  blank: {
    // 空白
    'C9C048D8-7F05-48CC-AFF2-4E9B65F0F197': {
      contentType: TemplateContentType.Video,
      name: '空白模板',
    },
  },
  default: {
    // 话术+音色
    '34E6F17F-5314-4456-9017-C7695BFED7C2': {
      contentType: TemplateContentType.Video,
      name: '基础模板',
      description: '只包含话术、音色组件',
    },
  },
  [ORIGIN_TYPE.DOC]: {
    // 学习中心-橙色
    [DOC_ID_ENUM.LEARNING_CENTER]: {
      contentType: TemplateContentType.Video,
      scenarios: 1, // 规则说明
      name: '学习中心橙色模版',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
  },
  [ORIGIN_TYPE.PPT]: {
    // PPT模板
    [PPT_ID_ENUM.VIRTUALMAN_1]: {
      contentType: TemplateContentType.Video,
      name: '蓝粉渐变PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_2]: {
      contentType: TemplateContentType.Video,
      name: '蓝色球状渐变PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_3]: {
      contentType: TemplateContentType.Video,
      name: '浅流体PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_4]: {
      contentType: TemplateContentType.Video,
      name: '深渐变PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_5]: {
      contentType: TemplateContentType.Video,
      name: '黑金PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_6]: {
      contentType: TemplateContentType.Video,
      name: '商务蓝PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_7]: {
      contentType: TemplateContentType.Video,
      name: '磨砂PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_8]: {
      contentType: TemplateContentType.Video,
      name: '可爱PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.VIRTUALMAN_9]: {
      contentType: TemplateContentType.Video,
      name: '金属流PPT课程（站姿数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_1]: {
      contentType: TemplateContentType.Video,
      name: '蓝粉渐变PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_2]: {
      contentType: TemplateContentType.Video,
      name: '蓝色球状渐变PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_3]: {
      contentType: TemplateContentType.Video,
      name: '浅流体PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_4]: {
      contentType: TemplateContentType.Video,
      name: '深渐变PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_5]: {
      contentType: TemplateContentType.Video,
      name: '黑金PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_6]: {
      contentType: TemplateContentType.Video,
      name: '商务蓝PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_7]: {
      contentType: TemplateContentType.Video,
      name: '磨砂PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_8]: {
      contentType: TemplateContentType.Video,
      name: '可爱PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.NO_VIRTUALMAN_9]: {
      contentType: TemplateContentType.Video,
      name: '金属流PPT课程（无数字人）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_1]: {
      contentType: TemplateContentType.Video,
      name: '蓝粉渐变电脑框PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_2]: {
      contentType: TemplateContentType.Video,
      name: '蓝色球状渐变PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_3]: {
      contentType: TemplateContentType.Video,
      name: '浅流体PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_4]: {
      contentType: TemplateContentType.Video,
      name: '深渐变PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_5]: {
      contentType: TemplateContentType.Video,
      name: '黑金PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_6]: {
      contentType: TemplateContentType.Video,
      name: '商务蓝PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_7]: {
      contentType: TemplateContentType.Video,
      name: '磨砂PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_8]: {
      contentType: TemplateContentType.Video,
      name: '可爱PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_9]: {
      contentType: TemplateContentType.Video,
      name: '金属流PPT课程（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
    [PPT_ID_ENUM.ADVANCED_10]: {
      contentType: TemplateContentType.Video,
      name: '全屏横版图片模板（高级）',
      size: LANDSCAPE_DEFAULT_SIZE,
      scene: SceneSpecification.Landscape,
    },
  },
  // 电商
  [ORIGIN_TYPE.E_COMMERCE]: {
    [ECOMMERCE_ID_ENUM.ECOMMERCE_CHINESE_TRADITIONAL_STYLE]: {
      contentType: TemplateContentType.Video,
      name: '国风祥云',
      size: TEXT_DEFAULT_SIZE,
      scene: SceneSpecification.Portrait,
    },
    [ECOMMERCE_ID_ENUM.ECOMMERCE_SUMMER]: {
      contentType: TemplateContentType.Video,
      name: '清凉夏日',
      size: TEXT_DEFAULT_SIZE,
      scene: SceneSpecification.Portrait,
    },
  },
  // 游戏视频
  [ORIGIN_TYPE.GAME_PROMOTION]: {
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NARRATION_PORTRAIT]:
      createVideoTemplate('全屏视频含贴片有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NO_NARRATION_PORTRAIT]:
      createVideoTemplate('全屏视频含贴片无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_NARRATION]:
      createVideoTemplate('清新斜条纹上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_NARRATION]:
      createVideoTemplate('清新粉格纹上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_NARRATION]:
      createVideoTemplate('清新云朵上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_NARRATION]:
      createVideoTemplate('清新花朵上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_NO_NARRATION]:
      createVideoTemplate('清新斜条纹上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_NO_NARRATION]:
      createVideoTemplate('清新粉格纹上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_NO_NARRATION]:
      createVideoTemplate('清新云朵上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_NO_NARRATION]:
      createVideoTemplate('清新花朵上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_WITH_NUMBER_PERSON]:
      createVideoTemplate('清新斜条纹上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_WITH_NUMBER_PERSON]:
      createVideoTemplate('清新粉格纹上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_WITH_NUMBER_PERSON]:
      createVideoTemplate('清新云朵上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_WITH_NUMBER_PERSON]:
      createVideoTemplate('清新花朵上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('清新斜条纹上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('清新粉格纹上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('清新云朵上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('清新花朵上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_NO_NARRATION]:
      createVideoTemplate('日式漫画上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_NO_NARRATION]:
      createVideoTemplate('彩色漫画上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_NO_NARRATION]:
      createVideoTemplate('夜晚童话上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_NO_NARRATION]:
      createVideoTemplate('奇幻漫画上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_NARRATION]:
      createVideoTemplate('日式漫画上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_NARRATION]:
      createVideoTemplate('彩色漫画上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_NARRATION]:
      createVideoTemplate('夜晚童话上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_NARRATION]:
      createVideoTemplate('奇幻漫画上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_WITH_NUMBER_PERSON]:
      createVideoTemplate('日式漫画上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_WITH_NUMBER_PERSON]:
      createVideoTemplate('彩色漫画上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_WITH_NUMBER_PERSON]:
      createVideoTemplate('夜晚童话上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_WITH_NUMBER_PERSON]:
      createVideoTemplate('奇幻漫画上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('日式漫画上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('彩色漫画上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('夜晚童话上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('奇幻漫画上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_NO_NARRATION]:
      createVideoTemplate('棋牌桌上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_NO_NARRATION]:
      createVideoTemplate('扑克花纹上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_NO_NARRATION]:
      createVideoTemplate('棋牌花纹上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_NO_NARRATION]:
      createVideoTemplate('棋牌散射光上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_NARRATION]:
      createVideoTemplate('棋牌桌上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_NARRATION]:
      createVideoTemplate('扑克花纹上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_NARRATION]:
      createVideoTemplate('棋牌花纹上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_NARRATION]:
      createVideoTemplate('棋牌散射光上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_WITH_NUMBER_PERSON]:
      createVideoTemplate('棋牌桌上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_WITH_NUMBER_PERSON]:
      createVideoTemplate('扑克花纹上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_WITH_NUMBER_PERSON]:
      createVideoTemplate('棋牌花纹上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_WITH_NUMBER_PERSON]:
      createVideoTemplate('棋牌散射光上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('棋牌桌上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('扑克花纹上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('棋牌花纹上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('棋牌散射光上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_NO_NARRATION]:
      createVideoTemplate('商务深蓝上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_NO_NARRATION]:
      createVideoTemplate('商务浅蓝上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_NO_NARRATION]:
      createVideoTemplate('商务蓝紫上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_NARRATION]:
      createVideoTemplate('商务深蓝上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_NARRATION]:
      createVideoTemplate('商务浅蓝上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_NARRATION]:
      createVideoTemplate('商务蓝紫上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_WITH_NUMBER_PERSON]:
      createVideoTemplate('商务深蓝上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_WITH_NUMBER_PERSON]:
      createVideoTemplate('商务浅蓝上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_WITH_NUMBER_PERSON]:
      createVideoTemplate('商务蓝紫上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('商务深蓝上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('商务浅蓝上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate('商务蓝紫上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT]: createVideoTemplate(
      '可爱粉点线面上下框有字幕（站姿数字人）'
    ),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT_1]:
      createVideoTemplate('粉绿斑点上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT_2]:
      createVideoTemplate('黄色斑点上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT_3]:
      createVideoTemplate('蓝色纵深线上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME]:
      createVideoTemplate('硬核战斗风上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME_1]:
      createVideoTemplate('战斗蓝红上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME_2]:
      createVideoTemplate('战斗禁区上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME_3]:
      createVideoTemplate('战斗硝烟上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME]:
      createVideoTemplate('硬核战斗风上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME_1]:
      createVideoTemplate('战斗蓝红上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME_2]:
      createVideoTemplate('战斗禁区上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME_3]:
      createVideoTemplate('战斗硝烟上下框有字幕（高级）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN]:
      createVideoTemplate('重山叠嶂上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN_1]:
      createVideoTemplate('江边小亭上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN_2]:
      createVideoTemplate('蜿蜒曲江上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN_3]:
      createVideoTemplate('薄暮远山上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN]:
      createVideoTemplate('重山叠嶂上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN_1]:
      createVideoTemplate('江边小亭上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN_2]:
      createVideoTemplate('蜿蜒曲江上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN_3]:
      createVideoTemplate('薄暮远山上下框有字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN]:
      createVideoTemplate('重山叠嶂上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN_1]:
      createVideoTemplate('江边小亭上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN_2]:
      createVideoTemplate('蜿蜒曲江上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN_3]:
      createVideoTemplate('薄暮远山上下框无字幕（无数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE]:
      createVideoTemplate('休闲云朵上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE_1]:
      createVideoTemplate('黄色染料上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE_2]:
      createVideoTemplate('宇宙星球上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE_3]:
      createVideoTemplate('卡通自然上下框有字幕（站姿数字人）'),
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_HORIZONTAL_SCREEN_WITH_STAND_NUMBER_PERSON]:
      createVideoTemplate(
        '全屏横板视频模板（站姿数字人）',
        LANDSCAPE_DEFAULT_SIZE
      ),
  },
};
export const LIVE_TEMPLATES: TemplateMap = {
  blank: {
    // 空白
    'BC6A8F93-059B-46C8-B221-C670068F8D10': {
      contentType: TemplateContentType.Live,
      name: '空白模板',
    },
  },
  [ORIGIN_TYPE.CONTENT]: {
    [ECOMMERCE_TEMPLATE_ENUM.skincare]: {
      contentType: TemplateContentType.Live,
      name: '美妆护肤带货模板（坐姿数字人）',
    },
    [ECOMMERCE_TEMPLATE_ENUM.tea]: {
      contentType: TemplateContentType.Live,
      name: '茶叶带货模板（坐姿数字人）',
    },
    [ECOMMERCE_TEMPLATE_ENUM.plants]: {
      contentType: TemplateContentType.Live,
      name: '鲜花绿植带货模板（坐姿数字人）',
    },
    [ECOMMERCE_TEMPLATE_ENUM.education]: {
      contentType: TemplateContentType.Live,
      name: '教育类带货模板（坐姿数字人）',
    },
    [ECOMMERCE_TEMPLATE_ENUM.booksDark]: {
      contentType: TemplateContentType.Live,
      name: '图书带货模板-深色（坐姿数字人）',
    },
    [ECOMMERCE_TEMPLATE_ENUM.booksLight]: {
      contentType: TemplateContentType.Live,
      name: '图书带货模板-浅色（坐姿数字人）',
    },
  },
  default: {
    // 话术+音色
    '2AF39FFA-C946-4635-A269-180BF41065DD': {
      contentType: TemplateContentType.Live,
      name: '基础模板',
      description: '只包含话术、音色组件',
    },
  },
  // [ORIGIN_TYPE.GAME_PROMOTION]: {
  //   [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NO_NARRATION_RICH_LANDSCAPE]: {
  //     contentType: TemplateContentType.Live,
  //     name: '直播测试',
  //     description: '只包含话术、音色组件',
  //   },
  // },
};
