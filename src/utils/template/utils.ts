import { commonStyle } from '@/utils/play-view';
import { CommonStyle, PagedooFontStyle } from '@tencent/pagedoo-library';
import { DEFAULT_VIRTUALMAN_VERSION_TEXTS, PPT_Width } from './ppt/constants';
import { DOC_WIDTH } from './doc/constants';
import { IHandleComponentConfig } from './type';
import { get } from 'lodash-es';

export const docCommonStyle = (
  left: number,
  top: number,
  width: number
): CommonStyle => {
  return commonStyle(left, top, width, DOC_WIDTH);
};
export const pptCommonStyle = (
  left: number,
  top: number,
  width: number
): CommonStyle => {
  return commonStyle(left, top, width, PPT_Width);
};

// size填写设计稿上的字号大小，单位为px
export const calFontSize = (size: number): number => {
  return (375 * size) / PPT_Width;
};

// width填写设计稿上的宽度，单位为px
export const calFontDisplayAreaWidth = (width: number): number => {
  return 375 * (width / PPT_Width);
};

export const getCustomTextConfig = (
  fontsize = DEFAULT_VIRTUALMAN_VERSION_TEXTS.titleH1.fontSize,
  width = DEFAULT_VIRTUALMAN_VERSION_TEXTS.titleH1.width,
  lineHeight = DEFAULT_VIRTUALMAN_VERSION_TEXTS.titleH1.lineHeight,
  color: string,
  textAlign: 'left' | 'right' | 'center',
  useImage = 1
): PagedooFontStyle => ({
  fontSize: calFontSize(fontsize),
  width: calFontDisplayAreaWidth(width),
  lineHeight,
  color: {
    color,
    realColor: color,
    show: true,
  },
  textAlign: textAlign ?? 'left',
  useImage,
});

export const getComponentIndex = (array: any[], item: any) => {
  return array.indexOf(item);
};

export function getBgmDuration(duration: string) {
  if (!duration) return 0;
  const [h, m, s] = duration.split(':');
  return (
    (parseInt(h ? h : '0', 10) * 60 * 60 +
      parseInt(m ? m : '0', 10) * 60 +
      parseInt(s ? s : '0', 10)) *
    1000
  );
}

export function genUniqueKey() {
  const step = 1_000_000_000;
  let counter = 1;
  return (index: number) => {
    const randomHash =
      Math.floor(Math.random() * step) + step * counter + index;
    counter += 1;

    return randomHash;
  };
}

export const handleComponentConfig = ({
  componentConfig,
  view,
  pagedooScript,
}: IHandleComponentConfig) => {
  if (!componentConfig) return undefined;
  const { componentConfigCommonType } = componentConfig;
  if (componentConfigCommonType === 'template') {
    const { value } = componentConfig;
    return value;
  }
  if (componentConfigCommonType === 'script') {
    const { scriptKey, scriptDataType, value } = componentConfig;
    let scriptData = null;
    if (scriptDataType === 'global' && pagedooScript) {
      scriptData = pagedooScript;
    }
    if ((!scriptDataType || scriptDataType === 'view') && view) {
      scriptData = view;
    }
    if (!scriptData) return undefined;
    return get(scriptData, scriptKey) || value;
  }
};
