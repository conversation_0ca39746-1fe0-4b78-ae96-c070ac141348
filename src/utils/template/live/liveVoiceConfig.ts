// 直播音色配置枚举

import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';

export const ad_voice_yangxiaohong: IVoiceItem = {
  platform: 'tencent',
  speed: 1,
  voiceDesc: '',
  voiceExtendConfig: '',
  voiceId: 'zshot_23582',
  voiceImage: '',
  voiceName: '杨小红',
  voiceTestText: '',
  driverMode: 'text',
  voiceAuthority: 0,
  appCode: 'advertisement',
  userId: '',
};

export const ad_voice_shenxiaona: IVoiceItem = {
  platform: 'ad_chattts',
  speed: 1,
  voiceDesc: '',
  voiceExtendConfig:
    '{"spk_uid":"0","spk":"ailive_clone_020_shenxiaona_restored_emb"}',
  voiceId: 'ailive_clone_020_shenxiaona_restored_emb',
  voiceImage: '',
  voiceName: '沈小娜',
  voiceTestText: '',
  driverMode: 'voice',
  voiceAuthority: 0,
  appCode: 'advertisement',
  userId: '',
};
