import { commonStyle } from '@/utils/play-view';
import {
  bkgRate,
  defaultRate,
  fromResultLeft,
  fromResultTop,
  fromResultWidth,
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';

export const EcommerceBlankConfig = {
  style: {
    bkg: transformBkgStyle(0, 0, 1242),
    liveSpeech: hiddenStyle,
    virtualMan: transformStyle(
      fromResultLeft(-26.696179384721802, defaultRate, 40),
      fromResultTop(-0.9846951412397387, defaultRate),
      fromResultWidth(140.48317348353604, defaultRate)
    ),
    // AI_identify: commonStyle(360, 290, 30),
    AI_identify: transformStyle(
      fromResultLeft(80.74412268488376, defaultRate, 40),
      fromResultTop(45.51430221803746, defaultRate),
      fromResultWidth(5.47046901659104, defaultRate)
    ),
    title: transformStyle(
      fromResultLeft(24.98274672187716, defaultRate, 40),
      fromResultTop(8.958432871476349, defaultRate),
      fromResultWidth(46.45113340765515, defaultRate)
    ),
    subTitle: transformStyle(
      fromResultLeft(27.50437967829272, defaultRate, 40),
      fromResultTop(15.096618357487923, defaultRate),
      fromResultWidth(46.45113340765515, defaultRate)
    ),
    liveImages: {
      foreground: transformBkgStyle(
        0,
        fromResultTop(75.36231884057972, bkgRate),
        1242
      ),
      productTable: transformStyle(
        fromResultLeft(8.791208791208792, defaultRate, 40),
        fromResultTop(62.07862186123055, defaultRate),
        fromResultWidth(82.41758241758241, defaultRate)
      ),
      productLeft: transformStyle(
        fromResultLeft(12.910306879154856, defaultRate, 40),
        fromResultTop(20.131325981055028, defaultRate),
        fromResultWidth(20.787782263045955, defaultRate)
      ),
      productRight: transformStyle(
        fromResultLeft(63.45744059245607, defaultRate, 40),
        fromResultTop(20.45955412205049, defaultRate),
        fromResultWidth(24.945338715655144, defaultRate)
      ),
    },
  },
  components: {
    title: {
      text: '直播专属福利',
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
        fontSize: 125,
        color: '#A05756',
        fontWeight: 700,
      }),
    },
    subTitle: {
      style: getFontStyle({
        width: 700,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
        fontSize: 55,
        color: '#A05756',
        fontWeight: 500,
      }),
      text: '——会员下单 立享好礼——',
    },
  },
  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/176b3ec2b98685e07e188bd9e0c5305c.png',
    AI_identify: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/2038a716edb7f009c104bb02f747a08a.svg',
      __resourceId: 'grey_3',
    },
    liveImages: {
      foreground:
        'https://pagedoo.pay.qq.com/material/@platform/c5095cfbd4fad68c8e2d0349543b6c72.png',
      productTable:
        'https://pagedoo.pay.qq.com/material/@platform/962220ca9c6eba5a4b0a909a24f9e901.png',
      productLeft:
        'https://avatarcdnams.pay.qq.com/material/62274756023af96db7ac8c1cd87a6bf5.png',
      productRight:
        'https://avatarcdnams.pay.qq.com/material/f7cf5452f04255468fab1371e271c8b7.png',
    },
  },
};
