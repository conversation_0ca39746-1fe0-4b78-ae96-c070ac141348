import { commonStyle } from '@/utils/play-view';
import { getFontStyle, transformBkgStyle, transformStyle } from '../utils';

export const EcommerceSkinConfig = {
  style: {
    bkg: transformBkgStyle(0, 0, 1242),
    virtualMan: transformStyle(-50, 688, 1344),
    foreground: transformBkgStyle(0, 1872, 1242),
    templateName: transformStyle(244, 270, 752),
    subTitle: transformStyle(282, 455, 700),
    product1: {
      base: transformStyle(40, 590, 351),
      p1: transformStyle(92, 794, 249),
      t1: transformStyle(94, 617, 253),
      t2: transformStyle(82, 699, 21),
      t3: transformStyle(103, 669, 230),
    },
    product2: {
      base: transformStyle(804, 612, 462),
      p1: transformStyle(913, 761, 256),
      p2: transformStyle(913, 839, 256),
      p3: transformStyle(913, 917, 256),
      p4: transformStyle(916, 1063, 256),
      p5: transformStyle(0, 1871, 1242),
      t1: transformStyle(886, 641, 297),
      t2: transformStyle(950, 749, 180),
      t3: transformStyle(950, 827, 180),
      t4: transformStyle(950, 906, 180),
    },
    AI_identify: commonStyle(360, 290, 30),
  },
  components: {
    templateName: {
      text: '直播专属福利',
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
        fontSize: 125,
        color: '#A05756',
        fontWeight: 700,
      }),
    },
    subTitle: {
      style: getFontStyle({
        width: 700,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
        fontSize: 55,
        color: '#A05756',
        fontWeight: 500,
      }),
      text: '——会员下单 立享好礼——',
    },
    product1: {
      t1: {
        text: '直播间专享',
        style: getFontStyle({
          width: 253,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#61372E',
          fontWeight: 500,
        }),
      },
      t2: {
        text: '¥',
        style: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 33,
          color: '#DF5655',
          fontWeight: 900,
        }),
      },
      t3: {
        text: '199/3件',
        style: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#DF5655',
          fontWeight: 900,
        }),
      },
    },
    product2: {
      t1: {
        style: getFontStyle({
          width: 297,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 45,
          color: '#6E3B3B',
          fontWeight: 900,
        }),
        text: '点关注有福利',
      },
      t2: {
        style: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#DF5655',
          fontWeight: 700,
        }),
        text: '镇店爆款',
      },
      t3: {
        style: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#DF5655',
          fontWeight: 700,
        }),
        text: '超值好物',
      },
      t4: {
        style: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#DF5655',
          fontWeight: 700,
        }),
        text: '关注领券',
      },
    },
  },

  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/176b3ec2b98685e07e188bd9e0c5305c.png',
    foreground:
      'https://pagedoo.pay.qq.com/material/@platform/c5095cfbd4fad68c8e2d0349543b6c72.png',
    product1: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/60daa395220ade647179a6f5f3875c58.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/841585c4ee1bf36d7ef3cded7091efac.png',
    },
    product2: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/0e9d27c79c8ce36088c61c16036ffc6c.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/c79afb674404dbf94547582a3668b9a7.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/c79afb674404dbf94547582a3668b9a7.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/c79afb674404dbf94547582a3668b9a7.png',
      p4: 'https://pagedoo.pay.qq.com/material/@platform/841585c4ee1bf36d7ef3cded7091efac.png',
      p5: 'https://pagedoo.pay.qq.com/material/@platform/962220ca9c6eba5a4b0a909a24f9e901.png',
    },
    AI_identify:
      'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
  },
};
