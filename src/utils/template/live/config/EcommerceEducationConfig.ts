import { commonStyle } from '@/utils/play-view';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';

export const EcommerceEducationConfig = {
  style: {
    liveSpeech: hiddenStyle,
    bkg: transformBkgStyle(0, 0, 1242),
    virtualMan: transformStyle(-50, 1098, 1344),
    foreground: transformBkgStyle(0, 2080, 1242),
    templateName: transformStyle(251, 180, 752),
    product2: {
      base: transformStyle(830, 1370, 338),
      t1: transformStyle(908, 1380, 182),
      t2: transformStyle(866, 1485, 270),
    },
    product1: {
      base: transformStyle(73, 1037, 274),
      p1: transformStyle(105, 1250, 213),
      p2: transformStyle(105, 1419, 213),
      t1: transformStyle(99, 1048, 228),
      t2: transformStyle(104, 1175, 216),
      t3: transformStyle(120, 1260, 186),
      t4: transformStyle(105, 1354, 216),
      t5: transformStyle(115, 1429, 194),
    },
    introduction: {
      base: transformStyle(73, 508, 1095),
      p1: transformStyle(881, 663, 243),
      t1: transformStyle(125, 538, 720),
      t2: transformStyle(918, 560, 214),
      t3: transformStyle(177, 654, 616),
      t4: transformStyle(935, 698, 132),
      t5: transformStyle(908, 727, 173),
      t6: transformStyle(1080, 782, 21),
      t7: transformStyle(935, 820, 132),
    },
    subTitle: {
      base: transformStyle(306, 395, 641),
      t0: transformStyle(408, 385, 487),
    },
    goods: transformStyle(0, 1880, 1242),
    table: transformStyle(0, 2506, 1242),
    AI_identify: commonStyle(360, 290, 30),
  },
  components: {
    templateName: {
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
        fontSize: 125,
        color: '#105DFF',
        fontWeight: 700,
      }),
      text: '直播专属福利',
    },
    subTitle: {
      style: getFontStyle({
        width: 487,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
        fontSize: 55,
        color: '#FFFFFF',
        fontWeight: 500,
      }),
      text: 'AI老师一对一辅导',
    },
    introduction: {
      t1: {
        style: getFontStyle({
          width: 720,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#105DFF',
          fontWeight: 700,
        }),
        text: '全新高知系列课程限时抢购',
      },
      t2: {
        style: getFontStyle({
          width: 214,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 40,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        text: '会员满2赠1',
      },
      t3: {
        style: getFontStyle({
          width: 616,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 36,
          color: '#FFFFFF',
          textAlign: 'left',
          lineHeight: 1.8333333333333333,
          fontWeight: 500,
        }),
        text: '由浅入深讲知识，方便理解好吸收.\n多维度讲解教学，全方位提升认知\n抓重点精炼总结，轻松掌握技技巧\n购买全系书籍附赠资深专家手写笔记。',
      },
      t4: {
        style: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        text: '终身会员',
      },
      t5: {
        style: getFontStyle({
          width: 173,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 70,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        text: '1099',
      },
      t6: {
        style: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 20,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        text: '元',
      },
      t7: {
        style: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        text: '持续升级',
      },
    },
    product1: {
      t1: {
        style: getFontStyle({
          width: 228,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 56,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        text: '新课上新',
      },
      t2: {
        style: getFontStyle({
          width: 216,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 43,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        text: '领券更优惠',
      },
      t3: {
        style: getFontStyle({
          width: 186,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 35,
          color: '#0A69F7',
          fontWeight: 700,
        }),
        text: '满300减50',
      },
      t4: {
        style: getFontStyle({
          width: 216,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 43,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        text: '直播抽大奖',
      },
      t5: {
        style: getFontStyle({
          width: 194,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 35,
          color: '#0A69F7',
          fontWeight: 700,
        }),
        text: '送50份礼包',
      },
    },
    product2: {
      t1: {
        style: getFontStyle({
          width: 182,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        text: '李老师',
      },
      t2: {
        style: getFontStyle({
          width: 270,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 30,
          color: '#FFFFFF',
          lineHeight: 1.5,
          fontWeight: 700,
        }),
        text: '武汉大学教育学硕士 北京大学心理学博士 一加学院创始人 百度第六季领学官',
      },
    },
  },
  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/e6e6effa4fd5ba279c06b50a64906207.png',
    foreground:
      'https://pagedoo.pay.qq.com/material/@platform/9fd3f882ec8abc9fd2fa701726775a5a.png',
    product2: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/c2c093ab55dcde3d7e8b3f523f48b0d1.png',
    },
    product1: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/f86dac7f0410e6eaf198f2d0c705fe9e.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/a9b319cbc134cf41490b0c07d070891f.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/a9b319cbc134cf41490b0c07d070891f.png',
    },
    introduction: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/2485d75826137061a99343826ae12d4f.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/25dcc31ae68013c86d4f1cbd40ba81f6.png',
    },
    subTitle:
      'https://pagedoo.pay.qq.com/material/@platform/bfc2a54accd34c4666a372f2ee3a482c.png',
    goods:
      'https://pagedoo.pay.qq.com/material/@platform/967ffb23d2a843dc0c61085cf15f29b5.png',
    table:
      'https://pagedoo.pay.qq.com/material/@platform/c81341a90e8b7e35b8ea0ec8595614c2.png',
    AI_identify:
      'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
  },
};
