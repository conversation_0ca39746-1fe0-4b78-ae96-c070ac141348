import { commonStyle } from '@/utils/play-view';
import { getFontStyle, transformBkgStyle, transformStyle } from '../utils';

export const EcommercePlantsConfig = {
  style: {
    bkg: transformBkgStyle(0, 0, 1242),
    table: transformBkgStyle(0, 1493, 1242),
    foreground: transformBkgStyle(0, 1610, 1242),
    subTitle: {
      t0: transformStyle(384, 385, 496),
      p0: transformStyle(356, 416, 42),
      p1: transformStyle(949, 416, 42),
    },
    product1: {
      t0: transformStyle(85, 611, 250),
      t1: transformStyle(84, 702, 21),
      t2: transformStyle(105, 672, 232),
      p0: transformStyle(59, 603, 301),
      p1: transformStyle(84, 779, 251),
    },
    product2: {
      p0: transformStyle(866, 570, 332),
      p1: transformStyle(887, 720, 291),
      p2: transformStyle(887, 904, 291),
      p3: transformStyle(887, 1100, 291),
      t0: transformStyle(893, 578, 279),
      t1: transformStyle(969, 762, 17),
      t2: transformStyle(986, 707, 93),
      t3: transformStyle(926, 818, 240),
      t4: transformStyle(969, 952, 17),
      t5: transformStyle(965, 896, 159),
      t6: transformStyle(926, 1008, 240),
      t7: transformStyle(969, 1149, 17),
      t8: transformStyle(986, 1093, 159),
      t9: transformStyle(926, 1205, 240),
    },
    bottom: {
      t0: transformStyle(164, 2197, 199),
      t1: transformStyle(441, 2197, 199),
      t2: transformStyle(678, 2197, 199),
      t3: transformStyle(915, 2197, 199),
      p0: transformStyle(157, 2124, 212),
      p1: transformStyle(416, 2124, 212),
      p2: transformStyle(657, 2124, 212),
      p3: transformStyle(898, 2124, 212),
    },
    templateName: transformStyle(244, 190, 752),
    goods: {
      p0: transformStyle(40, 922, 1242),
      p1: transformStyle(0, 1012, 587),
    },
    AI_identify: commonStyle(360, 290, 30),
  },
  components: {
    product1: {
      t0: {
        style: getFontStyle({
          width: 250,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 50,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        text: '直播间专享',
      },
      t1: {
        style: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 33,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        text: '¥',
      },
      t2: {
        style: getFontStyle({
          width: 232,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 60,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        text: '199/3件',
      },
    },
    product2: {
      t0: {
        style: getFontStyle({
          width: 279,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 55,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        text: '直播间福利',
      },
      t1: {
        style: getFontStyle({
          width: 17,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 27,
          color: '#568C45',
          fontWeight: 900,
        }),
        text: '¥',
      },
      t2: {
        style: getFontStyle({
          width: 93,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 76,
          color: '#568C45',
          fontWeight: 900,
        }),
        text: '50',
      },
      t3: {
        style: getFontStyle({
          width: 240,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 34,
          color: '#2F4B26',
          fontWeight: 700,
        }),
        text: '全场满399可用',
      },
      t4: {
        style: getFontStyle({
          width: 17,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 27,
          color: '#568C45',
          fontWeight: 900,
        }),
        text: '¥',
      },
      t5: {
        style: getFontStyle({
          width: 159,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 76,
          color: '#568C45',
          fontWeight: 900,
        }),
        text: '100',
      },
      t6: {
        style: getFontStyle({
          width: 240,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 34,
          color: '#2F4B26',
          textAlign: 'left',
          fontWeight: 700,
        }),
        text: '全场满399可用',
      },
      t7: {
        style: getFontStyle({
          width: 17,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 27,
          color: '#568C45',
          fontWeight: 900,
        }),
        text: '¥',
      },
      t8: {
        style: getFontStyle({
          width: 159,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 76,
          color: '#568C45',
          fontWeight: 900,
        }),
        text: '200',
      },
      t9: {
        style: getFontStyle({
          width: 240,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 34,
          color: '#2F4B26',
          fontWeight: 700,
        }),
        text: '全场满399可用',
      },
    },
    bottom: {
      t0: {
        style: getFontStyle({
          width: 199,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#276620',
          fontWeight: 700,
        }),
        text: '100%正品',
      },
      t1: {
        style: getFontStyle({
          width: 169,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#276620',
          fontWeight: 700,
        }),
        text: '全国联保',
      },
      t2: {
        style: getFontStyle({
          width: 169,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#276620',
          fontWeight: 700,
        }),
        text: '七天可退',
      },
      t3: {
        style: getFontStyle({
          width: 199,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          textAlign: 'center',
          color: '#276620',
          fontWeight: 700,
        }),
        text: '保价30天',
      },
    },
    templateName: {
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
        fontSize: 125,
        color: '#127804',
        fontWeight: 700,
      }),
      text: '直播专属福利',
    },
    subTitle: {
      style: getFontStyle({
        width: 496,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
        fontSize: 55,
        color: '#127804',
        fontWeight: 700,
      }),
      text: '会员下单 立享好礼',
    },
  },
  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/e0fff5ef68a0b5d2cd853d0fce8ca584.png',
    foreground:
      'https://pagedoo.pay.qq.com/material/@platform/65590e0d7758a5a9f219fbb74bcd95b3.png',
    table:
      'https://pagedoo.pay.qq.com/material/@platform/974af29fe96670123dda51f66af6747b.png',
    subTitle: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/c7ef360311a45c8d4cd5523a78d318b9.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/c7ef360311a45c8d4cd5523a78d318b9.png',
    },
    product1: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/3135e848aa5bce0b8d74bc64a717958b.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/497d881b38cfb72e21565e0a5a2c995d.png',
    },
    product2: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/95ba217563526afc97105745d55bd9f7.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/36cdc557382b263af18442d81545fa23.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/36cdc557382b263af18442d81545fa23.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/36cdc557382b263af18442d81545fa23.png',
    },
    bottom: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
    },
    goods: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/6d574692a362bb7ec39b37b2dc8e1bda.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/673495940723cf596430f6d434bf34e4.png',
    },
    AI_identify:
      'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
  },
};
