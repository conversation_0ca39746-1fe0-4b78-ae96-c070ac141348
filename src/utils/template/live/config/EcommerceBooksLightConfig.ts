import { commonStyle } from '@/utils/play-view';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';

export const EcommerceBooksLightConfig = {
  style: {
    liveSpeech: hiddenStyle,
    templateName: transformStyle(246, 190, 752),
    bkg: transformBkgStyle(0, 0, 1242),
    virtualMan: transformStyle(-80, 880, 1344),
    foreground: transformBkgStyle(0, 1865, 1242),
    product1: {
      p0: transformStyle(81, 1031, 273),
      p1: transformStyle(96, 1118, 242),
      p2: transformStyle(112, 1213, 210),
      t0: transformStyle(102, 1037, 230),
      t1: transformStyle(117, 1103, 200),
    },
    teacher: {
      p0: transformStyle(848, 1206, 338),
      t0: transformStyle(925, 1242, 184),
      t1: transformStyle(882, 1331, 270),
    },
    introduction: {
      p0: transformStyle(88, 488, 1067),
      p1: transformStyle(745, 730, 176),
      p2: transformStyle(930, 730, 176),
      p3: transformStyle(930, 730, 176),
      p4: transformStyle(745, 730, 176),
      t0: transformStyle(158, 532, 472),
      t1: transformStyle(770, 546, 308),
      t2: transformStyle(811, 568, 264),
      t3: transformStyle(770, 619, 38),
      t4: transformStyle(756, 740, 153),
      t5: transformStyle(943, 740, 153),
      t6: transformStyle(767, 785, 131),
      t7: transformStyle(951, 785, 131),
    },
    goods: {
      p0: transformStyle(0, 1899, 373),
      p1: transformStyle(448, 1910, 825),
      p2: transformStyle(0, 2230, 1242),
    },
    subTitle: transformStyle(284, 373, 695),
    AI_identify: commonStyle(360, 290, 30),
  },
  components: {
    templateName: {
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
        fontSize: 125,
        color: '#57341F',
        fontWeight: 700,
      }),
      text: '直播专属福利',
    },
    subTitle: {
      style: getFontStyle({
        width: 695,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
        fontSize: 55,
        color: '#57341F',
        fontWeight: 500,
      }),
      text: '——会员下单 立享好礼——',
    },
    introduction: {
      t0: {
        style: getFontStyle({
          width: 472,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 40,
          color: '#6D452E',
          textAlign: 'left',
          lineHeight: 1.4,
          fontWeight: 500,
        }),
        text: '2100 张初高中高频单词卡\n10本中考逆袭手册\n400个语法精讲视频\n30天单词卡\n90天金牌专家辅导伴学\n10次1v1学情诊断',
      },
      t1: {
        style: getFontStyle({
          width: 308,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 40,
          color: '#6D452E',
          fontWeight: 700,
        }),
        text: '会员专享 拍1得8',
      },
      t2: {
        style: getFontStyle({
          width: 264,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 108,
          color: '#57341F',
          fontWeight: 900,
        }),
        text: '1099',
      },
      t3: {
        style: getFontStyle({
          width: 38,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 61,
          color: '#57341F',
          fontWeight: 900,
        }),
        text: '¥',
      },
      t4: {
        style: getFontStyle({
          width: 153,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 21,
          color: '#EBDFCF',
          fontWeight: 900,
        }),
        text: '直播间限时抢购',
      },
      t5: {
        style: getFontStyle({
          width: 153,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 21,
          color: '#EBDFCF',
          fontWeight: 900,
        }),
        text: '满199赠会员礼',
      },
      t6: {
        style: getFontStyle({
          width: 131,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 21,
          color: '#6D452E',
          fontWeight: 400,
          lineHeight: 1.4285714285714286,
        }),
        text: '全程专家辅导 1V1 特辑培训',
      },
      t7: {
        style: getFontStyle({
          width: 131,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 21,
          color: '#6D452E',
          fontWeight: 400,
          lineHeight: 1.4285714285714286,
        }),
        text: '逆袭学情诊断 语法精讲视频',
      },
    },
    product1: {
      t0: {
        style: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 46,
          color: '#61372E',
          fontWeight: 700,
        }),
        text: '直播间专享',
      },
      t1: {
        style: getFontStyle({
          width: 200,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 42,
          color: '#F4E7D6',
          fontWeight: 500,
        }),
        text: '满199即赠',
      },
    },
    teacher: {
      t0: {
        style: getFontStyle({
          width: 184,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#57341F',
          fontWeight: 900,
        }),
        text: '李老师',
      },
      t1: {
        style: getFontStyle({
          width: 270,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 30,
          color: '#6D452E',
          lineHeight: 1.5,
          fontWeight: 700,
        }),
        text: '武汉大学教育学硕士 北京大学心理学博士 一加学院创始人 百度第六季领学官',
      },
    },
  },
  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/807388322d5723764b6b5f8aaa203d87.png',
    foreground:
      'https://pagedoo.pay.qq.com/material/@platform/8f912a6dec49496904fe80104757077c.png',
    product1: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/37e2bff39646722357b160f455ede536.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/16010b25692e51d52defc35abc43302e.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/0590c1ada3658fce8a915edd306345bb.png',
    },
    teacher:
      'https://pagedoo.pay.qq.com/material/@platform/5f9645f7779fe98132c2bf06d53f55f1.png',
    introduction: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/8b45ea659abde5e97ddd6d3138a05108.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/aeb28bd13396e238f622a84f238341a2.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/aeb28bd13396e238f622a84f238341a2.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/7bc9bb5b8c25505f932b1e50931604a5.png',
      p4: 'https://pagedoo.pay.qq.com/material/@platform/7bc9bb5b8c25505f932b1e50931604a5.png',
    },
    goods: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/827ef4a027372bac488b4409051ee427.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/3d2b1663891bb0ac4a96e3e5832f4e4c.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/149c8f6a9ec0163c651da5bb1efd58a4.png',
    },
    AI_identify:
      'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
  },
};
