import { commonStyle } from '@/utils/play-view';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';

export const EcommerceBooksDarkConfig = {
  style: {
    liveSpeech: hiddenStyle,
    bkg: transformBkgStyle(0, 0, 1242),
    foreground: transformBkgStyle(0, 2050, 1242),
    virtualMan: transformStyle(-60, 1010, 1344),
    templateName: transformStyle(246, 186, 752),
    subTitle: transformStyle(284, 381, 700),
    product: {
      p0: transformStyle(88, 1152, 317),
      p1: transformStyle(119, 1243, 242),
      p2: transformStyle(114, 1316, 234),
      t0: transformStyle(124, 1162, 230),
      t1: transformStyle(140, 1228, 200),
    },
    introduction: {
      base: transformStyle(88, 497, 1067),
      p0: transformStyle(118, 641, 44),
      p1: transformStyle(118, 706, 44),
      p2: transformStyle(118, 771, 44),
      p3: transformStyle(118, 836, 44),
      p4: transformStyle(871, 637, 243),
      t0: transformStyle(117, 512, 720),
      t1: transformStyle(898, 527, 214),
      t2: transformStyle(181, 628, 612),
      t3: transformStyle(925, 672, 132),
      t4: transformStyle(925, 794, 132),
      t5: transformStyle(898, 701, 173),
      t6: transformStyle(1067, 756, 21),
    },
    teacher: {
      t0: transformStyle(876, 1343, 184),
      t1: transformStyle(877, 1452, 270),
      p0: transformStyle(840, 1337, 338),
    },
    goods: {
      p0: transformStyle(51, 1792, 350),
      p1: transformStyle(450, 2141, 802),
      p2: transformStyle(0, 2453, 1242),
    },
    AI_identify: commonStyle(360, 290, 30),
  },
  components: {
    templateName: {
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
        fontSize: 125,
        color: '#F4E7D7',
        fontWeight: 700,
      }),
      text: '直播专属福利',
    },
    subTitle: {
      style: getFontStyle({
        width: 700,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
        fontSize: 55,
        color: '#F4E7D7',
        fontWeight: 500,
      }),
      text: '——会员下单 立享好礼——',
    },
    introduction: {
      t0: {
        style: getFontStyle({
          width: 720,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#F4E7D7',
          fontWeight: 900,
        }),
        text: '全新认知系列套书限时抢购',
      },
      t1: {
        style: getFontStyle({
          width: 214,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 40,
          color: '#F4E7D7',
          fontWeight: 900,
        }),
        text: '会员满2赠1',
      },
      t2: {
        style: getFontStyle({
          width: 612,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 36,
          color: '#FFF9F2',
          lineHeight: 1.8333333333333333,
          textAlign: 'left',
          fontWeight: 400,
        }),
        text: '由浅入深讲知识，方便理解好吸收。\n多维度讲解教学，全方位提升认知。\n抓重点精炼总结，轻松掌握技技巧。\n购买全系书籍附赠资深专家手写笔记。',
      },
      t3: {
        style: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#57341F',
          fontWeight: 500,
        }),
        text: '终身会员',
      },
      t4: {
        style: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#57341F',
          fontWeight: 500,
        }),
        text: '持续升级',
      },
      t5: {
        style: getFontStyle({
          width: 173,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 71,
          color: '#57341F',
          fontWeight: 900,
        }),
        text: '1099',
      },
      t6: {
        style: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 20,
          color: '#57341F',
          fontWeight: 900,
        }),
        text: '元',
      },
    },
    product: {
      t0: {
        style: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 46,
          color: '#61372E',
          fontWeight: 700,
        }),
        text: '直播间专享',
      },
      t1: {
        style: getFontStyle({
          width: 200,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 42,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        text: '满199即赠',
      },
    },
    teacher: {
      t0: {
        style: getFontStyle({
          width: 184,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          color: '#E4CDB8',
          fontSize: 61,
          fontWeight: 900,
        }),
        text: '李老师',
      },
      t1: {
        style: getFontStyle({
          width: 270,
          fontSize: 30,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          color: '#FFFFFF',
          textAlign: 'left',
          lineHeight: 1.5,
          fontWeight: 700,
        }),
        text: '武汉大学教育学硕士北京大学心理学博士一加学院创始人百度第六季领学官',
      },
    },
  },
  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/08f9a053d2d9a08fa5f795b06554bc4a.png',
    foreground:
      'https://pagedoo.pay.qq.com/material/@platform/b014af09e49c7033345588ca9d54512a.png',
    teacher: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/88e546d79c1d457775d5e2d12b2c66f7.png',
    },
    product: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/a7edf55580991ce9bb7a4e2527a30207.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/b28ec245c045e3669f84320f2425f093.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/ff8ded7daae6ac057552e53ef91ca33e.png',
    },
    introduction: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/75aed134a075d26b88d9e5186a97db8a.png',
      p0: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
      p4: 'https://pagedoo.pay.qq.com/material/@platform/771a4a1d33c95cda16b3b004d172c5b7.png',
    },
    goods: {
      p0: 'https://pagedoo.pay.qq.com/material/@platform/8df467501c3b112d7e774eca780ddc36.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/38c28fc141fab99854d3722d08432010.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/4fa82b2e65d95e539914feb9073d50d9.png',
    },
    AI_identify:
      'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
  },
};
