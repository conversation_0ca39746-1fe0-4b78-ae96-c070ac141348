import { commonStyle } from '@/utils/play-view';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';
export const EcommerceTeaConfig = {
  style: {
    liveSpeech: hiddenStyle,
    bkg: transformBkgStyle(0, 0, 1242),
    foreground: transformBkgStyle(0, 0, 1242),
    foregroundGreen: transformStyle(0, 2048, 1242),
    subTitle: {
      p1: transformStyle(361, 408, 533),
      text: transformStyle(413, 398, 430),
    },
    product1: {
      base: transformStyle(65, 621, 300),
      p1: transformStyle(98, 784, 234),
      p2: transformStyle(93, 978, 244),
      p3: transformStyle(138, 1051, 134),
      t1: transformStyle(157, 658, 115),
      t2: transformStyle(100, 690, 230),
      t3: transformStyle(120, 778, 187),
      t4: transformStyle(100, 891, 230),
      t5: transformStyle(153, 972, 150),
    },
    product2: {
      base: transformStyle(877, 644, 300),
      p1: transformStyle(933, 812, 188),
      p2: transformStyle(910, 745, 234),
      t1: transformStyle(927, 655, 200),
      t2: transformStyle(932, 738, 180),
    },
    bottom: {
      p1: transformStyle(118, 2411, 204),
      p2: transformStyle(385, 2411, 204),
      p3: transformStyle(652, 2411, 204),
      p4: transformStyle(919, 2411, 204),
      t1: transformStyle(130, 2439, 183),
      t2: transformStyle(412, 2439, 152),
      t3: transformStyle(679, 2439, 152),
      t4: transformStyle(946, 2439, 172),
    },
    goods: transformStyle(0, 1564, 1242),
    templateName: transformStyle(246, 201, 752),
    AI_identify: commonStyle(360, 290, 30),
  },
  components: {
    templateName: {
      style: getFontStyle({
        width: 752,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
        fontSize: 125,
        color: '#2B6D22',
        fontWeight: 900,
      }),
      text: '直播专属福利',
    },
    subTitle: {
      style: getFontStyle({
        width: 430,
        fontFamily:
          'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
        fontSize: 48,
        color: '#FCE9B6',
        fontWeight: 900,
      }),
      text: '会员下单 立享好礼',
    },
    product1: {
      t1: {
        style: getFontStyle({
          width: 115,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 28,
          color: '#23561C',
          fontWeight: 900,
        }),
        text: '百亿补贴',
      },
      t2: {
        style: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 57,
          color: '#23561C',
          fontWeight: 900,
        }),
        text: '周年盛典',
      },
      t3: {
        style: getFontStyle({
          width: 187,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 30,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '单价低至99元',
      },
      t4: {
        style: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 57,
          color: '#23561C',
          fontWeight: 900,
        }),
        text: '直播福利',
      },
      t5: {
        style: getFontStyle({
          width: 125,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 32,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '满599赠',
      },
    },
    product2: {
      t1: {
        style: getFontStyle({
          width: 200,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 50,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '畅享巨惠',
      },
      t2: {
        style: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 30,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '下单一件即赠',
      },
    },
    bottom: {
      t1: {
        style: getFontStyle({
          width: 183,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '100%正品',
      },
      t2: {
        style: getFontStyle({
          width: 152,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '全国联保',
      },
      t3: {
        style: getFontStyle({
          width: 152,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '七天可退',
      },
      t4: {
        style: getFontStyle({
          width: 152,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        text: '保价30天',
      },
    },
  },
  materialMap: {
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/fee5f1a062ceba82fd7f8dbe532d257b.png',
    foreground:
      'https://pagedoo.pay.qq.com/material/@platform/b77bba2f3b2631fc98e44a04b52ed520.png',
    foregroundGreen:
      'https://pagedoo.pay.qq.com/material/@platform/398b51c5d8a75847d63f16c3c20dd72a.png',
    subTitle:
      'https://pagedoo.pay.qq.com/material/@platform/379b9d305cbc1914c1d90ffe11e32fc1.png',
    product1: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/613e335fc79bbbb84dd7a48ac6cb58db.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/df853539982ca0a1fe3cf48915c06928.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/df853539982ca0a1fe3cf48915c06928.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/1c849d6f14ca71f4bdbbe04011d7ca4c.png',
    },
    product2: {
      base: 'https://pagedoo.pay.qq.com/material/@platform/7ab7ef74d04569e7a78d32132dfebba5.png',
      p1: 'https://pagedoo.pay.qq.com/material/@platform/1c849d6f14ca71f4bdbbe04011d7ca4c.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/df853539982ca0a1fe3cf48915c06928.png',
    },
    bottom: {
      p1: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
      p2: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
      p3: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
      p4: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
    },
    goods:
      'https://pagedoo.pay.qq.com/material/@platform/a4dc78029e9b0cb1bd41884a380a7dad.png',
    AI_identify:
      'https://pagedoo.pay.qq.com/material/@platform/507584cbda5da275b81a3f23f6abef99.svg',
  },
};
