import { BaseScript } from '@/components/ScriptForm/type';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { TEXT_DEFAULT_SIZE } from '@/pages/Question/constant';
import { viewMocker } from '../../mock/utils';

export const adMockScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.E_COMMERCE,
  totalDuration: 200000,
  globalField: {
    title: '直播专属福利',
    subTitle: '——会员下单 立享好礼——',
  },
  size: TEXT_DEFAULT_SIZE,
  views: [
    // 概述
    viewMocker({
      duration: '00:00-03:20',
    }),
  ],
};
