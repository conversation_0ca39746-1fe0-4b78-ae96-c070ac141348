import { BaseScript } from '@/components/ScriptForm/type';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { TEXT_DEFAULT_SIZE } from '@/pages/Question/constant';
import { viewMocker } from '../../mock/utils';

export const baseMockScript: BaseScript = {
  backgroundImage: [],
  type: ORIGIN_TYPE.E_COMMERCE,
  totalDuration: 30000,
  globalField: {
    title: '直播专属福利',
    subTitle: '——会员下单 立享好礼——',
  },
  size: TEXT_DEFAULT_SIZE,
  globalSpeech:
    '亲爱的小伙伴们，欢迎来到我们的电商直播间，今晚 8 点，我们将为您带来一场精彩纷呈的直播盛宴，我们为您精心挑选了一系列热销商品，包括家居用品、美妆护肤、服饰鞋包等，满足您的各种需求，在直播期间，我们为您准备了丰厚的优惠券和折扣，让您买到心仪的商品更划算。',
  views: [
    // 概述
    viewMocker({
      duration: '00:00-00:05',
      speech: '亲爱的小伙伴们，欢迎来到我们的电商直播间，',
    }),
    viewMocker({
      duration: '00:05-00:15',
      speech:
        '今晚 8 点，我们将为您带来一场精彩纷呈的直播盛宴，我们为您精心挑选了一系列热销商品，',
    }),
    viewMocker({
      duration: '00:15-00:22',
      speech: '包括家居用品、美妆护肤、服饰鞋包等，满足您的各种需求,',
    }),
    viewMocker({
      duration: '00:22-00:30',
      speech:
        '在直播期间，我们为您准备了丰厚的优惠券和折扣，让您买到心仪的商品更划算。',
    }),
  ],
};
