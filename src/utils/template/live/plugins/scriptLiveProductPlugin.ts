import { PlayConfig, Script } from '@/type/pagedoo';
import { SinglePlugin } from '../..';
import { IBasePluginOptions } from '../../plugins/type';
import { liveProduct } from '@/utils/play-component';
import { allViews } from '@/utils/play-view';
import { uuid } from '@tencent/midas-util';
import { BaseScript } from '@/components/ScriptForm/type';
import { IPluginOptions } from '../types';

export function scriptLiveProductPlugin(options: IPluginOptions): SinglePlugin {
  return (conf: PlayConfig) => {
    const { script, key, timelineIndex, duration } = options;
    const productList = script.adExtendData?.productList || [];
    if (!productList.length) return;
    let idx = 0;
    while (idx < productList.length) {
      conf.timeline[timelineIndex].node[idx] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: liveProduct(
          key,
          { product: productList[idx].product_id },
          allViews['广告联调模板'].hidden
        ),
        duration,
        id: uuid(),
        key,
        offset: idx * duration,
      };
      idx += 1;
    }
  };
}
