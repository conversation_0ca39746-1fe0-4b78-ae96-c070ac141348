import { PlayConfig } from '@/type/pagedoo';
import { SinglePlugin } from '../..';
import { IBasePluginOptions, View } from '../../plugins/type';
import { customVirtualManWave } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { DipListItem } from '@/type';
import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';
import { VirtualManType } from '../types';
import { VirtualManPlatformToMaterialType } from '@/const/virtualman';

interface IPluginOptions extends IBasePluginOptions {
  baseFn: (view?: View) => {
    defaultDip?: DipListItem;
    defaultVoiceItem?: IVoiceItem;
  };
  componentName: {
    name: string;
    subName: string;
  };
  virtualMan: VirtualManType;
}
export function scriptLiveVirtualManPlugin(
  options: IPluginOptions
): SinglePlugin {
  return (conf: PlayConfig) => {
    const {
      script,
      key,
      timelineIndex,
      baseFn,
      commonStyle,
      duration = () => 0,
      isSkip = () => false,
      actualDuration = 0,
      offset = 0,
      componentName,
      virtualMan,
    } = options;
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (isSkip?.(view, index)) continue;
      const { defaultDip, defaultVoiceItem } = baseFn(view);
      let tagInfo = {
        tag: [] as string[],
      };
      if (defaultDip) {
        try {
          tagInfo = JSON.parse(defaultDip.dip_feature_keywords);
        } catch (e) {}
      }
      const component = customVirtualManWave(
        key,
        '',
        commonStyle,
        {
          ...virtualMan,
          __env: 'ams',
          // 存在默认选中的数字人，进行设置
          ...(defaultDip
            ? {
                key: defaultDip.virtual_man_key || defaultDip.dip_id,
                asset: !!defaultDip.virtual_man_key,
                img: defaultDip.dip_image,
                label: defaultDip.dip_name,
                appkey: defaultDip.platform_account_id,
                type:
                  VirtualManPlatformToMaterialType[defaultDip.platform] ||
                  defaultDip.platform,
                // 实景数字人不需要绿幕抠图
                ...(tagInfo.tag.includes('background')
                  ? {
                      chromaKey: 0,
                    }
                  : undefined),
              }
            : undefined),
        },
        {
          isSegmentTexts: true,
          voiceConfig: {
            currentVoiceItem: {
              platform: 'azure',
              speed: 1,
              voiceExtendConfig:
                '{"ShortName":"zh-CN-XiaochenNeural","Gender":"Female","Locale":"zh-CN"}',
              voiceId: 'zh-CN-XiaochenNeural',
              ...(defaultVoiceItem
                ? {
                    platform: defaultVoiceItem.platform,
                    speed: defaultVoiceItem.speed,
                    voiceExtendConfig: defaultVoiceItem.voiceExtendConfig,
                    voiceId: defaultVoiceItem.voiceId,
                    driverMode: defaultVoiceItem.driverMode || 'voice',
                  }
                : undefined),
            },
          },
        }
      );

      Object.assign(component.data, {
        __component_name: componentName.name,
        __component_sub_name: componentName.subName,
      });
      conf.timeline[timelineIndex].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component,
        duration: duration(view),
        id: uuid(),
        key,
        offset: 0,
      };
    }
  };
}
