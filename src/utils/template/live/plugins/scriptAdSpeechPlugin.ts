import { BaseScript } from '@/components/ScriptForm/type';
import { SinglePlugin } from '../..';
import { liveSpeechAD } from '@/utils/play-component';
import { allViews } from '@/utils/play-view';
import { uuid } from '@tencent/midas-util';
import { PlayConfig } from '@/type/pagedoo';
import { IBasePluginOptions } from '../../plugins/type';

export const scriptAdSpeechPlugin = (
  options: IBasePluginOptions
): SinglePlugin => {
  const { script, timelineIndex, key } = options;
  return (conf: PlayConfig) => {
    conf.timeline[timelineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveSpeechAD(
        key,
        'aigc',
        script.adExtendData?.productList
          ? script.adExtendData?.productList
          : [],
        allViews['广告联调模板'].hidden
      ),
      duration: 0,
      id: uuid(),
      key,
      offset: 0,
    };
  };
};
