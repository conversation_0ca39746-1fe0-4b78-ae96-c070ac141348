import { PlayConfig } from '@/type/pagedoo';
import { SinglePlugin } from '../..';
import { liveProduct, liveQA } from '@/utils/play-component';
import { allViews } from '@/utils/play-view';
import { uuid } from '@tencent/midas-util';
import { BaseScript } from '@/components/ScriptForm/type';
import { IPluginOptions } from '../types';

export function scriptLiveQAPlugin(options: IPluginOptions): SinglePlugin {
  return (conf: PlayConfig) => {
    const { script, key, timelineIndex, duration } = options;
    conf.timeline[timelineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveQA(
        key,
        [
          // {
          //   id: '234234',
          //   question: '这是一个问题',
          //   answer: '这是回答',
          // },
        ],
        allViews['广告联调模板'].hidden
      ),
      duration,
      id: uuid(),
      key,
      offset: 0,
    };
  };
}
