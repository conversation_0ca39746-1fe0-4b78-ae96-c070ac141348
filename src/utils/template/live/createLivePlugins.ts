import { Script } from '@/type/pagedoo';
import {
  scriptLiveSpeechPlugin,
  scriptVirtualManPlugin,
  scriptLiveBkgPlugin,
  scriptLiveImagePlugin,
  scriptLiveTextPlugin,
} from '../plugins';
import { AdTemplateConfigType } from './types';
import { hiddenStyle, safeKey } from './utils';
import { scriptLiveVirtualManPlugin } from './plugins/scriptLiveVirtualManPlugin';
import { ComponentList } from '../video/type';
import { COMPONENT_LIST } from '../video/constants';
import { BackFn } from '../video/plugin-init';
import { BaseScript } from '@/components/ScriptForm/type';
import { handleComponentConfig } from '../utils';
import { twoTimeToSeconds } from '../plugins/utils';
import { MatchVirtualMap } from './constants';
import { scriptLiveIdentifyPlugin } from './plugins/scriptLiveIdentifyPlugin';
export const createLivePlugins = (props: {
  pagedooScript: BaseScript;
  components: AdTemplateConfigType[string]['usage'];
  type: 'AD' | 'PAGEDOO';
  templateId: string;
}) => {
  const { pagedooScript, components, type, templateId } = props;
  const liveDuration = twoTimeToSeconds(
    pagedooScript.views.slice(-1)[0].duration.split('-')[1]
  );
  // 匹配出该环境下的数字人组件名
  const virtualComponentName = MatchVirtualMap!.virtualManComponentName;

  const pluginKeyMap: Partial<Record<ComponentList, BackFn>> = {
    // 前景
    [COMPONENT_LIST.LIVE_FOREGROUND]: ({
      componentConfig,
      componentStyle,
      timelineIndex,
    }) =>
      scriptLiveImagePlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({ componentConfig, view, pagedooScript })
          ) {
            // 没有资源的时候
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            // 如果有配置 单条轨道，说明是只渲染一个
            return true;
          }
          return false;
        },
        baseFn: (view) =>
          handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }) as string,
        duration: (view) => {
          if (componentConfig?.isSingle) {
            return liveDuration;
          }
          return 0;
        },
      }),
    // 话术
    [COMPONENT_LIST.LIVE_SPEECH]: ({ componentStyle, timelineIndex }) =>
      MatchVirtualMap.speechPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle ?? hiddenStyle,
      }),

    // 数字人
    [COMPONENT_LIST.VIRTUALMAN]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveVirtualManPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        // 不同模版对应不同数字人形象
        virtualMan: MatchVirtualMap!.virtualManMap[templateId],
        isSkip: (_, index) => {
          return index !== 0;
        },
        baseFn: () => {
          const defaultDip = pagedooScript.adExtendData?.defaultDipItem;
          const defaultVoiceItem =
            pagedooScript.adExtendData?.defaultVoiceItem ||
            MatchVirtualMap.voiceMap?.[templateId];
          return {
            defaultDip,
            defaultVoiceItem,
          };
        },
        duration: () => liveDuration,
        componentName: virtualComponentName,
      }),
    // 背景图
    [COMPONENT_LIST.LIVE_BKG]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveBkgPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          return (
            !handleComponentConfig({
              componentConfig,
              view,
              pagedooScript,
            }) || index !== 0
          );
        },
        baseFn: (view) =>
          (handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }) as string) || '',
        duration: () => {
          if (componentConfig?.isSingle) {
            return liveDuration;
          }
          return 0;
        },
      }),
    // 通用贴片
    [COMPONENT_LIST.LIVE_IMAGE]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveImagePlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({ componentConfig, view, pagedooScript })
          ) {
            // 没有资源的时候
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            // 如果有配置 单条轨道，说明是只渲染一个
            return true;
          }
          return false;
        },
        baseFn: (view) =>
          handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }) as string,
        duration: () => {
          if (componentConfig?.isSingle) {
            return liveDuration;
          }
          return 0;
        },
      }),
    // // 辅助文字
    [COMPONENT_LIST.LIVE_TEXT]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveTextPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({
              componentConfig,
              view,
              pagedooScript,
            })
          ) {
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            return true;
          }
          return false;
        },
        baseFn: (view) =>
          handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }) || '',
        getAnimationConfig: () => ({ animation: '', speed: 40 }),
        duration: () => {
          if (componentConfig?.isSingle) {
            return liveDuration;
          }
          return 0;
        },
        fontStyle: componentConfig?.fontStyle,
      }),
    // AI 主播标识
    [COMPONENT_LIST.LIVE_IDENTIFY]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveIdentifyPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({ componentConfig, view, pagedooScript })
          ) {
            // 没有资源的时候
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            // 如果有配置 单条轨道，说明是只渲染一个
            return true;
          }
          return false;
        },
        baseFn: (view) =>
          handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }) as string,
        duration: () => {
          if (componentConfig?.isSingle) {
            return liveDuration;
          }
          return 0;
        },
      }),
  };
  return components.map((component, index) =>
    pluginKeyMap[component.componentKey]?.({
      ...component,
      timelineIndex: index,
    })
  );
};
