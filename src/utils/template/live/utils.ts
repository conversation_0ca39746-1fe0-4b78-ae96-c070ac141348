import { CommonStyle } from '@tencent/pagedoo-library';
import { LiveTextType } from './types';
import { baseSize, commonStyle } from '@/utils/play-view';
import { PagedooFontStyle } from '@tencent/pagedoo-library/es/types/pagedoo-type';

export const hiddenStyle = {
  position: {
    top: -5000,
    left: -5000,
    bottom: 0,
    right: 0,
    type: 'absolute',
    outset: 'TopLeft',
    unit: {
      left: 'percent',
      right: 'percent',
      top: 'percent',
      bottom: 'percent',
    },
  },
} as CommonStyle;

export const defaultRate = 375 / 1242;
export const bkgRate = 455 / 1242;
export function transformFn(
  rate: number,
  safe: number,
  left: number,
  top: number,
  width: number
): CommonStyle {
  return commonStyle(safe + rate * left, rate * top, rate * width);
}
export const transformStyle = transformFn.bind(null, defaultRate, 40);
export const transformBkgStyle = transformFn.bind(null, bkgRate, 0);

export const fromResultWidth = (width: number, ratio: number) => {
  return ((width / 100) * baseSize.width) / ratio;
};
export const fromResultLeft = (left: number, ratio: number, safe = 0) => {
  return ((left / 100) * baseSize.width - safe) / ratio;
};
export const fromResultTop = (top: number, ratio: number) => {
  return (((top * 2) / 100) * baseSize.width) / ratio;
};

export const getFontStyle = (style: LiveTextType): PagedooFontStyle => {
  const rate = 315 / 1242;
  const {
    fontSize,
    fontFamily,
    color,
    textAlign,
    width,
    lineHeight = 2,
    fontWeight,
  } = style;
  return {
    fontSize: fontSize * rate,
    fontFamily: [fontFamily],
    color: {
      color,
      realColor: color,
      show: true,
    },
    useImage: 0,
    textAlign,
    lineHeight,
    width: width && width * rate,
    fontWeight,
  };
};

// components key ---> plugins
const KEY_START_INDEX = 3000;

export const safeKey = (timelineIndex: number) => {
  return Math.floor(Math.random() * 100000) + KEY_START_INDEX + timelineIndex;
};
