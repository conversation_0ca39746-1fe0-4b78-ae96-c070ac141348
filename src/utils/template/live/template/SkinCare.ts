import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';
import { IUsage } from '../../video/config';
import { COMPONENT_LIST } from '../../video/constants';
import { GLOBAL_FIELDS } from '../../type';
import { EcommerceSkinConfig } from '../config/EcommerceSkinCareConfig';
type IConfig = typeof EcommerceSkinConfig;
export const SkinCareComponentUsage = (conf: IConfig): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
      componentStyle: hiddenStyle,
    },
    // 背景图
    {
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: conf.style.bkg,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bkg,
      },
    },
    // 数字人
    {
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentStyle: conf.style.virtualMan,
    },
    // 前景
    {
      componentKey: COMPONENT_LIST.LIVE_FOREGROUND,
      componentStyle: conf.style.foreground,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.foreground,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.base,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p1,
      },
    },
    // 右侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.base,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p1,
      },
    },

    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p2,
      },
    },

    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p3,
      },
    },

    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p4,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p5,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p5,
      },
    },

    // 文字
    // 模版标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.templateName,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        isSingle: true,
        value: conf.components.templateName.text,
        fontStyle: conf.components.templateName.style,
      },
    },
    // 子标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.subTitle,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        isSingle: true,
        value: conf.components.subTitle.text,
        fontStyle: conf.components.subTitle.style,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t1.text,
        fontStyle: conf.components.product1.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t2.text,
        fontStyle: conf.components.product1.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t3.text,
        fontStyle: conf.components.product1.t3.style,
      },
    },

    // 右侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t1.text,
        fontStyle: conf.components.product2.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t2.text,
        fontStyle: conf.components.product2.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t3.text,
        fontStyle: conf.components.product2.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t4.text,
        fontStyle: conf.components.product2.t4.style,
      },
    },
    // AI标识
    {
      componentKey: COMPONENT_LIST.LIVE_IDENTIFY,
      componentStyle: conf.style.AI_identify,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.AI_identify,
      },
    },
  ];
};
