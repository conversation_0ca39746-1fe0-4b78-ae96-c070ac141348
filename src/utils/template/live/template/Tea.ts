import { IUsage } from '../../video/config';
import { COMPONENT_LIST } from '../../video/constants';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';
import { EcommerceTeaConfig } from '../config/EcommerceTeaConfig';
import { GLOBAL_FIELDS } from '../../type';
type IConfig = typeof EcommerceTeaConfig;
export const TeaComponentUsage = (conf: IConfig): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
      componentStyle: conf.style.liveSpeech,
    },
    // 背景
    {
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: conf.style.bkg,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bkg,
      },
    },
    // 数字人
    {
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentStyle: transformStyle(22, 487, 1198),
    },
    // 前景
    {
      componentKey: COMPONENT_LIST.LIVE_FOREGROUND,
      componentStyle: conf.style.foreground,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.foreground,
      },
    },
    // 小标题
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.subTitle.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.subTitle,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.base,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p2,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p3,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.base,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.p2,
      },
    },
    // 前景-绿色
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.foregroundGreen,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.foregroundGreen,
      },
    },
    // 底部图片
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.bottom.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bottom.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.bottom.p2,

      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bottom.p2,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.bottom.p3,

      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bottom.p3,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.bottom.p4,

      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bottom.p4,
      },
    },
    // 桌子上产品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods,
      },
    },
    // 文字
    // 主标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.templateName,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        isSingle: true,
        value: conf.components.templateName.text,
        fontStyle: conf.components.templateName.style,
      },
    },
    // 副标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.subTitle.text,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        isSingle: true,
        value: conf.components.subTitle.text,
        fontStyle: conf.components.subTitle.style,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t1.text,
        fontStyle: conf.components.product1.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t2.text,
        fontStyle: conf.components.product1.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t3.text,
        fontStyle: conf.components.product1.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t4.text,
        fontStyle: conf.components.product1.t4.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t5,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t5.text,
        fontStyle: conf.components.product1.t5.style,
      },
    },
    // 右侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t1.text,
        fontStyle: conf.components.product2.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t2.text,
        fontStyle: conf.components.product2.t2.style,
      },
    },
    // 底部文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.bottom.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.bottom.t1.text,
        fontStyle: conf.components.bottom.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.bottom.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.bottom.t2.text,
        fontStyle: conf.components.bottom.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.bottom.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.bottom.t3.text,
        fontStyle: conf.components.bottom.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.bottom.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.bottom.t4.text,
        fontStyle: conf.components.bottom.t4.style,
      },
    },
    // AI标识
    {
      componentKey: COMPONENT_LIST.LIVE_IDENTIFY,
      componentStyle: conf.style.AI_identify,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.AI_identify,
      },
    },
  ];
};
