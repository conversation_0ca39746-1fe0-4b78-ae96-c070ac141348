import { GLOBAL_FIELDS } from '../../type';
import { IUsage } from '../../video/config';
import { COMPONENT_LIST } from '../../video/constants';
import { EcommerceBooksDarkConfig } from '../config/EcommerceBooksDarkConfig';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';
type IConfig = typeof EcommerceBooksDarkConfig;
export const BooksDarkComponentUsage = (conf: IConfig): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
      componentStyle: conf.style.liveSpeech,
    },
    // 背景
    {
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: conf.style.bkg,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bkg,
      },
    },
    // 前景
    {
      componentKey: COMPONENT_LIST.LIVE_FOREGROUND,
      componentStyle: conf.style.foreground,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.foreground,
      },
    },
    // 数字人
    {
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentStyle: conf.style.virtualMan,
    },
    // 介绍
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.base,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p0,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p2,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p3,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p4,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product.p0,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product.p2,
      },
    },
    // 右侧讲师信息
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.teacher.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.teacher.p0,
      },
    },
    // 桌子上的产品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods.p0,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods.p2,
      },
    },
    // 文字
    // 模版标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.templateName,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        isSingle: true,
        value: conf.components.templateName.text,
        fontStyle: conf.components.templateName.style,
      },
    },
    // 副标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.subTitle,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        isSingle: true,
        value: conf.components.subTitle.text,
        fontStyle: conf.components.subTitle.style,
      },
    },
    // 介绍文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t0.text,
        fontStyle: conf.components.introduction.t0.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t1.text,
        fontStyle: conf.components.introduction.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t2.text,
        fontStyle: conf.components.introduction.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t3.text,
        fontStyle: conf.components.introduction.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t4.text,
        fontStyle: conf.components.introduction.t4.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t5,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t5.text,
        fontStyle: conf.components.introduction.t5.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t6,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t6.text,
        fontStyle: conf.components.introduction.t6.style,
      },
    },
    // 讲师文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.teacher.t0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.teacher.t0.text,
        fontStyle: conf.components.teacher.t0.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.teacher.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.teacher.t1.text,
        fontStyle: conf.components.teacher.t1.style,
      },
    },
    // 左侧商品文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product.t0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product.t0.text,
        fontStyle: conf.components.product.t0.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product.t1.text,
        fontStyle: conf.components.product.t1.style,
      },
    },
    // AI标识
    {
      componentKey: COMPONENT_LIST.LIVE_IDENTIFY,
      componentStyle: conf.style.AI_identify,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.AI_identify,
      },
    },
  ];
};
