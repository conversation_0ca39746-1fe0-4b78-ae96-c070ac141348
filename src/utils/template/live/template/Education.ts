import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';
import { IUsage } from '../../video/config';
import { COMPONENT_LIST } from '../../video/constants';
import { EcommerceEducationConfig } from '../config/EcommerceEducationConfig';
import { GLOBAL_FIELDS } from '../../type';
type IConfig = typeof EcommerceEducationConfig;
export const EducationComponentUsage = (conf: IConfig): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
      componentStyle: conf.style.liveSpeech,
    },
    // 背景
    {
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: conf.style.bkg,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bkg,
      },
    },
    // 数字人
    {
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentStyle: conf.style.virtualMan,
    },
    // 前景
    {
      componentKey: COMPONENT_LIST.LIVE_FOREGROUND,
      componentStyle: conf.style.foreground,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.foreground,
      },
    },
    // 右侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product2.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product2.base,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.base,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p2,
      },
    },
    // 介绍
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.base,
      },
    },
    // 副标题图片
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.subTitle.base,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.subTitle,
      },
    },
    // 桌子上商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.table,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.table,
      },
    },
    // 文字
    // 主标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.templateName,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        isSingle: true,
        value: conf.components.templateName.text,
        fontStyle: conf.components.templateName.style,
      },
    },
    // 副标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.subTitle.t0,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        isSingle: true,
        value: conf.components.subTitle.text,
        fontStyle: conf.components.subTitle.style,
      },
    },
    // 介绍文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t1.text,
        fontStyle: conf.components.introduction.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t2.text,
        fontStyle: conf.components.introduction.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t3.text,
        fontStyle: conf.components.introduction.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t4.text,
        fontStyle: conf.components.introduction.t4.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t5,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t5.text,
        fontStyle: conf.components.introduction.t5.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t6,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t6.text,
        fontStyle: conf.components.introduction.t6.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t7,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t7.text,
        fontStyle: conf.components.introduction.t7.style,
      },
    },
    // 左侧商品文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t1.text,
        fontStyle: conf.components.product1.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t2.text,
        fontStyle: conf.components.product1.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t3.text,
        fontStyle: conf.components.product1.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t4.text,
        fontStyle: conf.components.product1.t4.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t5,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t5.text,
        fontStyle: conf.components.product1.t5.style,
      },
    },
    // 右侧商品文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t1.text,
        fontStyle: conf.components.product2.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product2.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product2.t2.text,
        fontStyle: conf.components.product2.t2.style,
      },
    },
    // AI标识
    {
      componentKey: COMPONENT_LIST.LIVE_IDENTIFY,
      componentStyle: conf.style.AI_identify,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.AI_identify,
      },
    },
  ];
};
