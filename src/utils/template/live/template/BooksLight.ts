import { GLOBAL_FIELDS } from '../../type';
import { IUsage } from '../../video/config';
import { COMPONENT_LIST } from '../../video/constants';
import { EcommerceBooksLightConfig } from '../config/EcommerceBooksLightConfig';
import {
  getFontStyle,
  hiddenStyle,
  transformBkgStyle,
  transformStyle,
} from '../utils';
type IConfig = typeof EcommerceBooksLightConfig;
export const BooksLightComponentUsage = (conf: IConfig): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
      componentStyle: conf.style.liveSpeech,
    },
    // 背景
    {
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: conf.style.bkg,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.bkg,
      },
    },
    // 数字人
    {
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentStyle: conf.style.virtualMan,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
      },
    },
    // 前景
    {
      componentKey: COMPONENT_LIST.LIVE_FOREGROUND,
      componentStyle: conf.style.foreground,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.foreground,
      },
    },
    // 介绍
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p0,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p2,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p3,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.introduction.p4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.introduction.p4,
      },
    },
    // 左侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p0,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.product1.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.product1.p2,
      },
    },
    // 右侧商品
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.teacher.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.teacher,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods.p0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods.p0,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods.p1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods.p1,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: conf.style.goods.p2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.goods.p2,
      },
    },
    // 标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.templateName,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        isSingle: true,
        value: conf.components.templateName.text,
        fontStyle: conf.components.templateName.style,
      },
    },
    // 副标题
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.subTitle,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'global',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        isSingle: true,
        value: conf.components.subTitle.text,
        fontStyle: conf.components.subTitle.style,
      },
    },
    // 介绍文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t0.text,
        fontStyle: conf.components.introduction.t0.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t1.text,
        fontStyle: conf.components.introduction.t1.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t2,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t2.text,
        fontStyle: conf.components.introduction.t2.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t3,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t3.text,
        fontStyle: conf.components.introduction.t3.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t4,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t4.text,
        fontStyle: conf.components.introduction.t4.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t5,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t5.text,
        fontStyle: conf.components.introduction.t5.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t6,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t6.text,
        fontStyle: conf.components.introduction.t6.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.introduction.t7,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.introduction.t7.text,
        fontStyle: conf.components.introduction.t7.style,
      },
    },
    // 左侧商品文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t0.text,
        fontStyle: conf.components.product1.t0.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.product1.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.product1.t1.text,
        fontStyle: conf.components.product1.t1.style,
      },
    },
    // 讲师文字
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.teacher.t0,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.teacher.t0.text,
        fontStyle: conf.components.teacher.t0.style,
      },
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: conf.style.teacher.t1,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.components.teacher.t1.text,
        fontStyle: conf.components.teacher.t1.style,
      },
    },
    // AI标识
    {
      componentKey: COMPONENT_LIST.LIVE_IDENTIFY,
      componentStyle: conf.style.AI_identify,
      componentConfig: {
        componentConfigCommonType: 'template',
        isSingle: true,
        value: conf.materialMap.AI_identify,
      },
    },
  ];
};
