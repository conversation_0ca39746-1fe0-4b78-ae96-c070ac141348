import { <PERSON><PERSON><PERSON> } from '@/type/pagedoo';
import { SkinCareComponentUsage } from './template/SkinCare';
import { ECOMMERCE_TEMPLATE_ENUM } from './constants';
import { viewMocker } from '../mock/utils';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { GLOBAL_FIELDS, VIDEO_DEFAULT_SIZE } from '@/pages/Question/constant';
import { PlantsComponentUsage } from './template/Plants';
import { EducationComponentUsage } from './template/Education';
import { BooksLightComponentUsage } from './template/BooksLight';
import { BooksDarkComponentUsage } from './template/BooksDark';
import { TeaComponentUsage } from './template/Tea';
import { AdTemplateConfigType } from './types';
import { baseMockScript } from './mock/baseMockScript';
import { EcommerceSkinConfig } from './config/EcommerceSkinCareConfig';
import { EcommerceTeaConfig } from './config/EcommerceTeaConfig';
import { EcommerceEducationConfig } from './config/EcommerceEducationConfig';
import { EcommercePlantsConfig } from './config/EcommercePlantsConfig';
import { EcommerceBooksDarkConfig } from './config/EcommerceBooksDarkConfig';
import { EcommerceBooksLightConfig } from './config/EcommerceBooksLightConfig';
import { BlankComponentUsage } from './template/Blank';
import { EcommerceBlankConfig } from './config/EcommerceBlankConfig';
export const EcommerceTemplateConfig: AdTemplateConfigType = {
  [ECOMMERCE_TEMPLATE_ENUM.skincare]: {
    usage: SkinCareComponentUsage(EcommerceSkinConfig),
    mockScript: baseMockScript,
  },
  [ECOMMERCE_TEMPLATE_ENUM.tea]: {
    usage: TeaComponentUsage(EcommerceTeaConfig),
    mockScript: baseMockScript,
  },
  [ECOMMERCE_TEMPLATE_ENUM.booksDark]: {
    usage: BooksDarkComponentUsage(EcommerceBooksDarkConfig),
    mockScript: baseMockScript,
  },
  [ECOMMERCE_TEMPLATE_ENUM.booksLight]: {
    usage: BooksLightComponentUsage(EcommerceBooksLightConfig),
    mockScript: baseMockScript,
  },
  [ECOMMERCE_TEMPLATE_ENUM.plants]: {
    usage: PlantsComponentUsage(EcommercePlantsConfig),
    mockScript: baseMockScript,
  },
  [ECOMMERCE_TEMPLATE_ENUM.education]: {
    usage: EducationComponentUsage(EcommerceEducationConfig),
    mockScript: baseMockScript,
  },
  [ECOMMERCE_TEMPLATE_ENUM.blank]: {
    usage: BlankComponentUsage(EcommerceBlankConfig),
    mockScript: baseMockScript,
  },
  // TODO:临时兼容旧模版
  skincare: {
    usage: SkinCareComponentUsage(EcommerceSkinConfig),
    mockScript: baseMockScript,
  },
  tea: {
    usage: TeaComponentUsage(EcommerceTeaConfig),
    mockScript: baseMockScript,
  },
  books_dark: {
    usage: BooksDarkComponentUsage(EcommerceBooksDarkConfig),
    mockScript: baseMockScript,
  },
  books_light: {
    usage: BooksLightComponentUsage(EcommerceBooksLightConfig),
    mockScript: baseMockScript,
  },
  plants: {
    usage: PlantsComponentUsage(EcommercePlantsConfig),
    mockScript: baseMockScript,
  },
  education: {
    usage: EducationComponentUsage(EcommerceEducationConfig),
    mockScript: baseMockScript,
  },
};
