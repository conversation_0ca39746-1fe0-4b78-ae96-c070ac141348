import {
  ad_xiao<PERSON>i,
  ad_xiao<PERSON>u,
  ad_xiaoMei,
  xiaoLi,
  xiaoMei,
  xiao<PERSON>ing,
  xiaoXin,
  xiaoYu,
  ad_xiao<PERSON>i,
  ad_xiaoNing,
  ad_xiaoXin,
  xiaoAi,
} from './virtualMan';
import type { TemplateRuntimeConfigType, VirtualManType } from './types';
import { scriptLiveSpeechPlugin } from '../plugins/scriptLiveSpeechPlugin';
import { hiddenStyle } from './utils';
import { scriptAdSpeechPlugin } from './plugins/scriptAdSpeechPlugin';
import { ad_voice_shenxia<PERSON> } from './liveVoiceConfig';

export enum ECOMMERCE_TEMPLATE_ENUM {
  // 美妆护肤
  skincare = '49e94a62-4980-407c-af19-8922fa7b9a85',
  // 图书(深色)
  booksDark = 'd58626fd-d898-4df7-bfa2-f43f7b29a02d',
  // 图书(浅色)
  booksLight = '7460f1c4-2838-454f-baf9-6d201225edb5',
  // 绿植
  plants = '038bae83-34b1-4a33-9adb-0384f08f58ce',
  // 茶叶
  tea = 'c97d2e9b-0de8-48c7-a098-2dbb23e4a664',
  // 教育
  education = '6f4890d4-459d-4c56-a298-e033c0a75871',
  // 空白模版
  blank = 'blank',
}
export const templateNameMap: Record<string, string> = {
  skincare: '美妆护肤',
  tea: '茶叶',
  books_light: '图书（浅色）',
  books_dark: '图书',
  education: '教育',
  plants: '鲜花绿植',
  [ECOMMERCE_TEMPLATE_ENUM.booksDark]: '图书',
  [ECOMMERCE_TEMPLATE_ENUM.education]: '教育',
  [ECOMMERCE_TEMPLATE_ENUM.tea]: '茶叶',
  [ECOMMERCE_TEMPLATE_ENUM.skincare]: '美妆护肤',
  [ECOMMERCE_TEMPLATE_ENUM.plants]: '鲜花绿植',
  [ECOMMERCE_TEMPLATE_ENUM.booksLight]: '图书（浅色）',
  [ECOMMERCE_TEMPLATE_ENUM.blank]: '无模版',
  // 临时逻辑：针对特定商品id，使用与之对应的炫酷的直播间装修
  mildewCleaner: '除霉剂',
};

export const TemplateRuntimeConfig: TemplateRuntimeConfigType = {
  AD: {
    virtualManComponentName: {
      name: '主播',
      subName: '主播',
    },
    virtualManMap: {
      [ECOMMERCE_TEMPLATE_ENUM.booksDark]: ad_xiaoYu,
      [ECOMMERCE_TEMPLATE_ENUM.education]: ad_xiaoMei,
      [ECOMMERCE_TEMPLATE_ENUM.tea]: ad_xiaoNing,
      [ECOMMERCE_TEMPLATE_ENUM.skincare]: ad_xiaoXin,
      [ECOMMERCE_TEMPLATE_ENUM.plants]: ad_xiaoLi,
      [ECOMMERCE_TEMPLATE_ENUM.booksLight]: ad_xiaoAi,
      // 临时逻辑：针对特定商品id，使用与之对应的炫酷的直播间装修
      [ECOMMERCE_TEMPLATE_ENUM.blank]: ad_xiaoXin,
      // TODO: 临时兼容旧版本
      books_dark: ad_xiaoYu,
      education: ad_xiaoMei,
      tea: ad_xiaoNing,
      skincare: ad_xiaoXin,
      plants: ad_xiaoLi,
      books_light: ad_xiaoAi,
    },
    voiceMap: {
      [ECOMMERCE_TEMPLATE_ENUM.blank]: ad_voice_shenxiaona,
      [ECOMMERCE_TEMPLATE_ENUM.booksDark]: ad_voice_shenxiaona,
      [ECOMMERCE_TEMPLATE_ENUM.education]: ad_voice_shenxiaona,
      [ECOMMERCE_TEMPLATE_ENUM.tea]: ad_voice_shenxiaona,
      [ECOMMERCE_TEMPLATE_ENUM.skincare]: ad_voice_shenxiaona,
      [ECOMMERCE_TEMPLATE_ENUM.plants]: ad_voice_shenxiaona,
      [ECOMMERCE_TEMPLATE_ENUM.booksLight]: ad_voice_shenxiaona,
      // TODO: 兼容配置
      books_dark: ad_voice_shenxiaona,
      education: ad_voice_shenxiaona,
      tea: ad_voice_shenxiaona,
      skincare: ad_voice_shenxiaona,
      plants: ad_voice_shenxiaona,
      books_light: ad_voice_shenxiaona,
    },
    speechPlugin: ({ commonStyle, timelineIndex, script, key }) =>
      scriptAdSpeechPlugin({
        script,
        timelineIndex,
        commonStyle,
        key,
      }),
  },
  PAGEDOO: {
    virtualManComponentName: {
      name: '数字人',
      subName: '数字人',
    },
    virtualManMap: {
      [ECOMMERCE_TEMPLATE_ENUM.booksDark]: xiaoYu,
      [ECOMMERCE_TEMPLATE_ENUM.education]: xiaoMei,
      [ECOMMERCE_TEMPLATE_ENUM.tea]: xiaoNing,
      [ECOMMERCE_TEMPLATE_ENUM.skincare]: xiaoXin,
      [ECOMMERCE_TEMPLATE_ENUM.plants]: xiaoLi,
      [ECOMMERCE_TEMPLATE_ENUM.booksLight]: xiaoAi,
    },
    speechPlugin: ({ commonStyle, timelineIndex, script, key }) =>
      scriptLiveSpeechPlugin({
        script,
        key,
        timelineIndex,
        commonStyle: commonStyle ?? hiddenStyle,
        isSkip: (_, index) => false,
        getSpeechParams: ({ view }) => ({
          speechData: {
            type: 'text',
            text: view.speech || '',
          } as const,
          speechDrive: { type: 'virtualman' },
          remixAnswer: {
            enabled: true,
            block: false,
            separator: ['。', '，', '！', '？'],
          },
        }),
        duration: (view) => {
          return 0;
        },
      }),
  },
};
// 本地测试环境使用下面
TemplateRuntimeConfig.debug = TemplateRuntimeConfig.PAGEDOO;

export const MatchVirtualMap =
  TemplateRuntimeConfig[import.meta.env.VITE_RUNNING_SYSTEM]!;
