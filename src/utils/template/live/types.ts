import { SinglePlugin } from '..';
import { IUsage } from '../video/config';
import { CommonStyle } from '@tencent/pagedoo-library';
import { BaseScript } from '@/components/ScriptForm/type';
import { xiaoAi } from './virtualMan';
import { IVoiceItem } from '@/pages/VoiceList/VoiceLibrary/type';

export type EnumToUnion<T extends object> = T[keyof T];
export type BackFn = (arg: IUsage & { timelineIndex: number }) => SinglePlugin;
export type AdTemplateConfigType = {
  [key in string]: {
    /**
     * 样式配置
     */
    // config: IConfig;
    /**
     * 数组顺序就是层级关系
     */
    usage: IUsage[];
    mockScript: BaseScript;
  };
};

export type LiveTextType = {
  fontSize: number;
  fontFamily: string;
  color: string;
  width?: number;
  textAlign?: 'center' | 'left' | 'right';
  lineHeight?: number;
  fontWeight?: number;
};

export type VirtualManType = typeof xiaoAi;

export type ITemplateConfig = {
  virtualManComponentName: {
    name: string;
    subName: string;
  };
  virtualManMap: Record<string, VirtualManType>;
  /**
   * 按模版配置的音色
   */
  voiceMap?: Record<string, IVoiceItem>;
  speechPlugin(props: {
    timelineIndex: number;
    commonStyle: CommonStyle;
    key: number;
    script: BaseScript;
  }): SinglePlugin;
};

export type TemplateRuntimeConfigType = {
  [key in Exclude<
    ImportMetaEnv['VITE_RUNNING_SYSTEM'],
    'debug'
  >]: ITemplateConfig;
} & {
  debug?: ITemplateConfig;
};

export interface IPluginOptions {
  script: BaseScript;
  key: number;
  timelineIndex: number;
  duration: number;
}
