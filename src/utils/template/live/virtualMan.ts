// 聚合版线数字人形象
export const xiaoYu = {
  key: '8bd7129283044cceaeda8fe94eaa776b',
  asset: true,
  appkey: '86f087fad7024efb82269070beed51db',
  type: 'qq',
  enabled: true,
  img: 'https://pagedoo.pay.qq.com/material/@platform/1783e10e7be903b1d5c39470beef43a8.png',
  label: '小玉-格子短裙-黑色圆桌',
  desc: '',
  chromaKey: 0.5,
};

export const xiaoMei = {
  key: '11517070d8ab45f29fa1b224e81ffef0',
  asset: true,
  appkey: '86f087fad7024efb82269070beed51db',
  type: 'qq',
  enabled: true,
  img: 'https://pagedoo.pay.qq.com/material/@platform/ebaa25b584fbe67a8fc5781cc4893b3f.png',
  label: '小美-米色连衣裙-白色桌子',
  desc: '',
  chromaKey: 0.5,
};

export const xiaoNing = {
  key: 'c2532a804a5f4c0e9073d884532325d8',
  asset: true,
  appkey: '86f087fad7024efb82269070beed51db',
  type: 'qq',
  enabled: true,
  img: 'https://pagedoo.pay.qq.com/material/@platform/4a6d18bbac89e99776ced04ae243a59e.png',
  label: '小宁-碎花纱裙',
  desc: '',
  chromaKey: 0.5,
};

export const xiaoXin = {
  key: 'a3362ec5a4df409eb0af83cf8ef4c1fa',
  asset: true,
  appkey: '86f087fad7024efb82269070beed51db',
  type: 'qq',
  enabled: true,
  img: 'https://pagedoo.pay.qq.com/material/@platform/0844c01717b6478cbd1fcd8efb9a7621.png',
  label: '小欣-白色西装-白色桌子',
  desc: '',
  chromaKey: 0.5,
};

export const xiaoLi = {
  key: '90e06e5c2d824bac9b672245682041e8',
  asset: true,
  appkey: '86f087fad7024efb82269070beed51db',
  type: 'qq',
  enabled: true,
  img: 'https://pagedoo.pay.qq.com/material/@platform/7bc948db15ee26e4cecbcabc0f59ae86.png',
  label: '小丽-花艺围裙-白色桌子',
  desc: '',
  chromaKey: 0.5,
};

export const xiaoAi = {
  key: '11ac8dac59fe463c892fee0fb145c92e',
  asset: true,
  appkey: '86f087fad7024efb82269070beed51db',
  type: 'qq',
  enabled: true,
  img: 'https://pagedoo.pay.qq.com/material/@platform/8108ca0146529c09ff133a034c98b832.png',
  label: '小爱-条纹毛衣-单耳环白色桌子',
  desc: '',
  chromaKey: 0.5,
};

// 广告线数字人形象
/** 图书 - 数字人 */
export const ad_xiaoYu = {
  key: '644e5fdac6edc0bda2a5838ea7c0ce64',
  asset: true,
  appkey: '1576283ab39a49419138d5ee8ee40586',
  type: 'qq',
  enabled: true,
  img: 'https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/1660/22092/21514/model_30155_20250116113752/preview.png',
  label: '小玉-格子短裙-黑色圆桌',
  use_virtual_man_cover: true,
  desc: '',
  chromaKey: 0.5,
};

/** 教育 - 数字人 */
export const ad_xiaoMei = {
  key: '644e5fdac6edc0bda2a5838ea7c0ce64',
  asset: true,
  appkey: '1576283ab39a49419138d5ee8ee40586',
  type: 'qq',
  enabled: true,
  img: 'https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/1660/22092/21514/model_30155_20250116113752/preview.png',
  label: '小美-米色连衣裙-白色桌子',
  use_virtual_man_cover: true,
  desc: '',
  chromaKey: 0.5,
};

/** 茶- 主播 */
export const ad_xiaoNing = {
  key: '644e5fdac6edc0bda2a5838ea7c0ce64',
  asset: true,
  appkey: '1576283ab39a49419138d5ee8ee40586',
  type: 'qq',
  enabled: true,
  img: 'https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/1660/22092/21514/model_30155_20250116113752/preview.png',
  label: '小宁-碎花纱裙',
  use_virtual_man_cover: true,
  desc: '',
  chromaKey: 0.5,
};

/** 空白模版 - 数字人 */
export const ad_xiaoXin = {
  key: '644e5fdac6edc0bda2a5838ea7c0ce64',
  asset: true,
  appkey: '1576283ab39a49419138d5ee8ee40586',
  type: 'qq',
  enabled: true,
  img: 'https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/1660/22092/21514/model_30155_20250116113752/preview.png',
  use_virtual_man_cover: true,
  label: '小欣-白色西装-白色桌子',
  desc: '',
  chromaKey: 0.5,
};

/** 绿植 - 数字人 */
export const ad_xiaoLi = {
  key: '644e5fdac6edc0bda2a5838ea7c0ce64',
  asset: true,
  appkey: '1576283ab39a49419138d5ee8ee40586',
  type: 'qq',
  enabled: true,
  img: 'https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/1660/22092/21514/model_30155_20250116113752/preview.png',
  label: '小丽-花艺围裙-白色桌子',
  use_virtual_man_cover: true,
  desc: '',
  chromaKey: 0.5,
};

/** 图书浅 - 数字人 */
export const ad_xiaoAi = {
  key: '644e5fdac6edc0bda2a5838ea7c0ce64',
  asset: true,
  appkey: '1576283ab39a49419138d5ee8ee40586',
  type: 'qq',
  enabled: true,
  img: 'https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/1660/22092/21514/model_30155_20250116113752/preview.png',
  label: '小爱-条纹毛衣-单耳环白色桌子',
  use_virtual_man_cover: true,
  desc: '',
  chromaKey: 0.5,
};
