import { Script } from '@/type/pagedoo';
import { durationWithNumber, format, initPlugin, livePlugin } from '../common';

import { EcommerceTemplateConfig } from './ecommerceTemplateConfig';
import { cloneDeep, isEmpty } from 'lodash-es';
import { createLivePlugins } from './createLivePlugins';
import { scriptLiveProductPlugin } from './plugins/scriptLiveProductPlugin';
import { scriptLiveQAPlugin } from './plugins/scriptLiveQAPlugin';
import { durationPlugin } from '../video/utils-plugins';
import { BaseScript } from '@/components/ScriptForm/type';
import { adMockScript } from './mock/adMockScript';
import { safeKey } from './utils';
import { IPluginOptions } from './types';

export const initLiveScriptTemplate = (
  pagedooScript: BaseScript,
  templateId: string,
  type: 'AD' | 'PAGEDOO'
) => {
  //   const { plugins, timelineLength } = createAdPlugins(script);
  // 模版配置
  const {
    // config: templateConfig,
    usage: templateUsage,
    mockScript,
  } = EcommerceTemplateConfig[templateId];
  // mock 生成的 script
  let copyPagedooScript;
  if (isEmpty(pagedooScript)) {
    // 聚合版无脚本直播创建
    copyPagedooScript = cloneDeep(mockScript);
  } else {
    // 聚合版有脚本直播创建 ｜｜ 也可能是广告直播创建
    copyPagedooScript = cloneDeep(pagedooScript);
  }

  // 广告直播创建没有views，赋值
  if (type === 'AD' || isEmpty(copyPagedooScript.views)) {
    copyPagedooScript.views = adMockScript.views;
  }

  // 赋值到脚本的globalSpeech
  let globalSpeech = '';
  copyPagedooScript.views.forEach((view) => (globalSpeech += view.speech));
  copyPagedooScript.globalSpeech = globalSpeech;

  // 通用plugins
  const plugins = createLivePlugins({
    pagedooScript: copyPagedooScript,
    components: templateUsage,
    type,
    templateId,
  });

  // 广告特有的plugins
  const adPlugins = () => {
    if (type === 'AD') {
      return [
        scriptLiveProductPlugin({
          script: copyPagedooScript,
          key: safeKey(templateUsage.length),
          timelineIndex: templateUsage.length,
          duration: 0,
        }),
        scriptLiveQAPlugin({
          script: copyPagedooScript,
          key: safeKey(templateUsage.length + 1),
          timelineIndex: templateUsage.length + 1,
          duration: 0,
        }),
      ];
    }
    return [];
  };

  // 初始化timeline总数
  const initLength = (type: string) => {
    if (type === 'AD') {
      return templateUsage.length + 2;
    }
    return templateUsage.length;
  };

  const durationPlugins = [];
  if (type === 'AD') {
    durationPlugins.push(durationWithNumber({ duration: 20000 }));
  } else {
    durationPlugins.push(durationPlugin(copyPagedooScript));
  }

  return [
    // 初始化配置项 + 2 预留 问答库，liveProduct
    initPlugin(copyPagedooScript, initLength(type)),
    // 通用plugins
    ...plugins,
    // 广告特有的plugins
    ...adPlugins(),
    ...durationPlugins,
    // durationPlugin(copyPagedooScript),
    // durationWithNumber({ duration: 20000 }),
    livePlugin,
    format,
  ];
};
