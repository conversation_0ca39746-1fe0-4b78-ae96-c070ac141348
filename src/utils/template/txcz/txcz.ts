import uuid from '@tencent/midas-util/lib/uuid';
import { cloneDeep, has, uniq } from 'lodash-es';
import {
  COMMENT_MODE_KEYWORD,
  CSV_PREFIX,
  CURRENT_LANGUAGE,
  LIVE_PLAY_PULL_KEYWORD,
  SCRIPT,
  VIDEO_BKG_URL,
  VIDEO_ORIENTATION,
  VIRTUALMAN,
} from './constants';
import {
  findNonEmptyStringIndex,
  formatExcelRowData,
  formatExcelWord,
  formatName,
  getExcelText,
  getMaterialInfo,
  getParts,
  getVirtualmanPose,
  isFullScreenMode,
  isSubVirtualmanMode,
} from './utils';

import {
  IExcelParsedData,
  MATERIAL_MAP,
  MATERIAL_TYPES,
  materialKeyChineseNames,
  VIRTUALMAN_POSE,
} from './type';

// 从Excel复制的台词设计
const EXCEL_STRING = SCRIPT;

export const MATERIAL_BASE_URL = `https://pagedoo-sandbox-cn-1259340503.cos.ap-hongkong.myqcloud.com/uploads/live/202505231700_${CURRENT_LANGUAGE}`;//

const VIDEO_URLS: Record<string, Record<string, string>> = {
  ar: {
    '527商业化视频1.mp4': 'https://pagedoo.pay.qq.com/material/@platform/bae132d4998e03404c90f4e411d39408.mp4',
    '527商业化视频2.mp4': 'https://pagedoo.pay.qq.com/material/@platform/81a67c3111f78d9f4f563df23e100dcc.mp4',
    '527商业化视频3.mp4': 'https://pagedoo.pay.qq.com/material/@platform/16b1aea71973167dfd6bdf94c9394a8c.mp4',
    '527游戏内橙装效果.mp4': 'https://pagedoo.pay.qq.com/material/@platform/fafdf807f76070f459832b5d4b2d0b8d.mp4',
    '视频_开场白_1.mp4': 'https://pagedoo.pay.qq.com/material/@platform/c66343a7668a77db6f9bd7f302ee5e38.mp4',
    '直播教程视频.mp4': 'https://pagedoo.pay.qq.com/material/@platform/89857f14dc27a2d969ee95a9d4ffb711.mp4',
    'Bağdat.mp4': 'https://pagedoo.pay.qq.com/material/@platform/176ea82fcb9550b8f43a1411ac45e8f4.mp4',
    'Lamma Bada Yatathanna.mp4': 'https://pagedoo.pay.qq.com/material/@platform/2385343331ad9d8365662bcd72c84980.mp4',
    'Moon River.mp4': 'https://pagedoo.pay.qq.com/material/@platform/01bdd99d2655842fa2f965d285e56295.mp4',
    'The Golden Touch.mp4': 'https://pagedoo.pay.qq.com/material/@platform/10f0b3e40a6b950040e9259ce839d654.mp4',
    'What a Wonderful World.mp4': 'https://pagedoo.pay.qq.com/material/@platform/b22e03b176ae5a33835eb79326c81e36.mp4',
  },
  en: {
    '527商业化视频1.mp4': 'https://pagedoo.pay.qq.com/material/@platform/414ca20950194c11581751f96680b68d.mp4',
    '527商业化视频2.mp4': 'https://pagedoo.pay.qq.com/material/@platform/81a67c3111f78d9f4f563df23e100dcc.mp4',
    '527商业化视频3.mp4': 'https://pagedoo.pay.qq.com/material/@platform/16b1aea71973167dfd6bdf94c9394a8c.mp4',
    '527游戏内橙装效果.mp4': 'https://pagedoo.pay.qq.com/material/@platform/fafdf807f76070f459832b5d4b2d0b8d.mp4',
    '视频_开场白_1.mp4': 'https://pagedoo.pay.qq.com/material/@platform/05c8600f3529491358163a1a6a1e21f8.mp4',
    'Bağdat.mp4': 'https://pagedoo.pay.qq.com/material/@platform/4a5d67a2c11ca8976702eac3e8c1a699.mp4',
    '直播教程视频.mp4': 'https://pagedoo.pay.qq.com/material/@platform/f1085805d40a9779602054c164d52fa7.mp4',
    'Lamma Bada Yatathanna.mp4': 'https://pagedoo.pay.qq.com/material/@platform/ce1186e65db821a64c278b38ae46fff3.mp4',
    'Moon River.mp4': 'https://pagedoo.pay.qq.com/material/@platform/4a731c1161df8a358bbf4ea9243d2613.mp4',
    'The Golden Touch.mp4': 'https://pagedoo.pay.qq.com/material/@platform/3e6f8e8ebb33b34848694180c0d314c9.mp4',
    'What a Wonderful World.mp4': 'https://pagedoo.pay.qq.com/material/@platform/ea310fd22bc9059e95072bd2b93dbcd7.mp4',
  },
  tr: {
    '527商业化视频1.mp4': 'https://pagedoo.pay.qq.com/material/@platform/c03c08d5b6fff440ef9c27b7270bc259.mp4',
    '527商业化视频2.mp4': 'https://pagedoo.pay.qq.com/material/@platform/81a67c3111f78d9f4f563df23e100dcc.mp4',
    '527商业化视频3.mp4': 'https://pagedoo.pay.qq.com/material/@platform/16b1aea71973167dfd6bdf94c9394a8c.mp4',
    '527游戏内橙装效果.mp4': 'https://pagedoo.pay.qq.com/material/@platform/fafdf807f76070f459832b5d4b2d0b8d.mp4',
    '视频_开场白_1.mp4': 'https://pagedoo.pay.qq.com/material/@platform/d4ddce485d8b0fd193cef1543db19195.mp4',
    '直播教程视频.mp4': 'https://pagedoo.pay.qq.com/material/@platform/70d85cb46636c86f09c6a063d4555b49.mp4',
    'Bağdat.mp4': 'https://pagedoo.pay.qq.com/material/@platform/f7fe9c220e0bb71682542c07f0dd2d12.mp4',
    'Lamma Bada Yatathanna.mp4': 'https://pagedoo.pay.qq.com/material/@platform/f67470d2b41cc0c28a9a2981abd145e4.mp4',
    'Moon River.mp4': 'https://pagedoo.pay.qq.com/material/@platform/5be9c25895324d6a5605b921f8c23983.mp4',
    'The Golden Touch.mp4': 'https://pagedoo.pay.qq.com/material/@platform/1fa1e1bdb3a24e27be04aa86a79bcaa7.mp4',
    'What a Wonderful World.mp4': 'https://pagedoo.pay.qq.com/material/@platform/6f90fc19179979f75e6c795b23ee9f1f.mp4',
  }
}

// 根据totalPart完善materialUrls的key，再结合keyValu构造初始数据
const MATERIAL_URLS: Record<string, MATERIAL_MAP> = {
  开场白: {
    背景: [],
    前景: [],
    视频: [
      {
        ar: 'https://pagedoo.pay.qq.com/material/@platform/c66343a7668a77db6f9bd7f302ee5e38.mp4',
        en: 'https://pagedoo.pay.qq.com/material/@platform/05c8600f3529491358163a1a6a1e21f8.mp4',
        tr: 'https://pagedoo.pay.qq.com/material/@platform/d4ddce485d8b0fd193cef1543db19195.mp4',
      }[CURRENT_LANGUAGE] || '',
    ],
  },
  // 福袋活动: {
  //   背景: ['https://pagedoo.midasbuy.com/uploads/202502191940/%E8%83%8C%E6%99%AF.png'],
  //   前景: ['https://pagedoo.midasbuy.com/uploads/202502191940/%E5%89%8D%E6%99%AF.png'],
  // },
  '主流程第1轮': {
    // 视频: {
    //   ar: [
    //     'https://pagedoo.pay.qq.com/material/@platform/03ae5a0fd67b4ccc664fd26a0e5c2189.mp4',
    //     'https://pagedoo.pay.qq.com/material/@platform/b4606ba55bcb1ea964744dc690848694.mp4',
    //   ],
    //   en: [
    //     'https://pagedoo.pay.qq.com/material/@platform/03ae5a0fd67b4ccc664fd26a0e5c2189.mp4',
    //     'https://pagedoo.pay.qq.com/material/@platform/b4606ba55bcb1ea964744dc690848694.mp4',
    //   ],
    //   tr: [
    //     'https://pagedoo.pay.qq.com/material/@platform/03ae5a0fd67b4ccc664fd26a0e5c2189.mp4',
    //     'https://pagedoo.pay.qq.com/material/@platform/b4606ba55bcb1ea964744dc690848694.mp4',
    //   ],
    // }[CURRENT_LANGUAGE],
  },
  '主流程第2轮': {
    // 视频: {
    //   ar: [
    //     'https://pagedoo.pay.qq.com/material/@platform/f2cc183714ae9e0ce995bb73df7277a5.mp4',
    //     'https://pagedoo.pay.qq.com/material/@platform/860cc4f5cbda98f04838bf51ff27b981.mp4',
    //   ],
    //   en: [
    //     'https://pagedoo.pay.qq.com/material/@platform/f2cc183714ae9e0ce995bb73df7277a5.mp4',
    //     'https://pagedoo.pay.qq.com/material/@platform/860cc4f5cbda98f04838bf51ff27b981.mp4',
    //   ],
    //   tr: [
    //     'https://pagedoo.pay.qq.com/material/@platform/f2cc183714ae9e0ce995bb73df7277a5.mp4',
    //     'https://pagedoo.pay.qq.com/material/@platform/860cc4f5cbda98f04838bf51ff27b981.mp4',
    //   ],
    // }[CURRENT_LANGUAGE],
  },
  '主流程第3轮': {
    // 视频: {
    //   ar: [
    //     'https://pagedoo.pay.qq.com/material/@platform/3febd9f229e64d00f65afb71cacb2ff3.mp4',
    //   ],
    //   en: [
    //     'https://pagedoo.pay.qq.com/material/@platform/3febd9f229e64d00f65afb71cacb2ff3.mp4',
    //   ],
    //   tr:[
    //     'https://pagedoo.pay.qq.com/material/@platform/3febd9f229e64d00f65afb71cacb2ff3.mp4',
    //   ],
    // }[CURRENT_LANGUAGE],
  },
  打游戏: {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/a0b57b31dc13f9f1f955dac8888ccebd.mp4',
    // ],
  },
  结束语: {
    // 背景: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/d92592d3dfc9582aa63ce8d9feb6642b.png',
    // ],
    // 前景: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/50aff34afcf71d2187dc295b2f9c6693.png',
    // ],
  },
  AoEM: {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/0e9911852970cf8c8e9c3794fd759f8e.mp4',
    // ],
  },
  'Delta Force': {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/c7c6ae29a9c5621c1ba807a0db31106d.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/0b772a4907d0416305a8214c3858fddc.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/842d6aeb335b5f1a3727f38d2eb77146.mp4',
    // ],
  },
  HOK: {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/338a4f578793ab77de80efdf64d8c2bc.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/0c105b497221818d9b7927d5effcfe3e.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/fdd79fb19721a3e84c7fdb11ba024a45.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/4abb8fbb188b8d3f2dcbaafe8ab0181b.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/10edf3b9dbaeec095de1f88cab4aa6ab.mp4',
    // ],
  },
  'PUBG MOBILE 第1遍': {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/e7c38c4ffb2bcd157d41aa0941c6c8ab.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/62fc671e7953dd6e86fef001e9b3ca3d.mp4',
    // ],
  },
  'PUBG MOBILE 第2遍': {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/e7c38c4ffb2bcd157d41aa0941c6c8ab.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/62fc671e7953dd6e86fef001e9b3ca3d.mp4',
    // ],
  },
  'PUBG MOBILE第1轮': {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/e7c38c4ffb2bcd157d41aa0941c6c8ab.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/62fc671e7953dd6e86fef001e9b3ca3d.mp4',
    // ],
  },
  'PUBG MOBILE第2轮': {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/e7c38c4ffb2bcd157d41aa0941c6c8ab.mp4',
    //   // 'https://pagedoo.pay.qq.com/material/@platform/62fc671e7953dd6e86fef001e9b3ca3d.mp4',
    // ],
  },
  'Whiteout Survival': {
    // 视频: [
    //   // 'https://pagedoo.pay.qq.com/material/@platform/bee726d09ea6914a0561f273efdde21f.mp4',
    // ],
  },
};

const { totalParts } = getParts(getExcelText(EXCEL_STRING) ?? []);

const {
  materialFileNames, // 用于复制至pagedoo-upload，用以建立素材序号与素材URL的映射关系
  materialMapParsedFromExcel,
  CSVStr: CSVStrForImportToTencentDocument, // （经过适当处理后可）用于腾讯文档导入生成Excel素材列表表格，用于收集图片与视频素材
} = getMaterialInfo(getExcelText(EXCEL_STRING) ?? [''], totalParts);

console.log(`**** 本次直播共${totalParts.length}个环节`);
console.log(`**** 按顺序为:`, totalParts);

console.log(
  '**** 所有素材及其命名',
  materialMapParsedFromExcel,
  uniq(materialFileNames),
  CSV_PREFIX + CSVStrForImportToTencentDocument.join('\n')
);

/**
 * 生成素材URL
 * @param name 素材名称
 * @param material 素材值
 * @param type 素材类型（背景/前景/视频/转场）
 * @returns 生成的URL
 */
function generateMaterialUrl(name: string, material: string | undefined, type: '背景' | '前景' | '视频' | '转场'): string {
  if (!material) return '';
  
  const extension = type === '视频' ? '.mp4' : '.png';
  const isDirectFile = material.endsWith(extension);
  const normalizedMaterial = formatName(material);
  const directName = `${type}_${formatName(name)}_${normalizedMaterial}${extension}`;
  
  return VIDEO_URLS[CURRENT_LANGUAGE]?.[decodeURIComponent(normalizedMaterial)]
    || VIDEO_URLS.en?.[decodeURIComponent(normalizedMaterial)]
    || VIDEO_URLS[CURRENT_LANGUAGE]?.[directName]
    || VIDEO_URLS.en?.[directName]
    || MATERIAL_URLS[name]?.[type]?.[Number(decodeURIComponent(normalizedMaterial)) - 1]
    || `${MATERIAL_BASE_URL}/${isDirectFile
      ? `${normalizedMaterial}?t=${Date.now()}`
      : `${type}_${formatName(name)}_${normalizedMaterial}${extension}?t=${Date.now()}`}`;
}

// 建立已收集到的素材URL与Excel解析后所需素材占位标识的映射关系
function mapPlaceholderValueToUrl() {
  // 从Excel解析获得的素材源数据，包含了模板所需素材的URL占位
  const excelData = cloneDeep(materialMapParsedFromExcel);
  // materialUrls是程序现有的素材数据（例如，上周直播的数据未经更新之前，直播环节与本周Excel台词解决得到的直播环节可能并非一一对应）
  const collectedURLs = cloneDeep(MATERIAL_URLS);

  // excelData包含了本次直播所有环节，本方法的目标是构建包含所有环节（即包含excelData所有key）、每个key下背景、前景、转场、视频的占位数据改为MATERIAL_URLS对应的真实URL
  // 直播存在某些固定环节（开场白、结束语、综合互动等）、重复环节（可能都有"王者荣耀"，但本周的"王者荣耀"与上周的"王者荣耀"虽然内容环节命名相同，但素材可能并不相同——数量不同、资源不同）
  try {
    Object.keys(excelData).forEach((name) => {
      const types = Object.values(MATERIAL_TYPES);

      for (let i = 0; i < types.length; i++) {
        // excelData是参考基准，如果MATERIAL_URLS找不到excelData所包含特定环节name相关的内容环节，先用excelData[name][types[i]]填充
        if (has(excelData, name)) {
          if (!has(collectedURLs, name)) {
            // 如果找不到name对应的URL资源，先直接以Excel解析数据占位
            collectedURLs[name] = cloneDeep(excelData[name]);
          } else if (
            // 有环节且找到对应的素材类型，但URL素材数量为0
            // has(collectedURLs, `${name}.${types[i]}`) &&
            has(excelData, `${name}.${types[i]}`) &&
            // 素材数等于0通常代表是一个新的（与上周完全不同的）环节
            (collectedURLs[name][types[i] as materialKeyChineseNames]
              ?.length === 0 ||
              // 素材数不相等可能是尚未收集完全，与上周环节相同（如都是DNF），但上周和本周环节设计不同而导致素材数量不同（注意，即使素材数量相同，通常素材每周一更，素材链接也会跟着替换，但这就不是这个方法关心的细节了）
              excelData[name][types[i] as materialKeyChineseNames]?.length !==
                collectedURLs[name][types[i] as materialKeyChineseNames]
                  ?.length)
          ) {
            collectedURLs[name][types[i] as materialKeyChineseNames] =
              excelData[name][types[i] as materialKeyChineseNames];
            // console.log(
            //   '没有这类素材',
            //   name,
            //   types[i],
            //   excelData[name][types[i] as materialKeyChineseNames]
            // );
          } else {
            // 有环节且找到对应的素材类型，且URL素材数量与excelData特定素材类型所需素材数量一致
            // 不做处理
            // console.log(
            //   '素材数量对上了，暂不做处理',
            //   name,
            //   types[i],
            //   excelData[name][types[i] as materialKeyChineseNames]
            // );
          }
        }
      }
    });
  } catch (e) {
    console.error('error: 数据预处理出错', e);
  }
  return { data: excelData, result: collectedURLs };
}

const checkedUrl = new Set<string>();
function checkUrl(url: string) {
  if (checkedUrl.has(url)) return;
  checkedUrl.add(url);
  // fetch(url, { mode: 'cors' })
  //   .then((res) => {
  //     if (!res.ok) throw res;
  //     return res;
  //   })
  //   .catch((err) => {
  //     console.error(`资源 ${url} 无法访问！请检查！`);
  //     const w = window as any;
  //     w.__badResources = w.__badResources || [];
  //     w.__badResources.push(url);
  //   });
}

function parseEventParameter(str: string): Record<string, unknown> {
  const entries = str
    .replace(/^"|"$/g, '')
    .trim()
    .split('\n')
    .filter(Boolean)
    .map((line) => {
      const [中文属性名, 配置值] = line.split(/[:：](.+)/);
      const 协议属性名 = {
        广告ID: 'ad_id',
        在线时长: 'affective_second',
      }[中文属性名.trim()];
      if (!协议属性名) {
        return [];
      }
      if (协议属性名 === 'affective_time') {
        const 秒数 = 配置值.match(/\d+\.\d*/)?.[0];
        if (!秒数) {
          alert(`时间参数 ${str} 配置不正确，请检查在线时长是否为正确的秒数`);
          return [];
        }
        return [协议属性名, parseFloat(秒数)];
      }
      return [协议属性名, 配置值.trim()];
    })
    .filter((arr) => arr.length);
  return Object.fromEntries(entries);
}

function formatText(array: string[]) {
  const data = [...array];
  const { result: materials } = mapPlaceholderValueToUrl();
  console.log('**** 素材链接合集：', materials, array);
  let result: IExcelParsedData[] = [];
  let prevVirtualman = -1; //  上一个数字人
  let needTransition;
  try {
    result = data
      .map((i) => formatExcelRowData(i))
      .map((view, i) => {
        const [
          name,
          background,
          video,
          virtualman1,
          virtualman2,
          virtualman3,
          soundemotion,
          foreground,
          transition,
          event,
          eventParameter,
        ] = view;

        // 当前数字人与前一个数字人不同
        if (
          findNonEmptyStringIndex([virtualman1, virtualman2, virtualman3]) !==
          prevVirtualman
        ) {
          // 更新上一个数字人
          prevVirtualman = findNonEmptyStringIndex([
            virtualman1,
            virtualman2,
            virtualman3,
          ]);
          needTransition = true;
        } else {
          needTransition = true;
        }

        const word =
          view[
            findNonEmptyStringIndex([virtualman1, virtualman2, virtualman3]) + 3
          ];
        const type =
          word?.indexOf(COMMENT_MODE_KEYWORD) === 0 ? 'comment' : 'text';
        const subVirtualmanIndex = [virtualman1, virtualman2, virtualman3].findIndex(t => isSubVirtualmanMode(t));
        const subVirtualmanPose = [
          VIRTUALMAN_POSE.human_primary,
          VIRTUALMAN_POSE.using_phone_in_portrait_orientation,
          VIRTUALMAN_POSE.using_phone_in_landscape_orientation,
        ][subVirtualmanIndex]

        // 避免台词过长
        if (word.length > 3000) {
          throw new Error(
            `"${word.slice(
              0,
              30
            )}单元格超3000字符，建议请求拆分为多段，避免无法驱动数字人播报`
          );
        }

        const virtualmanPose: `${VIRTUALMAN_POSE}` = getVirtualmanPose([
          virtualman1,
          virtualman2,
          virtualman3,
        ]);
        const eventParams = parseEventParameter(eventParameter);

        const bgUrl = generateMaterialUrl(name, background, '背景');
        const fgUrl = generateMaterialUrl(name, foreground, '前景');
        const vdUrl = generateMaterialUrl(name, video, '视频');
        const trUrl = transition && transition !== '无' 
          ? generateMaterialUrl(name, transition, '转场')
          : '';
        [bgUrl, fgUrl, vdUrl, trUrl]
          .filter(Boolean)
          .forEach((url) => checkUrl(url));
        return {
          id: uuid(),
          name: formatName(name),
          backgroundImage: [
            {
              url: bgUrl, // 默认为1，即第一张背景图
              index: !background ? 0 : Number(background) - 1,
            },
          ],
          foregroundImage: [
            {
              url: fgUrl, // 默认为1，即第一张背景图
            },
          ],
          event:
            {
              开启福袋抽奖: 'START_LUCKY_BAG_LOTTERY',
              开启贴片广告: 'PUSH_LIVE_POP_AD_START',
              关闭贴片广告: 'PUSH_LIVE_POP_AD_STOP',
            }[event] || '',
          eventParameter: Object.keys(eventParams).length
            ? {
                mount: parseEventParameter(eventParameter),
              }
            : null,
          luckyBagEventParameter: event === '开启福袋抽奖' ? {
            mount: parseEventParameter(eventParameter),
          } : null,
          台词文案: type === 'text' ? formatExcelWord(word) : '',
          互动画面: type === 'text' ? '否' : '是',
          virtualman: VIRTUALMAN[virtualmanPose],
          subVirtualman: VIRTUALMAN[subVirtualmanPose],
          video:
            video === '无' || !video
              ? undefined
              : video === LIVE_PLAY_PULL_KEYWORD
              ? [
                  {
                    url: '', // 主播打游戏，推流地址为空即可
                    type: 'live',
                    orientation: VIDEO_ORIENTATION[virtualmanPose],
                    bkgUrl: VIDEO_BKG_URL[VIDEO_ORIENTATION[virtualmanPose]],
                  },
                ]
              : [
                  {
                    url: vdUrl,
                    type: video.match(/[(（](.+)[)）]/)?.[1],
                    orientation: VIDEO_ORIENTATION[virtualmanPose],
                    bkgUrl: VIDEO_BKG_URL[VIDEO_ORIENTATION[virtualmanPose]],
                  },
                ],
          soundemotion,
          transitionImage:
            needTransition && trUrl
              ? [
                  {
                    url: trUrl,
                  },
                ]
              : undefined,
        };
      });
  } catch (e) {
    console.error('error: formatText', e);
  }
  return result;
}

function extendEvents(data: IExcelParsedData[]) {
  let currentAd: any = null;
  const errors: string[] = [];
  data.forEach((item, i) => {
    const currentEventName = item.event;
    const currentEventParameter = item.eventParameter?.mount;
    if (item.event === 'PUSH_LIVE_POP_AD_START') {
      if (currentAd) {
        errors.push(`第 ${i + 1} 个环节打开了贴片广告，但是前一个贴片广告并未关闭`);
      }
      currentAd = {
        event: item.event,
        eventParameter: { ...item.eventParameter, event_type: item.event },
      };
    }
    if (currentAd) {
      item.event = currentAd?.event;
      item.eventParameter = item.eventParameter || {};
      item.eventParameter.mount = currentAd?.eventParameter?.mount;
    }
    if (currentEventName === 'PUSH_LIVE_POP_AD_STOP') {
      if (!currentAd) {
        alert(
          `第 ${
            i + 1
          } 个环节关闭了贴片广告，但是在前面并未找到对应的打开贴片广告配置`
        );
      } else {
        currentAd = null;
        data[i].eventParameter.unmount = data[i].eventParameter.mount;
        // 下架配置回填
        while (i--) {
          if (data[i]?.event === 'PUSH_LIVE_POP_AD_START') {
            data[i].eventParameter.unmount = {
              ...currentEventParameter,
              event_type: currentEventName,
            };
          } else {
            break;
          }
        }
      }
    }
  });
  if (errors.length) {
    alert(errors.join('\n'));
  }

  return data;
}

export const scriptData = extendEvents(
  formatText(getExcelText(EXCEL_STRING) ?? [])
);
console.log(
  'scriptData',
  scriptData,
  scriptData.map((x) => x.eventParameter)
);
// console.log('**** Excel数据解析结果：', formatText(getExcelText()));
console.log(
  '**** 台词：',
  formatText(getExcelText(EXCEL_STRING) ?? []).map((item) => item.台词文案)
);
