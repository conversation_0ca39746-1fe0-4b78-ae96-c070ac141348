import {
  PagedooSingleImage,
  PagedooSingleVideo,
} from '@tencent/pagedoo-library';

export enum TXCZ_TEMPLATE_COMPONENTS {
  BKG = '背景',
  VIDEO_BKG = '视频背景',
  VIDEO = '视频',
  VIRTUALMAN_DEFAULT = '数字人-默认',
  VIRTUALMAN_PORTRAIT = '数字人-竖屏手机',
  VIRTUALMAN_LANDSCAPE = '数字人-横屏手机',
  SPEECH = '话术',
  FOREGROUND = '前景',
  TRANSITION = '转场',
  PRE_TRANSITION = '转场预加载',
  BACKGROUND_MUSIC = '背景音乐',
  LUCKY_BAG = '福袋广告',
  PURCHASE = '购买广告',
}

// 枚举每个内容环节有4种素材类型：背景、前景、转场、视频
export enum MATERIAL_TYPES {
  BACKGROUND = '背景',
  FOREGROUND = '前景',
  TRANSITION = '转场',
  VIDEO = '视频',
}
export type materialKeyChineseNames = `${MATERIAL_TYPES}`;
// MATERIAL_MAP is a type alias
export type MATERIAL_MAP = Partial<Record<MATERIAL_TYPES, string[]>>;

export enum VIRTUALMAN_POSE {
  human_primary = 'human_primary',
  using_phone_in_landscape_orientation = 'using_phone_in_landscape_orientation',
  using_phone_in_portrait_orientation = 'using_phone_in_portrait_orientation',
}
export enum VIDEO_POSITION_COMMON_STYLE_KEY {
  landscape = 'phone_in_landscape_orientation',
  portrait = 'phone_in_portrait_orientation',
  fullscreen = 'phone_in_fullscreen',
}
// create a interface
export type TMATERIAL = MATERIAL_MAP;

export interface IExcelParsedData {
  id: string;
  // 环节名
  name: string;
  // 背景图片
  backgroundImage: PagedooSingleImage;
  // 前景图片
  foregroundImage: PagedooSingleImage;
  // 触发 Midasbuy 事件
  event:
    | 'PUSH_LIVE_POP_AD_STOP'
    | 'PUSH_LIVE_POP_AD_START'
    | 'START_LUCKY_BAG_LOTTERY'
    | '';
  // 事件参数
  eventParameter: {
    mount?: Record<string, unknown>;
    unmount?: Record<string, unknown>;
  };
  luckyBagEventParameter: {
    mount?: Record<string, unknown>;
    unmount?: Record<string, unknown>;
  };
  台词文案: string;
  互动画面: '是' | '否';
  // 视频/直播推流？
  video?: PagedooSingleVideo;
  // 数字人
  virtualman: {
    key: string;
    label: string;
  };
  // 音色情感
  soundemotion?: string;
  // 转场图片
  transitionImage?: PagedooSingleImage;
}
