import { uniq } from 'lodash-es';
import {
  CLIPPING_SHAPE_KEYWORD,
  COMMENT_MODE_KEYWORD,
  CURRENT_LANGUAGE,
  excelLineEndFlag,
  excelLineStartFlag,
  FULL_VIDEO_KEYWORD,
  LIVE_PLAY_PULL_KEYWORD,
  SUB_VIRTUALMAN_KEYWORD,
} from './constants';
import {
  MATERIAL_MAP,
  MATERIAL_TYPES,
  materialKeyChineseNames,
  VIRTUALMAN_POSE,
} from './type';

// 解析Excel文档
export function getExcelText(excelStr: string) {
  const excelLineStartFlagLength =
    excelStr.match(new RegExp(excelLineStartFlag, 'gm'))?.length || 0;
  const excelLineEndFlagLength =
    excelStr.match(new RegExp(excelLineEndFlag, 'gm'))?.length || 0;

  try {
    if (excelLineStartFlagLength !== excelLineEndFlagLength) {
      alert(
        `getExcelText \n\n开始标识${excelLineStartFlag}(${excelLineStartFlagLength}个)与结束标识${excelLineEndFlag}(${excelLineEndFlagLength}个)不匹配，请检查脚本文档`
      );
      return;
    }
  } catch (e) {
    console.error('error: getExcelText 结束标识与开始标识不匹配', e);
  }

  const matches = [];
  try {
    const regex = new RegExp(
      `${excelLineStartFlag}([\\s\\S]*?)${excelLineEndFlag}`,
      'g'
    );
    let match;
    while ((match = regex.exec(excelStr)) !== null) {
      // TAB制表符检测，如果Excel增删列，需要对应调整这里
      if (
        match[1].match(/\t/gm)?.length !==
        [
          'name', // 内容模块
          'background', // 背景图
          'video', // 视频
          'virtualman1', // 数字人1
          'virtualman2', // 数字人2
          'virtualman3', // 数字人3
          'soundemotion', // 音色
          'foreground', // 前景
          'transition', // 转场
          'event', // 事件
          'event_parameter', // 事件参数
        ].length +
          1
      ) {
        console.error(
          `**** 以下台词可能包含TAB(制表符)符号，会导致台词解析出错，请检查！\n\n"${match[1]}"`
        );
      }
      matches.push(match[1]);
    }
    matches.filter((i) => i); // 去掉“内容模板”为空的行
  } catch (e) {
    console.error('error: getExcelText', e);
  }

  return matches.map((match) => match.slice(1, -1));
}
export function formatExcelRowData(str: string) {
  // Excel复制行数据时，相邻单元格通过\t来划分
  return str.split('\t');
}

export function isRepeated(name: string) {
  // “内容模块”如果最后一位是数字，则代表是重复环节
  return new RegExp(/\d/).test(name[name.length - 1]);
}

export function findNonEmptyStringIndex(array: string[]): number {
  return array.findIndex((item) => item !== '无' && item !== SUB_VIRTUALMAN_KEYWORD && item !== '');
}

export function getVirtualmanPose(array: string[]): `${VIRTUALMAN_POSE}` {
  return [
    VIRTUALMAN_POSE.human_primary,
    VIRTUALMAN_POSE.using_phone_in_portrait_orientation,
    VIRTUALMAN_POSE.using_phone_in_landscape_orientation,
  ][findNonEmptyStringIndex(array)];
}

// 内容环节名称（会作为对象的key值，需进行适当）处理
export function formatName(str: string) {
  return encodeURIComponent(str.replace(/^\s*[(（)].+[)）]/, ''));
}

// 将“停顿（5s）”标识转换为“<break time="5000ms"/>”
export function breakTimeFormat(substring: string) {
  if (!substring) return '';
  if (substring.match(/\d*\.?\d*/g)) {
    return `<break time="${
      Number(substring?.match(/\d*\.?\d*/g)?.join('')) * 1000
    }ms"/>`;
  }
  return '';
}

const corrections = {
  'PUBG MOBILE': {
    ipa: 'ˈpʌb ˈdʒi ˈmoʊˌbaɪl',
  },
  PUBGM: {
    ipa: 'ˈpʌb ˈdʒi ˈmoʊˌbaɪl',
  },
  'Honor of Kings': {
    ipa: 'ˈɑːnər əv kɪŋz',
  },
  'Delta Force': {
    ipa: 'ˈdɛltə fɔrs',
  },
  "Midasbuy's": {
    ipa: 'ˈmaɪdəsˌbaɪz',
  },
  "Mighdisbigh's": {
    ipa: 'ˈmaɪdəsˌbaɪz',
  },
  Midasbuy: {
    ipa: 'ˈmaɪdəsˌbaɪ',
  },
  Mighdisbigh: {
    ipa: 'ˈmaɪdəsˌbaɪ',
  },
  Meera: {
    ipa: 'ˈmɪrə',
  },
  Mira: {
    ipa: 'ˈmɪrə',
  },
  Mishal: {
    ipa: 'ˈmɪʃɛl',
  },
  UC: {
    ipa: 'juː siː',
  },
};

export function formatExcelWord(word: string) {
  if (!word) {
    return '';
  }

  // .replace(/\bmidasbuy\b/gi, 'Mighdusbye')
  word = word
    .replace(/\n+/g, '\n')
    .replace(/\n/g, ' ')
    .replace(/(\(|（)(停顿|暂停)\d\.?\d*(s|秒)(\)|）)/g, (str) => {
      return breakTimeFormat(str);
    })
    .replace(/520/gm, '五二零')
    .replace(/(~|～)/gm, '。')
    .replace(/MOBA/g, '哞吧')
    .replace(/Q币/g, 'Cue币')
    .replace(/弹幕/g, '蛋木')
    .replace(/DNF/g, '蒂恩霭酜')
    .replace(/\[Live Chat\]/g, 'Live Chat')
    .replace(/\bMidasbuy\b/g, 'Mighdisbigh')
    .replace(/\bPUBGM\b/g, 'PUBG MOBILE')
    .replace(/\bMira\b/g, 'Meera')
    .replace(/\b[A-Z]+\b/g, (word) => {
      if (['PUBG', 'UC', 'QR', 'VIP', 'ID'].includes(word)) {
        return word;
      }
      return word[0].toUpperCase() + word.slice(1).toLowerCase();
    });

  if (CURRENT_LANGUAGE !== 'en') {
    Object.entries(corrections).forEach(([keyword, { ipa }]) => {
      word = word.replaceAll(
        new RegExp(String.raw`\b${keyword}\b`, 'ig'),
        `<phoneme alphabet="ipa" ph="${ipa}">{{{{${btoa(keyword)}}}}}</phoneme>`
      );
    });
    return word.replace(/\{\{\{\{(.+?)\}\}\}\}/g, (_, $1) => {
      return atob($1);
    });
  }

  return word;
}

function getCSVRowStr(name: string, value: string) {
  return `${name},${value},,否`;
}

// 获取活动内容
export function getParts(array: string[]) {
  const excelRows = [...array];
  const totalParts: string[] = uniq(
    excelRows
      .map((i) => formatExcelRowData(i))
      .map((i) => i[0]) // 获取“内容模块”
      .filter((i) => i) // 过滤空白内容模块
      .map((i) => formatName(i)) // 去除空格
  );

  return { totalParts };
}

export function exportMaterialInfoToDesigner(
  materialMapParsedFromExcel: Record<string, MATERIAL_MAP>
) {
  // 导出给设计师的素材列表命名规范
  const materialFileNames: string[] = [];
  const CSVStr: string[] = [];

  Object.keys(materialMapParsedFromExcel).map((name) => {
    Object.keys(materialMapParsedFromExcel[name]).map((type) => {
      console.log(materialMapParsedFromExcel[name]);
      materialMapParsedFromExcel[name][
        type as materialKeyChineseNames
      ]?.forEach((value: string) => {
        const filename = `${type}_${name}_${value}.${
          type === MATERIAL_TYPES.VIDEO ? 'mp4' : 'png'
        }`;
        materialFileNames.push(filename);
        CSVStr.push(
          getCSVRowStr(
            name,
            filename.replace(/.mp4/gi, '').replace(/.png/gi, '')
          )
        );
      });
    });
  });

  return { materialFileNames, CSVStr };
}

// 初始化每个环节，每个环节（name）为一个对象，每个环节包含四个素材类型，每个素材类型为一个字符串数组，数组中包含所有素材名称（与Excel特定单元格的内容一一对应）
// 从Excel中获取的素材占位符通常为1、2、3等数字，数字代表同一素材类型多个素材（例如同一个环节可能用到多张背景、多个视频、多张转场图等）的出现顺序
export function init(totalParts: string[]): Record<string, MATERIAL_MAP> {
  // todo 下面的string如何优化为动态获取本次直播真实用到的内容环节
  let result: Record<string, MATERIAL_MAP> = {};
  // 内容环节
  let segments = {};
  const MaterialTypes = [
    MATERIAL_TYPES.BACKGROUND,
    MATERIAL_TYPES.FOREGROUND,
    MATERIAL_TYPES.TRANSITION,
    MATERIAL_TYPES.VIDEO,
  ];
  MaterialTypes.forEach((type) => {
    segments = { ...segments, [type]: [] };
  });
  totalParts.forEach((name) => {
    result = { ...result, [name]: JSON.parse(JSON.stringify(segments)) };
  });
  return result;
}

export function getMaterialInfo(array: string[], totalParts: string[]) {
  const materialMapParsedFromExcel = init(totalParts);
  const data = [...array];
  data
    .map((excelRow) => formatExcelRowData(excelRow))
    .forEach((excelRow) => {
      const [name, background, video, , , , , foreground, transition] =
        excelRow;
      initMaterialPlaceholderValue(
        background,
        formatName(name),
        MATERIAL_TYPES.BACKGROUND
      );
      initMaterialPlaceholderValue(
        foreground,
        formatName(name),
        MATERIAL_TYPES.FOREGROUND
      );
      initMaterialPlaceholderValue(
        video,
        formatName(name),
        MATERIAL_TYPES.VIDEO
      );
      initMaterialPlaceholderValue(
        transition,
        formatName(name),
        MATERIAL_TYPES.TRANSITION
      );
    });

  // 注意，这里的materialMap不是init后的结果，实际上已经经过诸多initMaterialPlaceholderValue处理了
  const { materialFileNames, CSVStr } = exportMaterialInfoToDesigner(
    materialMapParsedFromExcel
  );
  return {
    materialFileNames,
    materialMapParsedFromExcel,
    CSVStr,
  };

  function initMaterialPlaceholderValue(
    value: string, // Excel单元格填写的内容
    name: string, // B列对应的内容模块，即直播环节，例如“开场白”“结束语”
    type: materialKeyChineseNames
  ) {
    if (
      value &&
      value !== '无' &&
      value !== LIVE_PLAY_PULL_KEYWORD && // 视频特有，先一并处理；后续如果有必要再细分场景处理
      !materialMapParsedFromExcel[name][type]?.includes(value)
    ) {
      materialMapParsedFromExcel[name][type]?.push(value);
    }
  }
}

// 全屏视频
export function isFullScreenMode(str: string) {
  return str.indexOf(FULL_VIDEO_KEYWORD) > -1;
}

// 陪伴模式
export function isSubVirtualmanMode(str: string) {
  return str.indexOf(SUB_VIRTUALMAN_KEYWORD) > -1;
}

// 纯互动
export function isCommentMode(str: string) {
  return str.indexOf(COMMENT_MODE_KEYWORD) > -1;
}

// 数字人裁剪成圆形，在角落显示
export function isVirtualmanInClippingRegion(str: string) {
  return str.indexOf(CLIPPING_SHAPE_KEYWORD) > -1;
}
