import { CommonStyle } from '@tencent/pagedoo-library';
import { allViews, baseSize, commonStyle } from '../play-view';
import { SinglePlugin } from '@/utils/template';
import { PlayConfig, Script } from '@/type/pagedoo';
import { PagedooFontStyle } from '@tencent/pagedoo-library/es/types/pagedoo-type';
import {
  customVirtualManWave,
  liveBackgroundMusic,
  liveBkg,
  liveImage,
  liveProduct,
  liveQA,
  liveSound,
  liveSpeechAD,
  liveText,
  liveVideo,
} from '../play-component';
import { uuid } from '@tencent/midas-util';
import { styleInject } from '../style';

export type TemplateItemType = {
  url: string;
  style: CommonStyle;
};
export type TemplateTextType = {
  fontStyle: PagedooFontStyle;
  text: string;
  commonStyle: CommonStyle;
};
export type TemplateMapType = {
  [key in string]: {
    bkg: TemplateItemType;
    title?: TemplateItemType;
    pic: TemplateItemType[];
    human: TemplateItemType;
    fgd: TemplateItemType;
    text?: TemplateTextType[];
  };
};
type LiveTextType = {
  fontSize: number;
  fontFamily: string;
  color: string;
  width?: number;
  textAlign?: 'center' | 'left' | 'right';
  lineHeight?: number;
  fontWeight?: number;
};
export function transformFn(
  rate: number,
  safe: number,
  left: number,
  top: number,
  width: number
): CommonStyle {
  return commonStyle(safe + rate * left, rate * top, rate * width);
}
const transformStyle = transformFn.bind(null, 375 / 1242, 40);
const transformBkgStyle = transformFn.bind(null, 455 / 1242, 0);

const adTemplateDuration = 200000;

export const getFontStyle = (style: LiveTextType): PagedooFontStyle => {
  const rate = 315 / 1242;
  const {
    fontSize,
    fontFamily,
    color,
    textAlign,
    width,
    lineHeight = 2,
    fontWeight,
  } = style;
  return {
    fontSize: fontSize * rate,
    fontFamily: [fontFamily],
    color: {
      color,
      realColor: color,
      show: true,
    },
    useImage: 0,
    textAlign,
    lineHeight,
    width: width && width * rate,
    fontWeight,
  };
};

export const templateNameMap: Record<string, string> = {
  skincare: '美妆护肤',
  tea: '茶叶',
  books_light: '图书（浅色）',
  books_dark: '图书',
  education: '教育',
  plants: '鲜花绿植',
};
export const templateMap: TemplateMapType = {
  skincare: {
    // title: { url: skinCare_title, style: transformStyle(0, 0, 1242) },
    text: [
      {
        text: '直播专属福利',
        fontStyle: getFontStyle({
          width: 752,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 125,
          color: '#A05756',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(244, 270, 752),
      },
      {
        text: '——会员下单 立享好礼——',
        fontStyle: getFontStyle({
          width: 700,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 55,
          color: '#A05756',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(282, 455, 700),
      },
      {
        text: '直播间专享',
        fontStyle: getFontStyle({
          width: 253,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#61372E',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(94, 617, 253),
      },
      {
        text: '¥',
        fontStyle: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 33,
          color: '#DF5655',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(82, 699, 21),
      },
      {
        text: '199/3件',
        fontStyle: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#DF5655',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(103, 669, 230),
      },
      {
        text: '点关注有福利',
        fontStyle: getFontStyle({
          width: 297,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 45,
          color: '#6E3B3B',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(886, 641, 297),
      },
      {
        text: '镇店爆款',
        fontStyle: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#DF5655',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(950, 749, 180),
      },
      {
        text: '超值好物',
        fontStyle: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#DF5655',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(950, 827, 180),
      },
      {
        text: '关注领券',
        fontStyle: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 45,
          color: '#DF5655',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(950, 906, 180),
      },
    ],
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/60daa395220ade647179a6f5f3875c58.png',
        style: transformStyle(40, 590, 351),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/0e9d27c79c8ce36088c61c16036ffc6c.png',
        style: transformStyle(804, 612, 462),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c79afb674404dbf94547582a3668b9a7.png',
        style: transformStyle(913, 761, 256),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c79afb674404dbf94547582a3668b9a7.png',
        style: transformStyle(913, 839, 256),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c79afb674404dbf94547582a3668b9a7.png',
        style: transformStyle(913, 917, 256),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/841585c4ee1bf36d7ef3cded7091efac.png',
        style: transformStyle(92, 794, 249),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/841585c4ee1bf36d7ef3cded7091efac.png',
        style: transformStyle(916, 1063, 256),
      },
      // {
      //   url: 'https://pagedoo.pay.qq.com/material/@platform/2946779baa430a882d401114b5965df0.png',
      //   style: transformStyle(0, 1833, 1242),
      // },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/962220ca9c6eba5a4b0a909a24f9e901.png',
        style: transformStyle(0, 1871, 1242),
      },
    ],
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/c5095cfbd4fad68c8e2d0349543b6c72.png',
      style: transformBkgStyle(0, 1872, 1242),
    },
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/176b3ec2b98685e07e188bd9e0c5305c.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    human: { url: '', style: transformStyle(-50, 688, 1344) },
  },
  tea: {
    text: [
      {
        text: '直播专属福利',
        fontStyle: getFontStyle({
          width: 752,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 125,
          color: '#2B6D22',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(246, 201, 752),
      },
      {
        text: '会员下单 立享好礼',
        fontStyle: getFontStyle({
          width: 430,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 48,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(413, 398, 430),
      },
      {
        text: '百亿补贴',
        fontStyle: getFontStyle({
          width: 115,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 28,
          color: '#23561C',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(157, 658, 115),
      },
      {
        text: '周年盛典',
        fontStyle: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 57,
          color: '#23561C',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(100, 690, 230),
      },
      {
        text: '单价低至99元',
        fontStyle: getFontStyle({
          width: 187,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 30,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(120, 778, 187),
      },
      {
        text: '直播福利',
        fontStyle: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 57,
          color: '#23561C',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(100, 891, 230),
      },
      {
        text: '满599赠',
        fontStyle: getFontStyle({
          width: 125,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 32,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(153, 972, 150),
      },
      {
        text: '畅享巨惠',
        fontStyle: getFontStyle({
          width: 200,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 50,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(927, 655, 200),
      },
      {
        text: '下单一件即赠',
        fontStyle: getFontStyle({
          width: 180,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 30,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(932, 738, 180),
      },
      {
        text: '100%正品',
        fontStyle: getFontStyle({
          width: 183,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(130, 2439, 183),
      },
      {
        text: '全国联保',
        fontStyle: getFontStyle({
          width: 152,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(412, 2439, 152),
      },
      {
        text: '七天可退',
        fontStyle: getFontStyle({
          width: 152,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(679, 2439, 152),
      },
      {
        text: '保价30天',
        fontStyle: getFontStyle({
          width: 172,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 37,
          color: '#FCE9B6',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(946, 2439, 172),
      },
    ],
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/379b9d305cbc1914c1d90ffe11e32fc1.png',
        style: transformStyle(361, 408, 533),
      },
      // 绿底
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/613e335fc79bbbb84dd7a48ac6cb58db.png',
        style: transformStyle(65, 621, 300),
      },
      // 上绿皮
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/df853539982ca0a1fe3cf48915c06928.png',
        style: transformStyle(98, 784, 234),
      },
      // 下绿皮
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/df853539982ca0a1fe3cf48915c06928.png',
        style: transformStyle(93, 978, 244),
      },
      // 商品
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/1c849d6f14ca71f4bdbbe04011d7ca4c.png',
        style: transformStyle(138, 1051, 134),
      },

      // 右底
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/7ab7ef74d04569e7a78d32132dfebba5.png',
        style: transformStyle(877, 644, 300),
      },
      // 右商品
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/1c849d6f14ca71f4bdbbe04011d7ca4c.png',
        style: transformStyle(933, 812, 188),
      },
      // 右绿皮
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/df853539982ca0a1fe3cf48915c06928.png',
        style: transformStyle(910, 745, 234),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/398b51c5d8a75847d63f16c3c20dd72a.png',
        style: transformStyle(0, 2048, 1242),
      },
      // 前景贴片
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
        style: transformStyle(118, 2411, 204),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
        style: transformStyle(385, 2411, 204),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
        style: transformStyle(652, 2411, 204),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/0d6a290518d9f15f661e77b10c2c88fe.png',
        style: transformStyle(919, 2411, 204),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/a4dc78029e9b0cb1bd41884a380a7dad.png',
        style: transformStyle(0, 1564, 1242),
      },
    ],
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/b77bba2f3b2631fc98e44a04b52ed520.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/fee5f1a062ceba82fd7f8dbe532d257b.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    human: {
      url: '',
      style: transformStyle(22, 487, 1198),
    },
  },
  books_light: {
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/807388322d5723764b6b5f8aaa203d87.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/8f912a6dec49496904fe80104757077c.png',
      style: transformBkgStyle(0, 1865, 1242),
    },
    human: { url: '', style: transformStyle(-80, 880, 1344) },
    text: [
      {
        text: '直播专属福利',
        fontStyle: getFontStyle({
          width: 752,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 125,
          color: '#57341F',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(246, 190, 752),
      },
      {
        text: '——会员下单 立享好礼——',
        fontStyle: getFontStyle({
          width: 695,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 55,
          color: '#57341F',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(284, 373, 695),
      },
      {
        text: '2100 张初高中高频单词卡\n10本中考逆袭手册\n400个语法精讲视频\n30天单词卡\n90天金牌专家辅导伴学\n10次1v1学情诊断',
        fontStyle: getFontStyle({
          width: 472,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 40,
          color: '#6D452E',
          textAlign: 'left',
          lineHeight: 56 / 40,
          fontWeight: 500,
        }),
        commonStyle: transformStyle(158, 532, 472),
      },
      {
        text: '会员专享 拍1得8',
        fontStyle: getFontStyle({
          width: 308,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 40,
          color: '#6D452E',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(770, 546, 308),
      },
      {
        text: '1099',
        fontStyle: getFontStyle({
          width: 264,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 108,
          color: '#57341F',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(811, 568, 264),
      },
      {
        text: '¥',
        fontStyle: getFontStyle({
          width: 38,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 61,
          color: '#57341F',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(770, 619, 38),
      },
      {
        text: '直播间限时抢购',
        fontStyle: getFontStyle({
          width: 153,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 21,
          color: '#EBDFCF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(756, 740, 153),
      },
      {
        text: '满199赠会员礼',
        fontStyle: getFontStyle({
          width: 153,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 21,
          color: '#EBDFCF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(943, 740, 153),
      },
      {
        text: '全程专家辅导 1V1 特辑培训',
        fontStyle: getFontStyle({
          width: 131,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 21,
          color: '#6D452E',
          fontWeight: 400,
          lineHeight: 30 / 21,
        }),
        commonStyle: transformStyle(767, 785, 131),
      },
      {
        text: '逆袭学情诊断 语法精讲视频',
        fontStyle: getFontStyle({
          width: 131,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 21,
          color: '#6D452E',
          fontWeight: 400,
          lineHeight: 30 / 21,
        }),
        commonStyle: transformStyle(951, 785, 131),
      },
      {
        text: '直播间专享',
        fontStyle: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 46,
          color: '#61372E',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(102, 1037, 230),
      },
      {
        text: '满199即赠',
        fontStyle: getFontStyle({
          width: 200,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 42,
          color: '#F4E7D6',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(117, 1103, 200),
      },
      {
        text: '李老师',
        fontStyle: getFontStyle({
          width: 184,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#57341F',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(925, 1242, 184),
      },
      {
        text: '武汉大学教育学硕士 北京大学心理学博士 一加学院创始人 百度第六季领学官',
        fontStyle: getFontStyle({
          width: 270,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 30,
          color: '#6D452E',
          lineHeight: 45 / 30,
          fontWeight: 700,
        }),
        commonStyle: transformStyle(882, 1331, 270),
      },
    ],
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/8b45ea659abde5e97ddd6d3138a05108.png',
        style: transformStyle(88, 488, 1067),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/aeb28bd13396e238f622a84f238341a2.png',
        style: transformStyle(745, 730, 176),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/aeb28bd13396e238f622a84f238341a2.png',
        style: transformStyle(930, 730, 176),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/7bc9bb5b8c25505f932b1e50931604a5.png',
        style: transformStyle(930, 730, 176),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/7bc9bb5b8c25505f932b1e50931604a5.png',
        style: transformStyle(745, 730, 176),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/37e2bff39646722357b160f455ede536.png',
        style: transformStyle(81, 1031, 273),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/16010b25692e51d52defc35abc43302e.png',
        style: transformStyle(96, 1118, 242),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/5f9645f7779fe98132c2bf06d53f55f1.png',
        style: transformStyle(848, 1206, 338),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/0590c1ada3658fce8a915edd306345bb.png',
        style: transformStyle(112, 1213, 210),
      },

      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/827ef4a027372bac488b4409051ee427.png',
        style: transformStyle(0, 1899, 373),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/3d2b1663891bb0ac4a96e3e5832f4e4c.png',
        style: transformStyle(448, 1910, 825),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/149c8f6a9ec0163c651da5bb1efd58a4.png',
        style: transformStyle(0, 2230, 1242),
      },
    ],
  },
  education: {
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/e6e6effa4fd5ba279c06b50a64906207.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/9fd3f882ec8abc9fd2fa701726775a5a.png',
      style: transformBkgStyle(0, 2080, 1242),
    },
    text: [
      {
        text: '直播专属福利',
        fontStyle: getFontStyle({
          width: 752,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 125,
          color: '#105DFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(251, 180, 752),
      },
      {
        text: 'AI老师一对一辅导',
        fontStyle: getFontStyle({
          width: 487,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 55,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(408, 385, 487),
      },
      // https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff
      {
        text: '全新高知系列课程限时抢购',
        fontStyle: getFontStyle({
          width: 720,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#105DFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(125, 538, 720),
      },
      {
        text: '会员满2赠1',
        fontStyle: getFontStyle({
          width: 214,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 40,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(918, 560, 214),
      },
      {
        text: '由浅入深讲知识，方便理解好吸收.\n多维度讲解教学，全方位提升认知\n抓重点精炼总结，轻松掌握技技巧\n购买全系书籍附赠资深专家手写笔记。',
        fontStyle: getFontStyle({
          width: 616,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 36,
          color: '#FFFFFF',
          textAlign: 'left',
          lineHeight: 66 / 36,
          fontWeight: 500,
        }),
        commonStyle: transformStyle(177, 654, 616),
      },
      {
        text: '终身会员',
        fontStyle: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(935, 698, 132),
      },
      {
        text: '1099',
        fontStyle: getFontStyle({
          width: 173,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 70,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(908, 727, 173),
      },
      {
        text: '元',
        fontStyle: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 20,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(1080, 782, 21),
      },
      {
        text: '持续升级',
        fontStyle: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(935, 820, 132),
      },
      {
        text: '新课上新',
        fontStyle: getFontStyle({
          width: 228,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 56,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(99, 1048, 228),
      },
      {
        text: '领券更优惠',
        fontStyle: getFontStyle({
          width: 216,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 43,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(104, 1175, 216),
      },
      {
        text: '满300减50',
        fontStyle: getFontStyle({
          width: 186,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 35,
          color: '#0A69F7',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(120, 1260, 186),
      },
      {
        text: '直播抽大奖',
        fontStyle: getFontStyle({
          width: 216,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 43,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(105, 1354, 216),
      },
      {
        text: '送50份礼包',
        fontStyle: getFontStyle({
          width: 194,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 35,
          color: '#0A69F7',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(115, 1429, 194),
      },
      {
        text: '李老师',
        fontStyle: getFontStyle({
          width: 182,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#FFFFFF',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(908, 1380, 182),
      },
      {
        text: '武汉大学教育学硕士 北京大学心理学博士 一加学院创始人 百度第六季领学官',
        fontStyle: getFontStyle({
          width: 270,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 30,
          color: '#FFFFFF',
          lineHeight: 45 / 30,
          fontWeight: 700,
        }),
        commonStyle: transformStyle(866, 1485, 270),
      },
    ],
    human: { url: '', style: transformStyle(-50, 1098, 1344) },
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c2c093ab55dcde3d7e8b3f523f48b0d1.png',
        style: transformStyle(830, 1370, 338),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/f86dac7f0410e6eaf198f2d0c705fe9e.png',
        style: transformStyle(73, 1037, 274),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/a9b319cbc134cf41490b0c07d070891f.png',
        style: transformStyle(105, 1250, 213),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/a9b319cbc134cf41490b0c07d070891f.png',
        style: transformStyle(105, 1419, 213),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/25dcc31ae68013c86d4f1cbd40ba81f6.png',
        style: transformStyle(881, 663, 243),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/2485d75826137061a99343826ae12d4f.png',
        style: transformStyle(73, 508, 1095),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/bfc2a54accd34c4666a372f2ee3a482c.png',
        style: transformStyle(306, 395, 641),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/967ffb23d2a843dc0c61085cf15f29b5.png',
        style: transformStyle(0, 1880, 1242),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c81341a90e8b7e35b8ea0ec8595614c2.png',
        style: transformStyle(0, 2506, 1242),
      },
    ],
  },
  plants: {
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/e0fff5ef68a0b5d2cd853d0fce8ca584.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/65590e0d7758a5a9f219fbb74bcd95b3.png',
      style: transformBkgStyle(0, 1610, 1242),
    },
    // title: { url: plants_title, style: transformStyle(0, 0, 1242) },
    human: { url: '', style: transformStyle(-90, 258, 1442) },
    text: [
      {
        text: '直播专属福利',
        fontStyle: getFontStyle({
          width: 752,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 125,
          color: '#127804',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(244, 190, 752),
      },
      {
        text: '会员下单 立享好礼',
        fontStyle: getFontStyle({
          width: 496,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 55,
          color: '#127804',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(384, 385, 496),
      },
      {
        text: '直播间专享',
        fontStyle: getFontStyle({
          width: 250,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 50,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(85, 611, 250),
      },
      {
        text: '¥',
        fontStyle: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 33,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(84, 702, 21),
      },
      {
        text: '199/3件',
        fontStyle: getFontStyle({
          width: 232,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 60,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(105, 672, 232),
      },
      {
        text: '直播间福利',
        fontStyle: getFontStyle({
          width: 279,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 55,
          color: '#FFFFFF',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(893, 578, 279),
      },
      {
        text: '¥',
        fontStyle: getFontStyle({
          width: 17,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 27,
          color: '#568C45',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(969, 762, 17),
      },
      {
        text: '50',
        fontStyle: getFontStyle({
          width: 93,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 76,
          color: '#568C45',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(986, 707, 93),
      },
      {
        text: '全场满399可用',
        fontStyle: getFontStyle({
          width: 240,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 34,
          color: '#2F4B26',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(926, 818, 240),
      },
      {
        text: '¥',
        fontStyle: getFontStyle({
          width: 17,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 27,
          color: '#568C45',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(969, 952, 17),
      },
      {
        text: '100',
        fontStyle: getFontStyle({
          width: 159,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 76,
          color: '#568C45',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(965, 896, 159),
      },
      {
        text: '全场满399可用',
        fontStyle: getFontStyle({
          width: 240,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 34,
          color: '#2F4B26',
          textAlign: 'left',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(926, 1008, 240),
      },
      {
        text: '¥',
        fontStyle: getFontStyle({
          width: 17,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 27,
          color: '#568C45',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(969, 1149, 17),
      },
      {
        text: '200',
        fontStyle: getFontStyle({
          width: 159,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 76,
          color: '#568C45',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(986, 1093, 159),
      },
      {
        text: '全场满399可用',
        fontStyle: getFontStyle({
          width: 240,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font20.woff',
          fontSize: 34,
          color: '#2F4B26',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(926, 1205, 240),
      },
      {
        text: '100%正品',
        fontStyle: getFontStyle({
          width: 199,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#276620',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(164, 2197, 199),
      },
      {
        text: '全国联保',
        fontStyle: getFontStyle({
          width: 169,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#276620',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(441, 2197, 199),
      },
      {
        text: '七天可退',
        fontStyle: getFontStyle({
          width: 169,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          color: '#276620',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(678, 2197, 199),
      },
      {
        text: '保价30天',
        fontStyle: getFontStyle({
          width: 199,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 42,
          textAlign: 'center',
          color: '#276620',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(915, 2197, 199),
      },
    ],
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/95ba217563526afc97105745d55bd9f7.png',
        style: transformStyle(866, 570, 332),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/974af29fe96670123dda51f66af6747b.png',
        style: transformBkgStyle(0, 1493, 1242),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/36cdc557382b263af18442d81545fa23.png',
        style: transformStyle(887, 720, 291),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/36cdc557382b263af18442d81545fa23.png',
        style: transformStyle(887, 904, 291),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/36cdc557382b263af18442d81545fa23.png',
        style: transformStyle(887, 1100, 291),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/3135e848aa5bce0b8d74bc64a717958b.png',
        style: transformStyle(59, 603, 301),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/497d881b38cfb72e21565e0a5a2c995d.png',
        style: transformStyle(84, 779, 251),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
        style: transformStyle(157, 2124, 212),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
        style: transformStyle(416, 2124, 212),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
        style: transformStyle(657, 2124, 212),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/cfb330012532ad2fecccb51086be81fc.png',
        style: transformStyle(898, 2124, 212),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c7ef360311a45c8d4cd5523a78d318b9.png',
        style: transformStyle(356, 416, 42),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/c7ef360311a45c8d4cd5523a78d318b9.png',
        style: transformStyle(949, 416, 42),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/6d574692a362bb7ec39b37b2dc8e1bda.png',
        style: transformStyle(40, 922, 1242),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/673495940723cf596430f6d434bf34e4.png',
        style: transformStyle(0, 1012, 587),
      },
    ],
  },
  books_dark: {
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/08f9a053d2d9a08fa5f795b06554bc4a.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/b014af09e49c7033345588ca9d54512a.png',
      style: transformBkgStyle(0, 2050, 1242),
    },
    // title: { url: books_dark_title, style: transformStyle(0, 0, 1242) },
    human: { url: '', style: transformStyle(-60, 1010, 1344) },
    text: [
      {
        text: '直播专属福利',
        fontStyle: getFontStyle({
          width: 752,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 125,
          color: '#F4E7D7',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(246, 186, 752),
      },
      {
        text: '——会员下单 立享好礼——',
        fontStyle: getFontStyle({
          width: 700,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 55,
          color: '#F4E7D7',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(284, 381, 700),
      },
      {
        text: '全新认知系列套书限时抢购',
        fontStyle: getFontStyle({
          width: 720,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 60,
          color: '#F4E7D7',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(117, 512, 720),
      },
      {
        text: '会员满2赠1',
        fontStyle: getFontStyle({
          width: 214,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 40,
          color: '#F4E7D7',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(898, 527, 214),
      },
      {
        text: '由浅入深讲知识，方便理解好吸收。\n多维度讲解教学，全方位提升认知。\n抓重点精炼总结，轻松掌握技技巧。\n购买全系书籍附赠资深专家手写笔记。',
        fontStyle: getFontStyle({
          width: 612,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 36,
          color: '#FFF9F2',
          lineHeight: 66 / 36,
          textAlign: 'left',
          fontWeight: 400,
        }),
        commonStyle: transformStyle(181, 628, 612),
      },
      {
        text: '终身会员',
        fontStyle: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#57341F',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(925, 672, 132),
      },
      {
        text: '持续升级',
        fontStyle: getFontStyle({
          width: 132,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font24.woff',
          fontSize: 32,
          color: '#57341F',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(925, 794, 132),
      },
      {
        text: '1099',
        fontStyle: getFontStyle({
          width: 173,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 71,
          color: '#57341F',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(898, 701, 173),
      },
      {
        text: '元',
        fontStyle: getFontStyle({
          width: 21,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          fontSize: 20,
          color: '#57341F',
          fontWeight: 900,
        }),
        commonStyle: transformStyle(1067, 756, 21),
      },
      {
        text: '李老师',
        fontStyle: getFontStyle({
          width: 184,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font22.woff',
          color: '#E4CDB8',
          fontSize: 61,
          fontWeight: 900,
        }),
        commonStyle: transformStyle(876, 1343, 184),
      },
      {
        text: '武汉大学教育学硕士北京大学心理学博士一加学院创始人百度第六季领学官',
        fontStyle: getFontStyle({
          width: 270,
          fontSize: 30,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          color: '#FFFFFF',
          textAlign: 'left',
          lineHeight: 45 / 30,
          fontWeight: 700,
        }),
        commonStyle: transformStyle(877, 1452, 270),
      },
      {
        text: '直播间专享',
        fontStyle: getFontStyle({
          width: 230,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 46,
          color: '#61372E',
          fontWeight: 700,
        }),
        commonStyle: transformStyle(124, 1162, 230),
      },
      {
        text: '满199即赠',
        fontStyle: getFontStyle({
          width: 200,
          fontFamily:
            'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font23.woff',
          fontSize: 42,
          color: '#FFFFFF',
          fontWeight: 500,
        }),
        commonStyle: transformStyle(140, 1228, 200),
      },
    ],
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/75aed134a075d26b88d9e5186a97db8a.png',
        style: transformStyle(88, 497, 1067),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
        style: transformStyle(118, 641, 44),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
        style: transformStyle(118, 706, 44),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
        style: transformStyle(118, 771, 44),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/dadeacb10f0619548e400a8b7478a725.png',
        style: transformStyle(118, 836, 44),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/771a4a1d33c95cda16b3b004d172c5b7.png',
        style: transformStyle(871, 637, 243),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/a7edf55580991ce9bb7a4e2527a30207.png',
        style: transformStyle(88, 1152, 317),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/b28ec245c045e3669f84320f2425f093.png',
        style: transformStyle(119, 1243, 242),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/ff8ded7daae6ac057552e53ef91ca33e.png',
        style: transformStyle(114, 1316, 234),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/88e546d79c1d457775d5e2d12b2c66f7.png',
        style: transformStyle(840, 1337, 338),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/8df467501c3b112d7e774eca780ddc36.png',
        style: transformStyle(51, 1792, 350),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/38c28fc141fab99854d3722d08432010.png',
        style: transformStyle(450, 2141, 802),
      },
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/4fa82b2e65d95e539914feb9073d50d9.png',
        style: transformStyle(0, 2453, 1242),
      },
    ],
  },
  default: {
    bkg: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/f6bae7e7c6f2c89aaea6b27330b4dcbd.png',
      style: transformBkgStyle(0, 0, 1242),
    },
    title: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/82bc45bcfe9e85ec0a8711b658d27063.png',
      style: commonStyle(40, 50, 400),
    },
    pic: [
      {
        url: 'https://pagedoo.pay.qq.com/material/@platform/342085455f07d60a883d58a5a84885d7.png',
        style: commonStyle(42, 230, 100),
      },
    ],
    fgd: {
      url: 'https://pagedoo.pay.qq.com/material/@platform/0c6cb3383818d25977254c53fc640470.png',
      style: commonStyle(-2, 650, 461),
    },
    human: {
      url: '',
      style: commonStyle(0, 160, 455),
    },
  },
};

type AdPluginFn = (
  script: Script,
  getKey: () => number,
  getTimeLineIndex: () => number
) => SinglePlugin;

// 添加视频
const scriptAdVideoPlugin: AdPluginFn = (
  _script: Script,
  getKey,
  getTimeLineIndex
) => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimeLineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveVideo(
        _key,
        'https://pagedoo.pay.qq.com/material/@platform/770b19a4f9abebeaee9c92a79da2e41d.mp4' ||
          '',
        allViews['广告联调模板'].video
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};
const scriptAdBkgPlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveBkg(
        _key,
        templateMap[script.adExtendData.templateId].bkg.url,
        '',
        templateMap[script.adExtendData.templateId].bkg.style,
        ''
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};

// 音色
const scriptAdSoundPlugin: AdPluginFn = (
  _script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const defaultVoiceItem = _script.adExtendData?.defaultVoiceItem;
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveSound(
        _key,
        {
          platform: 'azure',
          speed: 1,
          voiceExtendConfig:
            '{"ShortName":"zh-CN-XiaochenNeural","Gender":"Female","Locale":"zh-CN"}',
          voiceId: 'zh-CN-XiaochenNeural',
          ...(defaultVoiceItem
            ? {
                platform: defaultVoiceItem.platform,
                speed: defaultVoiceItem.speed,
                voiceExtendConfig: defaultVoiceItem.voiceExtendConfig,
                voiceId: defaultVoiceItem.voiceId,
              }
            : undefined),
        },
        allViews['广告联调模板'].hidden
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};

// 直播话术
const scriptAdSpeechPlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveSpeechAD(
        _key,
        'aigc',
        script.adExtendData?.productList
          ? script.adExtendData?.productList
          : [
              {
                after_sale: '享受2年质保,7天无理由退换货。',
                detail_info:
                  'iPhone 13 Pro采用6.1英寸SuperRetina XDR显示屏,配备A15 Bionic芯片,拥有业界顶级的摄像头系统和5G连接。',
                endorsement: '经多位用户好评,被誉为目前最出色的iPhone手机。',
                evaluation:
                  '专业媒体测评,该机拥有出色的拍照性能、出色的续航和出色的性能表现。',
                header_imgs: ['https://tdesign.gtimg.com/site/avatar.jpg'],
                price: '99元三件',
                product_id: 'MocKProduct2',
                product_name: 'Apple iPhone 13 Pro',
                promotion: '下单立享9折优惠,赠送无线充电器一个。',
                selling_points: '5G连接、超级摄像头、强大的A15 Bionic芯片',
                user_pain_points: '续航时间短、价格偏高',
              },
            ],
        allViews['广告联调模板'].hidden
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};
const scriptAdQAPlugin: AdPluginFn = (
  _script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveQA(
        _key,
        [
          // {
          //   id: '234234',
          //   question: '这是一个问题',
          //   answer: '这是回答',
          // },
        ],
        allViews['广告联调模板'].hidden
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};
const scriptAdBackgroundMusicPlugin: AdPluginFn = (
  _script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveBackgroundMusic(
        _key,
        20,
        [],
        true,
        allViews['广告联调模板'].hidden
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};
// 标题
const scriptAdTitlePlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveImage(
        _key,
        templateMap[script.adExtendData.templateId].title.url,
        templateMap[script.adExtendData.templateId].title.style
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};
// 前景
const scriptAdForegroundPlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const _key = getKey();
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component: liveImage(
        _key,
        templateMap[script.adExtendData.templateId].fgd.url,
        templateMap[script.adExtendData.templateId].fgd.style
      ),
      duration: adTemplateDuration,
      id: uuid(),
      key: _key,
      offset: 0,
    };
  };
};
const scriptAdVirtualManPlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    const timeLineIndex = getTimelineIndex();
    const key = getKey();
    const defaultDip = script.adExtendData?.defaultDipItem;
    const defaultVoiceItem = script.adExtendData?.defaultVoiceItem;
    let tagInfo = {
      tag: [] as string[],
    };
    if (defaultDip) {
      try {
        tagInfo = JSON.parse(defaultDip.dip_feature_keywords);
      } catch (e) {}
    }
    const component = customVirtualManWave(
      key,
      '',
      templateMap[script.adExtendData.templateId].human.style,
      {
        key: '8a5a4a243d204589aceedc656024a83b',
        appkey: '1576283ab39a49419138d5ee8ee40586',
        type: 'qq',
        enabled: true,
        img: 'https://pagedoo.pay.qq.com/material/@platform/16bf81030237b145b013fa431f18362e.png',
        // script.adExtendData?.templateId
        //   ? templateMap[script.adExtendData.templateId].human
        //   : 'https://pagedoo.pay.qq.com/material/@platform/16bf81030237b145b013fa431f18362e.png',
        label: '小好-白色裙子-坐姿',
        chromaKey: 0.5,
        __env: 'ams',
        // 存在默认选中的数字人，进行设置
        ...(defaultDip
          ? {
              key: defaultDip.virtual_man_key || defaultDip.dip_id,
              asset: !!defaultDip.virtual_man_key,
              img: defaultDip.dip_image,
              label: defaultDip.dip_name,
              appkey: defaultDip.platform_account_id,
              // 实景数字人不需要绿幕抠图
              ...(tagInfo.tag.includes('background')
                ? {
                    chromaKey: 0,
                  }
                : undefined),
            }
          : undefined),
      },
      {
        isSegmentTexts: true,
        voiceConfig: {
          currentVoiceItem: {
            platform: 'azure',
            speed: 1,
            voiceExtendConfig:
              '{"ShortName":"zh-CN-XiaochenNeural","Gender":"Female","Locale":"zh-CN"}',
            voiceId: 'zh-CN-XiaochenNeural',
            ...(defaultVoiceItem
              ? {
                  platform: defaultVoiceItem.platform,
                  speed: defaultVoiceItem.speed,
                  voiceExtendConfig: defaultVoiceItem.voiceExtendConfig,
                  voiceId: defaultVoiceItem.voiceId,
                }
              : undefined),
          },
        },
      }
    );
    Object.assign(component.data, {
      __component_name: '主播',
      __component_sub_name: '主播',
    });
    conf.timeline[timeLineIndex].node[0] = {
      __config: { thumbnail: '', title: '', type: 'component' },
      actualDuration: 0,
      component,
      duration: adTemplateDuration,
      id: uuid(),
      key,
      offset: 0,
    };
  };
};

const liveTextPlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimeLineIndex: () => number
): SinglePlugin => {
  return (conf: PlayConfig) => {
    if (script.adExtendData?.templateId) {
      templateMap[script.adExtendData.templateId].text?.forEach((t) => {
        const key = getKey();
        const timeLineIndex = getTimeLineIndex();
        conf.timeline[timeLineIndex].node[0] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveText(key, t.fontStyle, t.text, t.commonStyle, {
            animation: 'none',
            speed: 40,
          }),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      });
    }
  };
};
const liveProductPlugin: AdPluginFn = (script, getKey, getTimelineIndex) => {
  console.log('liveproduct script ', script);
  return (conf) => {
    const key = getKey();
    const timelineIdx = getTimelineIndex();
    let idx = 0;
    if (
      Array.isArray(script.adExtendData?.productList) &&
      script.adExtendData.productList.length > 0
    ) {
      const { productList } = script.adExtendData;
      while (idx < productList.length) {
        conf.timeline[timelineIdx].node[idx] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveProduct(
            key,
            { product: productList[idx].product_id },
            allViews['广告联调模板'].hidden
          ),
          duration: adTemplateDuration,
          id: uuid(),
          key,
          offset: idx * adTemplateDuration,
        };
        idx += 1;
      }
    }
  };
};
const scriptAdPicPlugin: AdPluginFn = (
  script: Script,
  getKey,
  getTimelineIndex
): SinglePlugin => {
  return (conf: PlayConfig) => {
    if (script.adExtendData?.templateId) {
      templateMap[script.adExtendData.templateId].pic.map((p) => {
        const _key = getKey();
        const timeLineIndex = getTimelineIndex();
        conf.timeline[timeLineIndex].node[0] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(_key, p.url, p.style),
          duration: adTemplateDuration,
          id: uuid(),
          key: _key,
          offset: 0,
        };
      });
    }
    // conf.timeline[2].node[0] = {
    //   __config: { thumbnail: '', title: '', type: 'component' },
    //   actualDuration: 0,
    //   component: liveImage(
    //     key,
    //     'https://pagedoo.pay.qq.com/material/@platform/342085455f07d60a883d58a5a84885d7.png' ||
    //       '',
    //     allViews['广告联调模板'].price
    //   ),
    //   duration: adTemplateDuration,
    //   id: uuid(),
    //   key,
    //   offset: 0,
    // };
  };
};

const fns = [
  scriptAdBkgPlugin,
  scriptAdVirtualManPlugin,
  scriptAdForegroundPlugin,
  scriptAdPicPlugin,
  liveTextPlugin,
  // scriptAdVideoPlugin,
  // scriptAdSoundPlugin, // 音色组件和数字人组件已经合并，这里不需要单独引入
  scriptAdSpeechPlugin,
  scriptAdQAPlugin,
  scriptAdBackgroundMusicPlugin,
  liveProductPlugin,
  // scriptAdTitlePlugin,
];

export const createAdPlugins = (
  script: Script
): {
  plugins: SinglePlugin[];
  timelineLength: number;
} => {
  let timelineIdx = -1;
  let key = 1001;
  const getTimelineIdx = () => {
    timelineIdx += 1;
    return timelineIdx;
  };
  // eslint-disable-next-line no-plusplus
  const getKey = () => key++;

  const plugins: SinglePlugin[] = [
    ...fns.map((plugin) => plugin(script, getKey, getTimelineIdx)),
  ];
  let len = fns.length;
  script.adExtendData &&
    Object.values(templateMap[script.adExtendData.templateId]).forEach((val) =>
      Array.isArray(val) ? (len += val.length - 1) : undefined
    );
  return {
    plugins,
    timelineLength: len,
  };
};
