import {
  COMPONENT_LIST,
  Direction,
  RESOURCES_TYPE,
  VideoTemplateSize,
} from '../constants';
import { GameVideoBaseConfig } from './baseConfig';
import { IConfig, IUsage } from './index';
import { WhiteYellow } from './base/showyTextConfig';
import { commonVideoStyle } from '../utils';
import { AiXiaoShu } from './base/timbreConfig';

/**
 * 全屏视频含贴片无字幕
 */
export const GameNoNarrationConfig: IConfig = {
  style: {
    ...GameVideoBaseConfig.style,
    videoContent: commonVideoStyle(0, 0, VideoTemplateSize.width), // 视频
    showyText: commonVideoStyle(80, 500, 140), // 花字
  },
  components: {
    ...GameVideoBaseConfig.components,
    showyText: {
      ...GameVideoBaseConfig.components.showyText,
      style: WhiteYellow,
    },
  },
  materialMap: {
    ...GameVideoBaseConfig.materialMap,
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/bbaa2edfb5ec37cb42efc53a00859f54.png',
  },
};

/**
 * 全屏视频含贴片无字幕
 */
export const GameNoNarrationComponentUsage = (
  gameConfig: IConfig
): IUsage[] => {
  return [
    {
      // 背景图
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: gameConfig.style.background,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.bkg || '',
        isSingle: true,
      },
    },
    {
      // 视频
      componentKey: COMPONENT_LIST.LIVE_VIDEO,
      componentStyle: gameConfig.style.videoContent,
      componentConfig: {
        componentConfigCommonType: 'template',
        videoType: Direction.Vertical,
      },
    },
    {
      // 贴片
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.liveImage,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resources.image_address`,
      },
    },
    {
      // 折扣背景图
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.discountBkg,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.discountBkg || '',
        isSingle: true,
      },
    },
    {
      // 折扣文字
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: gameConfig.style.discountText,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.discountText?.text,
        fontStyle: gameConfig.components.discountText?.style,
        isSingle: true,
      },
    },
    {
      // 折扣描述
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: gameConfig.style.discountDesc,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.discountDesc?.text,
        fontStyle: gameConfig.components.discountDesc?.style,
        isSingle: true,
      },
    },
    {
      // 花字
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.flowerText`,
        fontStyle: gameConfig.components.showyText?.style,
        animation: gameConfig.components.showyText?.animation,
      },
      componentStyle: gameConfig.style.showyText,
    },
    {
      // 特效动画
      componentKey: COMPONENT_LIST.SPECIAL_EFFECTS_ANIMATION,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.image_address`,
        animation: gameConfig.components.specialEffectsAnimation?.animation,
      },
      componentStyle: gameConfig.style.specialEffectsAnimation,
    },
    {
      // 特色音效
      componentKey: COMPONENT_LIST.FEATURED_SOUND,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource`,
        soundConfig: gameConfig.components.featuredSoundCofig,
      },
    },
  ];
};
