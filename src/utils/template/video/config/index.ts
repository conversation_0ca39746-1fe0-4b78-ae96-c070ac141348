import { VIDEO_UNDERSTANDING_ID_ENUM } from '../../templates';
import {
  GameNoNarrationComponentUsage,
  GameNoNarrationConfig,
} from './GameNoNarrationConfig';
import { GameVideoBaseConfig } from './baseConfig';
import { GameWithNumberPersonConfig } from './GameWithNumberPersonConfig';
import { ComponentList, IComponentConfigCommon } from '../type';
import {
  GameNarrationComponentUsage,
  GameNarrationConfig,
} from './GameNarrationConfig';
import { gameMockScript } from '../../mock/game';
import { gameLandScapeScript } from '../../mock/game-landscape';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from './GameWithNPStandConfig';
import { IAnimationConfig } from '../../plugins/scriptLiveShowyTextPlugin';
import { ILiveTextAnimationConfig } from '../../plugins/scriptLiveTextPlugin';
import type { FontSelectorData } from '@tencent/formily-cms/esm/font-selector/type';
import { ILiveFeaturedSoundConfig } from '@/utils/play-component';
import { GAME_X_GAME, GAME_X_GAME_CIRCLE } from './templates/gameXGame';
import { GAME_X_FRESHANDCUTE } from './templates/gameXFreshAndCute';
import {
  GAME_X_NL_CN,
  GAME_X_NNL_CN,
  GAME_X_NPS_CN,
} from './templates/gameXCNStyle';
import { GAME_X_COMICS } from './templates/gameXComics';
import { GAME_X_NPS_DOT } from './templates/gameXDot';
import { GAME_X_NPS_Leisure } from './templates/gameXLeisure';
import { GAME_X_CHESS_CARDS } from './templates/gameXChessCards';
import { GAME_X_BUSINESS } from './templates/gameXBusiness';
import { EnumToUnion, UnionToInter } from '../../type';
import { BaseScript } from '@/components/ScriptForm/type';
import { GAME_OVERSEAS } from './templates/overseas';

type IdUnion = EnumToUnion<typeof VIDEO_UNDERSTANDING_ID_ENUM>;

type IBaseStyle = Partial<typeof GameVideoBaseConfig.style>;
type IComponents = typeof GameWithNumberPersonConfig.components;
type IMaterialMap = Partial<typeof GameVideoBaseConfig.materialMap>;
export type IcomponentConfigUnion = Partial<IComponents[keyof IComponents]>;
type IcomponentConfig = Partial<UnionToInter<IcomponentConfigUnion>>;
// componentConfig通用协议
export interface IComponentConfigCommonBase {
  value?: IcomponentConfig | string; // 模版的值 如果componentConfigCommonType是script 则为脚本的兜底值
  isSingle?: boolean; // 定义节点是否独占整条轨道
  animation?: ILiveTextAnimationConfig | Partial<IAnimationConfig>; // 动画
  fontStyle?: FontSelectorData; // 字体样式
  videoType?: string; // 视频类型
  soundConfig?: ILiveFeaturedSoundConfig['soundConfig']; // 特色音色配置
}

export interface IConfig {
  style: IBaseStyle;
  components: Partial<IComponents>;
  materialMap?: IMaterialMap;
}
export interface IUsage {
  componentKey: ComponentList;
  componentStyle?: IBaseStyle[keyof IBaseStyle];
  componentConfig?: IComponentConfigCommon<
    | (IComponentConfigCommonBase & { componentConfigCommonType: 'script' })
    | (IComponentConfigCommonBase & { componentConfigCommonType: 'template' })
  >;
}

export type VideoTemplateConfigType = {
  [key in string]: {
    /**
     * 样式配置
     */
    // config: IConfig;
    /**
     * 数组顺序就是层级关系
     */
    usage: IUsage[];
    mockScript: BaseScript;
  };
};

/**
 * 竖版数字人
 */
export const VideoTemplateNPSConfig: VideoTemplateConfigType = {
  /**
   * 站姿数字人，战斗游戏 全部模板
   */
  ...GAME_X_GAME,
  /**
   * 清新&可爱 全部模板
   */
  ...GAME_X_FRESHANDCUTE,
  /**
   * 国风
   */
  ...GAME_X_NPS_CN,
  /**
   * 漫画&二次元 全部模板
   */
  ...GAME_X_COMICS,
  /**
   * 棋牌 全部模板
   */
  ...GAME_X_CHESS_CARDS,
  /**
   * 商务 全部模板
   */
  ...GAME_X_BUSINESS,
  /*
   * 通用点线面
   */
  ...GAME_X_NPS_DOT,
  /**
   * 休闲的模板
   */
  ...GAME_X_NPS_Leisure,
};

/**
 * 圆圈数字人
 */
export const VideoTemplateNPConfig: VideoTemplateConfigType = {
  ...GAME_X_GAME_CIRCLE,
};

/**
 * 无数字人，有字幕，有旁白
 */
export const VideoTemplateNLConfig: VideoTemplateConfigType = {
  ...GAME_X_NL_CN,
};

/**
 * 无数字人，无字幕，无旁白
 */

export const VideoTemplateNNLConfig: VideoTemplateConfigType = {
  ...GAME_X_NNL_CN,
};

/**
 * 横屏全屏模版
 */
export const VideoHorizontalScreenTemplateConfig: VideoTemplateConfigType = {
  ...GAME_OVERSEAS,
};

/**
 * 模版配置
 * 模版 id  --> 模版配置
 * TODO: 类型修复 ！！！
 */
export const VideoTemplateConfig: VideoTemplateConfigType = {
  /**
   * 全屏视频含贴片带字幕
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NARRATION_PORTRAIT]: {
    usage: GameNarrationComponentUsage(GameNarrationConfig),
    mockScript: gameMockScript,
  },
  /**
   * 全屏视频含贴片无字幕
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NO_NARRATION_PORTRAIT]: {
    usage: GameNoNarrationComponentUsage(GameNoNarrationConfig),
    mockScript: gameMockScript,
  },
  ...VideoTemplateNPSConfig,
  ...VideoTemplateNPConfig,
  ...VideoTemplateNLConfig,
  ...VideoTemplateNNLConfig,
  ...VideoHorizontalScreenTemplateConfig,
};
