import type { FontSelectorData } from '@tencent/formily-cms/esm/font-selector/type';
export const YellowWhite: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff',
  ],
  letterSpacing: 0,
  fontSize: 32,
  width: 0,
  color: {
    color: '#FFF732',
    realColor: '#FFF732',
    show: true,
  },
  fontWeight: 400,
  style:
    '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n  transform: rotate(-6deg);\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        -webkit-text-stroke: 0.04rem #FF70E8;\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n\n          left: -0.08rem;\n          top: -0.04rem;\n          color: #fff;\n          font-weight: 400;\n          -webkit-text-stroke: 0.04rem #5B41A5;\n          z-index: 1;\n        }\n      }',
  ellipsis: 0,
};

/**
 * 白色加粗
 */
export const White: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff',
  ],
  letterSpacing: 0,
  fontSize: 36,
  width: 0,
  color: {
    color: '#FFFFFF',
    realColor: '#FFFFFF',
    show: true,
  },
  fontWeight: 400,
  ellipsis: 0,
};

export const WhiteYellow: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff',
  ],
  letterSpacing: 0,
  fontSize: 32,
  width: 0,
  color: {
    color: '#FFFFFF',
    realColor: '#FFFFFF',
    show: true,
  },
  fontWeight: 400,
  style:
    '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n  transform: rotate(-6deg);\n       span {\n        \n    position: relative;\n    display: inline-block;\n\n        -webkit-text-stroke: 0.08rem transparent;\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n\n          background-image: linear-gradient(0deg, #FF00B8, #FFC700);\n          background-clip: text;\n       font-weight: inherit;\n        }\n      }',
  ellipsis: 0,
};

export const WhiteNormal: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: ['PingFang SC'],
  // fontFamily: ['HYYaKuHeiW'],
  width: 0,
  color: {
    color: '#FFFFFF',
    realColor: '#FFFFFF',
    show: true,
  },
  fontWeight: 600,
  ellipsis: 0,
};

/**
 * 红色描边
 */
export const WhiteRedNormal: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: ['PingFang SC'],
  // fontFamily: ['HYYaKuHeiW'],
  width: 0,
  color: {
    color: '#FFFFFF',
    realColor: '#FFFFFF',
    show: true,
  },
  fontWeight: 600,
  style:
    '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n       span {\n        \n    position: relative;\n    display: inline-block;\n\n        -webkit-text-stroke: 0.08rem transparent;\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n\n          background-image: linear-gradient(0deg, #E26D50, #E26D50);\n          background-clip: text;\n       font-weight: inherit;\n        }\n      }',
  ellipsis: 0,
};

/**
 * 紫色描边
 */
export const WhitePNormal: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff',
  ],
  // fontFamily: ['HYYaKuHeiW'],
  width: 0,
  color: {
    color: '#FFFFFF',
    realColor: '#FFFFFF',
    show: true,
  },
  fontWeight: 400,
  style: `padding: 0.2rem;
    margin: -0.2rem;
      span {
        
    position: relative;
    display: inline-block;
        &::before {
          
    position: absolute;
    content: attr(data-text);
    left: 0;
    top: 0;
    font-weight: 400;
    z-index: -1;

          -webkit-text-stroke: 0.06rem #AD00FF;
          color: #AD00FF;
        }`,
  ellipsis: 0,
};

/**
 * 白色 HY 雅酷
 */
export const WhiteHYNoraml: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff',
  ],
  width: 0,
  color: {
    color: '#FFFFFF',
    realColor: '#FFFFFF',
    show: true,
  },
  fontWeight: 300,
  ellipsis: 0,
};

/**
 * 黑色镇魂
 */
export const BlackZHWXNormal: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff',
  ],
  width: 0,
  color: {
    color: '#000',
    realColor: '#000',
    show: true,
  },
  fontWeight: 400,
  ellipsis: 0,
};

/**
 * HYCuYuan
 */
export const BlackHYCuYuan: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font30.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font30.woff',
  ],
  width: 0,
  color: {
    color: '#3E0B0B',
    realColor: '#3E0B0B',
    show: true,
  },
  fontWeight: 400,
  ellipsis: 0,
};

/**
 * HYCuYuan white
 */
export const WhiteHYCuYuan: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font30.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font30.woff',
  ],
  width: 0,
  color: {
    color: '#fff',
    realColor: '#fff',
    show: true,
  },
  fontWeight: 400,
  ellipsis: 0,
};

/**
 * HYZhuZiZhiYuan
 */
export const WhiteHYZhuZiZhiYuan: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1.1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: ['Yuanti SC,YouYuan'],
  width: 0,
  color: {
    color: '#fff',
    realColor: '#fff',
    show: true,
  },
  fontWeight: 600,
  ellipsis: 0,
};

const LinearBase: FontSelectorData = {
  text: '',
  rem: 50,
  useImage: 0,
  lineHeight: 1.1,
  textAlign: 'left',
  letterSpacing: 0,
  fontSize: 32,
  fontFamily: ['Yuanti SC,YouYuan'],
  width: 0,
  color: {
    color: '#fff',
    realColor: '#fff',
    show: true,
  },
  fontWeight: 600,
  ellipsis: 0,
  style: ` background: linear-gradient(180deg, #FFFBE1, #fff); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
  -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
  -webkit-text-fill-color: transparent;/*给文字设置成透明*/`,
};

/**
 * 浅绿色渐变
 */
export const LinearA: FontSelectorData = {
  ...LinearBase,
  style: ` background: linear-gradient(180deg, #FFFBE1, #fff); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
  -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
  -webkit-text-fill-color: transparent;/*给文字设置成透明*/`,
};

export const LinearB: FontSelectorData = {
  ...LinearBase,
  style: ` background: linear-gradient(180deg, #D6DEFF, #fff); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
  -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
  -webkit-text-fill-color: transparent;/*给文字设置成透明*/`,
};
