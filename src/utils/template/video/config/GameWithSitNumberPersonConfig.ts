import { IConfig, IUsage } from './index';
import {
  COMPONENT_LIST,
  RESOURCES_TYPE,
  VideoTemplateSize,
} from '../constants';
import { Xiaoxiao } from './base/virtualMan';
import { GameVideoBaseConfig } from './baseConfig';
import { GLOBAL_FIELDS } from '@/utils/template/type';
import { commonVideoStyle } from '../utils';

/**
 * （坐姿数字人）
 */
export const GameWithSitNumberPersonConfig = {
  style: {
    ...GameVideoBaseConfig.style,
    human: commonVideoStyle(-24, 220, VideoTemplateSize.width),
  },
  components: {
    ...GameVideoBaseConfig.components,
    virtuanman: {
      dataConfig: Xiaoxiao,
      style: {},
    },
  },
  materialMap: {
    ...GameVideoBaseConfig.materialMap,
  },
} as const;

/**
 * （坐姿数字人）
 */

export const GameWithSitNumberPersonConfigUsage = (
  gameConfig: IConfig
): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
    },
    {
      // 背景图
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: gameConfig.style.background,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.bkg || '',
        isSingle: true,
      },
    },
    {
      // 视频
      componentKey: COMPONENT_LIST.LIVE_VIDEO,
      componentStyle: gameConfig.style.videoContent,
    },
    {
      // 模板文字背景
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.tipImage,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.materialMap?.tipImage,
      },
    },
    {
      // 模板标题名
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.templateName?.style,
        value: gameConfig.components.templateName?.text,
        isSingle: true,
      },
      componentStyle: gameConfig.style.templateName,
    },
    {
      // 模板文字 -- 描述
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: gameConfig.style.tipText,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.tipText?.style,
        value: gameConfig.components.tipText?.text,
        isSingle: true,
      },
    },
    {
      // 贴片
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.liveImage,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resources.image_address`,
      },
    },
    {
      // 花字
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.flowerText`,
        fontStyle: gameConfig.components.showyText?.style,
        animation: gameConfig.components.showyText?.animation,
      },
      componentStyle: gameConfig.style.showyText,
    },
    {
      // 特效动画
      componentKey: COMPONENT_LIST.SPECIAL_EFFECTS_ANIMATION,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.image_address`,
        animation: gameConfig.components.specialEffectsAnimation?.animation,
      },
      componentStyle: gameConfig.style.specialEffectsAnimation,
    },
    {
      // 数字人
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.virtuanman,
      },
      componentStyle: gameConfig.style.human,
    },
    {
      // 视频字幕
      componentKey: COMPONENT_LIST.VIDEO_SUBTITLE,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.subtitle,
      },
      componentStyle: gameConfig.style.subtitle,
    },
    {
      // 特色音效
      componentKey: COMPONENT_LIST.FEATURED_SOUND,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource`,
        soundConfig: gameConfig.components.featuredSoundCofig,
      },
    },
  ];
};
