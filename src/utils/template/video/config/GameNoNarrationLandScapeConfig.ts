// 清新斜条纹上下框模板无字幕（无数字人）
import { GLOBAL_FIELDS } from '@/utils/template/type';
import { IConfig, IUsage } from '.';
import { COMPONENT_LIST, RESOURCES_TYPE } from '../constants';
import { GameVideoBaseConfig } from './baseConfig';

/**
 * 清新斜条纹上下框模板无字幕（无数字人）
 */
export const GameNoNarrationLandScapeConfig: IConfig = {
  ...GameVideoBaseConfig,
  components: GameVideoBaseConfig.components,
};

/**
 * 清新斜条纹上下框模板无字幕（无数字人）
 */
export const GameNoNarrationLandScapeConfigUsage = (
  gameConfig: IConfig
): IUsage[] => {
  return [
    {
      // 背景图
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: gameConfig.style.background,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.bkg || '',
        isSingle: true,
      },
    },
    {
      // 视频
      componentKey: COMPONENT_LIST.LIVE_VIDEO,
      componentStyle: gameConfig.style.videoContent,
    },
    {
      // 模板文字背景
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.tipImage,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.materialMap?.tipImage,
        isSingle: true,
      },
    },
    {
      // 小标题 文字
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: gameConfig.style.tipText,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.tipText?.style,
        value: gameConfig.components.tipText?.text,
        isSingle: true,
      },
    },
    {
      // 模板名 花字
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.templateName?.style,
        animation: gameConfig.components.templateName?.animation,
        value: gameConfig.components.templateName?.text,
        isSingle: true,
      },
      componentStyle: gameConfig.style.templateName,
    },
    {
      // 花字
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.flowerText`,
        fontStyle: gameConfig.components.showyText?.style,
        animation: gameConfig.components.showyText?.animation,
      },
      componentStyle: gameConfig.style.showyText,
    },
    {
      // 贴片
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.liveImage,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resources.image_address`,
      },
    },
    {
      // 特效动画
      componentKey: COMPONENT_LIST.SPECIAL_EFFECTS_ANIMATION,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.image_address`,
        animation: gameConfig.components.specialEffectsAnimation?.animation,
      },
      componentStyle: gameConfig.style.specialEffectsAnimation,
    },
    {
      // 特色音效
      componentKey: COMPONENT_LIST.FEATURED_SOUND,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource`,
        soundConfig: gameConfig.components.featuredSoundCofig,
      },
    },
  ];
};
