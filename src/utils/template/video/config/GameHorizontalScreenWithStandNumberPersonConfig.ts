import { IConfig, IUsage } from './index';
import {
  COMPONENT_LIST,
  horizontalScreenVideoTemplateSize,
} from '../constants';
import { XiaoMei2 } from './base/virtualMan';
import { GameVideoBaseConfig } from './baseConfig';
import { commonVideoStyle } from '../utils';
import { jaJPNanamiNeural } from './base/timbreConfig';

/**
 * 横屏视频带站姿数字人
 */
export const GameHorizontalScreenWithStandNumberPersonConfig = {
  style: {
    videoContent: commonVideoStyle(
      0,
      0,
      horizontalScreenVideoTemplateSize.width,
      horizontalScreenVideoTemplateSize.width
    ), // 视频
    human: commonVideoStyle(
      1400,
      420,
      600,
      horizontalScreenVideoTemplateSize.width
    ), // 数字人
    subtitle: commonVideoStyle(
      360,
      900,
      900,
      horizontalScreenVideoTemplateSize.width
    ), // 字幕
  },
  components: {
    ...GameVideoBaseConfig.components,
    virtuanman: {
      dataConfig: XiaoMei2,
      voiceConfig: jaJPNanamiNeural,
      style: {},
    },
    subtitle: {
      ...GameVideoBaseConfig.components.subtitle,
      fontSize: 14,
      width: 260,
      textAlign: 'center',
      lineHeight: 1.2,
    },
  },
  materialMap: {},
} as const;

/**
 * 清新斜条纹上下框模板带字幕（有数字人）
 */

export const GameHorizontalScreenWithStandNumberPersonConfigUsage = (
  gameConfig: IConfig
): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
    },
    {
      // 视频
      componentKey: COMPONENT_LIST.LIVE_VIDEO,
      componentStyle: gameConfig.style.videoContent,
    },
    {
      // 数字人
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.virtuanman,
      },
      componentStyle: gameConfig.style.human,
    },
    {
      // 视频字幕
      componentKey: COMPONENT_LIST.VIDEO_SUBTITLE,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.subtitle,
      },
      componentStyle: gameConfig.style.subtitle,
    },
  ];
};
