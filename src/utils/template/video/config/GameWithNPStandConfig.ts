import { IConfig, IUsage } from './index';
import { COMPONENT_LIST, RESOURCES_TYPE } from '../constants';
import { Taxi2 } from './base/virtualMan';
import {
  DEFAULT_ANIMATION,
  DEFAULT_CENTER,
  GameVideoBaseConfig,
} from './baseConfig';
import { commonVideoStyle } from '../utils';
import { WhiteNormal } from './base/showyTextConfig';
import { GLOBAL_FIELDS } from '@/utils/template/type';
import { AiXiaoYe } from './base/timbreConfig';

/**
 * 游戏视频--数字人 -- 站姿
 */
export const GameWithNPStandConfig = {
  style: {
    ...GameVideoBaseConfig.style,
    human: commonVideoStyle(206, 345, 180),
    templateName: commonVideoStyle(0, 89, 192),
    tipText: commonVideoStyle(0, 135, 168),
    tipImage: commonVideoStyle(93, 115, 188),
  },
  components: {
    ...GameVideoBaseConfig.components,
    virtuanman: {
      dataConfig: Taxi2,
      voiceConfig: AiXiaoYe,
      style: {},
    },
    templateName: {
      text: '偏通用的模版',
      style: { ...WhiteNormal, ...DEFAULT_CENTER },
      animation: DEFAULT_ANIMATION,
    },
  },
  materialMap: {
    ...GameVideoBaseConfig.materialMap,
    // https://pagedoo.pay.qq.com/material/@platform/5690d4bd22f7c23e7198139addc00dab.png
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/f45a25df1174e9f7bf0c35b21823f9e6.png',
  },
} as const;

/**
 * 游戏视频--数字人
 */

export const GameWithNPSConfigUsage = (gameConfig: IConfig): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
    },
    {
      // 背景图
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: gameConfig.style.background,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.bkg || '',
        isSingle: true,
      },
    },
    {
      // 视频
      componentKey: COMPONENT_LIST.LIVE_VIDEO,
      componentStyle: gameConfig.style.videoContent,
    },
    {
      // 模板文字背景
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.tipImage,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.materialMap?.tipImage,
      },
    },
    {
      // 模板标题名
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.templateName?.style,
        value: gameConfig.components.templateName?.text,
        isSingle: true,
      },
      componentStyle: gameConfig.style.templateName,
    },
    {
      // 模板文字 -- 描述
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: gameConfig.style.tipText,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.tipText?.style,
        value: gameConfig.components.tipText?.text,
        isSingle: true,
      },
    },
    {
      // 贴片
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.liveImage,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resources.image_address`,
      },
    },
    {
      // 花字
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.flowerText`,
        fontStyle: gameConfig.components.showyText?.style,
        animation: gameConfig.components.showyText?.animation,
      },
      componentStyle: gameConfig.style.showyText,
    },
    {
      // 特效动画
      componentKey: COMPONENT_LIST.SPECIAL_EFFECTS_ANIMATION,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.image_address`,
        animation: gameConfig.components.specialEffectsAnimation?.animation,
      },
      componentStyle: gameConfig.style.specialEffectsAnimation,
    },
    {
      // 数字人
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.virtuanman,
      },
      componentStyle: gameConfig.style.human,
    },
    {
      // 视频字幕
      componentKey: COMPONENT_LIST.VIDEO_SUBTITLE,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.subtitle,
      },
      componentStyle: gameConfig.style.subtitle,
    },
    {
      // 特色音效
      componentKey: COMPONENT_LIST.FEATURED_SOUND,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource`,
        soundConfig: gameConfig.components.featuredSoundCofig,
      },
    },
  ];
};
