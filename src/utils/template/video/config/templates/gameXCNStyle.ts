// 国风模板

import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '..';
import { GLOBAL_FIELDS } from '@/utils/template/type';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../GameWithNPStandConfig';
import { commonVideoStyle } from '../../utils';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import { DEFAULT_CENTER } from '../baseConfig';
import { BlackZHWXNormal } from '../base/showyTextConfig';
import { GameNarrationLandScapeConfigUsage } from '../GameNarrationLandScapeConfig';
import { GameNoNarrationLandScapeConfigUsage } from '../GameNoNarrationLandScapeConfig';
import { ITemplateType } from './constant';
import { GameWithNumberPersonConfigUsage } from '../GameWithNumberPersonConfig';

/**
 * 整张背景
 */
const GAME_CN_FILL_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/3e04998dcd555d0fadbd44ecfe6a893f.png',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/3d8dc5c1e94e863fc9a01a772d57d0ab.png',
  BKG_2:
    'https://pagedoo.pay.qq.com/material/@platform/0df3b9a8b87f3f61bfcc4d9f1d1f9827.png',
  BKG_3:
    'https://pagedoo.pay.qq.com/material/@platform/c6bfa017f25d5d59617760ec88b98eb8.png',
};

/**
 * 文字背景
 */
const GAME_CN_TEXT_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/a4a7126ed829656ead7ba31df05c63e1.png',
};

const GAME_BASE_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '武侠修仙的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字',
};

/**
 * mock 数据
 */
const MockScript_CN = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_TITLE,
  },
};

/**
 * 元素位置
 */
const CN_TEXT_STYLE_POSITION = {
  ...GameWithNPStandConfig.style,
  templateName: commonVideoStyle(0, 70, 192),
  tipText: commonVideoStyle(-15, 120, 168),
  tipImage: commonVideoStyle(93, 85, 268),
};

/**
 * 创建统一的样式模板
 * TODO: 字体包太大，暂时先用这个
 * @param key
 * @returns
 */
const createCNUsage = (
  key: keyof typeof GAME_CN_FILL_BKG,
  usageType: ITemplateType
) => {
  const UsageGetter = {
    NPS: GameWithNPSConfigUsage,
    NL: GameNarrationLandScapeConfigUsage,
    NNL: GameNoNarrationLandScapeConfigUsage,
    NP: GameWithNumberPersonConfigUsage,
  };
  return UsageGetter[usageType]({
    style: CN_TEXT_STYLE_POSITION,
    components: {
      ...GameWithNPStandConfig.components,
      templateName: {
        ...GameWithNPStandConfig.components.templateName,
        style: { ...BlackZHWXNormal, ...DEFAULT_CENTER },
        text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
      },
    },
    materialMap: {
      ...GameWithNPStandConfig.materialMap,
      bkg: GAME_CN_FILL_BKG[key],
      tipImage: GAME_CN_TEXT_BKG.BKG,
    },
  });
};

/**
 * 国风 - 竖版数字人
 */
export const GAME_X_NPS_CN: VideoTemplateConfigType = {
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN]: {
    usage: createCNUsage('BKG', 'NPS'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN_1]: {
    usage: createCNUsage('BKG_1', 'NPS'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN_2]: {
    usage: createCNUsage('BKG_2', 'NPS'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_CN_3]: {
    usage: createCNUsage('BKG_3', 'NPS'),
    mockScript: MockScript_CN,
  },
};

/**
 * 国风 - 有旁白
 */
export const GAME_X_NL_CN: VideoTemplateConfigType = {
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN]: {
    usage: createCNUsage('BKG', 'NL'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN_1]: {
    usage: createCNUsage('BKG_1', 'NL'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN_2]: {
    usage: createCNUsage('BKG_2', 'NL'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NL_CN_3]: {
    usage: createCNUsage('BKG_3', 'NL'),
    mockScript: MockScript_CN,
  },
};

/**
 * 国风 - 无旁白
 */
export const GAME_X_NNL_CN: VideoTemplateConfigType = {
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN]: {
    usage: createCNUsage('BKG', 'NNL'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN_1]: {
    usage: createCNUsage('BKG_1', 'NNL'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN_2]: {
    usage: createCNUsage('BKG_2', 'NNL'),
    mockScript: MockScript_CN,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NNL_CN_3]: {
    usage: createCNUsage('BKG_3', 'NNL'),
    mockScript: MockScript_CN,
  },
};
