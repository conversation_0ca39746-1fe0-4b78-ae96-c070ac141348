import { GAME_BASE_CHESS_CARDS_TITLE } from './index';
import { IConfig } from '../..';
import { White } from '../../base/showyTextConfig';
import { DEFAULT_ANIMATION, DEFAULT_CENTER } from '../../baseConfig';
import { GLOBAL_FIELDS } from '@/utils/template/type';

export type GAMEXCHESSCARDS_TYPE =
  | 'CHESS_CARDS1'
  | 'CHESS_CARDS2'
  | 'CHESS_CARDS3'
  | 'CHESS_CARDS4';

export const GAMEXCHESSCARDS_BKG: {
  [key in GAMEXCHESSCARDS_TYPE]: {
    whole: string;
    hole: string;
    tipImage?: string;
  };
} = {
  CHESS_CARDS1: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/72b1d0106627f77f27d9c1b7f2369a4d.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/2100c8050f6e9e24ab8dcd6072aeb05b.png',
  },
  CHESS_CARDS2: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/98cc08b6e9cb8b0e10b9fe279cdd2719.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/888b69cce243c24a82bf2ef444b87776.png',
  },
  CHESS_CARDS3: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/7fdd35bb6dd0fa2e1755e3f3e649a864.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/47be588a9c02b8e2b3fcc5e5de5808fa.png',
  },
  CHESS_CARDS4: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/246ef27a868e116a54c52f808255d1b7.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/6be8ed0515bf3d753750d3e7199e53d1.png',
  },
};

export const gameXChessCardsConfig = (
  rest: IConfig,
  type: GAMEXCHESSCARDS_TYPE,
  wholeBkg?: string // 圆圈数字人时候需要传纯色背景图
): IConfig => {
  const defaultConfig = { ...(rest || {}) };
  const { style, components, materialMap } = defaultConfig;
  const { whole, hole, tipImage } = GAMEXCHESSCARDS_BKG[type];
  const newMaterialMap = {
    ...materialMap,
    bkg: wholeBkg || whole,
    humanBg: hole,
    tipImage,
  };
  switch (type) {
    case 'CHESS_CARDS1':
      return {
        ...defaultConfig,
        style: {
          ...style,
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_CHESS_CARDS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#ffffff',
                realColor: '#ffffff',
                show: true,
              },
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        background-image: linear-gradient(180deg, #FFFFFF 12.77%, #B9EDBD 90.43%);\n        background-clip: text;\n        -webkit-text-fill-color: transparent;\n       }',
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'CHESS_CARDS2':
      return {
        ...defaultConfig,
        style: {
          ...style,
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_CHESS_CARDS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#ffffff',
                realColor: '#ffffff',
                show: true,
              },
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.1rem #184E97;\n          color: #ffffff;\n          font-weight: 400;\n        }\n        &::after {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.16rem #175296;\n          color: #53A2FF;\n          font-weight: 400;\n          z-index: -2;\n        }}',
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'CHESS_CARDS3':
      return {
        ...defaultConfig,
        style: {
          ...style,
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_CHESS_CARDS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#FDD506',
                realColor: '#FDD506',
                show: true,
              },
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.1rem #0C2922;\n          color: #FDD506;\n          font-weight: 400;\n        }\n        &::after {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.16rem #267E1F;\n          color: #267E1F;\n          font-weight: 400;\n          z-index: -2;\n        }}',
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
          tipText: {
            ...(components.tipText || {}),
            text: GAME_BASE_CHESS_CARDS_TITLE[GLOBAL_FIELDS.SUBTITLE],
            style: {
              ...(components.tipText?.style || {}),
              color: {
                color: '#FDD506',
                realColor: '#FDD506',
                show: true,
              },
              text: '',
              fontSize: 14,
              fontFamily: [],
            },
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'CHESS_CARDS4':
      return {
        ...defaultConfig,
        style: {
          ...style,
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_CHESS_CARDS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#ffffff',
                realColor: '#ffffff',
                show: true,
              },
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        background-image: linear-gradient(180deg, #D6DEFF 9.3%, #FFFFFF 90.43%);\n        background-clip: text;\n        -webkit-text-fill-color: transparent;\n       }',
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
        },
        materialMap: { ...newMaterialMap },
      };
    default:
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: { ...newMaterialMap },
      };
  }
};
