import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '../../index';
import {
  GameNarrationLandScapeConfig,
  GameNarrationLandScapeConfigUsage,
} from '../../GameNarrationLandScapeConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import {
  GameNoNarrationLandScapeConfig,
  GameNoNarrationLandScapeConfigUsage,
} from '../../GameNoNarrationLandScapeConfig';
import {
  GameWithNumberPersonConfig,
  GameWithNumberPersonConfigUsage,
} from '../../GameWithNumberPersonConfig';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../../GameWithNPStandConfig';
import { gameXChessCardsConfig } from './utils';
import { GLOBAL_FIELDS } from '@/utils/template/type';

export const GAME_BASE_CHESS_CARDS_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '棋牌风格的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};
export const gameXChessCards_mockScript = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_CHESS_CARDS_TITLE,
  },
};

/**
 * 棋牌
 * 全部配色模板
 */
// 无数字人&无字幕
const GAME_CHESS_CARDS_NO_NARRATION: VideoTemplateConfigType = {
  /**
   * 棋牌1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNoNarrationLandScapeConfig, 'CHESS_CARDS1')
    ),
    mockScript: gameXChessCards_mockScript,
  },
  /**
   * 棋牌2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNoNarrationLandScapeConfig, 'CHESS_CARDS2')
    ),
    mockScript: gameXChessCards_mockScript,
  },
  /**
   * 棋牌3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNoNarrationLandScapeConfig, 'CHESS_CARDS3')
    ),
    mockScript: gameXChessCards_mockScript,
  },
  /**
   * 棋牌4
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNoNarrationLandScapeConfig, 'CHESS_CARDS4')
    ),
    mockScript: gameXChessCards_mockScript,
  },
};
// 无数字人&带字幕
const GAME_CHESS_CARDS_NARRATION: VideoTemplateConfigType = {
  /**
   * 棋牌1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNarrationLandScapeConfig, 'CHESS_CARDS1')
    ),
    mockScript: gameXChessCards_mockScript,
  },
  /**
   * 棋牌2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNarrationLandScapeConfig, 'CHESS_CARDS2')
    ),
    mockScript: gameXChessCards_mockScript,
  },
  /**
   * 棋牌3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNarrationLandScapeConfig, 'CHESS_CARDS3')
    ),
    mockScript: gameXChessCards_mockScript,
  },
  /**
   * 棋牌4
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXChessCardsConfig(GameNarrationLandScapeConfig, 'CHESS_CARDS4')
    ),
    mockScript: gameXChessCards_mockScript,
  },
};
// 圆圈数字人&带字幕
const GAME_CHESS_CARDS_WITH_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 棋牌1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXChessCardsConfig(
          GameWithNumberPersonConfig,
          'CHESS_CARDS1',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXChessCards_mockScript,
    },
  /**
   * 棋牌2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXChessCardsConfig(
          GameWithNumberPersonConfig,
          'CHESS_CARDS2',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXChessCards_mockScript,
    },
  /**
   * 棋牌3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXChessCardsConfig(
          GameWithNumberPersonConfig,
          'CHESS_CARDS3',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXChessCards_mockScript,
    },
  /**
   * 棋牌4
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXChessCardsConfig(
          GameWithNumberPersonConfig,
          'CHESS_CARDS4',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXChessCards_mockScript,
    },
};
// 站姿数字人
const GAME_CHESS_CARDS_WITH_STAND_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 棋牌1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS1_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXChessCardsConfig(GameWithNPStandConfig, 'CHESS_CARDS1')
      ),
      mockScript: gameXChessCards_mockScript,
    },
  /**
   * 棋牌2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS2_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXChessCardsConfig(GameWithNPStandConfig, 'CHESS_CARDS2')
      ),
      mockScript: gameXChessCards_mockScript,
    },
  /**
   * 棋牌3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS3_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXChessCardsConfig(GameWithNPStandConfig, 'CHESS_CARDS3')
      ),
      mockScript: gameXChessCards_mockScript,
    },
  /**
   * 棋牌4
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CHESS_CARDS4_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXChessCardsConfig(GameWithNPStandConfig, 'CHESS_CARDS4')
      ),
      mockScript: gameXChessCards_mockScript,
    },
};

export const GAME_X_CHESS_CARDS = {
  ...GAME_CHESS_CARDS_NO_NARRATION,
  ...GAME_CHESS_CARDS_NARRATION,
  ...GAME_CHESS_CARDS_WITH_NUMBER_PERSON,
  ...GAME_CHESS_CARDS_WITH_STAND_NUMBER_PERSON,
};
