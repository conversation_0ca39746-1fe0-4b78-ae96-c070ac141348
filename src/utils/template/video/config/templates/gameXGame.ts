import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '..';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../GameWithNPStandConfig';
import { commonVideoStyle } from '../../utils';
import {
  WhiteHYNoraml,
  WhitePNormal,
  WhiteRedNormal,
} from '../base/showyTextConfig';
import { DEFAULT_CENTER } from '../baseConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import {
  GameWithNumberPersonConfig,
  GameWithNumberPersonConfigUsage,
} from '../GameWithNumberPersonConfig';
import { GLOBAL_FIELDS } from '@/utils/template/type';

/**
 * 副标题文字背景
 */
const GAME_TEXT_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/f2c24f4f495afe45b921d52427c4be2d.png',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/64d33bf3885b3fc93939ab83673de2b2.png',
  BKG_2: '',
  BKG_3:
    'https://pagedoo.pay.qq.com/material/@platform/b71c2da3e3e6ddba06f00b59649ad390.png',
};

/**
 * 完整模板背景图
 */
const GAME_FILL_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/5690d4bd22f7c23e7198139addc00dab.png',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/b536ba78d29b8ded0462daca01c675e9.png',
  BKG_2:
    'https://pagedoo.pay.qq.com/material/@platform/b6bc71370a5f51d1412bd83f2be95044.png',
  BKG_3:
    'https://pagedoo.pay.qq.com/material/@platform/517b3517940eb7c5c6af86c50eaccd8d.png',
};

const GAME_BASE_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '战斗游戏的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};

const MockScript_Game = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_TITLE,
  },
};

/**
 * 战斗游戏 竖屏数字人
 * 全部配色模板
 */
export const GAME_X_GAME: VideoTemplateConfigType = {
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhiteRedNormal, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: GAME_FILL_BKG.BKG,
        tipImage: GAME_TEXT_BKG.BKG,
      },
    }),
    mockScript: MockScript_Game,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME_1]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhitePNormal, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: GAME_FILL_BKG.BKG_1,
        tipImage: GAME_TEXT_BKG.BKG_1,
      },
    }),
    mockScript: MockScript_Game,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME_2]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 128, 192),
        tipText: commonVideoStyle(0, 168, 168),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhiteRedNormal, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: GAME_FILL_BKG.BKG_2,
        tipImage: '',
      },
    }),
    mockScript: MockScript_Game,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_GAME_3]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhiteHYNoraml, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: GAME_FILL_BKG.BKG_3,
        tipImage: GAME_TEXT_BKG.BKG_3,
      },
    }),
    mockScript: MockScript_Game,
  },
};

/**
 * 圆圈背景图，挖洞
 */
const GAME_CIRCLE_DOT_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/6250512a13fe2a68f80aa97281297f42.png',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/ba832a5b31dd12cb16cfa147f90c8924.png',
  BKG_2:
    'https://pagedoo.pay.qq.com/material/@platform/b0cb288daa3e6321fe1027e5de6cc0ec.png',
  BKG_3:
    'https://pagedoo.pay.qq.com/material/@platform/2014a46ecd22482ab03638ab13df2c8a.png',
};

/**
 * 圆圈数字人
 */
export const GAME_X_GAME_CIRCLE: VideoTemplateConfigType = {
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME]: {
    usage: GameWithNumberPersonConfigUsage({
      style: {
        ...GameWithNumberPersonConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
      },
      components: {
        ...GameWithNumberPersonConfig.components,
        templateName: {
          ...GameWithNumberPersonConfig.components.templateName,
          style: { ...WhiteRedNormal, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNumberPersonConfig.materialMap,
        humanBg: GAME_CIRCLE_DOT_BKG.BKG,
        tipImage: GAME_TEXT_BKG.BKG,
      },
    }),
    mockScript: MockScript_Game,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME_1]: {
    usage: GameWithNumberPersonConfigUsage({
      style: {
        ...GameWithNumberPersonConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
      },
      components: {
        ...GameWithNumberPersonConfig.components,
        templateName: {
          ...GameWithNumberPersonConfig.components.templateName,
          style: { ...WhitePNormal, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNumberPersonConfig.materialMap,
        humanBg: GAME_CIRCLE_DOT_BKG.BKG_1,
        tipImage: GAME_TEXT_BKG.BKG_1,
      },
    }),
    mockScript: MockScript_Game,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME_2]: {
    usage: GameWithNumberPersonConfigUsage({
      style: {
        ...GameWithNumberPersonConfig.style,
        templateName: commonVideoStyle(0, 128, 192),
        tipText: commonVideoStyle(0, 168, 168),
      },
      components: {
        ...GameWithNumberPersonConfig.components,
        templateName: {
          ...GameWithNumberPersonConfig.components.templateName,
          style: { ...WhiteRedNormal, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNumberPersonConfig.materialMap,
        humanBg: GAME_CIRCLE_DOT_BKG.BKG_2,
        tipImage: '',
      },
    }),
    mockScript: MockScript_Game,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_CIRCLE_GAME_3]: {
    usage: GameWithNumberPersonConfigUsage({
      style: {
        ...GameWithNumberPersonConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
      },
      components: {
        ...GameWithNumberPersonConfig.components,
        templateName: {
          ...GameWithNumberPersonConfig.components.templateName,
          style: { ...WhiteHYNoraml, ...DEFAULT_CENTER },
          text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNumberPersonConfig.materialMap,
        humanBg: GAME_CIRCLE_DOT_BKG.BKG_3,
        tipImage: GAME_TEXT_BKG.BKG_3,
      },
    }),
    mockScript: MockScript_Game,
  },
};
