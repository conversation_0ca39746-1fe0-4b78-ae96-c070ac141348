// 通用点线面

import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../GameWithNPStandConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import { GLOBAL_FIELDS } from '@/utils/template/type';
import { commonVideoStyle } from '../../utils';
import { DEFAULT_CENTER } from '../baseConfig';
import {
  BlackHYCuYuan,
  WhiteHYCuYuan,
  WhiteNormal,
} from '../base/showyTextConfig';
import { VideoTemplateSize } from '../../constants';

const C_BASE_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '偏通用的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};

const MockScript_Dot = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...C_BASE_TITLE,
  },
};

const DOT_FILL_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/f45a25df1174e9f7bf0c35b21823f9e6.png',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/3c9fe692a17082d78c90b39a52349077.png',
  BKG_2:
    'https://pagedoo.pay.qq.com/material/@platform/570b69f95dd9a8d50fc0de055c336ce4.png',
  BKG_3:
    'https://pagedoo.pay.qq.com/material/@platform/8aaba81a0a29a4f4994c0fcec223f36f.png',
};

const DOT_TEXT_BKG = {
  BKG: '',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/724842a297eb249df57a8bdf0d21f6ae.png',
  BKG_2:
    'https://pagedoo.pay.qq.com/material/@platform/f8c561de48d53b33da6256109ab974cb.png',
  BKG_3: '',
};

/**
 * 站姿数字人 通用点线面
 */
export const GAME_X_NPS_DOT = {
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT]: {
    usage: GameWithNPSConfigUsage(GameWithNPStandConfig),
    mockScript: MockScript_Dot,
  },
  // 原模板无副标题
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT_1]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 90, 192),
        tipText: commonVideoStyle(0, 130, 168),
        tipImage: commonVideoStyle(36, 72, 300),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhiteNormal, ...DEFAULT_CENTER },
          text: C_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: DOT_FILL_BKG.BKG_1,
        tipImage: DOT_TEXT_BKG.BKG_1,
      },
    }),
    mockScript: MockScript_Dot,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT_2]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(-20, 97, 192),
        tipText: commonVideoStyle(-20, 135, 168),
        tipImage: commonVideoStyle(0, 0, VideoTemplateSize.width),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...BlackHYCuYuan, ...DEFAULT_CENTER },
          text: C_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
        tipText: {
          ...GameWithNPStandConfig.components.tipText,
          style: {
            ...GameWithNPStandConfig.components.tipText.style,
            color: {
              color: '#3E0B0B',
              realColor: '#3E0B0B',
              show: true,
            },
          },
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: DOT_FILL_BKG.BKG_2,
        tipImage: DOT_TEXT_BKG.BKG_2,
      },
    }),
    mockScript: MockScript_Dot,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_DOT_3]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 70, 192),
        tipText: commonVideoStyle(0, 120, 168),
        videoContent: commonVideoStyle(0, 250, VideoTemplateSize.width),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhiteHYCuYuan, ...DEFAULT_CENTER },
          text: C_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: DOT_FILL_BKG.BKG_3,
        tipImage: DOT_TEXT_BKG.BKG_3,
      },
    }),
    mockScript: {
      ...gameLandScapeScript,
      globalField: {
        [GLOBAL_FIELDS.TITLE]: '休闲风格的模板',
        [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
      },
    },
  },
};
