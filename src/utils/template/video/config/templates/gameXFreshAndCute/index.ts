import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '../../index';
import {
  GameNarrationLandScapeConfig,
  GameNarrationLandScapeConfigUsage,
} from '../../GameNarrationLandScapeConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import {
  GameNoNarrationLandScapeConfig,
  GameNoNarrationLandScapeConfigUsage,
} from '../../GameNoNarrationLandScapeConfig';
import {
  GameWithNumberPersonConfig,
  GameWithNumberPersonConfigUsage,
} from '../../GameWithNumberPersonConfig';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../../GameWithNPStandConfig';
import { gameXFreshAndCuteConfig } from './utils';
import { GLOBAL_FIELDS } from '@/utils/template/type';

export const GAME_BASE_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '偏清新的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};
const gameXFreshAndCute_mockScript = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_TITLE,
  },
};
/**
 * 清新&可爱
 * 全部配色模板
 */
// 无数字人&无字幕
const GAME_FRESHANDCUTE_NO_NARRATION: VideoTemplateConfigType = {
  /**
   * 清新斜条纹上下框模板无字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNoNarrationLandScapeConfig, 'TWILL')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新栅格纹模板无字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNoNarrationLandScapeConfig, 'PLAID')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新云朵模板无字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNoNarrationLandScapeConfig, 'CLOUDS')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新花朵模板无字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNoNarrationLandScapeConfig, 'FLOWERS')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
};
// 无数字人&带字幕
const GAME_FRESHANDCUTE_NARRATION: VideoTemplateConfigType = {
  /**
   * 清新斜条纹上下框模板带字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNarrationLandScapeConfig, 'TWILL')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新栅格纹模板带字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNarrationLandScapeConfig, 'PLAID')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新云朵模板带字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNarrationLandScapeConfig, 'CLOUDS')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新花朵模板带字幕（无数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXFreshAndCuteConfig(GameNarrationLandScapeConfig, 'FLOWERS')
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
};
// 圆圈数字人&带字幕
const GAME_FRESHANDCUTE_WITH_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 清新斜条纹上下框模板带字幕（有数字人聚焦）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXFreshAndCuteConfig(
        GameWithNumberPersonConfig,
        'TWILL',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新栅格纹模板带字幕（有数字人聚焦）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXFreshAndCuteConfig(
        GameWithNumberPersonConfig,
        'PLAID',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新云朵模板带字幕（有数字人聚焦）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXFreshAndCuteConfig(
        GameWithNumberPersonConfig,
        'CLOUDS',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
  /**
   * 清新花朵模板带字幕（有数字人聚焦）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXFreshAndCuteConfig(
        GameWithNumberPersonConfig,
        'FLOWERS',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gameXFreshAndCute_mockScript,
  },
};
// 站姿数字人
const GAME_FRESHANDCUTE_WITH_STAND_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 清新斜条纹上下框模板带字幕（站姿数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_TWILL_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXFreshAndCuteConfig(GameWithNPStandConfig, 'TWILL')
      ),
      mockScript: gameXFreshAndCute_mockScript,
    },
  /**
   * 清新栅格纹模板带字幕（站姿数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_PLAID_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXFreshAndCuteConfig(GameWithNPStandConfig, 'PLAID')
      ),
      mockScript: gameXFreshAndCute_mockScript,
    },
  /**
   * 清新云朵模板带字幕（站姿数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_CLOUDS_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXFreshAndCuteConfig(GameWithNPStandConfig, 'CLOUDS')
      ),
      mockScript: gameXFreshAndCute_mockScript,
    },
  /**
   * 清新花朵模板带字幕（站姿数字人）
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_FLOWERS_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXFreshAndCuteConfig(GameWithNPStandConfig, 'FLOWERS')
      ),
      mockScript: gameXFreshAndCute_mockScript,
    },
};

export const GAME_X_FRESHANDCUTE = {
  ...GAME_FRESHANDCUTE_NO_NARRATION,
  ...GAME_FRESHANDCUTE_NARRATION,
  ...GAME_FRESHANDCUTE_WITH_NUMBER_PERSON,
  ...GAME_FRESHANDCUTE_WITH_STAND_NUMBER_PERSON,
};
