import { GLOBAL_FIELDS } from '@/utils/template/type';
import { GAME_BASE_TITLE } from '.';
import { IConfig } from '../..';
import { commonVideoStyle } from '../../../utils';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../../base/showyTextConfig';
import { DEFAULT_ANIMATION, DEFAULT_CENTER } from '../../baseConfig';

export type GAMEXFRESHANDCUTE_TYPE = 'TWILL' | 'PLAID' | 'CLOUDS' | 'FLOWERS';

export const GAMEXFRESHANDCUTE_BKG: {
  [key in GAMEXFRESHANDCUTE_TYPE]: {
    whole: string;
    hole: string;
  };
} = {
  TWILL: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/2338b649512b245acb839c89ef877a87.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/4a499b44956130914b1dde1cbe26301f.png',
  },
  PLAID: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/0ac40084d3d735adab92c2db9a3a571a.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/7793c2d16c7ae50da78da3f2baba49ba.png',
  },
  CLOUDS: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/797461ff53df781fdb025501b6902dd4.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/25b2ebeb549f323293a3dac21c3ab7bd.png',
  },
  FLOWERS: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/bf56e36b75f91c6b8c04e4af6129726b.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/43f371e634ef9edf3842bf740b7ce4bd.png',
  },
};

export const gameXFreshAndCuteConfig = (
  rest: IConfig,
  type: GAMEXFRESHANDCUTE_TYPE,
  wholeBkg?: string // 圆圈数字人时候需要传纯色背景图
): IConfig => {
  const defaultConfig = { ...(rest || {}) };
  const { style, components, materialMap } = defaultConfig;
  const newMaterialMap = {
    ...materialMap,
    bkg: wholeBkg || GAMEXFRESHANDCUTE_BKG[type].whole,
    humanBg: GAMEXFRESHANDCUTE_BKG[type].hole,
  };
  switch (type) {
    case 'TWILL':
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: { ...newMaterialMap },
      };
    case 'PLAID':
      return {
        ...defaultConfig,
        style: {
          ...style,
          templateName: commonVideoStyle(0, 90, 261), // 模版名称
          tipText: commonVideoStyle(0, 134, 84), // 辅助文字
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#FF881A',
                realColor: '#FF881A',
                show: true,
              },
              fontSize: 36,
            },
            animation: DEFAULT_ANIMATION,
          },
          tipText: {
            ...(components.tipText || {}),
            text: GAME_BASE_TITLE[GLOBAL_FIELDS.SUBTITLE],
            style: {
              ...(components.tipText?.style || {}),
              color: {
                color: '#FF881A',
                realColor: '#FF881A',
                show: true,
              },
              text: '',
              fontSize: 14,
              fontFamily: [],
            },
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'CLOUDS':
      return {
        ...defaultConfig,
        style: { ...style },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...WhiteYellow,
              ...DEFAULT_CENTER,
              fontSize: 36,
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n   span {\n        \n    position: relative;\n    display: inline-block;\n\n        -webkit-text-stroke: 0.08rem transparent;\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n\n          background-image: linear-gradient(0deg, #FF00B8, #FFC700);\n          background-clip: text;\n       font-weight: inherit;\n        }\n      }',
            },
            animation: DEFAULT_ANIMATION,
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'FLOWERS':
      return {
        ...defaultConfig,
        style: { ...style },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#5399F3',
                realColor: '#5399F3',
                show: true,
              },
              fontSize: 36,
            },
            animation: DEFAULT_ANIMATION,
          },
          tipText: {
            ...(components.tipText || {}),
            text: GAME_BASE_TITLE[GLOBAL_FIELDS.SUBTITLE],
            style: {
              ...(components.tipText?.style || {}),
              color: {
                color: '#5399F3',
                realColor: '#5399F3',
                show: true,
              },
              text: '',
              fontSize: 14,
              fontFamily: [],
            },
          },
        },
        materialMap: { ...newMaterialMap },
      };
    default:
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: { ...newMaterialMap },
      };
  }
};
