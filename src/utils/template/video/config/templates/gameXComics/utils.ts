import { GLOBAL_FIELDS } from '@/utils/template/type';
import { GAME_BASE_COMICS_TITLE, GAME_BASE_DIMENSIONS_TITLE } from './index';
import { IConfig } from '../..';
import { commonVideoStyle } from '../../../utils';
import { White } from '../../base/showyTextConfig';
import { DEFAULT_ANIMATION, DEFAULT_CENTER } from '../../baseConfig';

export type GAMEXCOMICS_TYPE = 'COMICS1' | 'COMICS2' | 'COMICS3' | 'COMICS4';

export const GAMEXCOMICS_BKG: {
  [key in GAMEXCOMICS_TYPE]: {
    whole: string;
    hole: string;
    tipImage?: string;
  };
} = {
  COMICS1: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/778a9fb764edf1f0634d5afea0416de0.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/da7549f4eb944b53b11063d2272c9be0.png',
    tipImage:
      'https://pagedoo.pay.qq.com/material/@platform/32e7c82b420c3e09f43530b15dcc16af.png',
  },
  COMICS2: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/c8c884d1327fdcf2859de64b9f75382a.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/5bbc018b9ce2cc35336e4d59ee20e5a9.png',
    tipImage:
      'https://pagedoo.pay.qq.com/material/@platform/32e7c82b420c3e09f43530b15dcc16af.png',
  },
  COMICS3: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/884af633083db4d9b0541dffb6baf522.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/b5bc2d6cda32ea351632d74ffd512d37.png',
  },
  COMICS4: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/c84362b939eea8c8c12570e94db6c8bf.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/70f40a2752f777a194269b8d70d5329b.png',
  },
};

export const gameXComicsConfig = (
  rest: IConfig,
  type: GAMEXCOMICS_TYPE,
  wholeBkg?: string // 圆圈数字人时候需要传纯色背景图
): IConfig => {
  const defaultConfig = { ...(rest || {}) };
  const { style, components, materialMap } = defaultConfig;
  const { whole, hole, tipImage } = GAMEXCOMICS_BKG[type];
  const newMaterialMap = {
    ...materialMap,
    bkg: wholeBkg || whole,
    humanBg: hole,
    tipImage,
  };
  switch (type) {
    case 'COMICS1':
    case 'COMICS2':
      return {
        ...defaultConfig,
        style: {
          ...style,
          tipImage: commonVideoStyle(20, -18, 335),
          templateName: commonVideoStyle(0, 82, 261),
          tipText: commonVideoStyle(-20, 140, 84), // 辅助文字
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_COMICS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#ffffff',
                realColor: '#ffffff',
                show: true,
              },
              fontFamily: [
                'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font7.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font7.woff',
              ],
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.1rem #000;\n          color: #000;\n          font-weight: 400;\n        }\n    }',
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
          tipText: {
            text: GAME_BASE_COMICS_TITLE[GLOBAL_FIELDS.SUBTITLE],
            style: {
              ...(components.tipText?.style || {}),
              color: {
                color: '#000000',
                realColor: '#000000',
                show: true,
              },
              text: '',
              style: 'transform: rotate(-3deg);',
              fontSize: 14,
              fontFamily: [],
            },
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'COMICS3':
    case 'COMICS4':
      return {
        ...defaultConfig,
        style: { ...style },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_DIMENSIONS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              fontSize: 36,
              style:
                '\n      \n    padding: 0.2rem;\n    margin: -0.2rem;\n      span {\n        \n    position: relative;\n    display: inline-block;\n\n        &::before {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.1rem #000;\n          color: #000;\n          font-weight: 400;\n        }\n        &::after {\n          \n    position: absolute;\n    content: attr(data-text);\n    left: 0;\n    top: 0;\n    font-weight: 900;\n    z-index: -1;\n    pointer-events: none;\n\n          -webkit-text-stroke: 0.16rem #A19ED2;\n          color: #A19ED2;\n          font-weight: 400;\n          z-index: -2;\n        }}',
            },
            animation: DEFAULT_ANIMATION,
          },
        },
        materialMap: { ...newMaterialMap },
      };
    default:
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: { ...newMaterialMap },
      };
  }
};
