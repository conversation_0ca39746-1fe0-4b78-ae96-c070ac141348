import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '../../index';
import {
  GameNarrationLandScapeConfig,
  GameNarrationLandScapeConfigUsage,
} from '../../GameNarrationLandScapeConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import {
  GameNoNarrationLandScapeConfig,
  GameNoNarrationLandScapeConfigUsage,
} from '../../GameNoNarrationLandScapeConfig';
import {
  GameWithNumberPersonConfig,
  GameWithNumberPersonConfigUsage,
} from '../../GameWithNumberPersonConfig';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../../GameWithNPStandConfig';
import { gameXComicsConfig } from './utils';
import { GLOBAL_FIELDS } from '@/utils/template/type';

export const GAME_BASE_COMICS_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '漫画风格的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};
export const GAME_BASE_DIMENSIONS_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '二次元风格的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};
const gameXComics_mockScript = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_COMICS_TITLE,
  },
};
const gamexdimensions_mockScript = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_DIMENSIONS_TITLE,
  },
};

/**
 * 漫画&二次元
 * 全部配色模板
 */
// 无数字人&无字幕
const GAME_COMICS_NO_NARRATION: VideoTemplateConfigType = {
  /**
   * 漫画1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNoNarrationLandScapeConfig, 'COMICS1')
    ),
    mockScript: gameXComics_mockScript,
  },
  /**
   * 漫画2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNoNarrationLandScapeConfig, 'COMICS2')
    ),
    mockScript: gameXComics_mockScript,
  },
  /**
   * 二次元1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNoNarrationLandScapeConfig, 'COMICS3')
    ),
    mockScript: gamexdimensions_mockScript,
  },
  /**
   * 二次元2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNoNarrationLandScapeConfig, 'COMICS4')
    ),
    mockScript: gamexdimensions_mockScript,
  },
};
// 无数字人&带字幕
const GAME_COMICS_NARRATION: VideoTemplateConfigType = {
  /**
   * 漫画1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNarrationLandScapeConfig, 'COMICS1')
    ),
    mockScript: gameXComics_mockScript,
  },
  /**
   * 漫画2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNarrationLandScapeConfig, 'COMICS2')
    ),
    mockScript: gameXComics_mockScript,
  },
  /**
   * 二次元1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNarrationLandScapeConfig, 'COMICS3')
    ),
    mockScript: gamexdimensions_mockScript,
  },
  /**
   * 二次元2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXComicsConfig(GameNarrationLandScapeConfig, 'COMICS4')
    ),
    mockScript: gamexdimensions_mockScript,
  },
};
// 圆圈数字人&带字幕
const GAME_COMICS_WITH_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 漫画1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXComicsConfig(
        GameWithNumberPersonConfig,
        'COMICS1',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gameXComics_mockScript,
  },
  /**
   * 漫画2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXComicsConfig(
        GameWithNumberPersonConfig,
        'COMICS2',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gameXComics_mockScript,
  },
  /**
   * 二次元1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXComicsConfig(
        GameWithNumberPersonConfig,
        'COMICS3',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gamexdimensions_mockScript,
  },
  /**
   * 二次元2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_WITH_NUMBER_PERSON]: {
    usage: GameWithNumberPersonConfigUsage(
      gameXComicsConfig(
        GameWithNumberPersonConfig,
        'COMICS4',
        'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
      )
    ),
    mockScript: gamexdimensions_mockScript,
  },
};
// 站姿数字人
const GAME_COMICS_WITH_STAND_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 漫画1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS1_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXComicsConfig(GameWithNPStandConfig, 'COMICS1')
      ),
      mockScript: gameXComics_mockScript,
    },
  /**
   * 漫画2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS2_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXComicsConfig(GameWithNPStandConfig, 'COMICS2')
      ),
      mockScript: gameXComics_mockScript,
    },
  /**
   * 二次元1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS3_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXComicsConfig(GameWithNPStandConfig, 'COMICS3')
      ),
      mockScript: gamexdimensions_mockScript,
    },
  /**
   * 二次元2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_COMICS4_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXComicsConfig(GameWithNPStandConfig, 'COMICS4')
      ),
      mockScript: gamexdimensions_mockScript,
    },
};

export const GAME_X_COMICS = {
  ...GAME_COMICS_NO_NARRATION,
  ...GAME_COMICS_NARRATION,
  ...GAME_COMICS_WITH_NUMBER_PERSON,
  ...GAME_COMICS_WITH_STAND_NUMBER_PERSON,
};
