import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../GameWithNPStandConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import { GLOBAL_FIELDS } from '@/utils/template/type';
import { commonVideoStyle } from '../../utils';
import { DEFAULT_CENTER } from '../baseConfig';
import { LinearA, LinearB, WhiteHYZhuZiZhiYuan } from '../base/showyTextConfig';

const LEISURE_FILL_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/ffee8266b9a3c1f1f2dde47765be5572.png',
  BKG_3:
    'https://pagedoo.pay.qq.com/material/@platform/c3d1ab04ddab0ef6120175d9d0f9b48b.png',
  BKG_2:
    'https://pagedoo.pay.qq.com/material/@platform/05ae5d64b289dadc7fb99c5f1875b5a2.png',
  BKG_1:
    'https://pagedoo.pay.qq.com/material/@platform/e52524f1152d2ffaa58f13d1271daa71.png',
};

const LEISURE_TEXT_BKG = {
  BKG: 'https://pagedoo.pay.qq.com/material/@platform/d8c33057b275d1c890865d0bc0a83171.png',
};

const L_BASE_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '休闲风格的模板',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};

const MockScript_Leisure = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...L_BASE_TITLE,
  },
};

export const GAME_X_NPS_Leisure = {
  // 休闲模板 1
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 83, 192),
        tipText: commonVideoStyle(0, 128, 168),
        tipImage: commonVideoStyle(58, 66, 258),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...WhiteHYZhuZiZhiYuan, ...DEFAULT_CENTER },
          text: L_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: LEISURE_FILL_BKG.BKG,
        tipImage: LEISURE_TEXT_BKG.BKG,
      },
    }),
    mockScript: MockScript_Leisure,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE_1]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 83, 192),
        tipText: commonVideoStyle(0, 128, 168),
        tipImage: commonVideoStyle(58, 66, 258),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...LinearA, ...DEFAULT_CENTER },
          text: L_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: LEISURE_FILL_BKG.BKG_1,
      },
    }),
    mockScript: MockScript_Leisure,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE_2]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 83, 192),
        tipText: commonVideoStyle(0, 128, 168),
        tipImage: commonVideoStyle(58, 66, 258),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...LinearB, ...DEFAULT_CENTER },
          text: L_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: LEISURE_FILL_BKG.BKG_2,
      },
    }),
    mockScript: MockScript_Leisure,
  },
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_NPS_LEISURE_3]: {
    usage: GameWithNPSConfigUsage({
      style: {
        ...GameWithNPStandConfig.style,
        templateName: commonVideoStyle(0, 83, 192),
        tipText: commonVideoStyle(0, 128, 168),
        tipImage: commonVideoStyle(58, 66, 258),
      },
      components: {
        ...GameWithNPStandConfig.components,
        templateName: {
          ...GameWithNPStandConfig.components.templateName,
          style: { ...LinearB, ...DEFAULT_CENTER },
          text: L_BASE_TITLE[GLOBAL_FIELDS.TITLE],
        },
      },
      materialMap: {
        ...GameWithNPStandConfig.materialMap,
        bkg: LEISURE_FILL_BKG.BKG_3,
      },
    }),
    mockScript: MockScript_Leisure,
  },
};
