import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '../../index';
import {
  GameNarrationLandScapeConfig,
  GameNarrationLandScapeConfigUsage,
} from '../../GameNarrationLandScapeConfig';
import { gameLandScapeScript } from '@/utils/template/mock/game-landscape';
import {
  GameNoNarrationLandScapeConfig,
  GameNoNarrationLandScapeConfigUsage,
} from '../../GameNoNarrationLandScapeConfig';
import {
  GameWithNumberPersonConfig,
  GameWithNumberPersonConfigUsage,
} from '../../GameWithNumberPersonConfig';
import {
  GameWithNPSConfigUsage,
  GameWithNPStandConfig,
} from '../../GameWithNPStandConfig';
import {
  GameWithSitNumberPersonConfigUsage,
  GameWithSitNumberPersonConfig,
} from '../../GameWithSitNumberPersonConfig';
import { gameXBusinessConfig } from './utils';
import { GLOBAL_FIELDS } from '@/utils/template/type';

export const GAME_BASE_BUSINESS_TITLE = {
  [GLOBAL_FIELDS.TITLE]: '偏商务的模版',
  [GLOBAL_FIELDS.SUBTITLE]: '辅助的小文字辅助的小文字',
};
export const gameXBusiness_mockScript = {
  ...gameLandScapeScript,
  globalField: {
    ...gameLandScapeScript.globalField,
    ...GAME_BASE_BUSINESS_TITLE,
  },
};

/**
 * 商务
 * 全部配色模板
 */
// 无数字人&无字幕
const GAME_BUSINESS_NO_NARRATION: VideoTemplateConfigType = {
  /**
   * 商务1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXBusinessConfig(GameNoNarrationLandScapeConfig, 'BUSINESS1')
    ),
    mockScript: gameXBusiness_mockScript,
  },
  /**
   * 商务2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXBusinessConfig(GameNoNarrationLandScapeConfig, 'BUSINESS2')
    ),
    mockScript: gameXBusiness_mockScript,
  },
  /**
   * 商务3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_NO_NARRATION]: {
    usage: GameNoNarrationLandScapeConfigUsage(
      gameXBusinessConfig(GameNoNarrationLandScapeConfig, 'BUSINESS3')
    ),
    mockScript: gameXBusiness_mockScript,
  },
};
// 无数字人&带字幕
const GAME_BUSINESS_NARRATION: VideoTemplateConfigType = {
  /**
   * 商务1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXBusinessConfig(GameNarrationLandScapeConfig, 'BUSINESS1')
    ),
    mockScript: gameXBusiness_mockScript,
  },
  /**
   * 商务2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXBusinessConfig(GameNarrationLandScapeConfig, 'BUSINESS2')
    ),
    mockScript: gameXBusiness_mockScript,
  },
  /**
   * 商务3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_NARRATION]: {
    usage: GameNarrationLandScapeConfigUsage(
      gameXBusinessConfig(GameNarrationLandScapeConfig, 'BUSINESS3')
    ),
    mockScript: gameXBusiness_mockScript,
  },
};
// 圆圈数字人&带字幕
const GAME_BUSINESS_WITH_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 商务1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXBusinessConfig(
          GameWithNumberPersonConfig,
          'BUSINESS1',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXBusiness_mockScript,
    },
  /**
   * 商务2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXBusinessConfig(
          GameWithNumberPersonConfig,
          'BUSINESS2',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXBusiness_mockScript,
    },
  /**
   * 商务3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_WITH_NUMBER_PERSON]:
    {
      usage: GameWithNumberPersonConfigUsage(
        gameXBusinessConfig(
          GameWithNumberPersonConfig,
          'BUSINESS3',
          'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png'
        )
      ),
      mockScript: gameXBusiness_mockScript,
    },
};
// 站姿数字人
const GAME_BUSINESS_WITH_STAND_NUMBER_PERSON: VideoTemplateConfigType = {
  /**
   * 商务1
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXBusinessConfig(GameWithNPStandConfig, 'BUSINESS1')
      ),
      mockScript: gameXBusiness_mockScript,
    },
  /**
   * 商务2
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXBusinessConfig(GameWithNPStandConfig, 'BUSINESS2')
      ),
      mockScript: gameXBusiness_mockScript,
    },
  /**
   * 商务3
   */
  [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_WITH_STAND_NUMBER_PERSON]:
    {
      usage: GameWithNPSConfigUsage(
        gameXBusinessConfig(GameWithNPStandConfig, 'BUSINESS3')
      ),
      mockScript: gameXBusiness_mockScript,
    },
};

// 坐姿数字人
const GAME_BUSINESS_WITH_SIT_NUMBER_PERSON: VideoTemplateConfigType = {
  // /**
  //  * 商务1
  //  */
  // [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS1_WITH_SIT_NUMBER_PERSON]:
  //   {
  //     usage: GameWithSitNumberPersonConfigUsage(
  //       gameXBusinessConfig(GameWithSitNumberPersonConfig, 'BUSINESS1')
  //     ),
  //     mockScript: gameXBusiness_mockScript,
  //   },
  // /**
  //  * 商务2
  //  */
  // [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS2_WITH_SIT_NUMBER_PERSON]:
  //   {
  //     usage: GameWithSitNumberPersonConfigUsage(
  //       gameXBusinessConfig(GameWithSitNumberPersonConfig, 'BUSINESS2')
  //     ),
  //     mockScript: gameXBusiness_mockScript,
  //   },
  // /**
  //  * 商务3
  //  */
  // [VIDEO_UNDERSTANDING_ID_ENUM.GAME_FRESHANDCUTE_BUSINESS3_WITH_SIT_NUMBER_PERSON]:
  //   {
  //     usage: GameWithSitNumberPersonConfigUsage(
  //       gameXBusinessConfig(GameWithSitNumberPersonConfig, 'BUSINESS3')
  //     ),
  //     mockScript: gameXBusiness_mockScript,
  //   },
};
export const GAME_X_BUSINESS = {
  ...GAME_BUSINESS_NO_NARRATION,
  ...GAME_BUSINESS_NARRATION,
  ...GAME_BUSINESS_WITH_NUMBER_PERSON,
  ...GAME_BUSINESS_WITH_STAND_NUMBER_PERSON,
  ...GAME_BUSINESS_WITH_SIT_NUMBER_PERSON,
};
