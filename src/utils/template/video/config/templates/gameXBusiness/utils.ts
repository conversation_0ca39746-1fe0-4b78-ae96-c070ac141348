import { GAME_BASE_BUSINESS_TITLE } from './index';
import { IConfig } from '../..';
import { White } from '../../base/showyTextConfig';
import { DEFAULT_ANIMATION, DEFAULT_CENTER } from '../../baseConfig';
import { GLOBAL_FIELDS } from '@/utils/template/type';

export type GAMEXBUSINESS_TYPE = 'BUSINESS1' | 'BUSINESS2' | 'BUSINESS3';

export const GAMEXCHESSCARDS_BKG: {
  [key in GAMEXBUSINESS_TYPE]: {
    whole: string;
    hole: string;
    tipImage?: string;
  };
} = {
  BUSINESS1: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/0d7b35fe1205d3b986d95ddb13f578de.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/8df9518fbfd5454a247fa0e42a1c10bf.png',
  },
  BUSINESS2: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/23fc83ec828cb3046f1d76301e0c4812.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/b82e03a2698d17118d37a8f2af079fb4.png',
  },
  BUSINESS3: {
    whole:
      'https://pagedoo.pay.qq.com/material/@platform/a59a3da34ecd5ee8d5355681bdd1c75e.png',
    hole: 'https://pagedoo.pay.qq.com/material/@platform/507be69937794d43b3c7f08f787b4098.png',
  },
};

export const gameXBusinessConfig = (
  rest: IConfig,
  type: GAMEXBUSINESS_TYPE,
  wholeBkg?: string // 圆圈数字人时候需要传纯色背景图
): IConfig => {
  const defaultConfig = { ...(rest || {}) };
  const { style, components, materialMap } = defaultConfig;
  const { whole, hole, tipImage } = GAMEXCHESSCARDS_BKG[type];
  const newMaterialMap = {
    ...materialMap,
    bkg: wholeBkg || whole,
    humanBg: hole,
    tipImage,
  };
  switch (type) {
    case 'BUSINESS1':
    case 'BUSINESS3':
      return {
        ...defaultConfig,
        style: {
          ...style,
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_BUSINESS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#ffffff',
                realColor: '#ffffff',
                show: true,
              },
              fontFamily: ['SimSun'],
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
        },
        materialMap: { ...newMaterialMap },
      };
    case 'BUSINESS2':
      return {
        ...defaultConfig,
        style: {
          ...style,
        },
        components: {
          ...components,
          templateName: {
            text: GAME_BASE_BUSINESS_TITLE[GLOBAL_FIELDS.TITLE],
            style: {
              ...White,
              ...DEFAULT_CENTER,
              color: {
                color: '#253090',
                realColor: '#253090',
                show: true,
              },
              fontFamily: ['SimSun'],
              fontSize: 32,
              shadow: false,
            },
            animation: DEFAULT_ANIMATION,
          },
          tipText: {
            ...(components.tipText || {}),
            text: GAME_BASE_BUSINESS_TITLE[GLOBAL_FIELDS.SUBTITLE],
            style: {
              ...(components.tipText?.style || {}),
              color: {
                color: '#253090',
                realColor: '#253090',
                show: true,
              },
              text: '',
              fontSize: 14,
              fontFamily: [],
            },
          },
        },
        materialMap: { ...newMaterialMap },
      };
    default:
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: { ...newMaterialMap },
      };
  }
};
