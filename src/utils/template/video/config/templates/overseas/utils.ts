import { IConfig } from '../..';
export type GAMEOVERSEAS_TYPE = 'GAME1';

export const gameOverseasConfig = (
  rest: IConfig,
  type: GAMEOVERSEAS_TYPE
): IConfig => {
  const defaultConfig = { ...(rest || {}) };
  const { style, components } = defaultConfig;
  switch (type) {
    case 'GAME1':
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: {},
      };
    default:
      return {
        ...defaultConfig,
        style: { ...style },
        components: { ...components },
        materialMap: {},
      };
  }
};
