import { VIDEO_UNDERSTANDING_ID_ENUM } from '@/utils/template/templates';
import { VideoTemplateConfigType } from '../../index';
import { gameOverseasScript } from '@/utils/template/mock/game-overseas';
import {
  GameHorizontalScreenWithStandNumberPersonConfigUsage,
  GameHorizontalScreenWithStandNumberPersonConfig,
} from '../../GameHorizontalScreenWithStandNumberPersonConfig';
import { gameOverseasConfig } from './utils';

/**
 * 海外-模版
 */
// 横屏全屏&站姿数字人
const GAME_HORIZONTAL_SCREEN_WITH_STAND_NUMBER_PERSON: VideoTemplateConfigType =
  {
    [VIDEO_UNDERSTANDING_ID_ENUM.GAME_HORIZONTAL_SCREEN_WITH_STAND_NUMBER_PERSON]:
      {
        usage: GameHorizontalScreenWithStandNumberPersonConfigUsage(
          gameOverseasConfig(
            GameHorizontalScreenWithStandNumberPersonConfig,
            'GAME1'
          )
        ),
        mockScript: gameOverseasScript,
      },
  };

export const GAME_OVERSEAS = {
  ...GAME_HORIZONTAL_SCREEN_WITH_STAND_NUMBER_PERSON,
};
