import { AiXiaoYe } from './base/timbreConfig';
import { JackInTheBox, RubberBand } from '../../config/animationConfig';
import { White, YellowWhite } from './base/showyTextConfig';
import { commonVideoStyle } from '../utils';
import { VideoTemplateSize } from '../constants';
import type { FontSelectorData } from '@tencent/formily-cms/esm/font-selector/type';
import { VIRTUALMAN_OPTIONS } from '../../constants';

export const DEFAULT_CENTER: FontSelectorData = {
  width: VideoTemplateSize.width,
  textAlign: 'center',
};

const DEFAULT_TEXT_STYLE: FontSelectorData = {
  rem: 50,
  useImage: 0,
  lineHeight: 1,
  letterSpacing: 0,
  textAlign: 'left',
  color: {
    color: '#ffffff',
    realColor: '#ffffff',
    show: true,
  },
  fontWeight: 400,
  ellipsis: 0,
  fontFamily: [
    'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff',
  ],
  width: 0,
};

const materialConfig = {
  // TODO:@linjuncheng 全部移除
  bkg: 'https://pagedoo.pay.qq.com/material/@platform/2338b649512b245acb839c89ef877a87.png',
  logo: '',
  tipImage: '',
  subTitleBkg:
    'https://pagedoo.pay.qq.com/material/@platform/a521c8992d8bcc848bab3aa4529d2334.png',
  humanBg:
    'https://pagedoo.pay.qq.com/material/@platform/4a499b44956130914b1dde1cbe26301f.png',
  discountBkg:
    'https://pagedoo.pay.qq.com/material/@platform/e87d3372d0d2585438e3254ce33aae69.png',
};

export const DEFAULT_ANIMATION = {
  entranceAnimation: JackInTheBox,
  exitAnimation: null,
  highlightAnimation: RubberBand,
};

const componentsConfig = {
  // TODO:@linjuncheng
  // 数字人(默认纯声音无形象)
  virtualman: {
    dataConfig: VIRTUALMAN_OPTIONS.none,
    voiceConfig: AiXiaoYe,
    style: {},
  },
  // 音效
  featuredSoundCofig: {
    volume: 0,
    fadeInTime: 0,
    fadeOutTime: 0,
  },
  // 花字
  showyText: {
    style: YellowWhite,
    animation: DEFAULT_ANIMATION,
  },
  // 特效动画
  specialEffectsAnimation: {
    animation: DEFAULT_ANIMATION,
  },
  // 字幕
  subtitle: {
    fontSize: 20,
    useImage: 1,
    color: {
      color: '#fff',
      realColor: '#fff',
      show: true,
    },
    width: 333,
    textAlign: 'center',
    lineHeight: 1.5,
  },
  templateName: {
    text: '偏清新的模版',
    style: { ...White, ...DEFAULT_CENTER },
    animation: DEFAULT_ANIMATION,
  },
  tipText: {
    text: '辅助的小文字辅助的小文字',
    style: {
      text: '',
      ...DEFAULT_TEXT_STYLE,
      fontFamily: [],
      fontSize: 14,
      ...DEFAULT_CENTER,
    },
  },
  discountDesc: {
    text: '劲爆内容速来!',
    style: {
      text: '',
      ...DEFAULT_TEXT_STYLE,
      fontFamily: [],
      fontSize: 12,
    },
  },
  discountText: {
    text: '不容错过',
    style: {
      text: '',
      ...DEFAULT_TEXT_STYLE,
      color: {
        color: '#000000',
        realColor: '#000000',
        show: true,
      },
      fontFamily: [
        'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff2,https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font31.woff',
      ],
      fontSize: 24,
    },
  },
};

export const GameVideoBaseConfig = {
  // 模板样式
  // TODO:@linjuncheng
  style: {
    background: commonVideoStyle(0, 0, VideoTemplateSize.width), // 背景
    logo: commonVideoStyle(16, 32, 80), // logo
    templateName: commonVideoStyle(0, 84, 261), // 模版名称
    tipImage: commonVideoStyle(103, 140, 254), // 辅助文字背景
    tipText: commonVideoStyle(0, 140, 84), // 辅助文字
    specialEffectsAnimation: commonVideoStyle(30, 140, 140), // 特效动画
    showyText: commonVideoStyle(100, 366, 140), // 右侧花字
    liveImage: commonVideoStyle(30, 140, 140), // 贴图
    subtitleBkg: commonVideoStyle(62, 670, 333), // 字幕背景
    subtitle: commonVideoStyle(42, 584, 333), // 字幕
    videoContent: commonVideoStyle(0, 200, VideoTemplateSize.width), // 视频
    human: commonVideoStyle(-50, 370, 400), // 数字人 100 -> 10.98
    humanBg: commonVideoStyle(0, 0, VideoTemplateSize.width), // 数字人 100 -> 10.98
    discountBkg: commonVideoStyle(0, 307, 194), // 折扣背景
    discountText: commonVideoStyle(50, 394, 88), // 折扣文字
    discountDesc: commonVideoStyle(59, 428, 92), // 折扣描述
  },
  // 组件
  components: componentsConfig,
  // 素材
  materialMap: materialConfig,
};
