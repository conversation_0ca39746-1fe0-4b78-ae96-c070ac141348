import { IConfig, IUsage } from './index';
import { COMPONENT_LIST, RESOURCES_TYPE } from '../constants';
import { Taxi2 } from './base/virtualMan';
import { GameVideoBaseConfig } from './baseConfig';
import { GLOBAL_FIELDS } from '@/utils/template/type';
import { commonVideoStyle } from '../utils';
import { AiXiaoYe } from './base/timbreConfig';

/**
 * 清新斜条纹上下框模板带字幕（有数字人）
 */
export const GameWithNumberPersonConfig = {
  style: {
    ...GameVideoBaseConfig.style,
    human: commonVideoStyle(22, 370, 165),
  },
  components: {
    ...GameVideoBaseConfig.components,
    virtuanman: {
      dataConfig: Taxi2,
      voiceConfig: AiXiaoYe,
      style: {},
    },
  },
  materialMap: {
    ...GameVideoBaseConfig.materialMap,
    bkg: 'https://pagedoo.pay.qq.com/material/@platform/092fcf9b6129a06d7b2ecf00d3ca3300.png',
  },
} as const;

/**
 * 清新斜条纹上下框模板带字幕（有数字人）
 */

export const GameWithNumberPersonConfigUsage = (
  gameConfig: IConfig
): IUsage[] => {
  return [
    {
      // 话术
      componentKey: COMPONENT_LIST.LIVE_SPEECH,
    },
    {
      // 背景图
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: gameConfig.style.background,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.bkg || '',
        isSingle: true,
      },
    },
    {
      // 数字人
      componentKey: COMPONENT_LIST.VIRTUALMAN,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.virtuanman,
      },
      componentStyle: gameConfig.style.human,
    },
    // 数字人下背景图
    {
      componentKey: COMPONENT_LIST.LIVE_BKG,
      componentStyle: gameConfig.style.humanBg,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig?.materialMap?.humanBg || '',
        isSingle: true,
      },
    },
    {
      // 视频
      componentKey: COMPONENT_LIST.LIVE_VIDEO,
      componentStyle: gameConfig.style.videoContent,
    },
    {
      // 贴片
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.liveImage,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resources.image_address`,
      },
    },
    {
      // 花字
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.flowerText`,
        fontStyle: gameConfig.components.showyText?.style,
        animation: gameConfig.components.showyText?.animation,
      },
      componentStyle: gameConfig.style.showyText,
    },
    {
      // 模板文字背景
      componentKey: COMPONENT_LIST.LIVE_IMAGE,
      componentStyle: gameConfig.style.tipImage,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.materialMap?.tipImage,
      },
    },
    {
      // 模板名
      componentKey: COMPONENT_LIST.LIVE_SHOWY_TEXT,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.TITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.templateName?.style,
        // animation: gameConfig.components.templateName?.animation,
        value: gameConfig.components.templateName?.text,
        isSingle: true,
      },
      componentStyle: gameConfig.style.templateName,
    },
    {
      componentKey: COMPONENT_LIST.LIVE_TEXT,
      componentStyle: gameConfig.style.tipText,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptKey: `globalField.${GLOBAL_FIELDS.SUBTITLE}`,
        scriptDataType: 'global',
        fontStyle: gameConfig.components.tipText?.style,
        value: gameConfig.components.tipText?.text,
        isSingle: true,
      },
    },
    {
      // 特效动画
      componentKey: COMPONENT_LIST.SPECIAL_EFFECTS_ANIMATION,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource.image_address`,
        animation: gameConfig.components.specialEffectsAnimation?.animation,
      },
      componentStyle: gameConfig.style.specialEffectsAnimation,
    },
    {
      // 视频字幕
      componentKey: COMPONENT_LIST.VIDEO_SUBTITLE,
      componentConfig: {
        componentConfigCommonType: 'template',
        value: gameConfig.components.subtitle,
      },
      componentStyle: gameConfig.style.subtitle,
    },
    {
      // 特色音效
      componentKey: COMPONENT_LIST.FEATURED_SOUND,
      componentConfig: {
        componentConfigCommonType: 'script',
        scriptDataType: 'view',
        scriptKey: `resource`,
        soundConfig: gameConfig.components.featuredSoundCofig,
      },
    },
  ];
};
