import {
  COMPONENT_LIST,
  DEFAULT_DURATION,
  Direction,
  handleSpeechDuration,
  RESOURCES_TYPE,
  speechDuration_templateId,
  speechDuration_templateId_config,
  VideoTemplateSize,
} from './constants';
import { commonVideoStyle, genBaseSound, hiddenStyle } from './utils';
import {
  scriptFeaturedSoundPlugin,
  scriptLiveShowyTextPlugin,
  scriptLiveSpeechPlugin,
  scriptSpecialEffectsAnimationPlugin,
  scriptVideoPlugin,
  scriptVirtualManPlugin,
  scriptLiveBkgPlugin,
  scriptLiveImagePlugin,
  scriptLiveTextPlugin,
  scriptSubtitlePlugin,
} from '../plugins';
import { IUsage, VideoTemplateConfig } from './config';
import { VirtualManConfV1 } from '../plugins/scriptVirtualManPlugin';
import { ComponentList } from './type';
import { SinglePlugin } from '..';
import { ILiveFeaturedSoundConfig } from '@/utils/play-component';
import { IAnimationConfig } from '../plugins/scriptLiveShowyTextPlugin';
import { NO_VIRTUALMAN_TYPE } from '../constants';
import { BaseScript, IGlobalResourceList } from '@/components/ScriptForm/type';
import { groupBy } from 'lodash-es';
import { MessagePlugin } from 'tdesign-react';
import { genUniqueKey, handleComponentConfig } from '../utils';

export type BackFn = (arg: IUsage & { timelineIndex: number }) => SinglePlugin;

export function pluginInit({
  pagedooScript,
  components,
  templateId,
}: {
  pagedooScript: BaseScript;
  components: (typeof VideoTemplateConfig)[number]['usage'];
  templateId: string;
}) {
  // 模版配置
  // 判断模版是否使用数字人形象
  const virtualManTimelineIndex = components.findIndex(
    (i) => i.componentKey === COMPONENT_LIST.VIRTUALMAN
  );

  const hasVirtualManComponent = virtualManTimelineIndex > -1;
  const { dataConfig } =
    handleComponentConfig({
      componentConfig: components[virtualManTimelineIndex]?.componentConfig,
    }) || {};
  const showVirtualman =
    hasVirtualManComponent && dataConfig?.type !== NO_VIRTUALMAN_TYPE;
  /**
   * 脚本时长
   */
  // const scriptDuration = endTime ? twoTimeToSeconds(endTime) : 0;
  let videoDuration = Number(pagedooScript.globalField?.info?.video_duration);
  if (
    speechDuration_templateId.includes(templateId) &&
    pagedooScript.globalSpeech
  ) {
    videoDuration =
      handleSpeechDuration(pagedooScript.globalSpeech) +
      (speechDuration_templateId_config[templateId]?.extendDuration || 0);
  }
  if (!videoDuration) {
    console.error('videoDuration is undefined');
    MessagePlugin.error('脚本协议出错');
  }

  // 每个模板用到的组件可能不一样

  // components key ---> plugins
  const safeKey = genUniqueKey();

  /**
   * 0829 版本变更
   * 贴片、音效、动效贴片 不再通过 views 来返回
   * 这里通过对 globalResourceList 进行分类
   * 将每一类资源渲染在一条轨道上
   * badcase: 暂时忽略时间重叠的情况
   */
  const dataSourceSort = groupBy(
    pagedooScript.globalField?.globalResourceList,
    'resourceKey'
  );

  const pluginKeyMap: Partial<Record<ComponentList, BackFn>> = {
    // 话术
    [COMPONENT_LIST.LIVE_SPEECH]: ({ componentStyle, timelineIndex }) =>
      scriptLiveSpeechPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle ?? hiddenStyle,
        isSkip: (_, index) => index !== 0,
        getSpeechParams: () => ({
          speechData: {
            type: 'text',
            text: pagedooScript.globalSpeech || '',
          } as const,
          speechDrive: { type: 'virtualman' },
          remixAnswer: {
            enabled: true,
            block: false,
            separator: ['。', '，', '！', '？'],
          },
        }),
        duration: () => {
          return videoDuration;
        },
      }),

    // 数字人
    [COMPONENT_LIST.VIRTUALMAN]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptVirtualManPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (_, index) => {
          return index !== 0;
        },
        baseFn: () => {
          const { dataConfig, voiceConfig } =
            handleComponentConfig({ componentConfig }) || {};
          return {
            keyLight: {
              enabled: false,
              tolerance: 0.2433,
            },
            virtualMan: dataConfig as VirtualManConfV1,
            voiceConfig: {
              currentVoiceItem: voiceConfig,
            },
            liveID: '',
            isGlobalVirtualman: false,
            isWave: true,
            isSegmentTexts: false,
            customScript: false,
          };
        },
        duration: () => videoDuration,
      }),
    // 音效
    [COMPONENT_LIST.FEATURED_SOUND]: ({ componentConfig, timelineIndex }) =>
      scriptFeaturedSoundPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: hiddenStyle,
        customDataSource: dataSourceSort[RESOURCES_TYPE.FEATURED_SOUND_EFFECTS],
        isSkip: (view) => {
          return !handleComponentConfig({
            view,
            componentConfig,
            pagedooScript,
          });
        },
        soundCreator: (view) => {
          return {
            sound: genBaseSound(
              handleComponentConfig({ view, componentConfig, pagedooScript })
            ),
            soundConfig:
              componentConfig?.soundConfig as ILiveFeaturedSoundConfig['soundConfig'],
          };
        },
        getOffset: (view) => {
          if (
            componentConfig?.componentConfigCommonType === 'script' &&
            componentConfig.scriptDataType === 'view'
          ) {
            return Number((view as IGlobalResourceList)?.showTime) * 1000 || 0;
          }
          return 0;
        },
      }),
    // 背景图
    [COMPONENT_LIST.LIVE_BKG]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveBkgPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          return (
            !handleComponentConfig({ componentConfig, view, pagedooScript }) ||
            index !== 0
          );
        },
        baseFn: (view) =>
          handleComponentConfig({ componentConfig, view, pagedooScript }) || '',
        duration: () => videoDuration,
      }),
    // 通用贴片
    [COMPONENT_LIST.LIVE_IMAGE]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveImagePlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        customDataSource: dataSourceSort[RESOURCES_TYPE.PATCH],
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({ componentConfig, view, pagedooScript })
          ) {
            // 没有资源的时候
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            // 如果有配置 单条轨道，说明是只渲染一个
            return true;
          }
          return false;
        },
        baseFn: (view) =>
          handleComponentConfig({ componentConfig, view, pagedooScript }),
        duration: () => {
          if (componentConfig?.isSingle) {
            return videoDuration;
          }
          if (componentConfig?.componentConfigCommonType === 'script') {
            return DEFAULT_DURATION;
          }
          return 0;
        },
        getOffset: (view) => {
          if (
            componentConfig?.componentConfigCommonType === 'script' &&
            componentConfig.scriptDataType === 'view'
          ) {
            return Number((view as IGlobalResourceList)?.showTime) * 1000 || 0;
          }
          return 0;
        },
      }),
    // 花字
    [COMPONENT_LIST.LIVE_SHOWY_TEXT]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveShowyTextPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        customDataSource: dataSourceSort[RESOURCES_TYPE.FLOWER_CHARACTER],
        // 有配置，读配置
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({ componentConfig, view, pagedooScript })
          ) {
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            return true;
          }
          return false;
        },
        baseFn: (view) => {
          return handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          });
        },
        getAnimationConfig: () => {
          return componentConfig?.animation as IAnimationConfig;
        },
        duration: () => {
          if (componentConfig?.isSingle) {
            return videoDuration;
          }
          // 脚本类型的贴片统一仅展示 DEFAULT_DURATION
          if (componentConfig?.componentConfigCommonType === 'script') {
            return DEFAULT_DURATION;
          }
          return 0;
        },
        fontStyle: componentConfig?.fontStyle,
        getOffset: (view) => {
          if (
            componentConfig?.componentConfigCommonType === 'script' &&
            componentConfig.scriptDataType === 'view'
          ) {
            return Number((view as IGlobalResourceList)?.showTime) * 1000 || 0;
          }
          return 0;
        },
      }),
    // 特效动画
    [COMPONENT_LIST.SPECIAL_EFFECTS_ANIMATION]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptSpecialEffectsAnimationPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        customDataSource: dataSourceSort[RESOURCES_TYPE.SPECIAL_EFFECTS_GIF],
        isSkip: (view) => {
          return !handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          });
        },
        baseFn: (view) =>
          handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }),
        getAnimationConfig: () => {
          return componentConfig?.animation as IAnimationConfig;
        },
        duration: () => {
          if (componentConfig?.isSingle) {
            return videoDuration;
          }
          if (componentConfig?.componentConfigCommonType === 'script') {
            return DEFAULT_DURATION;
          }
          // 默认 3500 秒
          return 0;
        },
        getOffset: (view) => {
          if (
            componentConfig?.componentConfigCommonType === 'script' &&
            componentConfig.scriptDataType === 'view'
          ) {
            return Number((view as IGlobalResourceList)?.showTime) * 1000 || 0;
          }
          return 0;
        },
      }),
    // 视频内容
    [COMPONENT_LIST.LIVE_VIDEO]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptVideoPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        setVideoStyle: (size) => {
          // 产品逻辑: 竖屏模版 1:1 居中
          if (componentConfig?.videoType === Direction.Vertical && size >= 1) {
            return commonVideoStyle(0, 200, VideoTemplateSize.width);
          }
          return componentStyle ?? {};
        },
        commonStyle: {}, // fake
        setVideoUrl: ({ script }) => {
          return script.globalField?.info?.video_address || '';
        },
        setVideoDuration: () => {
          return videoDuration;
        },
        setVideoHeight: (size) => {
          if (componentConfig?.videoType === Direction.Vertical) {
            // 竖屏模版
            return 0;
          }

          return size >= 1 ? 0 : VideoTemplateSize.height - 220;
        },
        isSkip: (_, index) => {
          console.log('!!!', pagedooScript.globalField);
          return !pagedooScript.globalField?.info?.video_address || index !== 0;
        },
        isLoop: false,
      }),
    // 字幕
    [COMPONENT_LIST.VIDEO_SUBTITLE]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptSubtitlePlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (_, index) => !pagedooScript.globalSpeech || index !== 0,
        fontData: () => {
          const fontData = handleComponentConfig({ componentConfig });
          return {
            fontSize: (fontData?.fontSize || 0) * (375 / 455), // 写多少就会体现在“字号”上为多少
            useImage: 1,
            color: {
              color: fontData?.color?.color || '',
              realColor: fontData?.color?.realColor || '',
              show: true,
            },
            width: (fontData?.width || 0) * (375 / 455),
            textAlign: fontData?.textAlign,
            lineHeight: fontData?.lineHeight,
          };
        },
        delayTime: showVirtualman ? 2000 : 200,
        duration: () => {
          return videoDuration;
        },
      }),
    // // 辅助文字
    [COMPONENT_LIST.LIVE_TEXT]: ({
      componentStyle,
      componentConfig,
      timelineIndex,
    }) =>
      scriptLiveTextPlugin({
        script: pagedooScript,
        key: safeKey(timelineIndex),
        timelineIndex,
        commonStyle: componentStyle || {},
        isSkip: (view, index) => {
          if (
            !handleComponentConfig({
              componentConfig,
              view,
              pagedooScript,
            })
          ) {
            return true;
          }
          if (componentConfig?.isSingle && index !== 0) {
            return true;
          }
          return false;
        },
        baseFn: (view) =>
          handleComponentConfig({
            componentConfig,
            view,
            pagedooScript,
          }) || '',
        getAnimationConfig: () => ({ animation: '', speed: 40 }),
        duration: () => {
          if (componentConfig?.isSingle) {
            return videoDuration;
          }
          return 0;
        },
        fontStyle: componentConfig?.fontStyle,
      }),
  };

  return components.map((item, index) => {
    return pluginKeyMap[item.componentKey]?.({ ...item, timelineIndex: index });
  });
}
