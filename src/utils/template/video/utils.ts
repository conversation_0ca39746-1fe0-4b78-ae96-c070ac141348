import { CommonStyle } from '@tencent/pagedoo-library';
import { View } from '../plugins/type';
import { COMPONENT_LIST, VideoTemplateSize } from './constants';
import { resourceItem, Script } from '@/type/pagedoo';
import { IUsage } from './config';
import { commonStyle } from '@/utils/play-view';
import { get } from 'lodash-es';
import { EnumToUnion, SceneSpecification, TemplateContentType } from '../type';
import { TEXT_DEFAULT_SIZE } from '@/pages/Question/constant';
import { BaseScript } from '@/components/ScriptForm/type';

export const genKeysMachine = (baseStartIndex: number) => {
  let startIndex = baseStartIndex;
  return () => {
    startIndex += 1;
    return startIndex;
  };
};

/**
 * 创建组件 key
 * 暂时还不知道这个 key 有什么作用，先创建
 * @param components
 * @param startIndex
 * @returns
 */
export const createComponentIdMap = (
  components: IUsage[],
  startIndex: number
) => {
  const genKeys = genKeysMachine(startIndex);
  const componentMap = new Map<EnumToUnion<typeof COMPONENT_LIST>, number>();
  for (let i = 0; i < components.length; i++) {
    componentMap.set(components[i].componentKey, genKeys());
  }
  return componentMap;
};

/**
 * 生成音效的默认配置
 * @returns
 */
export const genSoundConfig = () => {
  return {
    volume: 0,
    fadeInTime: 0,
    fadeOutTime: 0,
  };
};

export const genBaseSound = (sound: resourceItem) => {
  // const duration = await
  return {
    id: sound.resource_id,
    title: sound.resource_name,
    url: sound.audio_address,
    duration: sound.duration,
  };
};

export function formatDuration(duration: number): string {
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  // 修正秒数，不会展示 00:00:00
  const seconds = duration > 0 && duration < 1 ? 1 : Math.floor(duration % 60);

  const paddedHours = hours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');
  const paddedSeconds = seconds.toString().padStart(2, '0');

  return `${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
}

export const getAudioDuration = (url: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio(url);
    audio.addEventListener('loadedmetadata', () => {
      resolve(audio.duration);
    });
    audio.addEventListener('error', (error) => {
      reject(error);
    });
  });
};

export async function getAudioDurations(
  audios: resourceItem[]
): Promise<Record<string, any>[]> {
  // 创建一个函数，用于加载单个音频文件并获取其时长
  const getAudioDuration = (
    audioItem: resourceItem
  ): Promise<Record<string, any>> =>
    new Promise((resolve, reject) => {
      const audio = new Audio(audioItem.audio_address);
      audio.addEventListener('loadedmetadata', () => {
        resolve({
          ...audioItem,
          duration: formatDuration(audio.duration),
        });
      });
      audio.addEventListener('error', (error) => {
        reject(error);
      });
    });

  // 使用 Promise.all 来并行获取所有音频文件的时长
  const durations = await Promise.all(
    audios.map((audio) => getAudioDuration(audio))
  );

  return durations;
}

export const getScriptAllTypeRescources = (views: View[], type: string) => {
  // views 下有个字段是 resources， views 是个数组，返回 views 下，所有的 key 为 type 的对象的 resources数组，
  // 可能有多个 type 相同的 resources
  return views.reduce((acc, view) => {
    if (view.resources?.[type]) {
      return [...acc, ...view.resources[type]];
    }
    return acc;
  }, [] as unknown as resourceItem[]);
};

export const hiddenStyle = {
  position: {
    top: -5000,
    left: -5000,
    bottom: 0,
    right: 0,
    type: 'absolute',
    outset: 'TopLeft',
    unit: {
      left: 'percent',
      right: 'percent',
      top: 'percent',
      bottom: 'percent',
    },
  },
} as CommonStyle;

export const commonVideoStyle = (
  left: number,
  top: number,
  width: number,
  videoTemplateSize: number = VideoTemplateSize.width
) => {
  return commonStyle(left, top, width, videoTemplateSize);
};

export const createVideoTemplate = (name: string, size = TEXT_DEFAULT_SIZE) => {
  return {
    contentType: TemplateContentType.Video,
    name,
    size,
    scene: SceneSpecification.Portrait,
  };
};
