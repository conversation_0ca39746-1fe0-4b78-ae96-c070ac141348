export enum COMPONENT_LIST {
  /**
   * 数字人
   */
  VIRTUALMAN = 'VirtualMan',
  /**
   * 话术
   */
  LIVE_SPEECH = 'LiveSpeech',
  /**
   * 音效
   */
  FEATURED_SOUND = 'FeaturedSound',
  /**
   * 背景图
   */
  LIVE_BKG = 'LiveBkg',
  /**
   * 视频
   */
  LIVE_VIDEO = 'LiveVideo',
  /**
   * 贴图
   */
  LIVE_IMAGE = 'LiveImage',
  /**
   * 文字
   */
  LIVE_TEXT = 'LiveText',
  /**
   * 花字
   */
  LIVE_SHOWY_TEXT = 'LiveShowyText',
  /**
   * 特效动画
   */
  SPECIAL_EFFECTS_ANIMATION = 'SpecialEffectsAnimation',
  /**
   * 视频字幕
   */
  VIDEO_SUBTITLE = 'VideoSubtitle',
  /**
   * 前景
   */
  LIVE_FOREGROUND = 'liveForeground',
  /**
   * AI主播标识
   */
  LIVE_IDENTIFY = 'liveIdentify',
}

/**
 * 资源类型
 */
export enum RESOURCES_TYPE {
  /**
   * 特色音效
   */
  FEATURED_SOUND_EFFECTS = 'featured-sound-effects',
  /**
   * 贴片
   */
  PATCH = 'patch',
  BACKGROUND_PATCH = 'background-patch',
  /**
   * 花字
   */
  FLOWER_CHARACTER = 'flower-character',
  /**
   *
   */
  COMPONENT_GIF = 'component-gif',
  /**
   * 转场动画
   */
  TRANSITION_GIF = 'transition-gif',
  /**
   * 特效动画
   */
  SPECIAL_EFFECTS_GIF = 'special-effects-gif',
  /**
   * 背景音乐
   */
  BACKGROUND_MUSIC = 'background-music',
  /**
   * 视频
   */
  VIDEO = 'video',
  TEMPLATE = 'template',
}

export const CUSTOM_FONT = {
  woff2: 'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff2',
  woff: 'https://pagedoo.pay.qq.com/cdn/fonts/pagedoo/font32.woff',
  // 汉仪雅酷黑
  hyykh:
    'https://pagedoo.pay.qq.com/material/@platform/8de705982072624fec713d23e082dbac.ttf',
  // 造字工房稚言中圆体
  zzgfzyzyt:
    'https://pagedoo.pay.qq.com/material/@platform/2db792b84e5b4e04ad43bb2609404d1e.ttf',
};

export const Direction = {
  Vertical: 'VerticalVersion',
  LandScape: 'LandScapeVersion',
};

export const VideoTemplateSize = {
  width: 375,
  height: 667,
};

export const horizontalScreenVideoTemplateSize = {
  width: 1920,
  height: 1080,
};

export const DEFAULT_DURATION = 3500;
export const KEY_START_INDEX = 3000;

export const speechDuration_templateId = [
  '748E1AA6-D566-4013-8A54-B1F3148240A9',
];

export const speechDuration_templateId_config: {
  [key in (typeof speechDuration_templateId)[number]]: {
    extendDuration: number;
  };
} = {
  '748E1AA6-D566-4013-8A54-B1F3148240A9': {
    extendDuration: 3000,
  },
};

export const handleSpeechDuration = (speech: string, modulus = 233) => {
  if (!speech) return 1000;
  return speech.length * modulus;
};
