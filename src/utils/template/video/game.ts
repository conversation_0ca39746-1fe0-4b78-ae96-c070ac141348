import { cloneDeep, isEmpty } from 'lodash-es';
import { format, initPlugin, livePlugin, setMeta } from '../common';
import { durationPlugin } from './utils-plugins';
import { VideoTemplateConfig } from './config';
import { pluginInit } from './plugin-init';
import { fontPreloadPlugin } from './utils-plugins/fontPreloadPlugin';
import { CUSTOM_FONT } from './constants';
import { BaseScript } from '@/components/ScriptForm/type';

export function initGameVideoTemplate(
  pagedooScript: BaseScript,
  templateId: string,
  size?: [number, number]
) {
  // 模版配置
  const {
    // config: templateConfig,
    usage: templateUsage,
    mockScript,
  } = VideoTemplateConfig[templateId];

  const copyPagedooScript = isEmpty(pagedooScript)
    ? cloneDeep(mockScript)
    : cloneDeep(pagedooScript);
  // 初始化组件，引入业务逻辑
  const plugins = pluginInit({
    pagedooScript: copyPagedooScript,
    // templateConfig,
    components: templateUsage,
    templateId,
  });

  return [
    // 初始化配置项 + 1 预留 字体预加载
    initPlugin(copyPagedooScript, templateUsage.length + 1),
    ...plugins,
    livePlugin,
    durationPlugin(copyPagedooScript),
    fontPreloadPlugin(1000, plugins.length, CUSTOM_FONT),
    format,
    setMeta({ size: size || copyPagedooScript.size }),
  ];
}
