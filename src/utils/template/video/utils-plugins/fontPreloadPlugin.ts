import { ScriptPlugin } from '../..';
import { liveText } from '@/utils/play-component';
import { uuid } from '@tencent/midas-util';
import { hiddenStyle } from '../utils';
import { PlayConfig } from '@/type/pagedoo';

// 字体预加载
export const fontPreloadPlugin = (
  key: number,
  timelineIndex: number,
  uniqueFont: Record<string, string>
): ScriptPlugin => {
  return (conf: PlayConfig) => {
    if (!key || !timelineIndex) {
      return;
    }

    // const keys = Object.keys(uniqueFont);
    const values = Object.values(uniqueFont);
    const fontUrls = values.join(',');

    for (let i = 0; i < 1; i++) {
      conf.timeline[timelineIndex].node[i] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: liveText(
          key + i,
          {
            fontSize: 0,
            fontFamily: [fontUrls],
          },
          '字体预加载，隐藏不显示',
          hiddenStyle,
          { animation: '', speed: 40 }
        ),
        hidden: true,
        duration: 1000,
        id: uuid(),
        key: key + i,
        offset: i * 1000,
      };
    }
  };
};
