import { PlayConfig } from '@/type/pagedoo';
import { uuid } from '@tencent/midas-util';
import { twoTimeToSeconds } from '../../plugins/utils';
import { BaseScript } from '@/components/ScriptForm/type';
import { isNumber, groupBy, maxBy } from 'lodash-es';

const isNonZeroNumber = (value: number) => {
  return isNumber(value) && value !== 0;
};

/**
 * 控制整体组件的偏移和持续时间
 * 1. 以视频时长为准，截掉所有组件
 * 2. offset 偏移量以 start 为准（已废弃，0829）
 * 3. 对于资源类节点，offset 已在 node 中写入，保留使用该值控制
 *
 * - 对于普通节点，根据 views 来对齐
 * - 对于资源类节点，如果有偏移，则不处理
 * - 视频总时长为最终时长，不依赖话术时间，如果环节大于视频时长，留在最后，hidden
 *
 * - 资源类节点指的是 音效、贴片等
 */
export const durationPlugin = (pagedooScript: BaseScript) => {
  return (conf: PlayConfig) => {
    // 根据 views 来对齐节点
    const sortPlugin = groupBy(
      pagedooScript.globalField?.globalResourceList,
      'resourceKey'
    );
    const pLen = maxBy(Object.values(sortPlugin), 'length') ?? [];
    const maxLength = Math.max(pagedooScript.views.length, pLen?.length);

    const mainVideoDuration =
      Number(pagedooScript.globalField?.info?.video_duration) || Infinity;

    // 打环节标
    conf.fragment.push({
      __config: { name: '' },
      id: uuid(),
      offset: 0,
    });

    // 遍历 timeline 下的 所有数组，每个节点实现 左对齐
    for (let i = 0; i < maxLength; i++) {
      const [start, end] = pagedooScript.views[i]?.duration.split('-') ?? [
        '00:00',
        '00:00',
      ];
      const [secondStart, secondEnd] = [
        twoTimeToSeconds(start),
        twoTimeToSeconds(end),
      ];
      // 这里需要处理最后一个环节，duration 不能大于视频总时长
      const durationTime = secondEnd - secondStart;

      // 同一列 timeline 上的节点 i 是第几行，左对齐
      for (const pagedooPlayTimeline of conf.timeline) {
        const node = pagedooPlayTimeline.node[i];
        const nextNode = pagedooPlayTimeline.node[i + 1];
        if (!node) continue;
        // 如果有 offset，需要采用组件本身的 offset
        if (isNonZeroNumber(node.offset)) {
          // 边界场景1，如果组件时间超出视频时长
          if (node.offset + node.duration > mainVideoDuration) {
            node.offset = mainVideoDuration - node.duration;
            // node.hidden = true;
          }

          // TODO: 非最佳写法
          // 避免当前时间轨内的组件重叠，尾部和下一个节点的头部不可相交
          // 10ms 强制卸载组件
          if (nextNode && node.offset + node.duration > nextNode.offset) {
            node.duration = nextNode.offset - node.offset - 10;
          }

          continue;
        }
        // TODO: 后续需要移除 views
        // 自动根据 views 生成 offset
        // 如果开始时间就大于 视频时间，
        if (secondStart > mainVideoDuration) {
          node.duration = Math.min(durationTime, node.duration);
          node.offset = mainVideoDuration - node.duration;
          node.hidden = true;
          continue;
        }

        // 如果脚本时间 大于视频时间
        if (secondEnd > mainVideoDuration) {
          // 截断
          node.duration =
            node.duration > 0
              ? Math.min(node.duration, mainVideoDuration - secondStart)
              : mainVideoDuration - secondStart;
          node.offset = secondStart;
          continue;
        }
        // 正常情况 如果 有 duration  那就显示
        node.duration = node.duration > 0 ? node.duration : durationTime;
        // 每一列的节点都是按照 start 来排
        node.offset = secondStart;
      }
    }
  };
};
