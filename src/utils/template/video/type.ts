import { EnumToUnion } from '../type';
import { COMPONENT_LIST } from './constants';

export type ComponentList = EnumToUnion<typeof COMPONENT_LIST>;

export type IComponentConfigCommon<T> = T extends {
  componentConfigCommonType: 'script';
}
  ? T & {
      scriptKey: string; // 解析脚本的key 支持嵌套取值a.b.c 如果中间存在数组嵌套可使用 a.b[0].c
      scriptDataType?: 'global' | 'view'; // 取值范围view或者原始脚本 不传默认view
    }
  : T;
