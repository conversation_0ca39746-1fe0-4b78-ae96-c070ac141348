/**
 * <AUTHOR>
 * @date 2024/9/5 下午3:53
 * @desc sessionStorageUtil
 */

class SessionStorageUtil {
  /**
   * 设置 sessionStorage 的值
   * @param key 键
   * @param value 值
   */
  static setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value);
      sessionStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error('设置 sessionStorage 出错:', error);
    }
  }

  /**
   * 获取 sessionStorage 的值
   * @param key 键
   * @returns 值
   */
  static getItem<T>(key: string): T | null {
    try {
      const serializedValue = sessionStorage.getItem(key);
      if (serializedValue === null) {
        return null;
      }
      return JSON.parse(serializedValue) as T;
    } catch (error) {
      console.error('获取 sessionStorage 出错:', error);
      return null;
    }
  }

  /**
   * 删除 sessionStorage 中的某个键值对
   * @param key 键
   */
  static removeItem(key: string): void {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error('删除 sessionStorage 出错:', error);
    }
  }

  /**
   * 清空 sessionStorage
   */
  static clear(): void {
    try {
      sessionStorage.clear();
    } catch (error) {
      console.error('清空 sessionStorage 出错:', error);
    }
  }
}

export default SessionStorageUtil;
