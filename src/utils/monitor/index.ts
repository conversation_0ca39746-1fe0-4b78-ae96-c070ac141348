import { AegisWrapperType, getAegis } from '@/utils/monitor/aegis';
import { LogMethod } from '@/utils/monitor/typings';

export let gloablMonitor: {
  aegisWrapper: AegisWrapperType | null;
  setUin: (u: string) => void;
  log: LogMethod;
} | null = null;

/**
 * 统一监控上报
 */
export const getMonitor = (options: {
  aegisReportId: string;
  env: 'development' | 'production';
  uin?: string;
}) => {
  if (gloablMonitor) return gloablMonitor;

  gloablMonitor = {
    aegisWrapper: null,
    setUin: (userID: string) => {
      gloablMonitor?.aegisWrapper?.setUin?.(userID);
    },
    log: (level, data) => {
      gloablMonitor?.aegisWrapper?.log?.(level, data);
    },
  };
  // 异步加载不影响主逻辑
  getAegis({
    reportId: options.aegisReportId,
    isRelease: options.env === 'production',
    uin: options.uin,
  }).then(({ aegisWrapper }) => {
    if (gloablMonitor) {
      gloablMonitor.aegisWrapper = aegisWrapper;
    }
  });

  return gloablMonitor;
};
