import type Aegis from 'aegis-web-sdk';
import loadScript from '@tencent/midas-util/lib/loadScript';
import { LogType } from '@/utils/monitor/typings';
let aegis: Aegis;
export interface IGetAegisOptions {
  reportId: string;
  isRelease: boolean;
  uin?: string;
}

const aegisWrapper = {
  setUin: (userID: string) => {
    aegis?.setConfig({
      uin: userID,
    });
  },
  log: (
    level: LogType,
    data: {
      msg: string;
      extension?: string[];
      trace?: string;
    }
  ) => {
    const logData = {
      msg: data.msg,
      ...(data.extension || []).reduce((prev, current, index) => {
        return {
          ...prev,
          [`ext${index + 1}`]: current,
        };
      }, {}),
      trace: data.trace || '',
    };
    if (level === 'info') {
      aegis?.infoAll?.(logData);
    } else if (level === 'error') {
      aegis?.error?.(logData);
    } else if (window.Aegis?.logType?.[level]) {
      aegis?.report({
        ...logData,
        level: window.Aegis.logType[level],
      });
    }
  },
};

export interface IGetAegisReturn {
  aegisWrapper: typeof aegisWrapper;
}

export type AegisWrapperType = typeof aegisWrapper;

export const getAegis = async (
  options: IGetAegisOptions
): Promise<IGetAegisReturn> => {
  if (aegis)
    return {
      aegisWrapper,
    };
  await new Promise<void>((resolve) => {
    if (!window.Aegis) {
      return loadScript(
        'https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js',
        () => {
          resolve();
        },
        void 0
      );
    }
    return resolve();
  }).then(() => {
    if (!window.Aegis) return;
    const { isRelease, reportId } = options;
    aegis = new window.Aegis({
      // 正式环境的上报id 和其他环境分开
      id: reportId, // 上报 id
      reportApiSpeed: true, // 接口测速
      reportAssetSpeed: true, // 静态资源测速
      spa: true, // spa 应用页面跳转的时候开启 pv 计算
      hostUrl: 'https://rumt-zh.com',
      env: isRelease ? 'production' : 'development',
      aid: true,
      uin: options.uin || '',
      // aid: options.aid,
      api: {
        apiDetail: true,
        reportRequest: true,
        retCodeHandler: (data: string) => {
          if (typeof data !== 'string')
            return {
              isErr: false,
              code: '0',
            };
          let parsedData;
          try {
            parsedData = JSON.parse(data);
          } catch (e) {}
          if (!parsedData) {
            return {
              isErr: true,
              code: 'DataParseError',
            };
          }
          const ret = parsedData.result_code || parsedData.code;
          if (ret === '0' || ret === 'SUCCESS') {
            // 有 error key
            return {
              isErr: false,
              code: '0',
            };
          }
          return {
            isErr: true,
            code: `${ret}`,
          };
        },
      },
    });
  });
  return {
    aegisWrapper,
  };
};
