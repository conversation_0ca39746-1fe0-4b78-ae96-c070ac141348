import { IADSSOOptions } from '@/utils/login/ad_sso/typings';
import { IWechatOAuth } from '@/utils/login/wechat/type';

export enum LoginTypeEnum {
  WX = 'wx',
  AD_SSO = 'ad_sso',
}

export type LoginTypes = 'wx' | 'ad_sso';

// 浏览器跳转方式
export type LoginMethod = 'iframe' | 'new_tab' | 'reload' | 'router_link';

// 用户信息
export interface ILoginUserInfo {
  // 用户id
  openid: string;

  openkey: string;

  // 用户昵称
  nickName: string;

  // 用户头像
  avatar: string;

  // 聚合版扩展信息
  pagedooExtend?: {
    account_id: string;
  };

  // 广告额外信息
  adExtend?: {
    account_id: string;
    login_type: 'qq' | 'wx' | 'qq_error';
    roles: string[];
    appType: string;
  };
}

export type LoginOptionsMappingType = {
  wx?: IWechatOAuth;
  ad_sso?: IADSSOOptions;
};

export type LoginTicketDataMappingType = {
  [K in LoginTypes]: {
    // refreshToken
    refreshToken: string;
  };
};

// 获取登录回调信息
export interface ILoginCallbackInfo {
  // 真实回调地址
  redirectUrl: string;

  loginUserInfo?: ILoginUserInfo | null;
}

// 存放在存储/cookie中的登录票据信息
export interface ILoginTicketInfo<
  T extends LoginTypes,
  Data = LoginTicketDataMappingType[T]
> {
  type: T;
  data: Data;
}
