/* eslint-disable class-methods-use-this */
import {
  clearLocalStorage,
  getLocalStorageWithExpiry,
  setLocalStorageWithExpiry,
} from '@/utils/localStorage';
import { IADSSOOptions, ISSOGetUserInfoResp } from './typings';
import { BaseLogin } from '@/utils/login/base-login';
import {
  ILoginCallbackInfo,
  ILoginUserInfo,
  LoginTypes,
} from '@/utils/login/typings';
import to from 'await-to-js';
import axios from 'axios';
import { Base64, fromBase64, toBase64 } from 'js-base64';
import { NeedRetryGetLoginInfoError } from '@/utils/login/login-errors';
import { MUSE_ACCOUNT_KEY } from '@/pages/MuseLogin/const';

const userInfoCacheKey = '__avatar_ad_user_info';

const urls = {
  queryUserInfo: '/intelligent/muse/user/info',
  logout: '/intelligent/muse/user/logout',
};

// 用户信息缓存
const userInfoCache = {
  set(key: string, data: ISSOGetUserInfoResp['data'], ttl: number) {
    setLocalStorageWithExpiry(key, Base64.encode(JSON.stringify(data)), ttl);
  },
  get(key: string): ISSOGetUserInfoResp['data'] | null {
    const str = getLocalStorageWithExpiry(key);
    try {
      return JSON.parse(Base64.decode(str));
    } catch (e) {}
    return null;
  },
};

const resolveUserInfoFromSSO = (
  data: ISSOGetUserInfoResp['data']
): ILoginUserInfo => {
  const userInfo: ILoginUserInfo = {
    openid: data.user_id ?? '',
    openkey: data.qq_number || data.wechat_id || '',
    avatar: data.user_avatar,
    nickName: data.user_name,
  };
  let loginType: ISSOGetUserInfoResp['data']['login_type'] | 'qq_error' =
    data.login_type;
  // 如果是qq登陆态且没有qq号，处理为qq_error，没有在广告平台注册过所以没有
  if (loginType === 'qq' && !data.qq_number) {
    loginType = 'qq_error';
  }
  userInfo.adExtend = {
    account_id: localStorage.getItem(MUSE_ACCOUNT_KEY) || '',
    login_type: loginType,
    roles: [],
    appType: '',
  };
  return userInfo;
};

export class AdSSOLogin extends BaseLogin<'ad_sso', IADSSOOptions> {
  public constructor(
    type: Extract<LoginTypes, 'ad_sso'>,
    options: IADSSOOptions
  ) {
    super(type, options);
  }
  public async getUserInfo(options?: {
    noCache?: boolean;
  }): Promise<ILoginUserInfo | undefined> {
    return await this.innerGetUserInfo(options);
  }

  public async getLoginUrl(options: {
    redirectUrl: string;
    proxyQuery?: Record<string, string>;
    useProxy?: boolean;
  }): Promise<string> {
    const { redirectUrl, proxyQuery, useProxy } = options;
    clearLocalStorage(userInfoCacheKey);
    localStorage.removeItem(MUSE_ACCOUNT_KEY);
    let realRedirectUrl: string;
    if (useProxy) {
      realRedirectUrl = this.getLoginRedirectUrl({
        proxyQuery: {
          ...proxyQuery,
          ru: redirectUrl,
        },
      });
    } else {
      realRedirectUrl = this.getLoginRedirectUrl({
        redirectUrl,
      });
    }
    const ru = new URL(realRedirectUrl);
    if (ru.hash.indexOf('?') === -1) {
      ru.hash = `${ru.hash}?`;
    }

    // 对ru进行base64编码防止被sso篡改
    const regex = /ru=([^&#]+)/;
    realRedirectUrl = ru
      .toString()
      .replace(regex, (_, p1) => `ru=${toBase64(p1)}`);

    const callbackUrl = new URL(
      `https://${window.location.host}/intelligent/muse/user/login`
    );
    callbackUrl.searchParams.set('url', realRedirectUrl);

    const loginBaseUrl = `https://sso.e.qq.com/login/hub?service_tag=${this.options.serviceTag}&redirect_target=self`;
    const u = new URL(loginBaseUrl);
    u.searchParams.set('sso_redirect_uri', callbackUrl.toString());
    const loginUrl = u.toString();
    return `${location.origin}${
      location.pathname
    }?t=${new Date().getTime()}#/sso-login?iframeUrl=${encodeURIComponent(
      loginUrl
    )}`;
  }
  /**
   *
   * @throws {NeedLoginError}
   * @throws {NeedRetryGetLoginInfoError}
   */
  public async getLoginCallbackInfo(): Promise<ILoginCallbackInfo> {
    const currentUrl = new URL(location.href);
    let redirectUrl = '';
    if (currentUrl.hash.indexOf('#/login_callback') > -1) {
      // 使用登录结果页方式接受登录结果，需要解析ru参数
      redirectUrl = fromBase64(currentUrl.searchParams.get('ru') || '');
    }
    return {
      redirectUrl,
    };
  }

  public async logout() {
    await this.clearTickInfo();
    return {
      // 是否重定向到登录页
      redirectToLogin: true,
    };
  }

  protected async innerGetUserInfo(options?: {
    noCache?: boolean;
  }): Promise<ILoginUserInfo> {
    const cached: ISSOGetUserInfoResp['data'] | null =
      userInfoCache.get(userInfoCacheKey);
    if (!options?.noCache && cached) {
      return resolveUserInfoFromSSO(cached);
    }
    const [err, result] = await to(
      axios.post<ISSOGetUserInfoResp>(
        urls.queryUserInfo,
        {},
        {
          baseURL: location.origin,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
    );
    if (err) {
      throw new NeedRetryGetLoginInfoError();
    }
    if (result.data?.code !== 0) {
      this.throwNeedLogin();
    }
    if (result.data.data) {
      // 用户信息缓存10分钟
      try {
        userInfoCache.set(userInfoCacheKey, result.data.data, 10 * 16);
      } catch (e) {
        // 设置缓存异常
        console.error(e);
      }
    }
    return resolveUserInfoFromSSO(result.data?.data);
  }
  // 退出登录接口实现
  public async clearTickInfo(): Promise<void> {
    clearLocalStorage(userInfoCacheKey);
    localStorage.removeItem(MUSE_ACCOUNT_KEY);
    await to(
      axios.post(
        urls.logout,
        {},
        {
          baseURL: location.origin,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
    );
  }
}
