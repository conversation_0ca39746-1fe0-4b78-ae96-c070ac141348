// 登录sdk桥接 ui 状态 -> login类过程
import { MatchedGlobalConfigItem } from '@/configs/global_runtime';
import { ILoginSession } from '@/model/login';
import { createLoginInstance } from '@/utils/login';
import { loginCBKeyName } from '@/utils/login/base-login';
import { ILoginUserInfo, LoginMethod, LoginTypes } from '@/utils/login/typings';
import { sleep } from '@/utils/sleep';
import EventBus from '@tencent/eventbus';
import { cookies, delParam } from '@tencent/midas-util';
import to from 'await-to-js';
import { DialogInstance, DialogPlugin, MessagePlugin } from 'tdesign-react';
import { MUSE_ACCOUNT_KEY } from '@/pages/MuseLogin/const';
import { AVATAR_ACCOUNT_KEY } from '@/pages/Index/ApplicationSelect/const';

const {
  runtimeConf: { loginConf },
} = MatchedGlobalConfigItem;
// TODO: 这里需要通过编译时注入一些配置
const currentLoginType: LoginTypes = loginConf.type;
const currentLoginOptions = loginConf.options;
const { method: currentMethod, loginHash } = loginConf;

console.log('登录配置 ', loginConf);
/**
 * iframe: 使用iframe方式嵌入登录页地址
 * new_tab: 新窗口打开登录页
 * relad: 当前页直接跳转后回调
 */

const loginMethodKeyname = '__login_method';

const sessionMapping: Record<
  LoginTypes,
  {
    session_id: string;
    session_type: string;
  }
> = {
  wx: {
    session_id: 'hy_gameid',
    session_type: 'wc_actoken',
  },
  ad_sso: {
    session_id: 'hy_gameid',
    session_type: 'ad_sso',
  },
};

export interface IGotoLoginOptions {
  redirectUrl?: string;
  method?: LoginMethod;
  userGesture?: boolean;
  navigate?: any;
}

export interface IFetchLoginInfo {
  loginSession: ILoginSession;
  loginUserInfo: ILoginUserInfo;
}

export interface IGoLogoutOptions {
  disablelogoutIndex?: boolean;
  disableRedirect?: boolean;
}

export const createLoginHandle = () => {
  // 跳登录流程
  const loginInst = createLoginInstance(
    currentLoginType as LoginTypes,
    currentLoginOptions
  );
  const fetchUserInfoQueue: {
    resolve: (info: IFetchLoginInfo) => void;
    reject: (e: Error) => void;
  }[] = [];
  const eventBus = new EventBus<{
    refresh: () => void;
  }>();
  let loginTipsDialog: DialogInstance;
  let isFetching = false;

  const gotoLogin = async (options?: IGotoLoginOptions) => {
    const method = options?.method || currentMethod;
    const doJumpLogin = async () => {
      if (currentMethod === 'router_link' && loginHash) {
        window.location.href = `${window.location.origin}${window.location.pathname}${location.search}`;
        localStorage.removeItem(MUSE_ACCOUNT_KEY);
        // window.location.replace(loginHash);
        return;
      }
      const url = await loginInst?.getLoginUrl({
        redirectUrl: options?.redirectUrl || location.href,
        proxyQuery: {
          [loginMethodKeyname]: method,
        },
        useProxy: loginConf.useProxy,
      });
      if (!url) return;
      if (method === 'reload') {
        localStorage.removeItem(AVATAR_ACCOUNT_KEY);
        location.replace(url);
        return void 0;
      }
      if (method === 'new_tab') {
        const opener = window.open(url);
        let loginSuccess = false;
        await new Promise((resolve) => {
          if (opener) {
            const onmessage = (ev: MessageEvent) => {
              if (ev.source !== opener) return;
              if (ev.data.status === 'success') {
                loginSuccess = true;
              }
            };
            window.addEventListener('message', onmessage);
            const interval = setInterval(() => {
              if (opener.closed) {
                clearInterval(interval);
                window.addEventListener('message', onmessage);
                resolve(void 0);
              }
            }, 1000);
          }
        });
        if (loginSuccess) {
          // 通知消费方刷新登录态
          console.log('刷新登录');
          eventBus.emit('refresh');
        }
        return loginSuccess;
      }
    };
    if (method === 'new_tab' && !options?.userGesture) {
      // 需要弹提示框确认跳转登录
      await new Promise((resolve) => {
        loginTipsDialog = DialogPlugin.confirm({
          closeBtn: false,
          cancelBtn: false,
          body: '登录已失效，请重新登录',
          onConfirm: async () => {
            if (await doJumpLogin()) {
              resolve(true);
            }
          },
        });
      });
      loginTipsDialog.destroy();
    } else {
      await doJumpLogin();
    }
  };

  const processLoginCallback = async () => {
    // 从url获取
    // const currentMethod = getParam(loginMethodKeyname);
    if (currentMethod === 'router_link') {
      if (window.parent) {
        window.parent.postMessage({
          status: 'success',
          url: `${window.location.origin}${window.location.pathname}${location.search}`,
        });
      }
      return;
    }
    const [err, info] = await to(loginInst?.getLoginCallbackInfo());
    if (!info || err) {
      MessagePlugin.error(err.message, 2000);
      if (currentMethod === 'new_tab') {
        await sleep(2000);
        window.close();
      }
      return;
    }

    if (currentMethod === 'reload') {
      console.log('回调地址 ', info.redirectUrl);
      if (window.parent) {
        window.parent.postMessage({
          status: 'success',
          redirectUrl: info.redirectUrl,
        });
        await sleep(300);
        window.close();
      } else {
        location.replace(info.redirectUrl);
      }
    } else if (currentMethod === 'new_tab') {
      if (window.opener) {
        (window.opener as Window).postMessage({
          status: 'success',
        });
        await sleep(300);
      }
      window.close();
    }
  };

  /**
   *
   * @throws {NeedLoginError}
   * @returns
   */
  const fetchLoginInfo = async (options?: {
    noCache?: boolean;
  }): Promise<IFetchLoginInfo> => {
    if (!loginInst) throw new Error('登录初始化异常');
    if (!isFetching) {
      const inner = async () => {
        // 判断是否要处理__login_cb=1 的情况
        const searchParams = new URLSearchParams(location.search);
        if (searchParams.get(loginCBKeyName) === '1') {
          const [err] = await to(loginInst?.getLoginCallbackInfo());
          if (!err) {
            const finalUrl = delParam([loginCBKeyName], location.href);
            const fu = new URL(finalUrl);
            fu.search = '';
            if (fu.hash.endsWith('?')) {
              // hash 中最后一个字符串有?，要移除掉
              fu.hash = fu.hash.substring(0, fu.hash.length - 1);
            }
            history.replaceState({}, '', fu.toString());
          }
        }
        const [err, info] = await to(loginInst.getUserInfo(options));
        if (err) {
          throw err;
        }
        const loginSession: ILoginSession = {
          type: currentLoginType,
          offer_id: '**********',
          openid: info?.openid || '',
          openkey: info?.openkey || '',
          app_id: info?.pagedooExtend?.account_id || '',
          strinifyLoginInfo: '',
          ...sessionMapping[currentLoginType],
        };
        if (loginSession.openid) {
          cookies.set('__avatar_uin', loginSession.openid, {
            domain: location.host,
            path: '/',
          });
        }
        let strinifyLoginInfo = '';
        if (currentLoginType === 'wx') {
          strinifyLoginInfo = JSON.stringify(loginSession);
        } else if (currentLoginType === 'ad_sso') {
          strinifyLoginInfo = JSON.stringify({
            app_id: localStorage.getItem(MUSE_ACCOUNT_KEY) || '',
          });
        }
        const loginInfo: IFetchLoginInfo = {
          loginSession: {
            type: currentLoginType,
            offer_id: '**********',
            app_id: info?.pagedooExtend?.account_id || '',
            openid: info?.openid || '',
            openkey: info?.openkey || '',
            strinifyLoginInfo,
            ...sessionMapping[currentLoginType],
          },
          loginUserInfo: info || {
            avatar: '',
            nickName: '',
            openid: '',
            openkey: '',
          },
        };
        return loginInfo;
      };
      isFetching = true;
      const [err, loginInfo] = await to(inner());
      isFetching = false;
      // 判断是否有票据
      while (fetchUserInfoQueue.length > 0) {
        const item = fetchUserInfoQueue.shift();
        console.log('flushing login queue');
        if (item) {
          if (err) {
            item.reject(err);
          } else {
            item.resolve(loginInfo);
          }
        }
      }
      if (err) throw err;
      return loginInfo;
    }
    console.log('queue fetch login info');
    return new Promise<IFetchLoginInfo>((resolve, reject) => {
      fetchUserInfoQueue.push({
        resolve,
        reject,
      });
    });
  };

  const onRefresh = (fn: () => void) => {
    eventBus.on('refresh', fn);
    return () => {
      eventBus.off('refresh', fn);
    };
  };
  const destory = () => {
    eventBus.off();
  };
  const getLoginType = (): LoginTypes => {
    return currentLoginType;
  };

  // 退出登录
  const goLoginOut = async (options?: IGoLogoutOptions) => {
    cookies.del('__avatar_uin', {
      domain: location.host,
      path: '/',
    });
    if (!options?.disablelogoutIndex && loginConf.logoutIndex) {
      location.replace(loginConf.logoutIndex);
    }
    // loginConf.logoutIndex && location.replace(loginConf.logoutIndex);
    const result = await loginInst.logout();
    if (result?.redirectToLogin && !options?.disableRedirect) {
      await gotoLogin();
    }
  };
  return {
    getLoginType,
    gotoLogin,
    processLoginCallback,
    fetchLoginInfo,
    onRefresh,
    destory,
    goLoginOut,
  };
};
