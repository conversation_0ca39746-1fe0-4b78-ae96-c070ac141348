import { LoginTypeEnum, LoginTypes } from '@/utils/login/typings';
import ExtendableError from 'extendable-error';

export class NeedLoginError extends ExtendableError {
  public constructor(protected type: LoginTypes, protected msg = '') {
    super(`登录过期 ${msg}`);
  }
}

export class TicketExpiredError extends ExtendableError {
  public constructor(protected type: LoginTypes, protected msg = '') {
    super(`登录 Token 已过期 ${msg}`);
  }
}

export class NeedRetryGetLoginInfoError extends ExtendableError {
  public constructor(msg = '') {
    super(`获取用户信息失败，请重试 ${msg}`);
  }
}
