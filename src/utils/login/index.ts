import { BaseLogin } from '@/utils/login/base-login';
import { delParam, getParam } from '@tencent/midas-util';
import { IWechatOAuth, WechatOAuthScopeType } from './wechat/type';
import { MessagePlugin } from 'tdesign-react';
import { setRecoil } from 'recoil-nexus';
import { LoginStateAtom } from '@/model/login';
import { UserInfoAtom } from '@/model/user';
import WechatOAuth, { WechatLogin } from './wechat';
import {
  LoginOptionsMappingType,
  LoginTypeEnum,
  LoginTypes,
} from '@/utils/login/typings';
import { AdSSOLogin } from '@/utils/login/ad_sso/adSSOLogin';
import { IADSSOOptions } from '@/utils/login/ad_sso/typings';

export interface LoginState {
  openid: string;
  openkey: string;
  session_id: 'hy_gameid';
  session_type: 'wc_actoken';
  wx_appid: string;
  avatar: string;
  nickname: string;
}

export function withoutLogin() {
  setRecoil(LoginStateAtom, undefined);
  setRecoil(UserInfoAtom, undefined);
  const noIndex = getParam('no_index');
  if (noIndex) {
    const wechat = new WechatOAuth({
      domain: 'pay.qq.com',
    });
    wechat.oAuth(WechatOAuthScopeType.snsapi_login);
    return;
  }
  let hashStr = window.location.hash;
  const index = hashStr.indexOf('?');
  if (index !== -1) {
    hashStr = hashStr.substring(0, index);
  }
  if (!['', '#/'].includes(hashStr)) {
    MessagePlugin.error({
      content: '您的登录已失效，请重新登录',
    });
  }
  window.location.hash = '#/';
}

export function createLoginInstance<
  T extends LoginTypes,
  Options = LoginOptionsMappingType[T]
>(type: T, options: Options) {
  if (type === 'wx') {
    return new WechatLogin(type, options as IWechatOAuth);
  }
  if (type === 'ad_sso') {
    return new AdSSOLogin(type, options as IADSSOOptions);
  }
  throw new Error('不支持的登录方式');
}
