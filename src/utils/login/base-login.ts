/* eslint-disable class-methods-use-this */
import { NeedLoginError } from '@/utils/login/login-errors';
import {
  ILoginCallbackInfo,
  ILoginTicketInfo,
  ILoginUserInfo,
  LoginTypeEnum,
  LoginTypes,
} from '@/utils/login/typings';
import { addParam, cookies } from '@tencent/midas-util';

export const loginCBKeyName = '__login_cb';

export abstract class BaseLogin<T extends LoginTypes, Options> {
  public constructor(protected type: T, protected options: Options) {}

  // 获取登录类型
  public getType(): T {
    return this.type;
  }
  // 获取用户信息
  public abstract getUserInfo(): Promise<ILoginUserInfo | undefined>;

  // 判断是否存在登录票据
  // public abstract checkTicketExists(): Promise<boolean>;

  // 获取跳转登录地址
  public abstract getLoginUrl(options: {
    redirectUrl: string;
    proxyQuery?: Record<string, string>;
    useProxy?: boolean;
  }): Promise<string>;

  /// 获取登录回调信息
  public abstract getLoginCallbackInfo(): Promise<ILoginCallbackInfo>;

  public abstract clearTickInfo(): Promise<void>;

  // 退出登录
  public abstract logout(): Promise<
    | {
        // 是否重定向到登录页
        redirectToLogin?: boolean;
      }
    | undefined
  >;

  // eslint-disable-next-line class-methods-use-this
  protected getLoginRedirectUrl(options: {
    redirectUrl?: string;
    proxyQuery?: Record<string, string>;
  }): string {
    const { proxyQuery, redirectUrl } = options;
    if (redirectUrl) {
      // 外部自定义redirectUrl，添加参数
      const u = new URL(redirectUrl);
      u.searchParams.set(loginCBKeyName, '1');
      return u.toString();
    }
    const u = new URL(
      `${location.protocol}//${location.host}${location.pathname}${location.search}#/login_callback?`
    );
    if (proxyQuery) {
      Object.keys(proxyQuery).forEach((key) => {
        u.searchParams.set(key, proxyQuery[key]);
      });
    }
    return u.toString();
  }

  // 通用的设置票据信息
  /**
   *
   * @param tickInfo 票据信息
   * @param ttl 存活时长，毫秒为单位
   */
  protected async setTicketInfo(
    tickInfo: ILoginTicketInfo<T>,
    ttl: number
  ): Promise<void> {
    const str = btoa(JSON.stringify(tickInfo));
    // base64方式存入
    cookies.set('__dm_b_tc', str, {
      time: ttl,
    });
  }
  // 获取票据信息
  protected async getTickInfo(): Promise<ILoginTicketInfo<T> | null> {
    const val = cookies.get('__dm_b_tc');
    if (!val) return null;
    try {
      const d = atob(val);
      return JSON.parse(d);
    } catch (e) {}
    return null;
  }

  /**
   * @throws {NeedLoginError}
   */
  protected throwNeedLogin() {
    throw new NeedLoginError(this.type);
  }
}
