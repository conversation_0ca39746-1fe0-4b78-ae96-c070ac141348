import axios from 'axios';

/**
 * 通过 openid/accessToken获取用户信息
 * @param code
 * @param refreshToken
 * @param wxAppid
 */
export default function getWechatAuthInfo({
  code,
  refreshToken,
  wxAppid,
  get_detail,
}: {
  code: string;
  refreshToken: string;
  wxAppid: string;
  get_detail?: string;
}) {
  const wechatAuthUrl = `https://avatar.pay.qq.com/cgi-bin/account/get_wechat_userinfo.cgi?code=${code}&refresh_token=${refreshToken}&appid=${wxAppid}&get_detail=${get_detail}`;

  return axios.get(wechatAuthUrl).then((res) => res.data);
}
