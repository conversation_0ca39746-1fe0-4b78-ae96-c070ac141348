export interface IWechatOAuth {
  domain: string;
  scope?: string;
}

export enum WechatOAuthScopeType {
  snsapi_base = 'snsapi_base',
  snsapi_userinfo = 'snsapi_userinfo',
  snsapi_login = 'snsapi_login',
}

export interface IWechatUserInfoResp {
  resultcode: number;
  resultinfo: {
    errmsg: string;
    list: any[];
    obj: {
      openid: string;
      nickname: string;
      sex: number;
      language: string;
      city: string;
      province: string;
      country: string;
      headimgurl: string;
      privilege: any[];
      unionid: string;
      access_token: string;
      refresh_token: string;
      expires_in: number;
      scope: string;
    };
  };
}
