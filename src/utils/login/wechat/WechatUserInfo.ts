import axios from 'axios';

/**
 * 通过 openid/accessToken获取用户信息
 * @param openid
 * @param accessToken
 * @param wxAppid
 */
export default function getWechatUserInfo({
  openid,
  accessToken,
  wxAppid,
}: {
  openid: string;
  accessToken: string;
  wxAppid: string;
}) {
  const getUserInfoUrl = `https://avatar.pay.qq.com/cgi-bin/account/get_wechat_userinfo.cgi?openid=${openid}&access_token=${accessToken}&get_detail=2&appid=${wxAppid}`;

  return axios
    .get(getUserInfoUrl)
    .then((res) => res.data?.resultinfo?.obj ?? {});
}
