import {
  cookies,
  delParam,
  getParam,
  getParams,
  session_wechat as SessionWechat,
} from '@tencent/midas-util';

import {
  IWechatOAuth,
  IWechatUserInfoResp,
  WechatOAuthScopeType,
} from './type';
import getWechatUserInfo from './WechatUserInfo';
import getWechatAuthInfo from './WechatAuth';
import { withoutLogin } from '..';
import { BaseLogin } from '@/utils/login/base-login';
import {
  ILoginCallbackInfo,
  ILoginTicketInfo,
  ILoginUserInfo,
  LoginTypeEnum,
  LoginTypes,
} from '@/utils/login/typings';
import { NeedLoginError, TicketExpiredError } from '@/utils/login/login-errors';
import {
  clearLocalStorage,
  getLocalStorageWithExpiry,
  setLocalStorageWithExpiry,
} from '@/utils/localStorage';
import to from 'await-to-js';
import { AVATAR_ACCOUNT_KEY } from '@/pages/Index/ApplicationSelect/const';

export default class WechatOAuth {
  /**
   * 类私有变量
   * @private
   */
  public readonly wxAppid: string;
  private readonly domain: string;
  public readonly refreshTokenKey: string;

  /**
   * 构造函数
   * @param options
   */
  constructor(options: IWechatOAuth) {
    this.wxAppid = import.meta.env.VITE_WXAPP_ID;
    this.domain = options.domain;
    this.refreshTokenKey = `${this.wxAppid}rt`;

    // 将 qq.com 中的也删除
    cookies.del(this.refreshTokenKey, {
      time: -3600000,
      domain: 'qq.com',
    });
  }

  /**
   * 获取微信登录态信息
   * @param scope
   */
  async getWechatSession(scope: WechatOAuthScopeType): Promise<any> {
    let code = '';
    let refreshToken = '';
    const paramsCode = getParam('code');
    const cookieRefreshToken = cookies.get(this.refreshTokenKey);

    if (paramsCode) {
      code = paramsCode;
    } else if (cookieRefreshToken) {
      refreshToken = cookieRefreshToken;
    }

    // 如果没有 code 或者 refreshToken 需要走 oAuth
    if (!code && !refreshToken) {
      // return this.oAuth(scope);
      return withoutLogin();
    }
    console.log('code', code);
    // 获取用户 openid 等基础信息
    const result = await getWechatAuthInfo({
      code,
      refreshToken,
      wxAppid: this.wxAppid,
      get_detail: scope === WechatOAuthScopeType.snsapi_login ? '1' : '',
    });

    if (!result.resultcode) {
      let baseInfo = result.resultinfo.obj;

      baseInfo.wxAppid = this.wxAppid;

      // 增加授权类型
      if (
        baseInfo.scope.indexOf(WechatOAuthScopeType.snsapi_userinfo) > -1 &&
        baseInfo.scope.indexOf(WechatOAuthScopeType.snsapi_base) === -1
      ) {
        baseInfo.scope = `${baseInfo.scope},${WechatOAuthScopeType.snsapi_base}`;
      }

      // 无 userinfo 授权跳转
      if (
        scope === WechatOAuthScopeType.snsapi_userinfo &&
        baseInfo.scope.indexOf(WechatOAuthScopeType.snsapi_userinfo) === -1
      ) {
        withoutLogin();
      }

      // 有授权则获取用户信息
      if (scope === WechatOAuthScopeType.snsapi_userinfo) {
        const userInfo = await getWechatUserInfo({
          openid: baseInfo.openid,
          accessToken: baseInfo.access_token,
          wxAppid: this.wxAppid,
        });

        baseInfo = {
          ...userInfo,
          ...baseInfo,
        };
      }

      // 种 refreshToken
      cookies.set(this.refreshTokenKey, baseInfo.refresh_token, {
        domain: this.domain,
      });

      return baseInfo;
    }
    // 清除错误 refreshToken
    if (cookieRefreshToken) {
      cookies.del(this.refreshTokenKey, {
        time: -3600000,
        domain: this.domain,
      });

      // 将 qq.com 中的也删除
      cookies.del(this.refreshTokenKey, {
        time: -3600000,
        domain: 'qq.com',
      });
    }

    // 授权过期重新授权
    if (result.resultcode === 12271 || result.resultcode === 1181) {
      return withoutLogin();
    }
  }

  /**
   * 跳微信授权
   * @param scope
   * @param context
   */
  oAuth(scope: WechatOAuthScopeType, context = '') {
    const jump = delParam(
      ['code', 'state', 'openid', 'openkey', 'sessionid', 'sessiontype'],
      location.href
    );
    const hasParam = jump.split('#')[1]?.indexOf('?') > -1;
    location.href =
      `//open.weixin.qq.com/connect/qrconnect?appid=${
        this.wxAppid
      }&redirect_uri=${encodeURIComponent(
        jump + (hasParam ? '' : '?')
      )}&response_type=code` +
      `&scope=${scope}&state=${context}#wechat_redirect`;
  }

  /**
   * 获取微信登录所有的信息
   * @param scope
   */
  async parseWechatSession(scope: WechatOAuthScopeType) {
    const session = await this.getWechatSession(scope);
    const wechatSession = new SessionWechat({
      appid: this.wxAppid,
      openid: session.openid,
      accessToken: session.access_token,
    });

    return {
      ...wechatSession.getSessionParam(),
      avatar: session.headimgurl,
      nickname: session.nickname,
    };
  }
}
const throwNeedLogin = (msg = '') => {
  throw new NeedLoginError(LoginTypeEnum.WX);
};
const resolveLoginUserInfoFromWx = (
  resp: IWechatUserInfoResp
): ILoginUserInfo => {
  const { resultinfo } = resp || {};
  return {
    avatar: resultinfo?.obj.headimgurl,
    nickName: resultinfo?.obj.nickname,
    openid: resultinfo?.obj?.openid,
    openkey: resultinfo?.obj?.access_token,
    pagedooExtend: {
      account_id: localStorage.getItem(AVATAR_ACCOUNT_KEY) || '',
    },
  };
};
const userInfoCacheKey = '__avatar_wx_user_info';

// 透传给微信的state结构，微信重定向时会带回来
interface IPassthroughState {
  // 真实回调地址
  redirectUrl: string;
}
export class WechatLogin extends BaseLogin<'wx', IWechatOAuth> {
  protected wechatOAuth: WechatOAuth;
  protected domain = 'pay.qq.com';
  protected code = '';
  public constructor(type: Extract<LoginTypes, 'wx'>, options: IWechatOAuth) {
    super(type, options);
    this.wechatOAuth = new WechatOAuth({
      domain: this.domain,
    });
  }
  public async logout(): Promise<undefined> {
    await this.clearTickInfo();
  }

  public async getLoginUrl(options: {
    redirectUrl: string;
    proxyQuery?: Record<string, string>;
    useProxy?: boolean;
  }): Promise<string> {
    const { redirectUrl, proxyQuery, useProxy } = options;
    let realRedirectUrl = '';
    const state: IPassthroughState = {
      redirectUrl,
    };
    if (useProxy) {
      realRedirectUrl = this.getLoginRedirectUrl({
        proxyQuery,
      });
    } else {
      realRedirectUrl = this.getLoginRedirectUrl({
        redirectUrl,
      });
      state.redirectUrl = '';
    }
    const ru = new URL(realRedirectUrl);
    if (ru.hash.indexOf('?') === -1) {
      ru.hash = `${ru.hash}?`;
    }
    realRedirectUrl = ru.toString();
    realRedirectUrl = delParam(['code', 'state'], realRedirectUrl);
    const loginUrl =
      `//open.weixin.qq.com/connect/qrconnect?appid=${
        this.wechatOAuth.wxAppid
      }&redirect_uri=${encodeURIComponent(
        realRedirectUrl
      )}&response_type=code` +
      `&scope=${this.options.scope || ''}&state=${btoa(
        JSON.stringify(state)
      )}#wechat_redirect`;
    await this.clearTickInfo();
    cookies.del(this.wechatOAuth.refreshTokenKey, {
      time: -3600000,
      domain: 'qq.com',
    });
    clearLocalStorage(userInfoCacheKey);
    return loginUrl;
  }
  public async getUserInfo(options?: {
    noCache?: boolean;
  }): Promise<ILoginUserInfo | undefined> {
    // 获取票据
    const ticketInfo = await this.getTickInfo();
    if (!this.code && !(await this.checkTicketExists())) throwNeedLogin();
    // const cachedUserInfo: ILoginUserInfo =
    //   getLocalStorageWithExpiry(userInfoCacheKey);
    // if (!options?.noCache && cachedUserInfo) return cachedUserInfo;
    const result = await this.innerGetUserInfo({
      code: this.code || '',
      refreshToken: ticketInfo?.data.refreshToken || '',
    });

    if (result.resultcode !== 0) {
      switch (result.resultcode) {
        // refresh token 失效
        case 1181:
          this.clearTickInfo();
          throw new TicketExpiredError(LoginTypeEnum.WX);
        default:
          throwNeedLogin();
      }
    }

    const loginUserInfo = resolveLoginUserInfoFromWx(result);

    /**
     * {
    "resultcode": 0,
    "resultinfo": {
        "errmsg": "",
        "list": [],
        "obj": {
            "openid": "oNAqP6LoVsyeCmIaVZYc6Xc9D2Jc",
            "nickname": "CatalystDP",
            "sex": 0,
            "language": "",
            "city": "",
            "province": "",
            "country": "",
            "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKTnMnBvvX2BK34ppxXzF0narxhetVZXtyEicwrJwNwpFJiaLu1IPHC5Q0giafbEIZP5tu5bIJetLJBg/132",
            "privilege": [],
            "unionid": "ouKRS6fjj0JggNiFNfpPiBMozZbo",
            "access_token": "81_dmv5jBsTbSeh6QJmP761f-cm0vodFjyiVwy81CLoIFREVk6ERExY-d6dNPIcGTkYmv3fPbx1_EUYLXPLTecR4lyeR6cLBjZNl-quF7ZpTdA",
            "refresh_token": "81_e51qdAHm-Zh6uaTd11YVn6rE7hkfBK2a1ygG_xW1BU1D61VgJALnDX07EEX-4IqB3zbTuYo9XIJOp_7aDBb4LiooaPaq3hiM2awSjaREMuE",
            "expires_in": 7200,
            "scope": "snsapi_login"
        }
    }
}
     */
    return loginUserInfo;
  }
  public async checkTicketExists(): Promise<boolean> {
    return !!(await this.getTickInfo())?.data?.refreshToken;
  }
  // 获取登录回调信息
  public async getLoginCallbackInfo(): Promise<ILoginCallbackInfo> {
    // 这里要起
    let code = '';
    let refreshToken = '';
    const paramsCode = getParam('code');
    const state = getParam('state');
    // const cookieRefreshToken = cookies.get(this.wechatOAuth.refreshTokenKey);
    const cookieRefreshToken =
      (await this.getTickInfo())?.data?.refreshToken || '';
    let parsedState: IPassthroughState | null = null;
    try {
      parsedState = JSON.parse(atob(state));
    } catch (e) {}
    if (!parsedState) throwNeedLogin();
    if (paramsCode) {
      code = paramsCode;
      this.code = code;
    } else if (cookieRefreshToken) {
      refreshToken = cookieRefreshToken;
    }
    if (!code && !refreshToken) {
      // return this.oAuth(scope);
      throwNeedLogin();
    }
    const [err, result] = await to(
      this.innerGetUserInfo({
        code,
        refreshToken,
      })
    );
    if (err || !result) {
      throwNeedLogin();
      return {
        redirectUrl: '',
        loginUserInfo: null,
      };
    }

    if (!result.resultcode) {
      const baseInfo = result.resultinfo.obj;

      // 增加授权类型
      if (
        baseInfo.scope.indexOf(WechatOAuthScopeType.snsapi_userinfo) > -1 &&
        baseInfo.scope.indexOf(WechatOAuthScopeType.snsapi_base) === -1
      ) {
        baseInfo.scope = `${baseInfo.scope},${WechatOAuthScopeType.snsapi_base}`;
      }

      // 无 userinfo 授权跳转
      if (
        this.options.scope === WechatOAuthScopeType.snsapi_userinfo &&
        baseInfo.scope.indexOf(WechatOAuthScopeType.snsapi_userinfo) === -1
      ) {
        throw new NeedLoginError(LoginTypeEnum.WX);
      }

      // 有授权则获取用户信息
      if (this.options.scope === WechatOAuthScopeType.snsapi_userinfo) {
        // const userInfo = await getWechatUserInfo({
        //   openid: baseInfo.openid,
        //   accessToken: baseInfo.access_token,
        //   wxAppid: this.wechatOAuth.wxAppid,
        // });
        // baseInfo = {
        //   ...userInfo,
        //   ...baseInfo,
        // };
      }
      const refreshToken = result?.resultinfo.obj?.refresh_token || '';
      if (paramsCode && state) {
        history.replaceState(
          {},
          '',
          delParam(['code', 'state'], location.href)
        );
      }

      await this.setTicketInfo(
        {
          type: 'wx',
          data: {
            refreshToken: refreshToken || '',
          },
        },
        24 * 3600 * 1000
      );

      const loginUserInfo = resolveLoginUserInfoFromWx(result);
      setLocalStorageWithExpiry(userInfoCacheKey, loginUserInfo, 10 * 60);

      // 种 refreshToken

      return {
        redirectUrl: parsedState?.redirectUrl || '',
        loginUserInfo,
      };
    }
    // 清除错误 refreshToken
    if (cookieRefreshToken) {
      await this.clearTickInfo();
    }

    // 授权过期重新授权
    if (result.resultcode === 12271 || result.resultcode === 1181) {
      throwNeedLogin();
    }
    throwNeedLogin();
    // dead code
    return {
      redirectUrl: '',
      loginUserInfo: null,
    };
  }

  protected async innerGetUserInfo(options: {
    code: string;
    refreshToken: string;
  }): Promise<IWechatUserInfoResp> {
    const { code, refreshToken } = options;
    const result: IWechatUserInfoResp = await getWechatAuthInfo({
      code: code || '',
      refreshToken: refreshToken || '',
      wxAppid: this.wechatOAuth.wxAppid,
      get_detail:
        this.options.scope === WechatOAuthScopeType.snsapi_login ? '1' : '',
    });
    return result;
  }

  // eslint-disable-next-line class-methods-use-this
  protected throwNeedLogin() {
    throw new NeedLoginError(LoginTypeEnum.WX);
  }

  protected async setTicketInfo(
    tickInfo: ILoginTicketInfo<'wx', { refreshToken: string }>,
    ttl: number
  ): Promise<void> {
    cookies.set(this.wechatOAuth.refreshTokenKey, tickInfo.data.refreshToken, {
      domain: this.domain,
      time: ttl,
    });
  }

  protected async getTickInfo(): Promise<ILoginTicketInfo<
    'wx',
    { refreshToken: string }
  > | null> {
    const refreshToken = cookies.get(this.wechatOAuth.refreshTokenKey);
    return {
      type: 'wx',
      data: {
        refreshToken,
      },
    };
  }
  public async clearTickInfo(): Promise<void> {
    cookies.del(this.wechatOAuth.refreshTokenKey, {
      time: -3600000,
      domain: this.domain,
    });

    // 将 qq.com 中的也删除
    cookies.del(this.wechatOAuth.refreshTokenKey, {
      time: -3600000,
      domain: 'qq.com',
    });
  }
}
