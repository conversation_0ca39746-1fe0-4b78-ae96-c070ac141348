import {
  COS_CONFIG,
  MATERIAL_TYPE,
  UPLOAD_CODE,
  UPLOAD_FORM_CONFIG,
} from '@/configs/upload';
import {
  CreateMediaJobs,
  DescribeMediaJobs,
  GetMediaInfo,
} from '@/pb/api/MetaFeedbackSvr_1';
import { ReqType } from '@/pb/config';
import { CosStoreSvr, MaterialSvr } from '@/pb/pb';
import {
  getLocalStorageWithExpiry,
  setLocalStorageWithExpiry,
} from '@/utils/localStorage';
import { to } from 'await-to-js';
import Cos, {
  CosError,
  GetAuthorizationCallbackParams,
  GetAuthorizationOptions,
  SliceUploadFileResult,
} from 'cos-js-sdk-v5';
import { resolve } from 'path';
import SparkMD5 from 'spark-md5';
import * as path from 'path-browserify';
import { createLogger } from './log';

const logger = createLogger('utils/cos');
export type FileRootType = ReqType<
  typeof MaterialSvr.GetTmpCOSSecretKey
>['file_root_type'];

export interface cosSecretKey {
  tmp_secret_id: string;
  tmp_secret_key: string;
  session_token: string;
  upload_path: string;
}
export interface UploadFilesParams {
  bucket: string;
  region: string;
  name: string;
  file: File | Blob;
  onProgress?: (progress: Progress) => void;
  // 需要执行转码前进行回调
  onBeforeTranscode?: (data: {
    type: MATERIAL_TYPE;
    inputPath: string;
    codec: string;
  }) => void;
  onTranscodeProgress?: (data: {
    type: MATERIAL_TYPE;
    progress: number;
    inputPath: string;
    outputPath: string;
  }) => void;
  onTaskReady?: (taskId: string, cos: any) => void;
  // onTaskStart?: (params: CosTaskStart) => void;
  onBackError?: (error: any) => void;
  onBackResult?: (res: UploadResponseProps) => void;
  getTask?: (taskId: any, cos: any) => void;
  cosSecretKey?: cosSecretKey;
  cosFullUrl?: string;
  /**
   * cos 文件以 下载方式而不是在浏览器中展示
   */
  disposition?: boolean;
  protocol?: 'https:' | 'http:';
  abortSignal?: AbortSignal;
}

export interface UploadResponseProps {
  key: string;
  url: string;
  code: number | undefined;
}
export interface CosTaskStart {
  id: string;
  Bucket: string;
  Region: string;
  Key: string;
  state: string;
  loaded: number;
  size: number;
  speed: number;
  percent: number;
  hashPercent: number;
}
export interface Progress {
  loaded: number;
  total: number;
  speed: number;
  percent: number;
}

export type UploadRequestProps = (
  type: MATERIAL_TYPE,
  file: File,
  index: number,
  getTask: (taskId: any, cos: any) => void,
  options?: {
    fileName?: string;
  } & Pick<
    UploadFilesParams,
    | 'onProgress'
    | 'disposition'
    | 'protocol'
    | 'onBeforeTranscode'
    | 'onTranscodeProgress'
    | 'abortSignal'
  >
) => Promise<UploadResponseProps>;

const getTemporaryKey = async (options: {
  uploadPath: string;
}): Promise<
  Pick<
    CosStoreSvr.GetTemporaryKeyResponse['data'],
    | 'cos_url'
    | 'session_token'
    | 'tmp_secret_id'
    | 'tmp_secret_key'
    | 'upload_path'
  >
> => {
  const { uploadPath } = options;
  const cosSecretKeyCache = getLocalStorageWithExpiry('COSSecretKey');
  if (cosSecretKeyCache) return cosSecretKeyCache;
  return CosStoreSvr.GetTemporaryKey({
    upload_path: uploadPath,
  }).then((res) => {
    setLocalStorageWithExpiry('COSSecretKey', res, 60 * 60);
    return res;
  });
};

/**
 * @param filePath cos上文件路径不需要以 / 开头
 */
export const checkVideoNeedTranscode = async (
  filePath: string
): Promise<{
  needTranscode: boolean;
  codec?: string;
}> => {
  const res = await GetMediaInfo({
    object: filePath,
  });
  if (!res.result)
    return {
      needTranscode: false,
    };
  const [video] = res.result?.media_info?.stream?.video || [];
  if (!video) return { needTranscode: false };
  logger.info('checkVideoNeedTranscode', video);
  const { codec_long_name } = video;
  if (!codec_long_name.toLowerCase().includes('h.264'))
    return {
      needTranscode: true,
      codec: codec_long_name,
    };
  return {
    needTranscode: false,
  };
  // if(!video) throw new
};

/**
 * 
[utils/cos] -  ["before transcodeVideo path obj ",{"root":"","dir":"material","base":"84118cda88246e49a5c1ba22e35bfe73.mp4","ext":".mp4","name":"84118cda88246e49a5c1ba22e35bfe73"}]
 */
export const transcodeVideo = async (
  options: {
    inputFilePath: string;
  } & Pick<UploadFilesParams, 'onTranscodeProgress' | 'abortSignal'>
): Promise<{
  outputFilePath: string;
}> => {
  const { inputFilePath, abortSignal } = options;
  const pathObj = path.parse(inputFilePath);
  logger.info('before transcodeVideo path obj ', pathObj);
  const { dir, name } = pathObj;
  const outputFilePath = `${dir}/${name}_h264${pathObj.ext}`;
  const { result } = await CreateMediaJobs({
    options: {
      input: {
        object: inputFilePath,
      },
      operation: {
        output: {
          object: outputFilePath,
        },
        template_id: import.meta.env.VITE_COS_TRANSCODE_VIDEO_TEMPLATE_ID,
      },
      queue_type: 'SpeedTranscoding',
      tag: 'Transcode',
    },
  });
  abortSignal?.throwIfAborted();
  const jobId = result?.jobs_detail?.job_id;
  if (!jobId) throw new Error('获取转码任务id失败');
  const waitForMediaTranspile = async () => {
    return new Promise((resolve, reject) => {
      let errCount = 0;
      const pollingJobs = async () => {
        const [err, resp] = await to(
          DescribeMediaJobs({
            job_id: jobId,
          })
        );
        if (abortSignal?.aborted) {
          reject(new Error(`转码任务取消 user abort`));
          return;
        }
        if (err) {
          errCount += 1;
          if (errCount > 10) {
            reject(new Error(`转码任务失败: ${err.message}`));
            return;
          }
        }
        if (!resp?.result) {
          reject(new Error(`转码任务查询失败`));
          return;
        }
        if (resp) {
          const {
            result: { jobs_detail },
          } = resp;
          if (jobs_detail.state === 'Success') {
            resolve({
              outputFilePath: jobs_detail.operation.output.object,
            });
            return;
          }
          if (jobs_detail.state === 'Failed') {
            reject(new Error(`转码任务失败: ${jobs_detail.message}`));
            return;
          }
          if (jobs_detail.state === 'Cancel') {
            reject(new Error(`转码任务取消`));
            return;
          }
          if (jobs_detail.state === 'Running') {
            options.onTranscodeProgress?.({
              type: MATERIAL_TYPE.VIDEO,
              inputPath: inputFilePath,
              outputPath: outputFilePath,
              progress: parseInt(jobs_detail.progress, 10),
            });
          }
        }
        setTimeout(() => {
          pollingJobs();
        }, 5000);
      };
      pollingJobs();
    });
  };
  await waitForMediaTranspile();
  return {
    outputFilePath,
  };
  // await TranscodeVideo({
  //   input: inputFilePath,
  //   output: outputFilePath,
  // });
  // return { outputFilePath };
};

export const uploadRequest: UploadRequestProps = (
  type,
  file,
  index,
  getTask,
  options
) => {
  return new Promise((resolve, reject) => {
    const fileType = file.name.includes('.')
      ? file.name.split('.').pop()?.toLowerCase()
      : '';
    console.time('time-cost-of-md5');
    const namePromise = options?.fileName
      ? Promise.resolve(options.fileName)
      : fileMD5(file, 2097152);
    // 分块大小设置为2M
    namePromise
      .then((fileName) => {
        console.timeEnd('time-cost-of-md5');
        // 把 getTask 透传到 调用 cos 的方法中
        return uploadFileRequest({
          // bucket 和 region 会在queryCosParams方法中获取
          bucket: '',
          region: '',
          name: `${fileName}.${fileType}`,
          file,
          getTask,
          onProgress: options?.onProgress,
          disposition: options?.disposition,
          protocol: options?.protocol,
          onBeforeTranscode: options?.onBeforeTranscode,
          onTranscodeProgress: options?.onTranscodeProgress,
          abortSignal: options?.abortSignal,
        });
      })
      .then((res) => {
        const { code = UPLOAD_CODE.ERROR } = res || {};
        if (code === UPLOAD_CODE.SUCCESS) {
          // 把上传资源的cos_url换成cdn_url
          res.url = res.url.replace(
            COS_CONFIG.cosBaseUrl,
            COS_CONFIG.cdnBaseUrl
          );
          resolve(res);
        } else {
          reject(new Error(`上传失败, 状态码为 ${code}`));
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const withPostUpload = async (
  promise: Promise<UploadResponseProps>,
  signedParams: Pick<
    CosStoreSvr.GetTemporaryKeyResponse['data'],
    | 'cos_url'
    | 'session_token'
    | 'tmp_secret_id'
    | 'tmp_secret_key'
    | 'upload_path'
  >,
  options: {
    isVideo: boolean;
  } & Pick<
    UploadFilesParams,
    'onBeforeTranscode' | 'onTranscodeProgress' | 'abortSignal'
  >
): Promise<UploadResponseProps> => {
  return promise.then(async (res) => {
    // 判断是否需要转码
    if (!options.isVideo) return res;
    const { abortSignal } = options;
    logger.info('withPostUpload get video res', res);
    const inputFilePath = res.key.replace(/^\//, '');
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [checkErr, checkResult] = await to(
      checkVideoNeedTranscode(res.key.replace(/^\//, ''))
    );
    abortSignal?.throwIfAborted();
    if (!checkErr) {
      if (checkResult.needTranscode) {
        options?.onBeforeTranscode?.({
          codec: checkResult.codec as string,
          inputPath: res.key,
          type: MATERIAL_TYPE.VIDEO,
        });
        // 需要进行转码
        const [err, result] = await to(
          transcodeVideo({
            inputFilePath,
            onTranscodeProgress: options?.onTranscodeProgress,
            abortSignal,
          })
        );
        abortSignal?.throwIfAborted();
        if (err) throw err;
        const { outputFilePath } = result;
        res.key = `/${outputFilePath}`;
        res.url = res.url.replace(inputFilePath, outputFilePath);
      }
    }
    return res;
  });
};

// 上传文件
export async function upload(
  params: UploadFilesParams
): Promise<UploadResponseProps> {
  return new Promise<UploadResponseProps>((resolve, reject) => {
    const {
      bucket,
      region,
      name,
      file,
      onProgress,
      onTaskReady,
      // onTaskStart,
      onBackError,
      onBackResult,
      getTask,
      cosSecretKey,
      cosFullUrl,
      protocol,
      abortSignal,
    } = params;
    console.log(bucket, region, 'bucket,region');
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { tmp_secret_id, tmp_secret_key, session_token, upload_path } =
      cosSecretKey as cosSecretKey;
    const startTime = Math.floor(new Date().getTime() / 1000);
    const expiredTime = startTime + 900;
    const cos = new Cos({
      Domain:
        cosFullUrl?.replace(/^\/\//, '') ||
        `${bucket}.cos-internal.${region}.tencentcos.cn`, // 内网域名
      getAuthorization: (
        options: GetAuthorizationOptions,
        callback: (params: GetAuthorizationCallbackParams) => unknown
      ) => {
        callback({
          TmpSecretId: tmp_secret_id,
          TmpSecretKey: tmp_secret_key,
          XCosSecurityToken: session_token,
          StartTime: startTime, // 时间戳，单位秒，如：1580000000
          ExpiredTime: expiredTime, // 时间戳，单位秒，如：1580000900
          ScopeLimit: true,
        });
      },
    });
    // NOTE: 返回值 key拼接
    const uploadPath =
      upload_path.split('')[upload_path.length - 1] === '/'
        ? upload_path
        : `${upload_path}/`;
    const key = `${uploadPath}${name}`;
    let taskId = '';
    abortSignal?.addEventListener('abort', () => {
      if (taskId) {
        cos.cancelTask(taskId);
      }
      reject(new Error('上传任务已取消'));
    });
    /**  putObject */
    cos.sliceUploadFile(
      {
        Bucket: bucket,
        Region: region,
        Key: key,
        StorageClass: 'MAZ_STANDARD',
        Body: file,
        ContentDisposition: 'attachment',
        onTaskReady: (_taskId: string) => {
          taskId = _taskId;
          if (abortSignal?.aborted) {
            cos.cancelTask(taskId);
          } else {
            onTaskReady?.(_taskId, cos);
            // NOTE: 在这里调用getTask, 入参 taskId 和 cos
            getTask?.(_taskId, cos);
          }
        },
        onProgress: (progress: Progress) => {
          onProgress?.(progress);
        },
      },
      (err: CosError, data: SliceUploadFileResult) => {
        if (err) {
          onBackError?.(err);
          reject(err);
        } else {
          const { Location, statusCode } = data;
          // NOTE: Promise<UploadResponseProps> 返回值组合
          const result = {
            key,
            url: /(^(http))|(^\/\/)|(^(https))/.test(Location)
              ? Location
              : `${protocol ? `${protocol}//${Location}` : `//${Location}`}`,
            code: statusCode,
          };
          onBackResult?.(result);
          resolve(result);
        }
      }
    );
  });
}

/** NOTE: 在 uploadRequest 方法中 把 getTask 透传到 uploadFileRequest 方法中 */
// 获取COS临时秘钥后上传文件
// "https://pagedoo-dman-dev-1258344706.cos-internal.ap-guangzhou.tencentcos.cn"
async function uploadFileRequest(
  params: UploadFilesParams,
  uploadPath = '/material'
): Promise<UploadResponseProps> {
  const isVideo = UPLOAD_FORM_CONFIG[MATERIAL_TYPE.VIDEO].accept.some((ext) =>
    params.name.endsWith(ext)
  );

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { tmp_secret_id, tmp_secret_key, session_token, upload_path, cos_url } =
    await getTemporaryKey({ uploadPath });
  params.abortSignal?.throwIfAborted();
  return withPostUpload(
    upload({
      ...params,
      ...queryCosParams(cos_url),
      cosFullUrl: COS_CONFIG.cosBaseUrl,
      cosSecretKey: {
        tmp_secret_id,
        tmp_secret_key,
        session_token,
        upload_path,
      },
    }),
    {
      cos_url,
      tmp_secret_id,
      tmp_secret_key,
      session_token,
      upload_path,
    },
    {
      isVideo,
      onBeforeTranscode: params.onBeforeTranscode,
      onTranscodeProgress: params.onTranscodeProgress,
      abortSignal: params.abortSignal,
    }
  );
}

export const queryCosParams = (
  cosUrl: string
): { bucket: string; region: string } => {
  // 截取//后面的内容
  const cos = cosUrl.split('//')[1] ?? '';
  //  获取pagedoo-dev-1258344706
  const bucket = cos.split('.')[0] ?? '';
  //   获取ap-guangzhou
  const region = cos.split('.')[2] ?? '';
  return { bucket, region };
};

// 格式化文件大小
const formatFileSize = (fileSize: number): string => {
  if (fileSize < 1024) {
    return `${fileSize}B`;
  }
  if (fileSize < 1024 * 1024) {
    const temp = fileSize / 1024;
    return `${temp.toFixed(2)}KB`;
  }
  if (fileSize < 1024 * 1024 * 1024) {
    const temp = fileSize / (1024 * 1024);
    return `${temp.toFixed(2)}MB`;
  }
  const temp = fileSize / (1024 * 1024 * 1024);
  return `${temp.toFixed(2)}GB`;
};

// 格式化时长
const formatSecond = (second: number, type: 1 | 2): string => {
  if (type === 1) {
    const h = Math.floor((second / 3600) % 24);
    const m = Math.floor((second / 60) % 60);
    const s = Math.floor(second % 60);
    let result = '';
    result = `${s}秒`;
    if (m > 0) {
      result = `${m}分${result}`;
    }
    if (h > 0) {
      result = `${h}时${result}`;
    }
    return result;
  }
  if (type === 2) {
    const m = Math.floor(second / 60);
    const s = Math.floor(second % 60);
    return `${m < 10 ? `0${m}` : m}:${s < 10 ? `0${s}` : s}`;
  }
  return '';
};

/**
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
const fileMD5 = (file: File | Blob, chunkSize: number): Promise<string> => {
  return new Promise((resolve, reject) => {
    const blobSlice = File.prototype.slice;
    const chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();

    fileReader.onload = function (e) {
      spark.append(e.target?.result as ArrayBuffer);
      currentChunk += 1;
      if (currentChunk < chunks) {
        loadNext();
      } else {
        const md5 = spark.end();
        resolve(md5);
      }
    };

    fileReader.onerror = function (e) {
      reject(e);
    };

    function loadNext(): void {
      const start = currentChunk * chunkSize;
      let end = start + chunkSize;
      if (end > file.size) {
        end = file.size;
      }
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }
    loadNext();
  });
};

// 最大公约数(辗转相除法)
const gcd = (a: number, b: number): number => {
  if (b === 0) {
    return a;
  }
  const r = a % b;
  return gcd(b, r);
};

// 格式化宽高比
const formatRadio = (a: number, b: number): string => {
  a = Math.floor(a);
  b = Math.floor(b);
  const gys = gcd(a, b);
  return `${a / gys}:${b / gys}`;
};

// 返回base64字符串的文件格式；
const returnBase64ImageType = (dataurl: string) => {
  const arr = dataurl.split(',');
  const dataInfo = arr[0].match(/data:(.*?);/);
  if (!arr || !dataInfo || !dataInfo[1]) {
    throw new Error('生成缩略图发生错误');
  } else {
    return dataInfo[1].split('/')[1];
  }
};

const dataURLtoBlob: (s: string) => Blob = (dataurl) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)![1];
  const bstr = window.atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n) {
    n -= 1;
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
};

// 用COS的缩略图能力
const getThumbnailUrl = (url: string, width = 120, height = 120): string => {
  // gif压缩后会出图片静止等情况，暂不做缩略图
  return url.split('.').pop()?.toLowerCase() === 'gif'
    ? url
    : `${url}?imageMogr2/thumbnail/${width}x${height}`;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
// const getCosObject = async (uploadPath: any) => {
//   let cosSecretKeyCache = getLocalStorageWithExpiry('COSSecretKey-object');

//   if (!cosSecretKeyCache) {
//     const res = await CosStoreSvr.GetTemporaryKey({ upload_path: uploadPath });
//     cosSecretKeyCache = res;
//     setLocalStorageWithExpiry(
//       'COSSecretKey-object',
//       cosSecretKeyCache,
//       60 * 60
//     );
//   }

//   const { tmp_secret_id, tmp_secret_key, session_token, cos_url } =
//     cosSecretKeyCache;

//   const { bucket, region } = queryCosParams(cos_url);
//   // eslint-disable-next-line @typescript-eslint/naming-convention
//   const startTime = Math.floor(new Date().getTime() / 1000);
//   const expiredTime = startTime + 900;
//   // 获取 key
//   const cos = new Cos({
//     // Domain: `${bucket}.cos-internal.${region}.tencentcos.cn`, // 内网域名
//     getAuthorization: (
//       options: GetAuthorizationOptions,
//       callback: (params: GetAuthorizationCallbackParams) => unknown
//     ) => {
//       callback({
//         TmpSecretId: tmp_secret_id,
//         TmpSecretKey: tmp_secret_key,
//         XCosSecurityToken: session_token,
//         StartTime: startTime, // 时间戳，单位秒，如：1580000000
//         ExpiredTime: expiredTime, // 时间戳，单位秒，如：1580000900
//         ScopeLimit: true,
//       });
//     },
//   });

//   const object = await cos.getObject({
//     Bucket: bucket,
//     Region: region,
//     Key: '111',
//   });
//   return object;
// };

export {
  dataURLtoBlob,
  fileMD5,
  formatFileSize,
  formatRadio,
  formatSecond,
  getThumbnailUrl,
  returnBase64ImageType,
  uploadFileRequest,
};
