/**
 * <AUTHOR>
 * @date 2024/10/28 14:41
 * @desc electron 相关方法
 */

/**
 * 判断是否在客户端运行
 */
export const runningInClient = !!window?.avatarClientBridge;

/**
 * 判断客户端是否有直播正在运行
 */
export const hasRunningLive = async () => {
  if (!runningInClient) return false;
  const list = (await window.avatarClientBridge?.pushTask.listPushTask()) || [];
  return list.length > 0;
};

export const isInClientEnv = () => {
  return (
    runningInClient ||
    navigator.userAgent.includes('@tencent/pagedoo-avatar-obs')
  );
};
