module.exports = {
  root: true,
  extends: ['@tencent', 'plugin:jsx-plus/recommended'],
  plugins: ['jsx-plus'],
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      extends: [
        '@tencent/prettier-typescript-react',
        '@tencent/react/lib/hooks',
      ],
      rules: {
        '@typescript-eslint/ban-types': 'warn',
        'max-len': 'warn',
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/naming-convention': 'off',
        'react/destructuring-assignment': 'off',
        'jsx-a11y/no-static-element-interactions': 'off',
        'jsx-a11y/media-has-caption': 'off',
        'jsx-a11y/click-events-have-key-events': 'off',
        '@typescript-eslint/no-misused-promises': 'off',
        'react/jsx-props-no-spreading': 'off',
        'jsx-a11y/no-noninteractive-element-interactions': 'off',
        'react/button-has-type': 'off',
        'react/no-unused-prop-types': 'off',
        'jsx-a11y/anchor-is-valid': 'off',
        'react/jsx-no-script-url': 'off',
        'react/jsx-no-useless-fragment': 'off',
        'react/react-in-jsx-scope': 'off',
        'react/jsx-uses-react': 'off',
        'react/no-unstable-nested-components': 'off',
        'react/no-unknown-property': [
          'warn',
          {
            ignore: ['css', 'x-if', 'x-else', 'x-for', 'x-slot:\\w+', 'x-class'],
          },
        ],
        'react/jsx-no-bind': 'off',
        'react/require-default-props': 'off',
        'react/prop-types': 'off',
        '@typescript-eslint/member-ordering': 'off',
        'react/jsx-pascal-case': 'off',
        '@typescript-eslint/no-unused-vars': 'warn',
        'jsx-a11y/control-has-associated-label': 'off',
        '@typescript-eslint/consistent-type-assertions': 'warn',
      },
    },
  ],
};
