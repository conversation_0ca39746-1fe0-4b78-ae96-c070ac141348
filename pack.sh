#!/bin/bash
echo "Usage: pack.sh [options]"
echo "Options:"
echo "  --env     Set the environment, 'DEV|CI|PROD|OTHER' default is DEV."

# 蓝盾的NODE版本
# 10.10.0  10.16.3  12.16.1  14.13.1  14.19.1  16.13.0  16.15.0  18.12.0  18.17.1  20.15.0  6.6.0   8.12.0  8.9.3  9.7.1
# 10.11.0  12.13.1  12.20.2  14.15.0  15.14.0  16.14.2  17.9.0   18.16.1  18.20.2  20.18.0  7.10.1  8.9.0   8.9.4
export NODE_VER=18.20.2

# 环境取值
# DEV CI PROD OTHER
export BUILD_ENV="DEV"

# 判断目录是否存在，存则表示在蓝盾构建机
if [ -d /data/bkdevops/apps/nodejs/$NODE_VER ]; then
    export PATH=/data/bkdevops/apps/nodejs/$NODE_VER/bin:$PATH
fi

while (( "$#" )); do
    case "$1" in
    --env)
        case $2 in
        'DEV'|'CI'|'OTHER'|'PROD')
            export BUILD_ENV=$2
            ;;
        *)
            echo "--env $2 is invalid, allowed: DEV|CI|OTHER"
            exit 1
            ;;
        esac
        shift 2
        ;;
    --)
      shift;break
      ;;
    *)
      echo "pack.sh Invalid argument"
      exit 1
      ;;
  esac
done

export build_env=${BUILD_ENV,,}
echo "BUILD_ENV:$BUILD_ENV, build_env:$build_env"


yarn install -y --ignore-engines
export NODE_OPTIONS=--max-old-space-size=8192

echo "===start build==="

if [[ "${ZHIYAN_PKG_VERSION_SUFFIX}" == "PAGEDOO" ]]; then
  echo "======= 构建产品：页匠 ======="
  if [[ "$BUILD_ENV" == "PROD" ]]; then
    echo "CMD: yarn build"
    yarn build
  elif [[ "$BUILD_ENV" == "DEV" ]]; then
    echo "CMD: yarn build:dev"
    yarn build:dev
  elif [[ "$BUILD_ENV" == "CI" ]]; then
    yarn build:dev
  elif [[ "$BUILD_ENV" == "OTHER" ]]; then
    yarn build:dev
  fi
elif [[ "${ZHIYAN_PKG_VERSION_SUFFIX}" == "AD" ]]; then
  echo "======= 构建产品：广告 ======="
  if [[ "$BUILD_ENV" == "PROD" ]]; then
    echo "CMD: yarn build-ad"
    yarn build
  elif [[ "$BUILD_ENV" == "DEV" ]]; then
    echo "CMD: yarn build:dev-ad"
    yarn build:dev-ad
  elif [[ "$BUILD_ENV" == "CI" ]]; then
    npx vite build --mode dev-ad --base //dev-avatarcdn.pay.qq.com/intelligent/live/prepage/
  elif [[ "$BUILD_ENV" == "OTHER" ]]; then
    yarn build:dev
  fi
else
  echo "======= 未知的产品类型 ======="
  exit 1
fi

echo "=== end of build==="

# 如果有sourcemaps
# mkdir sourcemaps
# date > sourcemaps/xxx.map
