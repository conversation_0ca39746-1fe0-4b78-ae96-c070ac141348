# 页匠数字人直播

## 依赖安装
    yarn

## 开发

    yarn dev-qa

## 配置 whistle 规则

    执行 w2 add

## 构建

    推送对应流水线

### 其他

1. **技术栈**

- React

2. **状态管理**
- recoil

3. **构建**
- vite

4. **样式库**
- [tdesign](https://tdesign.woa.com/react/overview)
- [tailwind](https://www.tailwindcss.cn/)



### 通用代理
```bash
127.0.0.1:8090 avatar.pay.qq.com excludeFilter://*/cgi-bin excludeFilter://*/api
127.0.0.1:8090 dev-avatar.pay.qq.com excludeFilter://*/cgi-bin excludeFilter://*/api excludeFilter://*/intelligent/live/api
wss://avatar.pay.qq.com ws://localhost:8090
avatar.pay.qq.com/cgi-bin/  resCors://*
avatar.pay.qq.com/api/ resCors://enable
dev-avatar.pay.qq.com/ resCors://enable
dev-avatar-inner.pay.qq.com/ resCors://*
avatar-inner.pay.qq.com/ resCors://enable
http://************* statusCode://200 resCors://* includeFilter://m:OPTIONS
http://*************  resCors://*
avatar.pay.qq.com/dman/preview.html localhost:8090/dman/preview.html
avatar.pay.qq.com/material/ ignore://*
avatar.pay.qq.com/hy/ ignore://*
```

### 特定环境构建
#### 妙思
- 本地开发
```bash
VITE_ENV=dev VITE_UPLOAD_COS_BASE_URL=//pagedoo-dman-dev-1258344706.cos-internal.ap-guangzhou.tencentcos.cn VITE_UPLOAD_CDN_BASE_URL=//dev-avatarcdn.pay.qq.com npx vite --mode production-ad --base https://admuse.qq.com/intelligent/live/page
```
- 生产环境
```bash
VITE_ENV=dev npx vite --mode production-ad --base https://admuse.qq.com/intelligent/live/page --force
```
对应代理
```bash
127.0.0.1:8090 admuse.qq.com includeFilter://*/intelligent/live/page
/^https://admuse.qq.com(.*)/ http://127.0.0.1:5678$1 excludeFilter://*/intelligent/live/api excludeFilter://*/intelligent/live/page excludeFilter://*/intelligent/api/

## 请求代理到dev环境
/api/chattts/api/v1/aigc/chattts(.*)/ https://dev-avatar.pay.qq.com/intelligent/live/api/chattts/api/v1/aigc/chattts$1
/intelligent/live/api/chattts(.*)/ https://dev-avatar.pay.qq.com/intelligent/live/api/chattts$1
/intelligent/live/api/logout(.*)/  https://dev-avatar.pay.qq.com/intelligent/live/api/logout$1
/intelligent/live/api(.*)/ https://dev-avatar.pay.qq.com/api$1

## 页面 + 请求代理到dev 环境（测试使用）

/^https://admuse.qq.com(.*)/ dev-avatar.pay.qq.com$1
/^https://avatarcdnams.pay.qq.com(.*)/ dev-avatarcdn.pay.qq.com$1

### 仅代理页面到devavatarcdn.pay.qq.com
# /^https://admuse.qq.com(.*)/ dev-avatar.pay.qq.com$1 includeFilter://*/intelligent/live/page/
```



