# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  version "1.2.6"
  resolved "https://mirrors.tencent.com/npm/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz"
  integrity sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://mirrors.tencent.com/npm/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/colors@^6.0.0":
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/@ant-design/colors/-/colors-6.0.0.tgz"
  integrity sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/icons-svg@^4.3.0":
  version "4.4.2"
  resolved "https://mirrors.tencent.com/npm/@ant-design/icons-svg/-/icons-svg-4.4.2.tgz"
  integrity sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==

"@ant-design/icons@^4.3.0", "@ant-design/icons@^4.7.0":
  version "4.8.3"
  resolved "https://mirrors.tencent.com/npm/@ant-design/icons/-/icons-4.8.3.tgz"
  integrity sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.3.0"
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    lodash "^4.17.15"
    rc-util "^5.9.4"

"@ant-design/react-slick@~0.29.1":
  version "0.29.2"
  resolved "https://mirrors.tencent.com/npm/@ant-design/react-slick/-/react-slick-0.29.2.tgz"
  integrity sha512-kgjtKmkGHa19FW21lHnAfyyH9AAoh35pBdcJ53rHmQ3O+cfFHGHnUbj/HFrRNJ5vIts09FKJVAD8RpaC+RaWfA==
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    lodash "^4.17.21"
    resize-observer-polyfill "^1.5.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.24.2", "@babel/code-frame@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/code-frame/-/code-frame-7.24.7.tgz"
  integrity sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==
  dependencies:
    "@babel/highlight" "^7.24.7"
    picocolors "^1.0.0"

"@babel/compat-data@^7.25.2":
  version "7.25.2"
  resolved "https://mirrors.tencent.com/npm/@babel/compat-data/-/compat-data-7.25.2.tgz"
  integrity sha512-bYcppcpKBvX4znYaPEeFau03bp89ShqNMLs+rmdptMw+heSZh9+z84d2YG+K7cYLbWwzdjtDoW/uqZmPjulClQ==

"@babel/core@^7.15.8", "@babel/core@^7.23.5":
  version "7.24.4"
  resolved "https://mirrors.tencent.com/npm/@babel/core/-/core-7.24.4.tgz"
  integrity sha512-MBVlMXP+kkl5394RBLSxxk/iLTeVGuXTV3cIDXavPpMMqnSnt6apKgan/U8O3USWZCWZT/TbgfEpKa4uMgN4Dg==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.4"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helpers" "^7.24.4"
    "@babel/parser" "^7.24.4"
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.1"
    "@babel/types" "^7.24.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/eslint-parser@^7.14.5":
  version "7.24.1"
  resolved "https://mirrors.tencent.com/npm/@babel/eslint-parser/-/eslint-parser-7.24.1.tgz"
  integrity sha512-d5guuzMlPeDfZIbpQ8+g1NaCNuAGBBGNECh0HVqz1sjOeVLh2CEaifuOysCH18URW6R7pqXINvf5PaR/dC6jLQ==
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.1"

"@babel/generator@^7.24.4", "@babel/generator@^7.25.0":
  version "7.25.0"
  resolved "https://mirrors.tencent.com/npm/@babel/generator/-/generator-7.25.0.tgz"
  integrity sha512-3LEEcj3PVW8pW2R1SR1M89g/qrYk/m/mB/tLqn7dn4sbBUQyTqnlod+II2U4dqiGtUmkcnAmkMDralTFZttRiw==
  dependencies:
    "@babel/types" "^7.25.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.22.5", "@babel/helper-annotate-as-pure@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.7.tgz"
  integrity sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-compilation-targets@^7.23.6":
  version "7.25.2"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.2.tgz"
  integrity sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==
  dependencies:
    "@babel/compat-data" "^7.25.2"
    "@babel/helper-validator-option" "^7.24.8"
    browserslist "^4.23.1"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.25.0":
  version "7.25.0"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.0.tgz"
  integrity sha512-GYM6BxeQsETc9mnct+nIIpf63SAyzvyYN7UB/IlTyd+MBg06afFGp0mIeUqGyWgS2mxad6vqbMrHVlaL3m70sQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.8"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/helper-replace-supers" "^7.25.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/traverse" "^7.25.0"
    semver "^6.3.1"

"@babel/helper-member-expression-to-functions@^7.24.8":
  version "7.24.8"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.24.8.tgz"
  integrity sha512-LABppdt+Lp/RlBxqrh4qgf1oEH/WxdzQNDJIu5gC/W1GyvPVrOBiItmmM8wan2fm4oYqFuFfkXmlGpLQhPY8CA==
  dependencies:
    "@babel/traverse" "^7.24.8"
    "@babel/types" "^7.24.8"

"@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.22.15", "@babel/helper-module-imports@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-module-imports/-/helper-module-imports-7.24.7.tgz"
  integrity sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-module-transforms@^7.23.3":
  version "7.25.2"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.25.2.tgz"
  integrity sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==
  dependencies:
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-simple-access" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"
    "@babel/traverse" "^7.25.2"

"@babel/helper-optimise-call-expression@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.24.7.tgz"
  integrity sha512-jKiTsW2xmWwxT1ixIdfXUZp+P5yURx2suzLZr5Hi64rURpDYdMW0pv+Uf17EYk2Rd428Lx4tLsnjGJzYKDM/6A==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.0", "@babel/helper-plugin-utils@^7.24.7", "@babel/helper-plugin-utils@^7.24.8":
  version "7.24.8"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.8.tgz"
  integrity sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==

"@babel/helper-replace-supers@^7.25.0":
  version "7.25.0"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.25.0.tgz"
  integrity sha512-q688zIvQVYtZu+i2PsdIu/uWGRpfxzr5WESsfpShfZECkO+d2o+WROWezCi/Q6kJ0tfPa5+pUGUlfx2HhrA3Bg==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.24.8"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/traverse" "^7.25.0"

"@babel/helper-simple-access@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-simple-access/-/helper-simple-access-7.24.7.tgz"
  integrity sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.24.7.tgz"
  integrity sha512-IO+DLT3LQUElMbpzlatRASEyQtfhSE0+m465v++3jyyXeBTBUjtVZg28/gHeV5mrTJqvEKhKroBGAvhW+qPHiQ==
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-string-parser@^7.24.8":
  version "7.24.8"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-string-parser/-/helper-string-parser-7.24.8.tgz"
  integrity sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==

"@babel/helper-validator-identifier@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.7.tgz"
  integrity sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==

"@babel/helper-validator-option@^7.23.5", "@babel/helper-validator-option@^7.24.8":
  version "7.24.8"
  resolved "https://mirrors.tencent.com/npm/@babel/helper-validator-option/-/helper-validator-option-7.24.8.tgz"
  integrity sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==

"@babel/helpers@^7.24.4":
  version "7.24.4"
  resolved "https://mirrors.tencent.com/npm/@babel/helpers/-/helpers-7.24.4.tgz"
  integrity sha512-FewdlZbSiwaVGlgT1DPANDuCHaDMiOo+D/IDYRFYjHOuv66xMSJ7fQwwODwRNAPkADIO/z1EoF/l2BCWlWABDw==
  dependencies:
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.1"
    "@babel/types" "^7.24.0"

"@babel/highlight@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/highlight/-/highlight-7.24.7.tgz"
  integrity sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.7"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.24.4", "@babel/parser@^7.25.0", "@babel/parser@^7.25.3":
  version "7.25.3"
  resolved "https://mirrors.tencent.com/npm/@babel/parser/-/parser-7.25.3.tgz"
  integrity sha512-iLTJKDbJ4hMvFPgQwwsVoxtHyWpKKPBrxkANrSYewDPaPpT5py5yeVkgPIJ7XYXhndxJpaA3PyALSXQ7u8e/Dw==
  dependencies:
    "@babel/types" "^7.25.2"

"@babel/plugin-syntax-jsx@^7.23.3":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.7.tgz"
  integrity sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-typescript@^7.24.7":
  version "7.24.7"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.7.tgz"
  integrity sha512-c/+fVeJBB0FeKsFvwytYiUD+LBvhHjGSI0g446PRGdSVGZLRNArBUno2PETbAly3tpiNAQR5XaZ+JslxkotsbA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-react-display-name@^7.24.1":
  version "7.24.1"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.24.1.tgz"
  integrity sha512-mvoQg2f9p2qlpDQRBC7M3c3XTr0k7cp/0+kFKKO/7Gtu0LSw16eKB+Fabe2bDT/UpsyasTBBkAnbdsLrkD5XMw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-react-jsx-development@^7.22.5":
  version "7.22.5"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.22.5.tgz"
  integrity sha512-bDhuzwWMuInwCYeDeMzyi7TaBgRQei6DqxhbyniL7/VG4RSS7HtSL2QbY4eESy1KJqlWt8g3xeEBGPuo+XqC8A==
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.22.5"

"@babel/plugin-transform-react-jsx-self@^7.23.3":
  version "7.24.1"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.24.1.tgz"
  integrity sha512-kDJgnPujTmAZ/9q2CN4m2/lRsUUPDvsG3+tSHWUJIzMGTt5U/b/fwWd3RO3n+5mjLrsBrVa5eKFRVSQbi3dF1w==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-react-jsx-source@^7.23.3":
  version "7.24.1"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.24.1.tgz"
  integrity sha512-1v202n7aUq4uXAieRTKcwPzNyphlCuqHHDcdSNc+vdhoTEZcFMh+L5yZuCmGaIO7bs1nJUNfHB89TZyoL48xNA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-react-jsx@^7.22.5", "@babel/plugin-transform-react-jsx@^7.23.4":
  version "7.23.4"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.23.4.tgz"
  integrity sha512-5xOpoPguCZCRbo/JeHlloSkTA8Bld1J/E1/kLfD1nsuiW1m8tduTA1ERCgIZokDflX/IBzKcqR3l7VlRgiIfHA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.23.3"
    "@babel/types" "^7.23.4"

"@babel/plugin-transform-react-pure-annotations@^7.24.1":
  version "7.24.1"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.24.1.tgz"
  integrity sha512-+pWEAaDJvSm9aFvJNpLiM2+ktl2Sn2U5DdyiWdZBxmLc6+xGt88dvFqsHiAiDS+8WqUwbDfkKz9jRxK3M0k+kA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-typescript@^7.15.8":
  version "7.25.2"
  resolved "https://mirrors.tencent.com/npm/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.2.tgz"
  integrity sha512-lBwRvjSmqiMYe/pS0+1gggjJleUJi7NzjvQ1Fkqtt69hBa/0t1YuW/MLQMAPixfwaQOHUXsd6jeU3Z+vdGv3+A==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-create-class-features-plugin" "^7.25.0"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/plugin-syntax-typescript" "^7.24.7"

"@babel/polyfill@^7.12.1":
  version "7.12.1"
  resolved "https://mirrors.tencent.com/npm/@babel/polyfill/-/polyfill-7.12.1.tgz#1f2d6371d1261bbd961f3c5d5909150e12d0bd96"
  integrity sha512-X0pi0V6gxLi6lFZpGmeNa4zxtwEmCs42isWLNjZZDE0Y8yVfgu0T2OAHlzBbdYlqbW/YXVvoBHpATEM+goCj8g==
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/preset-react@^7.16.5":
  version "7.24.1"
  resolved "https://mirrors.tencent.com/npm/@babel/preset-react/-/preset-react-7.24.1.tgz"
  integrity sha512-eFa8up2/8cZXLIpkafhaADTXSnl7IsUFCYenRWrARBz0/qZwcT0RBXpys0LJU4+WfPoF2ZG6ew6s2V6izMCwRA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-transform-react-display-name" "^7.24.1"
    "@babel/plugin-transform-react-jsx" "^7.23.4"
    "@babel/plugin-transform-react-jsx-development" "^7.22.5"
    "@babel/plugin-transform-react-pure-annotations" "^7.24.1"

"@babel/runtime-corejs2@^7.6.3":
  version "7.25.0"
  resolved "https://mirrors.tencent.com/npm/@babel/runtime-corejs2/-/runtime-corejs2-7.25.0.tgz"
  integrity sha512-aoYVE3tm+vgAoezmXFWmVcp+NlSdsUqQMPL7c6zRxq8KDHCf570pamC7005Q/UkSlTuoL6oeE16zIw/9J3YFyw==
  dependencies:
    core-js "^2.6.12"
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.1.5", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.2", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.5", "@babel/runtime@^7.15.4", "@babel/runtime@^7.16.5", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.2.0", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.8", "@babel/runtime@^7.5.5", "@babel/runtime@^7.6.2", "@babel/runtime@^7.7.2", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2", "@babel/runtime@~7.24.7":
  version "7.24.8"
  resolved "https://mirrors.tencent.com/npm/@babel/runtime/-/runtime-7.24.8.tgz"
  integrity sha512-5F7SDGs1T72ZczbRwbGO9lQi0NLjQxzl6i4lJxLxfW9U5UluCSyEJeniWvnhl3/euNiqQVbo8zruhsDfid0esA==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.17.8":
  version "7.26.9"
  resolved "https://mirrors.tencent.com/npm/@babel/runtime/-/runtime-7.26.9.tgz#aa4c6facc65b9cb3f87d75125ffd47781b475433"
  integrity sha512-aA63XwOkcl4xxQa3HjPMqOP6LiK0ZDv3mUPYEFXkpHbaFjtGggE1A61FjFzJnB+p7/oy2gA8E+rcBNl/zC1tMg==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.24.0", "@babel/template@^7.25.0":
  version "7.25.0"
  resolved "https://mirrors.tencent.com/npm/@babel/template/-/template-7.25.0.tgz"
  integrity sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/parser" "^7.25.0"
    "@babel/types" "^7.25.0"

"@babel/traverse@^7.24.1", "@babel/traverse@^7.24.7", "@babel/traverse@^7.24.8", "@babel/traverse@^7.25.0", "@babel/traverse@^7.25.2":
  version "7.25.3"
  resolved "https://mirrors.tencent.com/npm/@babel/traverse/-/traverse-7.25.3.tgz"
  integrity sha512-HefgyP1x754oGCsKmV5reSmtV7IXj/kpaE1XYY+D9G5PvKKoFfSbiS4M77MdjuwlZKDIKFCffq9rPU+H/s3ZdQ==
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.25.0"
    "@babel/parser" "^7.25.3"
    "@babel/template" "^7.25.0"
    "@babel/types" "^7.25.2"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.23.4", "@babel/types@^7.24.0", "@babel/types@^7.24.7", "@babel/types@^7.24.8", "@babel/types@^7.25.0", "@babel/types@^7.25.2", "@babel/types@^7.5.0":
  version "7.25.2"
  resolved "https://mirrors.tencent.com/npm/@babel/types/-/types-7.25.2.tgz"
  integrity sha512-YTnYtra7W9e6/oAZEHj0bJehPRUlLH9/fbpT5LfB0NhQXyALCRkRs3zH9v07IYhkgpqX6Z78FnuccZr/l4Fs4Q==
  dependencies:
    "@babel/helper-string-parser" "^7.24.8"
    "@babel/helper-validator-identifier" "^7.24.7"
    to-fast-properties "^2.0.0"

"@bufbuild/protobuf@^2.0.0":
  version "2.2.2"
  resolved "https://mirrors.tencent.com/npm/@bufbuild/protobuf/-/protobuf-2.2.2.tgz#1a6d89603fb215dc4d4178052d05b30b83c75402"
  integrity sha512-UNtPCbrwrenpmrXuRwn9jYpPoweNXj8X5sMvYgsqYyaH8jQ6LfUJSk3dJLnBK+6sfYPrF4iAIo5sd5HQ+tg75A==

"@commitlint/cli@^17.0.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/cli/-/cli-17.8.1.tgz"
  integrity sha512-ay+WbzQesE0Rv4EQKfNbSMiJJ12KdKTDzIt0tcK4k11FdsWmtwP0Kp1NWMOUswfIWo6Eb7p7Ln721Nx9FLNBjg==
  dependencies:
    "@commitlint/format" "^17.8.1"
    "@commitlint/lint" "^17.8.1"
    "@commitlint/load" "^17.8.1"
    "@commitlint/read" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    execa "^5.0.0"
    lodash.isfunction "^3.0.9"
    resolve-from "5.0.0"
    resolve-global "1.0.0"
    yargs "^17.0.0"

"@commitlint/config-conventional@^17.0.0":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/config-conventional/-/config-conventional-17.8.1.tgz"
  integrity sha512-NxCOHx1kgneig3VLauWJcDWS40DVjg7nKOpBEEK9E5fjJpQqLCilcnKkIIjdBH98kEO1q3NpE5NSrZ2kl/QGJg==
  dependencies:
    conventional-changelog-conventionalcommits "^6.1.0"

"@commitlint/config-validator@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/config-validator/-/config-validator-17.8.1.tgz"
  integrity sha512-UUgUC+sNiiMwkyiuIFR7JG2cfd9t/7MV8VB4TZ+q02ZFkHoduUS4tJGsCBWvBOGD9Btev6IecPMvlWUfJorkEA==
  dependencies:
    "@commitlint/types" "^17.8.1"
    ajv "^8.11.0"

"@commitlint/ensure@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/ensure/-/ensure-17.8.1.tgz"
  integrity sha512-xjafwKxid8s1K23NFpL8JNo6JnY/ysetKo8kegVM7c8vs+kWLP8VrQq+NbhgVlmCojhEDbzQKp4eRXSjVOGsow==
  dependencies:
    "@commitlint/types" "^17.8.1"
    lodash.camelcase "^4.3.0"
    lodash.kebabcase "^4.1.1"
    lodash.snakecase "^4.1.1"
    lodash.startcase "^4.4.0"
    lodash.upperfirst "^4.3.1"

"@commitlint/execute-rule@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/execute-rule/-/execute-rule-17.8.1.tgz"
  integrity sha512-JHVupQeSdNI6xzA9SqMF+p/JjrHTcrJdI02PwesQIDCIGUrv04hicJgCcws5nzaoZbROapPs0s6zeVHoxpMwFQ==

"@commitlint/format@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/format/-/format-17.8.1.tgz"
  integrity sha512-f3oMTyZ84M9ht7fb93wbCKmWxO5/kKSbwuYvS867duVomoOsgrgljkGGIztmT/srZnaiGbaK8+Wf8Ik2tSr5eg==
  dependencies:
    "@commitlint/types" "^17.8.1"
    chalk "^4.1.0"

"@commitlint/is-ignored@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/is-ignored/-/is-ignored-17.8.1.tgz"
  integrity sha512-UshMi4Ltb4ZlNn4F7WtSEugFDZmctzFpmbqvpyxD3la510J+PLcnyhf9chs7EryaRFJMdAKwsEKfNK0jL/QM4g==
  dependencies:
    "@commitlint/types" "^17.8.1"
    semver "7.5.4"

"@commitlint/lint@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/lint/-/lint-17.8.1.tgz"
  integrity sha512-aQUlwIR1/VMv2D4GXSk7PfL5hIaFSfy6hSHV94O8Y27T5q+DlDEgd/cZ4KmVI+MWKzFfCTiTuWqjfRSfdRllCA==
  dependencies:
    "@commitlint/is-ignored" "^17.8.1"
    "@commitlint/parse" "^17.8.1"
    "@commitlint/rules" "^17.8.1"
    "@commitlint/types" "^17.8.1"

"@commitlint/load@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/load/-/load-17.8.1.tgz"
  integrity sha512-iF4CL7KDFstP1kpVUkT8K2Wl17h2yx9VaR1ztTc8vzByWWcbO/WaKwxsnCOqow9tVAlzPfo1ywk9m2oJ9ucMqA==
  dependencies:
    "@commitlint/config-validator" "^17.8.1"
    "@commitlint/execute-rule" "^17.8.1"
    "@commitlint/resolve-extends" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    "@types/node" "20.5.1"
    chalk "^4.1.0"
    cosmiconfig "^8.0.0"
    cosmiconfig-typescript-loader "^4.0.0"
    lodash.isplainobject "^4.0.6"
    lodash.merge "^4.6.2"
    lodash.uniq "^4.5.0"
    resolve-from "^5.0.0"
    ts-node "^10.8.1"
    typescript "^4.6.4 || ^5.2.2"

"@commitlint/message@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/message/-/message-17.8.1.tgz"
  integrity sha512-6bYL1GUQsD6bLhTH3QQty8pVFoETfFQlMn2Nzmz3AOLqRVfNNtXBaSY0dhZ0dM6A2MEq4+2d7L/2LP8TjqGRkA==

"@commitlint/parse@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/parse/-/parse-17.8.1.tgz"
  integrity sha512-/wLUickTo0rNpQgWwLPavTm7WbwkZoBy3X8PpkUmlSmQJyWQTj0m6bDjiykMaDt41qcUbfeFfaCvXfiR4EGnfw==
  dependencies:
    "@commitlint/types" "^17.8.1"
    conventional-changelog-angular "^6.0.0"
    conventional-commits-parser "^4.0.0"

"@commitlint/read@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/read/-/read-17.8.1.tgz"
  integrity sha512-Fd55Oaz9irzBESPCdMd8vWWgxsW3OWR99wOntBDHgf9h7Y6OOHjWEdS9Xzen1GFndqgyoaFplQS5y7KZe0kO2w==
  dependencies:
    "@commitlint/top-level" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    fs-extra "^11.0.0"
    git-raw-commits "^2.0.11"
    minimist "^1.2.6"

"@commitlint/resolve-extends@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/resolve-extends/-/resolve-extends-17.8.1.tgz"
  integrity sha512-W/ryRoQ0TSVXqJrx5SGkaYuAaE/BUontL1j1HsKckvM6e5ZaG0M9126zcwL6peKSuIetJi7E87PRQF8O86EW0Q==
  dependencies:
    "@commitlint/config-validator" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    import-fresh "^3.0.0"
    lodash.mergewith "^4.6.2"
    resolve-from "^5.0.0"
    resolve-global "^1.0.0"

"@commitlint/rules@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/rules/-/rules-17.8.1.tgz"
  integrity sha512-2b7OdVbN7MTAt9U0vKOYKCDsOvESVXxQmrvuVUZ0rGFMCrCPJWWP1GJ7f0lAypbDAhaGb8zqtdOr47192LBrIA==
  dependencies:
    "@commitlint/ensure" "^17.8.1"
    "@commitlint/message" "^17.8.1"
    "@commitlint/to-lines" "^17.8.1"
    "@commitlint/types" "^17.8.1"
    execa "^5.0.0"

"@commitlint/to-lines@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/to-lines/-/to-lines-17.8.1.tgz"
  integrity sha512-LE0jb8CuR/mj6xJyrIk8VLz03OEzXFgLdivBytoooKO5xLt5yalc8Ma5guTWobw998sbR3ogDd+2jed03CFmJA==

"@commitlint/top-level@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/top-level/-/top-level-17.8.1.tgz"
  integrity sha512-l6+Z6rrNf5p333SHfEte6r+WkOxGlWK4bLuZKbtf/2TXRN+qhrvn1XE63VhD8Oe9oIHQ7F7W1nG2k/TJFhx2yA==
  dependencies:
    find-up "^5.0.0"

"@commitlint/types@^17.8.1":
  version "17.8.1"
  resolved "https://mirrors.tencent.com/npm/@commitlint/types/-/types-17.8.1.tgz"
  integrity sha512-PXDQXkAmiMEG162Bqdh9ChML/GJZo6vU+7F03ALKDK8zYc6SuAr47LjG7hGYRqUOz+WK0dU7bQ0xzuqFMdxzeQ==
  dependencies:
    chalk "^4.1.0"

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://mirrors.tencent.com/npm/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@ctrl/tinycolor@^3.4.0":
  version "3.6.1"
  resolved "https://mirrors.tencent.com/npm/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  integrity sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==

"@dnd-kit/accessibility@^3.1.0":
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/@dnd-kit/accessibility/-/accessibility-3.1.0.tgz"
  integrity sha512-ea7IkhKvlJUv9iSHJOnxinBcoOI3ppGnnL+VDJ75O45Nss6HtZd8IdN8touXPDtASfeI2T2LImb8VOZcL47wjQ==
  dependencies:
    tslib "^2.0.0"

"@dnd-kit/core@^6.0.0":
  version "6.1.0"
  resolved "https://mirrors.tencent.com/npm/@dnd-kit/core/-/core-6.1.0.tgz"
  integrity sha512-J3cQBClB4TVxwGo3KEjssGEXNJqGVWx17aRTZ1ob0FliR5IjYgTxl5YJbKTzA6IzrtelotH19v6y7uoIRUZPSg==
  dependencies:
    "@dnd-kit/accessibility" "^3.1.0"
    "@dnd-kit/utilities" "^3.2.2"
    tslib "^2.0.0"

"@dnd-kit/sortable@^7.0.0":
  version "7.0.2"
  resolved "https://mirrors.tencent.com/npm/@dnd-kit/sortable/-/sortable-7.0.2.tgz"
  integrity sha512-wDkBHHf9iCi1veM834Gbk1429bd4lHX4RpAwT0y2cHLf246GAvU2sVw/oxWNpPKQNQRQaeGXhAVgrOl1IT+iyA==
  dependencies:
    "@dnd-kit/utilities" "^3.2.0"
    tslib "^2.0.0"

"@dnd-kit/utilities@^3.2.0", "@dnd-kit/utilities@^3.2.2":
  version "3.2.2"
  resolved "https://mirrors.tencent.com/npm/@dnd-kit/utilities/-/utilities-3.2.2.tgz"
  integrity sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg==
  dependencies:
    tslib "^2.0.0"

"@emotion/babel-plugin@^11.11.0", "@emotion/babel-plugin@^11.12.0":
  version "11.12.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/babel-plugin/-/babel-plugin-11.12.0.tgz"
  integrity sha512-y2WQb+oP8Jqvvclh8Q55gLUyb7UFvgv7eJfsj7td5TToBrIUtPay2kMrZi4xjq9qw2vD0ZR5fSho0yqoFgX7Rw==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/serialize" "^1.2.0"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0", "@emotion/cache@^11.13.0":
  version "11.13.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/cache/-/cache-11.13.0.tgz"
  integrity sha512-hPV345J/tH0Cwk2wnU/3PBzORQ9HeX+kQSbwI+jslzpRCHE6fSGTohswksA/Ensr8znPzwfzKZCmAM9Lmlhp7g==
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.0"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/css@^11.11.2":
  version "11.13.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/css/-/css-11.13.0.tgz"
  integrity sha512-BUk99ylT+YHl+W/HN7nv1RCTkDYmKKqa1qbvM/qLSQEg61gipuBF5Hptk/2/ERmX2DCv0ccuFGhz9i0KSZOqPg==
  dependencies:
    "@emotion/babel-plugin" "^11.12.0"
    "@emotion/cache" "^11.13.0"
    "@emotion/serialize" "^1.3.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.0"

"@emotion/hash@^0.9.2":
  version "0.9.2"
  resolved "https://mirrors.tencent.com/npm/@emotion/hash/-/hash-0.9.2.tgz"
  integrity sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==

"@emotion/memoize@^0.9.0":
  version "0.9.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/memoize/-/memoize-0.9.0.tgz"
  integrity sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==

"@emotion/react@^11.11.4":
  version "11.11.4"
  resolved "https://mirrors.tencent.com/npm/@emotion/react/-/react-11.11.4.tgz"
  integrity sha512-t8AjMlF0gHpvvxk5mAtCqR4vmxiGHCeJBaQO6gncUSdklELOgtwjerNY2yuJNfwnc6vi16U/+uMF+afIawJ9iw==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/cache" "^11.11.0"
    "@emotion/serialize" "^1.1.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.1.3", "@emotion/serialize@^1.2.0", "@emotion/serialize@^1.3.0":
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/serialize/-/serialize-1.3.0.tgz"
  integrity sha512-jACuBa9SlYajnpIVXB+XOXnfJHyckDfe6fOpORIM6yhBDlqGuExvDdZYHDQGoDf3bZXGv7tNr+LpLjJqiEQ6EA==
  dependencies:
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/unitless" "^0.9.0"
    "@emotion/utils" "^1.4.0"
    csstype "^3.0.2"

"@emotion/sheet@^1.4.0":
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/sheet/-/sheet-1.4.0.tgz"
  integrity sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==

"@emotion/unitless@^0.9.0":
  version "0.9.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/unitless/-/unitless-0.9.0.tgz"
  integrity sha512-TP6GgNZtmtFaFcsOgExdnfxLLpRDla4Q66tnenA9CktvVSdNKDvMVuUah4QvWPIpNjrWsGg3qeGo9a43QooGZQ==

"@emotion/use-insertion-effect-with-fallbacks@^1.0.1":
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.0.1.tgz"
  integrity sha512-jT/qyKZ9rzLErtrjGgdkMBn2OP8wl0G3sQlBb3YPryvKHsjvINUhVaPFfP+fpBcOkmrVOVEEHQFJ7nbj2TH2gw==

"@emotion/utils@^1.2.1", "@emotion/utils@^1.4.0":
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/utils/-/utils-1.4.0.tgz"
  integrity sha512-spEnrA1b6hDR/C68lC2M7m6ALPUHZC0lIY7jAS/B/9DuuO1ZP04eov8SMv/6fwRd8pzmsn2AuJEznRREWlQrlQ==

"@emotion/weak-memoize@^0.3.1":
  version "0.3.1"
  resolved "https://mirrors.tencent.com/npm/@emotion/weak-memoize/-/weak-memoize-0.3.1.tgz"
  integrity sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==

"@emotion/weak-memoize@^0.4.0":
  version "0.4.0"
  resolved "https://mirrors.tencent.com/npm/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz"
  integrity sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/android-arm/-/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/android-x64/-/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz#e495b539660e51690f3928af50a76fb0a6ccff2a"
  integrity sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c"
  integrity sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "https://mirrors.tencent.com/npm/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
  integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.10.0"
  resolved "https://mirrors.tencent.com/npm/@eslint-community/regexpp/-/regexpp-4.10.0.tgz"
  integrity sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://mirrors.tencent.com/npm/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.0":
  version "8.57.0"
  resolved "https://mirrors.tencent.com/npm/@eslint/js/-/js-8.57.0.tgz"
  integrity sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==

"@formily/antd@2.3.1":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/antd/-/antd-2.3.1.tgz"
  integrity sha512-+304KIcJXrt4tH8tW4uqwuz95o9ftl79jxS8k9GqWlaaSudapOxIdKp1mwAvVp9GUVUa1BNlyZNRtaSvx3+GnA==
  dependencies:
    "@dnd-kit/core" "^6.0.0"
    "@dnd-kit/sortable" "^7.0.0"
    "@formily/core" "2.3.1"
    "@formily/grid" "2.3.1"
    "@formily/json-schema" "2.3.1"
    "@formily/react" "2.3.1"
    "@formily/reactive" "2.3.1"
    "@formily/reactive-react" "2.3.1"
    "@formily/shared" "2.3.1"
    classnames "^2.2.6"
    react-sticky-box "^0.9.3"

"@formily/core@2.2.0":
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/@formily/core/-/core-2.2.0.tgz"
  integrity sha512-NYx3BAMDZaHtblTg+YXdK4tIBiXmPpkjeQbewR4l+6FlO4S6eqt198arAq8j6k6OQmDRgyLdpBNw8bpXrPLn5w==
  dependencies:
    "@formily/reactive" "2.2.0"
    "@formily/shared" "2.2.0"
    "@formily/validator" "2.2.0"

"@formily/core@2.3.1", "@formily/core@^2.1.4", "@formily/core@^2.2.29":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/core/-/core-2.3.1.tgz"
  integrity sha512-dCBPnmzDpQKdN4ddxi98VbdurpbH6SHb12S9y9SnzW/QTslZnkvrvBhTlucWB5XqXJfwcyCzpZIn/GdMAbPpZg==
  dependencies:
    "@formily/reactive" "2.3.1"
    "@formily/shared" "2.3.1"
    "@formily/validator" "2.3.1"

"@formily/grid@2.3.1", "@formily/grid@^2.2.0":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/grid/-/grid-2.3.1.tgz"
  integrity sha512-Je+sWa7b71qZgysbVQXiv+3bOqXnKZ3vU4RIhMXrTL4ac46+9dNSXGSVrC85C98lq9EJp/uUJhtLdUb6P3WStg==
  dependencies:
    "@formily/reactive" "2.3.1"
    "@juggle/resize-observer" "^3.3.1"

"@formily/json-schema@2.2.0":
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/@formily/json-schema/-/json-schema-2.2.0.tgz"
  integrity sha512-gCGfM2g1s91ARsHCWR6SsFAou66oCAU84jcg+9wKtIuyXWoVEEt/18ozvQGdR5aZoWgPjYwbUObRPOL/3QkMkg==
  dependencies:
    "@formily/core" "2.2.0"
    "@formily/reactive" "2.2.0"
    "@formily/shared" "2.2.0"

"@formily/json-schema@2.3.1":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/json-schema/-/json-schema-2.3.1.tgz"
  integrity sha512-LcJmU1BOYGdoip+Q9YAxHdrpjdl781WFSg0fDTD9/0A7c3xUXWwxdCYZIN0cvoXi2qq8Khyzba0pizl3YSkf0A==
  dependencies:
    "@formily/core" "2.3.1"
    "@formily/reactive" "2.3.1"
    "@formily/shared" "2.3.1"

"@formily/path@2.2.0":
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/@formily/path/-/path-2.2.0.tgz"
  integrity sha512-oxsqDT7p5qHRooCGwXU26FoV9hUueYtH0+MNQxFWuC6QgJ4KKKe8EEkoA7vLco3CPu96NKwTHbhhMd86XcGCrw==

"@formily/path@2.3.1":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/path/-/path-2.3.1.tgz"
  integrity sha512-BVo89K5nAFntx02+EV696If1b1bVIm5I1tRPtVyCVIjBIfAgga5hK4k80GZ01Dlk3tpReHpiIbZVg2DNVfw7jA==

"@formily/react@2.3.1", "@formily/react@^2.1.4", "@formily/react@^2.2.29":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/react/-/react-2.3.1.tgz"
  integrity sha512-8KkFJe2OnbsgiXwY/txUcUaCQIfIkfowWQinOSni02U2ssgs2bpb1ifFHlZrFGfrrW/xhce4ANUHRgoVYt7W4Q==
  dependencies:
    "@formily/core" "2.3.1"
    "@formily/json-schema" "2.3.1"
    "@formily/reactive" "2.3.1"
    "@formily/reactive-react" "2.3.1"
    "@formily/shared" "2.3.1"
    "@formily/validator" "2.3.1"
    hoist-non-react-statics "^3.3.2"

"@formily/reactive-react@2.3.1", "@formily/reactive-react@^2.2.0":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/reactive-react/-/reactive-react-2.3.1.tgz"
  integrity sha512-r6d94JBWhwXGpsffNLnRzgksQJtmqudD1cq7ky2Oljtxg5Ynl8H0Ppcnsv0bd9NbNSAeDeFs2uwWz56exgDzjA==
  dependencies:
    "@formily/reactive" "2.3.1"
    hoist-non-react-statics "^3.3.2"

"@formily/reactive@2.2.0":
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/@formily/reactive/-/reactive-2.2.0.tgz"
  integrity sha512-lmcl9Z4yUu8hSD+Bj7pk/3xFiDeMZ8LSA8aLkhqriO4OU1noXuRPrqqHNjC0JNS5SoHI+Ot9VhhmgkFr+PIT9A==

"@formily/reactive@2.3.1":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/reactive/-/reactive-2.3.1.tgz"
  integrity sha512-IVHOZW7VBc+Gq9eB/gPldi7pEC3wDonDb99KvHlS8SmzsY6+a/iAdrw2mDagXXUficsC2gT4y4EcJ2f1ALMKtQ==

"@formily/shared@2.2.0":
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/@formily/shared/-/shared-2.2.0.tgz"
  integrity sha512-wtwTKXdwc+BiZ986SldU0E6l1ZuVIwsIFzTEe317HlxVGVu5z3n7SIDSBL+pXmgvYW4cKkm5qzKypk90ZYa7oA==
  dependencies:
    "@formily/path" "2.2.0"
    camel-case "^4.1.1"
    lower-case "^2.0.1"
    no-case "^3.0.4"
    param-case "^3.0.4"
    pascal-case "^3.1.1"
    upper-case "^2.0.1"

"@formily/shared@2.3.1", "@formily/shared@^2.2.0":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/shared/-/shared-2.3.1.tgz"
  integrity sha512-qnlh6jnnIbUjcK0rWF9bm6AxgyxuBgURrzU5vMSxTNAN86P7K9+mSc/28qPsdNP9flEA2/clSexP5WEJAGYVgw==
  dependencies:
    "@formily/path" "2.3.1"
    camel-case "^4.1.1"
    lower-case "^2.0.1"
    no-case "^3.0.4"
    param-case "^3.0.4"
    pascal-case "^3.1.1"
    upper-case "^2.0.1"

"@formily/tdesign-react@^1.0.0-beta.15":
  version "1.0.0-beta.15"
  resolved "https://mirrors.tencent.com/npm/@formily/tdesign-react/-/tdesign-react-1.0.0-beta.15.tgz#884106f98a7b99b33901c8a68d12f7b958acee01"
  integrity sha512-jVWh9Laus91InKweGCV0g6cOUCNe9qnoNeHNL4Ob1yAyTd8+eHWLuiirnuKg/XtW1IsrZtORXnpmL1JCOMySbg==
  dependencies:
    "@formily/grid" "^2.2.0"
    "@formily/json-schema" "2.2.0"
    "@formily/reactive-react" "^2.2.0"
    "@formily/shared" "^2.2.0"
    classnames "^2.3.1"
    react-sortable-hoc "^2.0.0"
    react-sticky-box "^0.9.3"

"@formily/validator@2.2.0":
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/@formily/validator/-/validator-2.2.0.tgz"
  integrity sha512-vpDqpOdoHLr0Tqnpdg9JlpRJHOpEc77RJH2pV2WVwVwcGBoLH75h0atQMmEjCoZ4nD5fVBs768osawrxz7hLCQ==
  dependencies:
    "@formily/shared" "2.2.0"

"@formily/validator@2.3.1":
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/@formily/validator/-/validator-2.3.1.tgz"
  integrity sha512-hM/IDvU/bachpS3fOUe02C9f5EytlNu6OJzDy+AyhWBmZYIVd6QVvPtjV8nyLOXIBJM9N4sxnGSliYQNAPjR1w==
  dependencies:
    "@formily/shared" "2.3.1"

"@hookform/error-message@0.0.5":
  version "0.0.5"
  resolved "https://mirrors.tencent.com/npm/@hookform/error-message/-/error-message-0.0.5.tgz"
  integrity sha512-es7eLLFA3SXNYAT8aUjvf7Gok1eMHK+9DMILtJA7ZEwYZlCCCPifhpoZmY+5SOopEtF7e+qxFOjX+MJnI3jOcg==

"@humanwhocodes/config-array@^0.11.14":
  version "0.11.14"
  resolved "https://mirrors.tencent.com/npm/@humanwhocodes/config-array/-/config-array-0.11.14.tgz"
  integrity sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.2"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.2":
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
  integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==

"@hypnosphi/create-react-context@^0.3.1":
  version "0.3.1"
  resolved "https://mirrors.tencent.com/npm/@hypnosphi/create-react-context/-/create-react-context-0.3.1.tgz"
  integrity sha512-V1klUed202XahrWJLLOT3EXNeCpFHCcJntdFGI15ntCwau+jfT386w7OFTMaCqOgXUH1fa0w/I1oZs+i/Rfr0A==
  dependencies:
    gud "^1.0.0"
    warning "^4.0.3"

"@icons/material@^0.2.4":
  version "0.2.4"
  resolved "https://mirrors.tencent.com/npm/@icons/material/-/material-0.2.4.tgz"
  integrity sha512-QPcGmICAPbGLGb6F/yNf/KzKqvFx8z5qx3D1yFqVAjoFmXK35EgyW+cJ57Te3CNsmzblwtzakLGFqHPqrfb4Tw==

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://mirrors.tencent.com/npm/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://mirrors.tencent.com/npm/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://mirrors.tencent.com/npm/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  version "1.5.0"
  resolved "https://mirrors.tencent.com/npm/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://mirrors.tencent.com/npm/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://mirrors.tencent.com/npm/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@juggle/resize-observer@^3.3.1":
  version "3.4.0"
  resolved "https://mirrors.tencent.com/npm/@juggle/resize-observer/-/resize-observer-3.4.0.tgz"
  integrity sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA==

"@mapbox/node-pre-gyp@^1.0.0":
  version "1.0.11"
  resolved "https://mirrors.tencent.com/npm/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz"
  integrity sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@mi18n/core@5.15.1":
  version "5.15.1"
  resolved "https://mirrors.tencent.com/npm/@mi18n/core/-/core-5.15.1.tgz"
  integrity sha512-i5GvDzXTM8bzm4BQTPNjbEJs95I7+c11Zfb9gaeUQvj0CsZKayJBURU/D2EVZEAIorWxnSJwqdQ/TrGKrvbdwg==

"@mi18n/react@^5.15.1":
  version "5.15.1"
  resolved "https://mirrors.tencent.com/npm/@mi18n/react/-/react-5.15.1.tgz"
  integrity sha512-zZJqDN8dRSf++pfBi5p2YJnyjO26JrFXmRUVgYxPBZu87IeQpKL8bbevaj+H8guIIt/ZRAeJiCnBdSqLeQgLRQ==
  dependencies:
    "@mi18n/web" "5.15.1"

"@mi18n/web@5.15.1":
  version "5.15.1"
  resolved "https://mirrors.tencent.com/npm/@mi18n/web/-/web-5.15.1.tgz"
  integrity sha512-8b2i8604WWHjeB4dSq+itu6rDHtBg3JkwD62lYA501ATDywXagNnF0UUSvVoiSSCIs7inID8+w4vGS4DxDoqEA==
  dependencies:
    "@mi18n/core" "5.15.1"

"@monaco-editor/loader@^1.3.2":
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/@monaco-editor/loader/-/loader-1.4.0.tgz"
  integrity sha512-00ioBig0x642hytVspPl7DbQyaSWRaolYie/UFNjoTdvoKPzo6xrXLhTk9ixgIKcLH5b5vDOjVNiGyY+uDCUlg==
  dependencies:
    state-local "^1.0.6"

"@monaco-editor/react@4.4.5":
  version "4.4.5"
  resolved "https://mirrors.tencent.com/npm/@monaco-editor/react/-/react-4.4.5.tgz"
  integrity sha512-IImtzU7sRc66OOaQVCG+5PFHkSWnnhrUWGBuH6zNmH2h0YgmAhcjHZQc/6MY9JWEbUtVF1WPBMJ9u1XuFbRrVA==
  dependencies:
    "@monaco-editor/loader" "^1.3.2"
    prop-types "^15.7.2"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "https://mirrors.tencent.com/npm/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
  integrity sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==
  dependencies:
    eslint-scope "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://mirrors.tencent.com/npm/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://mirrors.tencent.com/npm/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://mirrors.tencent.com/npm/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://mirrors.tencent.com/npm/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@popperjs/core@~2.11.2":
  version "2.11.8"
  resolved "https://mirrors.tencent.com/npm/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==

"@react-three/fiber@^8.17.10":
  version "8.17.14"
  resolved "https://mirrors.tencent.com/npm/@react-three/fiber/-/fiber-8.17.14.tgz#a8e119f8e716f5476082d2b1e3a62048daf587f2"
  integrity sha512-Al2Zdhn5vRefK0adJXNDputuM8hwRNh3goH8MCzf06gezZBbEsdmjt5IrHQQ8Rpr7l/znx/ipLUQuhiiVhxifQ==
  dependencies:
    "@babel/runtime" "^7.17.8"
    "@types/react-reconciler" "^0.26.7"
    "@types/webxr" "*"
    base64-js "^1.5.1"
    buffer "^6.0.3"
    its-fine "^1.0.6"
    react-reconciler "^0.27.0"
    react-use-measure "^2.1.7"
    scheduler "^0.21.0"
    suspend-react "^0.1.3"
    zustand "^3.7.1"

"@remix-run/router@1.18.0":
  version "1.18.0"
  resolved "https://mirrors.tencent.com/npm/@remix-run/router/-/router-1.18.0.tgz"
  integrity sha512-L3jkqmqoSVBVKHfpGZmLrex0lxR5SucGA0sUfFzGctehw+S/ggL9L/0NnC5mw6P8HUWpFZ3nQw3cRApjjWx9Sw==

"@rollup/pluginutils@^4.1.1":
  version "4.2.1"
  resolved "https://mirrors.tencent.com/npm/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rollup/rollup-android-arm-eabi@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.37.0.tgz#9bedc746a97fe707154086365f269ced92ff4aa9"
  integrity sha512-l7StVw6WAa8l3vA1ov80jyetOAEo1FtHvZDbzXDO/02Sq/QVvqlHkYoFwDJPIMj0GKiistsBudfx5tGFnwYWDQ==

"@rollup/rollup-android-arm64@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.37.0.tgz#6edc6ffc8af8773e4bc28c72894dd5e846b8ee6c"
  integrity sha512-6U3SlVyMxezt8Y+/iEBcbp945uZjJwjZimu76xoG7tO1av9VO691z8PkhzQ85ith2I8R2RddEPeSfcbyPfD4hA==

"@rollup/rollup-darwin-arm64@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.37.0.tgz#737a7b8be9ff79bd24a7efaae0903e8c66ac0676"
  integrity sha512-+iTQ5YHuGmPt10NTzEyMPbayiNTcOZDWsbxZYR1ZnmLnZxG17ivrPSWFO9j6GalY0+gV3Jtwrrs12DBscxnlYA==

"@rollup/rollup-darwin-x64@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.37.0.tgz#a6a697bb685ca9462a7caeea5f22f6a686acff1f"
  integrity sha512-m8W2UbxLDcmRKVjgl5J/k4B8d7qX2EcJve3Sut7YGrQoPtCIQGPH5AMzuFvYRWZi0FVS0zEY4c8uttPfX6bwYQ==

"@rollup/rollup-freebsd-arm64@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.37.0.tgz#18113e8e133ccb6de4b9dc9d3e09f7acff344cb7"
  integrity sha512-FOMXGmH15OmtQWEt174v9P1JqqhlgYge/bUjIbiVD1nI1NeJ30HYT9SJlZMqdo1uQFyt9cz748F1BHghWaDnVA==

"@rollup/rollup-freebsd-x64@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.37.0.tgz#5e56ffd4a0d7ccfcbc86867c40b8f0e6a2c0c81e"
  integrity sha512-SZMxNttjPKvV14Hjck5t70xS3l63sbVwl98g3FlVVx2YIDmfUIy29jQrsw06ewEYQ8lQSuY9mpAPlmgRD2iSsA==

"@rollup/rollup-linux-arm-gnueabihf@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.37.0.tgz#5addf1a51e1495ae7ff28d26442a88adf629c980"
  integrity sha512-hhAALKJPidCwZcj+g+iN+38SIOkhK2a9bqtJR+EtyxrKKSt1ynCBeqrQy31z0oWU6thRZzdx53hVgEbRkuI19w==

"@rollup/rollup-linux-arm-musleabihf@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.37.0.tgz#00cddb9ab51086c5f2cd33cd4738259e24be4e73"
  integrity sha512-jUb/kmn/Gd8epbHKEqkRAxq5c2EwRt0DqhSGWjPFxLeFvldFdHQs/n8lQ9x85oAeVb6bHcS8irhTJX2FCOd8Ag==

"@rollup/rollup-linux-arm64-gnu@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.37.0.tgz#c3b4324496236b6fd9f31fda5701c6d6060b1512"
  integrity sha512-oNrJxcQT9IcbcmKlkF+Yz2tmOxZgG9D9GRq+1OE6XCQwCVwxixYAa38Z8qqPzQvzt1FCfmrHX03E0pWoXm1DqA==

"@rollup/rollup-linux-arm64-musl@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.37.0.tgz#b5222180bb1a50e6e9bc8263efd771c1ce770b6f"
  integrity sha512-pfxLBMls+28Ey2enpX3JvjEjaJMBX5XlPCZNGxj4kdJyHduPBXtxYeb8alo0a7bqOoWZW2uKynhHxF/MWoHaGQ==

"@rollup/rollup-linux-loongarch64-gnu@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.37.0.tgz#5660181c1c1efb7b19c7a531d496e685236c5ce7"
  integrity sha512-yCE0NnutTC/7IGUq/PUHmoeZbIwq3KRh02e9SfFh7Vmc1Z7atuJRYWhRME5fKgT8aS20mwi1RyChA23qSyRGpA==

"@rollup/rollup-linux-powerpc64le-gnu@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.37.0.tgz#8273166495d2f5d3fbc556cf42a5a6e24b78bdab"
  integrity sha512-NxcICptHk06E2Lh3a4Pu+2PEdZ6ahNHuK7o6Np9zcWkrBMuv21j10SQDJW3C9Yf/A/P7cutWoC/DptNLVsZ0VQ==

"@rollup/rollup-linux-riscv64-gnu@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.37.0.tgz#9677e39288ccc91ebcd707cdd794732d701cd174"
  integrity sha512-PpWwHMPCVpFZLTfLq7EWJWvrmEuLdGn1GMYcm5MV7PaRgwCEYJAwiN94uBuZev0/J/hFIIJCsYw4nLmXA9J7Pw==

"@rollup/rollup-linux-riscv64-musl@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.37.0.tgz#71cc5ca7be1ed263357618bfe4f8f50c09725a7e"
  integrity sha512-DTNwl6a3CfhGTAOYZ4KtYbdS8b+275LSLqJVJIrPa5/JuIufWWZ/QFvkxp52gpmguN95eujrM68ZG+zVxa8zHA==

"@rollup/rollup-linux-s390x-gnu@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.37.0.tgz#6b0b7df33eb32b0ee7423898b183acc1b5fee33e"
  integrity sha512-hZDDU5fgWvDdHFuExN1gBOhCuzo/8TMpidfOR+1cPZJflcEzXdCy1LjnklQdW8/Et9sryOPJAKAQRw8Jq7Tg+A==

"@rollup/rollup-linux-x64-gnu@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.37.0.tgz#52c27717d3c4819d13b5ebc2373ddea099d2e71b"
  integrity sha512-pKivGpgJM5g8dwj0ywBwe/HeVAUSuVVJhUTa/URXjxvoyTT/AxsLTAbkHkDHG7qQxLoW2s3apEIl26uUe08LVQ==

"@rollup/rollup-linux-x64-musl@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.37.0.tgz#c134a22d30642345de8b799c816345674bf68019"
  integrity sha512-E2lPrLKE8sQbY/2bEkVTGDEk4/49UYRVWgj90MY8yPjpnGBQ+Xi1Qnr7b7UIWw1NOggdFQFOLZ8+5CzCiz143w==

"@rollup/rollup-win32-arm64-msvc@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.37.0.tgz#8063d5f8195dd1845e056d069366fbe06a424d09"
  integrity sha512-Jm7biMazjNzTU4PrQtr7VS8ibeys9Pn29/1bm4ph7CP2kf21950LgN+BaE2mJ1QujnvOc6p54eWWiVvn05SOBg==

"@rollup/rollup-win32-ia32-msvc@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.37.0.tgz#891d90e3b5517f9d290bb416afdfe2ebfb12139e"
  integrity sha512-e3/1SFm1OjefWICB2Ucstg2dxYDkDTZGDYgwufcbsxTHyqQps1UQf33dFEChBNmeSsTOyrjw2JJq0zbG5GF6RA==

"@rollup/rollup-win32-x64-msvc@4.37.0":
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.37.0.tgz#a54d7304c3bd45573d8bcd1270de89771f8195fe"
  integrity sha512-LWbXUBwn/bcLx2sSsqy7pK5o+Nr+VCoRoAohfJ5C/aBio9nfJmGQqHAhU6pwxV/RmyTk5AqdySma7uwWGlmeuA==

"@sindresorhus/is@^0.14.0":
  version "0.14.0"
  resolved "https://mirrors.tencent.com/npm/@sindresorhus/is/-/is-0.14.0.tgz"
  integrity sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==

"@szmarczak/http-timer@^1.1.2":
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/@szmarczak/http-timer/-/http-timer-1.1.2.tgz"
  integrity sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==
  dependencies:
    defer-to-connect "^1.0.1"

"@tailwindcss/line-clamp@^0.4.4":
  version "0.4.4"
  resolved "https://mirrors.tencent.com/npm/@tailwindcss/line-clamp/-/line-clamp-0.4.4.tgz"
  integrity sha512-5U6SY5z8N42VtrCrKlsTAA35gy2VSyYtHWCsg1H87NU1SXnEfekTVlrga9fzUDrrHcGi2Lb5KenUWb4lRQT5/g==

"@tencent/aegis-web-sdk@^1.33.12":
  version "1.43.7"
  resolved "https://mirrors.tencent.com/npm/@tencent/aegis-web-sdk/-/@tencent/aegis-web-sdk-1.43.7.tgz"
  integrity sha512-+mT9y9oYPDEOVNz1HbHqw0aeHubGtdYEU2Jppnezm6BSWMSf9aFQSE3yJHan0DpQJGf9wrfAcEbkzaURslId4Q==
  dependencies:
    web-vitals "^3.4.0"

"@tencent/avatar-client-bridge-type@^0.0.24":
  version "0.0.24"
  resolved "https://mirrors.tencent.com/npm/@tencent/avatar-client-bridge-type/-/@tencent/avatar-client-bridge-type-0.0.24.tgz#8f9cc72df12b8966aa044c6a05a9877756c3275e"
  integrity sha512-Z3G98PXpnNvx3dJmoPW5RJHrMwLm//7JYNYf/CqSoWr1qkJhTfugtcmve2B+qgwtJs8hXrIecIvFAujCaJeGiw==

"@tencent/climb-cms-sdk@^1.1.40":
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/climb-cms-sdk/-/@tencent/climb-cms-sdk-1.3.0.tgz"
  integrity sha512-EYhy5ay4OAkLPswG/7UV2E9MmM10g5FQ7qDXRc4SmBfA22ZdwKnIhz5h9jG0dLXaMZnaKiMUMo+ty19KUyatWg==

"@tencent/cmsweb-component@^0.2.15":
  version "0.2.15"
  resolved "https://mirrors.tencent.com/npm/@tencent/cmsweb-component/-/@tencent/cmsweb-component-0.2.15.tgz"
  integrity sha512-D2vHlKi9TYGysz+8HA0G3I/k0xTWnPJUR9+ud6irsbwPTkCbw8vfw/t6Lp8BnCoyIuJyYhthFLt7Px7K4QPEgw==
  dependencies:
    "@mi18n/react" "^5.15.1"
    "@tencent/climb-cms-sdk" "^1.1.40"
    "@tencent/sr-ui-component" "^0.2.35"
    "@tencent/tea-component" "2.7.4-beta.23"
    "@tencent/tea-sr" "^1.1.2-beta.7"
    axios "^0.27.2"
    moment "^2.29.3"
    react-query "^3.39.1"

"@tencent/creative-mb@^0.0.59":
  version "0.0.59"
  resolved "https://mirrors.tencent.com/npm/@tencent/creative-mb/-/@tencent/creative-mb-0.0.59.tgz#aae45834ccb4df6e689a67f91f95c450ab5851d0"
  integrity sha512-40hW3XAdHABY7YK6GX81fIzgOnBAjVWstuvKfqyUlFQx3jPD0UDdLP6bWv3h0h1vCm0b4JH3la+OvSJrfe2Xtg==
  dependencies:
    "@react-three/fiber" "^8.17.10"
    "@tencent/font-select" "1.0.6"
    "@tencent/od-hooks" "^0.0.38"
    "@tencent/spaui" "3.0.335-beta.0"
    "@tencent/tacc-typings" "0.2.1"
    "@webav/av-canvas" "^1.0.5"
    "@webav/av-cliper" "^1.0.5"
    react "^18.2.0"
    react-dom "^18.2.0"
    swiper "^9.3.2"
    wavesurfer.js "^7.8.4"

"@tencent/eslint-config-prettier-typescript-react@^2.1.0":
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config-prettier-typescript-react/-/@tencent/eslint-config-prettier-typescript-react-2.1.0.tgz"
  integrity sha512-svdbI7sIkHaLedhBLhvbsk+otOrzY5z8a2zad9XURUfK1tAtRuVPwNN2GuaxGCUEyMXS12KQPWqrTssq7neK/Q==
  dependencies:
    "@tencent/eslint-config-prettier" "2.0.0"
    "@tencent/eslint-config-typescript-react" "2.1.0"

"@tencent/eslint-config-prettier@2.0.0":
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config-prettier/-/@tencent/eslint-config-prettier-2.0.0.tgz"
  integrity sha512-y4lvA2O7fGxBMllbPxEXCyXpjIhoric6yy5CtT2zqs9Miq909H5FZCiErz7HU5mEgDPF1ckJlxX/qEOBBluKKQ==
  dependencies:
    "@tencent/eslint-config" "2.0.0"
    eslint-config-prettier "^8.3.0"
    eslint-plugin-prettier "^4.0.0"
    prettier "^2.5.0"

"@tencent/eslint-config-react@2.1.0":
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config-react/-/@tencent/eslint-config-react-2.1.0.tgz"
  integrity sha512-aUIZKoTn86IFiZeZsZN7zRHUtIrbT+nNTMEoR0oEBMlTTL6JKl0Z1dvnZpOa/AVaNsOf1ZYfgG2QvXQliD7vAA==
  dependencies:
    "@babel/preset-react" "^7.16.5"
    "@tencent/eslint-config" "2.0.0"
    eslint-config-airbnb "^19.0.1"
    eslint-plugin-jsx-a11y "^6.5.1"
    eslint-plugin-react "^7.27.1"
    eslint-plugin-react-hooks "^4.3.0"

"@tencent/eslint-config-tencent@^0.16.0":
  version "0.16.1"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config-tencent/-/@tencent/eslint-config-tencent-0.16.1.tgz"
  integrity sha512-tXKLa9OAhX+au8DNka8SQjVM7o1EbAFurUiXbrCtqw8oSVS6KTCHPSdHRwZYTqunD6d9rQUltgTtaU5p6Tnimg==
  dependencies:
    "@babel/eslint-parser" "^7.14.5"
    eslint-plugin-import "^2.23.4"

"@tencent/eslint-config-typescript-react@2.1.0":
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config-typescript-react/-/@tencent/eslint-config-typescript-react-2.1.0.tgz"
  integrity sha512-cbmcoYf2/lVPBd0gfe4yymnENfqvRSVSnA2Skq1a5tBqeGwmVvbBR7Ktpo0o4K0z8vI8nQFRoPmgmnvy7+RoAQ==
  dependencies:
    "@tencent/eslint-config-react" "2.1.0"
    "@tencent/eslint-config-typescript" "2.1.0"

"@tencent/eslint-config-typescript@2.1.0":
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config-typescript/-/@tencent/eslint-config-typescript-2.1.0.tgz"
  integrity sha512-Hh5UGp22iMdwohxDXMGROM3BQDOWlfnZexyiZI3XsgjbeVODL3kbmnSYeCr1Dmb+Ihs4XoJbkH2FTfrX0bcyxQ==
  dependencies:
    "@tencent/eslint-config" "2.0.0"
    "@tencent/eslint-config-tencent" "^0.16.0"
    "@typescript-eslint/eslint-plugin" "^5.7.0"
    "@typescript-eslint/parser" "^5.7.0"

"@tencent/eslint-config@2.0.0":
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/eslint-config/-/@tencent/eslint-config-2.0.0.tgz"
  integrity sha512-ozG7xwicGB87tTWoXxlmBbAkijHzKxGuNF6yZ+bQlc7q18rN27o+5CxnH9txgeWumhoDuasHTKrRgQB8yNvhlg==
  dependencies:
    "@tencent/eslint-config-tencent" "^0.16.0"

"@tencent/eventbus@^1.0.8":
  version "1.0.8"
  resolved "https://mirrors.tencent.com/npm/@tencent/eventbus/-/@tencent/eventbus-1.0.8.tgz"
  integrity sha512-W8L+1LDfOp8+83GBPZJreEkOn/v2AyM8xaeFluELXuARHuyU1bCcHo4EGhnsWW6g855iC/GpKTayIquO2kjleQ==

"@tencent/font-select@1.0.6":
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/@tencent/font-select/-/@tencent/font-select-1.0.6.tgz#fff05179c8d1a08a800be1a9d87c8ad64d55a321"
  integrity sha512-eG+stgVw3/2n4EvxkKOuIj+KCySrjcPnNB5708I7vnie1tGUJfxIbFPfJCT2pOjIQqZaN0RXqjgYU22Nelo8hw==
  dependencies:
    "@types/react" "^16"
    "@types/react-dom" "^16"

"@tencent/formily-cms@^1.0.55":
  version "1.0.72"
  resolved "https://mirrors.tencent.com/npm/@tencent/formily-cms/-/@tencent/formily-cms-1.0.72.tgz"
  integrity sha512-f61l/agv1IXfOOobJrvhYHRJJrgKdiyXhDDyeqXjhpNT87H88LYdFFaXshH0zZ2wnx9ZYW1PFq2HoYmnpfLQqQ==
  dependencies:
    "@formily/core" "^2.1.4"
    "@formily/react" "^2.1.4"
    "@mi18n/react" "^5.15.1"
    "@tencent/cmsweb-component" "^0.2.15"
    "@tencent/formily-tea" "^1.0.11"
    "@tencent/gems-library" "^1.1.4"
    "@tencent/midas-util" "^1.0.53"
    "@tencent/pagedoo-icon" "^1.0.7"
    "@tencent/pagedoo-library" "^1.0.64"
    "@tencent/react-designable" "^1.0.7"
    "@tencent/tstate" "^1.0.16"
    axios "^1.6.7"
    cos-js-sdk-v5 "^1.6.0"
    react-color "^2.19.3"
    react-is "^18.2.0"
    react-quill "^2.0.0"
    stylis "^4.3.2"
    tinycolor2 "1.4.2"

"@tencent/formily-tea@^1.0.11":
  version "1.0.11"
  resolved "https://mirrors.tencent.com/npm/@tencent/formily-tea/-/@tencent/formily-tea-1.0.11.tgz"
  integrity sha512-zQ8sWD2yk+/gNWIavQNuHWAPnpGlCDczX5IFoHdQ02YBlYRR4+o/bBddgRM2ZwBL7FzM+lQNxKfKASJpNTVvSw==
  dependencies:
    "@formily/core" "^2.1.4"
    "@formily/react" "^2.1.4"
    "@mi18n/react" "^5.15.1"
    react-sortable-hoc "^1.11.0"

"@tencent/gems-ability@^0.1.3":
  version "0.1.3"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-ability/-/@tencent/gems-ability-0.1.3.tgz"
  integrity sha512-Ko2l8RUbFzu+0KROCKd9xrA2n6v/kPW5OkgE2huCIRrW/B0+eJvvWr39fWDpBILFE9EQSSzLRTdDjTq5A5zV0w==
  dependencies:
    axios "^0.27.2"
    lodash-es "^4.17.21"
    nanoid "^4.0.0"

"@tencent/gems-html-generator@^0.0.27":
  version "0.0.27"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-html-generator/-/@tencent/gems-html-generator-0.0.27.tgz"
  integrity sha512-TlUAU7mT4fIhg2FMnDOZ4rwxdh9RpFTBrrGydws9aZwPB7fDIWwmN7UdWEZ60QqD+MO7Ou3YWRzIApK1Fqx3fg==
  dependencies:
    axios "*"
    json5 "^2.2.2"

"@tencent/gems-library@^1.1.4":
  version "1.1.22"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-library/-/@tencent/gems-library-1.1.22.tgz"
  integrity sha512-OWhDg5n5TYyE3Uo7XX795+fzBfrkKyN0PgHr2/Mj3EWCIUVpF/GaRynql0fIWJK0V4R0j+fhyuzFVXbHVIVqWw==

"@tencent/gems-preview@^1.0.37":
  version "1.0.37"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-preview/-/@tencent/gems-preview-1.0.37.tgz"
  integrity sha512-jPI90kFlxbUxaA0UT16AiH1c6QoDwPvr87h54PY8MTQSm9l0qldS97YC1VzAVGfr/8EPCJy9wZjt3WVZWVOxlw==
  dependencies:
    "@tencent/eventbus" "^1.0.8"
    "@tencent/gems-renderer-react" "0.19.0"
    lodash-es "^4.17.21"
    patch-package "^6.4.7"
    postinstall-postinstall "^2.1.0"
    redux-zero "^5.0.2"

"@tencent/gems-renderer-core@0.19.0":
  version "0.19.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-renderer-core/-/@tencent/gems-renderer-core-0.19.0.tgz"
  integrity sha512-7o40kS916nOE9G1OJv4qA2vu3jvv+l0TRmn1uUbCwMfPZR7+hlCNbs2MqBaZgiI+aPvmpdzDVIdIWiDFE64zBQ==
  dependencies:
    "@tencent/gems-shared" "0.19.0"
    classnames "^2.2.1"
    intersection-observer-polyfill "^0.1.0"
    lodash "^4.17.21"
    promise-timeout "^1.3.0"
    qs "^6.9.4"
    ramda "^0.27.1"

"@tencent/gems-renderer-react@0.19.0":
  version "0.19.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-renderer-react/-/@tencent/gems-renderer-react-0.19.0.tgz"
  integrity sha512-pJ5fomY+YxvWyfTv2Cqdg89LpgWl+/d5o6rUHpPvBg1c5w60GSIWs4wPcej1oLBkKk/PCKFfMRne4gaqwXjpmA==
  dependencies:
    "@tencent/aegis-web-sdk" "^1.33.12"
    "@tencent/gems-renderer-core" "0.19.0"
    "@tencent/gems-shared" "0.19.0"
    classnames "^2.2.1"
    intersection-observer-polyfill "^0.1.0"
    lodash "^4.17.21"
    promise-timeout "^1.3.0"
    qs "^6.9.4"
    ramda "^0.27.1"

"@tencent/gems-shared@0.19.0":
  version "0.19.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/gems-shared/-/@tencent/gems-shared-0.19.0.tgz"
  integrity sha512-/VK9JXVCnZ3p13/yqc8BAduuQ/IEvMGM7HbsiYMjEg75NGnww+lRpQ9FqmOdKIyEun4pKw/X115GnCcC95Cj4g==
  dependencies:
    json-schema-defaults "^0.4.0"
    lodash "^4.17.21"
    promise-timeout "^1.3.0"
    ramda "^0.27.1"

"@tencent/midas-util@^1.0.53":
  version "1.0.53"
  resolved "https://mirrors.tencent.com/npm/@tencent/midas-util/-/@tencent/midas-util-1.0.53.tgz"
  integrity sha512-L/ptYbhSuApmj3soWVz1usCcQ3AQcmW9XhZBTP2+7Qlpa+YSTyQuP1d6RIl5kLe+ZkgthYvCrhsojzsr39BO/Q==
  dependencies:
    crypto-js "^4.0.0"
    lodash.foreach "^4.5.0"
    object-assign "^4.1.1"
    promise "^8.0.1"

"@tencent/od-animate@2.0.10":
  version "2.0.10"
  resolved "https://mirrors.tencent.com/npm/@tencent/od-animate/-/@tencent/od-animate-2.0.10.tgz#ba6e335c9da1086b89cb3bf95009fce5ba3e48ba"
  integrity sha512-scb1iUEOW6Ep8ZSP5WueP5REkxXfSclB314ZipcDJxKhou/xYb1qTZlEYlgQvDd4DYUZT2JYCDmuKt5BZ9FX3w==
  dependencies:
    classnames "^2.3.1"
    prop-types "15.x"
    rc-util "^5.18.1"

"@tencent/od-hooks@^0.0.38":
  version "0.0.38"
  resolved "https://mirrors.tencent.com/npm/@tencent/od-hooks/-/@tencent/od-hooks-0.0.38.tgz#0c003381dbb592139999f949d2a45c0a54587565"
  integrity sha512-cmKPF08yew0q+cp45PxxH938+xCg8TFw3ht3TSQdR3i+0swXloVq3jQ0x20ihxb0Q582fRWdea1gcT/XizgT3A==
  dependencies:
    intersection-observer "^0.12.0"
    lodash.debounce "^4.0.8"
    lodash.throttle "^4.1.1"
    resize-observer-polyfill "^1.5.1"

"@tencent/page_editor__types@^0.1.143":
  version "0.1.303"
  resolved "https://mirrors.tencent.com/npm/@tencent/page_editor__types/-/@tencent/page_editor__types-0.1.303.tgz"
  integrity sha512-Nk1YdbiQmo7jeu8qyBleKJWl23WrgLQiw2QG82l+tb409LmuewJp/DC5LlYrNsNtE6gFdeIShkUh8SYgQxGrdQ==

"@tencent/pagedoo-editor@0.0.79":
  version "0.0.79"
  resolved "https://mirrors.tencent.com/npm/@tencent/pagedoo-editor/-/@tencent/pagedoo-editor-0.0.79.tgz#695490aa0657af57cf24eb9b464326ccb7dda740"
  integrity sha512-mqeI/oI5JqEA9E1qUO7fhxGypNc6F8GfQV7TkUsDXVLwXLTbEbq9h+wGcX8LnfIG/ys3f5Klj2rPosrhZQdjWw==
  dependencies:
    "@ant-design/icons" "^4.3.0"
    "@babel/polyfill" "^7.12.1"
    "@tencent/aegis-web-sdk" "^1.33.12"
    "@tencent/climb-cms-sdk" "^1.1.40"
    "@tencent/eventbus" "^1.0.8"
    "@tencent/gems-preview" "^1.0.37"
    "@tencent/gems-renderer-core" "0.19.0"
    "@tencent/gems-renderer-react" "0.19.0"
    "@tencent/gems-shared" "0.19.0"
    "@tencent/pagedoo-icon" "^1.0.7"
    "@tencent/tea-component" "~2.7.6"
    "@tencent/tea-sr" "^1.1.2-beta.7"
    "@tencent/tstate" "^2.0.2"
    "@types/lodash" "^4.17.6"
    ahooks "^3.7.11"
    antd "^4.12.3"
    axios "^0.27.2"
    classnames "^2.2.1"
    copy-to-clipboard "^3.3.3"
    core-js "^3.1.4"
    events "^3.3.0"
    hotkeys-js "^3.8.1"
    html2canvas "^1.4.1"
    immer "6.0.9"
    json-schema-defaults "^0.4.0"
    lodash "^4.17.20"
    lodash-es "^4.17.21"
    lodash.template "^4.5.0"
    mockjs "^1.1.0"
    moment "^2.29.1"
    nanoid "^4.0.0"
    process "^0.11.2"
    promise-timeout "^1.3.0"
    qrcode "^1.5.4"
    query-string "^6.13.7"
    ramda "^0.27.1"
    react "^18.2.0"
    react-dom "^18.2.0"
    react-error-boundary "^4.1.2"
    react-fast-compare "^3.2.2"
    react-icons "^3.11.0"
    react-sortable-hoc "1.9.0"
    react-sortablejs "^6.1.4"
    react-use "^15.3.4"
    recoil "^0.7.7"
    redux-zero "^5.1.7"
    regenerator-runtime "^0.14.1"
    sortablejs "^1.15.2"
    tdesign-react "^1.1.17"
    throttle-debounce "^3.0.1"
    uuid "^8.3.1"

"@tencent/pagedoo-formily@^1.0.46":
  version "1.0.54"
  resolved "https://mirrors.tencent.com/npm/@tencent/pagedoo-formily/-/@tencent/pagedoo-formily-1.0.54.tgz"
  integrity sha512-3QK5R8DLvJWzQvkE352y8j+osRFtMwm03wTsneGNY5ikVCm1xtgH+8narnz4CKMFK7rqqreI0uoQVhmARjXvxw==
  dependencies:
    "@formily/core" "^2.2.29"
    "@formily/react" "^2.2.29"
    "@formily/tdesign-react" "^1.0.0-beta.15"
    "@monaco-editor/react" "4.4.5"
    "@tencent/page_editor__types" "^0.1.143"
    "@tencent/tea-component" "^2.7.6"
    ahooks "^3.7.0"
    antd "^4.22.5"
    axios "^0.27.2"
    nanoid "^4.0.0"
    react-error-boundary "^3.1.4"
    react-sortablejs "^6.1.4"
    sortablejs "^1.15.1"
    tdesign-react "^1.2.1"

"@tencent/pagedoo-icon@^1.0.7":
  version "1.0.9"
  resolved "https://mirrors.tencent.com/npm/@tencent/pagedoo-icon/-/@tencent/pagedoo-icon-1.0.9.tgz"
  integrity sha512-hUwCvLp0dYDYW3VEAp29dQUv1cn9qB0kX4WbKLjSC9enCb+cAG/6qLEdmP3tD9LPZziJz+lxCFseJRp2XW6vwQ==

"@tencent/pagedoo-library@^1.0.64", "@tencent/pagedoo-library@^1.0.65":
  version "1.0.66"
  resolved "https://mirrors.tencent.com/npm/@tencent/pagedoo-library/-/@tencent/pagedoo-library-1.0.66.tgz"
  integrity sha512-G5haOkrmBjghybiK2/LK4orlH1Fn6fwDwfuECeM2vAAMY1QZK/1T7tjH4jTUMJX7DvJr2DOMRsXgLGY/Ype9+g==
  dependencies:
    "@tencent/formily-cms" "^1.0.55"
    "@tencent/pagedoo-formily" "^1.0.46"
    "@tencent/tstate" "^1.0.21"
    react-router-dom "^6.8.2"

"@tencent/pagedoo-time-navigator@1.0.39":
  version "1.0.39"
  resolved "https://mirrors.tencent.com/npm/@tencent/pagedoo-time-navigator/-/@tencent/pagedoo-time-navigator-1.0.39.tgz#8f373b56d9adc05f44bb9bc402273581c43aac4c"
  integrity sha512-+WlsdAOqEcD5QSNkuFeEcUGEzyktz4iuYPfwPzH+sYuHzRzuwrqq2o1Wh7k2aNL0spLkzAZ2IcXkiUJkYN8ETg==
  dependencies:
    react-sortablejs "^6.1.4"
    tdesign-react "1.9.0"

"@tencent/pbmockts@^2.0.29":
  version "2.0.29"
  resolved "https://mirrors.tencent.com/npm/@tencent/pbmockts/-/@tencent/pbmockts-2.0.29.tgz#1e375c7898e35686f7ab2f51c105752e62becdcb"
  integrity sha512-sqREDEA1bn66DnmVUhu9KPTrSiexbGmQI+Sth9rS+DUlS0ruwFxG6cOObaSqlI7o66/TMcVbGwhJGt1f1eFryQ==
  dependencies:
    fixbug-protobufjs "^1.0.1"
    fs-extra "^11.2.0"
    glob "^7.2.3"
    listr2 "^8.2.4"
    md5 "^2.3.0"
    mockjs "^1.1.0"
    pkg-dir "^4.2.0"
    prettier "^3.0.3"
    protobufjs "^7.3.2"
    update-notifier "^5.1.0"

"@tencent/react-designable@^1.0.7":
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/@tencent/react-designable/-/@tencent/react-designable-1.0.7.tgz"
  integrity sha512-ee2r7XbD+FdzwGYAw71vEmKYtbrRMkYd64IwTY85ysg27Of/H9U33GtX3AaAGdKfj3yswNElw0OyM5iMxlJnmA==
  dependencies:
    ahooks "^3.7.8"

"@tencent/spa-clipper@^1.3.5":
  version "1.3.8"
  resolved "https://mirrors.tencent.com/npm/@tencent/spa-clipper/download/@tencent/spa-clipper-1.3.8.tgz#327f9fa0977eb5be3797b90461fd3e94866896d7"
  integrity sha1-Mn+foJd+tb43l7kEYf0+lIZoltc=

"@tencent/spa-pinyin@^1.3.0":
  version "1.3.1"
  resolved "https://mirrors.tencent.com/npm/@tencent/spa-pinyin/-/@tencent/spa-pinyin-1.3.1.tgz#bfe0c97f01c2889209c73ac831cafc46efe1bbf9"
  integrity sha512-dUZyh/m4VWvr0Yxbearwop193Cvi1qTQwNevV7VjSksYLVC70HLNUJQ0PG7uuYUDtwgYUxADEKxSxCt4MxqsrA==

"@tencent/spaui-alert@1.0.161-beta.0":
  version "1.0.161-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-alert/-/@tencent/spaui-alert-1.0.161-beta.0.tgz#05182c37306b67ca6ec6a13c7da8b572c7fbacc7"
  integrity sha512-JP1IHVHLqGWj7HK4/S2869LSOrsR9xudFmQY/ktInyZupG4ph4KbFVMxPztpFLc1QjWCqcUx8bo5akjOhjDJfw==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-alert@1.0.164-beta.0":
  version "1.0.164-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-alert/-/@tencent/spaui-alert-1.0.164-beta.0.tgz#0e1a91643238df2b810bb4a35cda8d827710689d"
  integrity sha512-DnJirt6QH4yu/ue7xErnt/+/pCQuzBmdXhhVXxJIDUFukod/R6jDF77OacVuPVsTKI1+CwYQiECzSeyU9cz68g==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-autocomplete@1.0.202-beta.0":
  version "1.0.202-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-autocomplete/-/@tencent/spaui-autocomplete-1.0.202-beta.0.tgz#9d8468e0c1b2bdaaefa2e548980bf8c92458a372"
  integrity sha512-oJyaqu5kzbvolPAH1DPTQZ71H54JZlUGBo71TSRdS7JNGgGuHBjOdXl1LV4+0AoJsuTaBsD5Wqk4TXnm9slAHA==
  dependencies:
    "@tencent/spa-pinyin" "^1.3.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-autocomplete@1.0.207-beta.0":
  version "1.0.207-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-autocomplete/-/@tencent/spaui-autocomplete-1.0.207-beta.0.tgz#810781952fd9b3ce8d80a03bb29734719d60802c"
  integrity sha512-gNxiuAY4w3aoASRcT48c62MMPh6ubNgFwRZNKy5b0e31+kowFV4GARuLhIrS8Y6sUBuimV9rHGpuW+/LKwdVmQ==
  dependencies:
    "@tencent/spa-pinyin" "^1.3.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-badge@1.0.149-beta.0":
  version "1.0.149-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-badge/-/@tencent/spaui-badge-1.0.149-beta.0.tgz#8cddf57898de4bad0ab781e6e426f87a411cbfa7"
  integrity sha512-Lipz/FfHv0SPAfgmpcXrjO5wjqR7v8m8brLyKpWOj0gY+AMI771drczvsC5UrITt4FGuDuVj0+ujFIBLu6lQnQ==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-badge@1.0.152-beta.0":
  version "1.0.152-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-badge/-/@tencent/spaui-badge-1.0.152-beta.0.tgz#3a8c2202fb44c20039587ca502fc7a68fd88527e"
  integrity sha512-VN1PAJ+OS3aHv9EuCFlRgiwIelcpWlTQmIO42CyNDjG31ZpY7jxY9TPAfJSOKFX8v4RWEzycRY9NZkFFRBVH7w==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-blockselector@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-blockselector/-/@tencent/spaui-blockselector-1.0.150-beta.0.tgz#3e8f12b6e588d64aea2332f1319294a1e492e3b0"
  integrity sha512-d7yWb6pxk9eFIkthx0BSVIhmWVkR1pKtWVzS0wTLhqICK6NsqV6VTOMBtUhM8DnstGY+mBfz7AeR1kgeQ3P5aw==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-blockselector@1.0.153-beta.0":
  version "1.0.153-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-blockselector/-/@tencent/spaui-blockselector-1.0.153-beta.0.tgz#01dd012ea09af0c732bcaef39390d92b4f5b9d83"
  integrity sha512-Kbhv/4ZMSEuLKw/hnachFswNrNOQEgtd3hWB/LRwz839+cSwzBb65URa5uZny90OoOmJBWBC0B2d2kMqIt3OYQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-breadcrumb@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-breadcrumb/-/@tencent/spaui-breadcrumb-1.0.151-beta.0.tgz#29f67426242679332d96b60a0c32d2f1ad531dff"
  integrity sha512-h6dx7OH+FPfbpPEtXI3Hkfh9w5fPQ1IOGFdQy1Du9QOqyyz2ICd1fsJ1/1vQwLjxLNhiv4Xx8rKrRZc16QCpFQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-breadcrumb@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-breadcrumb/-/@tencent/spaui-breadcrumb-1.0.154-beta.0.tgz#a602a332cc988a051dadb35b08544c29aaa88acf"
  integrity sha512-nTNMjS/nOnvdluZYEQv0mVVxWd6/Ks8yS8XnoALWjsYnhShjy9kQuQdXJUPxNq0B5YmhICY0UuCFSssFxg7xXQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-button@1.0.168-beta.0":
  version "1.0.168-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-button/-/@tencent/spaui-button-1.0.168-beta.0.tgz#b68f71de973543185694fcfd6cc020fd68229882"
  integrity sha512-ZfZSgC/T6GlBBqPoS/6symCXUU1/n3obnZ9RNEkdUmr7xoLXJ+oT3YkCLhe6Ydu4Lz4RdP2IN7NWzIB2D/Icgg==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-button@1.0.171-beta.0":
  version "1.0.171-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-button/-/@tencent/spaui-button-1.0.171-beta.0.tgz#659ab900a07aa019f15b4d52d00c5f7370cd3239"
  integrity sha512-sGxgiYJCd6Fm/7IzOG0VOxoA3Ih5HpdduJijVRkvnxR3HLPwfujSkYdZQhW41/ZeDcEdnrtbHDWMs1Pd4Dq6lQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-buttongroup@1.0.169-beta.0":
  version "1.0.169-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-buttongroup/-/@tencent/spaui-buttongroup-1.0.169-beta.0.tgz#b73151c75bc1e7a13ae0aaf68927a39a2c5df31c"
  integrity sha512-kX8zfRy36BqKs+/o3xp20NKevcp4eJZzflOfrrLWH+z66H1G6zE6KyQSQndn6Vn7zItAMbn7zBi4hkaFLLXNog==
  dependencies:
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-buttongroup@1.0.172-beta.0":
  version "1.0.172-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-buttongroup/-/@tencent/spaui-buttongroup-1.0.172-beta.0.tgz#2730e7c328532e41ea9492b2bd68267d73490096"
  integrity sha512-egaEwpz5YLbtvsORUIduswRMThVHuIe0ooP5PysxQ7FQUzwLkGQG3PnTKNQbPwAbr540JIf+Pc/b002S4MNJDQ==
  dependencies:
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-carousel@1.0.161-beta.0":
  version "1.0.161-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-carousel/-/@tencent/spaui-carousel-1.0.161-beta.0.tgz#0ef3cc4a5e1d96bc0a6132b84e87d130921f2041"
  integrity sha512-EODTzuf5B8u6ZoUDh0KKvfjJVsp17OaiLmQSAb8/0gByuzkB/0hEClL17tFTn6teKXsQW1D8xTqV83sRKfRj+A==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    "@tencent/spaui-viewer" "1.0.152-beta.0"
    classnames "^2.2.5"

"@tencent/spaui-carousel@1.0.164-beta.0":
  version "1.0.164-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-carousel/-/@tencent/spaui-carousel-1.0.164-beta.0.tgz#741638d75753015cf5fb2d39ba1b2752de18cecc"
  integrity sha512-jzRKsx1FP6w+i9IwImRxRkc0YXPGY5hie8NsvOtsEu5kugTrMnMxi89LT+7x/rv5fQP/IuF0xnEuMlXahI+I0w==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    "@tencent/spaui-viewer" "1.0.155-beta.0"
    classnames "^2.2.5"

"@tencent/spaui-chart@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-chart/-/@tencent/spaui-chart-1.0.151-beta.0.tgz#ee82c922c3ab4e7c97e1d4b4fbe544ff649ac7a0"
  integrity sha512-xnlKim4Ly3uOvF3XN8QSvs5JdVd/ZXkdFjLOW4yLaXmg7FqRsXCVVJ/a7oxf3j67H7oVUeD/F/fPPn0jPgTWMg==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-chart@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-chart/-/@tencent/spaui-chart-1.0.154-beta.0.tgz#a6cc37870887e3cb68c0c55ded7b2161c58c88cd"
  integrity sha512-jXkCoji2A0xxbktzKadh1pZ53KC2iqtT3yHo2INN+La2gNlr9nsIXKQs7ucNPyxULP1Se3Mhe+PpFRoIiGGFRg==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-checkbox@1.0.170-beta.0":
  version "1.0.170-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-checkbox/-/@tencent/spaui-checkbox-1.0.170-beta.0.tgz#5d6b3686c9522fa1b6d4186fadb44c0eb62acb73"
  integrity sha512-3NvY4+cNVuStctRACzU2wbnDxKShK3PNxxTogAqgJWp8LMW7RiKlB5RvODRNsws7pnXcf6+KDs7Bg2wYla2ijA==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-checkbox@1.0.173-beta.0":
  version "1.0.173-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-checkbox/-/@tencent/spaui-checkbox-1.0.173-beta.0.tgz#84817c3a60ce7e3a20ea7db8b6bdd77f6b00a8b3"
  integrity sha512-T69Mr/R2H/cCT+GZDefTNk0tXEudneW0D4y3fa6OHliYRzH1dFD+nB54wKa3uWxQ6VRWJ/w6f5YQk/wGpBqOUQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-checkboxgroup@1.0.171-beta.0":
  version "1.0.171-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-checkboxgroup/-/@tencent/spaui-checkboxgroup-1.0.171-beta.0.tgz#c67ac8c08e67f0d6b09c7492d25cf6ced911fd9e"
  integrity sha512-YBQiC+NkasfD9jFIX1KAqL1at+lvD/HOnImvk3RASgcFle424omSGIzksDTYIHcdBt190WGLDXJLcT7s/42Ljg==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.170-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-checkboxgroup@1.0.174-beta.0":
  version "1.0.174-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-checkboxgroup/-/@tencent/spaui-checkboxgroup-1.0.174-beta.0.tgz#526f91bfb16a40eb204f905bbf7795e539252782"
  integrity sha512-peCfI295DZ60YXQ/QJhF0GeuxkI2qyweXzDkDQMzIeTb6KhbKkCYvMEjCxFQYBfB2Z8df4g2YHD0pUzLUxhLQw==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.173-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-clipper@1.0.147-beta.0":
  version "1.0.147-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-clipper/-/@tencent/spaui-clipper-1.0.147-beta.0.tgz#2508e8797f2206d799b3cf560ec0651f70fb626d"
  integrity sha512-bngW7Zs37yVl9mzdSTGRXcJiUPAEcWWqxRgGD3RYgAmutBHHOw+gnET5Y6h8hozGcq9YVjYaYfPXuAPkDAri9A==
  dependencies:
    "@tencent/spa-clipper" "^1.3.5"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-clipper@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-clipper/-/@tencent/spaui-clipper-1.0.150-beta.0.tgz#a6335f8cadbc4ab15692f42e186d6ff853ea46f6"
  integrity sha512-HKDl7eqMmAE4YQ4CvYtrh3bGMpD+MVO0GIq1w78UWQm9aOmMdFM8NUlEhWxjKvZZkLV9fhSvPqTj21k8GoddIg==
  dependencies:
    "@tencent/spa-clipper" "^1.3.5"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-coder@1.0.147-beta.0":
  version "1.0.147-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-coder/-/@tencent/spaui-coder-1.0.147-beta.0.tgz#89d28aac6f615521bf7089a59b6ef7e8b1245180"
  integrity sha512-zhebJFF3y3HIfBpDs464yMSZDx2mXiE1OY4/Zf6YjwRxpxifxTLfVPXqwfdVfv2gTYYA88ySDzSoVKcHCE5jyQ==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-coder@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-coder/-/@tencent/spaui-coder-1.0.150-beta.0.tgz#d250823f6d1bfcff37168079e7fa91f7645b363f"
  integrity sha512-9Ry7SCIK9HzWwmcGvPptP80YOsteUDdVtXoCSARnjQlBhA8j8NmAnWP97Dy/gb1cQ1yZTFf/2gkrp7zDnyiJzw==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-collapse@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-collapse/-/@tencent/spaui-collapse-1.0.151-beta.0.tgz#c107e14d03770593cecc5fcaa265541b73c22464"
  integrity sha512-uI1CY1S0oBC2idWbqhb/JIK/R0BC6OVlfP3KJvMjw9xdHI9mpF7ePsgIxhYemPAmaiOPzKonOnNYtvHfdMCpXw==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-collapse@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-collapse/-/@tencent/spaui-collapse-1.0.154-beta.0.tgz#c64f940d8441385d238073d6b62991e980dfecfc"
  integrity sha512-6XHta1c+Ske2p9OmKbfqCDqaH5dWJjwwv4RIaTOGX7NRr/GCRJoUyww9Q8VEnfoQcNMfDbRZCyhpxF0N9LlRqA==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-datepicker@1.0.226-beta.0":
  version "1.0.226-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-datepicker/-/@tencent/spaui-datepicker-1.0.226-beta.0.tgz#d13a45ebbaf1a58f8baddd06928526aaf4fe8ee5"
  integrity sha512-qcIgD9TRvXRwVZUM91Zi/q5pbmACG3iT9AVNjzv/BkWhfIxlP6bKVXfid3iEtnGUx/4/75edSfINPrrCgEQF1Q==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-formgroup" "1.0.200-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-timepicker" "1.0.202-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-datepicker@1.0.232-beta.0":
  version "1.0.232-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-datepicker/-/@tencent/spaui-datepicker-1.0.232-beta.0.tgz#91935b3a443abc672460fad80d40e757deeafbc6"
  integrity sha512-agXyuY9LkE9vQ5V65LOb8v6OsoXpQNwsOYb124SIViO3OtdXyplZgWau/Adufkf11IXIeFecUPUcunroA8jjzw==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-formgroup" "1.0.205-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-timepicker" "1.0.207-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-decorate@^0.1.10":
  version "0.1.17"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-decorate/-/@tencent/spaui-decorate-0.1.17.tgz#a8eb08ba70d1029e9552dc6a97c564eb61354399"
  integrity sha512-HjVMH3RyZm4bYmAm5JN9De7m3Mu0hAzorcOcGxnUvSjFtNnW2SS2I/kpAfiGYOXGiS4ZDyZM0AS9kxMr3C7WAg==

"@tencent/spaui-devtool@^0.1.10", "@tencent/spaui-devtool@^0.1.7":
  version "0.1.10"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-devtool/download/@tencent/spaui-devtool-0.1.10.tgz#1c617187a723a621bb52260f83971116753e13f8"
  integrity sha1-HGFxh6cjpiG7UiYPg5cRFnU+E/g=

"@tencent/spaui-dialog@1.0.181-beta.0":
  version "1.0.181-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-dialog/-/@tencent/spaui-dialog-1.0.181-beta.0.tgz#d9e785d4f7ba36964536050b70120cb07a699365"
  integrity sha512-fO5X7UZyD4eyH3loTaxp1gtl6CaEAvTW17qzAJaehNLzHyY24nbhIF+u+ALoAkn0NGfLyDjQfVKG+bYsk8v3PQ==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    "@tencent/spaui-viewer" "1.0.152-beta.0"
    classnames "^2.2.5"

"@tencent/spaui-dialog@1.0.185-beta.0":
  version "1.0.185-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-dialog/-/@tencent/spaui-dialog-1.0.185-beta.0.tgz#b6c16fe1cb2e17630a66762ab4b3825c7c72f6f5"
  integrity sha512-1bv2GpoDB0yAc3PQLMCyehRwDTq4KrHeBodKVzEiGqPS7TKWc7MftU5EuXLcIu5fM6asnQOxS/CklT5Fl53w+Q==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    "@tencent/spaui-viewer" "1.0.155-beta.0"
    classnames "^2.2.5"

"@tencent/spaui-drawer@1.0.185-beta.0":
  version "1.0.185-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-drawer/-/@tencent/spaui-drawer-1.0.185-beta.0.tgz#2da1f4c44457c876b638e5358e7e2c702364bf34"
  integrity sha512-ssecVdz9qHhp6r3xHdWhrAu7jv5dJwLwXf7trYtzEYi072nYQTNlgOXiJ99wnE9PdYrwsm83rpiu/Gu2v1EBlw==
  dependencies:
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"
    rc-motion "2.4.6"

"@tencent/spaui-drawer@1.0.188-beta.0":
  version "1.0.188-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-drawer/-/@tencent/spaui-drawer-1.0.188-beta.0.tgz#f6ca797fbf145c99a7a7c410ad761ef479ddac22"
  integrity sha512-f9nQ9d6FUYmYaW5hDeNb/8sXaD2lDpclO9av24ohv1pL0pHv4mMV7t1XQg/2rPXbrpUD2wo+LzDGr9JD700/ww==
  dependencies:
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"
    rc-motion "2.4.6"

"@tencent/spaui-dropdown@1.0.172-beta.0":
  version "1.0.172-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-dropdown/-/@tencent/spaui-dropdown-1.0.172-beta.0.tgz#ca1df42a99078f350332b47d34e5ecd8ea4b7465"
  integrity sha512-NODlG01+9B2jmTuoe94K0TcFUkKBtn5VG6wxXX3kJY3vnd3Z1qe8rVkmyJe/dD2NSnI9LB0oG4UMPk7nQjoX8w==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-buttongroup" "1.0.169-beta.0"
    "@tencent/spaui-dropmenu" "1.0.154-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-dropdown@1.0.175-beta.0":
  version "1.0.175-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-dropdown/-/@tencent/spaui-dropdown-1.0.175-beta.0.tgz#c056425577c82984acb8f37a9431718b0f9c430a"
  integrity sha512-tHgShhgDmwXSgKN/izqy1jN+LPtX84RAqj3hVzAOD5XmSV3BVYN7KsIVgyh0v0Nv06lpPs9wjHtC1uz5EmZypg==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-buttongroup" "1.0.172-beta.0"
    "@tencent/spaui-dropmenu" "1.0.157-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-dropmenu@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-dropmenu/-/@tencent/spaui-dropmenu-1.0.154-beta.0.tgz#9f4cd2928a460f417d681ef0fc006c6d3abce9e7"
  integrity sha512-h+WP0Kg1ieoi4IogoCBg/mdODd6Zh3PtdXDYWHpDi2OzhR3kd0FHyZpmhFuUQOwkTXJIJ+eeTeP8AMKlFjTQaw==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-dropmenu@1.0.157-beta.0":
  version "1.0.157-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-dropmenu/-/@tencent/spaui-dropmenu-1.0.157-beta.0.tgz#a6bc543f81c2374399f429085d749eb5c4a3213f"
  integrity sha512-P+LTbOKMPFkwq6/Kjo13SvQbvpa+W4w0uhE4AW6/fKn0STH+Dz8Tab94ruw9qsW/JwOpTLRLTdgHk0YzuCbwQg==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-echarts@1.0.158-beta.0":
  version "1.0.158-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-echarts/-/@tencent/spaui-echarts-1.0.158-beta.0.tgz#6ef4fd524d40bf62da232fe08fef337e0038b4cb"
  integrity sha512-HdgpXdjeH+BbwRpRUi5svlTNKVPajoq/UxLoU6R93ZQbbisoNZD9al2n1+5MiSGt2//ajCKrOBeslATQ3B0K1g==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-echarts@1.0.161-beta.0":
  version "1.0.161-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-echarts/-/@tencent/spaui-echarts-1.0.161-beta.0.tgz#32d6d58ec889ba511a9dd8c8eeb203a5697af592"
  integrity sha512-iG89tyzTKXYJ7gc/MQRjfFBJj0plIpBSW8EyRBUGUh46m3BvVzy8MD8oHoUkLqT204wKNhIrjbrSIjKA2jBUcw==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-ellipsis@^0.1.0":
  version "0.1.5"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-ellipsis/download/@tencent/spaui-ellipsis-0.1.5.tgz#d629c222a8d01ba0a10151da616d085171e1453b"
  integrity sha1-1inCIqjQG6ChAVHaYW0IUXHhRTs=
  dependencies:
    "@tencent/spaui-popover" "0.1.110"
    "@tencent/spaui-utils" "^0.2.9"

"@tencent/spaui-empty@1.0.147-beta.0":
  version "1.0.147-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-empty/-/@tencent/spaui-empty-1.0.147-beta.0.tgz#a5bdb44ec85b443e784c2b3ab83c056744fab88e"
  integrity sha512-deKTJVaILI+THrOVCGcAv+2Mc6E/yaDDN9PRcLoINE+CZsj8qr647mrZYoW0l3Jft3uqkReNFe8732TaoPFMGg==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-empty@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-empty/-/@tencent/spaui-empty-1.0.150-beta.0.tgz#b48741fd8681ab2b25b763929cc3026b64ed83a3"
  integrity sha512-rhzCANJkJURfGDyQf2lzc5/qy/myYW6EcSg8Iuigiry7ITYLFBFmXtagP99qt5h3ET2jsDz06nVW/+awWxOhNw==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-formgroup@1.0.200-beta.0":
  version "1.0.200-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-formgroup/-/@tencent/spaui-formgroup-1.0.200-beta.0.tgz#4ab31f02689e9295841b294413d07652fa5d8292"
  integrity sha512-sqoSqNmarE1rcdr1t/pjdq9FkjwP+yl7uEdTAHTBkp6Ug4lZshdvzeh3EKv812fT7Yj3P04TX2rAqupcIajYeQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-formgroup@1.0.205-beta.0":
  version "1.0.205-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-formgroup/-/@tencent/spaui-formgroup-1.0.205-beta.0.tgz#053b47756ee70eeb2f4356a16ecd827ab9bb0d25"
  integrity sha512-iWoTuDSMKvGEz3UjxBgSySmjQk9CJV20UZhm8dvmfUE2F+67r9i0q4NWZu+gckM3Pk6i15a6NAsWaZqiKiVtLA==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-icon@0.1.59":
  version "0.1.59"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-icon/download/@tencent/spaui-icon-0.1.59.tgz#e806607f6250cfd73539ab498693c43f6d4df596"
  integrity sha1-6AZgf2JQz9c1OatJhpPEP21N9ZY=
  dependencies:
    "@tencent/spaui-theme" "0.0.4"
    "@tencent/spaui-utils" "^0.2.109"
    classnames "^2.2.5"

"@tencent/spaui-icon@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-icon/-/@tencent/spaui-icon-1.0.150-beta.0.tgz#c8d9ddc57885745be142454efff65e2095da40db"
  integrity sha512-I77kVFUAHH/23R0VAGFxnDc8peAfn5kVQ1a9SJvrbml4M57kCl3PBRB4k6ShUJFLQ8V8ueB/pVpidYvPU8gX6g==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-icon@1.0.153-beta.0":
  version "1.0.153-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-icon/-/@tencent/spaui-icon-1.0.153-beta.0.tgz#fcd56ceaa2a12a04b2410232b4fefa52b3c3e8fe"
  integrity sha512-bQ1PsbNyh8AxcpL8s3/bMV+G53HQ+W3dSTD4bf/TIzfXExGCmJuk9T635P3SkSxKLe68ySxlEKBwXS81dHxWyg==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-input@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-input/-/@tencent/spaui-input-1.0.154-beta.0.tgz#ce0206023a678bffb52f8f6411921cf237ecc117"
  integrity sha512-EYFl56+nUef54VqnTlaKkhuTRTJFPXLcnTxyumK2lUBZxr3hAW45IFUxegyOAeBpNQZtA/tEWIs+GrTYt9TnlQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-input@1.0.157-beta.0":
  version "1.0.157-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-input/-/@tencent/spaui-input-1.0.157-beta.0.tgz#0a5bdc08f9d22ab349bd00be91f49c4ab2ce375f"
  integrity sha512-mD7dtWE9sudafAEfqJSRM2t6HUrmSRgBizzVANyaizKGR73i/OhWQ/VdTWCLdaMfrsqMF3/NheKAlF1j7OC9QQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-keyday@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-keyday/-/@tencent/spaui-keyday-1.0.151-beta.0.tgz#dacee0771c08428c47ec773e1a2b932c4bb99260"
  integrity sha512-Q0zXLw4m96e2OiVzfBcHBLSKdjICqhQQgUnIXwpkqQs5UPKXXhdeikhLjGECE9q5NusDJNeoQJ0ahFquYJQZFg==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-keyday@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-keyday/-/@tencent/spaui-keyday-1.0.154-beta.0.tgz#29518179cbe680b2215dd5fd7c3d4928bb8346c0"
  integrity sha512-ZTcuK/uG2107wGiLTT3IBOyNiQC0GSPzz1X1zNm/u2L5isjpMenZkE52xdphyEsrv7wMdt3e13oD5im8Z7a76A==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-labels@1.0.168-beta.0":
  version "1.0.168-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-labels/-/@tencent/spaui-labels-1.0.168-beta.0.tgz#5c50fc1046a8ed1a5b2808009d38ff587f37ba7f"
  integrity sha512-ZJmqnJ+Gks/aOB2M+y0MKtQ+XtAYg0+Omb3bkBSHdePxWOdAZgO/mBLn7xeLUKgqEXTHxaqpYYxBUQ3SRJKEPQ==
  dependencies:
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-labels@1.0.171-beta.0":
  version "1.0.171-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-labels/-/@tencent/spaui-labels-1.0.171-beta.0.tgz#c6bcd7f406101ca6105f5fc44bfe35239b152b57"
  integrity sha512-A+AD4vSrClKuEF4BajdQvjIUzm2PgeCzvAE/WkpVrhkjYEi9X6R/UjuUV8Q6TxV/4Fzb+UIdXmfBxkd1yHSi/w==
  dependencies:
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-list@1.0.162-beta.0":
  version "1.0.162-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-list/-/@tencent/spaui-list-1.0.162-beta.0.tgz#609aa3df4a15721ddcfd1dee6301e9ced3ab8f71"
  integrity sha512-d98+w0qBKdT3B1mOYkJ/e0w1e0qAF+cAWmHhoC+Y91h821cupFj6HxEY9+DtRGyjmsFADtXdJiG4Y5QXsy8YWA==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-list@1.0.165-beta.0":
  version "1.0.165-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-list/-/@tencent/spaui-list-1.0.165-beta.0.tgz#8a38d10210af3c1273090d02f8bcede33a6b017c"
  integrity sha512-KsTVS+LE9T+DSypJRHJy9/Ky+91LPQT7oYjbOLOCu28pxvZQd5drz2UfSBwm2vKPx4PsVerq0UpRJqaSpkV1mw==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-loading@1.0.148-beta.0":
  version "1.0.148-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-loading/-/@tencent/spaui-loading-1.0.148-beta.0.tgz#cece7b584bcd65397abb797f4bb2a678a7d31b0c"
  integrity sha512-Dn0Fq2mHux5iHPF8si84UY2GmEM1DHH4KmnPFwBZIZYv5b/o233zRKZ3UV+ZcY8lOds6aHPmB5hnD4nTXrlqKQ==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-loading@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-loading/-/@tencent/spaui-loading-1.0.151-beta.0.tgz#aba44ed0bd9216730c96c84fc8bf771cf7d2b895"
  integrity sha512-YKM73b10PH28i+T3eBfIE3WhFvQG2Bg2Uzh/Vf6Ue6OUKmVZVgSs8aBTp3KKiZBMccRzzz9ytP/YQ5SzZAxGbQ==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-menu@1.0.171-beta.0":
  version "1.0.171-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-menu/-/@tencent/spaui-menu-1.0.171-beta.0.tgz#77bf5d29c08bf0f64688f2653ce2c43f7b3c31fd"
  integrity sha512-qqq3pnyd1aDU13Ajyfl0XWPVBQT0kFt/Sbb3Sr+GFhTBsNazgkr7EVPECvCvbJq5vC/lZdOnSKv4BriA8vMS7A==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-menu@1.0.174-beta.0":
  version "1.0.174-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-menu/-/@tencent/spaui-menu-1.0.174-beta.0.tgz#c6be5d5d880109eec47a2689c8231cff148755dd"
  integrity sha512-2jIOB/3oe2v34SQeiuh8ErqTR6nfQRcP0L3vm7BO3G4qph6D2b4QvZaWJBW0pKgF67SFjYvnRlJOXPpusYInjQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-message@1.0.179-beta.0":
  version "1.0.179-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-message/-/@tencent/spaui-message-1.0.179-beta.0.tgz#db3f7d071a9254e0791074ef25c0c980eeb34e06"
  integrity sha512-9qaq72cyKVNL3+iEc2PNyPT+LKtm0WW1kNnC8cfPkJN4hVx5HXzTwnAPHjuc3BjYRmS+SzaW1HU2VxPFkJGBIQ==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-alert" "1.0.161-beta.0"
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-message@1.0.182-beta.0":
  version "1.0.182-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-message/-/@tencent/spaui-message-1.0.182-beta.0.tgz#08258f1de8ae123af5e769ee414c940c2e2ea316"
  integrity sha512-jLGLhL6vfmdwtbiyb7EUQgJqVao07jt9jfZ2IFkM4sxIQEql/U8LbGlxi+33ho/hVeSX/PvGgPXmFJLX1n/vbw==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-alert" "1.0.164-beta.0"
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-pagebar@1.0.203-beta.0":
  version "1.0.203-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-pagebar/-/@tencent/spaui-pagebar-1.0.203-beta.0.tgz#da51d904da27a474a1fba4623e387f5a9673cce0"
  integrity sha512-/1JRdGPVwL1BKOhiGCxLNT+MrFFEJf2o9lkKfp0WZiknr1R0oagqQyhdhMlViZ4hJkgFkgYwKcBIsGkH6X9Dyw==
  dependencies:
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-formgroup" "1.0.200-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-pagebar@1.0.208-beta.0":
  version "1.0.208-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-pagebar/-/@tencent/spaui-pagebar-1.0.208-beta.0.tgz#383ceb2a1ad7cb6157945fb7f602b1c4ec6cb485"
  integrity sha512-oiaDdgUlXrwR+E27wMga8mxifv1TY/pJzV8E57G3fZUHlg3qmPzGGtIszcw0R0u/yvmvvOt5MjE5HGr0041urg==
  dependencies:
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-formgroup" "1.0.205-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-popover@0.1.110":
  version "0.1.110"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-popover/download/@tencent/spaui-popover-0.1.110.tgz#08d883cf500b69f99613cddefef53f07df17c65e"
  integrity sha1-CNiDz1ALafmWE83e/vU/B98Xxl4=
  dependencies:
    "@tencent/spaui-icon" "0.1.59"
    "@tencent/spaui-portal" "0.1.42"
    "@tencent/spaui-theme" "0.0.4"
    "@tencent/spaui-utils" "^0.2.109"
    classnames "^2.2.5"

"@tencent/spaui-popover@1.0.164-beta.0":
  version "1.0.164-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-popover/-/@tencent/spaui-popover-1.0.164-beta.0.tgz#b59fa6f7adc50e0721f1b7fbca6f0478c3b968b4"
  integrity sha512-6lRDY8VAUqq/brrDJmPCo77TFKlaPxW2+5mQsULNRqCjZiwIGjTK5K2XDlTKRgJMsBX6wamHQ7c9rRKH/EyJXQ==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-popover@1.0.167-beta.0":
  version "1.0.167-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-popover/-/@tencent/spaui-popover-1.0.167-beta.0.tgz#09447c212d80537d9bdfee88bb0f7926250ec2cd"
  integrity sha512-ARoFUkWVgsaHZkKvj3Cm706KthMveGSgy5CE+kcFehA6vCxT+3JL9+FStcr9Yul5XJLNkJl7V2ONB+e0a53Hng==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-portal@0.1.42":
  version "0.1.42"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-portal/download/@tencent/spaui-portal-0.1.42.tgz#a57aa671d0a5614e203cfdc54585731b4946e5df"
  integrity sha1-pXqmcdClYU4gPP3FRYVzG0lG5d8=
  dependencies:
    "@tencent/spaui-theme" "0.0.4"
    "@tencent/spaui-utils" "^0.2.109"
    classnames "^2.2.5"

"@tencent/spaui-portal@1.0.147-beta.0":
  version "1.0.147-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-portal/-/@tencent/spaui-portal-1.0.147-beta.0.tgz#3d443f59dc66f0594f219602d355dbc7c4132669"
  integrity sha512-6n/QLD/l9WvlroYUYSFEGGYmaEh07Ys7DxxqbZ4bp+LkywlirgHhhwyNQrtxqzyznJIDFfoyKZ6UFGQXPjOz+w==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-portal@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-portal/-/@tencent/spaui-portal-1.0.150-beta.0.tgz#358e37ca3c4dc8d69658a523ca56f917fffd3a1c"
  integrity sha512-opASoNUEF9RU1yrngSdTiz6EFKMrBP7xtjWP8HiVejfCkPEGpuchLorlaTkvi7B8f+BisqYHysEi/nCSa1HBLw==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-radio@1.0.165-beta.0":
  version "1.0.165-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-radio/-/@tencent/spaui-radio-1.0.165-beta.0.tgz#e4ce17f564e194ffd8aafebb8bffc7fd8ec7b6e0"
  integrity sha512-rCnNap/XPJQAdYSrNqXXJgiHz4WqgS7TYlnIUAjRrP4rfkOrGeeEwfn2W61rK8HPNZk+Xqfuzi9UnBgolN8dVQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-radio@1.0.168-beta.0":
  version "1.0.168-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-radio/-/@tencent/spaui-radio-1.0.168-beta.0.tgz#661e5782df7c632028bb0fc5156ed7fc59611dc2"
  integrity sha512-PdJjq5BHmjeNUiOc3fk/mmQdVdf+MxGYnptTPUvjdit1Sz/YJrJZtYRxeLapboXl85XVcfxNfwDK1q0ADh99pg==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-radiogroup@1.0.167-beta.0":
  version "1.0.167-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-radiogroup/-/@tencent/spaui-radiogroup-1.0.167-beta.0.tgz#8a6b7c9a193d984f445a3beec776a30a1d78cc86"
  integrity sha512-cc/N4Ouyi0EXX486yFdyc3yzDlqvjgYAzIlcc32Fo4nasmiPfdICK96jf1Fqtt3Hat0y3uuC4vi/AhHsFAvTQg==
  dependencies:
    "@tencent/spaui-radio" "1.0.165-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-radiogroup@1.0.170-beta.0":
  version "1.0.170-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-radiogroup/-/@tencent/spaui-radiogroup-1.0.170-beta.0.tgz#fbb18220c131be75e91cd917184ef4f98b4f0622"
  integrity sha512-npsr2Ylp4mnZV4554B1CoSj3zaL+u+xrfJOymxivVhOI/wgNtbg4vuT0SpnJq3rauf0pVPaSDjIm00inRbo3vA==
  dependencies:
    "@tencent/spaui-radio" "1.0.168-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-rating@1.0.148-beta.0":
  version "1.0.148-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-rating/-/@tencent/spaui-rating-1.0.148-beta.0.tgz#28cf4e9011c3efcd2737d7957f6135af4f13e218"
  integrity sha512-UgMATJCEqNugNdGv1Iz0gD1qBS6SC1ef1o1mU6/ieVmkrXHUvR1z7J938T8S1uV7jOFgrSe//kMASQ/lvXseAw==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-rating@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-rating/-/@tencent/spaui-rating-1.0.151-beta.0.tgz#728e1884e06ae855d0868baf92a0e82708c23caf"
  integrity sha512-v0QdU+9fmId2cLUYjul7gFj9j+PRhUyfiGVyNEKUbcndgkxt0Zw237MPP9YdDezIfHyb2HEb+bPNqqjL7URkBg==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-select-tree@1.0.166-beta.0":
  version "1.0.166-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-select-tree/-/@tencent/spaui-select-tree-1.0.166-beta.0.tgz#3e060313c9b8f650cee67743bcc3bd57197c0227"
  integrity sha512-y/ZLXu1RqmRac29r0yeWybr6whwOvWkQ5qIUhfkpLmcl6ZfsNpB5JhH6FKDAbghsqTixOl/7BXjEgQxsWxCf+A==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-tree" "1.0.184-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-select-tree@1.0.171-beta.0":
  version "1.0.171-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-select-tree/-/@tencent/spaui-select-tree-1.0.171-beta.0.tgz#49341ac5bbb93dd81351053a6857ef92b1e5fb8e"
  integrity sha512-Sul90OHKWUAVskyRmYoMARgAJvHHS7t9qAGYpV5DIURJ7avHpmWiGbeYjK5GCF5RDJ9tZL6ivOKVnwFo8GHWJQ==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-tree" "1.0.187-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-select@1.0.202-beta.0":
  version "1.0.202-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-select/-/@tencent/spaui-select-1.0.202-beta.0.tgz#c4b8e40f97ae8c0d0e817ebd4eee8077c6729c30"
  integrity sha512-rTk9FihrnuHJA7gMuKqW9OaujLazjRQn7oj+27sfhfnWQr9bHf7AcXleR3Qc+kQedclWXyLh7ODQruQs2dEJRg==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.170-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-labels" "1.0.168-beta.0"
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-select@1.0.208-beta.0":
  version "1.0.208-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-select/-/@tencent/spaui-select-1.0.208-beta.0.tgz#b72e2b33b261a6497f9dc4d9a37efd7958adb8b4"
  integrity sha512-3zDPiKS5gWawWaxf5B99AWCXEkMdwFLl0rx6OcDg4KRealtVEqKM6N3mvRPT+6wLnY7L+IhJ44WfQ/Q9si0BSw==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.173-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-labels" "1.0.171-beta.0"
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-slider@1.0.165-beta.0":
  version "1.0.165-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-slider/-/@tencent/spaui-slider-1.0.165-beta.0.tgz#da197c2f881c0ba69ac7722a65457c7ba44a6233"
  integrity sha512-8wLG7bTGue6ctLxURkZIwqxEJt/PlA690jME0v9IaPduxh3foYchQFB3HGCj5HlvlI725Co8ps8jGEFOcATkKw==
  dependencies:
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-slider@1.0.168-beta.0":
  version "1.0.168-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-slider/-/@tencent/spaui-slider-1.0.168-beta.0.tgz#f01e78d1cafd8f254b6083fed71fd640f0eb5caa"
  integrity sha512-uYu5u7IsxaDksufMbsWrGsnFABsfwW2oRsM/G2k+h/6JIF5/de29FZHi2wd/J+ed968B+H4tKq+PpxbDEYh0dg==
  dependencies:
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-space@1.0.149-beta.0":
  version "1.0.149-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-space/-/@tencent/spaui-space-1.0.149-beta.0.tgz#855546b633b31e073869db7ef247e18b284ee715"
  integrity sha512-ikfukUiNvSySEhZM5viRHyiE9Zat23mkolbNLXMRkrXNNaxe8uqbZByBal3aTmz8rJYI29QOMIsq7bx1BH3y3w==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-space@1.0.152-beta.0":
  version "1.0.152-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-space/-/@tencent/spaui-space-1.0.152-beta.0.tgz#8ca8124e62527c63d902e7f70259d35613d2a68d"
  integrity sha512-fb2oZbdhG2eFTNUDKrnCDasBWRPjTHzdBQFE+3zpsPCoKt40Ce2DpjbK0jaqg7rlvnfMce44F6tR0/ewpUMM2A==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-switch@1.0.151-beta.0":
  version "1.0.151-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-switch/-/@tencent/spaui-switch-1.0.151-beta.0.tgz#65bf0dff425ef0bce759ef03766fe7c95270b385"
  integrity sha512-wXtiEtSOvR/SN1yW24kEa+0h8nteIouTbKZeQgs9NFMxDUYlu2uqW1mGTCFGF3mq3AJBxllKxIKAKBep1pIOog==
  dependencies:
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-switch@1.0.154-beta.0":
  version "1.0.154-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-switch/-/@tencent/spaui-switch-1.0.154-beta.0.tgz#c1aa1f1ad747c0a3ef084b2a13f846dc81f367b5"
  integrity sha512-ftFLC2NoQC7gLbjmc48sATYvm0E5pC27OgR89z/V6MM6O7QZijK4DaumchVcjXipbQC7FavljEZ61451YL1t3g==
  dependencies:
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-table@1.0.223-beta.0":
  version "1.0.223-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-table/-/@tencent/spaui-table-1.0.223-beta.0.tgz#ad33d6e95306fe6649abed6afb2868bc7d9d6778"
  integrity sha512-CbKeoYr3zr2lR6+CiLH35dv/GnnU575ZMuOw85KioiF3ORKS6usIE+k8DxCO8qsf24KFYDKyZa2oLuR2kfFExQ==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.170-beta.0"
    "@tencent/spaui-ellipsis" "^0.1.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-radio" "1.0.165-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-table@1.0.228-beta.0":
  version "1.0.228-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-table/-/@tencent/spaui-table-1.0.228-beta.0.tgz#af3ee05bebec61af391fdf57cf1ea938456bbb1b"
  integrity sha512-9JtE0aq4v019ScpDEmETJwtTv4+fov7RqL0rpnJ/s3bHEg5e5xvnfhviKY8d39RbWa/+OHBUDCYEQ/+n4j/Tzg==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.173-beta.0"
    "@tencent/spaui-ellipsis" "^0.1.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-radio" "1.0.168-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-tabs@1.0.209-beta.0":
  version "1.0.209-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-tabs/-/@tencent/spaui-tabs-1.0.209-beta.0.tgz#7baa6ccc41a42901029d06ba4179a5de11443423"
  integrity sha512-Gpht08gBEDv94qEA98bMUWNGXJXglYzhj9uQMiOaEYSgx+0BzeIQstZmiGj58zyhdaDd4rnwxrU2Lpgxp53OiQ==
  dependencies:
    "@tencent/spaui-dialog" "1.0.181-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"
    natives "^1.1.6"
    types "^0.1.1"

"@tencent/spaui-tabs@1.0.215-beta.0":
  version "1.0.215-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-tabs/-/@tencent/spaui-tabs-1.0.215-beta.0.tgz#798ad7185c3972ada78bd4a003e8071e3733fa44"
  integrity sha512-yW3Iq+z/jJaZaJEyuBGRh6w7LUMtS6b3g4lpV/RyTF0mYBP4S6os9K20jmKUtqKJKd0spsXeoYxaj26rYM6c1Q==
  dependencies:
    "@tencent/spaui-dialog" "1.0.185-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"
    natives "^1.1.6"
    types "^0.1.1"

"@tencent/spaui-task@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-task/-/@tencent/spaui-task-1.0.150-beta.0.tgz#e13aeb647b0a21fb12a0b53e4f0f0dbc935329e5"
  integrity sha512-9tE/0LH1g8RJ4rFDY7yjdwyiEA4MmUTpU0wngfvbuKLO5nvTAtJ4GPtumTff8OUIBsEcJqFQNSVWPTuxM1YDxg==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-task@1.0.153-beta.0":
  version "1.0.153-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-task/-/@tencent/spaui-task-1.0.153-beta.0.tgz#73ea23c6b23c48149ad758a1c3e32a1efcb11812"
  integrity sha512-YAFL9LXfmqUwuoWt39b2TsSjea00XG5tXxcGiVtV4+n7NGxi4iHK1YTNX3vV02y0qcZ/96m/qLVoRp1a7Q1/Qg==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-theme@0.0.4":
  version "0.0.4"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-theme/download/@tencent/spaui-theme-0.0.4.tgz#3e25eba240b2a42947053ea00d38a24564c0455d"
  integrity sha1-PiXrokCypClHBT6gDTiiRWTARV0=

"@tencent/spaui-theme@1.0.144-beta.0":
  version "1.0.144-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-theme/-/@tencent/spaui-theme-1.0.144-beta.0.tgz#98471904256ee468672859b9c2263023de4a7e93"
  integrity sha512-CkufRJgyKG1LJVEaXbWVw6oCMOp6hSeYTSNm2bAAzSUwPj6VhyFEY4hjAw3oG+YhzXv9QAkmyom8D5w1B/pDig==

"@tencent/spaui-theme@1.0.147-beta.0":
  version "1.0.147-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-theme/-/@tencent/spaui-theme-1.0.147-beta.0.tgz#6e11c4cd59477c56dd8a8032f454d7a3f3e95feb"
  integrity sha512-DO6RZb0lyEe5G/F7zhs5U+SjUsIg3tGQgx1jjKUIgiICfk5ce2tQzSKzFdqU2qBrIPstziBbY1XWtihS+mn71g==

"@tencent/spaui-timepicker@1.0.202-beta.0":
  version "1.0.202-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-timepicker/-/@tencent/spaui-timepicker-1.0.202-beta.0.tgz#358db7c3d279fc2f45f9c2762cca96954a8e4570"
  integrity sha512-VRvSfB0kbyqUraGYtld2IOgDu3kmf2L3n7sl5SEb9vlFOt8z8VVsqj8YyB3AcEk5a0DnDA22bCCsZM86Aq498g==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-formgroup" "1.0.200-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-timepicker@1.0.207-beta.0":
  version "1.0.207-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-timepicker/-/@tencent/spaui-timepicker-1.0.207-beta.0.tgz#4d297baaa0da44eecae4461211c5cd9157bfb4bc"
  integrity sha512-m02N8thsKpSakKPvf3bFjSrLT9IZ6jlc5mDrV9/eWjkA4a/2P6V6R83wJSUFeBr/YxL0V6Xt80+QIaVms/bPdg==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-formgroup" "1.0.205-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-timeroutineselector@1.0.147-beta.0":
  version "1.0.147-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-timeroutineselector/-/@tencent/spaui-timeroutineselector-1.0.147-beta.0.tgz#ef9084b4adb99085d72cb157f44b0fc7fcaef29f"
  integrity sha512-RirUkZNlRTP3gVj3FGwjV9K51lubgpOOQhQL7AwF5U3UbaUKiuxi3c1TzrM4JvhJ3QM23lxFMNXiP59Ps9MLEg==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-timeroutineselector@1.0.150-beta.0":
  version "1.0.150-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-timeroutineselector/-/@tencent/spaui-timeroutineselector-1.0.150-beta.0.tgz#93a88c1f9a942478966b844dfddbb16cdd942ee7"
  integrity sha512-gvzArSIexYfcuMXUQnDUQ3O4erRej1DJCVB8J0xh04pGuUkufm82TyIS/Iymf+oeY7LJwcrZJNMHf1v2lj2VeQ==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-tooltip@1.0.165-beta.0":
  version "1.0.165-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-tooltip/-/@tencent/spaui-tooltip-1.0.165-beta.0.tgz#36aa10a1ba9982cd5cb27581d64244de9dbca576"
  integrity sha512-J0dZcvanuqr+8zKeo2AMxHxUJBXj/ZBQ/VsdDq9WfQDWe1Lj1q0drx+b02p8oL6DTW4c3EyJdB7e8UPogOqQrQ==
  dependencies:
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-tooltip@1.0.168-beta.0":
  version "1.0.168-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-tooltip/-/@tencent/spaui-tooltip-1.0.168-beta.0.tgz#79efd8a5e3896671adba1eb70c0a7f203e969085"
  integrity sha512-EcsalpGEFruEFDj4U5Nc/R8KZDnthCK6oduaygVvGnIWRLLqmpK/pKZ5dhsDhQeTYIWFLz1v9wVbQPOzX10sKg==
  dependencies:
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-topnav@1.0.149-beta.0":
  version "1.0.149-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-topnav/-/@tencent/spaui-topnav-1.0.149-beta.0.tgz#a1b6dd8f6a3e122c8d8f2e035d1bfafb862a4709"
  integrity sha512-AVg3DTLERidjOn2f7g1C2CdlLKTVsmlrrbW/cclx7VajFPT9Fc/C1yAVknQinSq8Spc1bL/R9FZ7V/bKTdgiew==
  dependencies:
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-topnav@1.0.152-beta.0":
  version "1.0.152-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-topnav/-/@tencent/spaui-topnav-1.0.152-beta.0.tgz#e9e83d900620db821f4d1ddf5fc5fb6b6f956330"
  integrity sha512-Asz8fsgF0fArR/axVigGFk9gLhWOFjg2QRBzTX57j8IrApNRR7Lfgg1aaEhyhZgaKlYEsMANUHVIAzYXgykZvQ==
  dependencies:
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-tree@1.0.184-beta.0":
  version "1.0.184-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-tree/-/@tencent/spaui-tree-1.0.184-beta.0.tgz#120ea5df08311b0c5452d4dd0ccbf8d7a2cf8093"
  integrity sha512-RuqS70Kj6WnNTyZJ+t6CRelj9luJJyAg7yLvaTrQTr2+90zu0mF3wLRE+cB562M0xIIJTt+wjDCvHdKjhNmaZQ==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.170-beta.0"
    "@tencent/spaui-devtool" "^0.1.7"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-radio" "1.0.165-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-tree@1.0.187-beta.0":
  version "1.0.187-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-tree/-/@tencent/spaui-tree-1.0.187-beta.0.tgz#441a8bf23bf282d544cc96d339f5b5d1ed84d112"
  integrity sha512-yuWxyOyoV1oPug4dxiIRR0aLlp4iGJhqarWihU+Kcg9Peo4GeY37/r0Xp2yQb0rhtm3ZXOXq81Kfai5ix6V6Og==
  dependencies:
    "@tencent/spaui-checkbox" "1.0.173-beta.0"
    "@tencent/spaui-devtool" "^0.1.7"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-radio" "1.0.168-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-uploader@1.0.180-beta.0":
  version "1.0.180-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-uploader/-/@tencent/spaui-uploader-1.0.180-beta.0.tgz#683eb446967091d255979698a7a02068ab04d528"
  integrity sha512-S28aXyQ5slIcRmMb6x1/HpBoL6y89Y7dSH2qNToaUI5hEHiXiqGakKsLSEkVkXHsD9q8aTsWtxhRaO4s6TUkHw==
  dependencies:
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-uploader@1.0.183-beta.0":
  version "1.0.183-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-uploader/-/@tencent/spaui-uploader-1.0.183-beta.0.tgz#bbf3cabb4c2d09c16faa526129750275b507a8f6"
  integrity sha512-b11yrZn2lz54+KfmqW8AwacLjWXZs2Bvn/4qJc6ygPAbYm8aBqVgaH8YFGpW0liePiPZFGwdPUd9rUZ/z9hwsA==
  dependencies:
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-userpicker@1.0.212-beta.0":
  version "1.0.212-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-userpicker/-/@tencent/spaui-userpicker-1.0.212-beta.0.tgz#16f88a470a007cd147dd67e36d3a3b50e2fe06ba"
  integrity sha512-STjgyvJoG/aEXQRTgp8T0Nsz1HTVjL1+svzpC2bQpXGp5GASOV8pzJytykuVIOIuN5vlgq6Q10W7QENMg4M11w==
  dependencies:
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-checkbox" "1.0.170-beta.0"
    "@tencent/spaui-coder" "1.0.147-beta.0"
    "@tencent/spaui-formgroup" "1.0.200-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-userpicker@1.0.217-beta.0":
  version "1.0.217-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-userpicker/-/@tencent/spaui-userpicker-1.0.217-beta.0.tgz#75d25dd65c36521c72e5eac714962ebbaf465e08"
  integrity sha512-HwiEjK0WmAzcyRUP2snpEelP/wurPZOUAo9NNeuo5msXnlLICzo91ylwXBVJT9bkKbjFaNL27pnZLqNnzpSDiA==
  dependencies:
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-checkbox" "1.0.173-beta.0"
    "@tencent/spaui-coder" "1.0.150-beta.0"
    "@tencent/spaui-formgroup" "1.0.205-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-utils@^0.2.109", "@tencent/spaui-utils@^0.2.128", "@tencent/spaui-utils@^0.2.130", "@tencent/spaui-utils@^0.2.9":
  version "0.2.130"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-utils/-/@tencent/spaui-utils-0.2.130.tgz#91f3dbb989c8eab748262b872b55c502fb0a1440"
  integrity sha512-knj9DWJMo8byJ2jYy6kLZlP3wiP2m+6jhbsbCMjftaTK5nOaQpPX2Ac6bT/oj0JwSGQKmtdvRmgR+i6WjZj33A==
  dependencies:
    "@tencent/spaui-decorate" "^0.1.10"
    "@tencent/spaui-devtool" "^0.1.10"
    classnames "^2.2.5"
    prop-types "^15.6.0"

"@tencent/spaui-viewer@1.0.152-beta.0":
  version "1.0.152-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-viewer/-/@tencent/spaui-viewer-1.0.152-beta.0.tgz#99394ea27ad5f6c73e3908f198bae3f362ad905a"
  integrity sha512-4Sc0DzlozfVSJglDlrm854352Mi8MDP0Uz7J9kiMmG6MPfrtleDzJqPcOK+HR6zKZG6DBHjEAATuG+q3dwD+5Q==
  dependencies:
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-viewer@1.0.155-beta.0":
  version "1.0.155-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-viewer/-/@tencent/spaui-viewer-1.0.155-beta.0.tgz#9fc7702e01a02579674635dc0973c3c7b04ee232"
  integrity sha512-LfkbVRKZ6H6+mkccLe7tRILFHNndU5L1+WZj/vBxkaG1z4xqBjTQH6e3uxBDUI6zze26U258kzjAthwl6z6BUA==
  dependencies:
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui-watermark@1.0.156-beta.0":
  version "1.0.156-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-watermark/-/@tencent/spaui-watermark-1.0.156-beta.0.tgz#9c5ff48785d40391962629eb744af09f20af64c3"
  integrity sha512-tu49kB27rEE/NQJ/v6fjdcukXVrVaNNPESAoqTMdgly9NNiSTZvD6hfajII9SDgm6u9ZovWfoH0g+nMMFe/7Sw==
  dependencies:
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-utils" "^0.2.128"
    classnames "^2.2.5"

"@tencent/spaui-watermark@1.0.159-beta.0":
  version "1.0.159-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui-watermark/-/@tencent/spaui-watermark-1.0.159-beta.0.tgz#30b50d96ef7c6ddcbe690c1db8ce93d5916e25ac"
  integrity sha512-ANaOABOZ5DtGdMCDf4roiHMT4L9fmCWSiYS3wHKzX8fSk5fsbFSyQOrSV1kQu9j8sWFh2ehtFLNwrCvdL/qjMg==
  dependencies:
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-utils" "^0.2.130"
    classnames "^2.2.5"

"@tencent/spaui@3.0.335-beta.0":
  version "3.0.335-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui/-/@tencent/spaui-3.0.335-beta.0.tgz#e0bae653d2161dad6895f9d173089ae4dbaf5007"
  integrity sha512-WiFV4/pdjjvmfokG8H1k4ODTPwuF8enBruCzgg/ecV00uBQQs7jQv+VDAtbGpGueRxnTPJkMknJTfBXOSC0zsA==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-alert" "1.0.164-beta.0"
    "@tencent/spaui-autocomplete" "1.0.207-beta.0"
    "@tencent/spaui-badge" "1.0.152-beta.0"
    "@tencent/spaui-blockselector" "1.0.153-beta.0"
    "@tencent/spaui-breadcrumb" "1.0.154-beta.0"
    "@tencent/spaui-button" "1.0.171-beta.0"
    "@tencent/spaui-buttongroup" "1.0.172-beta.0"
    "@tencent/spaui-carousel" "1.0.164-beta.0"
    "@tencent/spaui-chart" "1.0.154-beta.0"
    "@tencent/spaui-checkbox" "1.0.173-beta.0"
    "@tencent/spaui-checkboxgroup" "1.0.174-beta.0"
    "@tencent/spaui-clipper" "1.0.150-beta.0"
    "@tencent/spaui-coder" "1.0.150-beta.0"
    "@tencent/spaui-collapse" "1.0.154-beta.0"
    "@tencent/spaui-datepicker" "1.0.232-beta.0"
    "@tencent/spaui-dialog" "1.0.185-beta.0"
    "@tencent/spaui-drawer" "1.0.188-beta.0"
    "@tencent/spaui-dropdown" "1.0.175-beta.0"
    "@tencent/spaui-dropmenu" "1.0.157-beta.0"
    "@tencent/spaui-echarts" "1.0.161-beta.0"
    "@tencent/spaui-empty" "1.0.150-beta.0"
    "@tencent/spaui-formgroup" "1.0.205-beta.0"
    "@tencent/spaui-icon" "1.0.153-beta.0"
    "@tencent/spaui-input" "1.0.157-beta.0"
    "@tencent/spaui-keyday" "1.0.154-beta.0"
    "@tencent/spaui-labels" "1.0.171-beta.0"
    "@tencent/spaui-list" "1.0.165-beta.0"
    "@tencent/spaui-loading" "1.0.151-beta.0"
    "@tencent/spaui-menu" "1.0.174-beta.0"
    "@tencent/spaui-message" "1.0.182-beta.0"
    "@tencent/spaui-pagebar" "1.0.208-beta.0"
    "@tencent/spaui-popover" "1.0.167-beta.0"
    "@tencent/spaui-portal" "1.0.150-beta.0"
    "@tencent/spaui-radio" "1.0.168-beta.0"
    "@tencent/spaui-radiogroup" "1.0.170-beta.0"
    "@tencent/spaui-rating" "1.0.151-beta.0"
    "@tencent/spaui-select" "1.0.208-beta.0"
    "@tencent/spaui-select-tree" "1.0.171-beta.0"
    "@tencent/spaui-slider" "1.0.168-beta.0"
    "@tencent/spaui-space" "1.0.152-beta.0"
    "@tencent/spaui-switch" "1.0.154-beta.0"
    "@tencent/spaui-table" "1.0.228-beta.0"
    "@tencent/spaui-tabs" "1.0.215-beta.0"
    "@tencent/spaui-task" "1.0.153-beta.0"
    "@tencent/spaui-theme" "1.0.147-beta.0"
    "@tencent/spaui-timepicker" "1.0.207-beta.0"
    "@tencent/spaui-timeroutineselector" "1.0.150-beta.0"
    "@tencent/spaui-tooltip" "1.0.168-beta.0"
    "@tencent/spaui-topnav" "1.0.152-beta.0"
    "@tencent/spaui-tree" "1.0.187-beta.0"
    "@tencent/spaui-uploader" "1.0.183-beta.0"
    "@tencent/spaui-userpicker" "1.0.217-beta.0"
    "@tencent/spaui-viewer" "1.0.155-beta.0"
    "@tencent/spaui-watermark" "1.0.159-beta.0"

"@tencent/spaui@^3.0.328-beta.0":
  version "3.0.328-beta.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/spaui/-/@tencent/spaui-3.0.328-beta.0.tgz#f4d104e84138de6b1cc8e52741f511586f376298"
  integrity sha512-qZ1DzWMzQx9uVZkABoKIvVBPg5kLFPS4Z2IVxRJ+JIbZjB2hojZwjIeO/zZCLq/4PecEXOSh5TUIjGnEbJoK8w==
  dependencies:
    "@tencent/od-animate" "2.0.10"
    "@tencent/spaui-alert" "1.0.161-beta.0"
    "@tencent/spaui-autocomplete" "1.0.202-beta.0"
    "@tencent/spaui-badge" "1.0.149-beta.0"
    "@tencent/spaui-blockselector" "1.0.150-beta.0"
    "@tencent/spaui-breadcrumb" "1.0.151-beta.0"
    "@tencent/spaui-button" "1.0.168-beta.0"
    "@tencent/spaui-buttongroup" "1.0.169-beta.0"
    "@tencent/spaui-carousel" "1.0.161-beta.0"
    "@tencent/spaui-chart" "1.0.151-beta.0"
    "@tencent/spaui-checkbox" "1.0.170-beta.0"
    "@tencent/spaui-checkboxgroup" "1.0.171-beta.0"
    "@tencent/spaui-clipper" "1.0.147-beta.0"
    "@tencent/spaui-coder" "1.0.147-beta.0"
    "@tencent/spaui-collapse" "1.0.151-beta.0"
    "@tencent/spaui-datepicker" "1.0.226-beta.0"
    "@tencent/spaui-dialog" "1.0.181-beta.0"
    "@tencent/spaui-drawer" "1.0.185-beta.0"
    "@tencent/spaui-dropdown" "1.0.172-beta.0"
    "@tencent/spaui-dropmenu" "1.0.154-beta.0"
    "@tencent/spaui-echarts" "1.0.158-beta.0"
    "@tencent/spaui-empty" "1.0.147-beta.0"
    "@tencent/spaui-formgroup" "1.0.200-beta.0"
    "@tencent/spaui-icon" "1.0.150-beta.0"
    "@tencent/spaui-input" "1.0.154-beta.0"
    "@tencent/spaui-keyday" "1.0.151-beta.0"
    "@tencent/spaui-labels" "1.0.168-beta.0"
    "@tencent/spaui-list" "1.0.162-beta.0"
    "@tencent/spaui-loading" "1.0.148-beta.0"
    "@tencent/spaui-menu" "1.0.171-beta.0"
    "@tencent/spaui-message" "1.0.179-beta.0"
    "@tencent/spaui-pagebar" "1.0.203-beta.0"
    "@tencent/spaui-popover" "1.0.164-beta.0"
    "@tencent/spaui-portal" "1.0.147-beta.0"
    "@tencent/spaui-radio" "1.0.165-beta.0"
    "@tencent/spaui-radiogroup" "1.0.167-beta.0"
    "@tencent/spaui-rating" "1.0.148-beta.0"
    "@tencent/spaui-select" "1.0.202-beta.0"
    "@tencent/spaui-select-tree" "1.0.166-beta.0"
    "@tencent/spaui-slider" "1.0.165-beta.0"
    "@tencent/spaui-space" "1.0.149-beta.0"
    "@tencent/spaui-switch" "1.0.151-beta.0"
    "@tencent/spaui-table" "1.0.223-beta.0"
    "@tencent/spaui-tabs" "1.0.209-beta.0"
    "@tencent/spaui-task" "1.0.150-beta.0"
    "@tencent/spaui-theme" "1.0.144-beta.0"
    "@tencent/spaui-timepicker" "1.0.202-beta.0"
    "@tencent/spaui-timeroutineselector" "1.0.147-beta.0"
    "@tencent/spaui-tooltip" "1.0.165-beta.0"
    "@tencent/spaui-topnav" "1.0.149-beta.0"
    "@tencent/spaui-tree" "1.0.184-beta.0"
    "@tencent/spaui-uploader" "1.0.180-beta.0"
    "@tencent/spaui-userpicker" "1.0.212-beta.0"
    "@tencent/spaui-viewer" "1.0.152-beta.0"
    "@tencent/spaui-watermark" "1.0.156-beta.0"

"@tencent/sr-ui-component@^0.2.35":
  version "0.2.43"
  resolved "https://mirrors.tencent.com/npm/@tencent/sr-ui-component/-/@tencent/sr-ui-component-0.2.43.tgz"
  integrity sha512-+ih9KZXvUlXM71O4CmDxxOHOEIb9VdiXILjBYpKdtAth0fwTIvINX8BiwnktzE7quu0uEzXlTYXJj+Jaldgreg==
  dependencies:
    "@hookform/error-message" "0.0.5"
    ahooks "^3.7.2"
    array-move "^3.0.1"
    big.js "^6.0.1"
    caniuse-lite "^1.0.30001275"
    classnames "^2.2.6"
    hoist-non-react-statics "^3.3.2"
    immer "^8.0.4"
    lodash-es "^4.17.21"
    query-string "^6.13.6"
    react "^16.13.1"
    react-animate-height "^2.0.23"
    react-beautiful-dnd "12.0.0"
    react-color "^2.19.3"
    react-custom-scrollbars "^4.2.1"
    react-dom "^16.13.1"
    react-helmet-async "^1.0.6"
    react-hook-form "^6.15.5"
    react-qmap "^0.1.9"
    react-sortable-hoc "^1.11.0"
    react-transition-group "^4.4.1"
    use-debounce "^3.4.3"

"@tencent/tacc-typings@0.2.1":
  version "0.2.1"
  resolved "https://mirrors.tencent.com/npm/@tencent/tacc-typings/-/@tencent/tacc-typings-0.2.1.tgz#f45b7bb1083c27093202a40f460ef10a4e72af84"
  integrity sha512-s9LCetyc+GzY0FyvAev0+2VbeFmoBp5jon925rvFMoDqmVj0jKAgMPn6rBB1Gn52B0fqTOsLxDu2Hw/gI8jbfA==

"@tencent/tea-component@2.7.4-beta.23":
  version "2.7.4-beta.23"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-component/-/@tencent/tea-component-2.7.4-beta.23.tgz"
  integrity sha512-ig4MI3zB069vYh+bDG18hQ21LKcCb6LvTwkka50CBzB/3cou2+LLLUYfaCQnhbEf10uOVKaDTuDnndLDiok1Dg==
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.1"
    "@types/react-transition-group" "=4.2.3"
    "@types/react-window" "^1.8.1"
    classnames "^2.2.6"
    clone "^2.1.2"
    eventemitter3 "^4.0.0"
    hoist-non-react-statics "^3.3.0"
    moment "^2.24.0"
    popper.js "^1.16.1-lts"
    react-copy-to-clipboard "^5.0.2"
    react-dropzone "^11.2.0"
    react-onclickoutside "6.10.0"
    react-popper "1.3.11"
    react-transition-group "^4.4.1"
    react-window "^1.8.5"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.25"
    tslib "^2.3.1"
    warning "^4.0.2"

"@tencent/tea-component@^2.7.6":
  version "2.8.0"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-component/-/@tencent/tea-component-2.8.0.tgz"
  integrity sha512-qrgPfpLKYfMesoNUS9zRQKU/MBftxxfp1SxtiNh26/hrdtZ4VroLfvHXKIP0rZJNkTycqlfg1o/2773avdoV7w==
  dependencies:
    "@tencent/tea-icons-react" "^1.0.48"
    "@tencent/tea-material-nav" ">=0.1.1"
    "@types/hoist-non-react-statics" "^3.3.1"
    "@types/react-transition-group" "=4.2.3"
    "@types/react-window" "^1.8.1"
    change-case "^4.1.2"
    classnames "^2.2.6"
    clone "^2.1.2"
    eventemitter3 "^4.0.0"
    hoist-non-react-statics "^3.3.0"
    moment "^2.24.0"
    nanoid "^3.3.7"
    popper.js "^1.16.1-lts"
    react-copy-to-clipboard "^5.0.2"
    react-dropzone "^11.2.0"
    react-onclickoutside "6.10.0"
    react-popper "1.3.11"
    react-transition-group "^4.4.1"
    react-window "^1.8.5"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.25"
    tslib "^2.3.1"
    warning "^4.0.2"

"@tencent/tea-component@^2.8.1-rc.31":
  version "2.8.1-rc.45"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-component/-/@tencent/tea-component-2.8.1-rc.45.tgz"
  integrity sha512-OfkKCSIkL9T/DGCVlV1seWGRlQrLY35d04vTRIohnNJOV+WIvFlDi/wpP3YYGDgjW/OXpqDEPA85MOVABFtp+Q==
  dependencies:
    "@tencent/tea-icons-react" "^1.0.48"
    "@tencent/tea-material-nav" ">=0.1.1"
    "@types/hoist-non-react-statics" "^3.3.1"
    "@types/react-transition-group" "=4.2.3"
    "@types/react-window" "^1.8.1"
    change-case "^4.1.2"
    classnames "^2.2.6"
    clone "^2.1.2"
    eventemitter3 "^4.0.0"
    hoist-non-react-statics "^3.3.0"
    moment "^2.24.0"
    nanoid "^3.3.7"
    popper.js "^1.16.1-lts"
    react-copy-to-clipboard "^5.0.2"
    react-dropzone "^11.2.0"
    react-onclickoutside "6.10.0"
    react-popper "1.3.11"
    react-transition-group "^4.4.1"
    react-window "^1.8.5"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.25"
    tslib "^2.3.1"
    warning "^4.0.2"

"@tencent/tea-component@~2.7.6":
  version "2.7.9"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-component/-/@tencent/tea-component-2.7.9.tgz#74e260daad7d4a544f809f3a63b531331846781e"
  integrity sha512-vhlwbf5I+embV1CcYvSakQNSaTYKUakXFT9Ei4q856xFFxsSDfOTljK+JHaS3HjXNh1UPLddyKeL81h8+FJ1Zg==
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.1"
    "@types/react-transition-group" "=4.2.3"
    "@types/react-window" "^1.8.1"
    classnames "^2.2.6"
    clone "^2.1.2"
    eventemitter3 "^4.0.0"
    hoist-non-react-statics "^3.3.0"
    moment "^2.24.0"
    popper.js "^1.16.1-lts"
    react-copy-to-clipboard "^5.0.2"
    react-dropzone "^11.2.0"
    react-onclickoutside "6.10.0"
    react-popper "1.3.11"
    react-transition-group "^4.4.1"
    react-window "^1.8.5"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.25"
    tslib "^2.3.1"
    warning "^4.0.2"

"@tencent/tea-icons-react@^1.0.48":
  version "1.0.50"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-icons-react/-/@tencent/tea-icons-react-1.0.50.tgz"
  integrity sha512-Md+GflSgastu0DwNY//07K9vW+/sdywBoi0F0RYnYYfLg/EjEErAkPo4LBZpmAEIZMbLDGNqtOwIvujgRuflRw==
  dependencies:
    classnames "^2.2.6"

"@tencent/tea-material-nav@>=0.1.1":
  version "0.1.1"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-material-nav/-/@tencent/tea-material-nav-0.1.1.tgz"
  integrity sha512-opdBVRh8nw2Yg7a7nZL1D4GcBoxXWuTNpvpZcZxTxFzgZzmSbGKmfJRVcCDJWRhJ7OdU39XjPdlnR3I8yX+XJQ==
  dependencies:
    "@emotion/css" "^11.11.2"
    "@tencent/tea-component" "^2.8.1-rc.31"
    classnames "^2.5.1"
    react "^17.0.2"
    react-dom "^17.0.2"

"@tencent/tea-sr@^1.1.2-beta.7":
  version "1.1.2-beta.7"
  resolved "https://mirrors.tencent.com/npm/@tencent/tea-sr/-/@tencent/tea-sr-1.1.2-beta.7.tgz"
  integrity sha512-iT7q/aVsiC/+o/JDVXEAEJAvxI0Pn/WMWiurJ1X/YtLKHtcT/wqc3tu9XfXJF2Um3zwcFiwfEcyBcyhMslH9uw==

"@tencent/tstate@^1.0.16", "@tencent/tstate@^1.0.21":
  version "1.0.21"
  resolved "https://mirrors.tencent.com/npm/@tencent/tstate/-/@tencent/tstate-1.0.21.tgz"
  integrity sha512-KbOHikId9FUc+IAOOa3/o6iKtTXroO9419YCMePdbV5YVp2nH4WbOe5qGQtaiFOjiWGOf58w7NgC1+hu9T9T2Q==
  dependencies:
    "@tencent/eventbus" "^1.0.8"

"@tencent/tstate@^2.0.2":
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/@tencent/tstate/-/@tencent/tstate-2.0.2.tgz"
  integrity sha512-NOPodntMFs9mvIP64mo64+9cIoJ4TD/yXAST3AFZY8fOvNLpHWwvOTIybkebzX3WOqx7EbWIobp3V2uPu/o39w==
  dependencies:
    "@tencent/eventbus" "^1.0.8"

"@tootallnate/once@2":
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/@tootallnate/once/-/once-2.0.0.tgz"
  integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==

"@tsconfig/node10@^1.0.7":
  version "1.0.11"
  resolved "https://mirrors.tencent.com/npm/@tsconfig/node10/-/node10-1.0.11.tgz"
  integrity sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://mirrors.tencent.com/npm/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==

"@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "https://mirrors.tencent.com/npm/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.8"
  resolved "https://mirrors.tencent.com/npm/@types/babel__generator/-/babel__generator-7.6.8.tgz"
  integrity sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://mirrors.tencent.com/npm/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.20.5"
  resolved "https://mirrors.tencent.com/npm/@types/babel__traverse/-/babel__traverse-7.20.5.tgz"
  integrity sha512-WXCyOcRtH37HAUkpXhUduaxdm82b4GSlyTqajXviN4EfiuPgNYR109xMCKvpl6zPIpua0DGlMEDCq+g8EdoheQ==
  dependencies:
    "@babel/types" "^7.20.7"

"@types/big.js@^6.2.2":
  version "6.2.2"
  resolved "https://mirrors.tencent.com/npm/@types/big.js/-/big.js-6.2.2.tgz#69422ec9ef59df1330ccfde2106d9e1159a083c3"
  integrity sha512-e2cOW9YlVzFY2iScnGBBkplKsrn2CsObHQ2Hiw4V1sSyiGbgWL8IyqE3zFi1Pt5o1pdAtYkDAIsF3KKUPjdzaA==

"@types/crypto-js@^4.2.2":
  version "4.2.2"
  resolved "https://mirrors.tencent.com/npm/@types/crypto-js/-/crypto-js-4.2.2.tgz"
  integrity sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==

"@types/estree@1.0.6":
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/@types/estree/-/estree-1.0.6.tgz#628effeeae2064a1b4e79f78e81d87b7e5fc7b50"
  integrity sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==

"@types/fabric@^5.3.8":
  version "5.3.8"
  resolved "https://mirrors.tencent.com/npm/@types/fabric/-/fabric-5.3.8.tgz"
  integrity sha512-NorXLdAVb1y9Ph2PhDyd5sknmwIm2gqegQRMeGEY29wVPqWgNqlAC8P89V6LZVESWFjAvHcZpFjKMhM7W4Ii0Q==

"@types/highlight-words-core@^1.2.3":
  version "1.2.3"
  resolved "https://mirrors.tencent.com/npm/@types/highlight-words-core/-/highlight-words-core-1.2.3.tgz#7ff626b068ae1f936d113fa9e178a6117dd08232"
  integrity sha512-PWNU/NR0CaYEsK38mcCTyDzeS2TlEGK9kRhRMz1i86jVAe836ZlA3gl6QYpu+CG6IpfNKTgWpEnJuRededvC0g==

"@types/hoist-non-react-statics@^3.3.0", "@types/hoist-non-react-statics@^3.3.1":
  version "3.3.5"
  resolved "https://mirrors.tencent.com/npm/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.5.tgz"
  integrity sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/js-cookie@2.2.6":
  version "2.2.6"
  resolved "https://mirrors.tencent.com/npm/@types/js-cookie/-/js-cookie-2.2.6.tgz#f1a1cb35aff47bc5cfb05cb0c441ca91e914c26f"
  integrity sha512-+oY0FDTO2GYKEV0YPvSshGq9t7YozVkgvXLty7zogQNuCxBhT9/3INX9Q7H1aRZ4SUDRXAKlJuA4EA5nTt7SNw==

"@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://mirrors.tencent.com/npm/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://mirrors.tencent.com/npm/@types/json5/-/json5-0.0.29.tgz"
  integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==

"@types/lodash-es@^4.17.12":
  version "4.17.12"
  resolved "https://mirrors.tencent.com/npm/@types/lodash-es/-/lodash-es-4.17.12.tgz"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.17.6":
  version "4.17.7"
  resolved "https://mirrors.tencent.com/npm/@types/lodash/-/lodash-4.17.7.tgz"
  integrity sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA==

"@types/long@^4.0.1":
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/@types/long/-/long-4.0.2.tgz"
  integrity sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==

"@types/minimist@^1.2.0":
  version "1.2.5"
  resolved "https://mirrors.tencent.com/npm/@types/minimist/-/minimist-1.2.5.tgz"
  integrity sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==

"@types/mockjs@^1.0.10":
  version "1.0.10"
  resolved "https://mirrors.tencent.com/npm/@types/mockjs/-/mockjs-1.0.10.tgz"
  integrity sha512-SXgrhajHG7boLv6oU93CcmdDm0HYRiceuz6b+7z+/2lCJPTWDv0V5YiwFHT2ejE4bQqgSXQiVPQYPWv7LGsK1g==

"@types/node@*", "@types/node@>=13.7.0":
  version "20.12.11"
  resolved "https://mirrors.tencent.com/npm/@types/node/-/node-20.12.11.tgz"
  integrity sha512-vDg9PZ/zi+Nqp6boSOT7plNuthRugEKixDv5sFTIpkE89MmNtEArAShI4mxuX2+UrLEe9pxC1vm2cjm9YlWbJw==
  dependencies:
    undici-types "~5.26.4"

"@types/node@20.5.1":
  version "20.5.1"
  resolved "https://mirrors.tencent.com/npm/@types/node/-/node-20.5.1.tgz"
  integrity sha512-4tT2UrL5LBqDwoed9wZ6N3umC4Yhz3W3FloMmiiG4JwmUJWpie0c7lcnUNd4gtMKuDEO4wRVS8B6Xa0uMRsMKg==

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"
  resolved "https://mirrors.tencent.com/npm/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz"
  integrity sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==

"@types/path-browserify@^1.0.3":
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/@types/path-browserify/-/path-browserify-1.0.3.tgz#25de712d4def94b3901f033c30d3d3bd16eba8d3"
  integrity sha512-ZmHivEbNCBtAfcrFeBCiTjdIc2dey0l7oCGNGpSuRTy8jP6UVND7oUowlvDujBy8r2Hoa8bfFUOCiPWfmtkfxw==

"@types/prop-types@*":
  version "15.7.12"
  resolved "https://mirrors.tencent.com/npm/@types/prop-types/-/prop-types-15.7.12.tgz"
  integrity sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==

"@types/qrcode@^1.5.5":
  version "1.5.5"
  resolved "https://mirrors.tencent.com/npm/@types/qrcode/-/qrcode-1.5.5.tgz"
  integrity sha512-CdfBi/e3Qk+3Z/fXYShipBT13OJ2fDO2Q2w5CIP5anLTLIndQG9z6P1cnm+8zCWSpm5dnxMFd/uREtb0EXuQzg==
  dependencies:
    "@types/node" "*"

"@types/quill@^1.3.10":
  version "1.3.10"
  resolved "https://mirrors.tencent.com/npm/@types/quill/-/quill-1.3.10.tgz"
  integrity sha512-IhW3fPW+bkt9MLNlycw8u8fWb7oO7W5URC9MfZYHBlA24rex9rs23D5DETChu1zvgVdc5ka64ICjJOgQMr6Shw==
  dependencies:
    parchment "^1.1.2"

"@types/react-beautiful-dnd@^13.1.8":
  version "13.1.8"
  resolved "https://mirrors.tencent.com/npm/@types/react-beautiful-dnd/-/react-beautiful-dnd-13.1.8.tgz"
  integrity sha512-E3TyFsro9pQuK4r8S/OL6G99eq7p8v29sX0PM7oT8Z+PJfZvSQTx4zTQbUJ+QZXioAF0e7TGBEcA1XhYhCweyQ==
  dependencies:
    "@types/react" "*"

"@types/react-dom@^16":
  version "16.9.25"
  resolved "https://mirrors.tencent.com/npm/@types/react-dom/-/react-dom-16.9.25.tgz#fc6440aaae3d2d3aa10f6afeb7f1b0c4a55d5e31"
  integrity sha512-ZK//eAPhwft9Ul2/Zj+6O11YR6L4JX0J2sVeBC9Ft7x7HFN7xk7yUV/zDxqV6rjvqgl6r8Dq7oQImxtyf/Mzcw==

"@types/react-dom@^18.2.22":
  version "18.2.25"
  resolved "https://mirrors.tencent.com/npm/@types/react-dom/-/react-dom-18.2.25.tgz"
  integrity sha512-o/V48vf4MQh7juIKZU2QGDfli6p1+OOi5oXx36Hffpc9adsHeXjVp8rHuPkjd8VT8sOJ2Zp05HR7CdpGTIUFUA==
  dependencies:
    "@types/react" "*"

"@types/react-reconciler@^0.26.7":
  version "0.26.7"
  resolved "https://mirrors.tencent.com/npm/@types/react-reconciler/-/react-reconciler-0.26.7.tgz#0c4643f30821ae057e401b0d9037e03e8e9b2a36"
  integrity sha512-mBDYl8x+oyPX/VBb3E638N0B7xG+SPk/EAMcVPeexqus/5aTpTphQi0curhhshOqRrc9t6OPoJfEUkbymse/lQ==
  dependencies:
    "@types/react" "*"

"@types/react-reconciler@^0.28.0":
  version "0.28.9"
  resolved "https://mirrors.tencent.com/npm/@types/react-reconciler/-/react-reconciler-0.28.9.tgz#d24b4864c384e770c83275b3fe73fba00269c83b"
  integrity sha512-HHM3nxyUZ3zAylX8ZEyrDNd2XZOnQ0D5XfunJF5FLQnZbHHYq4UWvW1QfelQNXv1ICNkwYhfxjwfnqivYB6bFg==

"@types/react-redux@^7.1.20":
  version "7.1.33"
  resolved "https://mirrors.tencent.com/npm/@types/react-redux/-/react-redux-7.1.33.tgz"
  integrity sha512-NF8m5AjWCkert+fosDsN3hAlHzpjSiXlVy9EgQEmLoBhaNXbmyeGs/aj5dQzKuF+/q+S7JQagorGDW8pJ28Hmg==
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"
    redux "^4.0.0"

"@types/react-transition-group@=4.2.3":
  version "4.2.3"
  resolved "https://mirrors.tencent.com/npm/@types/react-transition-group/-/react-transition-group-4.2.3.tgz"
  integrity sha512-Hk8jiuT7iLOHrcjKP/ZVSyCNXK73wJAUz60xm0mVhiRujrdiI++j4duLiL282VGxwAgxetHQFfqA29LgEeSkFA==
  dependencies:
    "@types/react" "*"

"@types/react-window@^1.8.1":
  version "1.8.8"
  resolved "https://mirrors.tencent.com/npm/@types/react-window/-/react-window-1.8.8.tgz"
  integrity sha512-8Ls660bHR1AUA2kuRvVG9D/4XpRC6wjAaPT9dil7Ckc76eP9TKWZwwmgfq8Q1LANX3QNDnoU4Zp48A3w+zK69Q==
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^18.2.66":
  version "18.3.3"
  resolved "https://mirrors.tencent.com/npm/@types/react/-/react-18.3.3.tgz"
  integrity sha512-hti/R0pS0q1/xx+TsI73XIqk26eBsISZ2R0wUijXIngRK9R/e7Xw/cXVxQK7R5JjW+SV4zGcn5hXjudkN/pLIw==
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/react@^16":
  version "16.14.62"
  resolved "https://mirrors.tencent.com/npm/@types/react/-/react-16.14.62.tgz#449e4e81caaf132d0c2c390644e577702db1dd9e"
  integrity sha512-BWf7hqninZav6nerxXj+NeZT/mTpDeG6Lk2zREHAy63CrnXoOGPGtNqTFYFN/sqpSaREDP5otVV88axIXmKfGA==
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "^0.16"
    csstype "^3.0.2"

"@types/scheduler@^0.16":
  version "0.16.8"
  resolved "https://mirrors.tencent.com/npm/@types/scheduler/-/scheduler-0.16.8.tgz#ce5ace04cfeabe7ef87c0091e50752e36707deff"
  integrity sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==

"@types/semver@^7.3.12":
  version "7.5.8"
  resolved "https://mirrors.tencent.com/npm/@types/semver/-/semver-7.5.8.tgz"
  integrity sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==

"@types/sortablejs@^1.10.7":
  version "1.15.8"
  resolved "https://mirrors.tencent.com/npm/@types/sortablejs/-/sortablejs-1.15.8.tgz"
  integrity sha512-b79830lW+RZfwaztgs1aVPgbasJ8e7AXtZYHTELNXZPsERt4ymJdjV4OccDbHQAvHrCcFpbF78jkm0R6h/pZVg==

"@types/spark-md5@^3.0.4":
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/@types/spark-md5/-/spark-md5-3.0.4.tgz"
  integrity sha512-qtOaDz+IXiNndPgYb6t1YoutnGvFRtWSNzpVjkAPCfB2UzTyybuD4Tjgs7VgRawum3JnJNRwNQd4N//SvrHg1Q==

"@types/tinycolor2@^1.4.3":
  version "1.4.6"
  resolved "https://mirrors.tencent.com/npm/@types/tinycolor2/-/tinycolor2-1.4.6.tgz"
  integrity sha512-iEN8J0BoMnsWBqjVbWH/c0G0Hh7O21lpR2/+PrvAVgWdzL7eexIFm4JN/Wn10PTcmNdtS6U67r499mlWMXOxNw==

"@types/validator@^13.1.3":
  version "13.12.0"
  resolved "https://mirrors.tencent.com/npm/@types/validator/-/validator-13.12.0.tgz"
  integrity sha512-nH45Lk7oPIJ1RVOF6JgFI6Dy0QpHEzq4QecZhvguxYPDwT8c93prCMqAtiIttm39voZ+DDR+qkNnMpJmMBRqag==

"@types/webxr@*":
  version "0.5.21"
  resolved "https://mirrors.tencent.com/npm/@types/webxr/-/webxr-0.5.21.tgz#2e25353af14c7569bcf082f2fb75921d2130db7e"
  integrity sha512-geZIAtLzjGmgY2JUi6VxXdCrTb99A7yP49lxLr2Nm/uIK0PkkxcEi4OGhoGDO4pxCf3JwGz2GiJL2Ej4K2bKaA==

"@typescript-eslint/eslint-plugin@^5.7.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz"
  integrity sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.7.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/parser/-/parser-5.62.0.tgz"
  integrity sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz"
  integrity sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz"
  integrity sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/types/-/types-5.62.0.tgz"
  integrity sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz"
  integrity sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/utils/-/utils-5.62.0.tgz"
  integrity sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz"
  integrity sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.2.0":
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==

"@vitejs/plugin-react@^4.2.1":
  version "4.2.1"
  resolved "https://mirrors.tencent.com/npm/@vitejs/plugin-react/-/plugin-react-4.2.1.tgz"
  integrity sha512-oojO9IDc4nCUUi8qIR11KoQm0XFFLIwsRBwHRR4d/88IWghn1y6ckz/bJ8GHDCsYEJee8mDzqtJxh15/cisJNQ==
  dependencies:
    "@babel/core" "^7.23.5"
    "@babel/plugin-transform-react-jsx-self" "^7.23.3"
    "@babel/plugin-transform-react-jsx-source" "^7.23.3"
    "@types/babel__core" "^7.20.5"
    react-refresh "^0.14.0"

"@webav/av-canvas@^1.0.5":
  version "1.0.15"
  resolved "https://mirrors.tencent.com/npm/@webav/av-canvas/-/av-canvas-1.0.15.tgz#38d85b59c2f127827b1c73e5b56d6233a836b983"
  integrity sha512-nteXuXuRobxnCVa0VSzAjRxOeAn1IynI7eYW9Be5tuZNf9POZxKJrQ6BYUJrg4kXR/KnwSogNUAHXOiy+vNCzg==
  dependencies:
    "@webav/av-cliper" "1.0.15"
    "@webav/internal-utils" "1.0.15"

"@webav/av-cliper@1.0.15", "@webav/av-cliper@^1.0.5":
  version "1.0.15"
  resolved "https://mirrors.tencent.com/npm/@webav/av-cliper/-/av-cliper-1.0.15.tgz#a83cc6282d067865291ba3fb68a4f5a23fb7cfd0"
  integrity sha512-52LzuVP6TjkmmVUy7ooflq6qo7VGLyV/3b8IwixH3U7m6fmVfeh5u0afR145yj9XZ4ContnWnPiHSjqctpy9AA==
  dependencies:
    "@webav/internal-utils" "1.0.15"
    "@webav/mp4box.js" "0.5.6"
    opfs-tools "^0.6.1"
    wave-resampler "^1.0.0"

"@webav/internal-utils@1.0.15":
  version "1.0.15"
  resolved "https://mirrors.tencent.com/npm/@webav/internal-utils/-/internal-utils-1.0.15.tgz#cd18995feea9c4f011107efebe58cc38f3115438"
  integrity sha512-5AmQS7iApmPERAFWjPcNaKPsaVjLrAyPalG+RCuL1GBxlLabPUIH80JVCN4Im5jDT2bzM22BSNeFBZmhCdYK0w==
  dependencies:
    "@webav/mp4box.js" "0.5.6"

"@webav/mp4box.js@0.5.6":
  version "0.5.6"
  resolved "https://mirrors.tencent.com/npm/@webav/mp4box.js/-/mp4box.js-0.5.6.tgz#b78d7d5daeca92ced00d7397b4e0e7e4eb12af1b"
  integrity sha512-DpZQlr624oyLwnmJjNIWggk4d2dfKsAwQdNMVf0akjx0BtXL45Fmd49tm0ekn9d9dB3zBIxxsgco5IcJQGpt4Q==

"@xmldom/xmldom@^0.8.6":
  version "0.8.10"
  resolved "https://mirrors.tencent.com/npm/@xmldom/xmldom/-/xmldom-0.8.10.tgz"
  integrity sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==

"@xobotyi/scrollbar-width@1.9.5":
  version "1.9.5"
  resolved "https://mirrors.tencent.com/npm/@xobotyi/scrollbar-width/-/scrollbar-width-1.9.5.tgz#80224a6919272f405b87913ca13b92929bdf3c4d"
  integrity sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==

"@yarnpkg/lockfile@^1.1.0":
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/@yarnpkg/lockfile/-/lockfile-1.1.0.tgz"
  integrity sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ==

JSONStream@^1.3.5:
  version "1.3.5"
  resolved "https://mirrors.tencent.com/npm/JSONStream/-/JSONStream-1.3.5.tgz"
  integrity sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ==
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

abab@^2.0.6:
  version "2.0.6"
  resolved "https://mirrors.tencent.com/npm/abab/-/abab-2.0.6.tgz"
  integrity sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==

abbrev@1:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

acorn-globals@^7.0.0:
  version "7.0.1"
  resolved "https://mirrors.tencent.com/npm/acorn-globals/-/acorn-globals-7.0.1.tgz"
  integrity sha512-umOSDSDrfHbTNPuNpC2NSnnA3LUrqpevPb4T9jRx4MagXNS0rs+gwiTcAvqCRmsD6utzsrzNt+ebm00SNWiC3Q==
  dependencies:
    acorn "^8.1.0"
    acorn-walk "^8.0.2"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://mirrors.tencent.com/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn-walk@^8.0.2, acorn-walk@^8.1.1:
  version "8.3.2"
  resolved "https://mirrors.tencent.com/npm/acorn-walk/-/acorn-walk-8.3.2.tgz"
  integrity sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==

acorn@^8.1.0, acorn@^8.4.1, acorn@^8.8.1, acorn@^8.9.0:
  version "8.11.3"
  resolved "https://mirrors.tencent.com/npm/acorn/-/acorn-8.11.3.tgz"
  integrity sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==

add-px-to-style@1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/add-px-to-style/-/add-px-to-style-1.0.0.tgz"
  integrity sha512-YMyxSlXpPjD8uWekCQGuN40lV4bnZagUwqa2m/uFv1z/tNImSk9fnXVMUI5qwME/zzI3MMQRvjZ+69zyfSSyew==

adler-32@~1.3.0:
  version "1.3.1"
  resolved "https://mirrors.tencent.com/npm/adler-32/-/adler-32-1.3.1.tgz"
  integrity sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==

aegis-web-sdk@^1.39.1:
  version "1.39.1"
  resolved "https://mirrors.tencent.com/npm/aegis-web-sdk/-/aegis-web-sdk-1.39.1.tgz"
  integrity sha512-PkMAzeekaLpo/PmfspXiHfmGwY6DfWf7xUl8hrlkmBajQti1HqKC6pHoO3gwVPDiA9Sio5abjFGDcPjxxVTcJg==
  dependencies:
    web-vitals "^3.4.0"

agent-base@6:
  version "6.0.2"
  resolved "https://mirrors.tencent.com/npm/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

ahooks@^3.7.0, ahooks@^3.7.11, ahooks@^3.7.2, ahooks@^3.7.8:
  version "3.8.0"
  resolved "https://mirrors.tencent.com/npm/ahooks/-/ahooks-3.8.0.tgz"
  integrity sha512-M01m+mxLRNNeJ/PCT3Fom26UyreTj6oMqJBetUrJnK4VNI5j6eMA543Xxo53OBXn6XibA2FXKcCCgrT6YCTtKQ==
  dependencies:
    "@babel/runtime" "^7.21.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^2.x.x"
    lodash "^4.17.21"
    react-fast-compare "^3.2.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    tslib "^2.4.1"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://mirrors.tencent.com/npm/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.11.0:
  version "8.12.0"
  resolved "https://mirrors.tencent.com/npm/ajv/-/ajv-8.12.0.tgz"
  integrity sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-align@^3.0.0:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/ansi-align/-/ansi-align-3.0.1.tgz"
  integrity sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==
  dependencies:
    string-width "^4.1.0"

ansi-escapes@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/ansi-escapes/-/ansi-escapes-5.0.0.tgz"
  integrity sha512-5GFMVX8HqE/TB+FuBJGuO5XG0WrsA6ptUqoODaT/n9mmUaZFkqnBueB4leqGBCmrUHnCnC4PCZTCd0E7QQ83bA==
  dependencies:
    type-fest "^1.0.2"

ansi-escapes@^7.0.0:
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/ansi-escapes/-/ansi-escapes-7.0.0.tgz#00fc19f491bbb18e1d481b97868204f92109bfe7"
  integrity sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==
  dependencies:
    environment "^1.0.0"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "https://mirrors.tencent.com/npm/ansi-regex/-/ansi-regex-6.0.1.tgz"
  integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://mirrors.tencent.com/npm/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://mirrors.tencent.com/npm/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.0.0, ansi-styles@^6.1.0, ansi-styles@^6.2.1:
  version "6.2.1"
  resolved "https://mirrors.tencent.com/npm/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

antd@^4.12.3, antd@^4.22.5:
  version "4.22.8"
  resolved "https://mirrors.tencent.com/npm/antd/-/antd-4.22.8.tgz"
  integrity sha512-mqHuCg9itZX+z6wk+mvRBcfz/U9iiIXS4LoNkyo8X/UBgdN8CoetFmrdvA1UQy1BuWa0/n62LiS1LatdvoTuHw==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons" "^4.7.0"
    "@ant-design/react-slick" "~0.29.1"
    "@babel/runtime" "^7.18.3"
    "@ctrl/tinycolor" "^3.4.0"
    classnames "^2.2.6"
    copy-to-clipboard "^3.2.0"
    lodash "^4.17.21"
    memoize-one "^6.0.0"
    moment "^2.29.2"
    rc-cascader "~3.6.0"
    rc-checkbox "~2.3.0"
    rc-collapse "~3.3.0"
    rc-dialog "~8.9.0"
    rc-drawer "~5.1.0"
    rc-dropdown "~4.0.0"
    rc-field-form "~1.27.0"
    rc-image "~5.7.0"
    rc-input "~0.0.1-alpha.5"
    rc-input-number "~7.3.5"
    rc-mentions "~1.9.1"
    rc-menu "~9.6.3"
    rc-motion "^2.6.1"
    rc-notification "~4.6.0"
    rc-pagination "~3.1.17"
    rc-picker "~2.6.10"
    rc-progress "~3.3.2"
    rc-rate "~2.9.0"
    rc-resize-observer "^1.2.0"
    rc-segmented "~2.1.0"
    rc-select "~14.1.1"
    rc-slider "~10.0.0"
    rc-steps "~4.1.0"
    rc-switch "~3.2.0"
    rc-table "~7.25.3"
    rc-tabs "~11.16.0"
    rc-textarea "~0.3.0"
    rc-tooltip "~5.2.0"
    rc-tree "~5.6.5"
    rc-tree-select "~5.4.0"
    rc-trigger "^5.2.10"
    rc-upload "~4.3.0"
    rc-util "^5.22.5"
    scroll-into-view-if-needed "^2.2.25"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://mirrors.tencent.com/npm/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

are-we-there-yet@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz"
  integrity sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://mirrors.tencent.com/npm/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

arg@^5.0.2:
  version "5.0.2"
  resolved "https://mirrors.tencent.com/npm/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^1.0.9:
  version "1.0.10"
  resolved "https://mirrors.tencent.com/npm/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-query@^5.3.0:
  version "5.3.0"
  resolved "https://mirrors.tencent.com/npm/aria-query/-/aria-query-5.3.0.tgz"
  integrity sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz"
  integrity sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==
  dependencies:
    call-bind "^1.0.5"
    is-array-buffer "^3.0.4"

array-ify@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/array-ify/-/array-ify-1.0.0.tgz"
  integrity sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng==

array-includes@^3.1.1, array-includes@^3.1.6, array-includes@^3.1.7:
  version "3.1.8"
  resolved "https://mirrors.tencent.com/npm/array-includes/-/array-includes-3.1.8.tgz"
  integrity sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-move@^3.0.1:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/array-move/-/array-move-3.0.1.tgz"
  integrity sha512-H3Of6NIn2nNU1gsVDqDnYKY/LCdWvCMMOWifNGhKcVQgiZ6nOek39aESOvro6zmueP07exSl93YLvkN4fZOkSg==

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  integrity sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

array.prototype.findlast@^1.2.4:
  version "1.2.5"
  resolved "https://mirrors.tencent.com/npm/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
  integrity sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.3:
  version "1.2.5"
  resolved "https://mirrors.tencent.com/npm/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.5.tgz"
  integrity sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.2"
  resolved "https://mirrors.tencent.com/npm/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz"
  integrity sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  resolved "https://mirrors.tencent.com/npm/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz"
  integrity sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.toreversed@^1.1.2:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/array.prototype.toreversed/-/array.prototype.toreversed-1.1.2.tgz"
  integrity sha512-wwDCoT4Ck4Cz7sLtgUmzR5UV3YF5mFHUlbChCzZBQZ+0m2cl/DH3tKgvphv1nKgFsJ48oCSg6p91q2Vm0I/ZMA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.3:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/array.prototype.tosorted/-/array.prototype.tosorted-1.1.3.tgz"
  integrity sha512-/DdH4TiTmOKzyQbp/eadcCVexiCb36xJg7HshYOYJnNZFDj33GEv0P7GxsynpShhq4OLYJzbGcBDkLsDt7MnNg==
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.1.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.3.tgz"
  integrity sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.2.1"
    get-intrinsic "^1.2.3"
    is-array-buffer "^3.0.4"
    is-shared-array-buffer "^1.0.2"

arrify@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/arrify/-/arrify-1.0.1.tgz"
  integrity sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==

asap@~2.0.6:
  version "2.0.6"
  resolved "https://mirrors.tencent.com/npm/asap/-/asap-2.0.6.tgz"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

ast-types-flow@^0.0.8:
  version "0.0.8"
  resolved "https://mirrors.tencent.com/npm/ast-types-flow/-/ast-types-flow-0.0.8.tgz"
  integrity sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==

async-validator@^4.1.0:
  version "4.2.5"
  resolved "https://mirrors.tencent.com/npm/async-validator/-/async-validator-4.2.5.tgz"
  integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://mirrors.tencent.com/npm/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/at-least-node/-/at-least-node-1.0.0.tgz"
  integrity sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==

attr-accept@^2.2.2:
  version "2.2.2"
  resolved "https://mirrors.tencent.com/npm/attr-accept/-/attr-accept-2.2.2.tgz"
  integrity sha512-7prDjvt9HmqiZ0cl5CRjtS84sEyhsHP2coDkaZKRKVfCDo9s7iw7ChVmar78Gu9pC4SoR/28wFu/G5JJhTnqEg==

autoprefixer@^10.4.19:
  version "10.4.19"
  resolved "https://mirrors.tencent.com/npm/autoprefixer/-/autoprefixer-10.4.19.tgz"
  integrity sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==
  dependencies:
    browserslist "^4.23.0"
    caniuse-lite "^1.0.30001599"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

await-to-js@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/await-to-js/-/await-to-js-3.0.0.tgz"
  integrity sha512-zJAaP9zxTcvTHRlejau3ZOY4V7SRpiByf3/dxx2uyKxxor19tpmpV2QRsTKikckwhaPmr2dVpxxMr7jOCYVp5g==

axe-core@=4.7.0:
  version "4.7.0"
  resolved "https://mirrors.tencent.com/npm/axe-core/-/axe-core-4.7.0.tgz"
  integrity sha512-M0JtH+hlOL5pLQwHOLNYZaXuhqmvS8oExsqB1SBYgA4Dk7u/xx+YdGHXaK5pyUfed5mYXdlYiphWq3G8cRi5JQ==

axios@*, axios@^1.6.7, axios@^1.6.8:
  version "1.7.7"
  resolved "https://mirrors.tencent.com/npm/axios/-/axios-1.7.7.tgz#2f554296f9892a72ac8d8e4c5b79c14a91d0a47f"
  integrity sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@^0.27.2:
  version "0.27.2"
  resolved "https://mirrors.tencent.com/npm/axios/-/axios-0.27.2.tgz"
  integrity sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

axobject-query@^3.2.1:
  version "3.2.1"
  resolved "https://mirrors.tencent.com/npm/axobject-query/-/axobject-query-3.2.1.tgz"
  integrity sha512-jsyHu61e6N4Vbz/v18DHwWYKK0bSWLqn47eeDSKPB7m8tqMHF9YJ+mhIk2lVteyZrY8tnSj/jHOv4YiTCuCJgg==
  dependencies:
    dequal "^2.0.3"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

babel-plugin-transform-jsx-class@^0.1.3:
  version "0.1.3"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-transform-jsx-class/-/babel-plugin-transform-jsx-class-0.1.3.tgz"
  integrity sha512-Udi3d5qzwEyteqchxKoV/JfgktZZ8O3SLlADO936Os8LXolkuhl3joq71KiU0yMip8wz7+3zBKU+7otK1iZCLw==

babel-plugin-transform-jsx-condition@^0.1.2:
  version "0.1.3"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-transform-jsx-condition/-/babel-plugin-transform-jsx-condition-0.1.3.tgz"
  integrity sha512-WfN3XpoL53z/k1YAXbLCEQ3qa7YxQd2Wc00eoe4Hfh9JFzSLHhU7lKGFdDbwEYJzM+YI+pDe0hXDqD2b784pXg==

babel-plugin-transform-jsx-fragment@^0.1.4:
  version "0.1.5"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-transform-jsx-fragment/-/babel-plugin-transform-jsx-fragment-0.1.5.tgz"
  integrity sha512-s12RL9XMM4Z3xgwaq3dSSyJhBmv2MZmFVlhdIc9L5qEcS/on4LkLgIcVU8S8dZB44wpBLoqU0k8LkvDvvIZVuA==

babel-plugin-transform-jsx-list@^0.1.2:
  version "0.1.2"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-transform-jsx-list/-/babel-plugin-transform-jsx-list-0.1.2.tgz"
  integrity sha512-6ajrUOorIkdKwHWAeN8ZUp67yaFl0PYyHKUGY31Pw/ZtF0EIygfD8oxjcbFp0rZpTcVEYTMmh+s1xc6cnKy79g==

babel-plugin-transform-jsx-memo@^0.1.4:
  version "0.1.4"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-transform-jsx-memo/-/babel-plugin-transform-jsx-memo-0.1.4.tgz"
  integrity sha512-v9L9oFFbPjaVytLhxsmuFLm3USIoTeZQHz4xid2qnT87/C5cCbE8GtE26xbPXTc2LBj1roSDIO3hgR+2JAv1vA==

babel-plugin-transform-jsx-slot@^0.1.2:
  version "0.1.2"
  resolved "https://mirrors.tencent.com/npm/babel-plugin-transform-jsx-slot/-/babel-plugin-transform-jsx-slot-0.1.2.tgz"
  integrity sha512-ske7KaYo4w4p2w1q7C3653pNje765P24vLF59qmXlgvlPYuFVCTaGS+PYEyb5xIDRDhgiSO2KtDdYK6f6ONxkQ==
  dependencies:
    "@babel/types" "^7.5.0"

babel-runtime-jsx-plus@^0.1.5:
  version "0.1.5"
  resolved "https://mirrors.tencent.com/npm/babel-runtime-jsx-plus/-/babel-runtime-jsx-plus-0.1.5.tgz"
  integrity sha512-5qjZDfUzZGxHgX8o0tkS9o0HbyBvnUuaAtqHC9IN5CgjWFGJBg6a0Xp31wiG7btiHV0dP5t1t8cthlTCYwtnig==

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  integrity sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==

base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "https://mirrors.tencent.com/npm/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

big-integer@^1.6.16:
  version "1.6.52"
  resolved "https://mirrors.tencent.com/npm/big-integer/-/big-integer-1.6.52.tgz"
  integrity sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==

big.js@^6.0.1, big.js@^6.2.2:
  version "6.2.2"
  resolved "https://mirrors.tencent.com/npm/big.js/-/big.js-6.2.2.tgz#be3bb9ac834558b53b099deef2a1d06ac6368e1a"
  integrity sha512-y/ie+Faknx7sZA5MfGA2xKlu0GDv8RWrXGsmlteyJQ2lvoKv9GBK/fpRMc2qlSoBAgNxrixICFCBefIq8WCQpQ==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://mirrors.tencent.com/npm/bowser/-/bowser-2.11.0.tgz"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

boxen@^5.0.0:
  version "5.1.2"
  resolved "https://mirrors.tencent.com/npm/boxen/-/boxen-5.1.2.tgz"
  integrity sha512-9gYgQKXx+1nP8mP7CzFyaUARhg7D3n1dF/FnErWmu9l6JvGpNUN278h0aSb+QjoiKSWG+iZ3uHrcqk0qrY9RQQ==
  dependencies:
    ansi-align "^3.0.0"
    camelcase "^6.2.0"
    chalk "^4.1.0"
    cli-boxes "^2.2.1"
    string-width "^4.2.2"
    type-fest "^0.20.2"
    widest-line "^3.1.0"
    wrap-ansi "^7.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://mirrors.tencent.com/npm/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://mirrors.tencent.com/npm/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

broadcast-channel@^3.4.1:
  version "3.7.0"
  resolved "https://mirrors.tencent.com/npm/broadcast-channel/-/broadcast-channel-3.7.0.tgz"
  integrity sha512-cIAKJXAxGJceNZGTZSBzMxzyOn72cVgPnKx4dc6LRjQgbaJUQqhy5rzL3zbMxkMWsGKkv2hSFkPRMEXfoMZ2Mg==
  dependencies:
    "@babel/runtime" "^7.7.2"
    detect-node "^2.1.0"
    js-sha3 "0.8.0"
    microseconds "0.2.0"
    nano-time "1.0.0"
    oblivious-set "1.0.0"
    rimraf "3.0.2"
    unload "2.2.0"

browserslist@^4.23.0, browserslist@^4.23.1:
  version "4.23.3"
  resolved "https://mirrors.tencent.com/npm/browserslist/-/browserslist-4.23.3.tgz"
  integrity sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==
  dependencies:
    caniuse-lite "^1.0.30001646"
    electron-to-chromium "^1.5.4"
    node-releases "^2.0.18"
    update-browserslist-db "^1.1.0"

buffer-builder@^0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/buffer-builder/-/buffer-builder-0.2.0.tgz#3322cd307d8296dab1f604618593b261a3fade8f"
  integrity sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://mirrors.tencent.com/npm/buffer/-/buffer-6.0.3.tgz#2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6"
  integrity sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

cacheable-request@^6.0.0:
  version "6.1.0"
  resolved "https://mirrors.tencent.com/npm/cacheable-request/-/cacheable-request-6.1.0.tgz"
  integrity sha512-Oj3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==
  dependencies:
    clone-response "^1.0.2"
    get-stream "^5.1.0"
    http-cache-semantics "^4.0.0"
    keyv "^3.0.0"
    lowercase-keys "^2.0.0"
    normalize-url "^4.1.0"
    responselike "^1.0.2"

call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/call-bind/-/call-bind-1.0.7.tgz"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camel-case@^4.1.1, camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://mirrors.tencent.com/npm/camel-case/-/camel-case-4.1.2.tgz"
  integrity sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "https://mirrors.tencent.com/npm/camelcase-keys/-/camelcase-keys-6.2.2.tgz"
  integrity sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://mirrors.tencent.com/npm/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://mirrors.tencent.com/npm/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001275, caniuse-lite@^1.0.30001599, caniuse-lite@^1.0.30001646:
  version "1.0.30001646"
  resolved "https://mirrors.tencent.com/npm/caniuse-lite/-/caniuse-lite-1.0.30001646.tgz"
  integrity sha512-dRg00gudiBDDTmUhClSdv3hqRfpbOnU28IpI1T6PBTLWa+kOj0681C8uML3PifYfREuBrVjDGhL3adYpBT6spw==

canvas@^2.11.2:
  version "2.11.2"
  resolved "https://mirrors.tencent.com/npm/canvas/-/canvas-2.11.2.tgz"
  integrity sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.0"
    nan "^2.17.0"
    simple-get "^3.0.3"

capital-case@^1.0.4:
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/capital-case/-/capital-case-1.0.4.tgz"
  integrity sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"
    upper-case-first "^2.0.2"

cfb@~1.2.1:
  version "1.2.2"
  resolved "https://mirrors.tencent.com/npm/cfb/-/cfb-1.2.2.tgz"
  integrity sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chalk@5.3.0:
  version "5.3.0"
  resolved "https://mirrors.tencent.com/npm/chalk/-/chalk-5.3.0.tgz"
  integrity sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==

chalk@^2.4.2:
  version "2.4.2"
  resolved "https://mirrors.tencent.com/npm/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://mirrors.tencent.com/npm/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

change-case@^4.1.2:
  version "4.1.2"
  resolved "https://mirrors.tencent.com/npm/change-case/-/change-case-4.1.2.tgz"
  integrity sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==
  dependencies:
    camel-case "^4.1.2"
    capital-case "^1.0.4"
    constant-case "^3.0.4"
    dot-case "^3.0.4"
    header-case "^2.0.4"
    no-case "^3.0.4"
    param-case "^3.0.4"
    pascal-case "^3.1.2"
    path-case "^3.0.4"
    sentence-case "^3.0.4"
    snake-case "^3.0.4"
    tslib "^2.0.3"

charenc@0.0.2:
  version "0.0.2"
  resolved "https://mirrors.tencent.com/npm/charenc/-/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"
  integrity sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==

chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://mirrors.tencent.com/npm/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://mirrors.tencent.com/npm/ci-info/-/ci-info-1.6.0.tgz"
  integrity sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/ci-info/-/ci-info-2.0.0.tgz"
  integrity sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==

classnames@2.3.1:
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/classnames/-/classnames-2.3.1.tgz"
  integrity sha512-OlQdbZ7gLfGarSqxesMesDa5uz7KFbID8Kpq/SxIoNGDqY8lSYs0D+hhtBXhcdB3rcbXArFr7vlHheLk1voeNA==

classnames@2.x, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1, classnames@^2.5.1, classnames@~2.5.1:
  version "2.5.1"
  resolved "https://mirrors.tencent.com/npm/classnames/-/classnames-2.5.1.tgz"
  integrity sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==

cli-boxes@^2.2.1:
  version "2.2.1"
  resolved "https://mirrors.tencent.com/npm/cli-boxes/-/cli-boxes-2.2.1.tgz"
  integrity sha512-y4coMcylgSCdVinjiDBuR8PCC2bLjyGTwEmPb9NHR/QaNU6EUOXcTY/s6VjGMD6ENSEaeQYHCY0GNGS5jfMwPw==

cli-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/cli-cursor/-/cli-cursor-4.0.0.tgz"
  integrity sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==
  dependencies:
    restore-cursor "^4.0.0"

cli-cursor@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/cli-cursor/-/cli-cursor-5.0.0.tgz#24a4831ecf5a6b01ddeb32fb71a4b2088b0dce38"
  integrity sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==
  dependencies:
    restore-cursor "^5.0.0"

cli-truncate@^3.1.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/cli-truncate/-/cli-truncate-3.1.0.tgz"
  integrity sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^5.0.0"

cli-truncate@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/cli-truncate/-/cli-truncate-4.0.0.tgz#6cc28a2924fee9e25ce91e973db56c7066e6172a"
  integrity sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^7.0.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/cliui/-/cliui-6.0.0.tgz"
  integrity sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://mirrors.tencent.com/npm/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone-response@^1.0.2:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/clone-response/-/clone-response-1.0.3.tgz"
  integrity sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==
  dependencies:
    mimic-response "^1.0.0"

clone@^2.1.1, clone@^2.1.2:
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/clone/-/clone-2.1.2.tgz"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

codepage@~1.15.0:
  version "1.15.0"
  resolved "https://mirrors.tencent.com/npm/codepage/-/codepage-1.15.0.tgz"
  integrity sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://mirrors.tencent.com/npm/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/color-name/-/color-name-1.1.3.tgz"
  integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://mirrors.tencent.com/npm/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-support@^1.1.2:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

colorette@^2.0.20:
  version "2.0.20"
  resolved "https://mirrors.tencent.com/npm/colorette/-/colorette-2.0.20.tgz"
  integrity sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==

colorjs.io@^0.5.0:
  version "0.5.2"
  resolved "https://mirrors.tencent.com/npm/colorjs.io/-/colorjs.io-0.5.2.tgz#63b20139b007591ebc3359932bef84628eb3fcef"
  integrity sha512-twmVoizEW7ylZSN32OgKdXRmo1qg+wT5/6C3xu5b9QsWzSFAhHLn2xd8ro0diCsKfCj1RdaTP/nrcW+vAoQPIw==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://mirrors.tencent.com/npm/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@*:
  version "12.0.0"
  resolved "https://mirrors.tencent.com/npm/commander/-/commander-12.0.0.tgz"
  integrity sha512-MwVNWlYjDTtOjX5PiD7o5pK0UrFU/OYgcJfjjK4RaHZETNtjJqrZa9Y9ds88+A+f+d5lv+561eZ+yCKoS3gbAA==

commander@11.0.0:
  version "11.0.0"
  resolved "https://mirrors.tencent.com/npm/commander/-/commander-11.0.0.tgz"
  integrity sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==

commander@^4.0.0:
  version "4.1.1"
  resolved "https://mirrors.tencent.com/npm/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

compare-func@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/compare-func/-/compare-func-2.0.0.tgz"
  integrity sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA==
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^5.1.0"

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "https://mirrors.tencent.com/npm/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz"
  integrity sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://mirrors.tencent.com/npm/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

configstore@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/configstore/-/configstore-5.0.1.tgz"
  integrity sha512-aMKprgk5YhBNyH25hj8wGt2+D52Sw1DRRIzqBwLp2Ya9mFmY8KPvvtvmna8SxVR9JMZ4kzMD68N22vlaRpkeFA==
  dependencies:
    dot-prop "^5.2.0"
    graceful-fs "^4.1.2"
    make-dir "^3.0.0"
    unique-string "^2.0.0"
    write-file-atomic "^3.0.0"
    xdg-basedir "^4.0.0"

confusing-browser-globals@^1.0.10:
  version "1.0.11"
  resolved "https://mirrors.tencent.com/npm/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
  integrity sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

constant-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/constant-case/-/constant-case-3.0.4.tgz"
  integrity sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"
    upper-case "^2.0.2"

conventional-changelog-angular@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/conventional-changelog-angular/-/conventional-changelog-angular-6.0.0.tgz"
  integrity sha512-6qLgrBF4gueoC7AFVHu51nHL9pF9FRjXrH+ceVf7WmAfH3gs+gEYOkvxhjMPjZu57I4AGUGoNTY8V7Hrgf1uqg==
  dependencies:
    compare-func "^2.0.0"

conventional-changelog-conventionalcommits@^6.1.0:
  version "6.1.0"
  resolved "https://mirrors.tencent.com/npm/conventional-changelog-conventionalcommits/-/conventional-changelog-conventionalcommits-6.1.0.tgz"
  integrity sha512-3cS3GEtR78zTfMzk0AizXKKIdN4OvSh7ibNz6/DPbhWWQu7LqE/8+/GqSodV+sywUR2gpJAdP/1JFf4XtN7Zpw==
  dependencies:
    compare-func "^2.0.0"

conventional-commits-parser@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/conventional-commits-parser/-/conventional-commits-parser-4.0.0.tgz"
  integrity sha512-WRv5j1FsVM5FISJkoYMR6tPk07fkKT0UodruX4je86V4owk451yjXAKzKAPOs9l7y59E2viHUS9eQ+dfUA9NSg==
  dependencies:
    JSONStream "^1.3.5"
    is-text-path "^1.0.1"
    meow "^8.1.2"
    split2 "^3.2.2"

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "https://mirrors.tencent.com/npm/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "https://mirrors.tencent.com/npm/copy-anything/-/copy-anything-2.0.6.tgz"
  integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
  dependencies:
    is-what "^3.14.1"

copy-to-clipboard@^3.2.0, copy-to-clipboard@^3.3.1, copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "https://mirrors.tencent.com/npm/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  integrity sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==
  dependencies:
    toggle-selection "^1.0.6"

core-js@^2.6.12, core-js@^2.6.5:
  version "2.6.12"
  resolved "https://mirrors.tencent.com/npm/core-js/-/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==

core-js@^3.1.4:
  version "3.39.0"
  resolved "https://mirrors.tencent.com/npm/core-js/-/core-js-3.39.0.tgz#57f7647f4d2d030c32a72ea23a0555b2eaa30f83"
  integrity sha512-raM0ew0/jJUqkJ0E6e8UDtl+y/7ktFivgWvqw8dNSQeNWoSDLvQ1H/RN3aPXB9tBd4/FhyR4RDPGhsNIMsAn7g==

cos-js-sdk-v5@^1.6.0:
  version "1.8.3"
  resolved "https://mirrors.tencent.com/npm/cos-js-sdk-v5/-/cos-js-sdk-v5-1.8.3.tgz"
  integrity sha512-Ne7VsOfuW6D5IuEWlR9VO9R2kFnotOSfAu6S0B5RyYETXmKlUWng3cl/JpVmwkn4VaFqcnsm2t45lCYnvozahA==
  dependencies:
    "@xmldom/xmldom" "^0.8.6"

cos-js-sdk-v5@^1.8.0:
  version "1.8.7"
  resolved "https://mirrors.tencent.com/npm/cos-js-sdk-v5/-/cos-js-sdk-v5-1.8.7.tgz#a8778b25da79bcc0d83d6d1e58eb3b906f9fdc62"
  integrity sha512-KK3PYbUiLxcjvVhyvEeBQRWzDgAmuldDZQL1lTRM0aeQaI+hlm84xQQQyv+oIRxPnnAIleZF2vUgB4sJFUzcxQ==
  dependencies:
    fast-xml-parser "4.5.0"

cosmiconfig-typescript-loader@^4.0.0:
  version "4.4.0"
  resolved "https://mirrors.tencent.com/npm/cosmiconfig-typescript-loader/-/cosmiconfig-typescript-loader-4.4.0.tgz"
  integrity sha512-BabizFdC3wBHhbI4kJh0VkQP9GkBfoHPydD0COMce1nJ1kJAB3F2TmJ/I7diULBKtmEWSwEbuN/KDtgnmUUVmw==

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://mirrors.tencent.com/npm/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cosmiconfig@^8.0.0:
  version "8.3.6"
  resolved "https://mirrors.tencent.com/npm/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"
  resolved "https://mirrors.tencent.com/npm/crc-32/-/crc-32-1.2.2.tgz"
  integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/create-require/-/create-require-1.1.1.tgz"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/cross-spawn/-/cross-spawn-5.1.0.tgz"
  integrity sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://mirrors.tencent.com/npm/cross-spawn/-/cross-spawn-6.0.5.tgz"
  integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://mirrors.tencent.com/npm/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "https://mirrors.tencent.com/npm/crypt/-/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"
  integrity sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==

crypto-js@^4.0.0, crypto-js@^4.2.0:
  version "4.2.0"
  resolved "https://mirrors.tencent.com/npm/crypto-js/-/crypto-js-4.2.0.tgz"
  integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
  integrity sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==

css-box-model@^1.2.0:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/css-box-model/-/css-box-model-1.2.1.tgz"
  integrity sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==
  dependencies:
    tiny-invariant "^1.0.6"

css-in-js-utils@^3.1.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz#640ae6a33646d401fc720c54fc61c42cd76ae2bb"
  integrity sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==
  dependencies:
    hyphenate-style-name "^1.0.3"

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/css-line-break/-/css-line-break-2.1.0.tgz"
  integrity sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==
  dependencies:
    utrie "^1.0.2"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/css-tree/-/css-tree-1.1.3.tgz#eb4870fb6fd7707327ec95c2ff2ab09b5e8db91d"
  integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

cssom@^0.5.0:
  version "0.5.0"
  resolved "https://mirrors.tencent.com/npm/cssom/-/cssom-0.5.0.tgz"
  integrity sha512-iKuQcq+NdHqlAcwUY0o/HL69XQrUaQdMjmStJ8JFmUaiiQErlhrmuigkg/CU4E2J0IyUKUrMAgl36TvN67MqTw==

cssom@~0.3.6:
  version "0.3.8"
  resolved "https://mirrors.tencent.com/npm/cssom/-/cssom-0.3.8.tgz"
  integrity sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/cssstyle/-/cssstyle-2.3.0.tgz"
  integrity sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==
  dependencies:
    cssom "~0.3.6"

csstype@^3.0.2, csstype@^3.1.2:
  version "3.1.3"
  resolved "https://mirrors.tencent.com/npm/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://mirrors.tencent.com/npm/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

dargs@^7.0.0:
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/dargs/-/dargs-7.0.0.tgz"
  integrity sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg==

data-urls@^3.0.2:
  version "3.0.2"
  resolved "https://mirrors.tencent.com/npm/data-urls/-/data-urls-3.0.2.tgz"
  integrity sha512-Jy/tj3ldjZJo63sVAvg6LHt2mHvl4V6AgRAmNDtLdm7faqtsx+aJG42rsyCo9JCoRVKwPFzKlIPx3DIibwSIaQ==
  dependencies:
    abab "^2.0.6"
    whatwg-mimetype "^3.0.0"
    whatwg-url "^11.0.0"

data-view-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/data-view-buffer/-/data-view-buffer-1.0.1.tgz"
  integrity sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz"
  integrity sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz"
  integrity sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@2.x:
  version "2.30.0"
  resolved "https://mirrors.tencent.com/npm/date-fns/-/date-fns-2.30.0.tgz"
  integrity sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==
  dependencies:
    "@babel/runtime" "^7.21.0"

dayjs@1.11.10:
  version "1.11.10"
  resolved "https://mirrors.tencent.com/npm/dayjs/-/dayjs-1.11.10.tgz"
  integrity sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==

dayjs@1.x, dayjs@^1.11.11, dayjs@^1.9.1:
  version "1.11.12"
  resolved "https://mirrors.tencent.com/npm/dayjs/-/dayjs-1.11.12.tgz"
  integrity sha512-Rt2g+nTbLlDWZTwwrIXjy9MeiZmSDI375FvZs72ngxx8PDC6YXOeR3q5LAuPzjZQxhiWdRKac7RKV+YyQYfYIg==

debug@4, debug@4.3.4, debug@^4.1.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  resolved "https://mirrors.tencent.com/npm/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://mirrors.tencent.com/npm/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

decamelize-keys@^1.1.0:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/decamelize-keys/-/decamelize-keys-1.1.1.tgz"
  integrity sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/decamelize/-/decamelize-1.2.0.tgz"
  integrity sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==

decimal.js@^10.4.2:
  version "10.4.3"
  resolved "https://mirrors.tencent.com/npm/decimal.js/-/decimal.js-10.4.3.tgz"
  integrity sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==

decode-uri-component@^0.2.0:
  version "0.2.2"
  resolved "https://mirrors.tencent.com/npm/decode-uri-component/-/decode-uri-component-0.2.2.tgz"
  integrity sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==

decompress-response@^3.3.0:
  version "3.3.0"
  resolved "https://mirrors.tencent.com/npm/decompress-response/-/decompress-response-3.3.0.tgz"
  integrity sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==
  dependencies:
    mimic-response "^1.0.0"

decompress-response@^4.2.0:
  version "4.2.1"
  resolved "https://mirrors.tencent.com/npm/decompress-response/-/decompress-response-4.2.1.tgz"
  integrity sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==
  dependencies:
    mimic-response "^2.0.0"

deep-equal@^1.0.1, deep-equal@^1.1.1:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/deep-equal/-/deep-equal-1.1.2.tgz"
  integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://mirrors.tencent.com/npm/deep-extend/-/deep-extend-0.6.0.tgz"
  integrity sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://mirrors.tencent.com/npm/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

defer-to-connect@^1.0.1:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/defer-to-connect/-/defer-to-connect-1.1.3.tgz"
  integrity sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://mirrors.tencent.com/npm/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==

define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

dequal@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-gpu@^5.0.59:
  version "5.0.59"
  resolved "https://mirrors.tencent.com/npm/detect-gpu/-/detect-gpu-5.0.59.tgz#1cb449b59c47606677434fb1af732bd046af19c2"
  integrity sha512-tBS01N6Lu7D2T6nmoA5or39u12PqtmO4RPCSfgvbgcOUGPhExU8RDiyUiT3iVxCKX9XTxIM1k+mVaeirLv6pwA==
  dependencies:
    webgl-constants "^1.1.1"

detect-libc@^2.0.0:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/detect-libc/-/detect-libc-2.0.3.tgz"
  integrity sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==

detect-node@^2.0.4, detect-node@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/detect-node/-/detect-node-2.1.0.tgz"
  integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://mirrors.tencent.com/npm/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

diff@^4.0.1:
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

dijkstrajs@^1.0.1:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/dijkstrajs/-/dijkstrajs-1.0.3.tgz"
  integrity sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-align@^1.7.0:
  version "1.12.4"
  resolved "https://mirrors.tencent.com/npm/dom-align/-/dom-align-1.12.4.tgz"
  integrity sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==

dom-css@^2.0.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/dom-css/-/dom-css-2.1.0.tgz"
  integrity sha512-w9kU7FAbaSh3QKijL6n59ofAhkkmMJ31GclJIz/vyQdjogfyxcB6Zf8CZyibOERI5o0Hxz30VmJS7+7r5fEj2Q==
  dependencies:
    add-px-to-style "1.0.0"
    prefix-style "2.0.1"
    to-camel-case "1.0.0"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://mirrors.tencent.com/npm/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

domexception@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/domexception/-/domexception-4.0.0.tgz"
  integrity sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==
  dependencies:
    webidl-conversions "^7.0.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/dot-case/-/dot-case-3.0.4.tgz"
  integrity sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dot-prop@^5.1.0, dot-prop@^5.2.0:
  version "5.3.0"
  resolved "https://mirrors.tencent.com/npm/dot-prop/-/dot-prop-5.3.0.tgz"
  integrity sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==
  dependencies:
    is-obj "^2.0.0"

duplexer3@^0.1.4:
  version "0.1.5"
  resolved "https://mirrors.tencent.com/npm/duplexer3/-/duplexer3-0.1.5.tgz"
  integrity sha512-1A8za6ws41LQgv9HrE/66jyC5yuSjQ3L/KOpFtoBilsAK2iA2wuS5rTt1OCzIvtS2V7nVmedsUU+DGRcjBmOYA==

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

electron-to-chromium@^1.5.4:
  version "1.5.4"
  resolved "https://mirrors.tencent.com/npm/electron-to-chromium/-/electron-to-chromium-1.5.4.tgz"
  integrity sha512-orzA81VqLyIGUEA77YkVA1D+N+nNfl2isJVjjmOyrlxuooZ19ynb+dOlaDTqd/idKRS9lDCSBmtzM+kyCsMnkA==

emoji-regex@^10.3.0:
  version "10.4.0"
  resolved "https://mirrors.tencent.com/npm/emoji-regex/-/emoji-regex-10.4.0.tgz#03553afea80b3975749cfcb36f776ca268e413d4"
  integrity sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://mirrors.tencent.com/npm/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://mirrors.tencent.com/npm/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://mirrors.tencent.com/npm/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

entities@^4.4.0:
  version "4.5.0"
  resolved "https://mirrors.tencent.com/npm/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

environment@^1.0.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/environment/-/environment-1.1.0.tgz#8e86c66b180f363c7ab311787e0259665f45a9f1"
  integrity sha512-xUtoPkMggbz0MPyPiIWr1Kp4aeWJjDZ6SMvURhimjdZgsRuDplF5/s9hcgGhyXMhs+6vpnuoiZ2kFiu3FMnS8Q==

errno@^0.1.1:
  version "0.1.8"
  resolved "https://mirrors.tencent.com/npm/errno/-/errno-0.1.8.tgz"
  integrity sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://mirrors.tencent.com/npm/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "https://mirrors.tencent.com/npm/error-stack-parser/-/error-stack-parser-2.1.4.tgz#229cb01cdbfa84440bfa91876285b94680188286"
  integrity sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.1, es-abstract@^1.23.2:
  version "1.23.3"
  resolved "https://mirrors.tencent.com/npm/es-abstract/-/es-abstract-1.23.3.tgz"
  integrity sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    arraybuffer.prototype.slice "^1.0.3"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    data-view-buffer "^1.0.1"
    data-view-byte-length "^1.0.1"
    data-view-byte-offset "^1.0.0"
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.0.3"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.4"
    get-symbol-description "^1.0.2"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    hasown "^2.0.2"
    internal-slot "^1.0.7"
    is-array-buffer "^3.0.4"
    is-callable "^1.2.7"
    is-data-view "^1.0.1"
    is-negative-zero "^2.0.3"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.3"
    is-string "^1.0.7"
    is-typed-array "^1.1.13"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.5"
    regexp.prototype.flags "^1.5.2"
    safe-array-concat "^1.1.2"
    safe-regex-test "^1.0.3"
    string.prototype.trim "^1.2.9"
    string.prototype.trimend "^1.0.8"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.2"
    typed-array-byte-length "^1.0.1"
    typed-array-byte-offset "^1.0.2"
    typed-array-length "^1.0.6"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.15"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/es-define-property/-/es-define-property-1.0.0.tgz"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.1.0, es-errors@^1.2.1, es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-iterator-helpers@^1.0.15, es-iterator-helpers@^1.0.17:
  version "1.0.18"
  resolved "https://mirrors.tencent.com/npm/es-iterator-helpers/-/es-iterator-helpers-1.0.18.tgz"
  integrity sha512-scxAJaewsahbqTYrGKJihhViaM6DDZDDoucfvzNbK0pOren1g/daDQ3IAhzn+1G14rBG7w+i5N+qul60++zlKA==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.0"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    globalthis "^1.0.3"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    iterator.prototype "^1.1.2"
    safe-array-concat "^1.1.2"

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/es-object-atoms/-/es-object-atoms-1.0.0.tgz"
  integrity sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz"
  integrity sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==
  dependencies:
    get-intrinsic "^1.2.4"
    has-tostringtag "^1.0.2"
    hasown "^2.0.1"

es-shim-unscopables@^1.0.0, es-shim-unscopables@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/es-shim-unscopables/-/es-shim-unscopables-1.0.2.tgz"
  integrity sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es6-promise@^4.2.8:
  version "4.2.8"
  resolved "https://mirrors.tencent.com/npm/es6-promise/-/es6-promise-4.2.8.tgz#4eb21594c972bc40553d276e510539143db53e0a"
  integrity sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==

esbuild@^0.21.3:
  version "0.21.5"
  resolved "https://mirrors.tencent.com/npm/esbuild/-/esbuild-0.21.5.tgz#9ca301b120922959b766360d8ac830da0d02997d"
  integrity sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.1.1, escalade@^3.1.2:
  version "3.1.2"
  resolved "https://mirrors.tencent.com/npm/escalade/-/escalade-3.1.2.tgz"
  integrity sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==

escape-goat@^2.0.0:
  version "2.1.1"
  resolved "https://mirrors.tencent.com/npm/escape-goat/-/escape-goat-2.1.1.tgz"
  integrity sha512-8/uIhbG12Csjy2JEW7D9pHbreaVaS/OpN3ycnyvElTdwM5n6GY6W6e2IPemfvGZeUMqZ9A/3GqIZMgKnBhAw/Q==

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://mirrors.tencent.com/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

escodegen@^2.0.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/escodegen/-/escodegen-2.1.0.tgz"
  integrity sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-airbnb-base@^15.0.0:
  version "15.0.0"
  resolved "https://mirrors.tencent.com/npm/eslint-config-airbnb-base/-/eslint-config-airbnb-base-15.0.0.tgz"
  integrity sha512-xaX3z4ZZIcFLvh2oUNvcX5oEofXda7giYmuplVxoOg5A7EXJMrUyqRgR+mhDhPK8LZ4PttFOBvCYDbX3sUoUig==
  dependencies:
    confusing-browser-globals "^1.0.10"
    object.assign "^4.1.2"
    object.entries "^1.1.5"
    semver "^6.3.0"

eslint-config-airbnb@^19.0.1:
  version "19.0.4"
  resolved "https://mirrors.tencent.com/npm/eslint-config-airbnb/-/eslint-config-airbnb-19.0.4.tgz"
  integrity sha512-T75QYQVQX57jiNgpF9r1KegMICE94VYwoFQyMGhrvc+lB8YF2E/M/PYDaQe1AJcWaEgqLE+ErXV1Og/+6Vyzew==
  dependencies:
    eslint-config-airbnb-base "^15.0.0"
    object.assign "^4.1.2"
    object.entries "^1.1.5"

eslint-config-prettier@^8.3.0:
  version "8.10.0"
  resolved "https://mirrors.tencent.com/npm/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz"
  integrity sha512-SM8AMJdeQqRYT9O9zguiruQZaN7+z+E4eAP9oiLNGKMtomwaB1E9dcgUD6ZAn/eQAb52USbvezbiljfZUhbJcg==

eslint-config-prettier@^9.1.0:
  version "9.1.0"
  resolved "https://mirrors.tencent.com/npm/eslint-config-prettier/-/eslint-config-prettier-9.1.0.tgz"
  integrity sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "https://mirrors.tencent.com/npm/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.8.0:
  version "2.8.1"
  resolved "https://mirrors.tencent.com/npm/eslint-module-utils/-/eslint-module-utils-2.8.1.tgz"
  integrity sha512-rXDXR3h7cs7dy9RNpUlQf80nX31XWJEyGq1tRMo+6GsO5VmTe4UTwtmonAD4ZkAsrfMVDA2wlGJ3790Ys+D49Q==
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@^2.23.4:
  version "2.29.1"
  resolved "https://mirrors.tencent.com/npm/eslint-plugin-import/-/eslint-plugin-import-2.29.1.tgz"
  integrity sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw==
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlastindex "^1.2.3"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.8.0"
    hasown "^2.0.0"
    is-core-module "^2.13.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.7"
    object.groupby "^1.0.1"
    object.values "^1.1.7"
    semver "^6.3.1"
    tsconfig-paths "^3.15.0"

eslint-plugin-jsx-a11y@^6.5.1:
  version "6.8.0"
  resolved "https://mirrors.tencent.com/npm/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.8.0.tgz"
  integrity sha512-Hdh937BS3KdwwbBaKd5+PLCOmYY6U4f2h9Z2ktwtNKvIdIEu137rjYbcb9ApSbVJfWxANNuiKTD/9tOKjK9qOA==
  dependencies:
    "@babel/runtime" "^7.23.2"
    aria-query "^5.3.0"
    array-includes "^3.1.7"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "=4.7.0"
    axobject-query "^3.2.1"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    es-iterator-helpers "^1.0.15"
    hasown "^2.0.0"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.entries "^1.1.7"
    object.fromentries "^2.0.7"

eslint-plugin-jsx-plus@^0.1.0:
  version "0.1.0"
  resolved "https://mirrors.tencent.com/npm/eslint-plugin-jsx-plus/-/eslint-plugin-jsx-plus-0.1.0.tgz"
  integrity sha512-iANfZsPWwUWT2czz3A7Ti7B5Iun8YvIMDe6c7VYEZAVjCZyZkB+djflAxOv1XD/TwQeFoEYhCoqaBRWFk5/vIA==
  dependencies:
    jsx-ast-utils "^2.2.1"
    requireindex "^1.1.0"

eslint-plugin-prettier@^4.0.0, eslint-plugin-prettier@^4.2.1:
  version "4.2.1"
  resolved "https://mirrors.tencent.com/npm/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
  integrity sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^4.3.0:
  version "4.6.0"
  resolved "https://mirrors.tencent.com/npm/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.0.tgz"
  integrity sha512-oFc7Itz9Qxh2x4gNHStv3BqJq54ExXmfC+a1NjAta66IAN87Wu0R/QArgIS9qKzX3dXKPI9H5crl9QchNMY9+g==

eslint-plugin-react@^7.27.1:
  version "7.34.1"
  resolved "https://mirrors.tencent.com/npm/eslint-plugin-react/-/eslint-plugin-react-7.34.1.tgz"
  integrity sha512-N97CxlouPT1AHt8Jn0mhhN2RrADlUAsk1/atcT2KyA/l9Q/E6ll7OIGwNumFmWfZ9skV3XXccYS19h80rHtgkw==
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlast "^1.2.4"
    array.prototype.flatmap "^1.3.2"
    array.prototype.toreversed "^1.1.2"
    array.prototype.tosorted "^1.1.3"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.0.17"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.7"
    object.fromentries "^2.0.7"
    object.hasown "^1.1.3"
    object.values "^1.1.7"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.10"

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://mirrors.tencent.com/npm/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://mirrors.tencent.com/npm/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://mirrors.tencent.com/npm/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@^8.46.0:
  version "8.57.0"
  resolved "https://mirrors.tencent.com/npm/eslint/-/eslint-8.57.0.tgz"
  integrity sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.0"
    "@humanwhocodes/config-array" "^0.11.14"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://mirrors.tencent.com/npm/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.1:
  version "4.0.1"
  resolved "https://mirrors.tencent.com/npm/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.4.2:
  version "1.5.0"
  resolved "https://mirrors.tencent.com/npm/esquery/-/esquery-1.5.0.tgz"
  integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://mirrors.tencent.com/npm/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://mirrors.tencent.com/npm/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://mirrors.tencent.com/npm/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^2.0.1:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/eventemitter3/-/eventemitter3-2.0.3.tgz"
  integrity sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://mirrors.tencent.com/npm/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/eventemitter3/-/eventemitter3-5.0.1.tgz"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

events@^3.3.0:
  version "3.3.0"
  resolved "https://mirrors.tencent.com/npm/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@7.2.0:
  version "7.2.0"
  resolved "https://mirrors.tencent.com/npm/execa/-/execa-7.2.0.tgz"
  integrity sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

execa@^0.8.0:
  version "0.8.0"
  resolved "https://mirrors.tencent.com/npm/execa/-/execa-0.8.0.tgz"
  integrity sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA==
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://mirrors.tencent.com/npm/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

extend@^3.0.2:
  version "3.0.2"
  resolved "https://mirrors.tencent.com/npm/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

extendable-error@^0.1.7:
  version "0.1.7"
  resolved "https://mirrors.tencent.com/npm/extendable-error/-/extendable-error-0.1.7.tgz"
  integrity sha512-UOiS2in6/Q0FK0R0q6UY9vYpQ21mr/Qn1KOnte7vsACuNJf514WvCCUHSRCPcgjPT2bAhNIJdlE6bVap1GKmeg==

fabric@^6.3.0:
  version "6.3.0"
  resolved "https://mirrors.tencent.com/npm/fabric/-/fabric-6.3.0.tgz"
  integrity sha512-sbuEb+WBjISVKq+Rtwc8+tdQRVpCDUj0XvFu3n+Iw33uydkAYspMaFX0DU32AglLTPPKFqx4/b4CYgApPyfrzA==
  optionalDependencies:
    canvas "^2.11.2"
    jsdom "^20.0.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://mirrors.tencent.com/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-diff@1.1.2:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/fast-diff/-/fast-diff-1.1.2.tgz"
  integrity sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/fast-diff/-/fast-diff-1.3.0.tgz"
  integrity sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==

fast-glob@^3.2.9, fast-glob@^3.3.0:
  version "3.3.2"
  resolved "https://mirrors.tencent.com/npm/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://mirrors.tencent.com/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fast-shallow-equal@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/fast-shallow-equal/-/fast-shallow-equal-1.0.0.tgz#d4dcaf6472440dcefa6f88b98e3251e27f25628b"
  integrity sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==

fast-xml-parser@4.5.0:
  version "4.5.0"
  resolved "https://mirrors.tencent.com/npm/fast-xml-parser/-/fast-xml-parser-4.5.0.tgz#2882b7d01a6825dfdf909638f2de0256351def37"
  integrity sha512-/PlTQCI96+fZMAOLMZK4CWG1ItCbfZ/0jx7UIJFChPNrx7tcEgerUgWbeieCM9MfHInUDyK8DWYZ+YrywDJuTg==
  dependencies:
    strnum "^1.0.5"

fastest-stable-stringify@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/fastest-stable-stringify/-/fastest-stable-stringify-2.0.2.tgz#3757a6774f6ec8de40c4e86ec28ea02417214c76"
  integrity sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://mirrors.tencent.com/npm/fastq/-/fastq-1.17.1.tgz"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://mirrors.tencent.com/npm/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

file-selector@^0.4.0:
  version "0.4.0"
  resolved "https://mirrors.tencent.com/npm/file-selector/-/file-selector-0.4.0.tgz"
  integrity sha512-iACCiXeMYOvZqlF1kTiYINzgepRBymz1wwjiuup9u9nayhb6g4fSwiyJ/6adli+EPwrWtpgQAh2PoS7HukEGEg==
  dependencies:
    tslib "^2.0.3"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://mirrors.tencent.com/npm/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/filter-obj/-/filter-obj-1.1.0.tgz"
  integrity sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/find-root/-/find-root-1.1.0.tgz"
  integrity sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

find-yarn-workspace-root@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/find-yarn-workspace-root/-/find-yarn-workspace-root-2.0.0.tgz"
  integrity sha512-1IMnbjt4KzsQfnhnzNd8wUEgXZ44IzZaZmnLYx7D5FZlaHt2gW20Cri8Q+E/t5tIj4+epTBub+2Zxu/vNILzqQ==
  dependencies:
    micromatch "^4.0.2"

fixbug-protobufjs@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/fixbug-protobufjs/-/fixbug-protobufjs-1.0.1.tgz"
  integrity sha512-R8Ns5UMddZEr+uo37optxYAsLueFcuaxBnWzNC5Z53ywi1FBvDe2H0Ajru7Gu4oFXFNY6HQBasp5ZfAh5jzDaw==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" ">=13.7.0"
    long "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://mirrors.tencent.com/npm/flat-cache/-/flat-cache-3.2.0.tgz"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.1"
  resolved "https://mirrors.tencent.com/npm/flatted/-/flatted-3.3.1.tgz"
  integrity sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==

flv.js@1.6.2:
  version "1.6.2"
  resolved "https://mirrors.tencent.com/npm/flv.js/-/flv.js-1.6.2.tgz#fa3340fe3f7ee01d3977f7876aee66b8436e5922"
  integrity sha512-xre4gUbX1MPtgQRKj2pxJENp/RnaHaxYvy3YToVVCrSmAWUu85b9mug6pTXF6zakUjNP2lFWZ1rkSX7gxhB/2A==
  dependencies:
    es6-promise "^4.2.8"
    webworkify-webpack "^2.1.5"

follow-redirects@^1.14.9, follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://mirrors.tencent.com/npm/follow-redirects/-/follow-redirects-1.15.6.tgz"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://mirrors.tencent.com/npm/for-each/-/for-each-0.3.3.tgz"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/foreground-child/-/foreground-child-3.1.1.tgz"
  integrity sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg==
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/form-data/-/form-data-4.0.0.tgz"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

frac@~1.1.2:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/frac/-/frac-1.1.2.tgz"
  integrity sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://mirrors.tencent.com/npm/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

fs-extra@^11.0.0, fs-extra@^11.2.0:
  version "11.2.0"
  resolved "https://mirrors.tencent.com/npm/fs-extra/-/fs-extra-11.2.0.tgz"
  integrity sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^9.0.0:
  version "9.1.0"
  resolved "https://mirrors.tencent.com/npm/fs-extra/-/fs-extra-9.1.0.tgz"
  integrity sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "https://mirrors.tencent.com/npm/fsevents/-/fsevents-2.3.3.tgz"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.5, function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "https://mirrors.tencent.com/npm/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
  integrity sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://mirrors.tencent.com/npm/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

gauge@^3.0.0:
  version "3.0.2"
  resolved "https://mirrors.tencent.com/npm/gauge/-/gauge-3.0.2.tgz"
  integrity sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://mirrors.tencent.com/npm/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://mirrors.tencent.com/npm/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-east-asian-width@^1.0.0:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/get-east-asian-width/-/get-east-asian-width-1.3.0.tgz#21b4071ee58ed04ee0db653371b55b4299875389"
  integrity sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://mirrors.tencent.com/npm/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/get-stream/-/get-stream-3.0.0.tgz"
  integrity sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==

get-stream@^4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/get-stream/-/get-stream-4.1.0.tgz"
  integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
  dependencies:
    pump "^3.0.0"

get-stream@^5.1.0:
  version "5.2.0"
  resolved "https://mirrors.tencent.com/npm/get-stream/-/get-stream-5.2.0.tgz"
  integrity sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0, get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://mirrors.tencent.com/npm/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-symbol-description@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/get-symbol-description/-/get-symbol-description-1.0.2.tgz"
  integrity sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==
  dependencies:
    call-bind "^1.0.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"

git-raw-commits@^2.0.11:
  version "2.0.11"
  resolved "https://mirrors.tencent.com/npm/git-raw-commits/-/git-raw-commits-2.0.11.tgz"
  integrity sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A==
  dependencies:
    dargs "^7.0.0"
    lodash "^4.17.15"
    meow "^8.0.0"
    split2 "^3.0.0"
    through2 "^4.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://mirrors.tencent.com/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://mirrors.tencent.com/npm/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.3.12"
  resolved "https://mirrors.tencent.com/npm/glob/-/glob-10.3.12.tgz"
  integrity sha512-TCNv8vJ+xz4QiqTpfOJA7HvYv+tNIRHKfUWw/q+v2jdgN4ebz+KY9tGx5J4rHP0o84mNP+ApH66HRX8us3Khqg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^2.3.6"
    minimatch "^9.0.1"
    minipass "^7.0.4"
    path-scurry "^1.10.2"

glob@^7.1.3, glob@^7.2.3:
  version "7.2.3"
  resolved "https://mirrors.tencent.com/npm/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.1:
  version "0.1.1"
  resolved "https://mirrors.tencent.com/npm/global-dirs/-/global-dirs-0.1.1.tgz"
  integrity sha512-NknMLn7F2J7aflwFOlGdNIuCDpN3VGoSoB+aap3KABFWbHVn1TCgFC+np23J8W2BiZbjfEw3BFBycSMv1AFblg==
  dependencies:
    ini "^1.3.4"

global-dirs@^3.0.0:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/global-dirs/-/global-dirs-3.0.1.tgz"
  integrity sha512-NBcGGFbBA9s1VzD41QXDG+3++t9Mn5t1FpLdhESY6oKY4gYTFpX4wO3sqGUa0Srjtbfj3szX0RnemmrVRUdULA==
  dependencies:
    ini "2.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://mirrors.tencent.com/npm/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.19.0:
  version "13.24.0"
  resolved "https://mirrors.tencent.com/npm/globals/-/globals-13.24.0.tgz"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/globalthis/-/globalthis-1.0.4.tgz"
  integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://mirrors.tencent.com/npm/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

got@^9.6.0:
  version "9.6.0"
  resolved "https://mirrors.tencent.com/npm/got/-/got-9.6.0.tgz"
  integrity sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==
  dependencies:
    "@sindresorhus/is" "^0.14.0"
    "@szmarczak/http-timer" "^1.1.2"
    cacheable-request "^6.0.0"
    decompress-response "^3.3.0"
    duplexer3 "^0.1.4"
    get-stream "^4.1.0"
    lowercase-keys "^1.0.1"
    mimic-response "^1.0.1"
    p-cancelable "^1.0.0"
    to-readable-stream "^1.0.0"
    url-parse-lax "^3.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://mirrors.tencent.com/npm/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

gud@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/gud/-/gud-1.0.0.tgz"
  integrity sha512-zGEOVKFM5sVPPrYs7J5/hYEw2Pof8KCyOwyhG8sAF26mCAeUFAcYPu1mwB7hhpIP29zOIBaDqwuHdLp0jvZXjw==

hamt_plus@1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/hamt_plus/-/hamt_plus-1.0.2.tgz"
  integrity sha512-t2JXKaehnMb9paaYA7J0BX8QQAY8lwfQ9Gjf4pg/mk4krt+cmwmU652HOoWonf+7+EQV97ARPMhhVgU1ra2GhA==

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/hard-rejection/-/hard-rejection-2.1.0.tgz"
  integrity sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/has-bigints/-/has-bigints-1.0.2.tgz"
  integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1, has-proto@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/has-proto/-/has-proto-1.0.3.tgz"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

has-yarn@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/has-yarn/-/has-yarn-2.1.0.tgz"
  integrity sha512-UqBRqi4ju7T+TqGNdqAO0PaSVGsDGJUBQvk9eUWNGRY1CFGDzYhLWoM7JQEemnlvVcv/YEmc2wNW8BC24EnUsw==

hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

header-case@^2.0.4:
  version "2.0.4"
  resolved "https://mirrors.tencent.com/npm/header-case/-/header-case-2.0.4.tgz"
  integrity sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==
  dependencies:
    capital-case "^1.0.4"
    tslib "^2.0.3"

highlight-words-core@^1.2.3:
  version "1.2.3"
  resolved "https://mirrors.tencent.com/npm/highlight-words-core/-/highlight-words-core-1.2.3.tgz#781f37b2a220bf998114e4ef8c8cb6c7a4802ea8"
  integrity sha512-m1O9HW3/GNHxzSIXWw1wCNXXsgLlxrP0OI6+ycGUhiUHkikqW3OrwVHz+lxeNBe5yqLESdIcj8PowHQ2zLvUvQ==

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1, hoist-non-react-statics@^3.3.2, hoist-non-react-statics@~3.3.2:
  version "3.3.2"
  resolved "https://mirrors.tencent.com/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://mirrors.tencent.com/npm/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  integrity sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/hosted-git-info/-/hosted-git-info-4.1.0.tgz"
  integrity sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==
  dependencies:
    lru-cache "^6.0.0"

hotkeys-js@^3.8.1:
  version "3.13.7"
  resolved "https://mirrors.tencent.com/npm/hotkeys-js/-/hotkeys-js-3.13.7.tgz#0188d8e2fca16a3f1d66541b48de0bb9df613726"
  integrity sha512-ygFIdTqqwG4fFP7kkiYlvayZppeIQX2aPpirsngkv1xM1lP0piDY5QEh68nQnIKvz64hfocxhBaD/uK3sSK1yQ==

html-encoding-sniffer@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz"
  integrity sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==
  dependencies:
    whatwg-encoding "^2.0.0"

html2canvas@^1.4.1:
  version "1.4.1"
  resolved "https://mirrors.tencent.com/npm/html2canvas/-/html2canvas-1.4.1.tgz"
  integrity sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

http-cache-semantics@^4.0.0:
  version "4.1.1"
  resolved "https://mirrors.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz"
  integrity sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==

http-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
  integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
  dependencies:
    "@tootallnate/once" "2"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0, https-proxy-agent@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

human-signals@^4.3.0:
  version "4.3.1"
  resolved "https://mirrors.tencent.com/npm/human-signals/-/human-signals-4.3.1.tgz"
  integrity sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==

hyphenate-style-name@^1.0.3:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz#1797bf50369588b47b72ca6d5e65374607cf4436"
  integrity sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==

iconv-lite@0.6.3, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://mirrors.tencent.com/npm/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore@^5.2.0:
  version "5.3.1"
  resolved "https://mirrors.tencent.com/npm/ignore/-/ignore-5.3.1.tgz"
  integrity sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==

image-size@~0.5.0:
  version "0.5.5"
  resolved "https://mirrors.tencent.com/npm/image-size/-/image-size-0.5.5.tgz"
  integrity sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://mirrors.tencent.com/npm/immediate/-/immediate-3.0.6.tgz"
  integrity sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==

immer@6.0.9:
  version "6.0.9"
  resolved "https://mirrors.tencent.com/npm/immer/-/immer-6.0.9.tgz#b9dd69b8e69b3a12391e87db1e3ff535d1b26485"
  integrity sha512-SyCYnAuiRf67Lvk0VkwFvwtDoEiCMjeamnHvRfnVDyc7re1/rQrNxuL+jJ7lA3WvdC4uznrvbmm+clJ9+XXatg==

immer@^8.0.4:
  version "8.0.4"
  resolved "https://mirrors.tencent.com/npm/immer/-/immer-8.0.4.tgz"
  integrity sha512-jMfL18P+/6P6epANRvRk6q8t+3gGhqsJ9EuJ25AXE+9bNTYtssvzeYbEd0mXRYWCmmXSIbnlpz6vd6iJlmGGGQ==

immutable@^5.0.2:
  version "5.0.2"
  resolved "https://mirrors.tencent.com/npm/immutable/-/immutable-5.0.2.tgz#bb8a987349a73efbe6b3b292a9cbaf1b530d296b"
  integrity sha512-1NU7hWZDkV7hJ4PJ9dur9gTNQ4ePNPN4k9/0YhwjzykTi/+3Q5pF93YU5QoVj8BuOnhLgaY8gs0U2pj4kSYVcw==

import-fresh@^3.0.0, import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  resolved "https://mirrors.tencent.com/npm/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/import-lazy/-/import-lazy-2.1.0.tgz"
  integrity sha512-m7ZEHgtw69qOGw+jwxXkHlrlIPdTGkyh66zXZ1ajZbxkDBNjSY/LGbmjc7h0s2ELsUDTAhFr55TrPSSqJGPG0A==

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://mirrors.tencent.com/npm/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3:
  version "2.0.4"
  resolved "https://mirrors.tencent.com/npm/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ini@2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/ini/-/ini-2.0.0.tgz"
  integrity sha512-7PnF4oN3CvZF23ADhA5wRaYEQpJ8qygSkbtTXWBeXWXmEVRXK+1ITciHWwHhsjv1TmW0MgacIv6hEi5pX5NQdA==

ini@^1.3.4, ini@~1.3.0:
  version "1.3.8"
  resolved "https://mirrors.tencent.com/npm/ini/-/ini-1.3.8.tgz"
  integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==

inline-style-prefixer@^7.0.1:
  version "7.0.1"
  resolved "https://mirrors.tencent.com/npm/inline-style-prefixer/-/inline-style-prefixer-7.0.1.tgz#9310f3cfa2c6f3901d1480f373981c02691781e8"
  integrity sha512-lhYo5qNTQp3EvSSp3sRvXMbVQTLrvGV6DycRMJ5dm2BLMiJ30wpXKdDdgX+GmJZ5uQMucwRKHamXSst3Sj/Giw==
  dependencies:
    css-in-js-utils "^3.1.0"

internal-slot@^1.0.7:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/internal-slot/-/internal-slot-1.0.7.tgz"
  integrity sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.0"
    side-channel "^1.0.4"

intersection-observer-polyfill@^0.1.0:
  version "0.1.0"
  resolved "https://mirrors.tencent.com/npm/intersection-observer-polyfill/-/intersection-observer-polyfill-0.1.0.tgz"
  integrity sha512-kYxriUk7Dl8GPu2TuOFAbtG5+UK0LLrLzlUErF8R9w5iPWmrtfVZsjCYbGF0/SyRDo1VfHMOZYBB2svb2locbw==

intersection-observer@^0.12.0:
  version "0.12.2"
  resolved "https://mirrors.tencent.com/npm/intersection-observer/-/intersection-observer-0.12.2.tgz"
  integrity sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==

invariant@^2.2.4:
  version "2.2.4"
  resolved "https://mirrors.tencent.com/npm/invariant/-/invariant-2.2.4.tgz"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

is-arguments@^1.1.1:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/is-arguments/-/is-arguments-1.1.1.tgz"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-array-buffer@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/is-array-buffer/-/is-array-buffer-3.0.4.tgz"
  integrity sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://mirrors.tencent.com/npm/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-async-function@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/is-async-function/-/is-async-function-2.0.0.tgz"
  integrity sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/is-bigint/-/is-bigint-1.0.4.tgz"
  integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@~1.1.6:
  version "1.1.6"
  resolved "https://mirrors.tencent.com/npm/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://mirrors.tencent.com/npm/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/is-ci/-/is-ci-1.2.1.tgz"
  integrity sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==
  dependencies:
    ci-info "^1.5.0"

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/is-ci/-/is-ci-2.0.0.tgz"
  integrity sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.13.0, is-core-module@^2.13.1, is-core-module@^2.5.0:
  version "2.13.1"
  resolved "https://mirrors.tencent.com/npm/is-core-module/-/is-core-module-2.13.1.tgz"
  integrity sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==
  dependencies:
    hasown "^2.0.0"

is-data-view@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/is-data-view/-/is-data-view-1.0.1.tgz"
  integrity sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==
  dependencies:
    is-typed-array "^1.1.13"

is-date-object@^1.0.1, is-date-object@^1.0.5:
  version "1.0.5"
  resolved "https://mirrors.tencent.com/npm/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://mirrors.tencent.com/npm/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://mirrors.tencent.com/npm/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-finalizationregistry@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/is-finalizationregistry/-/is-finalizationregistry-1.0.2.tgz"
  integrity sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==
  dependencies:
    call-bind "^1.0.2"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz"
  integrity sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==

is-fullwidth-code-point@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-5.0.0.tgz#9609efced7c2f97da7b60145ef481c787c7ba704"
  integrity sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==
  dependencies:
    get-east-asian-width "^1.0.0"

is-generator-function@^1.0.10:
  version "1.0.10"
  resolved "https://mirrors.tencent.com/npm/is-generator-function/-/is-generator-function-1.0.10.tgz"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://mirrors.tencent.com/npm/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-installed-globally@^0.4.0:
  version "0.4.0"
  resolved "https://mirrors.tencent.com/npm/is-installed-globally/-/is-installed-globally-0.4.0.tgz"
  integrity sha512-iwGqO3J21aaSkC7jWnHP/difazwS7SFeIqxv6wEtLU8Y5KlzFTjyqcSIT0d8s4+dDhKytsk9PJZ2BkS5eZwQRQ==
  dependencies:
    global-dirs "^3.0.0"
    is-path-inside "^3.0.2"

is-map@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/is-map/-/is-map-2.0.3.tgz"
  integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
  integrity sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==

is-npm@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/is-npm/-/is-npm-5.0.0.tgz"
  integrity sha512-WW/rQLOazUq+ST/bCAVBp/2oMERWLsR7OrKyt052dNDk4DHcDE0/7QSXITlmi+VBcV13DfIbysG3tZJm5RfdBA==

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/is-number-object/-/is-number-object-1.0.7.tgz"
  integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/is-obj/-/is-obj-2.0.0.tgz"
  integrity sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==

is-path-inside@^3.0.2, is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://mirrors.tencent.com/npm/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  integrity sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://mirrors.tencent.com/npm/is-regex/-/is-regex-1.1.4.tgz"
  integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/is-set/-/is-set-2.0.3.tgz"
  integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==

is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz"
  integrity sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==
  dependencies:
    call-bind "^1.0.7"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/is-stream/-/is-stream-1.1.0.tgz"
  integrity sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-stream@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/is-stream/-/is-stream-3.0.0.tgz"
  integrity sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/is-string/-/is-string-1.0.7.tgz"
  integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/is-symbol/-/is-symbol-1.0.4.tgz"
  integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
  dependencies:
    has-symbols "^1.0.2"

is-text-path@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/is-text-path/-/is-text-path-1.0.1.tgz"
  integrity sha512-xFuJpne9oFz5qDaodwmmG08e3CawH/2ZV8Qqza1Ko7Sk8POWbkRdwIoAWVhqvq0XeUzANEhKo2n0IXUGBm7A/w==
  dependencies:
    text-extensions "^1.0.0"

is-typed-array@^1.1.13:
  version "1.1.13"
  resolved "https://mirrors.tencent.com/npm/is-typed-array/-/is-typed-array-1.1.13.tgz"
  integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
  dependencies:
    which-typed-array "^1.1.14"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/is-typedarray/-/is-typedarray-1.0.0.tgz"
  integrity sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/is-weakmap/-/is-weakmap-2.0.2.tgz"
  integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/is-weakref/-/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-weakset@^2.0.3:
  version "2.0.3"
  resolved "https://mirrors.tencent.com/npm/is-weakset/-/is-weakset-2.0.3.tgz"
  integrity sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"

is-what@^3.14.1:
  version "3.14.1"
  resolved "https://mirrors.tencent.com/npm/is-what/-/is-what-3.14.1.tgz"
  integrity sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==

is-wsl@^2.1.1, is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

is-yarn-global@^0.3.0:
  version "0.3.0"
  resolved "https://mirrors.tencent.com/npm/is-yarn-global/-/is-yarn-global-0.3.0.tgz"
  integrity sha512-VjSeb/lHmkoyd8ryPVIKvOCn4D1koMqY+vqyjjUfc3xyKtP4dYOxM44sZrnqQSzSds3xyOrUTLTC9LVCVgLngw==

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://mirrors.tencent.com/npm/isarray/-/isarray-2.0.5.tgz"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

iterator.prototype@^1.1.2:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/iterator.prototype/-/iterator.prototype-1.1.2.tgz"
  integrity sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==
  dependencies:
    define-properties "^1.2.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    reflect.getprototypeof "^1.0.4"
    set-function-name "^2.0.1"

its-fine@^1.0.6:
  version "1.2.5"
  resolved "https://mirrors.tencent.com/npm/its-fine/-/its-fine-1.2.5.tgz#5466c287f86a0a73e772c8d8d515626c97195dc9"
  integrity sha512-fXtDA0X0t0eBYAGLVM5YsgJGsJ5jEmqZEPrGbzdf5awjv0xE7nqv3TVnvtUF060Tkes15DbDAKW/I48vsb6SyA==
  dependencies:
    "@types/react-reconciler" "^0.28.0"

jackspeak@^2.3.6:
  version "2.3.6"
  resolved "https://mirrors.tencent.com/npm/jackspeak/-/jackspeak-2.3.6.tgz"
  integrity sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^1.21.0:
  version "1.21.0"
  resolved "https://mirrors.tencent.com/npm/jiti/-/jiti-1.21.0.tgz"
  integrity sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==

js-base64@^3.7.7:
  version "3.7.7"
  resolved "https://mirrors.tencent.com/npm/js-base64/-/js-base64-3.7.7.tgz"
  integrity sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==

js-cookie@^2.2.1, js-cookie@^2.x.x:
  version "2.2.1"
  resolved "https://mirrors.tencent.com/npm/js-cookie/-/js-cookie-2.2.1.tgz"
  integrity sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==

js-sha3@0.8.0:
  version "0.8.0"
  resolved "https://mirrors.tencent.com/npm/js-sha3/-/js-sha3-0.8.0.tgz"
  integrity sha512-gF1cRrHhIzNfToc802P800N8PpXS+evLLXfsVpowqmAFR9uwbi89WvXg2QspOmXL8QL86J4T1EpFu+yUkwJY3Q==

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsdom@^20.0.1:
  version "20.0.3"
  resolved "https://mirrors.tencent.com/npm/jsdom/-/jsdom-20.0.3.tgz"
  integrity sha512-SYhBvTh89tTfCD/CRdSOm13mOBa42iTaTyfyEWBdKcGdPxPtLFBXuHR8XHb33YNYaP+lLbmSvBTsnoesCNJEsQ==
  dependencies:
    abab "^2.0.6"
    acorn "^8.8.1"
    acorn-globals "^7.0.0"
    cssom "^0.5.0"
    cssstyle "^2.3.0"
    data-urls "^3.0.2"
    decimal.js "^10.4.2"
    domexception "^4.0.0"
    escodegen "^2.0.0"
    form-data "^4.0.0"
    html-encoding-sniffer "^3.0.0"
    http-proxy-agent "^5.0.0"
    https-proxy-agent "^5.0.1"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.2"
    parse5 "^7.1.1"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^4.1.2"
    w3c-xmlserializer "^4.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^2.0.0"
    whatwg-mimetype "^3.0.0"
    whatwg-url "^11.0.0"
    ws "^8.11.0"
    xml-name-validator "^4.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://mirrors.tencent.com/npm/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

json-buffer@3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/json-buffer/-/json-buffer-3.0.0.tgz"
  integrity sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ==

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-defaults@^0.4.0:
  version "0.4.0"
  resolved "https://mirrors.tencent.com/npm/json-schema-defaults/-/json-schema-defaults-0.4.0.tgz"
  integrity sha512-UsUrkDVNvHTneyeQOYHH9ZHb3+6OjwYfJ831SdO0yjtXtYZ7Jh8BKWsuJYUQW7qckP5JhHawsg4GI6A5fMaR/Q==
  dependencies:
    argparse "^1.0.9"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://mirrors.tencent.com/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/json2mq/-/json2mq-0.2.0.tgz"
  integrity sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==
  dependencies:
    string-convert "^0.2.0"

json5@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://mirrors.tencent.com/npm/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://mirrors.tencent.com/npm/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "https://mirrors.tencent.com/npm/jsonparse/-/jsonparse-1.3.1.tgz"
  integrity sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==

jsx-ast-utils@^2.2.1:
  version "2.4.1"
  resolved "https://mirrors.tencent.com/npm/jsx-ast-utils/-/jsx-ast-utils-2.4.1.tgz"
  integrity sha512-z1xSldJ6imESSzOjd3NNkieVJKRlKYSOtMG8SFyCj2FIrvSaSuli/WjpBkEzCBoR9bYYYFgqJw61Xhu7Lcgk+w==
  dependencies:
    array-includes "^3.1.1"
    object.assign "^4.1.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  resolved "https://mirrors.tencent.com/npm/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
  integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^3.0.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/keyv/-/keyv-3.1.0.tgz"
  integrity sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==
  dependencies:
    json-buffer "3.0.0"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://mirrors.tencent.com/npm/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

kind-of@^6.0.3:
  version "6.0.3"
  resolved "https://mirrors.tencent.com/npm/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

klaw-sync@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/klaw-sync/-/klaw-sync-6.0.0.tgz"
  integrity sha512-nIeuVSzdCCs6TDPTqI8w1Yre34sSq7AkZ4B3sfOBbI2CgVSB4Du4aLQijFU2+lhAFCwt9+42Hel6lQNIv6AntQ==
  dependencies:
    graceful-fs "^4.1.11"

language-subtag-registry@^0.3.20:
  version "0.3.22"
  resolved "https://mirrors.tencent.com/npm/language-subtag-registry/-/language-subtag-registry-0.3.22.tgz"
  integrity sha512-tN0MCzyWnoz/4nHS6uxdlFWoUZT7ABptwKPQ52Ea7URk6vll88bWBVhodtnlfEuCcKWNGoc+uGbw1cwa9IKh/w==

language-tags@^1.0.9:
  version "1.0.9"
  resolved "https://mirrors.tencent.com/npm/language-tags/-/language-tags-1.0.9.tgz"
  integrity sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
  dependencies:
    language-subtag-registry "^0.3.20"

latest-version@^5.1.0:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/latest-version/-/latest-version-5.1.0.tgz"
  integrity sha512-weT+r0kTkRQdCdYCNtkMwWXQTMEswKrFBkm4ckQOMVhhqhIMI1UT2hMj+1iigIhgSZm5gTmrRXBNoGUgaTY1xA==
  dependencies:
    package-json "^6.3.0"

less-plugin-module-resolver@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/less-plugin-module-resolver/-/less-plugin-module-resolver-1.0.3.tgz"
  integrity sha512-w5xD9Kv1KpqG0ie181t1RH99n0tfbGpBnajrpOfRKJgqrlRdKsfYax5WUFqRlJ1tE5pJqssV6FJQeodY0IGylw==

less@^4.2.0:
  version "4.2.0"
  resolved "https://mirrors.tencent.com/npm/less/-/less-4.2.0.tgz"
  integrity sha512-P3b3HJDBtSzsXUl0im2L7gTO5Ubg8mEN6G8qoTS77iXxXX4Hvu4Qj540PZDvQ8V6DmX6iXo98k7Md0Cm1PrLaA==
  dependencies:
    copy-anything "^2.0.1"
    parse-node-version "^1.0.1"
    tslib "^2.3.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    needle "^3.1.0"
    source-map "~0.6.0"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://mirrors.tencent.com/npm/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lie@3.1.1:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/lie/-/lie-3.1.1.tgz"
  integrity sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==
  dependencies:
    immediate "~3.0.5"

lilconfig@2.1.0, lilconfig@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/lilconfig/-/lilconfig-2.1.0.tgz"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lilconfig@^3.0.0:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/lilconfig/-/lilconfig-3.1.1.tgz"
  integrity sha512-O18pf7nyvHTckunPWCV1XUNXU1piu01y2b7ATJ0ppkUkk8ocqVWBrYjJBCwHDjD/ZWcfyrA0P4gKhzWGi5EINQ==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://mirrors.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

lint-staged@^13.0.3:
  version "13.3.0"
  resolved "https://mirrors.tencent.com/npm/lint-staged/-/lint-staged-13.3.0.tgz"
  integrity sha512-mPRtrYnipYYv1FEE134ufbWpeggNTo+O/UPzngoaKzbzHAthvR55am+8GfHTnqNRQVRRrYQLGW9ZyUoD7DsBHQ==
  dependencies:
    chalk "5.3.0"
    commander "11.0.0"
    debug "4.3.4"
    execa "7.2.0"
    lilconfig "2.1.0"
    listr2 "6.6.1"
    micromatch "4.0.5"
    pidtree "0.6.0"
    string-argv "0.3.2"
    yaml "2.3.1"

listr2@6.6.1:
  version "6.6.1"
  resolved "https://mirrors.tencent.com/npm/listr2/-/listr2-6.6.1.tgz"
  integrity sha512-+rAXGHh0fkEWdXBmX+L6mmfmXmXvDGEKzkjxO+8mP3+nI/r/CWznVBvsibXdxda9Zz0OW2e2ikphN3OwCT/jSg==
  dependencies:
    cli-truncate "^3.1.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^5.0.1"
    rfdc "^1.3.0"
    wrap-ansi "^8.1.0"

listr2@^8.2.4:
  version "8.2.5"
  resolved "https://mirrors.tencent.com/npm/listr2/-/listr2-8.2.5.tgz#5c9db996e1afeb05db0448196d3d5f64fec2593d"
  integrity sha512-iyAZCeyD+c1gPyE9qpFu8af0Y+MRtmKOncdGoA2S5EY8iFq99dmmvkNnHiWo+pj0s7yH7l3KPIgee77tKpXPWQ==
  dependencies:
    cli-truncate "^4.0.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^6.1.0"
    rfdc "^1.4.1"
    wrap-ansi "^9.0.0"

localforage@^1.10.0:
  version "1.10.0"
  resolved "https://mirrors.tencent.com/npm/localforage/-/localforage-1.10.0.tgz"
  integrity sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==
  dependencies:
    lie "3.1.1"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.15, lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://mirrors.tencent.com/npm/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"
  integrity sha512-xYHt68QRoYGjeeM/XOE1uJtvXQAgvszfBhjV4yvsQH0u2i9I6cI6c6/eG4Hh3UAOVn0y/xAXwmTzEay49Q//HA==

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://mirrors.tencent.com/npm/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://mirrors.tencent.com/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "https://mirrors.tencent.com/npm/lodash.foreach/-/lodash.foreach-4.5.0.tgz"
  integrity sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ==

lodash.isfunction@^3.0.9:
  version "3.0.9"
  resolved "https://mirrors.tencent.com/npm/lodash.isfunction/-/lodash.isfunction-3.0.9.tgz"
  integrity sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw==

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://mirrors.tencent.com/npm/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://mirrors.tencent.com/npm/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  integrity sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://mirrors.tencent.com/npm/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.mergewith@^4.6.2:
  version "4.6.2"
  resolved "https://mirrors.tencent.com/npm/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz"
  integrity sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==

lodash.snakecase@^4.1.1:
  version "4.1.1"
  resolved "https://mirrors.tencent.com/npm/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz"
  integrity sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw==

lodash.startcase@^4.4.0:
  version "4.4.0"
  resolved "https://mirrors.tencent.com/npm/lodash.startcase/-/lodash.startcase-4.4.0.tgz"
  integrity sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg==

lodash.template@^4.5.0:
  version "4.5.0"
  resolved "https://mirrors.tencent.com/npm/lodash.template/-/lodash.template-4.5.0.tgz#f976195cf3f347d0d5f52483569fe8031ccce8ab"
  integrity sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A==
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "https://mirrors.tencent.com/npm/lodash.templatesettings/-/lodash.templatesettings-4.2.0.tgz#e481310f049d3cf6d47e912ad09313b154f0fb33"
  integrity sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ==
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://mirrors.tencent.com/npm/lodash.throttle/-/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://mirrors.tencent.com/npm/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  integrity sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==

lodash.upperfirst@^4.3.1:
  version "4.3.1"
  resolved "https://mirrors.tencent.com/npm/lodash.upperfirst/-/lodash.upperfirst-4.3.1.tgz"
  integrity sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg==

lodash@^4.0.1, lodash@^4.17.15, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.4, lodash@~4.17.15:
  version "4.17.21"
  resolved "https://mirrors.tencent.com/npm/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-update@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/log-update/-/log-update-5.0.1.tgz"
  integrity sha512-5UtUDQ/6edw4ofyljDNcOVJQ4c7OjDro4h3y8e1GQL5iYElYclVHJ3zeWchylvMaKnDbDilC8irOVyexnA/Slw==
  dependencies:
    ansi-escapes "^5.0.0"
    cli-cursor "^4.0.0"
    slice-ansi "^5.0.0"
    strip-ansi "^7.0.1"
    wrap-ansi "^8.0.1"

log-update@^6.1.0:
  version "6.1.0"
  resolved "https://mirrors.tencent.com/npm/log-update/-/log-update-6.1.0.tgz#1a04ff38166f94647ae1af562f4bd6a15b1b7cd4"
  integrity sha512-9ie8ItPR6tjY5uYJh8K/Zrv/RMZ5VOlOWvtZdEHYSTFKZfIBPQa9tOAEeAWhd+AnIneLJ22w5fjOYtoutpWq5w==
  dependencies:
    ansi-escapes "^7.0.0"
    cli-cursor "^5.0.0"
    slice-ansi "^7.1.0"
    strip-ansi "^7.1.0"
    wrap-ansi "^9.0.0"

long@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/long/-/long-4.0.0.tgz"
  integrity sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==

long@^5.0.0:
  version "5.2.3"
  resolved "https://mirrors.tencent.com/npm/long/-/long-5.2.3.tgz#a3ba97f3877cf1d778eccbcb048525ebb77499e1"
  integrity sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.1, lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/lower-case/-/lower-case-2.0.2.tgz"
  integrity sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==
  dependencies:
    tslib "^2.0.3"

lowercase-keys@^1.0.0, lowercase-keys@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
  integrity sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==

lowercase-keys@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/lowercase-keys/-/lowercase-keys-2.0.0.tgz"
  integrity sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==

lru-cache@^10.2.0:
  version "10.2.0"
  resolved "https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-10.2.0.tgz"
  integrity sha512-2bIM8x+VAf6JT4bKAljS1qUWgMsqZRPGJS6FSahIMPVvctcNhyVp7AJu7quxOW9jwkryBReKZY5tY5JYv2n/7Q==

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-4.1.5.tgz"
  integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "https://mirrors.tencent.com/npm/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

map-obj@^1.0.0:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/map-obj/-/map-obj-1.0.1.tgz"
  integrity sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==

map-obj@^4.0.0:
  version "4.3.0"
  resolved "https://mirrors.tencent.com/npm/map-obj/-/map-obj-4.3.0.tgz"
  integrity sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==

match-sorter@^6.0.2:
  version "6.3.4"
  resolved "https://mirrors.tencent.com/npm/match-sorter/-/match-sorter-6.3.4.tgz"
  integrity sha512-jfZW7cWS5y/1xswZo8VBOdudUiSd9nifYRWphc9M5D/ee4w4AoXLgBEdRbgVaxbMuagBPeUC5y2Hi8DO6o9aDg==
  dependencies:
    "@babel/runtime" "^7.23.8"
    remove-accents "0.5.0"

material-colors@^1.2.1:
  version "1.2.6"
  resolved "https://mirrors.tencent.com/npm/material-colors/-/material-colors-1.2.6.tgz"
  integrity sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg==

md5@^2.3.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/md5/-/md5-2.3.0.tgz#c3da9a6aae3a30b46b7b0c349b87b110dc3bda4f"
  integrity sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://mirrors.tencent.com/npm/mdn-data/-/mdn-data-2.0.14.tgz#7113fc4281917d63ce29b43446f701e68c25ba50"
  integrity sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==

"memoize-one@>=3.1.1 <6", memoize-one@^5.1.1:
  version "5.2.1"
  resolved "https://mirrors.tencent.com/npm/memoize-one/-/memoize-one-5.2.1.tgz"
  integrity sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

meow@^8.0.0, meow@^8.1.2:
  version "8.1.2"
  resolved "https://mirrors.tencent.com/npm/meow/-/meow-8.1.2.tgz"
  integrity sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q==
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://mirrors.tencent.com/npm/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@4.0.5:
  version "4.0.5"
  resolved "https://mirrors.tencent.com/npm/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.7"
  resolved "https://mirrors.tencent.com/npm/micromatch/-/micromatch-4.0.7.tgz"
  integrity sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

microseconds@0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/microseconds/-/microseconds-0.2.0.tgz"
  integrity sha512-n7DHHMjR1avBbSpsTBj6fmMGh2AGrifVV4e+WYc3Q9lO+xnSZ3NyhcBND3vzzatt05LFhoKFRxrIyklmLlUtyA==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://mirrors.tencent.com/npm/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://mirrors.tencent.com/npm/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@^1.4.1:
  version "1.6.0"
  resolved "https://mirrors.tencent.com/npm/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mime@^4.0.4:
  version "4.0.4"
  resolved "https://mirrors.tencent.com/npm/mime/-/mime-4.0.4.tgz"
  integrity sha512-v8yqInVjhXyqP6+Kw4fV3ZzeMRqEW6FotRsKXjRS5VMTNIuXsdRoAvklpoRgSqXm6o9VNH4/C0mgedko9DdLsQ==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/mimic-fn/-/mimic-fn-4.0.0.tgz"
  integrity sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==

mimic-function@^5.0.0:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/mimic-function/-/mimic-function-5.0.1.tgz#acbe2b3349f99b9deaca7fb70e48b83e94e67076"
  integrity sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==

mimic-response@^1.0.0, mimic-response@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/mimic-response/-/mimic-response-1.0.1.tgz"
  integrity sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==

mimic-response@^2.0.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/mimic-response/-/mimic-response-2.1.0.tgz"
  integrity sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==

min-indent@^1.0.0:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/min-indent/-/min-indent-1.0.1.tgz"
  integrity sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://mirrors.tencent.com/npm/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.1:
  version "9.0.4"
  resolved "https://mirrors.tencent.com/npm/minimatch/-/minimatch-9.0.4.tgz"
  integrity sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==
  dependencies:
    brace-expansion "^2.0.1"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/minimist-options/-/minimist-options-4.1.0.tgz"
  integrity sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://mirrors.tencent.com/npm/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass@^3.0.0:
  version "3.3.6"
  resolved "https://mirrors.tencent.com/npm/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/minipass/-/minipass-5.0.0.tgz"
  integrity sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.0.4:
  version "7.0.4"
  resolved "https://mirrors.tencent.com/npm/minipass/-/minipass-7.0.4.tgz"
  integrity sha512-jYofLM5Dam9279rdkWzqHozUo4ybjdZmCsDHePy5V/PbBcVMiSZR97gmAy45aqi8CK1lG2ECd356FU86avfwUQ==

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mitt@^3.0.0:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/mitt/-/mitt-3.0.1.tgz"
  integrity sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

mockjs@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/mockjs/-/mockjs-1.1.0.tgz#e6a0c378e91906dbaff20911cc0273b3c7d75b06"
  integrity sha512-eQsKcWzIaZzEZ07NuEyO4Nw65g0hdWAyurVol1IPl1gahRwY+svqzfgfey8U8dahLwG44d6/RwEzuK52rSa/JQ==
  dependencies:
    commander "*"

moment@^2.24.0, moment@^2.29.1, moment@^2.29.2, moment@^2.29.3, moment@^2.30.1:
  version "2.30.1"
  resolved "https://mirrors.tencent.com/npm/moment/-/moment-2.30.1.tgz"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

ms@2.1.2:
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@^2.1.1:
  version "2.1.3"
  resolved "https://mirrors.tencent.com/npm/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://mirrors.tencent.com/npm/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.17.0:
  version "2.20.0"
  resolved "https://mirrors.tencent.com/npm/nan/-/nan-2.20.0.tgz"
  integrity sha512-bk3gXBZDGILuuo/6sKtr0DQmSThYHLtNCdSdXk9YkxD/jK6X2vmCyyXBBxyqZ4XcnzTyYEAThfX3DCEnLf6igw==

nano-css@^5.2.1:
  version "5.6.2"
  resolved "https://mirrors.tencent.com/npm/nano-css/-/nano-css-5.6.2.tgz#584884ddd7547278f6d6915b6805069742679a32"
  integrity sha512-+6bHaC8dSDGALM1HJjOHVXpuastdu2xFoZlC77Jh4cg+33Zcgm+Gxd+1xsnpZK14eyHObSp82+ll5y3SX75liw==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"
    css-tree "^1.1.2"
    csstype "^3.1.2"
    fastest-stable-stringify "^2.0.2"
    inline-style-prefixer "^7.0.1"
    rtl-css-js "^1.16.1"
    stacktrace-js "^2.0.2"
    stylis "^4.3.0"

nano-time@1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/nano-time/-/nano-time-1.0.0.tgz"
  integrity sha512-flnngywOoQ0lLQOTRNexn2gGSNuM9bKj9RZAWSzhQ+UJYaAFG9bac4DW9VHjUAzrOaIcajHybCTHe/bkvozQqA==
  dependencies:
    big-integer "^1.6.16"

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://mirrors.tencent.com/npm/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

nanoid@^3.3.8:
  version "3.3.11"
  resolved "https://mirrors.tencent.com/npm/nanoid/-/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

nanoid@^4.0.0:
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/nanoid/-/nanoid-4.0.2.tgz"
  integrity sha512-7ZtY5KTCNheRGfEFxnedV5zFiORN1+Y1N6zvPTnHQd8ENUvfaDBeuJDZb2bN/oXwXxu3qkTXDzy57W5vAmDTBw==

natives@^1.1.6:
  version "1.1.6"
  resolved "https://mirrors.tencent.com/npm/natives/-/natives-1.1.6.tgz#a603b4a498ab77173612b9ea1acdec4d980f00bb"
  integrity sha512-6+TDFewD4yxY14ptjKaS63GVdtKiES1pTPyxn9Jb0rBqPMZ7VcCiooEhPNsr+mqHtMGxa/5c/HhcC4uPEUw/nA==

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"
  integrity sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

needle@^3.1.0:
  version "3.3.1"
  resolved "https://mirrors.tencent.com/npm/needle/-/needle-3.3.1.tgz"
  integrity sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==
  dependencies:
    iconv-lite "^0.6.3"
    sax "^1.2.4"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://mirrors.tencent.com/npm/nice-try/-/nice-try-1.0.5.tgz"
  integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/no-case/-/no-case-3.0.4.tgz"
  integrity sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://mirrors.tencent.com/npm/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-releases@^2.0.18:
  version "2.0.18"
  resolved "https://mirrors.tencent.com/npm/node-releases/-/node-releases-2.0.18.tgz"
  integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/nopt/-/nopt-5.0.0.tgz"
  integrity sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==
  dependencies:
    abbrev "1"

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://mirrors.tencent.com/npm/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "https://mirrors.tencent.com/npm/normalize-package-data/-/normalize-package-data-3.0.3.tgz"
  integrity sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/normalize-path/-/normalize-path-1.0.0.tgz"
  integrity sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://mirrors.tencent.com/npm/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

normalize-url@^4.1.0:
  version "4.5.1"
  resolved "https://mirrors.tencent.com/npm/normalize-url/-/normalize-url-4.5.1.tgz"
  integrity sha512-9UZCFRHQdNrfTpGg8+1INIg93B6zE0aXMVFkw1WFwvO4SlZywU6aLg5Of0Ap/PgcbSw4LNxvMWXMeugwMCX0AA==

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/npm-run-path/-/npm-run-path-2.0.2.tgz"
  integrity sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://mirrors.tencent.com/npm/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "https://mirrors.tencent.com/npm/npm-run-path/-/npm-run-path-5.3.0.tgz"
  integrity sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==
  dependencies:
    path-key "^4.0.0"

npmlog@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/npmlog/-/npmlog-5.0.1.tgz"
  integrity sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

nwsapi@^2.2.2:
  version "2.2.12"
  resolved "https://mirrors.tencent.com/npm/nwsapi/-/nwsapi-2.2.12.tgz"
  integrity sha512-qXDmcVlZV4XRtKFzddidpfVP4oMSGhga+xdMc25mv8kaLUHtgzCDhUxkrN8exkGdTlLNaXj7CV3GtON7zuGZ+w==

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://mirrors.tencent.com/npm/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.13.1:
  version "1.13.2"
  resolved "https://mirrors.tencent.com/npm/object-inspect/-/object-inspect-1.13.2.tgz"
  integrity sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==

object-is@^1.1.5:
  version "1.1.6"
  resolved "https://mirrors.tencent.com/npm/object-is/-/object-is-1.1.6.tgz"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.0, object.assign@^4.1.2, object.assign@^4.1.4, object.assign@^4.1.5:
  version "4.1.5"
  resolved "https://mirrors.tencent.com/npm/object.assign/-/object.assign-4.1.5.tgz"
  integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.5, object.entries@^1.1.7:
  version "1.1.8"
  resolved "https://mirrors.tencent.com/npm/object.entries/-/object.entries-1.1.8.tgz"
  integrity sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

object.fromentries@^2.0.7:
  version "2.0.8"
  resolved "https://mirrors.tencent.com/npm/object.fromentries/-/object.fromentries-2.0.8.tgz"
  integrity sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.1:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/object.groupby/-/object.groupby-1.0.3.tgz"
  integrity sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.hasown@^1.1.3:
  version "1.1.4"
  resolved "https://mirrors.tencent.com/npm/object.hasown/-/object.hasown-1.1.4.tgz"
  integrity sha512-FZ9LZt9/RHzGySlBARE3VF+gE26TxR38SdmqOqliuTnl9wrKulaQs+4dee1V+Io8VfxqzAfHu6YuRgUy8OHoTg==
  dependencies:
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.values@^1.1.6, object.values@^1.1.7:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/object.values/-/object.values-1.2.0.tgz"
  integrity sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

oblivious-set@1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/oblivious-set/-/oblivious-set-1.0.0.tgz"
  integrity sha512-z+pI07qxo4c2CulUHCDf9lcqDlMSo72N/4rLUpRXf6fu+q8vjt8y0xS+Tlf8NTJDdTXHbdeO1n3MlbctwEoXZw==

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://mirrors.tencent.com/npm/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/onetime/-/onetime-6.0.0.tgz"
  integrity sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==
  dependencies:
    mimic-fn "^4.0.0"

onetime@^7.0.0:
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/onetime/-/onetime-7.0.0.tgz#9f16c92d8c9ef5120e3acd9dd9957cceecc1ab60"
  integrity sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==
  dependencies:
    mimic-function "^5.0.0"

open@^7.4.2:
  version "7.4.2"
  resolved "https://mirrors.tencent.com/npm/open/-/open-7.4.2.tgz"
  integrity sha512-MVHddDVweXZF3awtlAS+6pgKLlm/JgxZ90+/NBurBoQctVOOB/zDdVjcyPzQ+0laDGbsWgrRkflI65sQeOgT9Q==
  dependencies:
    is-docker "^2.0.0"
    is-wsl "^2.1.1"

open@^8.4.0:
  version "8.4.2"
  resolved "https://mirrors.tencent.com/npm/open/-/open-8.4.2.tgz"
  integrity sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

opfs-tools@^0.6.1:
  version "0.6.2"
  resolved "https://mirrors.tencent.com/npm/opfs-tools/-/opfs-tools-0.6.2.tgz#66d1ac4825e0e9996874269e2b48ef3b6fac342b"
  integrity sha512-C2+ElLE6WL9jBqhNPM5vN8QW88adNrFo13lMqyqelUQGSdH1R5MSNa8n6CnqmYWoOyGUqkmCZ5jxNl0n+/xHLg==

optionator@^0.9.3:
  version "0.9.3"
  resolved "https://mirrors.tencent.com/npm/optionator/-/optionator-0.9.3.tgz"
  integrity sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==

p-cancelable@^1.0.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/p-cancelable/-/p-cancelable-1.1.0.tgz"
  integrity sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/p-finally/-/p-finally-1.0.0.tgz"
  integrity sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

package-json@^6.3.0:
  version "6.5.0"
  resolved "https://mirrors.tencent.com/npm/package-json/-/package-json-6.5.0.tgz"
  integrity sha512-k3bdm2n25tkyxcjSKzB5x8kfVxlMdgsbPr0GkZcwHsLpba6cBjqCt1KlcChKEvxHIcTB1FVMuwoijZ26xex5MQ==
  dependencies:
    got "^9.6.0"
    registry-auth-token "^4.0.0"
    registry-url "^5.0.0"
    semver "^6.2.0"

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/param-case/-/param-case-3.0.4.tgz"
  integrity sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parchment@^1.1.2, parchment@^1.1.4:
  version "1.1.4"
  resolved "https://mirrors.tencent.com/npm/parchment/-/parchment-1.1.4.tgz"
  integrity sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://mirrors.tencent.com/npm/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-node-version@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/parse-node-version/-/parse-node-version-1.0.1.tgz"
  integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==

parse5@^7.1.1:
  version "7.1.2"
  resolved "https://mirrors.tencent.com/npm/parse5/-/parse5-7.1.2.tgz"
  integrity sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==
  dependencies:
    entities "^4.4.0"

pascal-case@^3.1.1, pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://mirrors.tencent.com/npm/pascal-case/-/pascal-case-3.1.2.tgz"
  integrity sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

patch-package@^6.4.7:
  version "6.5.1"
  resolved "https://mirrors.tencent.com/npm/patch-package/-/patch-package-6.5.1.tgz"
  integrity sha512-I/4Zsalfhc6bphmJTlrLoOcAF87jcxko4q0qsv4bGcurbr8IskEOtdnt9iCmsQVGL1B+iUhSQqweyTLJfCF9rA==
  dependencies:
    "@yarnpkg/lockfile" "^1.1.0"
    chalk "^4.1.2"
    cross-spawn "^6.0.5"
    find-yarn-workspace-root "^2.0.0"
    fs-extra "^9.0.0"
    is-ci "^2.0.0"
    klaw-sync "^6.0.0"
    minimist "^1.2.6"
    open "^7.4.2"
    rimraf "^2.6.3"
    semver "^5.6.0"
    slash "^2.0.0"
    tmp "^0.0.33"
    yaml "^1.10.2"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/path-browserify/-/path-browserify-1.0.1.tgz#d98454a9c3753d5790860f16f68867b9e46be1fd"
  integrity sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==

path-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/path-case/-/path-case-3.0.4.tgz"
  integrity sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/path-key/-/path-key-2.0.1.tgz"
  integrity sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-key@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/path-key/-/path-key-4.0.0.tgz"
  integrity sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.10.2:
  version "1.10.2"
  resolved "https://mirrors.tencent.com/npm/path-scurry/-/path-scurry-1.10.2.tgz"
  integrity sha512-7xTavNy5RQXnsjANvVvMkEjvloOinkAjv/Z6Ildz9v2RinZ4SBKTWFOVRbaF8p0vpHnyjV/UwNDdKuUv6M5qcA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==

picocolors@^1.0.0, picocolors@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/picocolors/-/picocolors-1.0.1.tgz"
  integrity sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/picocolors/-/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pidtree@0.6.0:
  version "0.6.0"
  resolved "https://mirrors.tencent.com/npm/pidtree/-/pidtree-0.6.0.tgz"
  integrity sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pify@^4.0.1:
  version "4.0.1"
  resolved "https://mirrors.tencent.com/npm/pify/-/pify-4.0.1.tgz"
  integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==

pirates@^4.0.1:
  version "4.0.6"
  resolved "https://mirrors.tencent.com/npm/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://mirrors.tencent.com/npm/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

pngjs@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/pngjs/-/pngjs-5.0.0.tgz"
  integrity sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==

popper.js@^1.14.4, popper.js@^1.16.1-lts:
  version "1.16.1"
  resolved "https://mirrors.tencent.com/npm/popper.js/-/popper.js-1.16.1.tgz"
  integrity sha512-Wb4p1J4zyFTbM+u6WuO4XstYx4Ky9Cewe4DWrel7B0w6VVICvPwdOpotjzcf6eD8TsckVnIMNONQyPIUFOUbCQ==

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz"
  integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://mirrors.tencent.com/npm/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://mirrors.tencent.com/npm/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.1:
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.0.1:
  version "6.0.1"
  resolved "https://mirrors.tencent.com/npm/postcss-nested/-/postcss-nested-6.0.1.tgz"
  integrity sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==
  dependencies:
    postcss-selector-parser "^6.0.11"

postcss-selector-parser@^6.0.11:
  version "6.0.16"
  resolved "https://mirrors.tencent.com/npm/postcss-selector-parser/-/postcss-selector-parser-6.0.16.tgz"
  integrity sha512-A0RVJrX+IUkVZbW3ClroRWurercFhieevHB38sr2+l9eUClMqome3LmEmnhlNy+5Mr2EYN6B2Kaw9wYdd+VHiw==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://mirrors.tencent.com/npm/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.4.23, postcss@^8.4.38:
  version "8.4.38"
  resolved "https://mirrors.tencent.com/npm/postcss/-/postcss-8.4.38.tgz"
  integrity sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.0"
    source-map-js "^1.2.0"

postcss@^8.4.43:
  version "8.5.3"
  resolved "https://mirrors.tencent.com/npm/postcss/-/postcss-8.5.3.tgz#1463b6f1c7fb16fe258736cba29a2de35237eafb"
  integrity sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

postinstall-postinstall@^2.1.0:
  version "2.1.0"
  resolved "https://mirrors.tencent.com/npm/postinstall-postinstall/-/postinstall-postinstall-2.1.0.tgz"
  integrity sha512-7hQX6ZlZXIoRiWNrbMQaLzUUfH+sSx39u8EJ9HYuDc1kLo9IXKWjM5RSquZN1ad5GnH8CGFM78fsAAQi3OKEEQ==

prefix-style@2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/prefix-style/-/prefix-style-2.0.1.tgz"
  integrity sha512-gdr1MBNVT0drzTq95CbSNdsrBDoHGlb2aDJP/FoY+1e+jSDPOb1Cv554gH2MGiSr2WTcXi/zu+NaFzfcHQkfBQ==

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prepend-http@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/prepend-http/-/prepend-http-2.0.0.tgz"
  integrity sha512-ravE6m9Atw9Z/jjttRUZ+clIXogdghyZAuWJ3qEzjT+jI/dL1ifAqhZeC5VHzQp1MSt1+jxKkFNemj/iO7tVUA==

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.5.0, prettier@^2.8.7:
  version "2.8.8"
  resolved "https://mirrors.tencent.com/npm/prettier/-/prettier-2.8.8.tgz"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

prettier@^3.0.3:
  version "3.3.3"
  resolved "https://mirrors.tencent.com/npm/prettier/-/prettier-3.3.3.tgz#30c54fe0be0d8d12e6ae61dbb10109ea00d53105"
  integrity sha512-i2tDNA0O5IrMO757lfrdQZCc2jPNDVntV0m/+4whiDfWaTKfMNgR7Qz0NAeGz/nRqF4m5/6CLzbP4/liHt12Ew==

process@^0.11.2:
  version "0.11.10"
  resolved "https://mirrors.tencent.com/npm/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==

promise-timeout@^1.3.0:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/promise-timeout/-/promise-timeout-1.3.0.tgz"
  integrity sha512-5yANTE0tmi5++POym6OgtFmwfDvOXABD9oj/jLQr5GPEyuNEb7jH4wbbANJceJid49jwhi1RddxnhnEAb/doqg==

promise@^8.0.1:
  version "8.3.0"
  resolved "https://mirrors.tencent.com/npm/promise/-/promise-8.3.0.tgz"
  integrity sha512-rZPNPKTOYVNEEKFaq1HqTgOwZD+4/YHS5ukLzQCypkj+OkYx7iv0mA91lJlpPPZ8vMau3IIGj5Qlwrx+8iiSmg==
  dependencies:
    asap "~2.0.6"

prop-types@15.x, prop-types@^15.5.10, prop-types@^15.5.7, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://mirrors.tencent.com/npm/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

protobufjs@^7.3.2:
  version "7.4.0"
  resolved "https://mirrors.tencent.com/npm/protobufjs/-/protobufjs-7.4.0.tgz#7efe324ce9b3b61c82aae5de810d287bc08a248a"
  integrity sha512-mRUWCc3KUU4w1jU8sGxICXH/gNS94DvI1gxqDvBzhj1JpcsimQkYiOJfwsPUykUI5ZaspFbSgmBLER8IrQ3tqw==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

prr@~1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/prr/-/prr-1.0.1.tgz"
  integrity sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/pseudomap/-/pseudomap-1.0.2.tgz"
  integrity sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==

psl@^1.1.33:
  version "1.9.0"
  resolved "https://mirrors.tencent.com/npm/psl/-/psl-1.9.0.tgz"
  integrity sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==

pump@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/pump/-/pump-3.0.0.tgz"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

pupa@^2.1.1:
  version "2.1.1"
  resolved "https://mirrors.tencent.com/npm/pupa/-/pupa-2.1.1.tgz"
  integrity sha512-l1jNAspIBSFqbT+y+5FosojNpVpF94nlI+wDUpqP9enwOTfHx9f0gh5nB96vl+6yTpsJsypeNrwfzPrKuHB41A==
  dependencies:
    escape-goat "^2.0.0"

qrcode@^1.5.3, qrcode@^1.5.4:
  version "1.5.4"
  resolved "https://mirrors.tencent.com/npm/qrcode/-/qrcode-1.5.4.tgz#5cb81d86eb57c675febb08cf007fff963405da88"
  integrity sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg==
  dependencies:
    dijkstrajs "^1.0.1"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@^6.9.4:
  version "6.12.1"
  resolved "https://mirrors.tencent.com/npm/qs/-/qs-6.12.1.tgz"
  integrity sha512-zWmv4RSuB9r2mYQw3zxQuHWeU+42aKi1wWig/j4ele4ygELZ7PEO6MM7rim9oAQH2A5MWfsAVf/jPvTPgCbvUQ==
  dependencies:
    side-channel "^1.0.6"

query-string@^6.13.6, query-string@^6.13.7:
  version "6.14.1"
  resolved "https://mirrors.tencent.com/npm/query-string/-/query-string-6.14.1.tgz#7ac2dca46da7f309449ba0f86b1fd28255b0c86a"
  integrity sha512-XDxAeVmpfu1/6IjyT/gXHOl+S0vQ9owggJ30hhWKdHAsNPOcasn5o9BW0eejZqL2e4vMjhAxoW3jVHcD6mbcYw==
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/querystringify/-/querystringify-2.2.0.tgz"
  integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://mirrors.tencent.com/npm/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "https://mirrors.tencent.com/npm/quick-lru/-/quick-lru-4.0.1.tgz"
  integrity sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "https://mirrors.tencent.com/npm/quill-delta/-/quill-delta-3.6.3.tgz"
  integrity sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill@^1.3.7:
  version "1.3.7"
  resolved "https://mirrors.tencent.com/npm/quill/-/quill-1.3.7.tgz"
  integrity sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

raf-schd@^4.0.2:
  version "4.0.3"
  resolved "https://mirrors.tencent.com/npm/raf-schd/-/raf-schd-4.0.3.tgz"
  integrity sha512-tQkJl2GRWh83ui2DiPTJz9wEiMN20syf+5oKfB03yYP7ioZcJwsIK8FjrtLwH1m7C7e+Tt2yYBlrOpdT+dyeIQ==

raf@^3.1.0, raf@~3.4.1:
  version "3.4.1"
  resolved "https://mirrors.tencent.com/npm/raf/-/raf-3.4.1.tgz"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

ramda@^0.27.1:
  version "0.27.2"
  resolved "https://mirrors.tencent.com/npm/ramda/-/ramda-0.27.2.tgz"
  integrity sha512-SbiLPU40JuJniHexQSAgad32hfwd+DRUdwF2PlVuI5RZD0/vahUco7R8vD86J/tcEKKF9vZrUVwgtmGCqlCKyA==

rc-align@^4.0.0:
  version "4.0.15"
  resolved "https://mirrors.tencent.com/npm/rc-align/-/rc-align-4.0.15.tgz"
  integrity sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    dom-align "^1.7.0"
    rc-util "^5.26.0"
    resize-observer-polyfill "^1.5.1"

rc-cascader@~3.6.0:
  version "3.6.2"
  resolved "https://mirrors.tencent.com/npm/rc-cascader/-/rc-cascader-3.6.2.tgz"
  integrity sha512-sf2otpazlROTzkD3nZVfIzXmfBLiEOBTXA5wxozGXBpS902McDpvF0bdcYBu5hN+rviEAm6Mh9cLXNQ1Ty8wKQ==
  dependencies:
    "@babel/runtime" "^7.12.5"
    array-tree-filter "^2.1.0"
    classnames "^2.3.1"
    rc-select "~14.1.0"
    rc-tree "~5.6.3"
    rc-util "^5.6.1"

rc-checkbox@~2.3.0:
  version "2.3.2"
  resolved "https://mirrors.tencent.com/npm/rc-checkbox/-/rc-checkbox-2.3.2.tgz"
  integrity sha512-afVi1FYiGv1U0JlpNH/UaEXdh6WUJjcWokj/nUN2TgG80bfG+MDdbfHKlLcNNba94mbjy2/SXJ1HDgrOkXGAjg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-collapse@~3.3.0:
  version "3.3.1"
  resolved "https://mirrors.tencent.com/npm/rc-collapse/-/rc-collapse-3.3.1.tgz"
  integrity sha512-cOJfcSe3R8vocrF8T+PgaHDrgeA1tX+lwfhwSj60NX9QVRidsILIbRNDLD6nAzmcvVC5PWiIRiR4S1OobxdhCg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.2.1"
    shallowequal "^1.1.0"

rc-dialog@~8.9.0:
  version "8.9.0"
  resolved "https://mirrors.tencent.com/npm/rc-dialog/-/rc-dialog-8.9.0.tgz"
  integrity sha512-Cp0tbJnrvPchJfnwIvOMWmJ4yjX3HWFatO6oBFD1jx8QkgsQCR0p8nUWAKdd3seLJhEC39/v56kZaEjwp9muoQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.21.0"

rc-dock@^3.3.0:
  version "3.3.0"
  resolved "https://mirrors.tencent.com/npm/rc-dock/-/rc-dock-3.3.0.tgz"
  integrity sha512-9rQAzHSLAdQz1ZpPqQGkZKlAt4YI6gNYjuqqpY6hIWBFDhVPccs0jYr7L7PP3OpmliZ/R60LgHfLFbrL9l+Tlg==
  dependencies:
    classnames "^2.5.1"
    lodash "^4.17.21"
    rc-dropdown "~4.0.1"
    rc-menu "~9.8.4"
    rc-new-window "^0.1.13"
    rc-tabs "~11.16.1"

rc-drawer@~5.1.0:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/rc-drawer/-/rc-drawer-5.1.0.tgz"
  integrity sha512-pU3Tsn99pxGdYowXehzZbdDVE+4lDXSGb7p8vA9mSmr569oc2Izh4Zw5vLKSe/Xxn2p5MSNbLVqD4tz+pK6SOw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-motion "^2.6.1"
    rc-util "^5.21.2"

rc-dropdown@~4.0.0, rc-dropdown@~4.0.1:
  version "4.0.1"
  resolved "https://mirrors.tencent.com/npm/rc-dropdown/-/rc-dropdown-4.0.1.tgz"
  integrity sha512-OdpXuOcme1rm45cR0Jzgfl1otzmU4vuBVb+etXM8vcaULGokAKVpKlw8p6xzspG7jGd/XxShvq+N3VNEfk/l5g==
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.6"
    rc-trigger "^5.3.1"
    rc-util "^5.17.0"

rc-field-form@~1.27.0:
  version "1.27.4"
  resolved "https://mirrors.tencent.com/npm/rc-field-form/-/rc-field-form-1.27.4.tgz"
  integrity sha512-PQColQnZimGKArnOh8V2907+VzDCXcqtFvHgevDLtqWc/P7YASb/FqntSmdS8q3VND5SHX3Y1vgMIzY22/f/0Q==
  dependencies:
    "@babel/runtime" "^7.18.0"
    async-validator "^4.1.0"
    rc-util "^5.8.0"

rc-image@~5.7.0:
  version "5.7.1"
  resolved "https://mirrors.tencent.com/npm/rc-image/-/rc-image-5.7.1.tgz"
  integrity sha512-QyMfdhoUfb5W14plqXSisaYwpdstcLYnB0MjX5ccIK2rydQM9sDPuekQWu500DDGR2dBaIF5vx9XbWkNFK17Fg==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    rc-dialog "~8.9.0"
    rc-util "^5.0.6"

rc-input-number@~7.3.5:
  version "7.3.11"
  resolved "https://mirrors.tencent.com/npm/rc-input-number/-/rc-input-number-7.3.11.tgz"
  integrity sha512-aMWPEjFeles6PQnMqP5eWpxzsvHm9rh1jQOWXExUEIxhX62Fyl/ptifLHOn17+waDG1T/YUb6flfJbvwRhHrbA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.23.0"

rc-input@~0.0.1-alpha.5:
  version "0.0.1-alpha.7"
  resolved "https://mirrors.tencent.com/npm/rc-input/-/rc-input-0.0.1-alpha.7.tgz"
  integrity sha512-eozaqpCYWSY5LBMwlHgC01GArkVEP+XlJ84OMvdkwUnJBSv83Yxa15pZpn7vACAj84uDC4xOA2CoFdbLuqB08Q==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.18.1"

rc-mentions@~1.9.1:
  version "1.9.2"
  resolved "https://mirrors.tencent.com/npm/rc-mentions/-/rc-mentions-1.9.2.tgz"
  integrity sha512-uxb/lzNnEGmvraKWNGE6KXMVXvt8RQv9XW8R0Dqi3hYsyPiAZeHRCHQKdLARuk5YBhFhZ6ga55D/8XuY367g3g==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-menu "~9.6.0"
    rc-textarea "^0.3.0"
    rc-trigger "^5.0.4"
    rc-util "^5.22.5"

rc-menu@~9.6.0, rc-menu@~9.6.3:
  version "9.6.4"
  resolved "https://mirrors.tencent.com/npm/rc-menu/-/rc-menu-9.6.4.tgz"
  integrity sha512-6DiNAjxjVIPLZXHffXxxcyE15d4isRL7iQ1ru4MqYDH2Cqc5bW96wZOdMydFtGLyDdnmEQ9jVvdCE9yliGvzkw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.2.0"
    rc-trigger "^5.1.2"
    rc-util "^5.12.0"
    shallowequal "^1.1.0"

rc-menu@~9.8.4:
  version "9.8.4"
  resolved "https://mirrors.tencent.com/npm/rc-menu/-/rc-menu-9.8.4.tgz"
  integrity sha512-lmw2j8I2fhdIzHmC9ajfImfckt0WDb2KVJJBBRIsxPEw2kGkEfjLMUoB1NgiNT/Q5cC8PdjGOGQjHJIJMwyNMw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.2.8"
    rc-trigger "^5.1.2"
    rc-util "^5.27.0"

rc-motion@2.4.6:
  version "2.4.6"
  resolved "https://mirrors.tencent.com/npm/rc-motion/-/rc-motion-2.4.6.tgz#b9e07391927fb7bc14e62dcd839146958db6b2ef"
  integrity sha512-nXIHve2EDQZ8BFHfgJI3HYMMOZ7HGsolCfA9ozP99/gc1UqpgKys1TYrQWdXa2trff0V3JLhgn2zz+w9VsyktA==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.19.2"

rc-motion@^2.0.0, rc-motion@^2.0.1, rc-motion@^2.2.0, rc-motion@^2.3.0, rc-motion@^2.3.4, rc-motion@^2.4.3, rc-motion@^2.4.4, rc-motion@^2.6.1:
  version "2.9.2"
  resolved "https://mirrors.tencent.com/npm/rc-motion/-/rc-motion-2.9.2.tgz"
  integrity sha512-fUAhHKLDdkAXIDLH0GYwof3raS58dtNUmzLF2MeiR8o6n4thNpSDQhOqQzWE4WfFZDCi9VEN8n7tiB7czREcyw==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.43.0"

rc-new-window@^0.1.13:
  version "0.1.13"
  resolved "https://mirrors.tencent.com/npm/rc-new-window/-/rc-new-window-0.1.13.tgz"
  integrity sha512-KqANLQVfgNcfs+R4ntpzV5ELyqXMlAUimdSfFHapk2VwsoZX3y+BK2RjFBFb7q865yqAshP87g0PbIPqblKVTg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    bowser "^2.11.0"
    classnames "2.x"
    lodash "^4.17.20"

rc-notification@~4.6.0:
  version "4.6.1"
  resolved "https://mirrors.tencent.com/npm/rc-notification/-/rc-notification-4.6.1.tgz"
  integrity sha512-NSmFYwrrdY3+un1GvDAJQw62Xi9LNMSsoQyo95tuaYrcad5Bn9gJUL8AREufRxSQAQnr64u3LtP3EUyLYT6bhw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.2.0"
    rc-util "^5.20.1"

rc-overflow@^1.0.0, rc-overflow@^1.2.0, rc-overflow@^1.2.8:
  version "1.3.2"
  resolved "https://mirrors.tencent.com/npm/rc-overflow/-/rc-overflow-1.3.2.tgz"
  integrity sha512-nsUm78jkYAoPygDAcGZeC2VwIg/IBGSodtOY3pMof4W3M9qRJgqaDYm03ZayHlde3I6ipliAxbN0RUcGf5KOzw==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.37.0"

rc-pagination@~3.1.17:
  version "3.1.17"
  resolved "https://mirrors.tencent.com/npm/rc-pagination/-/rc-pagination-3.1.17.tgz"
  integrity sha512-/BQ5UxcBnW28vFAcP2hfh+Xg15W0QZn8TWYwdCApchMH1H0CxiaUUcULP8uXcFM1TygcdKWdt3JqsL9cTAfdkQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-picker@~2.6.10:
  version "2.6.11"
  resolved "https://mirrors.tencent.com/npm/rc-picker/-/rc-picker-2.6.11.tgz"
  integrity sha512-INJ7ULu+Kj4UgqbcqE8Q+QpMw55xFf9kkyLBHJFk0ihjJpAV4glialRfqHE7k4KX2BWYPQfpILwhwR14x2EiRQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    date-fns "2.x"
    dayjs "1.x"
    moment "^2.24.0"
    rc-trigger "^5.0.4"
    rc-util "^5.4.0"
    shallowequal "^1.1.0"

rc-progress@~3.3.2:
  version "3.3.3"
  resolved "https://mirrors.tencent.com/npm/rc-progress/-/rc-progress-3.3.3.tgz"
  integrity sha512-MDVNVHzGanYtRy2KKraEaWeZLri2ZHWIRyaE1a9MQ2MuJ09m+Wxj5cfcaoaR6z5iRpHpA59YeUxAlpML8N4PJw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-rate@~2.9.0:
  version "2.9.3"
  resolved "https://mirrors.tencent.com/npm/rc-rate/-/rc-rate-2.9.3.tgz"
  integrity sha512-2THssUSnRhtqIouQIIXqsZGzRczvp4WsH4WvGuhiwm+LG2fVpDUJliP9O1zeDOZvYfBE/Bup4SgHun/eCkbjgQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-resize-observer@^1.0.0, rc-resize-observer@^1.1.0, rc-resize-observer@^1.2.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/rc-resize-observer/-/rc-resize-observer-1.4.0.tgz"
  integrity sha512-PnMVyRid9JLxFavTjeDXEXo65HCRqbmLBw9xX9gfC4BZiSzbLXKzW3jPz+J0P71pLbD5tBMTT+mkstV5gD0c9Q==
  dependencies:
    "@babel/runtime" "^7.20.7"
    classnames "^2.2.1"
    rc-util "^5.38.0"
    resize-observer-polyfill "^1.5.1"

rc-segmented@~2.1.0:
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/rc-segmented/-/rc-segmented-2.1.2.tgz"
  integrity sha512-qGo1bCr83ESXpXVOCXjFe1QJlCAQXyi9KCiy8eX3rIMYlTeJr/ftySIaTnYsitL18SvWf5ZEHsfqIWoX0EMfFQ==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-motion "^2.4.4"
    rc-util "^5.17.0"

rc-select@~14.1.0, rc-select@~14.1.1:
  version "14.1.18"
  resolved "https://mirrors.tencent.com/npm/rc-select/-/rc-select-14.1.18.tgz"
  integrity sha512-4JgY3oG2Yz68ECMUSCON7mtxuJvCSj+LJpHEg/AONaaVBxIIrmI/ZTuMJkyojall/X50YdBe5oMKqHHPNiPzEg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.0.0"
    rc-trigger "^5.0.4"
    rc-util "^5.16.1"
    rc-virtual-list "^3.2.0"

rc-slider@~10.0.0:
  version "10.0.1"
  resolved "https://mirrors.tencent.com/npm/rc-slider/-/rc-slider-10.0.1.tgz"
  integrity sha512-igTKF3zBet7oS/3yNiIlmU8KnZ45npmrmHlUUio8PNbIhzMcsh+oE/r2UD42Y6YD2D/s+kzCQkzQrPD6RY435Q==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.18.1"
    shallowequal "^1.1.0"

rc-steps@~4.1.0:
  version "4.1.4"
  resolved "https://mirrors.tencent.com/npm/rc-steps/-/rc-steps-4.1.4.tgz"
  integrity sha512-qoCqKZWSpkh/b03ASGx1WhpKnuZcRWmvuW+ZUu4mvMdfvFzVxblTwUM+9aBd0mlEUFmt6GW8FXhMpHkK3Uzp3w==
  dependencies:
    "@babel/runtime" "^7.10.2"
    classnames "^2.2.3"
    rc-util "^5.0.1"

rc-switch@~3.2.0:
  version "3.2.2"
  resolved "https://mirrors.tencent.com/npm/rc-switch/-/rc-switch-3.2.2.tgz"
  integrity sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-util "^5.0.1"

rc-table@~7.25.3:
  version "7.25.3"
  resolved "https://mirrors.tencent.com/npm/rc-table/-/rc-table-7.25.3.tgz"
  integrity sha512-McsLJ2rg8EEpRBRYN4Pf9gT7ZNYnjvF9zrBpUBBbUX/fxk+eGi5ff1iPIhMyiHsH71/BmTUzX9nc9XqupD0nMg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.22.5"
    shallowequal "^1.1.0"

rc-tabs@~11.16.0, rc-tabs@~11.16.1:
  version "11.16.1"
  resolved "https://mirrors.tencent.com/npm/rc-tabs/-/rc-tabs-11.16.1.tgz"
  integrity sha512-bR7Dap23YyfzZQwtKomhiFEFzZuE7WaKWo+ypNRSGB9PDKSc6tM12VP8LWYkvmmQHthgwP0WRN8nFbSJWuqLYw==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "~4.0.0"
    rc-menu "~9.6.0"
    rc-resize-observer "^1.0.0"
    rc-util "^5.5.0"

rc-textarea@^0.3.0, rc-textarea@~0.3.0:
  version "0.3.7"
  resolved "https://mirrors.tencent.com/npm/rc-textarea/-/rc-textarea-0.3.7.tgz"
  integrity sha512-yCdZ6binKmAQB13hc/oehh0E/QRwoPP1pjF21aHBxlgXO3RzPF6dUu4LG2R4FZ1zx/fQd2L1faktulrXOM/2rw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.7.0"
    shallowequal "^1.1.0"

rc-tooltip@~5.2.0:
  version "5.2.2"
  resolved "https://mirrors.tencent.com/npm/rc-tooltip/-/rc-tooltip-5.2.2.tgz"
  integrity sha512-jtQzU/18S6EI3lhSGoDYhPqNpWajMtS5VV/ld1LwyfrDByQpYmw/LW6U7oFXXLukjfDHQ7Ju705A82PRNFWYhg==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "^2.3.1"
    rc-trigger "^5.0.0"

rc-tree-select@~5.4.0:
  version "5.4.1"
  resolved "https://mirrors.tencent.com/npm/rc-tree-select/-/rc-tree-select-5.4.1.tgz"
  integrity sha512-xhXnKP8Stu2Q7wTcjJaSzSOLd4wmFtUZOwmy1cioaWyPbpiKlYdnALXA/9U49HOaV3KFXdRHE9Yi0KYED7yOAQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-select "~14.1.0"
    rc-tree "~5.6.1"
    rc-util "^5.16.1"

rc-tree@~5.6.1, rc-tree@~5.6.3, rc-tree@~5.6.5:
  version "5.6.9"
  resolved "https://mirrors.tencent.com/npm/rc-tree/-/rc-tree-5.6.9.tgz"
  integrity sha512-si8aGuWQ2/sh2Ibk+WdUdDeAxoviT/+kDY+NLtJ+RhqfySqPFqWM5uHTwgFRrWUvKCqEeE/PjCYuuhHrK7Y7+A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.4.8"

rc-trigger@^5.0.0, rc-trigger@^5.0.4, rc-trigger@^5.1.2, rc-trigger@^5.2.10, rc-trigger@^5.3.1:
  version "5.3.4"
  resolved "https://mirrors.tencent.com/npm/rc-trigger/-/rc-trigger-5.3.4.tgz"
  integrity sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw==
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.6"
    rc-align "^4.0.0"
    rc-motion "^2.0.0"
    rc-util "^5.19.2"

rc-upload@~4.3.0:
  version "4.3.6"
  resolved "https://mirrors.tencent.com/npm/rc-upload/-/rc-upload-4.3.6.tgz"
  integrity sha512-Bt7ESeG5tT3IY82fZcP+s0tQU2xmo1W6P3S8NboUUliquJLQYLkUcsaExi3IlBVr43GQMCjo30RA2o0i70+NjA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-util@^5.0.1, rc-util@^5.0.6, rc-util@^5.12.0, rc-util@^5.16.1, rc-util@^5.17.0, rc-util@^5.18.1, rc-util@^5.19.2, rc-util@^5.2.0, rc-util@^5.2.1, rc-util@^5.20.1, rc-util@^5.21.0, rc-util@^5.21.2, rc-util@^5.22.5, rc-util@^5.23.0, rc-util@^5.26.0, rc-util@^5.27.0, rc-util@^5.36.0, rc-util@^5.37.0, rc-util@^5.38.0, rc-util@^5.4.0, rc-util@^5.43.0, rc-util@^5.5.0, rc-util@^5.6.1, rc-util@^5.7.0, rc-util@^5.8.0, rc-util@^5.9.4:
  version "5.43.0"
  resolved "https://mirrors.tencent.com/npm/rc-util/-/rc-util-5.43.0.tgz"
  integrity sha512-AzC7KKOXFqAdIBqdGWepL9Xn7cm3vnAmjlHqUnoQaTMZYhM4VlXGLkkHHxj/BZ7Td0+SOPKB4RGPboBVKT9htw==
  dependencies:
    "@babel/runtime" "^7.18.3"
    react-is "^18.2.0"

rc-virtual-list@^3.2.0, rc-virtual-list@^3.4.8:
  version "3.14.5"
  resolved "https://mirrors.tencent.com/npm/rc-virtual-list/-/rc-virtual-list-3.14.5.tgz"
  integrity sha512-ZMOnkCLv2wUN8Jz7yI4XiSLa9THlYvf00LuMhb1JlsQCewuU7ydPuHw1rGVPhe9VZYl/5UqODtNd7QKJ2DMGfg==
  dependencies:
    "@babel/runtime" "^7.20.0"
    classnames "^2.2.6"
    rc-resize-observer "^1.0.0"
    rc-util "^5.36.0"

rc@1.2.8, rc@^1.2.8:
  version "1.2.8"
  resolved "https://mirrors.tencent.com/npm/rc/-/rc-1.2.8.tgz"
  integrity sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-animate-height@^2.0.23:
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/react-animate-height/-/react-animate-height-2.1.2.tgz"
  integrity sha512-A9jfz/4CTdsIsE7WCQtO9UkOpMBcBRh8LxyHl2eoZz1ki02jpyUL5xt58gabd0CyeLQ8fRyQ+s2lyV2Ufu8Owg==
  dependencies:
    classnames "^2.2.5"
    prop-types "^15.6.1"

react-beautiful-dnd@12.0.0:
  version "12.0.0"
  resolved "https://mirrors.tencent.com/npm/react-beautiful-dnd/-/react-beautiful-dnd-12.0.0.tgz"
  integrity sha512-NdttVdqLL1y8vKNx7VlcBCsC4c/8R/H4NIYo5rqPGU1HF48oXHrTpSDWw1HPMTBifVty4OKOkSIwo1NcvE3bDw==
  dependencies:
    "@babel/runtime-corejs2" "^7.6.3"
    css-box-model "^1.2.0"
    memoize-one "^5.1.1"
    raf-schd "^4.0.2"
    react-redux "^7.1.1"
    redux "^4.0.4"
    use-memo-one "^1.1.1"

react-beautiful-dnd@^13.1.1:
  version "13.1.1"
  resolved "https://mirrors.tencent.com/npm/react-beautiful-dnd/-/react-beautiful-dnd-13.1.1.tgz"
  integrity sha512-0Lvs4tq2VcrEjEgDXHjT98r+63drkKEgqyxdA7qD3mvKwga6a5SscbdLPO2IExotU1jW8L0Ksdl0Cj2AF67nPQ==
  dependencies:
    "@babel/runtime" "^7.9.2"
    css-box-model "^1.2.0"
    memoize-one "^5.1.1"
    raf-schd "^4.0.2"
    react-redux "^7.2.0"
    redux "^4.0.4"
    use-memo-one "^1.1.1"

react-color@^2.19.3:
  version "2.19.3"
  resolved "https://mirrors.tencent.com/npm/react-color/-/react-color-2.19.3.tgz"
  integrity sha512-LEeGE/ZzNLIsFWa1TMe8y5VYqr7bibneWmvJwm1pCn/eNmrabWDh659JSPn9BuaMpEfU83WTOJfnCcjDZwNQTA==
  dependencies:
    "@icons/material" "^0.2.4"
    lodash "^4.17.15"
    lodash-es "^4.17.15"
    material-colors "^1.2.1"
    prop-types "^15.5.10"
    reactcss "^1.2.0"
    tinycolor2 "^1.4.1"

react-copy-to-clipboard@^5.0.2:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/react-copy-to-clipboard/-/react-copy-to-clipboard-5.1.0.tgz"
  integrity sha512-k61RsNgAayIJNoy9yDsYzDe/yAZAzEbEgcz3DZMhF686LEyukcE1hzurxe85JandPUG+yTfGVFzuEw3xt8WP/A==
  dependencies:
    copy-to-clipboard "^3.3.1"
    prop-types "^15.8.1"

react-custom-scrollbars@^4.2.1:
  version "4.2.1"
  resolved "https://mirrors.tencent.com/npm/react-custom-scrollbars/-/react-custom-scrollbars-4.2.1.tgz"
  integrity sha512-VtJTUvZ7kPh/auZWIbBRceGPkE30XBYe+HktFxuMWBR2eVQQ+Ur6yFJMoaYcNpyGq22uYJ9Wx4UAEcC0K+LNPQ==
  dependencies:
    dom-css "^2.0.0"
    prop-types "^15.5.10"
    raf "^3.1.0"

react-dom@^16.13.1:
  version "16.14.0"
  resolved "https://mirrors.tencent.com/npm/react-dom/-/react-dom-16.14.0.tgz"
  integrity sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.19.1"

react-dom@^17.0.2:
  version "17.0.2"
  resolved "https://mirrors.tencent.com/npm/react-dom/-/react-dom-17.0.2.tgz"
  integrity sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    scheduler "^0.20.2"

react-dom@^18.2.0:
  version "18.2.0"
  resolved "https://mirrors.tencent.com/npm/react-dom/-/react-dom-18.2.0.tgz"
  integrity sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.0"

react-dropzone@^11.2.0:
  version "11.7.1"
  resolved "https://mirrors.tencent.com/npm/react-dropzone/-/react-dropzone-11.7.1.tgz"
  integrity sha512-zxCMwhfPy1olUEbw3FLNPLhAm/HnaYH5aELIEglRbqabizKAdHs0h+WuyOpmA+v1JXn0++fpQDdNfUagWt5hJQ==
  dependencies:
    attr-accept "^2.2.2"
    file-selector "^0.4.0"
    prop-types "^15.8.1"

react-error-boundary@^3.1.4:
  version "3.1.4"
  resolved "https://mirrors.tencent.com/npm/react-error-boundary/-/react-error-boundary-3.1.4.tgz"
  integrity sha512-uM9uPzZJTF6wRQORmSrvOIgt4lJ9MC1sNgEOj2XGsDTRE4kmpWxg7ENK9EWNKJRMAOY9z0MuF4yIfl6gp4sotA==
  dependencies:
    "@babel/runtime" "^7.12.5"

react-error-boundary@^4.1.1, react-error-boundary@^4.1.2:
  version "4.1.2"
  resolved "https://mirrors.tencent.com/npm/react-error-boundary/-/react-error-boundary-4.1.2.tgz#bc750ad962edb8b135d6ae922c046051eb58f289"
  integrity sha512-GQDxZ5Jd+Aq/qUxbCm1UtzmL/s++V7zKgE8yMktJiCQXCCFZnMZh9ng+6/Ne6PjNSXH0L9CjeOEREfRnq6Duag==
  dependencies:
    "@babel/runtime" "^7.12.5"

react-fast-compare@^3.0.1, react-fast-compare@^3.2.0, react-fast-compare@^3.2.2:
  version "3.2.2"
  resolved "https://mirrors.tencent.com/npm/react-fast-compare/-/react-fast-compare-3.2.2.tgz"
  integrity sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==

react-helmet-async@^1.0.6:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/react-helmet-async/-/react-helmet-async-1.3.0.tgz"
  integrity sha512-9jZ57/dAn9t3q6hneQS0wukqC2ENOBgMNVEhb/ZG9ZSxUetzVIw4iAmEU38IaVg3QGYauQPhSeUTuIUtFglWpg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    invariant "^2.2.4"
    prop-types "^15.7.2"
    react-fast-compare "^3.2.0"
    shallowequal "^1.1.0"

react-hook-form@^6.15.5:
  version "6.15.8"
  resolved "https://mirrors.tencent.com/npm/react-hook-form/-/react-hook-form-6.15.8.tgz"
  integrity sha512-prq82ofMbnRyj5wqDe8hsTRcdR25jQ+B8KtCS7BLCzjFHAwNuCjRwzPuP4eYLsEBjEIeYd6try+pdLdw0kPkpg==

react-icons@^3.11.0:
  version "3.11.0"
  resolved "https://mirrors.tencent.com/npm/react-icons/-/react-icons-3.11.0.tgz#2ca2903dfab8268ca18ebd8cc2e879921ec3b254"
  integrity sha512-JRgiI/vdF6uyBgyZhVyYJUZAop95Sy4XDe/jmT3R/bKliFWpO/uZBwvSjWEdxwzec7SYbEPNPck0Kff2tUGM2Q==
  dependencies:
    camelcase "^5.0.0"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://mirrors.tencent.com/npm/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^17.0.2:
  version "17.0.2"
  resolved "https://mirrors.tencent.com/npm/react-is/-/react-is-17.0.2.tgz"
  integrity sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==

react-is@^18.2.0:
  version "18.3.1"
  resolved "https://mirrors.tencent.com/npm/react-is/-/react-is-18.3.1.tgz"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

react-onclickoutside@6.10.0:
  version "6.10.0"
  resolved "https://mirrors.tencent.com/npm/react-onclickoutside/-/react-onclickoutside-6.10.0.tgz"
  integrity sha512-7i2L3ef+0ILXpL6P+Hg304eCQswh4jl3ynwR71BSlMU49PE2uk31k8B2GkP6yE9s2D4jTGKnzuSpzWxu4YxfQQ==

react-popper@1.3.11:
  version "1.3.11"
  resolved "https://mirrors.tencent.com/npm/react-popper/-/react-popper-1.3.11.tgz"
  integrity sha512-VSA/bS+pSndSF2fiasHK/PTEEAyOpX60+H5EPAjoArr8JGm+oihu4UbrqcEBpQibJxBVCpYyjAX7abJ+7DoYVg==
  dependencies:
    "@babel/runtime" "^7.1.2"
    "@hypnosphi/create-react-context" "^0.3.1"
    deep-equal "^1.1.1"
    popper.js "^1.14.4"
    prop-types "^15.6.1"
    typed-styles "^0.0.7"
    warning "^4.0.2"

react-popper@~2.3.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/react-popper/-/react-popper-2.3.0.tgz"
  integrity sha512-e1hj8lL3uM+sgSR4Lxzn5h1GxBlpa4CQz0XLF8kx4MDrDRWY0Ena4c97PUeSX9i5W3UAfDP0z0FXCTQkoXUl3Q==
  dependencies:
    react-fast-compare "^3.0.1"
    warning "^4.0.2"

react-qmap@^0.1.9:
  version "0.1.9"
  resolved "https://mirrors.tencent.com/npm/react-qmap/-/react-qmap-0.1.9.tgz"
  integrity sha512-+TAMKnSDzMIQz/xcJYcZo9yVmvFQBNUUIRYRDp+Zr7eD2VzwVNVWIHIdeLXptUb7Ulctj2QOfJl0o6kXmTX8Sw==
  dependencies:
    prop-types "^15.6.1"

react-query@^3.39.1:
  version "3.39.3"
  resolved "https://mirrors.tencent.com/npm/react-query/-/react-query-3.39.3.tgz"
  integrity sha512-nLfLz7GiohKTJDuT4us4X3h/8unOh+00MLb2yJoGTPjxKs2bc1iDhkNx2bd5MKklXnOD3NrVZ+J2UXujA5In4g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    broadcast-channel "^3.4.1"
    match-sorter "^6.0.2"

react-quill@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/react-quill/-/react-quill-2.0.0.tgz"
  integrity sha512-4qQtv1FtCfLgoD3PXAur5RyxuUbPXQGOHgTlFie3jtxp43mXDtzCKaOgQ3mLyZfi1PUlyjycfivKelFhy13QUg==
  dependencies:
    "@types/quill" "^1.3.10"
    lodash "^4.17.4"
    quill "^1.3.7"

react-reconciler@^0.27.0:
  version "0.27.0"
  resolved "https://mirrors.tencent.com/npm/react-reconciler/-/react-reconciler-0.27.0.tgz#360124fdf2d76447c7491ee5f0e04503ed9acf5b"
  integrity sha512-HmMDKciQjYmBRGuuhIaKA1ba/7a+UsM5FzOZsMO2JYHt9Jh8reCb7j1eDC95NOyUlKM9KRyvdx0flBuDvYSBoA==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.21.0"

react-redux@^7.1.1, react-redux@^7.2.0:
  version "7.2.9"
  resolved "https://mirrors.tencent.com/npm/react-redux/-/react-redux-7.2.9.tgz"
  integrity sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    hoist-non-react-statics "^3.3.2"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^17.0.2"

react-refresh@^0.14.0:
  version "0.14.0"
  resolved "https://mirrors.tencent.com/npm/react-refresh/-/react-refresh-0.14.0.tgz"
  integrity sha512-wViHqhAd8OHeLS/IRMJjTSDHF3U9eWi62F/MledQGPdJGDhodXJ9PBLNGr6WWL7qlH12Mt3TyTpbS+hGXMjCzQ==

react-router-dom@^6.22.3, react-router-dom@^6.8.2:
  version "6.25.1"
  resolved "https://mirrors.tencent.com/npm/react-router-dom/-/react-router-dom-6.25.1.tgz"
  integrity sha512-0tUDpbFvk35iv+N89dWNrJp+afLgd+y4VtorJZuOCXK0kkCWjEvb3vTJM++SYvMEpbVwXKf3FjeVveVEb6JpDQ==
  dependencies:
    "@remix-run/router" "1.18.0"
    react-router "6.25.1"

react-router@6.25.1:
  version "6.25.1"
  resolved "https://mirrors.tencent.com/npm/react-router/-/react-router-6.25.1.tgz"
  integrity sha512-u8ELFr5Z6g02nUtpPAggP73Jigj1mRePSwhS/2nkTrlPU5yEkH1vYzWNyvSnSzeeE2DNqWdH+P8OhIh9wuXhTw==
  dependencies:
    "@remix-run/router" "1.18.0"

react-sortable-hoc@1.9.0:
  version "1.9.0"
  resolved "https://mirrors.tencent.com/npm/react-sortable-hoc/-/react-sortable-hoc-1.9.0.tgz#ca09607d6ea717ebda899abad72cb2df9e0c20db"
  integrity sha512-8ziTjeZ5Bg4fCvv/HaX5ULz+wCr7q2j5S41/SXypSfX/k7Z0n61K62m++lZRLG06E5WO5FBRpnd4zbDsF8dPzQ==
  dependencies:
    "@babel/runtime" "^7.2.0"
    invariant "^2.2.4"
    prop-types "^15.5.7"

react-sortable-hoc@^1.11.0:
  version "1.11.0"
  resolved "https://mirrors.tencent.com/npm/react-sortable-hoc/-/react-sortable-hoc-1.11.0.tgz"
  integrity sha512-v1CDCvdfoR3zLGNp6qsBa4J1BWMEVH25+UKxF/RvQRh+mrB+emqtVHMgZ+WreUiKJoEaiwYoScaueIKhMVBHUg==
  dependencies:
    "@babel/runtime" "^7.2.0"
    invariant "^2.2.4"
    prop-types "^15.5.7"

react-sortable-hoc@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/react-sortable-hoc/-/react-sortable-hoc-2.0.0.tgz"
  integrity sha512-JZUw7hBsAHXK7PTyErJyI7SopSBFRcFHDjWW5SWjcugY0i6iH7f+eJkY8cJmGMlZ1C9xz1J3Vjz0plFpavVeRg==
  dependencies:
    "@babel/runtime" "^7.2.0"
    invariant "^2.2.4"
    prop-types "^15.5.7"

react-sortablejs@^6.1.4:
  version "6.1.4"
  resolved "https://mirrors.tencent.com/npm/react-sortablejs/-/react-sortablejs-6.1.4.tgz"
  integrity sha512-fc7cBosfhnbh53Mbm6a45W+F735jwZ1UFIYSrIqcO/gRIFoDyZeMtgKlpV4DdyQfbCzdh5LoALLTDRxhMpTyXQ==
  dependencies:
    classnames "2.3.1"
    tiny-invariant "1.2.0"

react-sticky-box@^0.9.3:
  version "0.9.3"
  resolved "https://mirrors.tencent.com/npm/react-sticky-box/-/react-sticky-box-0.9.3.tgz"
  integrity sha512-Y/qO7vTqAvXuRR6G6ZCW4fX2Bz0GZRwiiLTVeZN5CVz9wzs37ev0Xj3KSKF/PzF0jifwATivI4t24qXG8rSz4Q==
  dependencies:
    "@babel/runtime" "^7.1.5"
    resize-observer-polyfill "^1.5.1"

react-transition-group@^4.4.1, react-transition-group@~4.4.1:
  version "4.4.5"
  resolved "https://mirrors.tencent.com/npm/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react-universal-interface@^0.6.2:
  version "0.6.2"
  resolved "https://mirrors.tencent.com/npm/react-universal-interface/-/react-universal-interface-0.6.2.tgz#5e8d438a01729a4dbbcbeeceb0b86be146fe2b3b"
  integrity sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==

react-use-measure@^2.1.7:
  version "2.1.7"
  resolved "https://mirrors.tencent.com/npm/react-use-measure/-/react-use-measure-2.1.7.tgz#36b8a2e7fd2fa58109ab851b3addcb0aad66ad1d"
  integrity sha512-KrvcAo13I/60HpwGO5jpW7E9DfusKyLPLvuHlUyP5zqnmAPhNc6qTRjUQrdTADl0lpPpDVU2/Gg51UlOGHXbdg==

react-use@^15.3.4:
  version "15.3.8"
  resolved "https://mirrors.tencent.com/npm/react-use/-/react-use-15.3.8.tgz#ca839ac7fb3d696e5ccbeabbc8dadc2698969d30"
  integrity sha512-GeGcrmGuUvZrY5wER3Lnph9DSYhZt5nEjped4eKDq8BRGr2CnLf9bDQWG9RFc7oCPphnscUUdOovzq0E5F2c6Q==
  dependencies:
    "@types/js-cookie" "2.2.6"
    "@xobotyi/scrollbar-width" "1.9.5"
    copy-to-clipboard "^3.2.0"
    fast-deep-equal "^3.1.3"
    fast-shallow-equal "^1.0.0"
    js-cookie "^2.2.1"
    nano-css "^5.2.1"
    react-universal-interface "^0.6.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    set-harmonic-interval "^1.0.1"
    throttle-debounce "^2.1.0"
    ts-easing "^0.2.0"
    tslib "^2.0.0"

react-window@^1.8.5:
  version "1.8.10"
  resolved "https://mirrors.tencent.com/npm/react-window/-/react-window-1.8.10.tgz"
  integrity sha512-Y0Cx+dnU6NLa5/EvoHukUD0BklJ8qITCtVEPY1C/nL8wwoZ0b5aEw8Ff1dOVHw7fCzMt55XfJDd8S8W8LCaUCg==
  dependencies:
    "@babel/runtime" "^7.0.0"
    memoize-one ">=3.1.1 <6"

react@^16.13.1:
  version "16.14.0"
  resolved "https://mirrors.tencent.com/npm/react/-/react-16.14.0.tgz"
  integrity sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

react@^17.0.2:
  version "17.0.2"
  resolved "https://mirrors.tencent.com/npm/react/-/react-17.0.2.tgz"
  integrity sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

react@^18.2.0:
  version "18.2.0"
  resolved "https://mirrors.tencent.com/npm/react/-/react-18.2.0.tgz"
  integrity sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==
  dependencies:
    loose-envify "^1.1.0"

reactcss@^1.2.0:
  version "1.2.3"
  resolved "https://mirrors.tencent.com/npm/reactcss/-/reactcss-1.2.3.tgz"
  integrity sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A==
  dependencies:
    lodash "^4.0.1"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "https://mirrors.tencent.com/npm/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  integrity sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "https://mirrors.tencent.com/npm/read-pkg/-/read-pkg-5.2.0.tgz"
  integrity sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@3, readable-stream@^3.0.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://mirrors.tencent.com/npm/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://mirrors.tencent.com/npm/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recoil-nexus@^0.5.0:
  version "0.5.0"
  resolved "https://mirrors.tencent.com/npm/recoil-nexus/-/recoil-nexus-0.5.0.tgz"
  integrity sha512-I2qerncPPmTPhFi0V/q2ebwKrRq9UXqZf30xoNUYLCZnjwxsusbfZKHICULVxjOhIJ3nkU2xrnVjdfVjKaYLEQ==

recoil@^0.7.7:
  version "0.7.7"
  resolved "https://mirrors.tencent.com/npm/recoil/-/recoil-0.7.7.tgz"
  integrity sha512-8Og5KPQW9LwC577Vc7Ug2P0vQshkv1y3zG3tSSkWMqkWSwHmE+by06L8JtnGocjW6gcCvfwB3YtrJG6/tWivNQ==
  dependencies:
    hamt_plus "1.0.2"

redent@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/redent/-/redent-3.0.0.tgz"
  integrity sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

redux-zero@^5.0.2, redux-zero@^5.1.7:
  version "5.1.7"
  resolved "https://mirrors.tencent.com/npm/redux-zero/-/redux-zero-5.1.7.tgz"
  integrity sha512-CGSnep4N3ivbmpdO/yAXwdmPv0zdbDBcim4XiErO77PLukpYolhSHBZbZo9HImb5qrnnKOd3dJpbgwFOrRN6uA==

redux@^4.0.0, redux@^4.0.4:
  version "4.2.1"
  resolved "https://mirrors.tencent.com/npm/redux/-/redux-4.2.1.tgz"
  integrity sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==
  dependencies:
    "@babel/runtime" "^7.9.2"

reflect.getprototypeof@^1.0.4:
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/reflect.getprototypeof/-/reflect.getprototypeof-1.0.6.tgz"
  integrity sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.1"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    globalthis "^1.0.3"
    which-builtin-type "^1.1.3"

regenerator-runtime@^0.13.4:
  version "0.13.11"
  resolved "https://mirrors.tencent.com/npm/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-runtime@^0.14.0, regenerator-runtime@^0.14.1:
  version "0.14.1"
  resolved "https://mirrors.tencent.com/npm/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.2:
  version "1.5.2"
  resolved "https://mirrors.tencent.com/npm/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz"
  integrity sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==
  dependencies:
    call-bind "^1.0.6"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.1"

registry-auth-token@^4.0.0:
  version "4.2.2"
  resolved "https://mirrors.tencent.com/npm/registry-auth-token/-/registry-auth-token-4.2.2.tgz"
  integrity sha512-PC5ZysNb42zpFME6D/XlIgtNGdTl8bBOCw90xQLVMpzuuubJKYDWFAEuUNc+Cn8Z8724tg2SDhDRrkVEsqfDMg==
  dependencies:
    rc "1.2.8"

registry-url@^5.0.0:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/registry-url/-/registry-url-5.1.0.tgz"
  integrity sha512-8acYXXTI0AkQv6RAOjE3vOaIXZkT9wo4LOFbBKYQEEnnMNBpKqdUrI6S4NT0KPIo/WVvJ5tE/X5LF/TQUf0ekw==
  dependencies:
    rc "^1.2.8"

remove-accents@0.5.0:
  version "0.5.0"
  resolved "https://mirrors.tencent.com/npm/remove-accents/-/remove-accents-0.5.0.tgz"
  integrity sha512-8g3/Otx1eJaVD12e31UbJj1YzdtVvzH85HV7t+9MJYk/u3XmkOUJ5Ys9wQrf9PCPK8+xn4ymzqYCiZl6QWKn+A==

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://mirrors.tencent.com/npm/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/require-main-filename/-/require-main-filename-2.0.0.tgz"
  integrity sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==

requireindex@^1.1.0:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/requireindex/-/requireindex-1.2.0.tgz"
  integrity sha512-L9jEkOi3ASd9PYit2cwRfyppc9NoABujTP8/5gFcbERmo5jUoAKovIC3fsF17pkTnGsrByysqX+Kxd2OTNI1ww==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://mirrors.tencent.com/npm/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-from@5.0.0, resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-global@1.0.0, resolve-global@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/resolve-global/-/resolve-global-1.0.0.tgz"
  integrity sha512-zFa12V4OLtT5XUX/Q4VLvTfBf+Ok0SPc1FNGM/z9ctUdiU618qwKpWnd0CHs3+RqROfyEg/DhuHbMWYqcgljEw==
  dependencies:
    global-dirs "^0.1.1"

resolve@^1.1.7, resolve@^1.10.0, resolve@^1.19.0, resolve@^1.22.2, resolve@^1.22.4:
  version "1.22.8"
  resolved "https://mirrors.tencent.com/npm/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "https://mirrors.tencent.com/npm/resolve/-/resolve-2.0.0-next.5.tgz"
  integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

responselike@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/responselike/-/responselike-1.0.2.tgz"
  integrity sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==
  dependencies:
    lowercase-keys "^1.0.0"

restore-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/restore-cursor/-/restore-cursor-4.0.0.tgz"
  integrity sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

restore-cursor@^5.0.0:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/restore-cursor/-/restore-cursor-5.1.0.tgz#0766d95699efacb14150993f55baf0953ea1ebe7"
  integrity sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==
  dependencies:
    onetime "^7.0.0"
    signal-exit "^4.1.0"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://mirrors.tencent.com/npm/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rfdc@^1.3.0, rfdc@^1.4.1:
  version "1.4.1"
  resolved "https://mirrors.tencent.com/npm/rfdc/-/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==

rimraf@3.0.2, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://mirrors.tencent.com/npm/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://mirrors.tencent.com/npm/rimraf/-/rimraf-2.7.1.tgz"
  integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
  dependencies:
    glob "^7.1.3"

rollup-plugin-visualizer@^5.12.0:
  version "5.12.0"
  resolved "https://mirrors.tencent.com/npm/rollup-plugin-visualizer/-/rollup-plugin-visualizer-5.12.0.tgz"
  integrity sha512-8/NU9jXcHRs7Nnj07PF2o4gjxmm9lXIrZ8r175bT9dK8qoLlvKTwRMArRCMgpMGlq8CTLugRvEmyMeMXIU2pNQ==
  dependencies:
    open "^8.4.0"
    picomatch "^2.3.1"
    source-map "^0.7.4"
    yargs "^17.5.1"

rollup@^4.20.0:
  version "4.37.0"
  resolved "https://mirrors.tencent.com/npm/rollup/-/rollup-4.37.0.tgz#e4172f8bdb6ea7df08a1b0acf99abeccb2250378"
  integrity sha512-iAtQy/L4QFU+rTJ1YUjXqJOJzuwEghqWzCEYD2FEghT7Gsy1VdABntrO4CLopA5IkflTyqNiLNwPcOJ3S7UKLg==
  dependencies:
    "@types/estree" "1.0.6"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.37.0"
    "@rollup/rollup-android-arm64" "4.37.0"
    "@rollup/rollup-darwin-arm64" "4.37.0"
    "@rollup/rollup-darwin-x64" "4.37.0"
    "@rollup/rollup-freebsd-arm64" "4.37.0"
    "@rollup/rollup-freebsd-x64" "4.37.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.37.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.37.0"
    "@rollup/rollup-linux-arm64-gnu" "4.37.0"
    "@rollup/rollup-linux-arm64-musl" "4.37.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.37.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.37.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.37.0"
    "@rollup/rollup-linux-riscv64-musl" "4.37.0"
    "@rollup/rollup-linux-s390x-gnu" "4.37.0"
    "@rollup/rollup-linux-x64-gnu" "4.37.0"
    "@rollup/rollup-linux-x64-musl" "4.37.0"
    "@rollup/rollup-win32-arm64-msvc" "4.37.0"
    "@rollup/rollup-win32-ia32-msvc" "4.37.0"
    "@rollup/rollup-win32-x64-msvc" "4.37.0"
    fsevents "~2.3.2"

rtl-css-js@^1.16.1:
  version "1.16.1"
  resolved "https://mirrors.tencent.com/npm/rtl-css-js/-/rtl-css-js-1.16.1.tgz#4b48b4354b0ff917a30488d95100fbf7219a3e80"
  integrity sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==
  dependencies:
    "@babel/runtime" "^7.1.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.4.0:
  version "7.8.1"
  resolved "https://mirrors.tencent.com/npm/rxjs/-/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.2:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/safe-array-concat/-/safe-array-concat-1.1.2.tgz"
  integrity sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://mirrors.tencent.com/npm/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-regex-test@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/safe-regex-test/-/safe-regex-test-1.0.3.tgz"
  integrity sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass-embedded-android-arm64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-android-arm64/-/sass-embedded-android-arm64-1.80.7.tgz#e5a28e08585b54b95ae782ca33d2c8ff53cc2e1e"
  integrity sha512-Gwl/OY80uEA14MLm7efJvc1ErgGT51SvAv4/kIpTziOJpkk+999/nrEJHQ6YAJ7r5DuQcKvC3lHipcENUIpP9A==

sass-embedded-android-arm@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-android-arm/-/sass-embedded-android-arm-1.80.7.tgz#6e8ed9b0988e6682833f970945ae54dbab95958a"
  integrity sha512-pMxJ70yOGXYGmfoGlAMKqnr/nuP/UgKV3jc7v5kpmWGpPPMF2u63DM2QkvTqM32FyfwyxSycVaNFNT+gPomTiw==

sass-embedded-android-ia32@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-android-ia32/-/sass-embedded-android-ia32-1.80.7.tgz#eb1d5554f44b599a460d6213a25e85975c09fe94"
  integrity sha512-CJccGPgBePPYiXhyQWvgHF8AqjIDSGf+mcC4Ac/f5upRd9Z/vhQVrJfsDxt4c4tV0HGEfbQpT9xOCYF1Z6luZQ==

sass-embedded-android-riscv64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-android-riscv64/-/sass-embedded-android-riscv64-1.80.7.tgz#9a204555cf4d31c8d9b3893a25dd4be27ef37c6c"
  integrity sha512-kIGcyuhNes9NUDzJ9VHy/ZGKdADCCt7JAwiC7lFSc6/xs5rJtGRn6hZ+mcG7gQWAezb5oK/VMQl8ps7HBFUEXw==

sass-embedded-android-x64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-android-x64/-/sass-embedded-android-x64-1.80.7.tgz#386f3ab0a6c13d84f0ac7d503c8210c2fdf295f8"
  integrity sha512-oLMQiFpbSczOrGZSWlZvVJ1T9L6nDjS2u8PTxfT0MFX/FT3EhaxylHeiYKrmtY4epRufNCC/G96DMVqnSNa1QQ==

sass-embedded-darwin-arm64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-darwin-arm64/-/sass-embedded-darwin-arm64-1.80.7.tgz#16ea36eb9912ac629b67bc237e37df121c3f6e10"
  integrity sha512-Vi5BbTWd9OO0tC60CPw5IY7w3Tccr1/Gy2DdkfE4qP6Rc368WmUis5ceG8ehAye0IT7aoRXpw8XTzWyXAZHbfw==

sass-embedded-darwin-x64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-darwin-x64/-/sass-embedded-darwin-x64-1.80.7.tgz#88e3cc6e821e5ec1d9e9ffb708e8cc928faefab0"
  integrity sha512-yeANclgSHJ7K/XLG4Lnk7aQ5dk7K+oqIOtoOP0bjXgWsdPbes9V7k1ZJ9mZGl+f/XAPaRRPqjKs4WHU9s8m8MA==

sass-embedded-linux-arm64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-arm64/-/sass-embedded-linux-arm64-1.80.7.tgz#c7fc8911590690b433819bbd18413aa72953271d"
  integrity sha512-Idb5K9LHHWklN7A/kqWUd6sktA36V70bSjZ/gvCDu/5CBJBkMsVNdrxcdpGzrZe7pYV4XUTkMZOwf91owEywtQ==

sass-embedded-linux-arm@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-arm/-/sass-embedded-linux-arm-1.80.7.tgz#a7fc36a28f4ddff2d323233f35bf44ea24932748"
  integrity sha512-ZttC6H2Z9YXUVFlprqZ0AgXuHdzqhvhUWsG7UUqkND9JSHvyFSwRij4h90aOK3gKg3PBGI4yG5tonLq2yV525A==

sass-embedded-linux-ia32@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-ia32/-/sass-embedded-linux-ia32-1.80.7.tgz#b49a09f3ad7f4b850743c6e4f8641ca5e45343bb"
  integrity sha512-xKnWWEFz1jFc9xDAG7nMcjPBCTuiJbqvTmEtwQoWj79hQrzVdkLM6SiUGVbGa1c2s2fJMS3Bg2fkDJBK6/BcuQ==

sass-embedded-linux-musl-arm64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-musl-arm64/-/sass-embedded-linux-musl-arm64-1.80.7.tgz#e76bf9c27155045d52e50347b7ed72d21a3f4ff2"
  integrity sha512-7+GCYIh+c1BG4ot/PbTvVXUxd2GxDWcMxV7i3sARStQBDpTZFfohWdjUytLyqGxQgJIrbq0Q60Ucrw6HUJtJ9A==

sass-embedded-linux-musl-arm@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-musl-arm/-/sass-embedded-linux-musl-arm-1.80.7.tgz#13894262bd3512f68f405ec5e7d8672c49d4d0e0"
  integrity sha512-gJLfSFiiuGaqWjaj0bcuhOlQ+t1jS9StuzXnW1b9gy2I6Y0uCprgbbELgtRVPSZlCG2BBolR76YCGQTB85M43Q==

sass-embedded-linux-musl-ia32@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-musl-ia32/-/sass-embedded-linux-musl-ia32-1.80.7.tgz#059b408cf686348e39719a02f0195c0456b889a6"
  integrity sha512-Iw2E6P1lha335C5tGNgPjLD7Oll7OdLBJ7uPKaU+I7KbiOPk7ELsxUL9AYIrKO0/MLtgxGqOWWfTo/5cvU8xSA==

sass-embedded-linux-musl-riscv64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-musl-riscv64/-/sass-embedded-linux-musl-riscv64-1.80.7.tgz#95b082ecadc32191139a540327d447eac36081bf"
  integrity sha512-gd92dkDVpTh4xJb2hpX82E6el30h4MxCb7VJLwtbQSrQuxOlZgaDX4plMSZifsNTLvOsafdLCYyI+QsZRr8bkA==

sass-embedded-linux-musl-x64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-musl-x64/-/sass-embedded-linux-musl-x64-1.80.7.tgz#3117be5592ff65a8e93323bca452695ccb98519b"
  integrity sha512-i5udU+i0LZrL3dhHAgIfK7LBaHtScwAceiykndNIHyRXc1TY2DX3lG0EolVUvPyWFUNnvGCgxZF8oUToPzJ+pw==

sass-embedded-linux-riscv64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-riscv64/-/sass-embedded-linux-riscv64-1.80.7.tgz#6a4cb268d319e5f194f0065f767a666b5226c140"
  integrity sha512-DvnXvu019c6THNQnSWfy2eY/HFWZ2ogGUjRkdKAxj7U7i/YD+bsDIxdDQHZ48qzOguzx8n2aRa/clriM0HQPUA==

sass-embedded-linux-x64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-linux-x64/-/sass-embedded-linux-x64-1.80.7.tgz#dc50e838e9afb48663c80f70a12920a070727bbb"
  integrity sha512-nQB+IZwCzVPpPkP5L9zV416/AGPLky7L2GGPWtvxG2CEeTV1Rzet+gkhzk2eYEdbh+3py/w9YVRTaQuZ3QV0vQ==

sass-embedded-win32-arm64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-win32-arm64/-/sass-embedded-win32-arm64-1.80.7.tgz#7c8110ad2b02474da27b14f4a99e5040b1b06a86"
  integrity sha512-Q6Rh/CM30m8txoKK5SIVamnwPXs028Mvfq4Ol4saHgSYro9kY/HTrrWlG/RPd6sPvYBCYIm1mX8oBteDUMCajQ==

sass-embedded-win32-ia32@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-win32-ia32/-/sass-embedded-win32-ia32-1.80.7.tgz#c678d605bf1a01a2cf77a4d052c891925a1f7298"
  integrity sha512-VZMRp81KWUZZDqNwkL3yTDT+VRxB7ScJKUJD1M8fq6P1nyJP35+r1byXLF4UQMoNgpC5B16txxMvqdkv43OqAA==

sass-embedded-win32-x64@1.80.7:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded-win32-x64/-/sass-embedded-win32-x64-1.80.7.tgz#67d716449ed702cc96388c47e76a40ab691bb174"
  integrity sha512-4p+GzOJJ1KqxPKrkIkKisod4YAcC70fj4WMRLrQLLuUW+MzAvtKgX2+ZJf90D50CozSdgETGBvdPSj3VLjBzZw==

sass-embedded@^1.80.3:
  version "1.80.7"
  resolved "https://mirrors.tencent.com/npm/sass-embedded/-/sass-embedded-1.80.7.tgz#68c51318dd6fba5d802c80de8c1df20c0f8ea966"
  integrity sha512-OwF0QvpDUjW2udPCvxgaObU0tQHycpsIgCDtHBVHuOqZ2LN0OkkY+uxSO7bOaw9wD7vXtt+1V+jiIZDTxiSRVQ==
  dependencies:
    "@bufbuild/protobuf" "^2.0.0"
    buffer-builder "^0.2.0"
    colorjs.io "^0.5.0"
    immutable "^5.0.2"
    rxjs "^7.4.0"
    supports-color "^8.1.1"
    sync-child-process "^1.0.2"
    varint "^6.0.0"
  optionalDependencies:
    sass-embedded-android-arm "1.80.7"
    sass-embedded-android-arm64 "1.80.7"
    sass-embedded-android-ia32 "1.80.7"
    sass-embedded-android-riscv64 "1.80.7"
    sass-embedded-android-x64 "1.80.7"
    sass-embedded-darwin-arm64 "1.80.7"
    sass-embedded-darwin-x64 "1.80.7"
    sass-embedded-linux-arm "1.80.7"
    sass-embedded-linux-arm64 "1.80.7"
    sass-embedded-linux-ia32 "1.80.7"
    sass-embedded-linux-musl-arm "1.80.7"
    sass-embedded-linux-musl-arm64 "1.80.7"
    sass-embedded-linux-musl-ia32 "1.80.7"
    sass-embedded-linux-musl-riscv64 "1.80.7"
    sass-embedded-linux-musl-x64 "1.80.7"
    sass-embedded-linux-riscv64 "1.80.7"
    sass-embedded-linux-x64 "1.80.7"
    sass-embedded-win32-arm64 "1.80.7"
    sass-embedded-win32-ia32 "1.80.7"
    sass-embedded-win32-x64 "1.80.7"

sax@^1.2.4:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/sax/-/sax-1.3.0.tgz"
  integrity sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==

saxes@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/saxes/-/saxes-6.0.0.tgz"
  integrity sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.19.1:
  version "0.19.1"
  resolved "https://mirrors.tencent.com/npm/scheduler/-/scheduler-0.19.1.tgz"
  integrity sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://mirrors.tencent.com/npm/scheduler/-/scheduler-0.20.2.tgz"
  integrity sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.21.0:
  version "0.21.0"
  resolved "https://mirrors.tencent.com/npm/scheduler/-/scheduler-0.21.0.tgz#6fd2532ff5a6d877b6edb12f00d8ab7e8f308820"
  integrity sha512-1r87x5fz9MXqswA2ERLo0EbOAU74DpIUO090gIasYTqlVoJeMcl+Z1Rg7WHz+qtPujhS/hGIt9kxZOYBV3faRQ==
  dependencies:
    loose-envify "^1.1.0"

scheduler@^0.23.0:
  version "0.23.2"
  resolved "https://mirrors.tencent.com/npm/scheduler/-/scheduler-0.23.2.tgz"
  integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
  dependencies:
    loose-envify "^1.1.0"

screenfull@^5.0.0:
  version "5.2.0"
  resolved "https://mirrors.tencent.com/npm/screenfull/-/screenfull-5.2.0.tgz"
  integrity sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==

scroll-into-view-if-needed@^2.2.25:
  version "2.2.31"
  resolved "https://mirrors.tencent.com/npm/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz"
  integrity sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==
  dependencies:
    compute-scroll-into-view "^1.0.20"

semver-diff@^3.1.1:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/semver-diff/-/semver-diff-3.1.1.tgz"
  integrity sha512-GX0Ix/CJcHyB8c4ykpHGIAvLyOwOobtM/8d+TQkAd81/bEjgPHrfba41Vpesr7jX/t8Uh+R3EX9eAS5be+jQYg==
  dependencies:
    semver "^6.3.0"

"semver@2 || 3 || 4 || 5", semver@^5.5.0, semver@^5.6.0:
  version "5.7.2"
  resolved "https://mirrors.tencent.com/npm/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@7.5.4:
  version "7.5.4"
  resolved "https://mirrors.tencent.com/npm/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

semver@^6.0.0, semver@^6.2.0, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://mirrors.tencent.com/npm/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.3.4, semver@^7.3.5, semver@^7.3.7, semver@^7.6.3:
  version "7.6.3"
  resolved "https://mirrors.tencent.com/npm/semver/-/semver-7.6.3.tgz"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

sentence-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/sentence-case/-/sentence-case-3.0.4.tgz"
  integrity sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"
    upper-case-first "^2.0.2"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://mirrors.tencent.com/npm/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.1, set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-harmonic-interval@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/set-harmonic-interval/-/set-harmonic-interval-1.0.1.tgz#e1773705539cdfb80ce1c3d99e7f298bb3995249"
  integrity sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==

shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/shallowequal/-/shallowequal-1.1.0.tgz"
  integrity sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/shebang-command/-/shebang-command-1.2.0.tgz"
  integrity sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/shebang-regex/-/shebang-regex-1.0.0.tgz"
  integrity sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4, side-channel@^1.0.6:
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/side-channel/-/side-channel-1.0.6.tgz"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://mirrors.tencent.com/npm/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1, signal-exit@^4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/simple-concat/-/simple-concat-1.0.1.tgz"
  integrity sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==

simple-get@^3.0.3:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/simple-get/-/simple-get-3.1.1.tgz"
  integrity sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==
  dependencies:
    decompress-response "^4.2.0"
    once "^1.3.1"
    simple-concat "^1.0.0"

slash@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/slash/-/slash-2.0.0.tgz"
  integrity sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/slice-ansi/-/slice-ansi-5.0.0.tgz"
  integrity sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

slice-ansi@^7.1.0:
  version "7.1.0"
  resolved "https://mirrors.tencent.com/npm/slice-ansi/-/slice-ansi-7.1.0.tgz#cd6b4655e298a8d1bdeb04250a433094b347b9a9"
  integrity sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==
  dependencies:
    ansi-styles "^6.2.1"
    is-fullwidth-code-point "^5.0.0"

snake-case@^3.0.4:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/snake-case/-/snake-case-3.0.4.tgz"
  integrity sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

sortablejs@^1.15.0, sortablejs@^1.15.1, sortablejs@^1.15.2:
  version "1.15.2"
  resolved "https://mirrors.tencent.com/npm/sortablejs/-/sortablejs-1.15.2.tgz"
  integrity sha512-FJF5jgdfvoKn1MAKSdGs33bIqLi3LmsgVTliuX6iITj834F+JRQZN90Z93yql8h0K2t0RwDPBmxwlbZfDcxNZA==

source-map-js@^1.2.0:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/source-map-js/-/source-map-js-1.2.0.tgz"
  integrity sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://mirrors.tencent.com/npm/source-map-js/-/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@0.5.6:
  version "0.5.6"
  resolved "https://mirrors.tencent.com/npm/source-map/-/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"
  integrity sha1-dc449SvwczxafwwRjYEzSiu19BI=

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://mirrors.tencent.com/npm/source-map/-/source-map-0.5.7.tgz"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://mirrors.tencent.com/npm/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.7.4:
  version "0.7.4"
  resolved "https://mirrors.tencent.com/npm/source-map/-/source-map-0.7.4.tgz"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

spark-md5@^3.0.2:
  version "3.0.2"
  resolved "https://mirrors.tencent.com/npm/spark-md5/-/spark-md5-3.0.2.tgz"
  integrity sha512-wcFzz9cDfbuqe0FZzfi2or1sgyIrsDwmPwfZC4hiNidPdPINjeUwNfv5kldczoEAcjl9Y1L3SM7Uz2PUEQzxQw==

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "https://mirrors.tencent.com/npm/spdx-correct/-/spdx-correct-3.2.0.tgz"
  integrity sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"
  resolved "https://mirrors.tencent.com/npm/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz"
  integrity sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.17"
  resolved "https://mirrors.tencent.com/npm/spdx-license-ids/-/spdx-license-ids-3.0.17.tgz"
  integrity sha512-sh8PWc/ftMqAAdFiBu6Fy6JUOYjqDJBJvIhpfDMyHrr0Rbp5liZqd4TjtQ/RgfLjKFZb+LMx5hpml5qOWy0qvg==

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/split-on-first/-/split-on-first-1.1.0.tgz"
  integrity sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==

split2@^3.0.0, split2@^3.2.2:
  version "3.2.2"
  resolved "https://mirrors.tencent.com/npm/split2/-/split2-3.2.2.tgz"
  integrity sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==
  dependencies:
    readable-stream "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

ssf@~0.11.2:
  version "0.11.2"
  resolved "https://mirrors.tencent.com/npm/ssf/-/ssf-0.11.2.tgz"
  integrity sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==
  dependencies:
    frac "~1.1.2"

ssr-window@^4.0.2:
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/ssr-window/-/ssr-window-4.0.2.tgz#dc6b3ee37be86ac0e3ddc60030f7b3bc9b8553be"
  integrity sha512-ISv/Ch+ig7SOtw7G2+qkwfVASzazUnvlDTwypdLoPoySv+6MqlOV10VwPSE6EWkGjhW50lUmghPmpYZXMu/+AQ==

stack-generator@^2.0.5:
  version "2.0.10"
  resolved "https://mirrors.tencent.com/npm/stack-generator/-/stack-generator-2.0.10.tgz#8ae171e985ed62287d4f1ed55a1633b3fb53bb4d"
  integrity sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==
  dependencies:
    stackframe "^1.3.4"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://mirrors.tencent.com/npm/stackframe/-/stackframe-1.3.4.tgz#b881a004c8c149a5e8efef37d51b16e412943310"
  integrity sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==

stacktrace-gps@^3.0.4:
  version "3.1.2"
  resolved "https://mirrors.tencent.com/npm/stacktrace-gps/-/stacktrace-gps-3.1.2.tgz#0c40b24a9b119b20da4525c398795338966a2fb0"
  integrity sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==
  dependencies:
    source-map "0.5.6"
    stackframe "^1.3.4"

stacktrace-js@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/stacktrace-js/-/stacktrace-js-2.0.2.tgz#4ca93ea9f494752d55709a081d400fdaebee897b"
  integrity sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==
  dependencies:
    error-stack-parser "^2.0.6"
    stack-generator "^2.0.5"
    stacktrace-gps "^3.0.4"

state-local@^1.0.6:
  version "1.0.7"
  resolved "https://mirrors.tencent.com/npm/state-local/-/state-local-1.0.7.tgz"
  integrity sha512-HTEHMNieakEnoe33shBYcZ7NX83ACUjCu8c40iOGEZsngj9zRnkqS9j1pqQPXwobB0ZcVTk27REb7COQ0UR59w==

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz"
  integrity sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==

string-argv@0.3.2:
  version "0.3.2"
  resolved "https://mirrors.tencent.com/npm/string-argv/-/string-argv-0.3.2.tgz"
  integrity sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://mirrors.tencent.com/npm/string-convert/-/string-convert-0.2.1.tgz"
  integrity sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://mirrors.tencent.com/npm/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.0.0, string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.2, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://mirrors.tencent.com/npm/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.0, string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://mirrors.tencent.com/npm/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^7.0.0:
  version "7.2.0"
  resolved "https://mirrors.tencent.com/npm/string-width/-/string-width-7.2.0.tgz#b5bb8e2165ce275d4d43476dd2700ad9091db6dc"
  integrity sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==
  dependencies:
    emoji-regex "^10.3.0"
    get-east-asian-width "^1.0.0"
    strip-ansi "^7.1.0"

string.prototype.matchall@^4.0.10:
  version "4.0.11"
  resolved "https://mirrors.tencent.com/npm/string.prototype.matchall/-/string.prototype.matchall-4.0.11.tgz"
  integrity sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    regexp.prototype.flags "^1.5.2"
    set-function-name "^2.0.2"
    side-channel "^1.0.6"

string.prototype.trim@^1.2.9:
  version "1.2.9"
  resolved "https://mirrors.tencent.com/npm/string.prototype.trim/-/string.prototype.trim-1.2.9.tgz"
  integrity sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.0"
    es-object-atoms "^1.0.0"

string.prototype.trimend@^1.0.8:
  version "1.0.8"
  resolved "https://mirrors.tencent.com/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.8.tgz"
  integrity sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://mirrors.tencent.com/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://mirrors.tencent.com/npm/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://mirrors.tencent.com/npm/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://mirrors.tencent.com/npm/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1, strip-ansi@^7.1.0:
  version "7.1.0"
  resolved "https://mirrors.tencent.com/npm/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/strip-eof/-/strip-eof-1.0.0.tgz"
  integrity sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/strip-final-newline/-/strip-final-newline-3.0.0.tgz"
  integrity sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/strip-indent/-/strip-indent-2.0.0.tgz"
  integrity sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA==

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/strip-indent/-/strip-indent-3.0.0.tgz"
  integrity sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  integrity sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==

strnum@^1.0.5:
  version "1.1.2"
  resolved "https://mirrors.tencent.com/npm/strnum/-/strnum-1.1.2.tgz#57bca4fbaa6f271081715dbc9ed7cee5493e28e4"
  integrity sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==

stylis@4.2.0:
  version "4.2.0"
  resolved "https://mirrors.tencent.com/npm/stylis/-/stylis-4.2.0.tgz"
  integrity sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==

stylis@^4.3.0, stylis@^4.3.2:
  version "4.3.4"
  resolved "https://mirrors.tencent.com/npm/stylis/-/stylis-4.3.4.tgz#ca5c6c4a35c4784e4e93a2a24dc4e9fa075250a4"
  integrity sha512-osIBl6BGUmSfDkyH2mB7EFvCJntXDrLhKjHTRj/rK6xLH0yuPrHULDRQzKokSOD4VoorhtKpfcfW1GAntu8now==

sucrase@^3.32.0:
  version "3.35.0"
  resolved "https://mirrors.tencent.com/npm/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://mirrors.tencent.com/npm/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://mirrors.tencent.com/npm/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.1.1:
  version "8.1.1"
  resolved "https://mirrors.tencent.com/npm/supports-color/-/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

suspend-react@^0.1.3:
  version "0.1.3"
  resolved "https://mirrors.tencent.com/npm/suspend-react/-/suspend-react-0.1.3.tgz#a52f49d21cfae9a2fb70bd0c68413d3f9d90768e"
  integrity sha512-aqldKgX9aZqpoDp3e8/BZ8Dm7x1pJl+qI3ZKxDN0i/IQTWUwBx/ManmlVJ3wowqbno6c2bmiIfs+Um6LbsjJyQ==

swiper@^9.3.2:
  version "9.4.1"
  resolved "https://mirrors.tencent.com/npm/swiper/-/swiper-9.4.1.tgz#2f48bcd6ab4b4fcf4ae93eaee53980531d42fd42"
  integrity sha512-1nT2T8EzUpZ0FagEqaN/YAhRj33F2x/lN6cyB0/xoYJDMf8KwTFT3hMOeoB8Tg4o3+P/CKqskP+WX0Df046fqA==
  dependencies:
    ssr-window "^4.0.2"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "https://mirrors.tencent.com/npm/symbol-tree/-/symbol-tree-3.2.4.tgz"
  integrity sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==

sync-child-process@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/sync-child-process/-/sync-child-process-1.0.2.tgz#45e7c72e756d1243e80b547ea2e17957ab9e367f"
  integrity sha512-8lD+t2KrrScJ/7KXCSyfhT3/hRq78rC0wBFqNJXv3mZyn6hW2ypM05JmlSvtqRbeq6jqA94oHbxAr2vYsJ8vDA==
  dependencies:
    sync-message-port "^1.0.0"

sync-message-port@^1.0.0:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/sync-message-port/-/sync-message-port-1.1.3.tgz#6055c565ee8c81d2f9ee5aae7db757e6d9088c0c"
  integrity sha512-GTt8rSKje5FilG+wEdfCkOcLL7LWqpMlr2c3LRuKt/YXxcJ52aGSbGBAdI4L3aaqfrBt6y711El53ItyH1NWzg==

tailwindcss@^3.4.3:
  version "3.4.3"
  resolved "https://mirrors.tencent.com/npm/tailwindcss/-/tailwindcss-3.4.3.tgz"
  integrity sha512-U7sxQk/n397Bmx4JHbJx/iSOOv5G+II3f1kpLpY2QeUv5DcPdcTsYLlusZfq1NthHS1c1cZoyFmmkex1rzke0A==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.5.3"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.0"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.0"
    lilconfig "^2.1.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.23"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.1"
    postcss-nested "^6.0.1"
    postcss-selector-parser "^6.0.11"
    resolve "^1.22.2"
    sucrase "^3.32.0"

tar@^6.1.11:
  version "6.2.1"
  resolved "https://mirrors.tencent.com/npm/tar/-/tar-6.2.1.tgz"
  integrity sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

tdesign-icons-react@^0.3.2, tdesign-icons-react@^0.3.4:
  version "0.3.4"
  resolved "https://mirrors.tencent.com/npm/tdesign-icons-react/-/tdesign-icons-react-0.3.4.tgz"
  integrity sha512-y4QOyqZkkxn7B2HDRHEqOKqQaVzw1ODp9Apu0i7TbpsLnakFYxnQzeTgDYUR7rIzsPrkkzIBkOh8myA9RKrZCQ==
  dependencies:
    "@babel/runtime" "^7.16.5"
    classnames "^2.2.6"

tdesign-react@1.9.0, tdesign-react@^1.1.17, tdesign-react@^1.2.1, tdesign-react@^1.6.0:
  version "1.9.0"
  resolved "https://mirrors.tencent.com/npm/tdesign-react/-/tdesign-react-1.9.0.tgz"
  integrity sha512-bwZhVwKZxWo1g3+JrxUeE64K40sz5YrQsfLNHo0eB8nML/WVS0hXbjScCHnS5czvatbYV7oYtu7QjjCvxEkz2A==
  dependencies:
    "@babel/runtime" "~7.24.7"
    "@popperjs/core" "~2.11.2"
    "@types/sortablejs" "^1.10.7"
    "@types/tinycolor2" "^1.4.3"
    "@types/validator" "^13.1.3"
    classnames "~2.5.1"
    dayjs "1.11.10"
    hoist-non-react-statics "~3.3.2"
    lodash "~4.17.15"
    mitt "^3.0.0"
    raf "~3.4.1"
    react-is "^18.2.0"
    react-popper "~2.3.0"
    react-transition-group "~4.4.1"
    sortablejs "^1.15.0"
    tdesign-icons-react "^0.3.4"
    tinycolor2 "^1.4.2"
    tslib "~2.3.1"
    validator "~13.7.0"

text-extensions@^1.0.0:
  version "1.9.0"
  resolved "https://mirrors.tencent.com/npm/text-extensions/-/text-extensions-1.9.0.tgz"
  integrity sha512-wiBrwC1EhBelW12Zy26JeOUkQ5mRu+5o8rpsJk5+2t+Y5vE7e842qtZDQ2g1NpX/29HdyFeJ4nSIhI47ENSxlQ==

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "https://mirrors.tencent.com/npm/text-segmentation/-/text-segmentation-1.0.3.tgz"
  integrity sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==
  dependencies:
    utrie "^1.0.2"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://mirrors.tencent.com/npm/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://mirrors.tencent.com/npm/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

throttle-debounce@^2.1.0:
  version "2.3.0"
  resolved "https://mirrors.tencent.com/npm/throttle-debounce/-/throttle-debounce-2.3.0.tgz#fd31865e66502071e411817e241465b3e9c372e2"
  integrity sha512-H7oLPV0P7+jgvrk+6mwwwBDmxTaxnu9HMXmloNLXwnNO0ZxZ31Orah2n8lU1eMPvsaowP2CX+USCgyovXfdOFQ==

throttle-debounce@^3.0.1:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/throttle-debounce/-/throttle-debounce-3.0.1.tgz#32f94d84dfa894f786c9a1f290e7a645b6a19abb"
  integrity sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==

through2@^4.0.0:
  version "4.0.2"
  resolved "https://mirrors.tencent.com/npm/through2/-/through2-4.0.2.tgz"
  integrity sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw==
  dependencies:
    readable-stream "3"

"through@>=2.2.7 <3":
  version "2.3.8"
  resolved "https://mirrors.tencent.com/npm/through/-/through-2.3.8.tgz"
  integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==

tiny-invariant@1.2.0:
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/tiny-invariant/-/tiny-invariant-1.2.0.tgz"
  integrity sha512-1Uhn/aqw5C6RI4KejVeTg6mIS7IqxnLJ8Mv2tV5rTc0qWobay7pDUz6Wi392Cnc8ak1H0F2cjoRzb2/AW4+Fvg==

tiny-invariant@^1.0.6:
  version "1.3.3"
  resolved "https://mirrors.tencent.com/npm/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  integrity sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==

tinycolor2@1.4.2:
  version "1.4.2"
  resolved "https://mirrors.tencent.com/npm/tinycolor2/-/tinycolor2-1.4.2.tgz"
  integrity sha512-vJhccZPs965sV/L2sU4oRQVAos0pQXwsvTLkWYdqJ+a8Q5kPFzJTuOFwy7UniPli44NKQGAglksjvOcpo95aZA==

tinycolor2@^1.4.1, tinycolor2@^1.4.2:
  version "1.6.0"
  resolved "https://mirrors.tencent.com/npm/tinycolor2/-/tinycolor2-1.6.0.tgz"
  integrity sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://mirrors.tencent.com/npm/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

to-camel-case@1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/to-camel-case/-/to-camel-case-1.0.0.tgz"
  integrity sha512-nD8pQi5H34kyu1QDMFjzEIYqk0xa9Alt6ZfrdEMuHCFOfTLhDG5pgTu/aAM9Wt9lXILwlXmWP43b8sav0GNE8Q==
  dependencies:
    to-space-case "^1.0.0"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==

to-no-case@^1.0.0:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/to-no-case/-/to-no-case-1.0.2.tgz"
  integrity sha512-Z3g735FxuZY8rodxV4gH7LxClE4H0hTIyHNIHdk+vpQxjLm0cwnKXq/OFVZ76SOQmto7txVcwSCwkU5kqp+FKg==

to-readable-stream@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/to-readable-stream/-/to-readable-stream-1.0.0.tgz"
  integrity sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://mirrors.tencent.com/npm/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

to-space-case@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/to-space-case/-/to-space-case-1.0.0.tgz"
  integrity sha512-rLdvwXZ39VOn1IxGL3V6ZstoTbwLRckQmn/U8ZDLuWwIXNpuZDhQ3AiRUlhTbOXFVE9C+dR51wM0CBDhk31VcA==
  dependencies:
    to-no-case "^1.0.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/toggle-selection/-/toggle-selection-1.0.6.tgz"
  integrity sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==

tough-cookie@^4.1.2:
  version "4.1.4"
  resolved "https://mirrors.tencent.com/npm/tough-cookie/-/tough-cookie-4.1.4.tgz"
  integrity sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/tr46/-/tr46-3.0.0.tgz"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://mirrors.tencent.com/npm/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/trim-newlines/-/trim-newlines-3.0.1.tgz"
  integrity sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==

ts-easing@^0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/ts-easing/-/ts-easing-0.2.0.tgz#c8a8a35025105566588d87dbda05dd7fbfa5a4ec"
  integrity sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://mirrors.tencent.com/npm/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

ts-node@^10.8.1:
  version "10.9.2"
  resolved "https://mirrors.tencent.com/npm/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "https://mirrors.tencent.com/npm/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
  integrity sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "https://mirrors.tencent.com/npm/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.0, tslib@^2.3.1, tslib@^2.4.1:
  version "2.8.1"
  resolved "https://mirrors.tencent.com/npm/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tslib@~2.3.1:
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/tslib/-/tslib-2.3.1.tgz"
  integrity sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw==

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://mirrors.tencent.com/npm/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://mirrors.tencent.com/npm/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.18.0:
  version "0.18.1"
  resolved "https://mirrors.tencent.com/npm/type-fest/-/type-fest-0.18.1.tgz"
  integrity sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://mirrors.tencent.com/npm/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://mirrors.tencent.com/npm/type-fest/-/type-fest-0.6.0.tgz"
  integrity sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://mirrors.tencent.com/npm/type-fest/-/type-fest-0.8.1.tgz"
  integrity sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==

type-fest@^1.0.2:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/type-fest/-/type-fest-1.4.0.tgz"
  integrity sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==

typed-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/typed-array-buffer/-/typed-array-buffer-1.0.2.tgz"
  integrity sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-typed-array "^1.1.13"

typed-array-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/typed-array-byte-length/-/typed-array-byte-length-1.0.1.tgz"
  integrity sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-byte-offset@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/typed-array-byte-offset/-/typed-array-byte-offset-1.0.2.tgz"
  integrity sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-length@^1.0.6:
  version "1.0.6"
  resolved "https://mirrors.tencent.com/npm/typed-array-length/-/typed-array-length-1.0.6.tgz"
  integrity sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"

typed-styles@^0.0.7:
  version "0.0.7"
  resolved "https://mirrors.tencent.com/npm/typed-styles/-/typed-styles-0.0.7.tgz"
  integrity sha512-pzP0PWoZUhsECYjABgCGQlRGL1n7tOHsgwYv3oIiEpJwGhFTuty/YNeduxQYzXXa3Ge5BdT6sHYIQYpl4uJ+5Q==

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://mirrors.tencent.com/npm/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
  dependencies:
    is-typedarray "^1.0.0"

types@^0.1.1:
  version "0.1.1"
  resolved "https://mirrors.tencent.com/npm/types/-/types-0.1.1.tgz#860c6859d11366293f835d8c95aebcf95029838e"
  integrity sha512-JuntZtJj4MKLE9x/XBs7IjsznYhzETwr34pw3XJTKvgYtAMdeMG+o8x8U85E5Lm6eCPa1DdOdGVsHMwq4ZnZAg==

"typescript@^4.6.4 || ^5.2.2", typescript@^5.2.2:
  version "5.4.5"
  resolved "https://mirrors.tencent.com/npm/typescript/-/typescript-5.4.5.tgz"
  integrity sha512-vcI4UpRgg81oIRUFwR0WSIHKt11nJ7SAVlYNIu+QpqeyXP+gpQJy/Z4+F0aGxSE4MqwjyXvW/TzgkLAx2AGHwQ==

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://mirrors.tencent.com/npm/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/unique-string/-/unique-string-2.0.0.tgz"
  integrity sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^0.2.0:
  version "0.2.0"
  resolved "https://mirrors.tencent.com/npm/universalify/-/universalify-0.2.0.tgz"
  integrity sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

unload@2.2.0:
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/unload/-/unload-2.2.0.tgz"
  integrity sha512-B60uB5TNBLtN6/LsgAf3udH9saB5p7gqJwcFfbOEZ8BcBHnGwCf6G/TGiEqkRAxX7zAFIUtzdrXQSdL3Q/wqNA==
  dependencies:
    "@babel/runtime" "^7.6.2"
    detect-node "^2.0.4"

update-browserslist-db@^1.1.0:
  version "1.1.0"
  resolved "https://mirrors.tencent.com/npm/update-browserslist-db/-/update-browserslist-db-1.1.0.tgz"
  integrity sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==
  dependencies:
    escalade "^3.1.2"
    picocolors "^1.0.1"

update-notifier@^5.1.0:
  version "5.1.0"
  resolved "https://mirrors.tencent.com/npm/update-notifier/-/update-notifier-5.1.0.tgz"
  integrity sha512-ItnICHbeMh9GqUy31hFPrD1kcuZ3rpxDZbf4KUDavXwS0bW5m7SLbDQpGX3UYr072cbrF5hFUs3r5tUsPwjfHw==
  dependencies:
    boxen "^5.0.0"
    chalk "^4.1.0"
    configstore "^5.0.1"
    has-yarn "^2.1.0"
    import-lazy "^2.1.0"
    is-ci "^2.0.0"
    is-installed-globally "^0.4.0"
    is-npm "^5.0.0"
    is-yarn-global "^0.3.0"
    latest-version "^5.1.0"
    pupa "^2.1.1"
    semver "^7.3.4"
    semver-diff "^3.1.1"
    xdg-basedir "^4.0.0"

upper-case-first@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/upper-case-first/-/upper-case-first-2.0.2.tgz"
  integrity sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==
  dependencies:
    tslib "^2.0.3"

upper-case@^2.0.1, upper-case@^2.0.2:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/upper-case/-/upper-case-2.0.2.tgz"
  integrity sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==
  dependencies:
    tslib "^2.0.3"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://mirrors.tencent.com/npm/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

url-parse-lax@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/url-parse-lax/-/url-parse-lax-3.0.0.tgz"
  integrity sha512-NjFKA0DidqPa5ciFcSrXnAltTtzz84ogy+NebPvfEgAck0+TNg4UJ4IN+fB7zRZfbgUf0syOo9MDxFkDSMuFaQ==
  dependencies:
    prepend-http "^2.0.0"

url-parse@^1.5.3:
  version "1.5.10"
  resolved "https://mirrors.tencent.com/npm/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-debounce@^3.4.3:
  version "3.4.3"
  resolved "https://mirrors.tencent.com/npm/use-debounce/-/use-debounce-3.4.3.tgz"
  integrity sha512-nxy+opOxDccWfhMl36J5BSCTpvcj89iaQk2OZWLAtBJQj7ISCtx1gh+rFbdjGfMl6vtCZf6gke/kYvrkVfHMoA==

use-memo-one@^1.1.1:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/use-memo-one/-/use-memo-one-1.1.3.tgz"
  integrity sha512-g66/K7ZQGYrI6dy8GLpVcMsBp4s17xNkYJVSMvTEevGy3nDxHOfE6z8BVE22+5G5x7t3+bhzrlTDB7ObrEE0cQ==

util-deprecate@^1.0.1, util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utrie@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/utrie/-/utrie-1.0.2.tgz"
  integrity sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==
  dependencies:
    base64-arraybuffer "^1.0.2"

uuid@^8.3.1:
  version "8.3.2"
  resolved "https://mirrors.tencent.com/npm/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://mirrors.tencent.com/npm/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validator@~13.7.0:
  version "13.7.0"
  resolved "https://mirrors.tencent.com/npm/validator/-/validator-13.7.0.tgz"
  integrity sha512-nYXQLCBkpJ8X6ltALua9dRrZDHVYxjJ1wgskNt1lH9fzGjs3tgojGSCBjmEPwkWS1y29+DrizMTW19Pr9uB2nw==

varint@^6.0.0:
  version "6.0.0"
  resolved "https://mirrors.tencent.com/npm/varint/-/varint-6.0.0.tgz#9881eb0ce8feaea6512439d19ddf84bf551661d0"
  integrity sha512-cXEIW6cfr15lFv563k4GuVuW/fiwjknytD37jIOLSdSWuOI6WnO/oKwmP2FQTU2l01LP8/M5TSAJpzUaGe3uWg==

vite-plugin-html-config@^1.0.11:
  version "1.0.11"
  resolved "https://mirrors.tencent.com/npm/vite-plugin-html-config/-/vite-plugin-html-config-1.0.11.tgz"
  integrity sha512-hUybhgI+/LQQ5q6xoMMsTvI4PBuQD/Wv6Z1vtDPVWjanS8weCIexXuLLYNGD/93f0v8W2hpNfXpmxgpZMahJ0g==

vite-plugin-jsx-plus@^0.1.0:
  version "0.1.0"
  resolved "https://mirrors.tencent.com/npm/vite-plugin-jsx-plus/-/vite-plugin-jsx-plus-0.1.0.tgz"
  integrity sha512-pSiJ/9ZAe9dVEHBHKj9VOjCcP/cE/ndH3dHenZVCW2UTfsW2aQcbwZTWnoh2XA5wzMKgjIhLSNz21hQ7aSuREw==
  dependencies:
    "@babel/core" "^7.15.8"
    "@babel/plugin-transform-typescript" "^7.15.8"
    "@rollup/pluginutils" "^4.1.1"
    babel-plugin-transform-jsx-class "^0.1.3"
    babel-plugin-transform-jsx-condition "^0.1.2"
    babel-plugin-transform-jsx-fragment "^0.1.4"
    babel-plugin-transform-jsx-list "^0.1.2"
    babel-plugin-transform-jsx-memo "^0.1.4"
    babel-plugin-transform-jsx-slot "^0.1.2"

vite@^5.4.15:
  version "5.4.15"
  resolved "https://mirrors.tencent.com/npm/vite/-/vite-5.4.15.tgz#2941547f10ebb4bf9b0fa0da863c06711eb7e5e5"
  integrity sha512-6ANcZRivqL/4WtwPGTKNaosuNJr5tWiftOC7liM7G9+rMb8+oeJeyzymDu4rTN93seySBmbjSfsS3Vzr19KNtA==
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

w3c-xmlserializer@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/w3c-xmlserializer/-/w3c-xmlserializer-4.0.0.tgz"
  integrity sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw==
  dependencies:
    xml-name-validator "^4.0.0"

warning@^4.0.2, warning@^4.0.3:
  version "4.0.3"
  resolved "https://mirrors.tencent.com/npm/warning/-/warning-4.0.3.tgz"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

wave-resampler@^1.0.0:
  version "1.0.0"
  resolved "https://mirrors.tencent.com/npm/wave-resampler/-/wave-resampler-1.0.0.tgz#127502338d28e6d5315be1abb06665c04bbb4fc6"
  integrity sha512-bE3rbpZXuKAV52Cd8/BeJvy82ZqEHK8pPWHrZ9JioaVVTBlmWbDC+u4p9blhFcf0Skepb4hlOAHc25XfqLC48g==

wavesurfer.js@^7.8.4:
  version "7.9.1"
  resolved "https://mirrors.tencent.com/npm/wavesurfer.js/-/wavesurfer.js-7.9.1.tgz#377afd7532e201d80116498976312c8cccd10c7e"
  integrity sha512-+pG8X9c9BrfAW8KR54OPzZcAj/57sOL08He/tc6/7Mt6NCX3IfDFwexQxXVTILggGZUOUk7tFKfny32yGrysKA==

web-vitals@^3.4.0:
  version "3.5.2"
  resolved "https://mirrors.tencent.com/npm/web-vitals/-/web-vitals-3.5.2.tgz"
  integrity sha512-c0rhqNcHXRkY/ogGDJQxZ9Im9D19hDihbzSQJrsioex+KnFgmMzBiy57Z1EjkhX/+OjyBpclDCzz2ITtjokFmg==

webgl-constants@^1.1.1:
  version "1.1.1"
  resolved "https://mirrors.tencent.com/npm/webgl-constants/-/webgl-constants-1.1.1.tgz#f9633ee87fea56647a60b9ce735cbdfb891c6855"
  integrity sha512-LkBXKjU5r9vAW7Gcu3T5u+5cvSvh5WwINdr0C+9jpzVB41cjQAP5ePArDtk/WHYdVj0GefCgM73BA7FlIiNtdg==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

webworkify-webpack@^2.1.5:
  version "2.1.5"
  resolved "https://mirrors.tencent.com/npm/webworkify-webpack/-/webworkify-webpack-2.1.5.tgz#bf4336624c0626cbe85cf1ffde157f7aa90b1d1c"
  integrity sha512-2akF8FIyUvbiBBdD+RoHpoTbHMQF2HwjcxfDvgztAX5YwbZNyrtfUMgvfgFVsgDhDPVTlkbb5vyasqDHfIDPQw==

whatwg-encoding@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz"
  integrity sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==
  dependencies:
    iconv-lite "0.6.3"

whatwg-mimetype@^3.0.0:
  version "3.0.0"
  resolved "https://mirrors.tencent.com/npm/whatwg-mimetype/-/whatwg-mimetype-3.0.0.tgz"
  integrity sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://mirrors.tencent.com/npm/whatwg-url/-/whatwg-url-11.0.0.tgz"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://mirrors.tencent.com/npm/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-builtin-type@^1.1.3:
  version "1.1.3"
  resolved "https://mirrors.tencent.com/npm/which-builtin-type/-/which-builtin-type-1.1.3.tgz"
  integrity sha512-YmjsSMDBYsM1CaFiayOVT06+KJeXf0o5M/CAd4o1lTadFAtacTUM49zoYxr/oroopFDfhvN6iEcBxUyc3gvKmw==
  dependencies:
    function.prototype.name "^1.1.5"
    has-tostringtag "^1.0.0"
    is-async-function "^2.0.0"
    is-date-object "^1.0.5"
    is-finalizationregistry "^1.0.2"
    is-generator-function "^1.0.10"
    is-regex "^1.1.4"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.0.2"
    which-collection "^1.0.1"
    which-typed-array "^1.1.9"

which-collection@^1.0.1:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/which-collection/-/which-collection-1.0.2.tgz"
  integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-module@^2.0.0:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/which-module/-/which-module-2.0.1.tgz"
  integrity sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==

which-typed-array@^1.1.14, which-typed-array@^1.1.15, which-typed-array@^1.1.9:
  version "1.1.15"
  resolved "https://mirrors.tencent.com/npm/which-typed-array/-/which-typed-array-1.1.15.tgz"
  integrity sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

which@^1.2.9:
  version "1.3.1"
  resolved "https://mirrors.tencent.com/npm/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://mirrors.tencent.com/npm/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  resolved "https://mirrors.tencent.com/npm/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

widest-line@^3.1.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/widest-line/-/widest-line-3.1.0.tgz"
  integrity sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==
  dependencies:
    string-width "^4.0.0"

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/wmf/-/wmf-1.0.2.tgz"
  integrity sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==

word@~0.3.0:
  version "0.3.0"
  resolved "https://mirrors.tencent.com/npm/word/-/word-0.3.0.tgz"
  integrity sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.0.1, wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrap-ansi@^9.0.0:
  version "9.0.0"
  resolved "https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-9.0.0.tgz#1a3dc8b70d85eeb8398ddfb1e4a02cd186e58b3e"
  integrity sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==
  dependencies:
    ansi-styles "^6.2.1"
    string-width "^7.0.0"
    strip-ansi "^7.1.0"

wrappy@1:
  version "1.0.2"
  resolved "https://mirrors.tencent.com/npm/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "https://mirrors.tencent.com/npm/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  integrity sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@^8.11.0:
  version "8.18.0"
  resolved "https://mirrors.tencent.com/npm/ws/-/ws-8.18.0.tgz"
  integrity sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==

xdg-basedir@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/xdg-basedir/-/xdg-basedir-4.0.0.tgz"
  integrity sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==

xlsx@^0.18.5:
  version "0.18.5"
  resolved "https://mirrors.tencent.com/npm/xlsx/-/xlsx-0.18.5.tgz"
  integrity sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xml-name-validator@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
  integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://mirrors.tencent.com/npm/xmlchars/-/xmlchars-2.2.0.tgz"
  integrity sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://mirrors.tencent.com/npm/y18n/-/y18n-4.0.3.tgz"
  integrity sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://mirrors.tencent.com/npm/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://mirrors.tencent.com/npm/yallist/-/yallist-2.1.2.tgz"
  integrity sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://mirrors.tencent.com/npm/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@2.3.1:
  version "2.3.1"
  resolved "https://mirrors.tencent.com/npm/yaml/-/yaml-2.3.1.tgz"
  integrity sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==

yaml@^1.10.0, yaml@^1.10.2:
  version "1.10.2"
  resolved "https://mirrors.tencent.com/npm/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yaml@^2.3.4:
  version "2.4.5"
  resolved "https://mirrors.tencent.com/npm/yaml/-/yaml-2.4.5.tgz"
  integrity sha512-aBx2bnqDzVOyNKfsysjA2ms5ZlnjSAW2eG3/L5G/CSujfjLJTJsEw1bGw8kCf04KodQWk1pxlGnZ56CRxiawmg==

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://mirrors.tencent.com/npm/yargs-parser/-/yargs-parser-18.1.3.tgz"
  integrity sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.3:
  version "20.2.9"
  resolved "https://mirrors.tencent.com/npm/yargs-parser/-/yargs-parser-20.2.9.tgz"
  integrity sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://mirrors.tencent.com/npm/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs@^15.3.1:
  version "15.4.1"
  resolved "https://mirrors.tencent.com/npm/yargs/-/yargs-15.4.1.tgz"
  integrity sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^17.0.0, yargs@^17.5.1:
  version "17.7.2"
  resolved "https://mirrors.tencent.com/npm/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yn@3.1.1:
  version "3.1.1"
  resolved "https://mirrors.tencent.com/npm/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://mirrors.tencent.com/npm/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://mirrors.tencent.com/npm/yorkie/-/yorkie-2.0.0.tgz"
  integrity sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw==
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

zustand@^3.7.1:
  version "3.7.2"
  resolved "https://mirrors.tencent.com/npm/zustand/-/zustand-3.7.2.tgz#7b44c4f4a5bfd7a8296a3957b13e1c346f42514d"
  integrity sha512-PIJDIZKtokhof+9+60cpockVOq05sJzHCriyvaLBmEJixseQ1a5Kdov6fWZfWOu5SK9c+FhH1jU0tntLxRJYMA==
