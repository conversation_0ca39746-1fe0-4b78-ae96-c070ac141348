/**
 * <AUTHOR>
 * @date 2024/4/5 15:19
 * @desc 修改文件提Cr2Pinocao
 */
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    colors: {
      primary: '#5A66FF',
      black: {
        DEFAULT: '#181818',
      },
      desc: {
        DEFAULT: '#8B8B8B',
      },
      brand: {
        7: '#0047F9',
      },
    },
    extend: {},
    // 4x
    margin: {
      4: '4px',
      8: '8px',
      12: '12px',
      16: '16px',
      20: '20px',
      24: '24px',
      28: '28px',
      32: '32px',
    },
    // 4x
    padding: {
      4: '4px',
      8: '8px',
      12: '12px',
      16: '16px',
      20: '20px',
      24: '24px',
      28: '28px',
      32: '32px',
    },
    // 4x
    gap: {
      4: '4px',
      8: '8px',
    },
    fontSize: {
      14: ['14px', '22px'],
      18: ['18px', '22px'],
      16: ['16px', '22px'],
    },
    borderRadius: {
      4: '4px',
      8: '8px',
      12: '12px',
      50: '50%',
    },
  },
  plugins: [require('@tailwindcss/line-clamp')],
};
