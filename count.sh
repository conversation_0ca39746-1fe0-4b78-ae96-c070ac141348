#!/bin/bash

# 直接在这里指定分支名
branch="feature/meta-push-step"  # 替换为你想要统计的分支名
after="2024-11-26 00:00:00"
before="2024-12-06 00:00:00"

# 提交次数
echo "$after 之后的代码统计"
echo "提交次数统计==========>"
git log "$branch" --after="$after" --before="$before" | grep "^Author: " | awk '{print $2}' | sort | uniq -c | sort -k1,1nr
echo ""

echo "代码行数统计==========>"
authors=$(git log "$branch" --after="$after" --before="2024-12-15 00:00:00" | grep "^Author: " | awk '{print $2}' | sort | uniq)

git log "$branch" --since="$after" --pretty=tformat: --numstat -- . ":(exclude)package-lock.json" ":(exclude)server/proto" ":(exclude)yarn.lock" ":(exclude)src/pb" ":(exclude)src/pb_toc" | awk '{ add += $1; subs += $2; loc += $1 + $2 } END { printf " total \t added lines: %s, \t removed lines: %s, \t total lines: %s\n", add, subs, loc }'

for value in $authors; do
    git log "$branch" --since="$after" --author="$value" --pretty=tformat: --numstat -- . ":(exclude)package-lock.json" ":(exclude)server/proto" ":(exclude)yarn.lock" ":(exclude)src/pb" | awk '{ add += $1; subs += $2; loc += $1 + $2 } END { printf " '$value' \t added lines: %s, \t removed lines: %s, \t total lines: %s\n", add, subs, loc }'
done
