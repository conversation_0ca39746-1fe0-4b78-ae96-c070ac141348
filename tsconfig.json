{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "types": [
      "@types/node",
      "@types/react",
      "@types/react-dom"
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@routes": [
        /*单纯提示*/
        "./src/routes.tsx"
      ],
      "@lib-dist/*": [
        "./lib-dist/*"
      ],
    },
    "jsxImportSource": "@emotion/react"
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "*.ts",
    "src/components/Editor/components/Outline",
    "src/utils/template/video/config/GameWithNumberPerson.Configts"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
