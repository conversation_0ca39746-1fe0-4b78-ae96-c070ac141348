import { defineConfig, UserConfig } from 'vite';
import * as path from 'path';

export default defineConfig(({ mode }) => {
  const isDev = mode === 'dev';

  const config: UserConfig = {
    resolve: {
      alias: {
        '@': path.join(__dirname, 'src'),
      },
    },
    build: {
      copyPublicDir: false,
      outDir: './lib-dist',
      lib: {
        entry: path.resolve(__dirname, 'src/utils/monitor/index.ts'),
        name: 'materialMonitor',
        fileName: 'material-monitor',
        formats: ['umd'],
      },
      minify: !isDev,
      rollupOptions: {
        external: ['aegis-web-sdk'],
        output: {
          globals: {
            'aegis-web-sdk': 'Aegis',
          },
        },
      },
    },
  };
  return config;
});

// export default defineConfig({
//   resolve: {
//     alias: {
//       '@': path.join(__dirname, 'src'),
//     },
//   },
//   build: {
//     lib: {
//       entry: path.resolve(__dirname, 'src/utils/monitor/index.ts'),
//       name: 'materialMonitor',
//       fileName: 'material-monitor',
//       formats: ['umd'],
//     },
//     rollupOptions: {
//       external: ['aegis-web-sdk'],
//       output: {
//         globals: {
//           'aegis-web-sdk': 'Aegis',
//         },
//       },
//     },
//   },
// });
